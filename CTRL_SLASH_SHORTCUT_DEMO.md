# 🚀 Ctrl + / 快捷键功能演示

## ✨ 新功能介绍

我们为编辑器添加了 **Ctrl + /** 快捷键，让你可以快速插入默认的 JSON 占位符，无需每次都打开弹窗填写表单！

## 🎯 功能特点

### 1. 快速插入
- **快捷键**：`Ctrl + /`
- **位置**：编辑器任意位置
- **效果**：立即插入预设的 JSON 占位符

### 2. 默认模板
插入的默认 JSON 为单行格式，逗号后有空格便于观察：
```
{"标记目的": "描述场景", "需要信息": "人物、情况、状态......", "字数限制": "80-150字", "风格要求": "正常", "内容种类": " ", "优先级": "60/100", "类型": "占位符"}
```

**优势**：
- 作为单个段落插入，重新加载时不会被分割
- 逗号后的空格让内容更易读
- 保持文档结构的完整性

### 3. 多种使用方式
1. **全局快捷键**：在编辑器中按 `Ctrl + /`（插入默认模板）
2. **面板快捷键**：标点符号面板打开时也可使用 `Ctrl + /`
3. **按钮点击**：点击面板中的 "JSON" 按钮（自定义模式）

**注意**：所有方式插入的 JSON 都是单行格式，确保重新加载时保持为一个段落

### 4. 用户体验优化
- ✅ 插入后自动聚焦编辑器
- ✅ 显示成功提示消息
- ✅ 支持暗色主题
- ✅ 面板中显示快捷键提示

## 🔧 使用场景

### 写作时快速标记
当你在写作过程中需要标记某个位置需要补充内容时：

1. 按 `Ctrl + /` 
2. JSON 占位符立即插入
3. 继续写作，稍后回来处理

### 批量标记
需要在多个位置添加占位符时，快捷键比反复打开弹窗更高效。

## 📝 实现细节

### 代码修改
1. **editor.vue**：添加全局和编辑器内部快捷键处理
2. **PunctuationPanel.vue**：添加面板内快捷键支持和UI提示

### 兼容性
- 不影响现有的 JSON 弹窗功能
- 与其他快捷键无冲突
- 支持所有主题模式

## 🎨 界面展示

标点符号面板现在会显示：
```
┌─────────────────────────┐
│ 功能按键                │
│ [删除] [回车] [JSON] [撤销] │
│                         │
│ ✏️ 快捷键：Ctrl + / 插入默认JSON │
└─────────────────────────┘
```

## 🚀 开始使用

1. 打开编辑器
2. 在任意位置按 `Ctrl + /`
3. 享受快速插入的便利！

---

**提示**：这个功能让符号 JSON 的使用更加便捷，既保留了自定义的灵活性，又提供了快速插入的效率！
