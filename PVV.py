# coding:utf-8

import platform

import webview
from backend.bridge.API import API
import os
import hashlib
import json
from pathlib import Path
import sys
import threading
import time
import logging
import hmac



# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.expanduser('~'), '.pvv', 'integrity.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('PVV-Integrity')

# 硬编码的签名密钥 - 在实际应用中应更加隐蔽并使用更复杂的保护机制
SIGNATURE_KEY = b"PVV_8f2a61a4e9b94a31b8ea8d6e2f13f308_SECRET_KEY"

# 硬编码的关键文件哈希值 - 此处仅作为示例，实际应由构建脚本自动填充
# 这些是最关键的不能被修改的文件
CRITICAL_FILE_HASHES = {
    "index.html": "8fc05f675051148f20141ec943f151f8499bf1487408ec11469170d6775135c8",
    "assets/A2B-Dp9HBGKX.js": "fb372aac4e9e0d67502cb8abd8f093daf93cb5c3f33960acce09e52ce3b7ed95",
    "assets/AdvancedMarkdownEditor-B-KriyJj.js": "c4ae8a8c573c0ba94838c84a754ec44eae76943d0df0b0a6872950fab83817b0",
    "assets/AiPrompt-D_Bq2104.js": "58752b71a6a56ae587086c10862b6e296a4931ce20c110b8fc49cdf3b5752bd0",
    "assets/AIProviderConfig-hssIYs10.js": "c8956b4d4c74ffce0519cf05dd87b58a008d8454592fd3aefd928c28acefd87c",
    "assets/aiProviders-BAOX-EH8.js": "a09f195d7e45fc1e00b9c5f1fc2ab744c92dd6470835cf2449fb36b706ed48be",
    "assets/AIRoleManager-vyNx1kT2.js": "96fe31f6687935fc06658120f301f39a8179491bfd1a7001850eb6a47304bdb0",
    "assets/aiRoles-BNrBgqza.js": "c81b60fd71c7e1c378a5a2e20e03fe9159c9a4f63c0fbb80a326299e56dc41e2",
    "assets/apiUtils-CGTCyBFs.js": "1d7359a7f16e6ad69f143b38b1983ed543b63a959aefd3582124a0ede2eb0527",
    "assets/application-o84WdE8P.js": "9c1fa38dd304444072dc02c68820dc3dddc9ea4df333056e2b93d903ad7d238b",
    "assets/BackupSettings-Dy3Lofw-.js": "380a63613a89a3e607fca40651dd9fe375bb692da640619514b5dd5537358952",
    "assets/book-BHcNewcO.js": "b1d93c61ab8915e7e1ac8cb846967559bd264a2bdafc9ebdc8db7669a25cf486",
    "assets/CharacterInspiration-CyntUvjX.js": "525551648dce8295f195c19356816936e778a88629ccf9fea093c127c10ce5a8",
    "assets/chat-5NsqQv1h.js": "7dc85c9c4882ed5a2ea2156b4bf0610601837eb10f0454eb5408d184c5326f10",
    "assets/ChatSettings-CAeRtEi2.js": "0504e6699c17d77ba42ef24ad096d00114f231f4f3469f059591b9cf364b6539",
    "assets/ChineseDictionary-CqFtcRXx.js": "437fac3d7df11c729ba06c092e457b4ac4328b8ca64489970b00ee8e4a349ac2",
    "assets/ChromeSettings-BuJ3Dll-.js": "56aac02b4eac38bbba4edc9638aaf54d602626382af2a48a2af81a62edab96f4",
    "assets/chunk-BCmGWj6J.js": "0a3841c44490078f83b5cae5d8a93ffbb0daba6462342861168fd148a772ef81",
    "assets/chunk-ChIpqalD.js": "d9abbc82d0849f645191e887d4bb56277e1966305e5a0ff4b1b8f837319b7cc3",
    "assets/chunk-D36TitLM.js": "60fbaf24c28699c82b5e9fe9b50af654859d3be19a6a8c54a8e9cb6677bb48d9",
    "assets/chunk-DN3JcA67.js": "c713bdb991fa8206452d3bdc79194590b9dfb041fe6f54f21e3c1f8b39943c33",
    "assets/chunk-hhEK-RPQ.js": "63d9039386559e7c671c3e55dc838321712d14bbee1bd0119449df0ead457e5a",
    "assets/chunk-lOOr5YPO.js": "ec017718291d5fd7b5c8b557e961f98da58140f9ef5d99dc3605b0fd6edb829a",
    "assets/chunk-m_wFnVgn.js": "4ebb001dbbcab5ac776d8782b5bfd5aee48ec53e4723aa28c45925f016988863",
    "assets/chunk-zL7v4EzI.js": "673a0c2a0eeb6b68b5a6a37c38372f12403d69c75560bd7db81f38f6200eaf7b",
    "assets/CustomPool-CWwbc66y.js": "bef72140fbac1defec70f246d90c840ef1531595ee69219cae2ee42d11618666",
    "assets/editor-BTWGr3BW.js": "ac403d4179bd665c39b389acb681e92d64f53b39b4e09364192bb911d14221e4",
    "assets/entry-BIjVVog3.js": "d3c9221779a0d7a32d8f58689feee2b405d3dd123f96b67427ed84bdf430c2a9",
    "assets/FeishuConfig-CwaMKjzu.js": "03c5478e2f68e9fe5b5083423be3af84d2d8ce00aa0fdefaf219145ea3d2960e",
    "assets/GitBackup-BKWJptLG.js": "017ee3aa5bc4d9d5316bacc5d52a7129fb5754e04ad5b33c8ffef0814d73386a",
    "assets/html2canvas-esm-B4CzG4On.js": "5ef118be2089d0c1abdaff1a8b32d8e1c646f14b8640eb6eb11f35ea2b3ef05d",
    "assets/index-BFevWLGk.js": "2a4fde0470b910a635b1b41e495d57adac01a91543bdc4d8c0676470e3b1a4f2",
    "assets/index-BNF5nexI.js": "d1fd46fc896d29adbc02e279c74733cb521dcbde2203a9605419a25e657e8974",
    "assets/index-browser-OxPLOBIU.js": "ed8936aeff5ff4eb47c61f234a27625f700da6baa04ab61ddfa6f2b07682be01",
    "assets/installCanvasRenderer-Cl52Q2Ud.js": "26d4fc7c67848acb7ea16a0be765dd9373a143b9037c53c64b7f9b04a58ea99b",
    "assets/MarkdownBubble-CJ_yNM_A.js": "b056a604423d8ff431ddffad0bf09a5d62d0fd6ec33cad5f96a6310b5ab43ce5",
    "assets/outline-L-wB9PUx.js": "d44e23b8321ae9dd9fd58310f48ef2c4f6c9808d5e4288506ffb922e474f2421",
    "assets/pilot-6ZMiwya8.js": "f8fa8b4c485b445fd7969022b37bbb3dfd1e0d56c84b0da169e41e741ada8b50",
    "assets/RelationShip-B-YENlUF.js": "96b16f344f3e2c093a14b42caace31be7906a182acdc7aa6e63b87f33b9fa548",
    "assets/SceneCards-Dpa-mcqw.js": "cb2c047218dc48f30ed3f1a65d8562186447e6f2a3febb3681a92dd0a65c76bd",
    "assets/StoryInspiration-K7yFMUhv.js": "934dae07031f9f9aa9c04e080dfec71730ac8d8b8313a386b33fcb9022c3e3ee",
    "assets/vuedraggable-umd-CP6rdxdP.js": "41e0fb8a5b3ac57e54cabf11d32731b0539bb2155d420927242dfd8b88d9f038",
    "assets/word-JUwTMPif.js": "2c708dcfea6698c4594c900505e80f1f2b876c2910c407ef030061cb6f06c052",
    "assets/xiehouyu-CzgqJG6Z.js": "259e27bd4c9af09a601fecbbcbbc3adeda76fec0da5b82463029abc3378dcafc",
}

class IntegrityChecker:
    """增强型前端文件完整性检查器"""

    def __init__(self, statics_dir, check_interval=30):
        """
        初始化完整性检查器
        
        :param statics_dir: 静态文件目录
        :param check_interval: 检查间隔(秒)，默认5分钟
        """
        self.statics_dir = statics_dir
        self.manifest_path = os.path.join(statics_dir, ".integrity")  # 隐藏文件
        self.check_interval = check_interval
        self.stop_event = threading.Event()
        self.is_running = False
        self.manifest = self._load_manifest()

    def _load_manifest(self):
        """加载完整性清单文件（带签名验证）"""
        try:
            print(f"检查完整性清单文件: {self.manifest_path}")
            if not os.path.exists(self.manifest_path):
                logger.warning("完整性清单文件不存在，将在开发模式下自动生成")
                print("完整性清单文件不存在")
                return None

            print("完整性清单文件存在，开始加载...")
            with open(self.manifest_path, 'rb') as f:
                raw_data = f.read()

            print(f"读取到数据长度: {len(raw_data)} 字节")

            # 分离签名和数据 - 修正签名长度为32字节而非64字节
            signature = raw_data[:32]  # HMAC-SHA256签名长度为32字节
            data = raw_data[32:]       # 剩余部分为数据

            print("开始验证签名...")
            # 验证签名
            expected_signature = hmac.new(SIGNATURE_KEY, data, hashlib.sha256).digest()
            if not hmac.compare_digest(signature, expected_signature):
                logger.critical("完整性清单签名验证失败！可能被篡改")
                print("签名验证失败")
                return None

            print("签名验证通过，解析JSON...")
            # 解码JSON
            raw_manifest = json.loads(data.decode('utf-8'))
            print(f"成功加载完整性清单，包含 {len(raw_manifest)} 个文件")

            # 修复路径分隔符问题：将所有路径统一为正斜杠
            manifest = {}
            for file_path, file_hash in raw_manifest.items():
                # 将反斜杠转换为正斜杠，确保跨平台兼容性
                normalized_path = file_path.replace('\\', '/')
                manifest[normalized_path] = file_hash

            # 调试信息：显示路径转换
            print("路径规范化示例:")
            for i, (original, normalized) in enumerate(zip(list(raw_manifest.keys())[:3], list(manifest.keys())[:3])):
                print(f"  {i+1}. {repr(original)} -> {repr(normalized)}")

            return manifest

        except Exception as e:
            logger.error(f"加载完整性清单失败: {str(e)}")
            print(f"加载完整性清单异常: {e}")
            return None

    def calculate_file_hash(self, file_path):
        """计算文件的SHA-256哈希值"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {str(e)}")
            return None

    def generate_integrity_manifest(self):
        """生成带签名的文件完整性清单"""
        manifest = {}
        file_count = 0

        # 扫描所有文件
        for path in Path(self.statics_dir).rglob('*'):
            if path.is_file() and path.name != ".integrity":
                relative_path = str(path.relative_to(self.statics_dir))
                # 统一使用正斜杠，确保跨平台兼容性
                relative_path = relative_path.replace('\\', '/')
                file_hash = self.calculate_file_hash(path)
                if file_hash:
                    manifest[relative_path] = file_hash
                    file_count += 1

        # 序列化数据
        json_data = json.dumps(manifest, sort_keys=True).encode('utf-8')

        # 生成签名
        signature = hmac.new(SIGNATURE_KEY, json_data, hashlib.sha256).digest()

        # 保存签名+数据
        try:
            with open(self.manifest_path, 'wb') as f:
                f.write(signature + json_data)

            logger.info(f"已生成签名保护的完整性清单，包含 {file_count} 个文件")
            return manifest
        except Exception as e:
            logger.error(f"保存完整性清单失败: {str(e)}")
            return None

    def verify_integrity(self):
        """验证文件完整性，包括硬编码哈希和清单哈希"""
        try:
            modified_files = []

            # 第1步：验证关键文件 (通过硬编码哈希)
            for file_path, expected_hash in CRITICAL_FILE_HASHES.items():
                # 将路径分隔符转换为当前系统的格式
                normalized_file_path = file_path.replace('/', os.sep).replace('\\', os.sep)
                full_path = os.path.join(self.statics_dir, normalized_file_path)

                if not os.path.exists(full_path):
                    logger.critical(f"关键文件丢失: {file_path}")
                    modified_files.append(f"关键丢失: {file_path}")
                    continue

                actual_hash = self.calculate_file_hash(full_path)
                if actual_hash != expected_hash:
                    logger.critical(f"关键文件被修改: {file_path}")
                    modified_files.append(f"关键修改: {file_path}")

            # 第2步：验证签名和加载清单
            if not self.manifest:
                # 尝试重新加载清单
                self.manifest = self._load_manifest()
                if not self.manifest:
                    logger.critical("无法加载或验证完整性清单！")
                    modified_files.append("完整性清单验证失败")
                    return False, modified_files

            # 第3步：用清单验证其他文件
            for file_path, expected_hash in self.manifest.items():
                # 跳过已在关键文件中检查过的
                if file_path in CRITICAL_FILE_HASHES:
                    continue

                # 将路径分隔符转换为当前系统的格式
                normalized_file_path = file_path.replace('/', os.sep).replace('\\', os.sep)
                full_path = os.path.join(self.statics_dir, normalized_file_path)

                if not os.path.exists(full_path):
                    logger.warning(f"文件丢失: {file_path}")
                    modified_files.append(f"丢失: {file_path}")
                    continue

                actual_hash = self.calculate_file_hash(full_path)
                if actual_hash != expected_hash:
                    logger.warning(f"文件被修改: {file_path}")
                    modified_files.append(f"修改: {file_path}")

            # 第4步：检查是否有新增文件
            for path in Path(self.statics_dir).rglob('*'):
                if path.is_file() and path.name != ".integrity":
                    relative_path = str(path.relative_to(self.statics_dir))
                    # 统一使用正斜杠进行比较
                    normalized_relative_path = relative_path.replace('\\', '/')

                    if normalized_relative_path not in self.manifest and normalized_relative_path not in CRITICAL_FILE_HASHES:
                        logger.warning(f"发现未知文件: {normalized_relative_path}")
                        modified_files.append(f"未知: {normalized_relative_path}")

            return len(modified_files) == 0, modified_files
        except Exception as e:
            logger.error(f"完整性验证出现异常: {str(e)}")
            if os.environ.get("PVV_FORCE_START") == "1":
                # 紧急情况：允许强制启动
                return True, []
            return False, ["验证过程出错"]

    def start_periodic_check(self, window=None):
        """启动定期检查线程，并添加随机性防止逃避检测"""
        if self.is_running:
            return

        self.is_running = True
        self.stop_event.clear()

        def check_thread():
            logger.info(f"完整性检查线程已启动")

            # 防逃避变量
            next_check = 0

            while not self.stop_event.is_set():
                # 随机变化检查间隔，防止预测
                if time.time() >= next_check:
                    # 执行完整性检查
                    is_intact, modified_files = self.verify_integrity()

                    if not is_intact:
                        logger.critical(f"检测到文件修改或签名失败: {', '.join(modified_files[:5])}")

                        if window:
                            try:
                                # 发送警告
                                window.evaluate_js("""
                                    alert('文件损坏，程序将退出！');
                                """)
                                # 给用户时间看警告
                                time.sleep(2)
                                # 退出程序
                                window.destroy()
                            except:
                                # 如果JS执行失败，直接退出
                                os._exit(1)
                        else:
                            # 如果没有窗口对象，直接退出
                            os._exit(1)

                    # 设置下次检查时间（有随机变化）
                    jitter = self.check_interval * 0.2  # 20%的随机变化
                    random_interval = self.check_interval + (hash(time.time()) % int(jitter)) - (jitter / 2)
                    next_check = time.time() + random_interval

                # 更短的等待间隔，提高响应性
                time.sleep(1)

        # 启动检查线程
        check_thread = threading.Thread(target=check_thread, daemon=True)
        check_thread.start()

        return check_thread

    def stop(self):
        """停止检查线程"""
        self.stop_event.set()
        self.is_running = False

def get_app_base_dir():
    """获取应用程序基础目录，支持跨平台"""
    if getattr(sys, 'frozen', False):
        # 打包后的应用
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller
            return os.path.dirname(sys.executable)
        else:
            # Nuitka 或其他打包工具
            exe_dir = os.path.dirname(sys.executable)

            # 在macOS上，检查是否在.app包内，如果是，需要特殊处理
            if platform.system() == "Darwin" and '.app' in exe_dir:
                # 对于 Nuitka 打包的 macOS 应用，statics 目录通常在以下位置之一：

                # 1. 首先检查可执行文件同级目录（最常见的情况）
                statics_in_exe_dir = os.path.join(exe_dir, "statics")
                if os.path.exists(statics_in_exe_dir):
                    print(f"找到 statics 在可执行文件目录: {statics_in_exe_dir}")
                    return exe_dir

                # 2. 检查 .app/Contents/Resources/ 目录
                if exe_dir.endswith('.app/Contents/MacOS'):
                    resources_dir = os.path.join(os.path.dirname(exe_dir), 'Resources')
                    statics_in_resources = os.path.join(resources_dir, "statics")
                    if os.path.exists(statics_in_resources):
                        print(f"找到 statics 在 Resources 目录: {statics_in_resources}")
                        return resources_dir

                # 3. 检查 .app/Contents/ 目录
                if exe_dir.endswith('.app/Contents/MacOS'):
                    contents_dir = os.path.dirname(exe_dir)
                    statics_in_contents = os.path.join(contents_dir, "statics")
                    if os.path.exists(statics_in_contents):
                        print(f"找到 statics 在 Contents 目录: {statics_in_contents}")
                        return contents_dir

                # 4. 检查 .app 根目录
                app_match = exe_dir.find('.app')
                if app_match != -1:
                    app_root = exe_dir[:app_match + 4]  # 包含 .app
                    statics_in_app_root = os.path.join(app_root, "statics")
                    if os.path.exists(statics_in_app_root):
                        print(f"找到 statics 在 app 根目录: {statics_in_app_root}")
                        return app_root

                # 5. 如果都不在，打印调试信息并回退到exe目录
                print(f"警告: 在 macOS .app 包中未找到 statics 目录")
                print(f"可执行文件目录: {exe_dir}")
                print(f"尝试的路径:")
                print(f"  - {statics_in_exe_dir}")
                if exe_dir.endswith('.app/Contents/MacOS'):
                    print(f"  - {statics_in_resources}")
                    print(f"  - {statics_in_contents}")
                return exe_dir
            else:
                return exe_dir
    else:
        # 开发环境
        return os.path.dirname(os.path.abspath(__file__))

if __name__ == '__main__':

    # 获取应用程序基础目录
    base_dir = get_app_base_dir()
    statics_dir = os.path.join(base_dir, "statics")

    # 调试信息：打印路径信息
    print(f"=== PVV 启动路径调试信息 ===")
    print(f"系统: {platform.system()}")
    print(f"是否打包: {getattr(sys, 'frozen', False)}")
    print(f"可执行文件: {sys.executable}")
    print(f"可执行文件目录: {os.path.dirname(sys.executable)}")

    # 在 macOS 上显示更多调试信息
    if platform.system() == "Darwin" and getattr(sys, 'frozen', False):
        exe_dir = os.path.dirname(sys.executable)
        print(f"macOS 特定调试信息:")
        print(f"  - 是否在 .app 包内: {'.app' in exe_dir}")
        if '.app' in exe_dir:
            print(f"  - 是否在 MacOS 目录: {exe_dir.endswith('.app/Contents/MacOS')}")
            if exe_dir.endswith('.app/Contents/MacOS'):
                contents_dir = os.path.dirname(exe_dir)
                resources_dir = os.path.join(contents_dir, 'Resources')
                print(f"  - Contents 目录: {contents_dir}")
                print(f"  - Resources 目录: {resources_dir}")
                print(f"  - Resources 目录存在: {os.path.exists(resources_dir)}")
                if os.path.exists(resources_dir):
                    try:
                        print(f"  - Resources 目录内容: {os.listdir(resources_dir)[:10]}")
                    except:
                        print(f"  - 无法读取 Resources 目录内容")

    print(f"基础目录: {base_dir}")
    print(f"静态文件目录: {statics_dir}")
    print(f"静态文件目录存在: {os.path.exists(statics_dir)}")
    if os.path.exists(statics_dir):
        try:
            print(f"静态文件目录内容: {os.listdir(statics_dir)[:10]}")  # 只显示前10个文件
        except Exception as e:
            print(f"无法读取静态文件目录内容: {e}")
    print(f"完整性文件: {os.path.join(statics_dir, '.integrity')}")
    print(f"完整性文件存在: {os.path.exists(os.path.join(statics_dir, '.integrity'))}")
    print(f"===============================")


    dev_mode = False

    # 添加更清晰的注释说明使用场景
    if dev_mode:
        logger.info("开发模式：生成完整性清单而不是验证")
        logger.info("注意: 仅在开发或重新生成清单时使用此模式")
        integrity_checker = IntegrityChecker(statics_dir)
        integrity_checker.generate_integrity_manifest()
    else:
        # 初始化完整性检查器
        integrity_checker = IntegrityChecker(statics_dir)

        # 启动时验证一次
        print("开始完整性检查...")
        is_intact, modified_files = integrity_checker.verify_integrity()

        print(f"完整性检查结果: {'通过' if is_intact else '失败'}")
        if modified_files:
            print(f"问题文件: {modified_files}")

        if not is_intact:
            logger.critical(f"启动检查：检测到文件被篡改: {', '.join(modified_files[:5])}")

            # 在macOS上，如果是清单问题，尝试重新生成
            if platform.system() == "Darwin" and any("完整性清单" in msg for msg in modified_files):
                print("macOS检测到完整性清单问题，尝试重新生成...")
                try:
                    integrity_checker.generate_integrity_manifest()
                    print("重新生成完整性清单成功，重新验证...")
                    is_intact, modified_files = integrity_checker.verify_integrity()
                    if is_intact:
                        print("重新验证通过，继续启动")
                    else:
                        print(f"重新验证仍然失败: {modified_files}")
                        print("程序文件完整性验证失败，程序无法启动")
                        sys.exit(1)
                except Exception as e:
                    print(f"重新生成完整性清单失败: {e}")
                    print("程序文件完整性验证失败，程序无法启动")
                    sys.exit(1)
            else:
                print("程序文件完整性验证失败，程序无法启动")
                sys.exit(1)

    # 创建窗口
    index_html_path = os.path.join(statics_dir, 'index.html')
    window = webview.create_window(
        title="PVV",
        width=1080,
        height=800,
        url=index_html_path,
        js_api=API(),
        frameless=True,
        resizable=True
    )

    # 启动完整性检查线程
    if not dev_mode:
        integrity_checker.start_periodic_check(window)



    # 启动应用
    logo_path = os.path.join(base_dir, 'logo.ico')
    webview.start(debug=False, gui="edgechromium", icon=logo_path)
