# coding:utf-8

import webview
from backend.bridge.API import API
import os
import sys
import atexit
import platform

# 跨平台编码设置，防止编码错误
import locale

def setup_encoding():
    """设置正确的编码环境，防止跨平台编码问题"""
    system = platform.system()

    if system == "Darwin":  # macOS
        # 设置环境变量解决 Unicode 编码问题
        os.environ['LC_ALL'] = 'en_US.UTF-8'
        os.environ['LANG'] = 'en_US.UTF-8'
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        # 尝试设置系统 locale
        try:
            locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
        except locale.Error:
            try:
                locale.setlocale(locale.LC_ALL, 'C.UTF-8')
            except locale.Error:
                pass  # 如果都失败了，继续使用默认设置

    elif system == "Windows":  # Windows
        # Windows 通常使用 UTF-8，但确保设置正确
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        # 尝试设置 Windows 的 locale
        try:
            # Windows 上尝试设置为系统默认的 UTF-8
            locale.setlocale(locale.LC_ALL, '')
        except locale.Error:
            try:
                # 备选方案：使用 C locale
                locale.setlocale(locale.LC_ALL, 'C')
            except locale.Error:
                pass

    else:  # Linux 和其他系统
        # 设置通用的 UTF-8 环境
        os.environ['LC_ALL'] = 'C.UTF-8'
        os.environ['LANG'] = 'C.UTF-8'
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        try:
            locale.setlocale(locale.LC_ALL, 'C.UTF-8')
        except locale.Error:
            try:
                locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
            except locale.Error:
                pass

# 在导入其他模块之前设置编码
setup_encoding()

# 导入嵌入的前端资源模块
from embedded_server import start_embedded_server, stop_embedded_server

print("✅ 使用嵌入式前端资源")




def get_app_base_dir():
    """获取应用程序基础目录，用于查找logo等资源"""
    if getattr(sys, 'frozen', False):
        # 打包后的应用
        return os.path.dirname(sys.executable)
    else:
        # 开发环境
        return os.path.dirname(os.path.abspath(__file__))

if __name__ == '__main__':
    # 获取应用程序基础目录
    base_dir = get_app_base_dir()

    # 启动嵌入式HTTP服务器
    server_url = start_embedded_server()
    frontend_url = server_url
    print(f"🌐 前端服务地址: {frontend_url}")

    # 注册退出时停止服务器
    atexit.register(stop_embedded_server)

    # 创建窗口
    window = webview.create_window(
        title="PVV",
        width=1100,
        height=800,
        url=frontend_url,
        js_api=API(),
        frameless=True,
        easy_drag=False,
    )

    # 启动应用
    logo_path = os.path.join(base_dir, 'logo.ico')

    try:
        webview.start(debug=False, gui="edgechromium", icon=logo_path)
    finally:
        # 确保服务器被停止
        stop_embedded_server()
        print("🛑 应用退出，服务器已停止")
