import threading
import os
import json
import copy
import time
import uuid






class ConfigManager:
    def __init__(self, config_file):
        self.config_file = config_file
        self._config_cache = None
        self._lock = threading.Lock()
        self._last_read_time = 0
        self._cache_ttl = 5  # 缓存有效期（秒）
        self.default_config = {
            "theme": "dark",
            "editor": {
                "fontFamily": "汉仪旗黑",
                "fontSize": 16,
                "lineHeight": 1.6,
                "contentWidth": 60
            },
            "chrome": {
                "default_path": "",
                "downloadDir": "",
                "userDataDirs": [

                ]
            },
            "openai": {
                "api_key": "",
                "base_url": ""
            },
            "oneapi": {
                "exe": "",
                "port": ""
            },
            "ai": {

            },
            "backgroundImage": "",
            "loaded": False
        }
        self._ensure_config_file()

    def _ensure_config_file(self):
        """确保配置文件存在，如果不存在则创建默认配置"""
        if not os.path.exists(os.path.dirname(self.config_file)):
            os.makedirs(os.path.dirname(self.config_file))
        if not os.path.exists(self.config_file):
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.default_config, f, ensure_ascii=False, indent=2)

    def _is_cache_valid(self):
        """检查缓存是否有效"""
        return (
                self._config_cache is not None
                and time.time() - self._last_read_time < self._cache_ttl
        )

    def load_config(self):
        """加载配置，优先使用缓存"""
        with self._lock:
            if self._is_cache_valid():
                return copy.deepcopy(self._config_cache)

            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self._config_cache = config
                self._last_read_time = time.time()
                return copy.deepcopy(config)
            except (json.JSONDecodeError, FileNotFoundError):
                # 如果配置文件损坏或不存在，重置为默认配置
                self._ensure_config_file()
                return copy.deepcopy(self.default_config)

    def save_config(self, config):
        """保存配置并更新缓存"""
        with self._lock:
            max_retries = 3
            retry_delay = 0.1  # 100ms

            for attempt in range(max_retries):
                try:
                    # 获取当前存储的配置（如果有的话）
                    current_config = None
                    try:
                        with open(self.config_file, 'r', encoding='utf-8') as f:
                            current_config = json.load(f)
                    except (FileNotFoundError, json.JSONDecodeError):
                        # 如果文件不存在或格式错误，使用默认配置作为基础
                        current_config = copy.deepcopy(self.default_config)

                    # 执行深度合并，保留现有配置中未被新配置覆盖的部分
                    merged_config = self._deep_merge_configs(current_config, config)

                    # 创建临时文件，使用更安全的临时文件名
                    import uuid
                    temp_file = f"{self.config_file}.tmp.{uuid.uuid4().hex[:8]}"

                    try:
                        # 写入临时文件
                        with open(temp_file, 'w', encoding='utf-8') as f:
                            json.dump(merged_config, f, ensure_ascii=False, indent=2)

                        # 确保文件写入完成
                        import os
                        if os.name == 'nt':  # Windows
                            # 在Windows上，确保文件句柄完全关闭
                            time.sleep(0.01)

                        # 检查临时文件是否成功创建且有内容
                        if not os.path.exists(temp_file) or os.path.getsize(temp_file) == 0:
                            raise Exception("临时文件创建失败或为空")

                        # 原子性地替换原文件
                        if os.name == 'nt':  # Windows系统
                            # Windows上的原子替换可能需要特殊处理
                            if os.path.exists(self.config_file):
                                # 创建备份
                                backup_file = f"{self.config_file}.bak"
                                if os.path.exists(backup_file):
                                    os.remove(backup_file)
                                os.rename(self.config_file, backup_file)

                            # 移动临时文件到目标位置
                            os.rename(temp_file, self.config_file)

                            # 删除备份文件
                            backup_file = f"{self.config_file}.bak"
                            if os.path.exists(backup_file):
                                try:
                                    os.remove(backup_file)
                                except:
                                    pass  # 备份文件删除失败不影响主要功能
                        else:
                            # Unix系统使用os.replace
                            os.replace(temp_file, self.config_file)

                        # 更新缓存
                        self._config_cache = copy.deepcopy(merged_config)
                        self._last_read_time = time.time()
                        return True

                    except Exception as e:
                        # 清理临时文件
                        if os.path.exists(temp_file):
                            try:
                                os.remove(temp_file)
                            except:
                                pass
                        raise e

                except Exception as e:
                    print(f"保存配置失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")

                    if attempt < max_retries - 1:
                        # 等待后重试
                        time.sleep(retry_delay * (attempt + 1))  # 递增延迟
                        continue
                    else:
                        # 最后一次尝试失败，返回False
                        print(f"保存配置最终失败: {str(e)}")
                        return False

            return False
                
    def _deep_merge_configs(self, target, source):
        """深度合并两个配置字典，保留target中未被source覆盖的值"""
        if not source:
            return target
            
        merged = copy.deepcopy(target)
        
        for key, value in source.items():
            # 如果source中的值是字典，且目标中对应的key也存在且也是字典
            if isinstance(value, dict) and key in merged and isinstance(merged[key], dict):
                # 递归合并子字典
                merged[key] = self._deep_merge_configs(merged[key], value)
            # 对于列表，直接替换而不是合并
            elif isinstance(value, list) or value is not None:
                # 替换为source中的值
                merged[key] = copy.deepcopy(value)
        
        return merged

    def update_config_item(self, path, value):
        """更新单个配置项"""
        with self._lock:
            config = self.load_config()
            current = config
            parts = path.split('.')

            # 遍历路径直到倒数第二个部分
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]

            # 设置最后一个属性的值
            current[parts[-1]] = value

            return self.save_config(config)
