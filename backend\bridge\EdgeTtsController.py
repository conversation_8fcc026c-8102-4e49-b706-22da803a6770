from backend.bridge.Base import ResponsePacket
import os
import json
import time
import tempfile
import shutil
import platform

class EdgeTtsController(ResponsePacket):
    def __init__(self, base_dir):
        self.base_dir = base_dir
        self.tts_config_file = os.path.join(base_dir, "config", "tts_config.json")
        
        # 默认配置 - 使用数值格式，便于前端处理
        self.default_config = {
            "voice": "zh-CN-XiaoxiaoNeural",
            "rate": 0,      # 语速，范围：-100到+100
            "volume": 0,    # 音量，范围：-100到+100
            "pitch": 0      # 音调，范围：-100到+100
        }
        

        
        # 初始化配置
        self._ensure_config_dir()
        self._load_config()

    def _ensure_config_dir(self):
        """确保配置目录存在"""
        config_dir = os.path.dirname(self.tts_config_file)
        if not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)
            # print(f"创建TTS配置目录: {config_dir}")

    def _load_config(self):
        """加载TTS配置"""
        try:
            if os.path.exists(self.tts_config_file):
                print(f"加载TTS配置文件: {self.tts_config_file}")
                with open(self.tts_config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    print(f"读取到的配置: {saved_config}")
                    
                    # 更新默认配置
                    for key in self.default_config:
                        if key in saved_config:
                            self.default_config[key] = saved_config[key]
                    
                    # print(f"合并后的配置: {self.default_config}")
            else:
                # print(f"TTS配置文件不存在，使用默认配置: {self.default_config}")
                # 创建默认配置文件
                self._save_config()
                
        except Exception as e:
            pass
            # print(f"加载TTS配置失败: {str(e)}")
            # print(f"使用默认配置: {self.default_config}")

    def _save_config(self):
        """保存TTS配置到独立文件"""
        try:
            # print(f"保存TTS配置到: {self.tts_config_file}")
            # print(f"配置内容: {self.default_config}")
            
            # 使用临时文件确保原子性写入
            temp_file = None
            try:
                # 创建临时文件
                config_dir = os.path.dirname(self.tts_config_file)
                temp_fd, temp_file = tempfile.mkstemp(
                    suffix='.json', 
                    dir=config_dir,
                    prefix='tts_config_'
                )
                
                # 写入配置
                with os.fdopen(temp_fd, 'w', encoding='utf-8') as f:
                    json.dump(self.default_config, f, ensure_ascii=False, indent=2)
                    f.flush()
                    os.fsync(f.fileno())  # 强制写入磁盘
                
                # print(f"临时文件写入完成: {temp_file}")
                
                # 跨平台原子性替换
                self._atomic_replace(temp_file, self.tts_config_file)
                
                temp_file = None  # 标记已成功移动
                # print(f"TTS配置保存成功: {self.tts_config_file}")
                
                # 验证保存结果
                self._verify_saved_config()
                return True
                
            except Exception as write_error:
                # print(f"写入配置文件失败: {str(write_error)}")
                if temp_file and os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                    except:
                        pass
                raise write_error
                
        except Exception as e:
            # print(f"保存TTS配置失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def _verify_saved_config(self):
        """验证保存的配置"""
        try:
            time.sleep(0.1)  # 短暂等待文件系统同步
            
            if os.path.exists(self.tts_config_file):
                with open(self.tts_config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    # print(f"✓ 配置验证成功: {saved_config}")
                    
                    # 验证关键字段
                    for key in ['voice', 'rate', 'volume', 'pitch']:
                        if key in saved_config and saved_config[key] == self.default_config[key]:
                            # print(f"✓ {key} 验证通过")
                            pass
                        else:
                            print(f"✗ {key} 验证失败: 期望 {self.default_config[key]}, 实际 {saved_config.get(key)}")
            else:
                print("✗ 配置文件不存在")
                
        except Exception as e:
            print(f"验证配置时出错: {str(e)}")

    def _atomic_replace(self, temp_file, target_file):
        """跨平台原子性文件替换"""
        try:
            if os.name == 'nt':  # Windows
                # Windows需要先删除目标文件
                if os.path.exists(target_file):
                    os.remove(target_file)
                shutil.move(temp_file, target_file)
            else:  # Unix/Linux/macOS
                # Unix系统支持原子性替换
                shutil.move(temp_file, target_file)

            print(f"文件原子性替换成功: {target_file}")

        except Exception as e:
            print(f"文件替换失败: {str(e)}")
            # 清理临时文件
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
            raise e

    def get_tts_config(self):
        """获取TTS配置"""
        try:
            # print(f"获取TTS配置: {self.default_config}")
            return self._success_response("获取TTS配置成功", self.default_config)
        except Exception as e:
            print(f"获取TTS配置失败: {str(e)}")
            return self._error_response(f"获取TTS配置失败: {str(e)}")

    def update_tts_config(self, config_updates):
        """更新TTS配置"""
        try:
            # print(f"更新TTS配置: {config_updates}")
            
            # 更新配置
            for key, value in config_updates.items():
                if key in self.default_config:
                    # 确保数值类型正确
                    if key != 'voice':
                        try:
                            value = int(value)
                            # 限制范围
                            value = max(-100, min(100, value))
                        except (ValueError, TypeError):
                            print(f"配置值 {key}={value} 无效，跳过")
                            continue
                    
                    self.default_config[key] = value
                    # print(f"更新配置 {key} = {value}")
                else:
                    print(f"未知配置项: {key}")
            
            # 保存配置
            if self._save_config():
                # print(f"TTS配置更新成功: {self.default_config}")
                return self._success_response("TTS配置更新成功", self.default_config)
            else:
                return self._error_response("保存配置失败")

        except Exception as e:
            print(f"更新TTS配置失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return self._error_response(f"更新TTS配置失败: {str(e)}")

    