#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import platform
import uuid
import subprocess
import hashlib
import re
import logging
from typing import Dict, List, Optional, Tuple
import sys

# 在Windows环境中添加隐藏窗口的支持
is_windows = platform.system().lower() == 'windows'

# --- 推荐安装的依赖 ---
# 为了在Windows上获得最佳性能和稳定性（无黑窗口），请安装WMI库:
# pip install WMI pywin32
#
# 为了更可靠地获取MAC地址，请安装psutil库:
# pip install psutil
# --------------------

class HardwareIdentifier:
    """
    跨平台硬件标识符生成器 (优化版)
    - 增强了在不同执行环境下的稳定性和一致性。
    - 优先使用Python库（WMI, psutil）而非命令行，减少环境依赖。
    - 提供了详细的日志记录，便于调试ID变化问题。
    """

    # 定义主要和次要标识符的键
    PRIMARY_KEYS = {'mb', 'bios', 'cpu'}
    SECONDARY_KEYS = {'mac', 'vol'}

    def __init__(self, enable_logging=False):
        """
        初始化硬件标识符生成器
        :param enable_logging: 是否开启日志记录，便于调试
        """
        self.system = platform.system().lower()
        self.logger = logging.getLogger("HardwareIdentifier")
        if enable_logging and not self.logger.handlers:
            logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # 尝试导入可选的但推荐的库
        self._wmi_service = None
        self._psutil_import = None
        if self.system == 'windows':
            try:
                import wmi
                self._wmi_service = wmi.WMI()
                # self.logger.info("WMI库加载成功，将优先使用WMI获取硬件信息。")
            except ImportError:
                pass
                # self.logger.warning("未安装WMI库(pip install WMI)，将回退到命令行方式，可能出现黑窗口或权限问题。")
        try:
            import psutil
            self._psutil_import = psutil
            # self.logger.info("psutil库加载成功，将用于获取MAC地址。")
        except ImportError:
            pass
            # self.logger.warning("未安装psutil库(pip install psutil)，将回退到备用方法获取MAC地址。")


    def get_machine_id(self) -> str:
        """
        获取机器ID - 使用基于权重的混合标识符策略。
        此方法现在是一个编排器，将具体实现委托给平台特定的方法。
        """
        try:
            all_ids = self._gather_all_identifiers()

            # 分离主要和次要标识符
            primary_ids = {k: v for k, v in all_ids.items() if k in self.PRIMARY_KEYS and v}
            secondary_ids = {k: v for k, v in all_ids.items() if k in self.SECONDARY_KEYS and v}

            # self.logger.info(f"收集到的原始标识符: {all_ids}")
            # self.logger.info(f"筛选出的主要标识符: {primary_ids}")
            # self.logger.info(f"筛选出的次要标识符: {secondary_ids}")

            primary_str = ":".join(f"{k}={v}" for k, v in sorted(primary_ids.items()))
            secondary_str = ":".join(f"{k}={v}" for k, v in sorted(secondary_ids.items()))

            if primary_str:
                # 策略1: 主要标识符存在，使用它们构建ID
                primary_hash = hashlib.sha256(primary_str.encode()).hexdigest()[:24]

                if secondary_str:
                    secondary_hash = hashlib.sha256(secondary_str.encode()).hexdigest()[:8]
                else:
                    # 如果没有次要标识符，使用系统信息作为补充
                    sys_info = f"{platform.system()}:{platform.machine()}"
                    secondary_hash = hashlib.sha256(sys_info.encode()).hexdigest()[:8]

                machine_id = f"{primary_hash}{secondary_hash}"
                # self.logger.info(f"生成机器ID(主要策略): {machine_id} (基于 {len(primary_ids)} 个主要和 {len(secondary_ids)} 个次要ID)")

            elif secondary_str:
                # 策略2: 主要标识符缺失，但次要标识符存在
                combined_str = f"{secondary_str}:{platform.system()}:{platform.node()}:{platform.machine()}"
                machine_id = hashlib.sha256(combined_str.encode()).hexdigest()
                # self.logger.warning(f"主要标识符缺失！生成机器ID(次要策略): {machine_id} (稳定性可能较差)")

            else:
                # 策略3: 极端情况，所有硬件信息都获取失败
                # self.logger.error("无法获取任何有效的硬件标识符！使用最终备用方案。")
                machine_id = self._fallback_machine_id()

            return machine_id

        except Exception as e:
            # self.logger.error(f"生成机器ID时发生未知错误: {e}", exc_info=True)
            return self._fallback_machine_id()

    def _gather_all_identifiers(self) -> Dict[str, Optional[str]]:
        """根据操作系统，调用相应的函数收集所有硬件ID"""
        ids = {key: None for key in self.PRIMARY_KEYS | self.SECONDARY_KEYS}

        if self.system == 'windows':
            ids.update(self._get_windows_identifiers())
        elif self.system == 'linux':
            ids.update(self._get_linux_identifiers())
        elif self.system == 'darwin':
            ids.update(self._get_darwin_identifiers())

        # MAC地址使用统一的psutil方法（如果可用）
        ids['mac'] = self._get_primary_mac()

        # 清理无效值
        for key, value in ids.items():
            if value and any(s in value for s in ['None', 'To be filled by O.E.M.', '00000000-0000-0000-0000-000000000000']):
                ids[key] = None
        return ids

    # --- 平台特定的实现 ---

    def _get_windows_identifiers(self) -> Dict[str, Optional[str]]:
        """获取Windows平台的硬件ID。优先使用WMI，失败则回退到CLI。"""
        if self._wmi_service:
            try:
                # 使用WMI一次性获取所有信息，效率更高
                cs_product = self._wmi_service.Win32_ComputerSystemProduct()[0]
                baseboard = self._wmi_service.Win32_BaseBoard()[0]
                cpu = self._wmi_service.Win32_Processor()[0]
                
                # 获取系统盘 - 不再硬编码为C:
                system_drive = os.environ.get('SystemDrive', 'C:')
                disk = self._wmi_service.Win32_LogicalDisk(DeviceID=system_drive)[0]

                return {
                    'bios': cs_product.UUID,
                    'mb': baseboard.SerialNumber.strip(),
                    'cpu': cpu.ProcessorId,
                    'vol': disk.VolumeSerialNumber,
                }
            except Exception as e:
                pass
                # self.logger.error(f"使用WMI获取信息失败: {e}。将尝试回退到命令行工具。")

        # WMI失败或不可用时的命令行回退方案
        return {
            'bios': self._run_command_and_parse(['wmic', 'csproduct', 'get', 'UUID']),
            'mb': self._run_command_and_parse(['wmic', 'baseboard', 'get', 'serialnumber']),
            'cpu': self._run_command_and_parse(['wmic', 'cpu', 'get', 'processorid']),
            'vol': self._get_windows_vol_cli(),
        }

    def _get_linux_identifiers(self) -> Dict[str, Optional[str]]:
        """获取Linux平台的硬件ID。"""
        # 尝试无需sudo的方法
        mb_id = self._run_command_and_parse(['cat', '/sys/devices/virtual/dmi/id/board_serial'])
        if not mb_id:
            mb_id = self._run_command_and_parse(['sudo', 'dmidecode', '-s', 'baseboard-serial-number'])
        
        bios_id = self._run_command_and_parse(['cat', '/sys/devices/virtual/dmi/id/product_uuid'])
        if not bios_id:
            bios_id = self._run_command_and_parse(['sudo', 'dmidecode', '-s', 'system-uuid'])
            
        vol_id = self._run_command_and_parse(['lsblk', '-no', 'UUID', '/dev/sda1'])
        if not vol_id:
            vol_id = self._run_command_and_parse(['sudo', 'blkid', '-o', 'value', '-s', 'UUID', '/'])
            
        return {
            'bios': bios_id,
            'mb': mb_id,
            'cpu': self._get_linux_cpu_id(),  # /proc/cpuinfo不需要特权
            'vol': vol_id,
        }

    def _get_darwin_identifiers(self) -> Dict[str, Optional[str]]:
        """获取macOS平台的硬件ID。"""
        hw_info = self._run_command(['system_profiler', 'SPHardwareDataType'])
        return {
            'bios': self._parse_darwin_info(hw_info, 'Hardware UUID'),
            'mb': self._parse_darwin_info(hw_info, 'Serial Number (system)'),
            'cpu': self._run_command_and_parse(['sysctl', '-n', 'machdep.cpu.brand_string']),
            'vol': self._parse_darwin_info(self._run_command(['diskutil', 'info', '/']), 'Volume UUID'),
        }

    # --- 辅助方法 ---

    def _run_command(self, cmd: List[str], timeout: int = 3) -> Optional[str]:
        """执行命令并返回其标准输出，带日志记录。"""
        kwargs = {
            'capture_output': True,
            'text': True,
            'timeout': timeout,
            'encoding': 'utf-8',
            'errors': 'ignore'
        }
        
        # 仅在Windows上使用隐藏窗口的特殊参数
        if self.system == 'windows':
            try:
                from subprocess import STARTUPINFO, STARTF_USESHOWWINDOW, SW_HIDE
                startupinfo = STARTUPINFO()
                startupinfo.dwFlags |= STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = SW_HIDE
                kwargs['startupinfo'] = startupinfo
                kwargs['creationflags'] = 0x08000000  # CREATE_NO_WINDOW
            except (ImportError, AttributeError):
                # 如果导入失败，忽略隐藏窗口功能
                pass

        try:
            # 安全运行命令
            result = subprocess.run(cmd, **kwargs)
            if result.returncode != 0:
                pass
                # self.logger.warning(f"命令 {cmd} 返回非零状态码: {result.returncode}")
            return result.stdout
        except Exception as e:
            pass
            # self.logger.error(f"执行命令 {cmd} 时出错: {e}")
            return None

    def _run_command_and_parse(self, cmd: List[str]) -> Optional[str]:
        """执行命令，提取第一个非空行并返回，过滤掉标题文本。"""
        output = self._run_command(cmd)
        if not output:
            return None
        
        # 尝试查找实际内容并过滤标头
        lines = [line.strip() for line in output.strip().split('\n') if line.strip()]
        if not lines:
            return None
            
        # 如果输出看起来是WMIC格式（第一行是标题），则返回第二行，否则返回第一行
        content = lines[1] if len(lines) > 1 and any(cmd_part == 'wmic' for cmd_part in cmd) else lines[0]
        return content.strip()

    def _get_windows_vol_cli(self) -> Optional[str]:
        """获取Windows卷序列号的备用方法"""
        try:
            # 使用环境变量获取系统盘符
            system_drive = os.environ.get('SystemDrive', 'C:')
            # 运行vol命令获取卷序列号
            output = self._run_command(['cmd', '/c', 'vol', system_drive])
            if output:
                # 提取卷序列号
                match = re.search(r'(?:Serial Number is|卷序列号是)\s+([0-9A-F-]+)', output)
                if match:
                    return match.group(1).replace('-', '')
        except Exception as e:
            pass
            # self.logger.error(f"获取Windows卷序列号失败: {e}")
        return None

    def _get_linux_cpu_id(self) -> Optional[str]:
        """尝试从/proc/cpuinfo中提取CPU ID"""
        try:
            with open('/proc/cpuinfo', 'r') as f:
                cpuinfo = f.read()
                
            for id_field in ['serial', 'Serial', 'model name', 'cpu model', 'Processor']:
                match = re.search(rf'{id_field}\s+:\s+([^\n]+)', cpuinfo)
                if match:
                    return match.group(1).strip()
        except Exception as e:
            # self.logger.error(f"读取CPU信息失败: {e}")
            pass
        return None

    def _parse_darwin_info(self, info: Optional[str], key: str) -> Optional[str]:
        """从macOS系统信息输出中提取特定键的值"""
        if not info:
            return None
        match = re.search(rf'{key}:\s+([^\n]+)', info)
        return match.group(1).strip() if match else None

    def _get_primary_mac(self) -> Optional[str]:
        """获取主要网络接口的MAC地址（跨平台方法）"""
        # 使用psutil库获取
        if self._psutil_import:
            try:
                import psutil
                for iface, addrs in psutil.net_if_addrs().items():
                    for addr in addrs:
                        if addr.family == psutil.AF_LINK and self._is_valid_mac(addr.address):
                            return addr.address.replace(':', '')
            except Exception as e:
                # self.logger.error(f"通过psutil获取MAC地址失败: {e}")
                pass
        
        # 尝试平台特定的备用方法
        try:
            if self.system == 'windows':
                output = self._run_command(['getmac', '/v', '/fo', 'csv'])
                if output:
                    for line in output.splitlines()[1:]:  # 跳过标题行
                        parts = line.strip('"').split('","')
                        if len(parts) >= 3:
                            mac = parts[2].strip('"')
                            if self._is_valid_mac(mac):
                                return mac.replace('-', '')
            elif self.system == 'linux':
                output = self._run_command(['ip', 'link', 'show'])
                if output:
                    for line in output.splitlines():
                        if 'link/ether' in line:
                            mac = line.split('link/ether')[1].split()[0].strip()
                            if self._is_valid_mac(mac):
                                return mac.replace(':', '')
            elif self.system == 'darwin':
                output = self._run_command(['ifconfig', 'en0'])
                if output:
                    match = re.search(r'ether\s+([0-9a-f:]+)', output)
                    if match and self._is_valid_mac(match.group(1)):
                        return match.group(1).replace(':', '')
        except Exception as e:
            # self.logger.error(f"通过备用方法获取MAC地址失败: {e}")
            pass
        return None
        
    def _is_valid_mac(self, mac: str) -> bool:
        """验证MAC地址有效性并排除虚拟接口"""
        if not mac:
            return False
            
        # 规范化MAC地址格式
        mac = mac.lower().replace('-', ':').replace('.', ':')
        
        # 检查格式是否有效
        if not re.match(r'^([0-9a-f]{2}[:-]){5}([0-9a-f]{2})$', mac):
            return False
            
        # 排除保留和虚拟MAC
        virtual_prefixes = {'00:05:69', '00:50:56', '00:0c:29', '00:1c:42', '08:00:27', '00:15:5d'}
        mac_prefix = mac[:8]
        if mac_prefix in virtual_prefixes:
            # self.logger.info(f"跳过虚拟MAC地址: {mac}")

            return False

            
        # 排除全零MAC
        if mac.count('00:00:00') > 0:
            return False
            
        return True

    def _fallback_machine_id(self) -> str:
        """最后的备用方案：使用半永久性标识符和系统信息"""
        components = []
        
        # 尝试获取hostname
        try:
            hostname = platform.node()
            if hostname:
                components.append(f"host={hostname}")
        except:
            pass
            
        # 系统类型和版本信息
        try:
            os_info = f"{platform.system()}-{platform.release()}"
            components.append(f"os={os_info}")
        except:
            pass
            
        # 尝试获取Python路径作为安装位置标识
        try:
            py_path = sys.executable
            if py_path:
                components.append(f"py={py_path}")
        except:
            pass
            
        # 创建一个UUID并存储
        import uuid
        machine_uuid = str(uuid.uuid4())
        components.append(f"uuid={machine_uuid}")
        
        # 生成一致的ID
        id_str = ":".join(components)
        return hashlib.sha256(id_str.encode()).hexdigest()[:32]

# --- 使用示例 ---
if __name__ == '__main__':
    print("正在生成硬件标识符...")
    # 创建实例，日志会默认打印到控制台
    identifier = HardwareIdentifier(enable_logging=False)
    machine_id = identifier.get_machine_id()

    print("\n" + "="*50)
    print(f"生成的机器ID: {machine_id}")
    print(f"ID长度: {len(machine_id)}")
    print("="*50)

    # 你可以运行此脚本两次，一次在普通命令行，一次在打包后的应用中，
    # 对比日志输出来找出ID不一致的原因。