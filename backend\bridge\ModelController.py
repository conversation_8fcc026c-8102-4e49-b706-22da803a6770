from openai import OpenAI
import json
import base64
import os
import time
import random
from threading import Lock
from typing import Dict, Optional, List
from backend.bridge.Base import ResponsePacket
import httpx

class ChatSession:
    """聊天会话类"""
    def __init__(self, chat_id, model_id):
        self.chat_id = chat_id
        self.model_id = model_id
        self.messages = []
        self.is_active = False
        self.current_response = None
        self.last_updated = time.time()
        self.title = f"新对话 {chat_id[:5]}"  # 默认标题
        self.memory_enabled = True  # 默认启用记忆模式
        self.last_save_time = 0  # 上次保存时间
        self.content_buffer_size = 0  # 内容缓冲区大小，用于跟踪自上次保存以来的变化
        self.modified = False  # 标记是否有未保存的更改

class ModelController(ResponsePacket):
    def __init__(self, api_key, base_url, base_dir):
        self.data_dir = os.path.join(base_dir, "chat")
        self.roles_file = os.path.join(self.data_dir, "ai_roles.json")
        self.providers_file = os.path.join(self.data_dir, "ai_providers.json")
        self.api_key = api_key
        self.base_url = base_url
        self.sessions = {}
        self.client_lock = Lock()
        self.roles = []
        self.providers = []
        self.providers_lock = Lock()  # 添加锁以保护providers的并发访问

        # 客户端缓存
        self.client_cache = {}  # 缓存 OpenAI 客户端
        self.http_client_cache = {}  # 缓存 httpx 客户端
        self.cache_lock = Lock()  # 缓存锁
        
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 初始化配置文件和内存状态
        self._init_providers_config()
        self._init_roles_config()
            
        # 检查 API 配置
        if not api_key:
            print("Warning: OpenAI API key not provided")
        if not base_url:
            print("Warning: OpenAI base URL not provided")
            
        # 使用默认API配置初始化客户端
        self.client = None
        
        # 会话管理
        self.sessions: Dict[str, ChatSession] = {}
        self.sessions_lock = Lock()
        self._load_sessions()

    def _generate_cache_key(self, api_key, base_url, proxy_config):
        """生成客户端缓存键"""
        proxy_key = ""
        if proxy_config and proxy_config.get('enabled', False):
            proxy_url = proxy_config.get('url', '')
            proxy_username = proxy_config.get('username', '')
            proxy_password = proxy_config.get('password', '')
            proxy_key = f"{proxy_url}:{proxy_username}:{proxy_password}"

        return f"{api_key}:{base_url}:{proxy_key}"

    def _generate_provider_cache_key(self, base_url, proxy_config):
        """生成服务商级别的缓存键（不包含具体API密钥）"""
        proxy_key = ""
        if proxy_config and proxy_config.get('enabled', False):
            proxy_url = proxy_config.get('url', '')
            proxy_username = proxy_config.get('username', '')
            proxy_password = proxy_config.get('password', '')
            proxy_key = f"{proxy_url}:{proxy_username}:{proxy_password}"

        return f"provider:{base_url}:{proxy_key}"

    def _get_cached_client(self, api_key, base_url, proxy_config):
        """获取缓存的客户端，如果不存在则创建"""
        cache_key = self._generate_cache_key(api_key, base_url, proxy_config)

        with self.cache_lock:
            # 检查是否已有缓存的客户端
            if cache_key in self.client_cache:
                print(f"使用缓存的客户端: {base_url}")
                return self.client_cache[cache_key]

            # 创建新的客户端
            print(f"创建新的客户端: {base_url}")
            client_kwargs = {
                'api_key': api_key,
                'base_url': base_url
            }

            # 创建 HTTP 客户端
            if proxy_config and proxy_config.get('enabled', False):
                # 配置代理
                proxy_url = proxy_config.get('url', '')
                proxy_username = proxy_config.get('username', '')
                proxy_password = proxy_config.get('password', '')

                if proxy_url:
                    # 构建代理URL
                    if proxy_username and proxy_password:
                        if '://' in proxy_url:
                            protocol, rest = proxy_url.split('://', 1)
                            proxy_url = f"{protocol}://{proxy_username}:{proxy_password}@{rest}"
                        else:
                            proxy_url = f"http://{proxy_username}:{proxy_password}@{proxy_url}"
                    elif not proxy_url.startswith(('http://', 'https://', 'socks5://')):
                        proxy_url = f"http://{proxy_url}"

                    print(f"使用代理: {proxy_url.split('@')[-1] if '@' in proxy_url else proxy_url}")

                    http_client = httpx.Client(
                        proxies=proxy_url,
                        timeout=proxy_config.get('timeout', 30.0),
                        verify=proxy_config.get('verify_ssl', True)
                    )
                    client_kwargs['http_client'] = http_client
            else:
                # 明确禁用代理
                print("禁用代理，直接连接")
                http_client = httpx.Client(
                    proxies=None,
                    timeout=30.0,
                    trust_env=False
                )
                client_kwargs['http_client'] = http_client

            # 创建 OpenAI 客户端
            client = OpenAI(**client_kwargs)

            # 缓存客户端
            self.client_cache[cache_key] = client

            return client

    def _get_load_balanced_client(self, provider_config, base_url, proxy_config, retry_count=0):
        """获取支持负载均衡的客户端，支持错误重试"""
        import random

        # 获取所有可用的API密钥
        api_keys = provider_config.get('apiKeys', [])
        if not api_keys:
            print("没有可用的API密钥")
            return None

        # 过滤出活跃的密钥
        active_keys = [key for key in api_keys if key.get('status') == 'active']
        if not active_keys:
            print("没有活跃的API密钥，使用所有密钥")
            active_keys = api_keys

        # 创建加权列表进行负载均衡
        weighted_keys = []
        for key in active_keys:
            weight = key.get('weight', 1)
            # 确保权重至少为1
            weight = max(1, weight)
            for _ in range(weight):
                weighted_keys.append(key)

        if not weighted_keys:
            print("无可用API密钥")
            return None

        # 随机选择一个密钥（负载均衡）
        selected_key = random.choice(weighted_keys)
        selected_key_id = selected_key.get('id', 'unknown')
        api_key = selected_key.get('key', '')

        if not api_key:
            print(f"API密钥 {selected_key_id} 值为空")
            return None

        print(f"负载均衡选择API密钥ID: {selected_key_id} (重试次数: {retry_count})")

        # 检查是否有该密钥的缓存客户端
        cache_key = self._generate_cache_key(api_key, base_url, proxy_config)

        with self.cache_lock:
            if cache_key in self.client_cache:
                print(f"使用缓存的客户端 (密钥: {selected_key_id})")
                return self.client_cache[cache_key]

        # 如果没有缓存，创建新客户端
        return self._get_cached_client(api_key, base_url, proxy_config)

    def _get_load_balanced_client_with_fallback(self, provider_config, base_url, proxy_config, max_retries=3):
        """获取负载均衡客户端，支持失败回退"""
        import random

        # 获取所有可用的API密钥
        api_keys = provider_config.get('apiKeys', [])
        if not api_keys:
            print("没有可用的API密钥")
            return None

        # 过滤出活跃的密钥
        active_keys = [key for key in api_keys if key.get('status') == 'active']
        if not active_keys:
            print("没有活跃的API密钥，使用所有密钥")
            active_keys = api_keys

        # 尝试多次获取可用客户端
        tried_keys = set()
        for attempt in range(max_retries):
            # 创建加权列表进行负载均衡（排除已尝试的密钥）
            available_keys = [key for key in active_keys if key.get('id') not in tried_keys]
            if not available_keys:
                print(f"所有密钥都已尝试过，重置尝试列表")
                tried_keys.clear()
                available_keys = active_keys

            weighted_keys = []
            for key in available_keys:
                weight = key.get('weight', 1)
                weight = max(1, weight)
                for _ in range(weight):
                    weighted_keys.append(key)

            if not weighted_keys:
                print("无可用API密钥")
                return None

            # 随机选择一个密钥
            selected_key = random.choice(weighted_keys)
            selected_key_id = selected_key.get('id', 'unknown')
            api_key = selected_key.get('key', '')

            tried_keys.add(selected_key_id)

            if not api_key:
                print(f"API密钥 {selected_key_id} 值为空，尝试下一个")
                continue

            print(f"负载均衡选择API密钥ID: {selected_key_id} (尝试 {attempt + 1}/{max_retries})")

            try:
                # 检查是否有该密钥的缓存客户端
                cache_key = self._generate_cache_key(api_key, base_url, proxy_config)

                with self.cache_lock:
                    if cache_key in self.client_cache:
                        print(f"使用缓存的客户端 (密钥: {selected_key_id})")
                        return self.client_cache[cache_key]

                # 如果没有缓存，创建新客户端
                client = self._get_cached_client(api_key, base_url, proxy_config)
                if client:
                    return client

            except Exception as e:
                print(f"创建客户端失败 (密钥: {selected_key_id}): {str(e)}")
                continue

        print(f"经过 {max_retries} 次尝试后仍无法获取可用客户端")
        return None

    def clear_client_cache(self):
        """清理客户端缓存"""
        with self.cache_lock:
            for client in self.client_cache.values():
                try:
                    if hasattr(client, '_client') and hasattr(client._client, 'close'):
                        client._client.close()
                except:
                    pass
            self.client_cache.clear()
            print("客户端缓存已清理")

    def __del__(self):
        """析构函数，清理资源"""
        try:
            self.clear_client_cache()
        except:
            pass

    def _init_providers_config(self):
        """初始化AI服务商配置，确保文件存在且格式正确"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.providers_file), exist_ok=True)
            
            # 检查文件是否存在
            if not os.path.exists(self.providers_file):
                # 文件不存在，创建默认配置
                default_providers = [
                    {
                        "id": "default",
                        "name": "默认OpenAI服务",
                        "baseUrl": "https://api.openai.com/v1",
                        "apiKeys": [],
                        "models": [
                            {"id": "gpt-4", "available": True},
                            {"id": "gpt-4-turbo", "available": True},
                            {"id": "gpt-4o", "available": True},
                            {"id": "gpt-3.5-turbo", "available": True}
                        ],
                        "proxy": {
                            "enabled": False,
                            "url": "",
                            "username": "",
                            "password": "",
                            "timeout": 30.0,
                            "verify_ssl": True
                        }
                    }
                ]
                
                # 安全写入文件
                self._safe_write_json(self.providers_file, default_providers)
                self.providers = default_providers
                print(f"已创建默认AI服务商配置文件: {self.providers_file}")
                return
            
            # 文件存在，尝试加载
            try:
                with open(self.providers_file, 'r', encoding='utf-8') as f:
                    providers = json.load(f)
                
                # 验证数据格式
                if not isinstance(providers, list):
                    print(f"AI服务商配置文件格式错误，应为数组而非 {type(providers).__name__}")
                    # 创建新的默认配置
                    default_providers = [
                        {
                            "id": "default",
                            "name": "默认OpenAI服务",
                            "baseUrl": "https://api.openai.com/v1",
                            "apiKeys": [],
                            "models": [
                                {"id": "gpt-4", "available": True},
                                {"id": "gpt-4-turbo", "available": True},
                                {"id": "gpt-4o", "available": True},
                                {"id": "gpt-3.5-turbo", "available": True}
                            ],
                            "proxy": {
                                "enabled": False,
                                "url": "",
                                "username": "",
                                "password": "",
                                "timeout": 30.0,
                                "verify_ssl": True
                            }
                        }
                    ]
                    self._safe_write_json(self.providers_file, default_providers)
                    self.providers = default_providers
                    print(f"已重置AI服务商配置文件: {self.providers_file}")
                else:
                    # 数据格式正确，更新内存状态
                    self.providers = providers
                    print(f"成功加载 {len(self.providers)} 个AI服务商配置")
            except json.JSONDecodeError as e:
                print(f"AI服务商配置文件JSON解析失败: {e}")
                # 创建新的默认配置
                default_providers = [
                    {
                        "id": "default",
                        "name": "默认OpenAI服务",
                        "baseUrl": "https://api.openai.com/v1",
                        "apiKeys": [],
                        "models": [
                            {"id": "gpt-4", "available": True},
                            {"id": "gpt-4-turbo", "available": True},
                            {"id": "gpt-4o", "available": True},
                            {"id": "gpt-3.5-turbo", "available": True}
                        ],
                        "proxy": {
                            "enabled": False,
                            "url": "",
                            "username": "",
                            "password": "",
                            "timeout": 30.0,
                            "verify_ssl": True
                        }
                    }
                ]
                self._safe_write_json(self.providers_file, default_providers)
                self.providers = default_providers
                print(f"已重置AI服务商配置文件: {self.providers_file}")
        except Exception as e:
            print(f"初始化AI服务商配置失败: {e}")
            # 确保内存中有默认配置
            self.providers = [
                {
                    "id": "default",
                    "name": "默认OpenAI服务",
                    "baseUrl": "https://api.openai.com/v1",
                    "apiKeys": [],
                    "models": [
                        {"id": "gpt-4", "available": True},
                        {"id": "gpt-4-turbo", "available": True},
                        {"id": "gpt-4o", "available": True},
                        {"id": "gpt-3.5-turbo", "available": True}
                    ]
                }
            ]

    def _init_roles_config(self):
        """初始化AI角色配置，确保文件存在且格式正确"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.roles_file), exist_ok=True)
            
            # 检查文件是否存在
            if not os.path.exists(self.roles_file):
                # 文件不存在，创建空配置
                self._safe_write_json(self.roles_file, [])
                self.roles = []
                print(f"已创建空的AI角色配置文件: {self.roles_file}")
                return
            
            # 文件存在，尝试加载
            try:
                with open(self.roles_file, 'r', encoding='utf-8') as f:
                    roles = json.load(f)
                
                # 验证数据格式
                if not isinstance(roles, list):
                    print(f"AI角色配置文件格式错误，应为数组而非 {type(roles).__name__}")
                    # 创建空配置
                    self._safe_write_json(self.roles_file, [])
                    self.roles = []
                    print(f"已重置AI角色配置文件: {self.roles_file}")
                else:
                    # 数据格式正确，更新内存状态
                    self.roles = roles
                    print(f"成功加载 {len(self.roles)} 个AI角色")
            except json.JSONDecodeError as e:
                print(f"AI角色配置文件JSON解析失败: {e}")
                # 创建空配置
                self._safe_write_json(self.roles_file, [])
                self.roles = []
                print(f"已重置AI角色配置文件: {self.roles_file}")
        except Exception as e:
            print(f"初始化AI角色配置失败: {e}")
            # 确保内存中有空配置
            self.roles = []

    def _safe_write_json(self, file_path, data):
        """安全地将JSON数据写入文件，使用临时文件和原子操作"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 使用临时文件
            temp_file = file_path + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 原子重命名操作
            if os.path.exists(file_path):
                os.remove(file_path)
            os.rename(temp_file, file_path)
            
            return True
        except Exception as e:
            print(f"安全写入JSON失败: {e}")
            return False



    def _atomic_save_json(self, file_path, data):
        """使用原子操作保存JSON数据"""
        try:
            # 确保数据是字典类型而非字符串
            if isinstance(data, str):
                try:
                    data = json.loads(data)
                except:
                    raise ValueError("无效的JSON字符串")
                
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 判断是否为配置文件，确定是哪种配置文件
            is_providers_config = "ai_providers.json" in file_path
            is_roles_config = "ai_roles.json" in file_path
            
            # 使用文件锁确保原子写入，避免多进程/线程同时写入
            lock_file = file_path + '.lock'
            lock_fd = None
            
            try:
                # 创建锁文件
                lock_fd = open(lock_file, 'w')
                
                # 使用跨平台的文件锁定机制
                import platform
                locking_successful = False
                
                # 尝试使用fcntl (Unix/Linux/Mac)
                if platform.system() != 'Windows':
                    try:
                        import fcntl
                        fcntl.flock(lock_fd, fcntl.LOCK_EX)
                        locking_successful = True
                    except ImportError:
                        print("警告: fcntl模块不可用，尝试其他锁定方式")
                
                # 尝试使用msvcrt (Windows)
                if platform.system() == 'Windows':
                    try:
                        import msvcrt
                        # 获取文件句柄
                        handle = msvcrt.get_osfhandle(lock_fd.fileno())
                        # 对句柄应用锁定，0x1表示排他锁定
                        msvcrt.locking(lock_fd.fileno(), msvcrt.LK_NBLCK, 1)
                        locking_successful = True
                    except (ImportError, OSError) as e:
                        print(f"警告: Windows锁定不可用 ({str(e)})")
                
                # 如果标准锁定方法都失败，尝试使用文件存在作为简单锁
                if not locking_successful:
                    print("使用简单文件锁作为备用方案")
                    # 简单锁：检查是否有其他进程正在使用
                    lock_flag_file = lock_file + ".flag"
                    if os.path.exists(lock_flag_file):
                        # 检查锁文件是否过期(超过30秒)
                        if time.time() - os.path.getctime(lock_flag_file) > 30:
                            os.remove(lock_flag_file)
                        else:
                            print("文件已被锁定，等待解锁...")
                            # 等待最多3秒
                            start_time = time.time()
                            while os.path.exists(lock_flag_file) and time.time() - start_time < 3:
                                time.sleep(0.1)
                            if os.path.exists(lock_flag_file):
                                os.remove(lock_flag_file)  # 强制移除锁
                    
                    # 创建锁标志文件
                    with open(lock_flag_file, 'w') as f:
                        f.write(str(os.getpid()))
                
                # 使用原子写入方式避免写入冲突
                temp_file = file_path + '.tmp'
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                    
                # 原子重命名操作
                if os.path.exists(file_path):
                    os.remove(file_path)
                os.rename(temp_file, file_path)
                
                # 记录写入的配置文件类型，方便调试
                if is_providers_config:
                    print(f"已更新AI服务商配置: {file_path}")
                elif is_roles_config:
                    print(f"已更新AI角色配置: {file_path}")
                else:
                    # print(f"已更新配置: {file_path}")
                    pass
                return True
            finally:
                # 确保释放文件锁
                if lock_fd:
                    # 先关闭文件
                    lock_fd.close()
                    
                    # 释放基于fcntl的锁会自动完成
                    
                    # 对于Windows msvcrt锁，文件关闭时会自动释放
                    
                    # 如果使用了简单锁，移除锁标志文件
                    lock_flag_file = lock_file + ".flag"
                    if os.path.exists(lock_flag_file):
                        try:
                            os.remove(lock_flag_file)
                        except:
                            pass
                    
                    # 移除锁文件
                    try:
                        os.remove(lock_file)
                    except:
                        pass
        except Exception as e:
            print(f"保存JSON失败: {str(e)}")
            return False



    def _load_json(self, file_path, default_value=None):
        """加载JSON文件，如果文件不存在则返回默认值"""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return default_value
        except Exception as e:
            print(f"加载JSON失败: {str(e)}")
            return default_value

    def _ensure_roles_file(self):
        """确保AI角色配置文件存在"""
        if not os.path.exists(self.roles_file):
            os.makedirs(os.path.dirname(self.roles_file), exist_ok=True)
            self._atomic_save_json(self.roles_file, [])
            print(f"创建空的AI角色配置文件: {self.roles_file}")

    def _ensure_providers_file(self):
        """确保AI服务商配置文件存在且有效"""
        create_default = False
        
        # 检查文件是否存在
        if not os.path.exists(self.providers_file):
            create_default = True
        else:
            # 文件存在，检查内容是否有效
            try:
                with open(self.providers_file, 'r', encoding='utf-8') as f:
                    providers = json.load(f)
                # 验证数据格式
                if not isinstance(providers, list) or len(providers) == 0:
                    print(f"AI服务商配置文件格式错误或为空，将重置为默认配置")
                    create_default = True
            except (json.JSONDecodeError, IOError) as e:
                print(f"读取AI服务商配置文件失败: {str(e)}，将重置为默认配置")
                create_default = True
        
        # 只有在需要创建默认配置时才执行
        if create_default:
            os.makedirs(os.path.dirname(self.providers_file), exist_ok=True)
            
            # 创建默认的服务商配置，不再使用isDefault字段
            default_providers = [
                {
                    "id": "default",
                    "name": "默认OpenAI服务",
                    "baseUrl": "https://api.openai.com/v1",
                    "apiKeys": [],
                    "models": [
                        {"id": "gpt-4", "available": True},
                        {"id": "gpt-4-turbo", "available": True},
                        {"id": "gpt-4o", "available": True},
                        {"id": "gpt-3.5-turbo", "available": True}
                    ],
                    "proxy": {
                        "enabled": False,
                        "url": "",
                        "username": "",
                        "password": "",
                        "timeout": 30.0,
                        "verify_ssl": True
                    }
                }
            ]
            
            self._atomic_save_json(self.providers_file, default_providers)
            print(f"创建默认AI服务商配置文件: {self.providers_file}")
        else:
            print(f"AI服务商配置文件已存在且有效，无需创建默认配置")

    def _load_providers(self):
        """加载AI服务商配置"""
        try:
            self.providers = self._load_json(self.providers_file, [])
            print(f"已加载 {len(self.providers)} 个AI服务商配置")
        except Exception as e:
            print(f"加载AI服务商配置失败: {str(e)}")
            self.providers = []

    def _get_client_for_model(self, model_unique_id: str):
        """根据模型唯一标识符选择合适的服务商和API密钥"""
        try:
            print(f"为模型 {model_unique_id} 选择合适的服务商和API密钥")

            # 解析模型唯一标识符
            # 前端格式: ${provider.id}:${model.id}
            # 需要只按第一个冒号分割，因为model.id可能包含冒号
            if ':' in model_unique_id:
                # 找到第一个冒号的位置
                colon_index = model_unique_id.find(':')
                provider_id = model_unique_id[:colon_index]
                model_id = model_unique_id[colon_index + 1:]
                print(f"解析模型标识符: 服务商ID={provider_id}, 模型ID={model_id}")
            else:
                # 兼容旧的模型ID格式，尝试查找匹配的模型
                model_id = model_unique_id
                provider_id = None
                print(f"使用兼容模式查找模型: {model_id}")

            # 如果没有providers配置，使用默认客户端
            if not self.providers:
                print("没有配置服务商，使用默认客户端")
                if not self.client:
                    print("警告: 默认客户端未配置，尝试创建一个基础客户端")
                    try:
                        return OpenAI(api_key=self.api_key or "sk-dummy", base_url=self.base_url or "https://api.openai.com/v1"), {}
                    except Exception as e:
                        print(f"创建基础客户端失败: {str(e)}")
                        return None, {}
                return self.client, {}
            
            # 找到所有支持此模型的服务商
            matching_providers = []
            model_config = {}  # 存储找到的模型配置

            # 如果指定了服务商ID，优先查找该服务商
            if provider_id:
                target_provider = None
                for provider in self.providers:
                    if provider.get('id') == provider_id:
                        target_provider = provider
                        break

                if target_provider and target_provider.get('apiKeys'):
                    # 在指定服务商中查找模型
                    provider_models = target_provider.get('models', [])
                    for model in provider_models:
                        if model.get('id') == model_id and model.get('available', True):
                            matching_providers.append(target_provider)
                            # 获取模型配置
                            model_config = model.get('config', {})
                            print(f"在指定服务商 {target_provider.get('name', 'unknown')} 中找到模型 {model_id}")
                            break
                else:
                    print(f"指定的服务商 {provider_id} 不存在或没有配置API密钥")

            # 如果没有指定服务商或在指定服务商中没找到，则在所有服务商中查找
            if not matching_providers:
                for provider in self.providers:
                    if not provider.get('apiKeys'):
                        print(f"服务商 {provider.get('name', 'unknown')} 没有配置API密钥，跳过")
                        continue

                    # 检查服务商的models列表是否包含这个模型
                    provider_models = provider.get('models', [])

                    for model in provider_models:
                        if model.get('id') == model_id and model.get('available', True):
                            matching_providers.append(provider)
                            # 获取模型配置
                            model_config = model.get('config', {})
                            print(f"找到支持模型 {model_id} 的服务商: {provider.get('name', 'unknown')}")
                            break

                    # 只有当服务商明确配置了模型列表且为空时，才假设支持所有模型
                    # 如果服务商根本没有配置models字段，则不假设支持任何模型
                    if 'models' not in provider:
                        # 服务商没有配置models字段，假设支持所有模型（向后兼容）
                        if provider.get('apiKeys'):
                            matching_providers.append(provider)
                            print(f"服务商 {provider.get('name', 'unknown')} 没有配置模型字段，假设支持所有模型")
                    elif not provider_models and provider.get('apiKeys'):
                        # 服务商配置了models字段但为空列表，不假设支持任何模型
                        print(f"服务商 {provider.get('name', 'unknown')} 配置了空的模型列表，跳过")
            
            if not matching_providers:
                # 如果没有找到匹配的服务商，使用任何有API密钥的服务商
                providers_with_keys = [p for p in self.providers if p.get('apiKeys')]
                if providers_with_keys:
                    matching_providers = [providers_with_keys[0]]
                    print(f"未找到匹配服务商，使用第一个可用服务商: {providers_with_keys[0].get('name', 'unknown')}")
                else:
                    print("没有找到任何配置有API密钥的服务商")
                    return None
            
            # 从匹配的服务商中随机选择一个
            target_provider = random.choice(matching_providers)
            print(f"选择服务商: {target_provider.get('name', 'unknown')}")

            # 检查服务商的base_url是否正确格式化
            base_url = target_provider.get('baseUrl', 'https://api.openai.com/v1')

            # 获取代理配置
            proxy_config = target_provider.get('proxy')

            # 使用改进的负载均衡客户端获取（支持失败回退）
            client = self._get_load_balanced_client_with_fallback(target_provider, base_url, proxy_config)
            if client:
                print(f"成功获取负载均衡API客户端, base_url={base_url}")
                return client, model_config
            else:
                print(f"无法获取服务商 {target_provider.get('name', 'Unknown')} 的客户端，尝试原始方法")
                # 回退到原始负载均衡方法
                client = self._get_load_balanced_client(target_provider, base_url, proxy_config)
                if client:
                    print(f"使用原始负载均衡方法成功获取客户端")
                    return client, model_config

                # 最后尝试使用默认客户端
                if self.client:
                    print("回退到默认客户端")
                    return self.client, {}
                return None, {}

        except Exception as e:
            print(f"选择API客户端失败: {str(e)}")
            # 出错时使用默认客户端
            if self.client:
                print("发生异常，回退到默认客户端")
                return self.client, {}
            return None, {}

    def _load_sessions(self):
        """加载所有保存的会话"""
        try:
            # 需要排除的特殊配置文件
            excluded_files = ["ai_providers.json", "ai_roles.json"]
            
            for filename in os.listdir(self.data_dir):
                if filename.endswith('.json') and filename not in excluded_files:
                    chat_id = filename[:-5]  # 移除 .json 后缀
                    filepath = os.path.join(self.data_dir, filename)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            # 验证是否是聊天数据而非配置文件
                            if not isinstance(data, dict) or 'messages' not in data:
                                print(f"跳过非聊天数据文件: {filename}")
                                continue
                                
                            session = ChatSession(chat_id, data.get('model_id', ''))
                            session.messages = data.get('messages', [])
                            session.last_updated = data.get('last_updated', time.time())
                            session.title = data.get('title', f"新对话 {chat_id[:5]}")
                            session.memory_enabled = data.get('memory_enabled', True)
                            self.sessions[chat_id] = session
                    except json.JSONDecodeError as e:
                        print(f"加载会话文件 {filename} 失败，JSON解析错误: {str(e)}")
                    except Exception as e:
                        print(f"加载会话文件 {filename} 失败: {str(e)}")
        except Exception as e:
            print(f"加载会话数据失败: {str(e)}")

    def _save_session(self, chat_id: str):
        """保存单个会话到文件，使用原子操作"""
        try:
            session = self.sessions.get(chat_id)
            if session:
                data = {
                    'chat_id': chat_id,
                    'model_id': session.model_id,
                    'messages': session.messages,
                    'last_updated': session.last_updated,
                    'title': session.title,
                    'memory_enabled': session.memory_enabled
                }
                filepath = os.path.join(self.data_dir, f"{chat_id}.json")
                return self._atomic_save_json(filepath, data)
        except Exception as e:
            print(f"保存会话数据失败: {str(e)}")
            return False



    def get_ai_providers(self):
        """获取所有AI服务商配置"""
        with self.providers_lock:
            # 直接返回内存中的配置，确保一致性
            return self._success_response("成功获取AI服务商配置", self.providers)
    
    def save_ai_providers(self, providers_data):
        """保存AI服务商配置"""
        try:
            # 验证数据格式
            if not isinstance(providers_data, list):
                return self._error_response("服务商数据必须是列表格式")

            # 验证每个服务商的必须字段
            for provider in providers_data:
                if not isinstance(provider, dict):
                    return self._error_response("每个服务商必须是字典格式")

                # 确保有id字段
                if 'id' not in provider:
                    provider['id'] = str(int(time.time() * 1000))

                # 移除isDefault字段，因为我们现在会使用所有服务商
                if 'isDefault' in provider:
                    del provider['isDefault']

            with self.providers_lock:
                # 安全写入新配置
                if not self._safe_write_json(self.providers_file, providers_data):
                    return self._error_response("保存配置文件失败")

                # 更新内存中的配置
                self.providers = providers_data

                # 清理客户端缓存，确保使用新配置
                self.clear_client_cache()

                return self._success_response("AI服务商配置已保存并生效")
        except Exception as e:
            print(f"保存AI服务商配置异常: {e}")
            return self._error_response(f"保存AI服务商配置失败: {e}")

    def update_ai_provider(self, provider_id, provider_data):
        """更新单个AI服务商配置"""
        try:
            # 验证数据格式
            if not isinstance(provider_data, dict):
                return self._error_response("服务商数据必须是字典格式")

            # 确保保留ID字段
            provider_data['id'] = provider_id

            # 移除isDefault字段，因为我们现在会使用所有服务商
            if 'isDefault' in provider_data:
                del provider_data['isDefault']

            with self.providers_lock:
                # 查找并更新服务商
                updated = False
                for i, provider in enumerate(self.providers):
                    if provider['id'] == provider_id:
                        # 更新内存中的服务商数据
                        self.providers[i] = provider_data
                        updated = True
                        updated_provider = self.providers[i]
                        break

                if not updated:
                    return self._error_response(f"未找到ID为{provider_id}的服务商")

                # 保存到文件
                if not self._safe_write_json(self.providers_file, self.providers):
                    return self._error_response("保存配置文件失败")

                # 清理客户端缓存，确保使用新配置
                self.clear_client_cache()

            return self._success_response("AI服务商配置已更新", updated_provider)
        except Exception as e:
            print(f"更新AI服务商配置异常: {e}")
            return self._error_response(f"更新AI服务商配置失败: {e}")

    def get_ai_roles(self):
        """获取所有AI角色"""
        try:
            # 直接返回内存中的配置，确保一致性
            return self._success_response("获取AI角色成功", self.roles)
        except Exception as e:
            print(f"获取AI角色失败: {e}")
            return self._error_response(f"获取AI角色失败: {e}")

    def add_ai_role(self, role_data):
        """添加新的AI角色"""
        try:
            # 生成新角色ID
            role_id = str(int(time.time() * 1000))
            role_data['id'] = role_id
            
            # 更新内存和文件
            with self.providers_lock:  # 复用现有锁保护文件操作
                # 添加到内存
                self.roles.append(role_data)
                
                # 保存到文件
                if not self._safe_write_json(self.roles_file, self.roles):
                    # 回滚内存修改
                    self.roles.pop()
                    return self._error_response("保存角色文件失败")
            
            return self._success_response("添加AI角色成功", role_data)
        except Exception as e:
            print(f"添加AI角色时出错: {e}")
            return self._error_response(f"添加AI角色失败: {e}")

    def update_ai_role(self, role_id, role_data):
        """更新AI角色"""
        try:
            with self.providers_lock:  # 复用现有锁保护文件操作
                # 查找并更新角色
                updated = False
                for i, role in enumerate(self.roles):
                    if role['id'] == role_id:
                        # 保留ID字段
                        role_data['id'] = role_id
                        # 更新内存
                        self.roles[i] = role_data
                        updated = True
                        updated_role = self.roles[i]
                        break
                
                if not updated:
                    return self._error_response(f"未找到ID为{role_id}的角色")
                
                # 保存到文件
                if not self._safe_write_json(self.roles_file, self.roles):
                    return self._error_response("保存角色文件失败")
            
            return self._success_response("更新AI角色成功", updated_role)
        except Exception as e:
            print(f"更新AI角色时出错: {e}")
            return self._error_response(f"更新AI角色失败: {e}")

    def delete_ai_role(self, role_id):
        """删除AI角色"""
        try:
            with self.providers_lock:  # 复用现有锁保护文件操作
                # 查找并删除角色
                original_length = len(self.roles)
                self.roles = [role for role in self.roles if role['id'] != role_id]
                
                if len(self.roles) == original_length:
                    return self._error_response(f"未找到ID为{role_id}的角色")
                
                # 保存到文件
                if not self._safe_write_json(self.roles_file, self.roles):
                    return self._error_response("保存角色文件失败")
            
            return self._success_response("删除AI角色成功", True)
        except Exception as e:
            print(f"删除AI角色时出错: {e}")
            return self._error_response(f"删除AI角色失败: {e}")

    def get_chat_history(self, chat_id: str):
        """获取指定会话的历史记录"""
        with self.sessions_lock:
            session = self.sessions.get(chat_id)
            if session:
                return self._success_response("获取历史记录成功", session.messages)
            return self._error_response("会话不存在")

    def stop_chat(self, chat_id: str):
        """停止指定的聊天会话"""
        try:
            with self.sessions_lock:
                session = self.sessions.get(chat_id)
                if session and session.is_active:
                    session.is_active = False  # 首先设置状态为非活动
                    session.current_response = None  # 清除响应对象
            return self._success_response("对话已停止", None)
        except Exception as e:
            return self._error_response(f"停止对话失败: {str(e)}")

    def _send_to_frontend(self, js_code):
        """安全地向前端发送消息"""
        try:
            import webview
            # 检查窗口是否存在
            if len(webview.windows) > 0:
                # 先检查函数是否存在
                check_js = f"""
                    if (typeof {js_code.split('(')[0]} === 'function') {{
                        {js_code}
                    }}
                """
                webview.windows[0].evaluate_js(check_js)
        except Exception as e:
            print(f"向前端发送消息失败: {str(e)}")

    def _send_error_to_frontend(self, chat_id, error_message):
        """发送错误消息到前端"""
        try:
            # 构建错误消息数据
            error_data = {
                'chat_id': chat_id,
                'error': True,
                'error_message': error_message
            }
            # 编码并发送
            b64content = base64.b64encode(json.dumps(error_data).encode()).decode()
            self._send_to_frontend(f"window.receiveChatError('{b64content}')")
        except Exception as e:
            print(f"向前端发送错误消息失败: {str(e)}")

    def chat(self, chat_id: str, model_unique_id: str, messages, config_data={}):
        """发送聊天请求，支持流式返回数据块"""
        try:
            # 选择合适的客户端并获取模型配置
            client, model_config = self._get_client_for_model(model_unique_id)

            # 检查是否有有效的客户端
            if not client:
                error_msg = "未找到可用的API客户端，请检查服务商配置和API密钥"
                # 发送错误消息到前端，避免前端一直等待
                self._send_error_to_frontend(chat_id, error_msg)
                return self._error_response(error_msg)

            # 合并配置：优先使用传入的配置，然后是模型配置，最后是默认配置
            final_config = {
                'top_p': 0.8,
                'temperature': 0.8,
                'max_tokens': 8192,
                'stream': True
            }

            # 应用模型配置
            if model_config:
                final_config.update(model_config)
                print(f"应用模型配置: {model_config}")

            # 应用传入的配置（优先级最高）
            if config_data:
                final_config.update(config_data)
                print(f"最终配置: {final_config}")

            # 解析实际的模型ID（去掉服务商前缀）
            actual_model_id = model_unique_id.split(':', 1)[-1] if ':' in model_unique_id else model_unique_id

            # 常量定义：保存间隔和缓冲区大小阈值
            SAVE_INTERVAL_SEC = 2.0  # 每2秒保存一次
            BUFFER_SIZE_THRESHOLD = 500  # 内容达到500字节时保存
            
            # 为这个聊天会话创建状态变量
            buffer_size = 0
            last_save_time = time.time()
            need_save = False

            # 1. 创建或获取会话，并保存初始状态
            with self.sessions_lock:
                session = self.sessions.get(chat_id)
                if not session:
                    session = ChatSession(chat_id, model_unique_id)
                    self.sessions[chat_id] = session

                # 更新会话状态
                session.model_id = model_unique_id
                session.messages = messages[:-1]  # 不包含最后一条空的assistant消息
                session.is_active = True
                session.last_updated = time.time()
                # 只有在初始化时保存会话
                self._save_session(chat_id)

            # 2. 准备发送请求
            top_p = final_config.get('top_p', 0.8)
            temperature = final_config.get('temperature', 0.8)
            max_tokens = final_config.get('max_tokens', 8192)
            stream = final_config.get('stream', True)

            # 3. 在会话中添加空的assistant回复
            with self.sessions_lock:
                session.messages.append({
                    'role': 'assistant',
                    'content': ''
                })
                # 这里也保存会话
                self._save_session(chat_id)

            try:
                # print(f"开始聊天 - 模型: {actual_model_id}, 会话ID: {chat_id}")
                # print(f"请求参数: temperature={temperature}, top_p={top_p}, max_tokens={max_tokens}, stream={stream}")
                # print(f"消息数量: {len(messages)}, 总字符数: {sum(len(str(msg)) for msg in messages)}")
                # print(f"完整消息内容: {messages}")

                # 4. 创建聊天完成请求
                response = client.chat.completions.create(
                    model=actual_model_id,
                    messages=messages,
                    top_p=top_p,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    stream=stream
                )
                session.current_response = response

                # 5. 流式响应处理
                if stream:
                    # 开始流式处理
                    try:
                        stream_finished = False
                        chunk_count = 0
                        for chunk in response:
                            chunk_count += 1
                            # 检查会话是否已被停止
                            if not session.is_active:
                                # print(f"会话已被手动停止，退出流式处理 (已处理{chunk_count}个chunk)")
                                break

                            # 打印chunk信息用于调试
                            # print(f"收到chunk #{chunk_count}: choices数量={len(chunk.choices) if hasattr(chunk, 'choices') and chunk.choices else 0}, session.is_active={session.is_active}")

                            # 检查chunk是否有choices且不为空
                            if hasattr(chunk, 'choices') and chunk.choices and len(chunk.choices) > 0:
                                choice = chunk.choices[0]

                                # 检查finish_reason来判断是否真正结束
                                if hasattr(choice, 'finish_reason') and choice.finish_reason:
                                    # print(f"检测到结束标志: {choice.finish_reason}")
                                    stream_finished = True
                                    # 即使有finish_reason，也要处理这个chunk的内容（如果有的话）

                                delta = choice.delta

                                # 创建响应数据字典
                                response_data = {
                                    'chat_id': chat_id,
                                }

                                # 处理content字段
                                if hasattr(delta, 'content') and delta.content:
                                    # 更新会话内容
                                    with self.sessions_lock:
                                        if session.is_active:  # 再次检查状态
                                            session.messages[-1]['content'] += delta.content
                                            need_save = True
                                            buffer_size += len(delta.content)

                                    response_data['content'] = delta.content
                                    # print(f"收到内容: {delta.content[:50]}...")

                                # 处理reasoning字段
                                if hasattr(delta, 'reasoning') and delta.reasoning:
                                    # 如果session.messages[-1]没有reasoning字段，添加一个
                                    with self.sessions_lock:
                                        if session.is_active:
                                            if 'reasoning' not in session.messages[-1]:
                                                session.messages[-1]['reasoning'] = ''
                                            session.messages[-1]['reasoning'] += delta.reasoning
                                            need_save = True
                                            buffer_size += len(delta.reasoning)

                                    response_data['reasoning'] = delta.reasoning
                                    # print(f"收到推理内容: {delta.reasoning[:50]}...")

                                # 只有当有数据要发送时才发送
                                if 'content' in response_data or 'reasoning' in response_data:
                                    b64content = base64.b64encode(json.dumps(response_data).encode()).decode()
                                    self._send_to_frontend(f"window.receiveChunk('{b64content}')")

                                # 检查是否应该保存当前状态
                                current_time = time.time()
                                if need_save and (current_time - last_save_time >= SAVE_INTERVAL_SEC or
                                                 buffer_size >= BUFFER_SIZE_THRESHOLD):
                                    with self.sessions_lock:
                                        if session.is_active:
                                            self._save_session(chat_id)
                                            need_save = False
                                            buffer_size = 0
                                            last_save_time = current_time

                                # 如果检测到结束标志，处理完当前chunk后退出
                                if stream_finished:
                                    # print("流式响应正常结束")
                                    break

                            else:
                                # 处理空的chunk或没有choices的chunk
                                print(f"收到空chunk或无choices的chunk，继续等待...")
                                # 不要break，继续处理下一个chunk
                        
                        # 流式响应完成，保存最终结果
                        # print(f"流式响应处理完成，stream_finished={stream_finished}")
                        with self.sessions_lock:
                            session.is_active = False
                            session.current_response = None

                            # 如果有待保存的内容，现在保存
                            if need_save:
                                self._save_session(chat_id)

                            # 通知前端消息完成
                            self._send_to_frontend(f"window.onMessageComplete('{chat_id}')")

                        if stream_finished:
                            # print("流式响应正常完成")
                            pass
                        else:
                            print("流式响应异常结束（可能被中断）")

                        return self._success_response("对话完成", {
                            'chat_id': chat_id,
                            'stream': True,
                            'finished_normally': stream_finished
                        })
                    except Exception as e:
                        # Clean up session state
                        with self.sessions_lock:
                            session.is_active = False
                            session.current_response = None
                            
                            # 保存当前进度以防数据丢失
                            if need_save:
                                self._save_session(chat_id)
                        
                        # Format error message
                        error_message = str(e)
                        print(f"流式响应处理异常: {error_message}")
                        
                        # Send error to frontend
                        self._send_error_to_frontend(chat_id, f"生成回复时出错: {error_message}")
                        
                        # Continue to raise for outer exception handler
                        raise e
                else:
                    # 对于非流式响应，等待完成并返回结果
                    response = response
                    if response.choices and len(response.choices) > 0:
                        content = response.choices[0].message.content
                        reasoning = None
                        # 检查是否有reasoning字段
                        if hasattr(response.choices[0].message, 'reasoning'):
                            reasoning = response.choices[0].message.reasoning
                        
                        # 更新会话消息
                        with self.sessions_lock:
                            session.messages[-1]['content'] = content
                            if reasoning:
                                session.messages[-1]['reasoning'] = reasoning
                            self._save_session(chat_id)
                            session.is_active = False
                        
                        result_data = {
                            'chat_id': chat_id,
                            'content': content,
                            'stream': False
                        }
                        if reasoning:
                            result_data['reasoning'] = reasoning
                            
                        return self._success_response("对话完成", result_data)
                    else:
                        return self._error_response("未收到有效回复")

            except Exception as e:
                # 清理会话状态
                with self.sessions_lock:
                    session.is_active = False
                    session.current_response = None
                    session.messages.pop()  # 移除空的assistant回复
                    self._save_session(chat_id)
                
                # 处理特定错误类型，提供更友好的错误信息
                error_message = str(e)
                if "404" in error_message:
                    if "/v1/chat/completions" in error_message:
                        error_msg = f"模型 {actual_model_id} 不可用或API端点错误。请检查API配置或选择其他模型。"
                        self._send_error_to_frontend(chat_id, error_msg)
                        return self._error_response(error_msg)
                    else:
                        error_msg = f"API端点错误: {error_message}。请检查服务商配置。"
                        self._send_error_to_frontend(chat_id, error_msg)
                        return self._error_response(error_msg)
                elif "authentication" in error_message.lower() or "api key" in error_message.lower():
                    error_msg = "API密钥认证失败。请检查您的API密钥配置。"
                    self._send_error_to_frontend(chat_id, error_msg)
                    return self._error_response(error_msg)
                elif "capacity" in error_message.lower() or "rate limit" in error_message.lower():
                    error_msg = "API请求超出限制。请稍后再试或选择其他模型。"
                    self._send_error_to_frontend(chat_id, error_msg)
                    return self._error_response(error_msg)
                else:
                    error_msg = f"发送对话请求失败: {error_message}"
                    self._send_error_to_frontend(chat_id, error_msg)
                    return self._error_response(error_msg)

        except Exception as e:
            error_message = str(e)
            print(f"聊天功能整体异常: {error_message}")
            error_msg = f"发送对话请求失败: {error_message}"
            try:
                self._send_error_to_frontend(chat_id, error_msg)
            except:
                pass  # If we can't send the error, at least we tried
            return self._error_response(error_msg)

    def get_all_chats(self):
        """获取所有保存的会话"""
        try:
            # 重新加载会话，确保数据最新
            self._load_sessions()
            
            chats = []
            with self.sessions_lock:
                for chat_id, session in self.sessions.items():
                    chats.append({
                        'id': chat_id,
                        'model_id': session.model_id,
                        'messages': session.messages,
                        'last_updated': session.last_updated,
                        'title': session.title  # 添加title字段
                    })
            
            # 按最后更新时间排序
            chats.sort(key=lambda x: x['last_updated'], reverse=True)
            return self._success_response("获取会话列表成功", chats)
        except Exception as e:
            return self._error_response(f"获取会话列表失败: {str(e)}")

    def delete_chat(self, chat_id: str):
        """删除指定的会话"""
        try:
            with self.sessions_lock:
                if chat_id in self.sessions:
                    # 停止正在进行的会话
                    session = self.sessions[chat_id]
                    if session.is_active and session.current_response:
                        session.current_response.close()
                    
                    # 删除会话文件
                    filepath = os.path.join(self.data_dir, f"{chat_id}.json")
                    if os.path.exists(filepath):
                        os.remove(filepath)
                    
                    # 从内存中删除会话
                    del self.sessions[chat_id]
                    
                    return self._success_response("删除会话成功", None)
                return self._error_response("会话不存在")
        except Exception as e:
            return self._error_response(f"删除会话失败: {str(e)}")

    def save_chat(self, chat_id: str, chat_data: dict):
        """保存单个会话的更新"""
        try:
            with self.sessions_lock:
                session = self.sessions.get(chat_id)
                if not session:
                    session = ChatSession(chat_id, chat_data.get('model_id', ''))
                    self.sessions[chat_id] = session
                
                # 更新会话数据
                session.model_id = chat_data.get('model_id', session.model_id)
                session.messages = chat_data.get('messages', [])
                session.last_updated = chat_data.get('last_updated', time.time())
                session.title = chat_data.get('title', session.title)  # 更新title字段
                session.memory_enabled = chat_data.get('memory_enabled', session.memory_enabled)
                
                # 保存到文件
                self._save_session(chat_id)
                return self._success_response("保存会话成功", None)
        except Exception as e:
            return self._error_response(f"保存会话失败: {str(e)}")

    def clear_all_chats(self):
        """清空所有对话"""
        try:
            with self.sessions_lock:
                # 停止所有活动的会话
                for session in self.sessions.values():
                    if session.is_active and session.current_response:
                        session.current_response.close()
                
                # 清空会话字典
                self.sessions.clear()
                
                # 删除所有会话文件，但保留配置文件
                protected_files = ["ai_providers.json", "ai_roles.json"]
                for filename in os.listdir(self.data_dir):
                    if filename.endswith('.json') and filename not in protected_files:
                        try:
                            os.remove(os.path.join(self.data_dir, filename))
                        except Exception as e:
                            print(f"删除文件 {filename} 失败: {str(e)}")
                
            return self._success_response("清空所有对话成功", None)
        except Exception as e:
            return self._error_response(f"清空对话失败: {str(e)}")

    def cleanup_old_sessions(self, max_age_days=30):
        """清理旧的会话数据"""
        current_time = time.time()
        with self.sessions_lock:
            for chat_id, session in list(self.sessions.items()):
                if current_time - session.last_updated > max_age_days * 24 * 3600:
                    try:
                        filepath = os.path.join(self.data_dir, f"{chat_id}.json")
                        if os.path.exists(filepath):
                            os.remove(filepath)
                        del self.sessions[chat_id]
                    except Exception as e:
                        print(f"清理会话 {chat_id} 失败: {str(e)}")

    def export_chat_to_file(self, chat_id: str, file_path: str):
        """导出指定聊天的对话内容到文件"""
        try:
            with self.sessions_lock:
                # 直接从会话或文件获取聊天数据，避免通过get_chat方法的额外处理
                session = self.sessions.get(chat_id)
                chat_data = None

                if session:
                    # 从内存中的会话获取数据
                    chat_data = {
                        "title": session.title,
                        "model_id": session.model_id,
                        "messages": session.messages
                    }
                else:
                    # 从文件直接读取
                    filepath = os.path.join(self.data_dir, f"{chat_id}.json")
                    if os.path.exists(filepath):
                        try:
                            with open(filepath, 'r', encoding='utf-8') as f:
                                chat_data = json.load(f)
                        except Exception as e:
                            return self._error_response(f"读取聊天文件失败: {str(e)}")
                    else:
                        return self._error_response("聊天不存在")

                if not chat_data:
                    return self._error_response("聊天数据为空")

                messages = chat_data.get('messages', [])

                # 构建导出数据
                export_data = {
                    "chat_id": chat_id,
                    "title": chat_data.get('title', f"聊天 {chat_id[:8]}"),
                    "model_id": chat_data.get('model_id', ''),
                    "export_time": int(time.time()),
                    "export_time_readable": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                    "message_count": len(messages),
                    "messages": []
                }

                # 直接导出原始消息，保持完整的数据结构
                for msg in messages:
                    if isinstance(msg, dict):
                        # 直接复制原始消息，不做任何转换
                        export_data["messages"].append(dict(msg))

                # 转换为格式化的JSON字符串
                json_content = json.dumps(export_data, ensure_ascii=False, indent=2)

                # 写入文件
                try:
                    # 规范化文件路径
                    normalized_path = os.path.normpath(file_path)
                    print(f"导出文件路径: {normalized_path}")

                    # 确保目录存在
                    directory = os.path.dirname(normalized_path)
                    if directory and not os.path.exists(directory):
                        os.makedirs(directory, exist_ok=True)

                    # 检查文件路径是否有效
                    if not normalized_path or len(normalized_path.strip()) == 0:
                        return self._error_response("文件路径无效")

                    # 检查文件名是否包含非法字符
                    filename = os.path.basename(normalized_path)
                    if not filename or len(filename.strip()) == 0:
                        return self._error_response("文件名无效")

                    # 在Windows系统下检查文件名中的非法字符
                    import platform
                    if platform.system() == "Windows":
                        invalid_chars = '<>:"/\\|?*'
                        for char in invalid_chars:
                            if char in filename:
                                return self._error_response(f"文件名包含非法字符: {char}")

                    # 检查文件名长度
                    if len(filename) > 255:
                        return self._error_response("文件名过长")

                    print(f"准备写入文件: {normalized_path}")
                    with open(normalized_path, 'w', encoding='utf-8') as f:
                        f.write(json_content)
                    print(f"文件写入成功: {normalized_path}")

                    return self._success_response("导出成功", {
                        "file_path": normalized_path,
                        "title": export_data["title"],
                        "message_count": len(messages)
                    })
                except Exception as e:
                    error_msg = f"写入文件失败: {str(e)} (路径: {normalized_path if 'normalized_path' in locals() else file_path})"
                    print(error_msg)
                    return self._error_response(error_msg)

        except Exception as e:
            return self._error_response(f"导出聊天失败: {str(e)}")

    def import_chat_from_file(self, file_path: str, target_chat_id: str = None):
        """从文件导入聊天内容到指定会话"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return self._error_response(f"文件不存在: {file_path}")

            # 读取文件内容
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                # 尝试其他编码
                try:
                    with open(file_path, 'r', encoding='gbk') as f:
                        content = f.read()
                except Exception as e:
                    return self._error_response(f"文件编码不支持: {str(e)}")
            except Exception as e:
                return self._error_response(f"读取文件失败: {str(e)}")

            # 尝试解析JSON内容
            try:
                import_data = json.loads(content)
            except json.JSONDecodeError as e:
                return self._error_response(f"JSON格式错误: {str(e)}")

            # 验证必要字段
            if not isinstance(import_data, dict):
                return self._error_response("导入数据格式错误，应为JSON对象")

            messages = import_data.get('messages', [])
            if not isinstance(messages, list):
                return self._error_response(f"消息列表格式错误，实际类型: {type(messages)}")

            # 确定目标聊天ID和数据
            if target_chat_id:
                # 导入到现有会话
                existing_chat = self.get_chat(target_chat_id)

                # 处理get_chat返回的JSON字符串
                if isinstance(existing_chat, str):
                    try:
                        existing_chat = json.loads(existing_chat)
                    except json.JSONDecodeError:
                        return self._error_response("获取目标聊天数据格式错误")

                if existing_chat.get('status') != 'success':
                    return self._error_response(f"目标聊天不存在: {target_chat_id}")

                chat_data = existing_chat.get('data', {})
                chat_id = target_chat_id
                import_mode = "append"
            else:
                # 创建新会话
                import uuid
                chat_id = str(uuid.uuid4())
                chat_data = {
                    "id": chat_id,
                    "title": import_data.get('title', f"导入的聊天 {chat_id[:8]}"),
                    "model_id": import_data.get('model_id', ''),
                    "messages": [],
                    "last_updated": time.time(),
                    "memory_enabled": True,
                    "roles": []
                }
                import_mode = "create"

            # 直接导入原始消息，不做复杂的字段转换
            imported_count = 0
            for msg_data in messages:
                if isinstance(msg_data, dict):
                    # 创建消息副本，只保留有效的消息字段
                    message = {}

                    # 基础字段
                    if 'role' in msg_data:
                        message['role'] = msg_data['role']
                    if 'content' in msg_data:
                        message['content'] = msg_data['content']
                    if 'timestamp' in msg_data:
                        message['timestamp'] = msg_data['timestamp']

                    # 推理相关字段（保持原始字段名）
                    if 'reasoning' in msg_data:
                        message['reasoning'] = msg_data['reasoning']
                    if 'reasoningTime' in msg_data:
                        message['reasoningTime'] = msg_data['reasoningTime']
                    if 'reasoningCollapsed' in msg_data:
                        message['reasoningCollapsed'] = msg_data['reasoningCollapsed']

                    # 其他状态字段
                    if 'isError' in msg_data:
                        message['isError'] = msg_data['isError']
                    if 'loading' in msg_data:
                        message['loading'] = msg_data['loading']

                    # 只有包含基础字段的消息才被添加
                    if 'role' in message and 'content' in message:
                        chat_data["messages"].append(message)
                        imported_count += 1

            # 保存聊天
            try:
                save_result = self.save_chat(chat_id, chat_data)

                # 如果save_result是字符串，需要解析为字典
                if isinstance(save_result, str):
                    try:
                        save_result = json.loads(save_result)
                    except json.JSONDecodeError:
                        return self._error_response("保存聊天返回格式错误")

                if save_result.get('status') != 'success':
                    return self._error_response(f"保存导入的聊天失败: {save_result.get('message', '未知错误')}")

                return self._success_response("导入成功", {
                    "chat_id": chat_id,
                    "title": chat_data["title"],
                    "message_count": imported_count,
                    "file_path": file_path,
                    "import_mode": import_mode
                })
            except Exception as e:
                return self._error_response(f"保存聊天时发生异常: {str(e)}")

        except Exception as e:
            return self._error_response(f"导入聊天失败: {str(e)}")

    def get_chat(self, chat_id: str):
        """获取单个聊天会话的详细信息，确保返回最新数据"""
        try:
            with self.sessions_lock:
                # 首先尝试从文件加载最新数据
                filepath = os.path.join(self.data_dir, f"{chat_id}.json")
                file_data = None
                
                if os.path.exists(filepath):
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            file_data = json.load(f)
                    except Exception as e:
                        return self._error_response(f"加载会话文件失败: {str(e)}")
                
                # 检查内存中的会话
                session = self.sessions.get(chat_id)
                
                # 如果内存中没有会话，但文件存在，则创建会话
                if not session and file_data:
                    session = ChatSession(chat_id, file_data.get('model_id', ''))
                    session.messages = file_data.get('messages', [])
                    session.last_updated = file_data.get('last_updated', time.time())
                    session.title = file_data.get('title', f"新对话 {chat_id[:5]}")
                    session.memory_enabled = file_data.get('memory_enabled', True)
                    self.sessions[chat_id] = session
                # 如果内存中有会话，但文件更新时间更新，则更新内存中的会话
                elif session and file_data and file_data.get('last_updated', 0) > session.last_updated:
                    session.messages = file_data.get('messages', [])
                    session.last_updated = file_data.get('last_updated', time.time())
                    session.title = file_data.get('title', session.title)
                    session.model_id = file_data.get('model_id', session.model_id)
                    session.memory_enabled = file_data.get('memory_enabled', session.memory_enabled)
                
                # 如果仍然没有会话，则返回错误
                if not session:
                    return self._error_response("会话不存在")
                
                # 返回会话数据
                chat_data = {
                    'id': chat_id,
                    'chat_id': chat_id,  # 确保两个ID字段都存在
                    'model_id': session.model_id,
                    'model': session.model_id,  # 兼容性字段
                    'messages': session.messages,
                    'last_updated': session.last_updated,
                    'title': session.title,
                    'memory_enabled': session.memory_enabled
                }
                
                return self._success_response("获取会话成功", chat_data)
        except Exception as e:
            return self._error_response(f"获取会话失败: {str(e)}")

    def get_client(self):
        """获取OpenAI客户端"""
        with self.client_lock:
            # 如果有默认服务商，在每次获取客户端时重新选择API密钥实现负载均衡
            if self.providers:
                default_provider = next((p for p in self.providers if p.get("isDefault")), None)
                if default_provider and default_provider.get("apiKeys"):
                    self.api_key = self._select_api_key_by_weight(default_provider["apiKeys"])
            
            return OpenAI(api_key=self.api_key, base_url=self.base_url)

    def get_models(self):
        """获取所有服务商的模型列表"""
        try:
            with self.providers_lock:
                # 从所有可用服务商中收集模型
                all_models = set()
                
                # 如果存在服务商配置，遍历所有服务商收集模型
                if self.providers:
                    for provider in self.providers:
                        # 检查服务商是否有可用API密钥
                        if provider.get('apiKeys'):
                            # 如果服务商已定义模型列表，直接使用
                            if 'models' in provider:
                                provider_models = [model['id'] for model in provider['models'] 
                                                if model.get('available', True)]
                                all_models.update(provider_models)
                                print(f"从服务商 {provider.get('name', 'unknown')} 添加 {len(provider_models)} 个预定义模型")
                    
                    # 如果成功收集到模型，返回结果
                    if all_models:
                        return self._success_response("获取所有可用模型列表成功", list(all_models))
                
                # 如果没有从服务商获取到模型，返回默认模型列表
                fallback_models = [
                    'gpt-4',
                    'gpt-4-turbo',
                    'gpt-4o',
                    'gpt-3.5-turbo',
                    'claude-3-opus',
                    'claude-3-sonnet',
                    'claude-3-haiku'
                ]
                return self._success_response("使用默认模型列表", fallback_models)
        except Exception as e:
            print(f"获取模型列表失败: {e}")
            # 出现异常时也返回一个默认列表，确保前端可以正常运行
            fallback_models = [
                'gpt-4',
                'gpt-4-turbo',
                'gpt-3.5-turbo'
            ]
            return self._success_response("返回基础模型列表", fallback_models)
            
    def test_api_key(self, params):
        """测试API密钥是否有效"""
        try:
            provider_id = params.get("provider_id")
            api_key = params.get("api_key")
            base_url = params.get("base_url")
            test_model = params.get("test_model", "gpt-3.5-turbo") # 默认使用常见模型进行测试
            proxy_config = params.get("proxy", {})

            if not api_key:
                return self._error_response("API密钥不能为空")

            # 使用缓存的客户端
            client = self._get_cached_client(api_key, base_url, proxy_config)
            
            # 使用简单的聊天请求来测试API密钥，这比获取模型列表更通用
            try:
                # 发送一个最小化的请求，设置max_tokens很小以减少资源消耗
                response = client.chat.completions.create(
                    model=test_model,
                    messages=[{"role": "user", "content": "Hello"}],
                    max_tokens=5,
                    temperature=0.5
                )
                # 如果请求成功，则API密钥有效
                return self._success_response("API密钥测试成功", None)
            except Exception as e:
                # 特定错误类型的处理
                error_msg = str(e).lower()
                if "authentication" in error_msg or "invalid api key" in error_msg:
                    return self._error_response("API密钥无效")
                elif "model" in error_msg and "not found" in error_msg:
                    # 模型不存在，但API可能是有效的，尝试查询模型列表
                    try:
                        models = client.models.list()
                        return self._success_response("API密钥有效，但所选测试模型不可用", None)
                    except:
                        pass
                return self._error_response(f"API密钥测试失败: {str(e)}")
        except Exception as e:
            return self._error_response(f"API密钥测试失败: {str(e)}")
    
    def fetch_models(self, params):
        """获取特定服务商的模型列表"""
        try:
            provider_id = params.get("provider_id")
            api_key = params.get("api_key")
            base_url = params.get("base_url")
            proxy_config = params.get("proxy", {})

            if not api_key:
                return self._error_response("API密钥不能为空")

            # 使用缓存的客户端
            client = self._get_cached_client(api_key, base_url, proxy_config)

            # 获取模型列表
            try:
                models = client.models.list()
                model_ids = [model.id for model in models.data]
                return self._success_response("成功获取模型列表", model_ids)
            except Exception as e:
                return self._error_response(f"获取模型列表失败: {str(e)}")
        except Exception as e:
            return self._error_response(f"获取模型列表失败: {str(e)}")

    def test_proxy_connection(self, params):
        """测试代理连接是否正常"""
        try:
            proxy_url = params.get("proxy_url", "")
            proxy_username = params.get("proxy_username", "")
            proxy_password = params.get("proxy_password", "")
            timeout = params.get("timeout", 30.0)
            verify_ssl = params.get("verify_ssl", True)
            target_url = "https://www.google.com"  # 使用谷歌作为测试目标

            if not proxy_url:
                return self._error_response("代理地址不能为空")

            # 构建代理URL
            if proxy_username and proxy_password:
                # 带认证的代理
                if '://' in proxy_url:
                    protocol, rest = proxy_url.split('://', 1)
                    proxy_url = f"{protocol}://{proxy_username}:{proxy_password}@{rest}"
                else:
                    proxy_url = f"http://{proxy_username}:{proxy_password}@{proxy_url}"
            elif not proxy_url.startswith(('http://', 'https://', 'socks5://')):
                # 如果没有协议前缀，默认使用http
                proxy_url = f"http://{proxy_url}"

            print(f"测试代理连接: {proxy_url.split('@')[-1] if '@' in proxy_url else proxy_url} -> {target_url}")

            # 创建带代理的HTTP客户端进行测试
            try:
                http_client = httpx.Client(
                    proxies=proxy_url,
                    timeout=timeout,
                    verify=verify_ssl
                )

                # 测试连接到谷歌
                response = http_client.get(target_url, timeout=timeout)

                # 关闭客户端
                http_client.close()

                if response.status_code == 200:
                    return self._success_response("代理连接测试成功，可以正常访问外网")
                elif response.status_code in [301, 302]:
                    # 重定向也算成功
                    return self._success_response("代理连接测试成功，可以正常访问外网（重定向）")
                else:
                    return self._error_response(f"代理连接成功，但访问测试网站异常 (HTTP {response.status_code})")

            except httpx.ProxyError as e:
                return self._error_response(f"代理连接失败: {str(e)}")
            except httpx.ConnectTimeout:
                return self._error_response("代理连接超时，请检查代理地址和端口是否正确")
            except httpx.ConnectError as e:
                return self._error_response(f"无法连接到代理服务器: {str(e)}")
            except Exception as e:
                return self._error_response(f"代理测试失败: {str(e)}")

        except Exception as e:
            return self._error_response(f"代理测试失败: {str(e)}")

    def detect_system_proxy(self):
        """检测系统代理配置 - 支持 Windows、macOS、Linux"""
        try:
            import os
            import platform
            import socket

            system = platform.system().lower()
            print(f"检测系统代理配置，当前系统: {system}")

            # 1. 首先检查环境变量中的代理设置（所有平台通用）
            proxy_result = self._detect_env_proxy()
            if proxy_result:
                return proxy_result

            # 2. 根据不同平台检测系统代理设置
            if system == 'windows':
                proxy_result = self._detect_windows_proxy()
                if proxy_result:
                    return proxy_result
            elif system == 'darwin':  # macOS
                proxy_result = self._detect_macos_proxy()
                if proxy_result:
                    return proxy_result
            elif system == 'linux':
                proxy_result = self._detect_linux_proxy()
                if proxy_result:
                    return proxy_result

            # 3. 最后尝试端口扫描检测本地代理服务（所有平台通用）
            proxy_result = self._detect_local_proxy_ports()
            if proxy_result:
                return proxy_result

            return self._success_response("未检测到系统代理配置", None)

        except Exception as e:
            return self._error_response(f"检测系统代理失败: {str(e)}")

    def _get_subprocess_kwargs(self):
        """获取 subprocess 参数以隐藏控制台窗口"""
        import platform

        kwargs = {
            'capture_output': True,
            'text': True,
            'timeout': 5
        }

        if platform.system().lower() == 'windows':
            try:
                import subprocess
                # Windows: 隐藏控制台窗口
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
            except AttributeError:
                # 旧版本 Python 可能没有 CREATE_NO_WINDOW
                try:
                    import subprocess
                    kwargs['creationflags'] = 0x08000000  # CREATE_NO_WINDOW 的值
                except:
                    pass  # 如果都失败了，就不设置标志

        return kwargs

    def _detect_env_proxy(self):
        """检测环境变量中的代理设置"""
        try:
            import os
            from urllib.parse import urlparse

            # 检查各种环境变量
            env_vars = ['HTTP_PROXY', 'http_proxy', 'HTTPS_PROXY', 'https_proxy',
                       'ALL_PROXY', 'all_proxy']

            for var in env_vars:
                proxy_url = os.environ.get(var)
                if proxy_url:
                    try:
                        parsed = urlparse(proxy_url)
                        result = {
                            'proxy_url': proxy_url,
                            'protocol': parsed.scheme or 'http',
                            'host': parsed.hostname,
                            'port': parsed.port,
                            'username': parsed.username,
                            'password': parsed.password,
                            'source': f'environment_variable_{var}'
                        }
                        return self._success_response(f"检测到环境变量代理配置 ({var})", result)
                    except Exception as parse_error:
                        print(f"解析环境变量代理URL失败: {parse_error}")
                        result = {
                            'proxy_url': proxy_url,
                            'source': f'environment_variable_{var}'
                        }
                        return self._success_response(f"检测到环境变量代理配置 ({var})", result)

            return None
        except Exception as e:
            print(f"检测环境变量代理失败: {e}")
            return None

    def _detect_windows_proxy(self):
        """检测Windows系统代理设置"""
        try:
            import winreg

            # 读取注册表中的代理设置
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                               r"Software\Microsoft\Windows\CurrentVersion\Internet Settings")

            # 检查是否启用了代理
            proxy_enable, _ = winreg.QueryValueEx(key, "ProxyEnable")
            if proxy_enable:
                proxy_server, _ = winreg.QueryValueEx(key, "ProxyServer")
                winreg.CloseKey(key)

                # 格式化代理地址
                if proxy_server:
                    if not proxy_server.startswith(('http://', 'https://', 'socks5://')):
                        proxy_server = f"http://{proxy_server}"

                    result = {
                        'proxy_url': proxy_server,
                        'source': 'windows_registry'
                    }
                    return self._success_response("检测到Windows系统代理配置", result)

            winreg.CloseKey(key)
            return None

        except Exception as e:
            print(f"读取Windows代理设置失败: {e}")
            return None

    def _detect_macos_proxy(self):
        """检测macOS系统代理设置"""
        try:
            import subprocess

            # 使用 networksetup 命令获取代理设置
            # 获取当前网络服务
            kwargs = self._get_subprocess_kwargs()
            result = subprocess.run(['networksetup', '-listallnetworkservices'], **kwargs)
            if result.returncode == 0:
                services = result.stdout.strip().split('\n')[1:]  # 跳过第一行标题

                for service in services:
                    if service.startswith('*'):  # 跳过禁用的服务
                        continue

                    # 检查HTTP代理
                    kwargs = self._get_subprocess_kwargs()
                    http_result = subprocess.run(['networksetup', '-getwebproxy', service], **kwargs)
                    if http_result.returncode == 0:
                        lines = http_result.stdout.strip().split('\n')
                        enabled = False
                        server = None
                        port = None

                        for line in lines:
                            if 'Enabled: Yes' in line:
                                enabled = True
                            elif line.startswith('Server:'):
                                server = line.split(':', 1)[1].strip()
                            elif line.startswith('Port:'):
                                port = line.split(':', 1)[1].strip()

                        if enabled and server and port:
                            proxy_url = f"http://{server}:{port}"
                            result = {
                                'proxy_url': proxy_url,
                                'source': 'macos_networksetup',
                                'service': service
                            }
                            return self._success_response(f"检测到macOS系统代理配置 ({service})", result)

                    # 检查HTTPS代理
                    kwargs = self._get_subprocess_kwargs()
                    https_result = subprocess.run(['networksetup', '-getsecurewebproxy', service], **kwargs)
                    if https_result.returncode == 0:
                        lines = https_result.stdout.strip().split('\n')
                        enabled = False
                        server = None
                        port = None

                        for line in lines:
                            if 'Enabled: Yes' in line:
                                enabled = True
                            elif line.startswith('Server:'):
                                server = line.split(':', 1)[1].strip()
                            elif line.startswith('Port:'):
                                port = line.split(':', 1)[1].strip()

                        if enabled and server and port:
                            proxy_url = f"https://{server}:{port}"
                            result = {
                                'proxy_url': proxy_url,
                                'source': 'macos_networksetup',
                                'service': service
                            }
                            return self._success_response(f"检测到macOS HTTPS代理配置 ({service})", result)

            return None

        except Exception as e:
            print(f"检测macOS代理设置失败: {e}")
            return None

    def _detect_linux_proxy(self):
        """检测Linux系统代理设置"""
        try:
            import subprocess
            import os

            # 1. 检查GNOME/KDE桌面环境的代理设置
            try:
                # GNOME (使用 gsettings)
                kwargs = self._get_subprocess_kwargs()
                result = subprocess.run(['gsettings', 'get', 'org.gnome.system.proxy', 'mode'], **kwargs)
                if result.returncode == 0 and 'manual' in result.stdout:
                    # 获取HTTP代理设置
                    kwargs = self._get_subprocess_kwargs()
                    http_result = subprocess.run(['gsettings', 'get', 'org.gnome.system.proxy.http', 'host'], **kwargs)
                    port_result = subprocess.run(['gsettings', 'get', 'org.gnome.system.proxy.http', 'port'], **kwargs)

                    if http_result.returncode == 0 and port_result.returncode == 0:
                        host = http_result.stdout.strip().strip("'\"")
                        port = port_result.stdout.strip()

                        if host and port and host != "''" and port != '0':
                            proxy_url = f"http://{host}:{port}"
                            result = {
                                'proxy_url': proxy_url,
                                'source': 'gnome_gsettings'
                            }
                            return self._success_response("检测到GNOME系统代理配置", result)
            except Exception as gnome_error:
                print(f"检测GNOME代理设置失败: {gnome_error}")

            # 2. 检查系统配置文件
            config_files = [
                '/etc/environment',
                os.path.expanduser('~/.bashrc'),
                os.path.expanduser('~/.zshrc'),
                os.path.expanduser('~/.profile')
            ]

            for config_file in config_files:
                try:
                    if os.path.exists(config_file):
                        with open(config_file, 'r') as f:
                            content = f.read()

                        # 查找代理设置
                        for line in content.split('\n'):
                            line = line.strip()
                            if line.startswith('export ') and ('http_proxy=' in line or 'HTTP_PROXY=' in line):
                                # 提取代理URL
                                if 'http_proxy=' in line:
                                    proxy_url = line.split('http_proxy=')[1].strip().strip('"\'')
                                else:
                                    proxy_url = line.split('HTTP_PROXY=')[1].strip().strip('"\'')

                                if proxy_url:
                                    result = {
                                        'proxy_url': proxy_url,
                                        'source': f'config_file_{config_file}'
                                    }
                                    return self._success_response(f"检测到Linux配置文件代理设置 ({config_file})", result)
                except Exception as file_error:
                    print(f"读取配置文件 {config_file} 失败: {file_error}")

            return None

        except Exception as e:
            print(f"检测Linux代理设置失败: {e}")
            return None

    def _detect_local_proxy_ports(self):
        """检测本地代理服务端口"""
        try:
            import socket

            # 常见的代理端口
            common_ports = [7890, 7891, 1080, 8080, 8888, 10809, 1087, 8001]

            for port in common_ports:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(0.5)  # 500ms超时
                    result = sock.connect_ex(('127.0.0.1', port))
                    sock.close()

                    if result == 0:  # 端口开放
                        proxy_url = f"http://127.0.0.1:{port}"
                        result = {
                            'proxy_url': proxy_url,
                            'source': 'port_scan',
                            'detected_port': port
                        }
                        return self._success_response(f"检测到本地代理服务 (端口 {port})", result)
                except Exception:
                    continue

            return None

        except Exception as e:
            print(f"端口扫描失败: {e}")
            return None

class ProxyModelController(ModelController):
    """代理模型控制器"""
    pass

#
if __name__ == "__main__":
    # model_controller = ModelController(api_key="sk-0wquLiBRPV9XaLCUF5FaE0B400Dd4010A0Bb780a4765F447", base_url="https://ggeeljdz.cloud.sealos.io/v1")
    # response = model_controller.chat("test-chat", "hunyuan-lite", messages=[
    #     {
    #         "role": "system",
    #         "content": f'网络小说灵感专家。'
    #     },
    #     {
    #         "role": "user",
    #         "content": "生成是个冲突。"
    #     }
    # ],config_data={"stream":True})
    #
    # # 流式打印结果
    # chapter_result =''
    # for chunk in response:
    #     for chunk in response:
    #
    #         if len(chunk.choices) > 0:
    #             delta = chunk.choices[0].delta
    #             if 'content' in str(delta):
    #                 choice_content = delta.content
    #                 chapter_result += choice_content
    #                 print(choice_content)
    # from openai import OpenAI
    #
    # client = OpenAI(
    #     api_key="************************************************************************************",
    #     base_url="https://aiproxy-tawny.vercel.app/grok", # 没有 /v1 后缀
    # )
    #
    # gemini = OpenAI(
    #     api_key="AIzaSyAn9zDrsNvOmwrj-ehZHhK0jEPukWQh5XU",
    #     base_url="https://llmproxy-vercel.vercel.app/gemini"
    # )
    # from openai import OpenAI
    #
    # client = OpenAI(
    #     api_key="AIzaSyDObSMvcvjmsiPpKeOQUxTmWHl9BjjuUFE",
    #     base_url="https://llmproxy-vercel.vercel.app/gemini",
    # )
    #
    # response = client.chat.completions.create(
    #     model="gemini-1.5-flash",
    #     messages=[{"role": "user", "content": "Hello world!"}],
    # )
    #
    # print(response.choices[0].message.content)
    pass