import os
import sys
import platform


class PathManager:
    """跨平台路径管理器，统一处理配置文件和数据目录路径"""

    def __init__(self):
        self.system = platform.system()
        self.is_frozen = getattr(sys, 'frozen', False)
        self._base_dir = None
        self._config_dir = None
        
    def get_application_dir(self):
        """获取应用程序目录，与PVV.py保持一致"""
        if self.is_frozen:
            # 打包后的应用
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller
                return os.path.dirname(sys.executable)
            else:
                # Nuitka 或其他打包工具
                exe_dir = os.path.dirname(sys.executable)

                # 在macOS上，检查是否在.app包内，如果是，需要特殊处理
                if self.system == "Darwin" and '.app' in exe_dir:
                    # 对于 Nuitka 打包的 macOS 应用，与 PVV.py 中的逻辑保持一致

                    # 1. 首先检查可执行文件同级目录（最常见的情况）
                    backup_in_exe_dir = os.path.join(exe_dir, "backup")
                    if os.path.exists(backup_in_exe_dir):
                        print(f"PathManager: 找到 backup 在可执行文件目录: {backup_in_exe_dir}")
                        return exe_dir

                    # 2. 检查 .app/Contents/Resources/ 目录
                    if exe_dir.endswith('.app/Contents/MacOS'):
                        resources_dir = os.path.join(os.path.dirname(exe_dir), 'Resources')
                        backup_in_resources = os.path.join(resources_dir, "backup")
                        if os.path.exists(backup_in_resources):
                            print(f"PathManager: 找到 backup 在 Resources 目录: {backup_in_resources}")
                            return resources_dir

                    # 3. 检查 .app/Contents/ 目录
                    if exe_dir.endswith('.app/Contents/MacOS'):
                        contents_dir = os.path.dirname(exe_dir)
                        backup_in_contents = os.path.join(contents_dir, "backup")
                        if os.path.exists(backup_in_contents):
                            print(f"PathManager: 找到 backup 在 Contents 目录: {backup_in_contents}")
                            return contents_dir

                    # 4. 检查 .app 根目录
                    app_match = exe_dir.find('.app')
                    if app_match != -1:
                        app_root = exe_dir[:app_match + 4]  # 包含 .app
                        backup_in_app_root = os.path.join(app_root, "backup")
                        if os.path.exists(backup_in_app_root):
                            print(f"PathManager: 找到 backup 在 app 根目录: {backup_in_app_root}")
                            return app_root

                    # 5. 如果都不在，回退到exe目录
                    print(f"PathManager: 警告: 在 macOS .app 包中未找到 backup 目录，使用可执行文件目录")
                    return exe_dir
                else:
                    return exe_dir
        else:
            # 开发环境：从 backend/bridge/ 回到项目根目录
            current_file = os.path.abspath(__file__)
            # backend/bridge/PathManager.py -> backend/bridge -> backend -> 项目根目录
            return os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
    
    def get_base_dir(self):
        """获取数据存储基础目录，使用固定的平台标准路径"""
        if self._base_dir is not None:
            return self._base_dir

        if self.system == "Darwin":
            # macOS：使用标准的 Application Support 目录下的 backup 子目录
            user_home = os.path.expanduser('~')
            pvv_dir = os.path.join(user_home, 'Library', 'Application Support', 'PVV')
            base_dir = os.path.join(pvv_dir, 'backup')
            print(f"PathManager: macOS 使用标准目录: {base_dir}")
        elif self.system == "Windows":
            # Windows：始终使用 exe 同级目录下的 backup
            app_dir = self.get_application_dir()
            base_dir = os.path.join(app_dir, "backup")
            print(f"PathManager: Windows 使用 exe 同级目录: {base_dir}")
        elif self.system == "Linux":
            # Linux：使用XDG规范
            user_home = os.path.expanduser('~')
            xdg_config_home = os.environ.get('XDG_CONFIG_HOME', os.path.join(user_home, '.config'))
            base_dir = os.path.join(xdg_config_home, 'pvv')
            print(f"PathManager: Linux 使用 XDG 目录: {base_dir}")
        else:
            # 其他系统：使用项目根目录下的backup
            app_dir = self.get_application_dir()
            base_dir = os.path.join(app_dir, "backup")
            print(f"PathManager: 其他系统使用项目目录: {base_dir}")

        self._base_dir = base_dir

        # 确保目录存在
        try:
            os.makedirs(base_dir, exist_ok=True)
            print(f"PathManager: 成功创建/确认目录: {base_dir}")
        except Exception as e:
            print(f"PathManager: 创建目录失败: {base_dir}, 错误: {e}")
            # 如果创建失败，回退到临时目录
            import tempfile
            base_dir = os.path.join(tempfile.gettempdir(), 'PVV')
            os.makedirs(base_dir, exist_ok=True)
            print(f"PathManager: 回退到临时目录: {base_dir}")
            self._base_dir = base_dir

        return self._base_dir
    
    def get_config_dir(self):
        """获取配置文件目录"""
        if self._config_dir is not None:
            return self._config_dir
            
        base_dir = self.get_base_dir()
        self._config_dir = os.path.join(base_dir, "config")
        
        # 确保配置目录存在
        os.makedirs(self._config_dir, exist_ok=True)
        
        return self._config_dir
    
    def get_config_file(self, filename="settings.json"):
        """获取配置文件完整路径"""
        return os.path.join(self.get_config_dir(), filename)
    
    def get_data_dir(self, subdir):
        """获取数据子目录路径"""
        base_dir = self.get_base_dir()
        data_dir = os.path.join(base_dir, subdir)
        
        # 确保目录存在
        os.makedirs(data_dir, exist_ok=True)
        
        return data_dir
    
    def get_backgrounds_dir(self):
        """获取背景图片目录"""
        return self.get_data_dir("backgrounds")
    
    def get_books_dir(self):
        """获取书籍目录"""
        return self.get_data_dir("book")
    
    def get_chat_dir(self):
        """获取聊天记录目录"""
        return self.get_data_dir("chat")
    
    def get_local_dir(self):
        """获取本地化目录"""
        return self.get_data_dir("local")

    def get_inject_project_dir(self):
        """获取注入项目目录"""
        return self.get_data_dir("inject_project")
    
    def get_path_info(self):
        """获取路径信息，用于调试"""
        if self.system == "Darwin":
            storage_strategy = "macOS标准目录 (~/Library/Application Support/PVV/backup)"
        elif self.system == "Windows":
            storage_strategy = "Windows exe同级目录 (./backup)"
        elif self.system == "Linux":
            storage_strategy = "Linux XDG配置目录 (~/.config/pvv)"
        else:
            storage_strategy = "其他系统项目目录"

        return {
            "system": self.system,
            "is_frozen": self.is_frozen,
            "application_dir": self.get_application_dir(),
            "base_dir": self.get_base_dir(),
            "config_dir": self.get_config_dir(),
            "executable_path": sys.executable if self.is_frozen else sys.argv[0],
            "current_working_dir": os.getcwd(),
            "pathmanager_file": os.path.abspath(__file__),
            "storage_strategy": storage_strategy,
            "user_home": os.path.expanduser('~'),
            "detection_logic": "使用固定的平台标准目录，避免路径检测问题"
        }
    



# 全局路径管理器实例
path_manager = PathManager()
