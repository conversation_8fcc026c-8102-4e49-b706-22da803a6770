from typing import Any

import webview
from webview import Window
import os

# 这种方式实现了一些窗口的操作绑定到js上了
class WebViewApp:
    def __init__(self,width=1200,height=800,draggable=True ):
        # self.title = title
        # self.html_path = html_path
        self.window = None
        self.width = width
        self.height = height
        self.draggable= draggable

    def run(self, url, js_api: Any):
        # mapping_backup = os.path.join(os.path.abspath(os.curdir),"backup")
        #
        # webview.settings["VIRTUAL_HOST_MAPPINGS"] = {
        #     # "backup": mapping_backup,
        # }
        # html中的使用方法如下


        self.window = webview.create_window('',url=url,
                                            width=self.width,height=self.height,



                                            js_api=js_api, frameless=True,
                                            resizable=True, draggable=self.draggable,
                                            )
        self.expose_functions(self.window)
        webview.start(debug=True,gui='edgechromium')  # 启动窗口事件循环

    #     下面是绑定一些窗口的操作给前端的js
    def expose_functions(self, window: Window):
        window.expose(self.destroy)
        window.expose(self.maximize)
        window.expose(self.minimize)
        window.expose(self.move)
        window.expose(self.resize)
        window.expose(self.restore)
        window.expose(self.show)
        window.expose(self.toggle_fullscreen)
        window.expose(self.hide)
        window.expose(self.load_css)
        window.expose(self.eval_js)
        window.expose(self.get_cookies)
        window.expose(self.get_current_url)
        window.expose(self.get_elements)
        # 添加文件选择相关功能
        # window.expose(self.select_file)
        # window.expose(self.select_directory)
        # window.expose(self.select_chrome_exe)

    def destroy(self):
        if self.window:
            self.window.destroy()

    def maximize(self):
        if self.window:
            self.window.maximize()

    def minimize(self):
        if self.window:
            self.window.minimize()

    def move(self, x, y):
        if self.window:
            self.window.move(x, y)

    def resize(self, width, height, fix_point):
        if self.window:
            self.window.resize(width, height, fix_point)

    def restore(self):
        if self.window:
            self.window.restore()

    def show(self):
        if self.window:
            self.window.show()

    def toggle_fullscreen(self):
        if self.window:
            self.window.toggle_fullscreen()

    def hide(self):
        if self.window:
            self.window.hide()

    def load_css(self, stylesheet):
        if self.window:
            self.window.load_css(stylesheet)

    def eval_js(self, script):
        if self.window:
            return self.window.evaluate_js(script)

    def get_cookies(self):
        if self.window:
            return self.window.get_cookies()

    def get_current_url(self):
        if self.window:
            return self.window.get_current_url()

    def get_elements(self, css_selector):
        if self.window:
            return self.window.get_elements(css_selector)

    # 添加文件选择相关方法

