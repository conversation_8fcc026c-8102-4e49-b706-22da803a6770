{"nodes": [{"id": "main-1", "type": "main-event", "x": 400, "y": 100, "data": {"label": "故事开始", "year": "2020", "month": "1", "day": "1", "content": "主角踏上旅程", "nodeType": "main", "color": "#409EFF"}, "nodeType": "main"}, {"id": "main-2", "type": "main-event", "x": 400, "y": 260, "data": {"label": "主角出发", "year": "2021", "month": "6", "day": "15", "content": "主角面临挑战", "nodeType": "main", "color": "#409EFF"}, "nodeType": "main"}, {"id": "main-3", "type": "main-event", "x": 400, "y": 400, "data": {"label": "第一次危机", "year": "2022", "month": "3", "day": "10", "content": "主角遇到危机", "nodeType": "main", "color": "#409EFF"}, "nodeType": "main"}, {"id": "branch-1", "type": "branch-event", "x": 660, "y": 100, "data": {"label": "初遇", "year": "2020", "month": "3", "day": "15", "content": "与女主角相遇", "nodeType": "branch", "color": "#e84393", "parentId": "main-1", "isLeftSide": false}, "nodeType": "branch"}, {"id": "branch-2", "type": "branch-event", "x": 160, "y": 260, "data": {"label": "挫折", "year": "2021", "month": "5", "day": "20", "content": "遭遇挫折", "nodeType": "branch", "color": "#00b894", "parentId": "main-2", "isLeftSide": true}, "nodeType": "branch"}], "edges": [{"id": "e-main1-main2", "source": "main-1", "target": "main-2", "sourceHandle": "bottom", "targetHandle": "top", "type": "smoothstep", "animated": true, "style": {"strokeWidth": 4, "stroke": "#409EFF"}, "markerEnd": {"type": "arrowclosed", "color": "#409EFF"}}, {"id": "e-main2-main3", "source": "main-2", "target": "main-3", "sourceHandle": "bottom", "targetHandle": "top", "type": "smoothstep", "animated": true, "style": {"strokeWidth": 4, "stroke": "#409EFF"}, "markerEnd": {"type": "arrowclosed", "color": "#409EFF"}}, {"id": "e-main1-branch1", "source": "main-1", "target": "branch-1", "sourceHandle": "right", "targetHandle": "left", "type": "smoothstep", "animated": true, "style": {"strokeWidth": 3, "stroke": "#e84393"}, "markerEnd": {"type": "arrowclosed", "color": "#e84393"}}, {"id": "e-main2-branch2", "source": "main-2", "target": "branch-2", "sourceHandle": "left", "targetHandle": "right", "type": "smoothstep", "animated": true, "style": {"strokeWidth": 3, "stroke": "#00b894"}, "markerEnd": {"type": "arrowclosed", "color": "#00b894"}}], "version": "2.0"}