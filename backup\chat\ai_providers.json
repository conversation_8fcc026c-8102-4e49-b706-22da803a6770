[{"id": "1748543286577", "name": "OpenRouter", "baseUrl": "https://openrouter.ai/api/v1", "apiKeys": [{"id": "1748543316792", "key": "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", "weight": 1, "status": "active"}, {"id": "1748543318705", "key": "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", "weight": 1, "status": "active"}, {"id": "1748543319254", "key": "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", "weight": 1, "status": "active"}, {"id": "1748543319614", "key": "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", "weight": 1, "status": "active"}, {"id": "1748543319896", "key": "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", "weight": 1, "status": "active"}, {"id": "1748543320167", "key": "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", "weight": 1, "status": "active"}, {"id": "1748543320428", "key": "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", "weight": 1, "status": "active"}, {"id": "1748543320853", "key": "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", "weight": 1, "status": "active"}, {"id": "1748543321203", "key": "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", "weight": 1, "status": "active"}, {"id": "1748543321453", "key": "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", "weight": 1, "status": "active"}], "models": [{"id": "deepseek/deepseek-r1-0528:free", "name": "deepseek/deepseek-r1-0528:free", "available": true, "config": {"temperature": 0.8, "max_tokens": 8192, "top_p": 0.8, "frequency_penalty": 0, "presence_penalty": 0, "stream": true}}, {"id": "qwen/qwen3-235b-a22b:free", "name": "qwen/qwen3-235b-a22b:free", "available": true, "config": {"temperature": 0.8, "max_tokens": 8192, "top_p": 0.8, "frequency_penalty": 0, "presence_penalty": 0, "stream": true}}, {"id": "moonshotai/kimi-vl-a3b-thinking:free", "name": "moonshotai/kimi-vl-a3b-thinking:free", "available": true, "config": {"temperature": 0.8, "max_tokens": 8192, "top_p": 0.8, "frequency_penalty": 0, "presence_penalty": 0, "stream": true}}, {"id": "deepseek/deepseek-chat-v3-0324:free", "name": "deepseek/deepseek-chat-v3-0324:free", "available": true, "config": {"temperature": 0.8, "max_tokens": 8192, "top_p": 0.8, "frequency_penalty": 0, "presence_penalty": 0, "stream": true}}, {"id": "deepseek/deepseek-r1:free", "name": "deepseek/deepseek-r1:free", "available": true, "config": {"temperature": 0.8, "max_tokens": 8192, "top_p": 0.8, "frequency_penalty": 0, "presence_penalty": 0, "stream": true}}, {"id": "google/gemini-2.0-flash-exp:free", "name": "google/gemini-2.0-flash-exp:free", "available": true, "config": {"temperature": 0.8, "max_tokens": 8192, "top_p": 0.8, "frequency_penalty": 0, "presence_penalty": 0, "stream": true}}, {"id": "moonshotai/kimi-k2:free", "name": "moonshotai/kimi-k2:free", "available": true, "config": {"temperature": 0.8, "max_tokens": 4096, "top_p": 0.8, "frequency_penalty": 0, "presence_penalty": 0, "stream": true}}], "proxy": {"enabled": true, "url": "127.0.0.1:10809", "username": "", "password": "", "timeout": 30, "verify_ssl": true}}, {"id": "1748543641242", "name": "智普", "baseUrl": "https://open.bigmodel.cn/api/paas/v4/", "apiKeys": [{"id": "1748543727124", "key": "26e65922632e855d1dbb119c48768f37.9gvSoRbUYSirPDMS", "weight": 1, "status": "error"}], "models": [{"id": "glm-z1-flash", "name": "glm-z1-flash", "available": true, "config": {"temperature": 0.8, "max_tokens": 8192, "top_p": 0.8, "frequency_penalty": 0, "presence_penalty": 0, "stream": true}}, {"id": "glm-4-flash", "name": "glm-4-flash", "available": true, "config": {"temperature": 0.8, "max_tokens": 8192, "top_p": 0.8, "frequency_penalty": 0, "presence_penalty": 0, "stream": true}}], "proxy": {"enabled": false, "url": "", "username": "", "password": "", "timeout": 30, "verify_ssl": true}}, {"id": "1748709660319", "name": "讯飞 x", "baseUrl": "https://spark-api-open.xf-yun.com/v2", "apiKeys": [{"id": "1748709725040", "key": "BxpMZONsDdsbiOZpXXQg:TBWAxUYuFoEDXzPiUPWx", "weight": 1, "status": "active"}], "models": [{"id": "x1", "name": "x1", "available": true, "config": {"temperature": 0.8, "max_tokens": 8192, "top_p": 0.8, "frequency_penalty": 0, "presence_penalty": 0, "stream": true}}], "proxy": {"enabled": false, "url": "", "username": "", "password": "", "timeout": 30, "verify_ssl": true}}, {"id": "1748709770726", "name": "讯飞lite", "baseUrl": "https://spark-api-open.xf-yun.com/v1", "apiKeys": [{"id": "1748709799922", "key": "JQOcnVDXXdpmTEipLInO:coOiCkAaapgobqrGaejQ", "weight": 1, "status": "active"}], "models": [{"id": "lite", "name": "lite", "available": true, "config": {"temperature": 0.8, "max_tokens": 8192, "top_p": 0.8, "frequency_penalty": 0, "presence_penalty": 0, "stream": true}}], "proxy": {"enabled": false, "url": "127.0.0.1:10809", "username": "", "password": "", "timeout": 30, "verify_ssl": true}}, {"id": "1755177011780", "name": "谷歌", "baseUrl": "https://generativelanguage.googleapis.com/v1beta/openai/", "apiKeys": [{"id": "1755177038579", "key": "AIzaSyC0SCvSnZKtoT2hD06Nvp1-wzsWsSCzYfc", "weight": 1, "status": "active"}], "models": [{"id": "gemini-2.5-flash", "name": "gemini-2.5-flash", "available": true, "config": {"temperature": 0.8, "max_tokens": 8192, "top_p": 0.8, "frequency_penalty": 0, "presence_penalty": 0, "stream": true}}], "proxy": {"enabled": true, "url": "127.0.0.1:10809", "username": "", "password": "", "timeout": 30, "verify_ssl": false}}]