#!/bin/bash

# 构建Mac应用


# 创建包装脚本解决Unicode编码问题
cat > macout2/PVV2.app/Contents/MacOS/PVV2_wrapper << 'EOF'
#!/bin/bash

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 设置正确的环境变量解决Unicode编码问题
export LC_ALL=en_US.UTF-8
export LANG=en_US.UTF-8
export PYTHONIOENCODING=utf-8

# 切换到脚本目录
cd "$SCRIPT_DIR"

# 启动应用
exec "$SCRIPT_DIR/PVV2" "$@"
EOF

# 设置包装脚本权限
chmod +x macout2/PVV2.app/Contents/MacOS/PVV2_wrapper

# 修复Info.plist文件
cat > macout2/PVV2.app/Contents/Info.plist << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDisplayName</key>
	<string>PVV2</string>
	<key>CFBundleExecutable</key>
	<string>PVV2_wrapper</string>
	<key>CFBundleIconFile</key>
	<string>Icons.icns</string>
	<key>CFBundleIdentifier</key>
	<string>com.pvv2.app</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>PVV2</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleVersion</key>
	<string>1.0</string>
	<key>LSMinimumSystemVersion</key>
	<string>10.15</string>
	<key>NSHighResolutionCapable</key>
	<true/>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.productivity</string>
</dict>
</plist>
EOF

# 复制logo.ico文件
echo "复制logo.ico文件..."
cp logo.ico macout2/PVV2.app/Contents/MacOS/

# 重新签名应用
echo "重新签名应用..."
codesign --force --deep --sign - macout2/PVV2.app
