# macOS WebKit 全屏功能修复

## 问题描述

在macOS平台上，pywebview使用WebKit作为底层渲染引擎，存在以下全屏API限制：

1. **时间线全屏无效**：`TimelineFlow.vue` 中的全屏切换点击无响应
2. **场景卡全屏无效**：`SceneCards.vue` 中的全屏切换点击无响应  
3. **关系图谱全屏正常**：因为使用的是弹窗模式，不依赖浏览器全屏API

## 根本原因

macOS WebKit对全屏API有严格的安全限制：
- 必须由用户直接触发（不能在异步回调中调用）
- 需要正确的事件处理机制
- 可能需要特定的权限设置
- 与Windows平台的WebView2行为不一致

## 解决方案

实现了**混合全屏模式**，优先使用原生API，失败时回退到CSS全屏：

### 1. 修改全屏切换逻辑

```javascript
const toggleFullscreen = async () => {
  try {
    if (!isFullscreen.value) {
      // 尝试原生全屏API
      if (element.requestFullscreen) {
        await element.requestFullscreen();
      } else if (element.webkitRequestFullscreen) {
        await element.webkitRequestFullscreen();
      } else {
        // 回退到CSS全屏模式
        element.classList.add('css-fullscreen');
      }
    } else {
      // 退出全屏逻辑
      if (document.fullscreenElement) {
        await document.exitFullscreen();
      } else {
        element.classList.remove('css-fullscreen');
      }
    }
  } catch (error) {
    // API失败时使用CSS全屏
    element.classList.toggle('css-fullscreen');
  }
};
```

### 2. 添加CSS全屏样式

```css
/* TimelineFlow.vue */
.timeline-flow-wrapper.css-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  background: var(--el-bg-color) !important;
}

/* SceneCards.vue */
.cards-grid.css-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  background: var(--el-bg-color) !important;
  overflow: auto !important;
}
```

### 3. 更新状态检测逻辑

```javascript
const handleFullscreenChange = () => {
  const hasNativeFullscreen = !!document.fullscreenElement || 
                              !!document.webkitFullscreenElement;
  
  const hasCssFullscreen = element && element.classList.contains('css-fullscreen');
  
  isFullscreen.value = hasNativeFullscreen || hasCssFullscreen;
};
```

## 修改的文件

1. **frontend/src/views/book/TimelineFlow.vue**
   - 修改 `toggleFullscreen()` 方法
   - 更新 `onFullscreenChange()` 状态检测
   - 添加 `.css-fullscreen` 样式

2. **frontend/src/views/book/SceneCards.vue**
   - 修改 `toggleFullscreen()` 方法
   - 更新 `handleFullscreenChange()` 状态检测
   - 添加 `.css-fullscreen` 样式

## 测试验证

修复后的行为：
- ✅ **Windows平台**：继续使用原生全屏API，行为不变
- ✅ **macOS平台**：优先尝试原生API，失败时自动回退到CSS全屏
- ✅ **跨平台兼容**：统一的用户体验

## 技术优势

1. **渐进增强**：优先使用原生API，确保最佳体验
2. **优雅降级**：API失败时自动回退，确保功能可用
3. **跨平台兼容**：统一的代码逻辑，适配不同平台差异
4. **用户体验一致**：无论使用哪种模式，用户感知相同

## 注意事项

- CSS全屏模式下，ESC键不会自动退出全屏，需要点击退出按钮
- 确保全屏元素的z-index足够高，避免被其他元素遮挡
- 全屏状态变化时需要重新调整视图布局
