# coding:utf-8
"""
嵌入式HTTP服务器
为pywebview提供嵌入的前端资源服务
"""

import threading
import socket
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import unquote
import logging
import time

# 尝试导入嵌入的资源
try:
    from frontend_resources import get_resource, get_mime_type, list_resources, get_stats
    HAS_EMBEDDED_RESOURCES = True
except ImportError:
    HAS_EMBEDDED_RESOURCES = False

class EmbeddedResourceHandler(BaseHTTPRequestHandler):
    """处理嵌入资源的HTTP请求"""
    
    def log_message(self, format, *args):
        """重写日志方法，避免控制台输出过多信息"""
        pass  # 静默处理，或者可以使用logging模块
    
    def do_GET(self):
        """处理GET请求"""
        try:
            # 解码URL路径
            path = unquote(self.path)
            
            # 移除查询参数
            if '?' in path:
                path = path.split('?')[0]
            
            # 处理根路径
            if path == '/' or path == '':
                path = '/index.html'
            
            # 移除开头的斜杠
            resource_path = path.lstrip('/')
            
            # 获取资源内容
            content = get_resource(resource_path)
            
            if content is not None:
                # 获取MIME类型
                mime_type = get_mime_type(resource_path)
                
                # 发送响应
                self.send_response(200)
                self.send_header('Content-Type', mime_type)
                self.send_header('Content-Length', str(len(content)))

                # 添加CORS跨域支持头
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
                self.send_header('Access-Control-Max-Age', '86400')  # 预检请求缓存24小时

                # 添加缓存控制头
                if resource_path.endswith(('.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf')):
                    self.send_header('Cache-Control', 'public, max-age=31536000')  # 1年缓存
                else:
                    self.send_header('Cache-Control', 'no-cache')

                self.end_headers()
                self.wfile.write(content)
                
                # 可选：记录访问日志
                # print(f"✅ 服务资源: {resource_path} ({len(content)} bytes, {mime_type})")
                
            else:
                # 资源不存在，返回404
                self.send_response(404)
                self.send_header('Content-Type', 'text/plain; charset=utf-8')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
                error_msg = f"Resource not found: {resource_path}"
                self.send_header('Content-Length', str(len(error_msg.encode('utf-8'))))
                self.end_headers()
                self.wfile.write(error_msg.encode('utf-8'))
                print(f"❌ 资源未找到: {resource_path}")

        except Exception as e:
            # 服务器错误
            self.send_response(500)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
            error_msg = f"Internal server error: {str(e)}"
            self.send_header('Content-Length', str(len(error_msg.encode('utf-8'))))
            self.end_headers()
            self.wfile.write(error_msg.encode('utf-8'))
            print(f"❌ 服务器错误: {e}")
    
    def do_OPTIONS(self):
        """处理OPTIONS预检请求（CORS）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        self.send_header('Access-Control-Max-Age', '86400')  # 预检请求缓存24小时
        self.send_header('Content-Length', '0')
        self.end_headers()

    def do_HEAD(self):
        """处理HEAD请求"""
        # 复用GET逻辑，但不发送内容
        class DummyFile:
            def write(self, data):
                pass

        original_wfile = self.wfile
        self.wfile = DummyFile()
        self.do_GET()
        self.wfile = original_wfile

class EmbeddedServer:
    """嵌入式HTTP服务器"""
    
    def __init__(self, host='127.0.0.1', port=0):
        """
        初始化服务器
        
        Args:
            host: 服务器主机地址
            port: 服务器端口，0表示自动分配
        """
        self.host = host
        self.port = port
        self.server = None
        self.thread = None
        self.running = False
        
    def find_free_port(self):
        """查找可用端口"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            s.listen(1)
            port = s.getsockname()[1]
        return port
    
    def start(self):
        """启动服务器"""
        if not HAS_EMBEDDED_RESOURCES:
            raise RuntimeError("未找到嵌入的前端资源模块 (frontend_resources.py)")
        
        if self.running:
            print("⚠️ 服务器已经在运行中")
            return
        
        try:
            # 如果端口为0，自动分配端口
            if self.port == 0:
                self.port = self.find_free_port()
            
            # 创建HTTP服务器
            self.server = HTTPServer((self.host, self.port), EmbeddedResourceHandler)
            
            # 在后台线程中运行服务器
            self.thread = threading.Thread(target=self._run_server, daemon=True)
            self.thread.start()
            
            # 等待服务器启动
            time.sleep(0.1)
            
            self.running = True
            
            # 显示统计信息
            stats = get_stats()
            print(f"🚀 嵌入式HTTP服务器已启动")
            print(f"   - 地址: http://{self.host}:{self.port}")
            print(f"   - 资源文件: {stats['total_files']} 个")
            print(f"   - 压缩大小: {stats['total_compressed_size']:,} bytes ({stats['total_compressed_size']/1024/1024:.2f} MB)")
            print(f"   - 压缩率: {stats['compression_ratio']:.1f}%")
            
            return f"http://{self.host}:{self.port}"
            
        except Exception as e:
            print(f"❌ 启动服务器失败: {e}")
            raise
    
    def _run_server(self):
        """在后台线程中运行服务器"""
        try:
            self.server.serve_forever()
        except Exception as e:
            if self.running:  # 只有在正常运行时才报告错误
                print(f"❌ 服务器运行错误: {e}")
    
    def stop(self):
        """停止服务器"""
        if not self.running:
            return
        
        try:
            self.running = False
            if self.server:
                self.server.shutdown()
                self.server.server_close()
            
            if self.thread and self.thread.is_alive():
                self.thread.join(timeout=2)
            
            print(f"🛑 嵌入式HTTP服务器已停止")
            
        except Exception as e:
            print(f"❌ 停止服务器时出错: {e}")
    
    def get_url(self):
        """获取服务器URL"""
        if self.running:
            return f"http://{self.host}:{self.port}"
        return None
    
    def is_running(self):
        """检查服务器是否在运行"""
        return self.running

# 全局服务器实例
_server_instance = None

def start_embedded_server(host='127.0.0.1', port=0):
    """
    启动嵌入式服务器（单例模式）
    
    Args:
        host: 服务器主机地址
        port: 服务器端口，0表示自动分配
    
    Returns:
        服务器URL字符串
    """
    global _server_instance
    
    if _server_instance and _server_instance.is_running():
        return _server_instance.get_url()
    
    _server_instance = EmbeddedServer(host, port)
    return _server_instance.start()

def stop_embedded_server():
    """停止嵌入式服务器"""
    global _server_instance
    
    if _server_instance:
        _server_instance.stop()
        _server_instance = None

def get_server_url():
    """获取当前服务器URL"""
    global _server_instance
    
    if _server_instance and _server_instance.is_running():
        return _server_instance.get_url()
    return None

if __name__ == '__main__':
    # 测试服务器
    print("🧪 测试嵌入式HTTP服务器")
    
    if not HAS_EMBEDDED_RESOURCES:
        print("❌ 请先运行 pack_frontend_resources.py 生成资源文件")
        exit(1)
    
    try:
        # 启动服务器
        url = start_embedded_server()
        print(f"✅ 服务器启动成功: {url}")
        print("按 Ctrl+C 停止服务器...")
        
        # 保持运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号")
        stop_embedded_server()
        print("👋 再见！")
