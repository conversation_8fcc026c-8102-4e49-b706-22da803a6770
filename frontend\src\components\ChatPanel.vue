<template>
  <div class="chat-panel" :class="{ 'settings-open': isSettingsPanelOpen }">
    <!-- 重新设计的头部区域 -->
    <header class="chat-header">
      <!-- 左侧区域：返回按钮 + 标题 -->
      <div class="header-left">
        <button class="back-btn" @click="handleBack">
          <el-icon><ArrowLeft /></el-icon>
        </button>
        <div class="chat-title">
          <span v-if="chatData">{{ chatData.title || '未命名对话' }}</span>
          <span v-else>{{ chatId ? '加载中...' : '新对话' }}</span>
        </div>
      </div>

      <!-- 右侧区域：控制按钮 -->
      <div class="header-right">
        <!-- 紧凑的设置按钮 -->
        <div class="settings-toggle" @click="toggleSettingsPanel">
          <el-icon><Setting /></el-icon>
        </div>

        <!-- 记忆模式切换 -->
        <div
          class="memory-toggle"
          :class="{
            'memory-enabled': chatMemoryEnabled,
            'memory-disabled': !chatMemoryEnabled
          }"
          @click="toggleMemoryMode"
          :title="chatMemoryEnabled ? '记忆模式：保持对话上下文' : '单次模式：每次独立对话'"
        >
          <el-icon v-if="chatMemoryEnabled"><Histogram /></el-icon>
          <el-icon v-else><Lightning /></el-icon>
        </div>
      </div>
    </header>

    <!-- 可折叠的设置面板 -->
    <div class="settings-panel" :class="{ 'is-open': isSettingsPanelOpen }">
      <div class="settings-content">
        <!-- 模型选择 -->
        <div class="setting-group">
          <label class="setting-label">AI模型</label>
          <UniversalSelector
            v-model="selectedModel"
            :options="modelOptions"
            placeholder="请选择模型"
            header-title="选择AI模型"
            :searchable="modelOptions.length > 8"
            max-height="320px"
            @change="onModelChange"
          />
        </div>

        <!-- 角色选择 -->
        <div class="setting-group">
          <label class="setting-label">AI角色</label>
          <UniversalSelector
            v-model="selectedRoles"
            :options="roleOptions"
            :multiple="true"
            placeholder="默认角色"
            header-title="选择AI角色"
            :searchable="roleOptions.length > 6"
            max-height="300px"
            @change="onRoleChange"
          />
        </div>
      </div>
    </div>

    <!-- 消息区域 -->
    <div ref="messagesContainer" class="messages-container" @scroll="handleScroll">
      <!-- 系统消息 -->
      <div v-if="chatData && selectedRoles.length > 0" class="system-role-info">
        <div class="role-badge" v-for="roleId in selectedRoles" :key="roleId">
          {{ getRoleName(roleId) }}
            </div>
        </div>
      
      <!-- 消息列表 -->
      <div class="messages-list">
        <template v-for="(message, index) in chatMessages">
          <!-- 系统通知 -->
          <div v-if="message.isSystemNotification" :key="message.id || 'system-' + index" class="system-message">
            {{ message.content }}
    </div>

          <!-- 用户消息和AI消息 -->
          <MarkdownBubble
            v-else
            :key="message.id || index"
            :content="message.content"
            :messageType="message.role"
            :isError="message.isError"
            :timestamp="message.timestamp * 1000"
            :reasoning="message.reasoning"
            :reasoningTime="parseReasoningTime(message.reasoningTime)"
            :hasReasoning="!!message.reasoning"
            :disabled="isGenerating"
            :senderName="message.role === 'user' ? '用户' : '助手'"
            @resend="resendMessage(message)"
            @copy="handleCopyMessage"
            @regenerate="handleRegenerateMessage(message)"
          />
        </template>
            </div>

      <!-- 加载指示器 -->
      <div v-if="isGenerating" class="typing-indicator">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
    
    <!-- 输入区域 -->
    <div class="input-area">
      <div class="input-container">
        <div class="textarea-wrapper">
        <highlight-input
          ref="inputRef"
          v-model="inputMessage"
          :autosize="{ minRows: 1, maxRows: 5 }"
          placeholder="输入消息... (输入 @ 可选择实体、章节、场景等)"
          :disabled="isGenerating && !props.selectedText"
          :entity-references="entityReferences"
          @keydown.enter.exact.prevent="sendMessage"
          @keydown.ctrl.enter="inputMessage += '\n'"
          @input="handleInputChange"
          @keydown="handleKeydown"
          @at-typed="handleAtTyped"
          />

          <button
            v-if="props.selectedText"
            class="selected-text-btn" 
            @click="useSelectedText"
            :disabled="isGenerating" 
          >
            <div class="btn-icon">
              <el-icon><Edit /></el-icon>
      </div>
            <span>使用选中文本</span>
          </button>
    </div>

        <button 
          class="send-btn" 
          @click="isGenerating ? handleStopGenerating() : sendMessage()" 
          :disabled="!isGenerating && !inputMessage.trim()" 
          :class="{ 'is-generating': isGenerating, 'stop-generating': isGenerating }"
        >
          <div class="btn-icon">
            <el-icon v-if="!isGenerating"><ArrowUp /></el-icon>
            <el-icon v-else><VideoPause /></el-icon> 
      </div>
          <span>{{ isGenerating ? '停止' : '发送' }}</span>
        </button>
      </div>
    </div>

    <!-- 实体选择器 - 悬浮在最外层 -->
    <entity-selector
      :visible="showEntitySelector"
      :book-id="props.bookId"
      :position="selectorPosition"
      :search-query="entitySearchQuery"
      @select="handleEntitySelect"
      @close="closeEntitySelector"
      ref="entitySelectorRef"
    />
  </div>
</template>




<script setup>
import { ref, computed, onMounted, nextTick, watch, onUnmounted } from 'vue'

import { marked } from 'marked'
import DOMPurify from 'dompurify'
import hljs from 'highlight.js/lib/core'
import javascript from 'highlight.js/lib/languages/javascript'
import python from 'highlight.js/lib/languages/python'
import xml from 'highlight.js/lib/languages/xml'
import css from 'highlight.js/lib/languages/css'
import json from 'highlight.js/lib/languages/json'
import { useConfigStore } from '@/stores/config'
import { useAIRolesStore } from '@/stores/aiRoles'
import { useAIProvidersStore } from '@/stores/aiProviders'
import MarkdownBubble from '@/components/MarkdownBubble.vue'
import EntitySelector from '@/components/EntitySelector.vue'
import HighlightInput from '@/components/HighlightInput.vue'
import UniversalSelector from '@/components/UniversalSelector.vue'
import {
  ArrowLeft,
  ArrowDown,
  ArrowUp,
  Document,
  Edit,
  Histogram,
  Lightning,
  Pointer,
  RefreshRight,
  Opportunity,
  Position,
  Loading,
  VideoPause,
  Check,
  Setting,
  Close
} from '@element-plus/icons-vue'
import { nanoid } from 'nanoid'
import { ElMessage } from 'element-plus'

// 注册常用语言的语法高亮
hljs.registerLanguage('javascript', javascript)
hljs.registerLanguage('js', javascript)
hljs.registerLanguage('python', python)
hljs.registerLanguage('py', python)
hljs.registerLanguage('html', xml)
hljs.registerLanguage('css', css)
hljs.registerLanguage('json', json)

// 导入语法高亮样式
import 'highlight.js/styles/github.css'  // 亮色主题
import 'highlight.js/styles/github-dark.css' // 暗色主题

// 配置 marked
marked.setOptions({
  highlight: (code, lang) => {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value
      } catch (err) {
        console.error('代码高亮出错:', err)
      }
    }
    return hljs.highlightAuto(code).value
  },
  breaks: true,
  gfm: true
})

// 存储管理
const configStore = useConfigStore()
const aiRolesStore = useAIRolesStore()
const aiProvidersStore = useAIProvidersStore()

// 获取模型配置的方法
const getModelConfig = (modelUniqueId) => {
  try {
    // 从aiProvidersStore获取模型配置
    const model = aiProvidersStore.allAvailableModels.find(m => m.uniqueId === modelUniqueId)
    if (model && model.config) {
      console.log('ChatPanel获取到模型配置:', model.config)
      return model.config
    }

    // 如果没有找到配置，返回默认配置
    console.log('ChatPanel未找到模型配置，使用默认配置')
    return {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true
    }
  } catch (error) {
    console.error('ChatPanel获取模型配置失败:', error)
    return {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true
    }
  }
}

// 组件属性
const props = defineProps({
  chatId: { type: String, default: '' },
  bookId: { type: String, default: '' },
  editor: { type: Object, default: null },
  selectedText: { type: String, default: '' }
})

// 组件事件
const emit = defineEmits(['back', 'chat-updated', 'insert-text'])

// 响应式状态
const chatMessages = ref([])
const inputMessage = ref('')
const isGenerating = ref(false)
const messagesContainer = ref(null)
const inputRef = ref(null)
const loadingChat = ref(false)
const chatData = ref(null)
const chatMemoryEnabled = ref(true)
const selectedModel = ref('')
const loadingModels = ref(false)
const selectedRoles = ref([])
const isSettingsPanelOpen = ref(false)

// 实体选择器相关状态
const showEntitySelector = ref(false)
const selectorPosition = ref({ top: 0, left: 0 })
const entitySearchQuery = ref('')
const entitySelectorRef = ref(null)
const atPosition = ref(0) // @符号的位置

// 保存原始处理函数的引用
let originalReceiveChunk = null
let originalOnMessageComplete = null
let originalReceiveChatError = null

// 计算属性
const availableModels = computed(() => {
  // 使用 modelOptions 获取更好的显示效果
  const options = aiProvidersStore.modelOptions
  return options.map(option => ({
    id: option.uniqueId,  // 使用唯一标识符作为ID
    name: option.label,  // 使用 label 作为显示名称
    providerId: option.providerId,
    providerName: option.providerName,
    uniqueId: option.uniqueId,  // 添加uniqueId字段
    config: option.config  // 添加config字段
  }))
})

// 模型选项（用于下拉选择）
const modelOptions = computed(() => {
  return availableModels.value.map(model => ({
    value: model.id,
    label: model.name,
    description: model.providerName ? `提供商: ${model.providerName}` : undefined,
    provider: model.providerName
  }))
})

// 角色选项（用于下拉选择）
const roleOptions = computed(() => {
  const options = availableRoles.value.map(role => ({
    value: role.id,
    label: role.name || role.id,
    description: role.description
  }))
  console.log('ChatPanel roleOptions:', options)
  return options
})

const availableRoles = computed(() => {
  return aiRolesStore.roles.filter(role => role.isEnabled !== false).map(role => ({
    id: role.id,
    name: role.name || role.id,
    description: role.description || '',
    prompt: role.prompt || ''
  }))
})

// 方法
const handleBack = () => {
  emit('back')
}

// 实体选择器相关方法
const handleInputChange = (value) => {
  // 如果选择器已经显示，处理搜索逻辑
  if (showEntitySelector.value) {
    const cursorPos = inputRef.value?.getCursorPosition?.() || 0
    const beforeCursor = value.slice(0, cursorPos)
    const atIndex = beforeCursor.lastIndexOf('@')

    if (atIndex !== -1 && atIndex === atPosition.value) {
      // 更新搜索查询
      const searchText = beforeCursor.slice(atIndex + 1)
      if (searchText.includes(' ') || searchText.includes('\n')) {
        // 如果包含空格或换行，关闭选择器
        closeEntitySelector()
      } else {
        entitySearchQuery.value = searchText
      }
    } else {
      // @符号位置改变或被删除，关闭选择器
      closeEntitySelector()
    }
  }
}

const handleKeydown = (event) => {
  if (showEntitySelector.value) {
    if (event.key === 'Escape') {
      event.preventDefault()
      closeEntitySelector()
    }
    // 其他键盘事件可以传递给选择器组件处理
  }
}

// 处理@符号输入
const handleAtTyped = (data) => {
  atPosition.value = data.position - 1 // @符号的位置
  showEntitySelector.value = true
  entitySearchQuery.value = ''
  updateSelectorPosition()
}



const updateSelectorPosition = () => {
  if (!inputRef.value?.editableDiv) {
    return
  }

  const inputEl = inputRef.value.editableDiv
  if (!inputEl) {
    return
  }

  // 获取输入框的位置信息
  const inputRect = inputEl.getBoundingClientRect()

  // 获取当前光标位置和文本内容
  const selection = window.getSelection()
  let textBeforeCursor = ''

  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    // 获取光标前的文本内容
    const preRange = document.createRange()
    preRange.selectNodeContents(inputEl)
    preRange.setEnd(range.startContainer, range.startOffset)
    textBeforeCursor = preRange.toString()
  }

  // 估算光标位置（简化版本）
  const lines = textBeforeCursor.split('\n')
  const currentLine = lines.length - 1
  const currentLineText = lines[currentLine] || ''

  // 获取字体信息
  const computedStyle = window.getComputedStyle(inputEl)
  const fontSize = parseFloat(computedStyle.fontSize)
  const lineHeight = parseFloat(computedStyle.lineHeight) || fontSize * 1.5

  // 尝试获取更精确的光标位置
  let cursorX = inputRect.left + 12 // 默认padding
  let cursorY = inputRect.top + 12 // 默认padding

  if (selection.rangeCount > 0) {
    try {
      const range = selection.getRangeAt(0)
      const rect = range.getBoundingClientRect()
      if (rect.width > 0 || rect.height > 0) {
        // 如果能获取到range的位置，使用精确位置
        cursorX = rect.left
        cursorY = rect.top
      } else {
        // 否则使用估算位置
        const charWidth = fontSize * 0.6 // 大概的字符宽度
        const estimatedX = currentLineText.length * charWidth
        cursorX = inputRect.left + Math.min(estimatedX, inputRect.width - 20) + 12
        cursorY = inputRect.top + (currentLine * lineHeight) + 12
      }
    } catch (e) {
      // 如果获取range位置失败，使用估算位置
      const charWidth = fontSize * 0.6
      const estimatedX = currentLineText.length * charWidth
      cursorX = inputRect.left + Math.min(estimatedX, inputRect.width - 20) + 12
      cursorY = inputRect.top + (currentLine * lineHeight) + 12
    }
  }

  const viewportHeight = window.innerHeight
  const viewportWidth = window.innerWidth
  const selectorHeight = 400 // 选择器的固定高度
  const selectorWidth = 320 // 选择器的固定宽度

  // 获取聊天消息区域的位置信息，避免覆盖
  const messagesContainer = document.querySelector('.messages-container')
  let chatAreaTop = 0
  let chatAreaBottom = viewportHeight

  if (messagesContainer) {
    const messagesRect = messagesContainer.getBoundingClientRect()
    chatAreaTop = messagesRect.top
    chatAreaBottom = messagesRect.bottom
  }

  let top, left

  // 智能定位：避免覆盖聊天对话区域
  // 1. 优先尝试在光标上方显示
  if (cursorY - selectorHeight > chatAreaTop + 10) {
    // 显示在光标上方，且不覆盖聊天区域
    top = cursorY - selectorHeight - 8
  }
  // 2. 尝试在光标下方显示
  else if (cursorY + 24 + selectorHeight < chatAreaBottom - 10) {
    // 显示在光标下方，且不覆盖聊天区域
    top = cursorY + 24
  }
  // 3. 尝试在聊天区域右侧显示
  else if (messagesContainer && cursorX + selectorWidth + 20 < viewportWidth) {
    // 显示在右侧
    top = Math.max(chatAreaTop + 10, Math.min(cursorY - 50, chatAreaBottom - selectorHeight - 10))
    left = cursorX + 20
  }
  // 4. 尝试在聊天区域左侧显示
  else if (messagesContainer && cursorX - selectorWidth - 20 > 0) {
    // 显示在左侧
    top = Math.max(chatAreaTop + 10, Math.min(cursorY - 50, chatAreaBottom - selectorHeight - 10))
    left = cursorX - selectorWidth - 20
  }
  // 5. 最后的备选方案：显示在输入框上方，可能会部分覆盖
  else {
    top = Math.max(10, inputRect.top - selectorHeight - 8)
  }

  // 如果还没有设置 left，使用默认的水平定位逻辑
  if (left === undefined) {
    left = cursorX
    if (left + selectorWidth > viewportWidth) {
      left = viewportWidth - selectorWidth - 10
    }
    if (left < 10) {
      left = 10
    }
  }

  // 确保选择器不会超出视口边界
  top = Math.max(10, Math.min(top, viewportHeight - selectorHeight - 10))
  left = Math.max(10, Math.min(left, viewportWidth - selectorWidth - 10))

  const position = { top, left }

  selectorPosition.value = position
}

const closeEntitySelector = () => {
  showEntitySelector.value = false
  entitySearchQuery.value = ''
  atPosition.value = 0
}

// 存储实体引用信息（用于发送消息时展开）
const entityReferences = ref(new Map()) // key: @entityName, value: fullContent

// 处理实体引用，将标签替换为完整内容
const processEntityReferences = (content) => {
  if (!content || entityReferences.value.size === 0) {
    return content
  }

  let processedContent = content

  // 遍历所有实体引用，将标签替换为完整内容
  entityReferences.value.forEach((fullContent, tagText) => {
    const regex = new RegExp(escapeRegExp(tagText), 'g')
    processedContent = processedContent.replace(regex, fullContent)
  })

  return processedContent
}

// 转义正则表达式特殊字符
const escapeRegExp = (string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

const handleEntitySelect = async (selection) => {
  if (selection.type === 'multiple') {
    // 处理多选结果
    await handleMultipleSelection(selection.items)
    return
  }

  // 处理单选结果（保持原有逻辑）
  const { type, template, entity, entities, chapter, volume, pool } = selection

  let tagText = ''
  let fullContent = ''

  if (type === 'template') {
    // 选择了模板下的所有实体
    tagText = `@${template.name}`
    fullContent = formatTemplateEntities(template, entities)
  } else if (type === 'entity') {
    // 选择了单个实体
    tagText = `@${entity.name}`
    fullContent = formatSingleEntity(entity, template)
  } else if (type === 'chapter') {
    // 选择了章节 - 需要获取章节内容
    tagText = `@${chapter.title}`

    try {
      // 获取完整的章节内容
      const response = await window.pywebview.api.book_controller.get_chapter(
        props.bookId,
        volume.id,
        chapter.id
      )
      const result = typeof response === 'string' ? JSON.parse(response) : response

      if (result.status === 'success' && result.data) {
        // 使用包含内容的完整章节数据
        fullContent = formatChapter(result.data, volume)
      } else {
        // 如果获取失败，使用基本信息
        fullContent = formatChapter(chapter, volume)
        ElMessage.warning('获取章节内容失败，仅显示基本信息')
      }
    } catch (error) {
      console.error('获取章节内容失败:', error)
      fullContent = formatChapter(chapter, volume)
      ElMessage.warning('获取章节内容失败，仅显示基本信息')
    }
  } else if (type === 'scenePool') {
    // 选择了场景卡池
    tagText = `@${pool.name}`
    fullContent = formatScenePool(pool)
  }

  // 存储实体引用信息
  entityReferences.value.set(tagText, fullContent)

  // 使用HighlightInput的方法替换@和搜索文本
  if (inputRef.value?.replaceAtWithEntity) {
    inputRef.value.replaceAtWithEntity(tagText, entitySearchQuery.value)
  }

  // 关闭选择器
  closeEntitySelector()

  // 聚焦回输入框
  nextTick(() => {
    if (inputRef.value?.focus) {
      inputRef.value.focus()
    }
  })
}

// 处理多选结果
const handleMultipleSelection = async (items) => {
  if (!items || items.length === 0) {
    ElMessage.warning('没有选择任何项目')
    return
  }

  let combinedTagText = ''
  let combinedContent = '\n=== 多项选择内容 ===\n\n'

  // 按类型分组处理
  const groupedItems = {}
  items.forEach(item => {
    if (!groupedItems[item.type]) {
      groupedItems[item.type] = []
    }
    groupedItems[item.type].push(item)
  })

  // 生成标签文本
  const tagParts = []
  Object.keys(groupedItems).forEach(type => {
    const count = groupedItems[type].length
    const typeNames = {
      'template': '模板',
      'entity': '实体',
      'chapter': '章节',
      'scenePool': '场景卡池'
    }
    tagParts.push(`${count}个${typeNames[type] || type}`)
  })
  combinedTagText = `@[${tagParts.join('、')}]`

  // 处理每个类型的内容
  for (const [type, typeItems] of Object.entries(groupedItems)) {
    if (type === 'template') {
      combinedContent += `【模板信息】(${typeItems.length}个)\n`
      for (const item of typeItems) {
        // 使用EntitySelector传来的实体数据，避免重复API调用
        const templateEntities = item.extraData || []
        combinedContent += formatTemplateEntities(item.data, templateEntities)
        combinedContent += '\n---\n\n'
      }
    } else if (type === 'entity') {
      combinedContent += `【实体信息】(${typeItems.length}个)\n`
      typeItems.forEach(item => {
        const template = item.extraData?.isSearchResult ? item.data.templateInfo : item.extraData
        combinedContent += formatSingleEntity(item.data, template)
        combinedContent += '\n---\n\n'
      })
    } else if (type === 'chapter') {
      combinedContent += `【章节信息】(${typeItems.length}个)\n`
      for (const item of typeItems) {
        try {
          // 获取完整的章节内容
          const response = await window.pywebview.api.book_controller.get_chapter(
            props.bookId,
            item.extraData.id,
            item.data.id
          )
          const result = typeof response === 'string' ? JSON.parse(response) : response

          if (result.status === 'success' && result.data) {
            combinedContent += formatChapter(result.data, item.extraData)
          } else {
            combinedContent += formatChapter(item.data, item.extraData)
          }
        } catch (error) {
          console.error('获取章节内容失败:', error)
          combinedContent += formatChapter(item.data, item.extraData)
        }
        combinedContent += '\n---\n\n'
      }
    } else if (type === 'scenePool') {
      combinedContent += `【场景卡池信息】(${typeItems.length}个)\n`
      typeItems.forEach(item => {
        combinedContent += formatScenePool(item.data)
        combinedContent += '\n---\n\n'
      })
    }
  }

  combinedContent += `以上是选择的${items.length}项内容，请根据需要进行分析或处理。\n`

  // 存储实体引用信息
  entityReferences.value.set(combinedTagText, combinedContent)

  // 使用HighlightInput的方法替换@和搜索文本
  if (inputRef.value?.replaceAtWithEntity) {
    inputRef.value.replaceAtWithEntity(combinedTagText, entitySearchQuery.value)
  }

  // 关闭选择器
  closeEntitySelector()

  // 聚焦回输入框
  nextTick(() => {
    if (inputRef.value?.focus) {
      inputRef.value.focus()
    }
  })

  ElMessage.success(`已选择${items.length}项内容`)
}

// 格式化模板下的所有实体
const formatTemplateEntities = (template, entities) => {
  if (!entities || entities.length === 0) {
    return `\n模板信息如下：\n【模板名称】${template.name}\n【实体数量】0个\n【状态】暂无实体\n\n该模板下暂无实体，请先创建实体后再使用。\n`
  }

  let result = `\n模板信息如下：\n`
  result += `【模板名称】${template.name}\n`
  result += `【实体数量】${entities.length}个\n`

  result += `\n【实体列表】\n`

  entities.forEach((entity, index) => {
    result += `\n${index + 1}. 【实体名称】${entity.name}\n`

    if (entity.description && entity.description.trim()) {
      result += `   【实体描述】${entity.description.trim()}\n`
    }

    // 添加维度信息
    if (entity.dimensions && typeof entity.dimensions === 'object') {
      const dimensions = Object.entries(entity.dimensions)
        .filter(([, value]) => value && value !== '未设定' && value.trim())

      if (dimensions.length > 0) {
        result += `   【属性信息】\n`
        dimensions.forEach(([key, value]) => {
          result += `   - ${key}：${value}\n`
        })
      }
    }
  })

  result += `\n以上是【${template.name}】模板下的所有实体信息，请根据需要进行分析或处理。\n`
  return result
}

// 格式化单个实体
const formatSingleEntity = (entity, template) => {
  let result = `\n实体信息如下：\n`
  result += `【实体名称】${entity.name}\n`

  if (template) {
    result += `【所属模板】${template.name}\n`
  }



  // 实体描述
  if (entity.description && entity.description.trim()) {
    result += `【实体描述】${entity.description.trim()}\n`
  } else {
    result += `【实体描述】暂无描述\n`
  }

  // 添加维度信息
  if (entity.dimensions && typeof entity.dimensions === 'object') {
    const dimensions = Object.entries(entity.dimensions)
      .filter(([, value]) => value && value !== '未设定' && value.trim())

    if (dimensions.length > 0) {
      result += `【属性信息】\n`
      dimensions.forEach(([key, value]) => {
        result += `- ${key}：${value}\n`
      })
    } else {
      result += `【属性信息】暂无详细属性\n`
    }
  } else {
    result += `【属性信息】暂无详细属性\n`
  }



  result += `\n以上是【${entity.name}】实体的详细信息，请根据需要进行分析或处理。\n`
  return result
}

// 格式化章节
const formatChapter = (chapter, volume) => {
  let result = `\n章节信息如下：\n`
  result += `【章节名称】${chapter.title}\n`

  // 章节内容
  if (chapter.content && chapter.content.trim()) {
    // 移除HTML标签，获取纯文本内容
    const textContent = chapter.content.replace(/<[^>]+>/g, '').trim()
    if (textContent) {
      result += `【章节内容】\n${textContent}\n`
    } else {
      result += `【章节内容】暂无内容\n`
    }
  } else {
    result += `【章节内容】暂无内容\n`
  }

  result += `\n以上是该章节的完整信息，请根据需要进行分析或处理。\n`

  return result
}

// 格式化场景卡池
const formatScenePool = (pool) => {
  let result = `\n场景卡池信息如下：\n`
  result += `【卡池名称】${pool.name}\n`





  const sceneCount = pool.scenes?.length || 0
  result += `【场景数量】${sceneCount}个\n`



  if (sceneCount > 0) {
    result += `\n【场景列表】\n`
    pool.scenes.forEach((scene, index) => {
      result += `${index + 1}. 【场景标题】${scene.title}\n`

      if (scene.description && scene.description.trim()) {
        result += `   【场景描述】${scene.description.trim()}\n`
      }

      if (scene.location) {
        result += `   【场景地点】${scene.location}\n`
      }

      if (scene.time) {
        result += `   【场景时间】${scene.time}\n`
      }

      if (scene.characters && scene.characters.length > 0) {
        result += `   【涉及角色】${scene.characters.join('、')}\n`
      }

      if (scene.mood) {
        result += `   【场景氛围】${scene.mood}\n`
      }

      result += '\n'
    })
  } else {
    result += `\n【场景列表】暂无场景\n`
  }

  result += `以上是【${pool.name}】场景卡池的详细信息，请根据需要进行分析或处理。\n`
  return result
}

// 点击外部关闭实体选择器
const closeEntitySelectorOnOutsideClick = (event) => {
  if (!showEntitySelector.value) return

  const entitySelector = entitySelectorRef.value?.$el
  const inputElement = inputRef.value?.$el || inputRef.value?.editableDiv?.parentElement

  if (entitySelector && !entitySelector.contains(event.target) &&
      inputElement && !inputElement.contains(event.target)) {
    closeEntitySelector()
  }
}

// 加载聊天数据
const loadChatData = async () => {
  if (!props.chatId) return
  
  try {
    loadingChat.value = true
    const response = await window.pywebview.api.model_controller.get_chat(props.chatId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status === 'success') {
      chatData.value = result.data
      
      // 设置模型和消息
      const modelId = chatData.value.model_id || chatData.value.model || ''

      // 检查是否已经是新格式的唯一标识符
      const directMatch = availableModels.value.find(m => m.id === modelId)
      if (directMatch) {
        selectedModel.value = modelId
      } else {
        // 可能是旧格式，尝试转换
        const matchingModel = availableModels.value.find(m => m.uniqueId && m.uniqueId.endsWith(':' + modelId))
        if (matchingModel) {
          selectedModel.value = matchingModel.id
          // 更新聊天记录中的模型ID为新格式
          chatData.value.model_id = matchingModel.id
        } else {
          // 找不到匹配的模型，使用默认模型
          if (availableModels.value.length > 0) {
            selectedModel.value = availableModels.value[0].id
            chatData.value.model_id = availableModels.value[0].id
          }
        }
      }

      chatMessages.value = chatData.value.messages || []
      
      // 设置记忆模式
      chatMemoryEnabled.value = chatData.value.memory_enabled !== false
      
      // 设置角色
      selectedRoles.value = chatData.value.roles || []
      
      // 通知父组件更新
      emit('chat-updated', chatData.value)
      
      // 加载聊天数据时强制滚动到底部
      shouldAutoScroll.value = true
      nextTick(() => {
        scrollToBottom(true)
      })
    } else {
      ElMessage.error(result.message || '加载聊天数据失败')
    }
  } catch (error) {
    console.error('加载聊天数据失败:', error)
    ElMessage.error('加载聊天数据失败')
  } finally {
    loadingChat.value = false
  }
}

// 加载模型列表
const loadModels = async () => {
  try {
    loadingModels.value = true

    // 确保AI提供商配置已加载
    try {
      const { useAIProvidersStore } = await import('@/stores/aiProviders')
      const aiProvidersStore = useAIProvidersStore()

      if (!aiProvidersStore.initialized) {
        console.log('ChatPanel: AI提供商配置未初始化，先加载提供商配置')
        await aiProvidersStore.loadProviders()
      }
    } catch (providerError) {
      console.warn('ChatPanel: 加载AI提供商配置失败:', providerError)
    }

    // 模型列表现在从 AI 提供商配置中获取，无需单独加载
    console.log('ChatPanel: 可用模型数量:', availableModels.value.length)

    // 如果没有选择模型，默认选择第一个
    if (!selectedModel.value && availableModels.value.length) {
      const firstModel = availableModels.value[0]
      selectedModel.value = firstModel.id
    }
  } catch (error) {
    console.error('ChatPanel: 加载模型列表失败:', error)
  } finally {
    loadingModels.value = false
  }
}

// 保存聊天数据
const saveChat = async () => {
  if (!chatData.value) return
  
  try {
    const response = await window.pywebview.api.model_controller.save_chat(
      props.chatId,
      {
        ...chatData.value,
        messages: chatMessages.value,
        model_id: selectedModel.value,
        memory_enabled: chatMemoryEnabled.value,
        roles: selectedRoles.value,
        last_updated: Math.floor(Date.now() / 1000)
      }
    )
    
    const result = typeof response === 'string' ? JSON.parse(response) : response
    
    if (result.status !== 'success') {
      console.error('保存聊天数据失败:', result.message)
    }
    
    // 通知父组件更新
    emit('chat-updated', chatData.value)
  } catch (error) {
    console.error('保存聊天数据失败:', error)
  }
}

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim() || isGenerating.value) return
  
  try {
    // 如果是新对话，创建一个聊天数据对象
    if (!chatData.value) {
      chatData.value = {
        id: props.chatId || `chat_${Date.now()}`,
        title: `新对话 ${new Date().toLocaleTimeString()}`,
        model_id: selectedModel.value,
        messages: [],
        roles: selectedRoles.value,
        memory_enabled: chatMemoryEnabled.value,
        created_at: Math.floor(Date.now() / 1000),
        last_updated: Math.floor(Date.now() / 1000)
      }
    }
    
    // 处理实体引用，将标签替换为完整内容
    const processedContent = processEntityReferences(inputMessage.value)

    // 添加用户消息
    const userMessage = {
      role: 'user',
      content: processedContent, // 使用处理后的内容
      id: nanoid(),
      timestamp: Math.floor(Date.now() / 1000)
    }

    chatMessages.value.push(userMessage)
    chatData.value.messages = chatMessages.value
    await saveChat()

    // 清空输入框和实体引用
    const userMessageContent = inputMessage.value
    inputMessage.value = ''
    entityReferences.value.clear() // 清空实体引用
    
    // 发送消息时强制滚动到底部
    shouldAutoScroll.value = true
    scrollToBottom(true)
    
    // 开始生成回复
    isGenerating.value = true
    
    // 准备API消息列表
    const apiMessages = []
    
    // 如果有系统角色设定，添加系统消息
    if (selectedRoles.value.length > 0) {
      const rolePrompts = selectedRoles.value.map(roleId => {
          const role = availableRoles.value.find(r => r.id === roleId)
        return role?.prompt || ''
      }).filter(Boolean)
      
      if (rolePrompts.length > 0) {
        apiMessages.push({
          role: 'system',
          content: rolePrompts.join('\n\n')
        })
      }
    }
    
    // 根据记忆模式决定发送的消息
    if (chatMemoryEnabled.value) {
      // 记忆模式：发送所有历史消息
      chatMessages.value.forEach(msg => {
        if (msg.role !== 'system' && !msg.isSystemNotification) {
          apiMessages.push({
        role: msg.role,
        content: msg.content
          })
        }
      })
    } else {
      // 单次模式：只发送当前用户消息
      apiMessages.push({
        role: 'user',
        content: userMessageContent
      })
    }
    
    // 获取模型配置
    const modelConfig = getModelConfig(selectedModel.value)

    // 合并配置：模型配置优先，只有stream强制为true
    const finalConfig = {
      stream: true,  // 强制启用流式输出
      ...modelConfig  // 模型配置（包括temperature, top_p, max_tokens等）
    }

    // 调用API
    try {
      const response = await window.pywebview.api.model_controller.chat(
        chatData.value.id,
        selectedModel.value,
        apiMessages,
        finalConfig
      )
      
      // 如果返回非流式响应
      if (response && typeof response === 'object') {
        const result = typeof response === 'string' ? JSON.parse(response) : response
        
        if (result.status !== 'success') {
          throw new Error(result.message || '生成回复失败')
        }
        
        if (!result.data.stream && result.data.content) {
          // 处理非流式响应
          const assistantMessage = {
            role: 'assistant',
            content: result.data.content,
            id: nanoid(),
            timestamp: Math.floor(Date.now() / 1000)
          }
          
          chatMessages.value.push(assistantMessage)
          chatData.value.messages = chatMessages.value
          await saveChat()
        }
      }
    } catch (error) {
      console.error('生成回复失败:', error)
      
      // 添加错误消息
      chatMessages.value.push({
        role: 'assistant',
        content: `生成回复时出错: ${error.message || '未知错误'}`,
        id: nanoid(),
        timestamp: Math.floor(Date.now() / 1000),
        isError: true
      })
      
      chatData.value.messages = chatMessages.value
      await saveChat()
      
      ElMessage.error('生成回复失败')
    } finally {
      isGenerating.value = false
      scrollToBottom()
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')
    isGenerating.value = false
  }
}

// 重发消息
const resendMessage = async (message) => {
  if (isGenerating.value) return
  
  // 找到消息索引
  const index = chatMessages.value.findIndex(m => m === message)
  if (index === -1) return
  
  // 删除该消息之后的所有消息
  chatMessages.value = chatMessages.value.slice(0, index + 1)
  
  // 设置输入框内容为该消息
  inputMessage.value = message.content
  
  // 保存更新后的消息列表
      chatData.value.messages = chatMessages.value
      await saveChat()
  
  // 发送消息
  await sendMessage()
}

// 复制消息
const copyMessage = async (content) => {
  try {
    await window.pywebview.api.copy_to_clipboard(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 处理 MarkdownBubble 的复制事件
const handleCopyMessage = async (content) => {
  await copyMessage(content)
}

// 处理 MarkdownBubble 的重新生成事件
const handleRegenerateMessage = async (message) => {
  await resendMessage(message)
}

// 解析推理时间字符串为数字（秒）
const parseReasoningTime = (reasoningTime) => {
  if (!reasoningTime) return 0
  if (typeof reasoningTime === 'number') return reasoningTime
  if (typeof reasoningTime === 'string') {
    // 解析 "2.5秒" 格式的字符串
    const match = reasoningTime.match(/(\d+\.?\d*)/)
    return match ? parseFloat(match[1]) : 0
  }
  return 0
}

// 插入到编辑器
const insertToEditor = (content) => {
  emit('insert-text', content)
  ElMessage.success('内容已插入到编辑器')
}

// 使用选中文本
const useSelectedText = () => {
  if (props.selectedText) {
    inputMessage.value = props.selectedText
    ElMessage.success('已使用选中文本')
  }
}

// 智能滚动系统
const isUserNearBottom = () => {
  if (!messagesContainer.value) return false
  const container = messagesContainer.value
  const threshold = 100 // 距离底部100px内认为是在底部附近
  return container.scrollHeight - container.scrollTop - container.clientHeight <= threshold
}

const shouldAutoScroll = ref(true) // 是否应该自动滚动

// 滚动到底部
const scrollToBottom = (force = false) => {
  nextTick(() => {
    if (messagesContainer.value && (force || shouldAutoScroll.value)) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 智能滚动 - 只有在用户接近底部时才自动滚动
const smartScroll = () => {
  if (isUserNearBottom()) {
    shouldAutoScroll.value = true
    scrollToBottom()
  } else {
    shouldAutoScroll.value = false
  }
}

// 监听用户滚动行为
const handleScroll = () => {
  if (!messagesContainer.value) return

  // 检查用户是否滚动到底部附近
  if (isUserNearBottom()) {
    shouldAutoScroll.value = true
  } else {
    shouldAutoScroll.value = false
  }
}

// 切换记忆模式
const toggleMemoryMode = async () => {
  chatMemoryEnabled.value = !chatMemoryEnabled.value
  
  if (chatData.value) {
    chatData.value.memory_enabled = chatMemoryEnabled.value
    await saveChat()
    
    ElMessage.info(`已切换到${chatMemoryEnabled.value ? '记忆' : '单次'}模式`)
  }
}

// 切换思考过程的显示/隐藏
const toggleReasoning = (message) => {
  if (message && message.reasoning) {
    message.reasoningCollapsed = !message.reasoningCollapsed
  }
}

// 获取角色名称
const getRoleName = (roleId) => {
  const role = availableRoles.value.find(r => r.id === roleId)
  return role?.name || roleId
}

// 格式化Markdown
const formatMarkdown = (content) => {
  if (!content) return ''
  return DOMPurify.sanitize(marked(content))
}

// 格式化思考过程
const formatReasoningMarkdown = (content) => {
  if (!content) return ''
  return DOMPurify.sanitize(marked(content))
}

// 处理流式响应
const handleReceiveChunk = (chunk) => {
  try {
    // 解码Base64字符串
    const decodedData = atob(chunk)
    const jsonString = new TextDecoder('utf-8').decode(
      new Uint8Array([...decodedData].map(c => c.charCodeAt(0)))
    )
    
    const messageData = JSON.parse(jsonString)
    const { chat_id, content, reasoning } = messageData
    
    if (chat_id === props.chatId) {
      // 检查是否已有AI回复
      const lastMessage = chatMessages.value[chatMessages.value.length - 1]
      
      if (!lastMessage || lastMessage.role !== 'assistant') {
        // 创建新的AI回复
        const newMessage = {
          role: 'assistant',
          content: content || '',
          id: nanoid(),
          timestamp: Math.floor(Date.now() / 1000)
        }
        
        // 添加思考过程
        if (reasoning) {
          newMessage.reasoning = reasoning
          newMessage.reasoningCollapsed = true
          newMessage.reasoningTime = 0 // 初始化为0
          newMessage.reasoningStartTime = Date.now()
        }
        
        chatMessages.value.push(newMessage)
      } else {
        // 更新已有AI回复
        if (content) {
          lastMessage.content += content
        }
        
        // 更新思考过程
        if (reasoning) {
          if (!lastMessage.reasoning) {
            lastMessage.reasoning = ''
            lastMessage.reasoningCollapsed = true
            lastMessage.reasoningTime = 0 // 初始化为0
            lastMessage.reasoningStartTime = Date.now()
          }
          
          lastMessage.reasoning += reasoning
        }
      }
      
      // 智能滚动 - 只有在用户接近底部时才自动滚动
      smartScroll()
    }
    } catch (error) {
    console.error('处理消息块失败:', error)
  }
}

// 处理消息完成
const handleMessageComplete = (chat_id) => {
  if (chat_id === props.chatId) {
    isGenerating.value = false
    
    // 更新思考时间
    const lastMessage = chatMessages.value[chatMessages.value.length - 1]
    if (lastMessage && lastMessage.role === 'assistant' && lastMessage.reasoning && lastMessage.reasoningStartTime) {
      const duration = Date.now() - lastMessage.reasoningStartTime
      lastMessage.reasoningTime = duration / 1000 // 存储为秒数（数字）
      delete lastMessage.reasoningStartTime
    }
    
    // 保存聊天记录
    chatData.value.messages = chatMessages.value
    saveChat()
    
    // 滚动到底部
    scrollToBottom()
  }
}

// 处理错误消息
const handleChatError = (chunk) => {
  try {
    // 解码Base64字符串
    const decodedData = atob(chunk)
    const jsonString = new TextDecoder('utf-8').decode(
      new Uint8Array([...decodedData].map(c => c.charCodeAt(0)))
    )
    
    const errorData = JSON.parse(jsonString)
    const { chat_id, error_message } = errorData
    
    console.log('ChatPanel: 收到错误消息:', chat_id, error_message);
    
    // 始终显示错误消息，无论是否是当前聊天
    ElMessage.error({
      message: `AI回复失败: ${error_message}`,
      duration: 5000,
      showClose: true
    });
    
    if (chat_id === props.chatId) {
      isGenerating.value = false
      
      // 添加错误消息
      chatMessages.value.push({
        role: 'assistant',
        content: `[错误: ${error_message}]`,
        id: nanoid(),
        timestamp: Math.floor(Date.now() / 1000),
        isError: true
      })
      
      // 保存聊天记录
    chatData.value.messages = chatMessages.value
      saveChat()
      
      // 滚动到底部
      scrollToBottom()
    }
  } catch (error) {
    console.error('处理错误消息失败:', error)
  }
}

// 切换设置面板显示/隐藏
const toggleSettingsPanel = () => {
  isSettingsPanelOpen.value = !isSettingsPanelOpen.value
}

// 选择器事件处理
const onModelChange = async (value, option) => {
  console.log('Model changed:', value, option)
  // 如果聊天数据存在，更新模型并保存
  if (chatData.value) {
    chatData.value.model_id = value
    await saveChat()
  }
}

const onRoleChange = async (values, option) => {
  console.log('Roles changed:', values, option)
  // 更新选中的角色
  selectedRoles.value = values
  // 如果聊天数据存在，更新角色并保存
  if (chatData.value) {
    chatData.value.roles = values
    await saveChat()

    // 显示角色变更提示
    if (values.length > 0) {
      const roleNames = values.map(roleId => {
        const role = availableRoles.value.find(r => r.id === roleId)
        return role ? role.name : roleId
      }).join(', ')
      ElMessage.success(`已选择角色: ${roleNames}`)
    } else {
      ElMessage.info('已清除所有角色设定')
    }
  }
}





// 监听模型变化
watch(selectedModel, async (newModel, oldModel) => {
  if (newModel !== oldModel && chatData.value) {
    chatData.value.model_id = newModel
    await saveChat()
  }
})

// 生命周期钩子
onMounted(async () => {
  // 保存原始处理函数
  originalReceiveChunk = window.receiveChunk
  originalOnMessageComplete = window.onMessageComplete
  originalReceiveChatError = window.receiveChatError

  // 替换为本组件的处理函数
  window.receiveChunk = handleReceiveChunk
  window.onMessageComplete = handleMessageComplete
  window.receiveChatError = handleChatError
  
  // 加载AI角色
  if (!aiRolesStore.roles.length) {
    await aiRolesStore.loadRoles()
  }
  
  // 初始化数据
  await loadModels()
  await loadChatData()
  
  // 聚焦输入框
  nextTick(() => {
    if (inputRef.value?.focus) {
      inputRef.value.focus()
    }
  })
  


  // 添加点击外部关闭实体选择器的事件监听
  document.addEventListener('click', closeEntitySelectorOnOutsideClick)

})

// 组件卸载时的清理
onUnmounted(() => {
  // 恢复原始处理函数
  if (originalReceiveChunk) window.receiveChunk = originalReceiveChunk
  if (originalOnMessageComplete) window.onMessageComplete = originalOnMessageComplete
  if (originalReceiveChatError) window.receiveChatError = originalReceiveChatError

  // 移除事件监听器
  document.removeEventListener('click', closeEntitySelectorOnOutsideClick)
})
const handleStopGenerating = async () => {
  if (!props.chatId || !isGenerating.value) return;

  try {
    const response = await window.pywebview.api.model_controller.stop_chat(props.chatId);
    const result = typeof response === 'string' ? JSON.parse(response) : response;
    
    if (result.status === 'success') {
      isGenerating.value = false; // Also ensure this is set correctly if stream doesn't send completion
      // The onMessageComplete callback should also handle isGenerating, but good to have here too.
      ElMessage.success('已停止生成');
    } else {
      throw new Error(result.message || '停止生成失败');
    }
  } catch (error) {
    console.error('停止对话失败:', error);
    ElMessage.error(error.message || '停止对话失败');
    // It's possible the generation already stopped or failed, so ensure UI reflects it.
    isGenerating.value = false; 
  }
  // No finally block needed to set isGenerating if onMessageComplete is reliable
  // or if errors always lead to generation stopping on backend.
  // However, for UI responsiveness, explicitly setting it on error is good.
};

// 监听窗口大小变化和 sidebar 宽度变化
let resizeObserver = null
let handleResize = null

onMounted(() => {
  // 监听窗口大小变化
  handleResize = () => {
    if (showEntitySelector.value) {
      nextTick(() => {
        updateSelectorPosition()
      })
    }
  }

  window.addEventListener('resize', handleResize)

  // 监听 CSS 变量变化（sidebar 宽度）
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
        const style = document.documentElement.getAttribute('style')
        if (style && style.includes('--chat-sidebar-width') && showEntitySelector.value) {
          nextTick(() => {
            updateSelectorPosition()
          })
        }
      }
    })
  })

  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['style']
  })

  resizeObserver = observer
})

onUnmounted(() => {
  if (handleResize) {
    window.removeEventListener('resize', handleResize)
  }
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})
</script>


<style lang="scss" scoped>
/* 主容器 */
.chat-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);
  overflow: hidden;

  /* 当设置面板打开时，允许溢出显示 */
  &.settings-open {
    overflow: visible;
  }
}

/* 重新设计的头部样式 */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  z-index: 10;
  user-select: none;
  min-height: 48px;

  .header-left {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0; /* 允许收缩 */

    .back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border: none;
      background: transparent;
      color: var(--el-text-color-regular);
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      flex-shrink: 0;

      &:hover {
        background-color: var(--el-fill-color-light);
        color: var(--el-color-primary);
      }
    }

    .chat-title {
      margin-left: 8px;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      letter-spacing: -0.01em;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      min-width: 0;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 6px;
    flex-shrink: 0;

    .settings-toggle,
    .memory-toggle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border: none;
      background: transparent;
      color: var(--el-text-color-regular);
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 14px;

      &:hover {
        background-color: var(--el-fill-color-light);
        color: var(--el-color-primary);
      }
    }

    .memory-toggle {
      &.memory-enabled {
        background-color: var(--el-color-primary-light-9);
        color: var(--el-color-primary);

        &:hover {
          background-color: var(--el-color-primary-light-8);
        }
      }

      &.memory-disabled {
        color: var(--el-text-color-placeholder);

        &:hover {
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}

/* 可折叠的设置面板 */
.settings-panel {
  background-color: var(--el-bg-color-overlay);
  border-bottom: 1px solid var(--el-border-color-light);
  overflow: visible; /* 改为 visible 以允许下拉框显示 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 0;
  opacity: 0;

  &.is-open {
    max-height: 200px;
    opacity: 1;
    overflow: visible; /* 确保展开时也是 visible */
  }

  .settings-content {
    padding: 12px 16px;

    .setting-group {
      margin-bottom: 12px;
      position: relative; /* 添加相对定位 */

      &:last-child {
        margin-bottom: 0;
      }

      .setting-label {
        display: block;
        font-size: 12px;
        font-weight: 500;
        color: var(--el-text-color-secondary);
        margin-bottom: 6px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      /* 第一个setting-group（模型选择器）有更高的z-index */
      &:first-child {
        z-index: 20;
      }

      /* 其他setting-group有较低的z-index */
      &:not(:first-child) {
        z-index: 10;
      }
    }
  }
}





/* 消息容器 */
.messages-container {
  flex: 1;
  padding: 20px 20px 16px;
  overflow-y: auto;
  // position: relative;
  background-color: var(--el-bg-color);
  
  /* 优化滚动条 */
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color) transparent;
  
  &::-webkit-scrollbar {
    width: 5px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
    margin: 4px 0;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color);
    border-radius: 10px;
    
    &:hover {
      background-color: var(--el-border-color-darker);
    }
  }
  
  /* 消息区域底部渐变效果 */
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40px;
    background: linear-gradient(to top, var(--el-bg-color) 0%, transparent 100%);
    pointer-events: none;
    opacity: 0.8;
    z-index: 1;
  }
}

/* 系统角色信息 */
.system-role-info {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
  justify-content: center;
  position: relative;
  padding: 10px 16px;
  user-select: none;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--el-border-color-light), transparent);
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--el-border-color-light), transparent);
  }
  
  .role-badge {
    padding: 5px 12px;
    border-radius: 16px;
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s;
    border: 1px solid var(--el-color-primary-light-7);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  
  &:hover {
      transform: translateY(-2px);
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
      background-color: var(--el-color-primary-light-8);
    }
  }
}

/* 消息列表 */
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 系统消息 */
.system-message {
  align-self: center;
  max-width: 90%;
  padding: 10px 18px;
  background-color: var(--el-color-info-light-9);
  border-radius: 10px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  font-style: italic;
  text-align: center;
  border: 1px dashed var(--el-border-color);
  margin: 12px 0;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  user-select: text;
  
  &::before {
    content: '•';
    position: absolute;
    left: 6px;
    color: var(--el-text-color-secondary);
    opacity: 0.5;
  }
  
  &::after {
    content: '•';
    position: absolute;
    right: 6px;
    color: var(--el-text-color-secondary);
    opacity: 0.5;
  }
}

/* 正在输入指示器 */
.typing-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  user-select: none;

  span {
    width: 8px;
    height: 8px;
    background-color: var(--el-color-primary-light-7);
    border-radius: 50%;
    margin: 0 3px;
    opacity: 0.8;
    animation: typing-bounce 1.4s infinite;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.4s; }
  }

  @keyframes typing-bounce {
    0%, 80%, 100% { transform: translateY(0); opacity: 0.5; }
    40% { transform: translateY(-8px); opacity: 1; }
  }
}

/* 输入区域 */
.input-area {
  padding: 12px 16px;
  background-color: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-light);
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
  z-index: 5;
  user-select: none;

.input-container {
  display: flex;
    flex-direction: row;
    gap: 10px;
    align-items: flex-start;
  
    .textarea-wrapper {
    flex: 1;
      position: relative;
      cursor: text !important;

      /* 确保所有子元素都显示文本光标 */
      * {
        cursor: text !important;
      }

    :deep(.el-textarea__inner) {
        padding: 12px 16px;
        border-radius: 8px;
        resize: none;
        line-height: 1.5;
        font-size: 15px;
        border: 1px solid var(--el-border-color-light);
        background-color: var(--el-bg-color);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        transition: all 0.2s;
        min-height: 45px;
        max-height: 180px;
        user-select: text;
        
    &:hover {
          border-color: var(--el-border-color);
        }
        
        &:focus {
          border-color: var(--el-color-primary-light-5);
          box-shadow: 0 0 0 1px var(--el-color-primary-light-7);
        }
      }
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background-color: var(--el-color-primary);
        transform: scaleX(0);
        transition: transform 0.3s ease;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
      }
      
      &:focus-within::after {
        transform: scaleX(1);
      }
    }
    
    .selected-text-btn {
      position: absolute;
      right: 8px;
      bottom: 8px;
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 6px 10px;
      border: 1px solid var(--el-border-color-light);
      background-color: var(--el-bg-color);
      border-radius: 6px;
      color: var(--el-text-color-regular);
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      z-index: 1;
      
      &:hover:not(:disabled) {
        border-color: var(--el-color-primary-light-5);
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }
      
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
      
      .btn-icon {
    display: flex;
    align-items: center;
        justify-content: center;
        font-size: 14px;
      }
    }
    
    .send-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 0 22px;
      height: auto;
      min-height: 45px;
      border: none;
      background-color: var(--el-color-primary);
      color: white;
      border-radius: 8px;
    font-weight: 500;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.25s cubic-bezier(0.3, 0, 0.2, 1);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
      flex-shrink: 0;
      align-self: stretch;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.1);
        transform: translateY(100%);
        transition: transform 0.25s cubic-bezier(0.3, 0, 0.2, 1);
      }
      
      &:hover:not(:disabled) {
        background-color: var(--el-color-primary-dark-2);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        
        &::before {
          transform: translateY(0);
        }
      }
      
      &:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
        &::before {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }
      
      &:disabled {
        background-color: var(--el-color-info-light-5);
        color: var(--el-color-info-light-1);
        opacity: 0.75;
        cursor: not-allowed;
        box-shadow: none;
      }
      
      &.is-generating {
        background-color: var(--el-color-warning);
        animation: pulse 2s infinite;
      }
      
      &.stop-generating {
        background-color: var(--el-color-error);
      }
      
      .btn-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
      }
      
      .loading-icon {
        animation: spin 1.2s linear infinite;
      }
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(230, 162, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0);
  }
}

/* 代码块样式 */
:deep(.markdown-body) {
  background: transparent;
  color: inherit;
  
  .code-block {
    margin: 12px 0;
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid var(--el-border-color);
    
    .code-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background-color: var(--el-fill-color-light);
      border-bottom: 1px solid var(--el-border-color);
      
      .code-lang {
        font-size: 12px;
        font-weight: 600;
        color: var(--el-text-color-secondary);
        text-transform: uppercase;
      }
      
      .code-actions {
        button {
          background: none;
          border: none;
          cursor: pointer;
          color: var(--el-text-color-secondary);
          width: 28px;
          height: 28px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s;
          
          &:hover {
            background-color: var(--el-fill-color);
            color: var(--el-color-primary);
          }
          
          i {
            font-size: 16px;
          }
        }
      }
    }
    
    pre {
      margin: 0;
      padding: 12px 16px;
      background-color: var(--el-fill-color-blank);
      overflow-x: auto;
      
      code {
        font-size: 14px;
        line-height: 1.6;
        padding: 0;
        background: transparent;
        color: inherit;
      }
    }
  }
  
  blockquote {
    padding: 8px 16px;
    margin: 12px 0;
    border-left: 4px solid var(--el-border-color);
    background-color: var(--el-fill-color-light);
    color: var(--el-text-color-regular);
  }
  
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 12px 0;
    
    th, td {
      border: 1px solid var(--el-border-color);
      padding: 8px 12px;
    }
    
    th {
      font-weight: 600;
      background-color: var(--el-fill-color-light);
    }
    
    tr:nth-child(even) {
      background-color: var(--el-fill-color-blank);
    }
  }
  
  img {
    max-width: 100%;
    height: auto;
  }
  
  ul, ol {
    padding-left: 24px;
  }
  
  a {
    color: var(--el-color-primary);
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

/* 主题适配: Element Plus 暗黑模式变量会自动改变 */
:deep(html.dark) {
.markdown-body {
    pre, code {
      background-color: var(--el-fill-color-darker) !important;
    }
    
    .hljs {
      background: transparent !important;
    }
  }
}

/* 消息内容重写样式 */
.message-content .message-text {
  user-select: text;
  
  &:deep(.hljs) {
    background: transparent;
  }
  
  &:deep(.hljs-comment),
  &:deep(.hljs-quote) {
    color: #999988;
    font-style: italic;
  }
  
  &:deep(.hljs-keyword),
  &:deep(.hljs-selector-tag) {
    color: var(--el-color-danger);
  }
  
  &:deep(.hljs-string),
  &:deep(.hljs-attr) {
    color: var(--el-color-success);
  }
  
  &:deep(.hljs-number),
  &:deep(.hljs-literal) {
    color: var(--el-color-warning);
  }
  
  &:deep(.hljs-variable),
  &:deep(.hljs-template-variable) {
    color: var(--el-color-primary);
  }
}
</style>