<!--
/**
 * ChatUI - AI 聊天组件
 *
 * @description
 * 一个功能完整的 AI 聊天界面组件，专注于 Chat 模式，
 * 具备完整的主题系统、消息管理等功能。
 *
 * @usage
 * <ChatUI
 *   :theme="'light'"
 *   :initial-model="'gpt-4'"
 *   :models="[
 *     { id: 'gpt-4', name: 'GPT-4', description: '最强大的语言模型' },
 *     { id: 'claude-3', name: 'Claude-<PERSON>', description: '安全可靠的模型' }
 *   ]"
 *   :config="{ maxInputLength: 4000, enableFileUpload: true }"
 *   @message-sent="handleMessageSent"
 *   @message-received="handleMessageReceived"
 *   @model-changed="handleModelChanged"
 *   @toggle-sidebar="handleToggleSidebar"
 * />
 *
 * @props
 * - theme: 'light' | 'dark' - 主题模式
 * - initialModel: string - 初始 AI 模型 ID
 * - models: array - 可用的 AI 模型列表
 * - config: object - 配置选项
 *   - maxInputLength: number - 最大输入长度 (默认: 4000)
 *   - enableFileUpload: boolean - 是否启用文件上传 (默认: true)
 *   - autoScroll: boolean - 是否自动滚动 (默认: true)
 *
 * @events
 * - message-sent: (message) => void - 消息发送时触发
 * - message-received: (message) => void - 收到 AI 回复时触发
 * - model-changed: (model) => void - 模型切换时触发
 * - toggle-sidebar: () => void - 会话历史侧边栏切换时触发
 *
 * @features
 * - 🎨 完整的主题系统 (浅色/暗色)
 * - 💬 流式消息显示
 * - 🔄 消息重新生成
 * - 📋 消息复制功能
 * - 🎯 快速建议功能
 * - ⌨️ 快捷键支持
 * - 📱 响应式设计
 *
 * <AUTHOR> Code
 * @version 2.0.0
 */
-->

<template>
  <div class="mate-chat" :class="[`theme-${theme}`, `mode-${mode}`]">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <!-- 左侧菜单按钮 -->
      <div class="header-left">
        <button class="menu-btn" @click="toggleSidebar" title="会话历史">
          <svg class="menu-icon" viewBox="0 0 16 16" fill="none">
            <path d="M2 4h12M2 8h12M2 12h12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          </svg>
        </button>
      </div>

      <!-- 中间空白区域 -->
      <div class="header-center">
      </div>

      <!-- 右侧控件组 -->
      <div class="header-right">
        <!-- 新建对话按钮 -->
        <button class="new-chat-btn" @click="newChat" title="新建对话">
          <svg class="plus-icon" viewBox="0 0 16 16" fill="none">
            <path d="M8 2V14M2 8H14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 消息区域 -->
    <div class="chat-messages">
      <!-- 会话历史面板 -->
      <div v-if="showSidebar" class="history-panel">
        <!-- 面板头部 -->
        <div class="history-header">
          <div class="header-left">
            <button class="back-btn" @click="showSidebar = false" title="返回">
              <svg class="back-icon" viewBox="0 0 24 24" fill="none">
                <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <div class="header-title">
              <h3 class="history-title">会话历史</h3>
              <span class="history-count">{{ chatSessions.length }} 个会话</span>
            </div>
          </div>

          <div class="header-actions">
            <button class="action-btn refresh-btn" @click="refreshChatSessions" title="刷新会话列表" :disabled="loadingChatSessions">
              <svg class="action-icon refresh-icon" :class="{ 'spinning': loadingChatSessions }" viewBox="0 0 24 24" fill="none">
                <path d="M1 4V10H7M23 20V14H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10M3.51 15A9 9 0 0 0 18.36 18.36L23 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <button class="action-btn new-chat-btn" @click="createNewSession" title="新建会话">
              <svg class="action-icon" viewBox="0 0 24 24" fill="none">
                <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- 搜索栏 -->
        <div class="history-search">
          <div class="search-wrapper">
            <svg class="search-icon" viewBox="0 0 24 24" fill="none">
              <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
              <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <input
              type="text"
              class="search-input"
              placeholder="搜索会话标题、内容或模型..."
              v-model="searchQuery"
            />
            <button v-if="searchQuery" class="clear-search" @click="searchQuery = ''" title="清除搜索">
              <svg viewBox="0 0 24 24" fill="none">
                <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- 会话历史内容 -->
        <div class="history-content">
          <!-- 加载状态 -->
          <div v-if="loadingChatSessions" class="loading-sessions">
            <div class="loading-spinner"></div>
            <span class="loading-text">加载会话中...</span>
          </div>

          <!-- 空状态 -->
          <div v-else-if="filteredSessions.length === 0" class="empty-sessions">
            <div class="empty-illustration">
              <svg class="empty-icon" viewBox="0 0 120 120" fill="none">
                <circle cx="60" cy="60" r="50" stroke="currentColor" stroke-width="2" opacity="0.3"/>
                <path d="M40 50H80M40 60H70M40 70H75" stroke="currentColor" stroke-width="2" stroke-linecap="round" opacity="0.5"/>
                <circle cx="45" cy="35" r="8" stroke="currentColor" stroke-width="2" opacity="0.4"/>
                <circle cx="75" cy="35" r="8" stroke="currentColor" stroke-width="2" opacity="0.4"/>
              </svg>
            </div>
            <div class="empty-content">
              <h4 class="empty-title">{{ searchQuery ? '未找到匹配的会话' : '还没有会话记录' }}</h4>
              <p class="empty-description">
                {{ searchQuery ? '尝试使用其他关键词搜索' : '开始你的第一次对话吧' }}
              </p>
              <button v-if="!searchQuery" class="empty-action" @click="createNewSession">
                <svg class="action-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                创建新会话
              </button>
            </div>
          </div>

          <!-- 会话列表 -->
          <div v-else class="sessions-container">
            <div class="sessions-list">
              <div
                v-for="session in filteredSessions"
                :key="session.id || session.chat_id"
                class="session-item"
                :class="{
                  'active': (session.id || session.chat_id) === currentSessionId,
                  'has-messages': session.messages && session.messages.length > 0
                }"
                @click="switchToSession(session.id || session.chat_id)"
              >
                <div class="session-avatar">
                  <svg class="avatar-icon" viewBox="0 0 24 24" fill="none">
                    <path d="M21 15A2 2 0 0 1 19 17H7L4 20V5A2 2 0 0 1 6 3H19A2 2 0 0 1 21 5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>

                <div class="session-content">
                  <div class="session-header">
                    <h4 class="session-title">{{ session.title || '未命名会话' }}</h4>
                    <span class="session-time">{{ formatTime(session.last_updated) }}</span>
                  </div>

                  <div class="session-preview">
                    <!-- 如果有搜索查询，显示匹配的内容片段 -->
                    <div v-if="searchQuery.trim()" class="search-match-preview">
                      <span class="match-snippet" v-html="getSearchMatchSnippet(session)"></span>
                    </div>
                    <!-- 否则显示默认信息 -->
                    <div v-else class="session-info">
                      <span class="message-count">{{ (session.messages || []).length }} 条消息</span>
                      <span class="session-model" v-if="session.model_id">
                        {{ getModelDisplayName(session.model_id) }}
                      </span>
                    </div>
                  </div>
                </div>

                <div class="session-actions">
                  <button
                    class="action-btn delete-btn"
                    @click.stop="deleteSession(session.id || session.chat_id)"
                    title="删除会话"
                  >
                    <svg class="action-icon" viewBox="0 0 24 24" fill="none">
                      <path d="M3 6H5H21M8 6V4A2 2 0 0 1 10 2H14A2 2 0 0 1 16 4V6M19 6V20A2 2 0 0 1 17 22H7A2 2 0 0 1 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 欢迎界面 -->
      <div v-if="!chatData || chatMessages.length === 0" class="welcome-screen">
        <div class="welcome-content">
          <div class="welcome-icon">
            <div class="icon-bg">💬</div>
          </div>
          <h2 class="welcome-title">
            {{ !chatData ? '选择或创建会话' : 'AI 对话助手' }}
          </h2>
          <p class="welcome-subtitle">
            {{ !chatData ? '从左侧历史记录中选择一个会话，或点击"+"创建新会话开始对话' : '开始与我对话，我会尽力为您提供帮助和解答' }}
          </p>
        </div>
      </div>

      <!-- 消息列表 -->
      <div v-else class="messages-list" ref="messagesContainer" @scroll="handleScroll">
        <template v-for="(message, index) in chatMessages">
          <!-- 系统通知消息 -->
          <div v-if="message.isSystemNotification" :key="message.id || 'system-' + index" class="system-message">
            {{ message.content }}
          </div>

          <!-- 用户消息和AI消息 -->
          <MarkdownBubble
            v-else
            :key="message.id || index"
            :content="processMessageContent(message.content, message.role, message)"
            :messageType="message.role"
            :isError="message.isError"
            :timestamp="message.timestamp * 1000"
            :reasoning="message.reasoning"
            :reasoningTime="parseReasoningTime(message.reasoningTime)"
            :hasReasoning="!!message.reasoning"
            :disabled="isGenerating"
            :senderName="message.role === 'user' ? '用户' : '助手'"
            @resend="resendMessage(message)"
            @copy="handleCopyMessage"
            @regenerate="handleRegenerateMessage(message)"
          />
        </template>

        <!-- 加载指示器 -->
        <div v-if="isGenerating" class="loading-indicator">
          <div class="loading-wrapper">
            <div class="loading-spinner-ai">
              <div class="spinner-ring"></div>
              <div class="spinner-dot"></div>
            </div>
            <div class="loading-text">
              <span class="text-content">AI 正在思考</span>
              <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <div class="input-container">
        <div class="input-wrapper">
          <div class="input-main">
            <!-- 富文本输入区域 -->
            <div class="rich-input-container">
              <div
                ref="inputRef"
                class="rich-message-input"
                :class="{ 'disabled': isGenerating || !chatData }"
                contenteditable="true"
                :data-placeholder="!chatData ? '请先选择或创建一个会话' : '输入您的消息...'"
                @keydown="handleKeyDown"
                @input="handleRichInput"
                @paste="handlePaste"
                @focus="handleInputFocus"
                @blur="handleInputBlur"
              ></div>
            </div>

            <!-- 输入框底部工具栏 -->
            <div class="input-bottom-bar">
              <!-- 左侧控件组 -->
              <div class="bottom-left">
                <!-- 章节内容按钮 -->
                <button
                  class="icon-btn context-btn"
                  :class="{ 'active': includeEditorContent }"
                  :disabled="isLoading"
                  @click="toggleEditorContent"
                  :title="includeEditorContent ? '已开启：编辑器内容将自动包含到对话中' : '点击开启：将编辑器内容包含到对话中（与@引用功能独立）'"
                >C</button>

                <!-- 分隔符 -->
                <div class="separator"></div>

                <!-- 引用按钮 -->
                <button
                  class="icon-btn reference-btn"
                  :disabled="isLoading"
                  @click="openEntitySelector"
                  title="引用特定实体、章节等内容（与编辑器内容包含功能独立）"
                >@</button>

                <!-- 分隔符 -->
                <div class="separator"></div>

                <!-- 模型选择器 -->
                <div class="model-selector" @click="toggleModelDropdown" ref="modelSelector">
                  <button class="model-btn">
                    <span class="model-icon">
                      <svg class="model-icon-svg" viewBox="0 0 16 16" fill="none">
                        <path d="M2 4h12M2 8h8M2 12h10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                      </svg>
                    </span>
                    <span class="model-name">{{ currentModel.name }}</span>
                    <svg class="model-dropdown-arrow" viewBox="0 0 16 16" fill="none" :class="{ 'rotated': showModelDropdown }">
                      <path d="M4 10L8 6L12 10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                </div>

                <!-- 分隔符 -->
                <div class="separator"></div>

                <!-- 系统设定选择器 -->
                <div class="role-selector" @click="toggleRoleDropdown" ref="roleSelector">
                  <button class="role-btn">
                    <span class="role-icon">
                      <svg class="role-icon-svg" viewBox="0 0 16 16" fill="none">
                        <path d="M8 2a3 3 0 1 0 0 6 3 3 0 0 0 0-6zM4 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H4z" fill="currentColor"/>
                      </svg>
                    </span>
                    <span class="role-name">{{ currentRoleDisplayName }}</span>
                    <svg class="role-dropdown-arrow" viewBox="0 0 16 16" fill="none" :class="{ 'rotated': showRoleDropdown }">
                      <path d="M4 10L8 6L12 10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                </div>
              </div>

              <!-- 右侧控件组 -->
              <div class="bottom-right">
                <!-- 发送按钮 -->
                <button
                  v-if="!isLoading"
                  class="icon-btn send-btn"
                  :class="{ 'can-send': canSend }"
                  :disabled="!canSend"
                  @click="sendMessage"
                  @click.capture="() => console.log('ChatUI: 发送按钮被点击')"
                  title="发送消息"
                >
                  <svg class="send-icon" viewBox="0 0 16 16" fill="none">
                    <path d="M2 8L14 2L10 8L14 14L2 8Z" fill="currentColor"/>
                  </svg>
                </button>

                <!-- 停止按钮 -->
                <button
                  v-else
                  class="icon-btn stop-btn"
                  @click="stopGeneration"
                  title="停止生成"
                >
                  ⏹
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模型下拉列表 - 渲染在最外层避免被 overflow 裁剪 -->
    <div v-if="showModelDropdown" class="model-dropdown-overlay" @click="closeModelDropdown">
      <div class="model-dropdown" :style="modelDropdownStyle" @click.stop>
        <div
          v-for="model in models"
          :key="model.id"
          class="model-option"
          :class="{ 'active': currentModel.id === model.id }"
          @click="selectModel(model)"
        >
          {{ model.name }}
        </div>
      </div>
    </div>

    <!-- 角色下拉列表 - 渲染在最外层避免被 overflow 裁剪 -->
    <div v-if="showRoleDropdown" class="role-dropdown-overlay" @click="closeRoleDropdown">
      <div class="role-dropdown" :style="roleDropdownStyle" @click.stop>
        <div
          v-for="role in availableRoles"
          :key="role.id"
          class="role-option"
          :class="{ 'active': selectedRoles.includes(role.id) }"
          @click="selectRole(role)"
        >
          <div class="role-option-content">
            <span class="role-name">{{ role.name || role.id }}</span>
            <div class="role-checkbox" :class="{ 'checked': selectedRoles.includes(role.id) }">
              <svg v-if="selectedRoles.includes(role.id)" class="check-icon" viewBox="0 0 16 16" fill="none">
                <path d="M13.5 4.5L6 12L2.5 8.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="role-description" v-if="role.description">
            {{ role.description }}
          </div>
        </div>
        <div v-if="availableRoles.length === 0" class="no-roles">
          <span>暂无可用角色</span>
        </div>
        <div v-if="selectedRoles.length > 0" class="role-actions">
          <button class="clear-all-btn" @click="clearAllRoles">
            清除全部
          </button>
        </div>
      </div>
    </div>

    <!-- EntitySelector 组件 -->
    <EntitySelector
      :visible="showEntitySelector"
      :book-id="bookId"
      :position="entitySelectorPosition"
      @select="handleEntitySelect"
      @close="closeEntitySelector"
    />
  </div>
</template>

<script setup>
import { ref, computed, nextTick,toRaw , onMounted, onBeforeUnmount, watch, onUnmounted, provide } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useConfigStore } from '@/stores/config'
import { useAIRolesStore } from '@/stores/aiRoles'
import { useAIProvidersStore } from '@/stores/aiProviders'
import MarkdownBubble from '@/components/MarkdownBubble.vue'
import EntitySelector from '@/components/EntitySelector.vue'
import { nanoid } from 'nanoid'

// 存储管理
const configStore = useConfigStore()
const aiRolesStore = useAIRolesStore()
const aiProvidersStore = useAIProvidersStore()

// 提供配置存储给子组件
provide('configStore', configStore)

// 获取模型配置的方法
const getModelConfig = (modelUniqueId) => {
  try {
    // 从aiProvidersStore获取模型配置
    const model = aiProvidersStore.allAvailableModels.find(m => m.uniqueId === modelUniqueId)
    if (model && model.config) {
      console.log('ChatUI获取到模型配置:', model.config)
      return model.config
    }

    // 如果没有找到配置，返回默认配置
    console.log('ChatUI未找到模型配置，使用默认配置')
    return {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true
    }
  } catch (error) {
    console.error('ChatUI获取模型配置失败:', error)
    return {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true
    }
  }
}



// 确认对话框
const MessageBox = {
  confirm: (message, title = '确认') => {
    return new Promise((resolve, reject) => {
      if (confirm(`${title}\n\n${message}`)) {
        resolve()
      } else {
        reject()
      }
    })
  }
}

// ===== Props 定义 =====
const props = defineProps({
  chatId: { type: String, default: '' },
  bookId: { type: String, default: '' },
  editor: { type: Object, default: null },
  selectedText: { type: String, default: '' }
})

// ===== Emits 定义 =====
const emit = defineEmits(['back', 'chat-updated', 'insert-text', 'get-editor-content'])

// ===== 响应式数据 =====
const messagesContainer = ref(null)
const inputRef = ref(null)
const modelSelector = ref(null)

// 聊天数据
const chatMessages = ref([])
const inputMessage = ref('')
const isGenerating = ref(false)
const loadingChat = ref(false)
const chatData = ref(null)
const chatMemoryEnabled = ref(true)
const selectedModel = ref('')
const loadingModels = ref(false)
const selectedRoles = ref([])
const isSettingsPanelOpen = ref(false)
const includeEditorContent = ref(false) // 是否包含编辑器内容

// 界面状态 - 为了兼容原有模板，但现在从 configStore 获取主题
const theme = computed(() => configStore.theme || 'light')
const mode = ref('chat')
const showSidebar = ref(false)
const messages = ref([]) // 兼容原有模板
const chatSessions = ref([]) // 会话列表
const currentSessionId = ref(null) // 当前会话ID
const loadingChatSessions = ref(false) // 加载会话列表状态
const searchQuery = ref('') // 搜索关键词
// 兼容原有模板 - isLoading 基于 isGenerating 状态
const isLoading = computed(() => isGenerating.value)
// 模型下拉框相关状态 - 兼容原有模板
const showModelDropdown = ref(false) // 模型下拉框
const modelDropdownStyle = ref({}) // 模型下拉框样式

// 角色下拉框相关状态
const showRoleDropdown = ref(false) // 角色下拉框
const roleDropdownStyle = ref({}) // 角色下拉框样式

// EntitySelector 相关状态
const showEntitySelector = ref(false)
const entitySelectorPosition = ref({ top: 0, left: 0 })
const selectedReferences = ref([]) // 存储选中的引用

// 当前模型 - 基于选中的模型计算
const currentModel = computed(() => {
  if (!selectedModel.value) {
    return { id: 'default', name: '默认模型' }
  }
  const model = availableModels.value.find(m => m.id === selectedModel.value)
  return model ? { id: model.id, name: model.name } : { id: 'default', name: '默认模型' }
})

// 模型列表 - 兼容原有模板
const models = computed(() => availableModels.value)

// 当前角色显示名称
const currentRoleDisplayName = computed(() => {
  if (!selectedRoles.value || selectedRoles.value.length === 0) {
    return '无角色'
  }
  if (selectedRoles.value.length === 1) {
    const role = availableRoles.value.find(r => r.id === selectedRoles.value[0])
    return role ? role.name : '未知角色'
  }
  return `${selectedRoles.value.length}个角色`
})

// 保存原始处理函数的引用
let originalReceiveChunk = null
let originalOnMessageComplete = null
let originalReceiveChatError = null

// ===== 计算属性 =====
const availableModels = computed(() => {
  // 使用 modelOptions 获取更好的显示效果
  const options = aiProvidersStore.modelOptions
  return options.map(option => ({
    id: option.uniqueId,  // 使用唯一标识符作为ID
    name: option.label,  // 使用 label 作为显示名称
    providerId: option.providerId,
    providerName: option.providerName,
    uniqueId: option.uniqueId,  // 添加uniqueId字段
    config: option.config  // 添加config字段
  }))
})

// 模型选项（用于下拉选择）
const modelOptions = computed(() => {
  return availableModels.value.map(model => ({
    value: model.id,
    label: model.name,
    description: model.providerName ? `提供商: ${model.providerName}` : undefined,
    provider: model.providerName
  }))
})

// 角色选项（用于下拉选择）
const roleOptions = computed(() => {
  const options = availableRoles.value.map(role => ({
    value: role.id,
    label: role.name || role.id,
    description: role.description
  }))
  console.log('ChatUI roleOptions:', options)
  return options
})

const availableRoles = computed(() => {
  return aiRolesStore.roles.filter(role => role.isEnabled !== false).map(role => ({
    id: role.id,
    name: role.name || role.id,
    description: role.description || '',
    prompt: role.prompt || ''
  }))
})

// 兼容原有模板的计算属性
const canSend = computed(() => {
  return inputMessage.value.trim().length > 0 && !isGenerating.value && chatData.value
})

// 清理文本内容，移除格式化标记
const cleanTextContent = (text) => {
  if (!text) return ''

  return text
    .replace(/<[^>]*>/g, '') // 移除HTML标签
    .replace(/```[\s\S]*?```/g, ' [代码块] ') // 替换代码块为占位符
    .replace(/`([^`]+)`/g, '$1') // 移除行内代码标记
    .replace(/\*\*([^*]+)\*\*/g, '$1') // 移除粗体标记
    .replace(/\*([^*]+)\*/g, '$1') // 移除斜体标记
    .replace(/#{1,6}\s+/g, '') // 移除标题标记
    .replace(/---+/g, '') // 移除分隔线
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 移除链接格式，保留文本
    .replace(/\s+/g, ' ') // 合并多个空格
    .toLowerCase()
    .trim()
}

// 检查文本是否匹配搜索关键词
const matchesSearchQuery = (text, keywords) => {
  if (!text || !keywords.length) return false

  const cleanText = cleanTextContent(text)

  // 支持多关键词搜索（AND逻辑）
  return keywords.every(keyword => cleanText.includes(keyword))
}

// 获取搜索匹配的内容片段
const getSearchMatchSnippet = (session) => {
  if (!searchQuery.value.trim()) return ''

  const keywords = searchQuery.value.toLowerCase().trim().split(/\s+/).filter(k => k.length > 0)

  // 首先检查标题匹配
  const title = session.title || ''
  if (matchesSearchQuery(title, keywords)) {
    return `<span class="match-type">标题:</span> ${highlightKeywords(title, keywords)}`
  }

  // 检查模型名称匹配
  const modelName = getModelDisplayName(session.model_id)
  if (matchesSearchQuery(modelName, keywords)) {
    return `<span class="match-type">模型:</span> ${highlightKeywords(modelName, keywords)}`
  }

  // 检查消息内容匹配
  if (session.messages && Array.isArray(session.messages)) {
    for (const message of session.messages) {
      if (!message.content) continue

      const content = message.displayContent || message.content
      if (matchesSearchQuery(content, keywords)) {
        const snippet = getContentSnippet(content, keywords, 60)
        const roleLabel = message.role === 'user' ? '用户' : 'AI'
        return `<span class="match-type">${roleLabel}:</span> ${highlightKeywords(snippet, keywords)}`
      }
    }
  }

  return ''
}

// 高亮关键词
const highlightKeywords = (text, keywords) => {
  if (!text || !keywords.length) return text

  let highlightedText = text
  keywords.forEach(keyword => {
    const regex = new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
    highlightedText = highlightedText.replace(regex, '<mark>$1</mark>')
  })

  return highlightedText
}

// 获取包含关键词的内容片段
const getContentSnippet = (text, keywords, maxLength = 100) => {
  if (!text || !keywords.length) return ''

  const cleanText = cleanTextContent(text)

  // 找到第一个关键词的位置
  let firstMatchIndex = -1
  let matchedKeyword = ''

  for (const keyword of keywords) {
    const index = cleanText.indexOf(keyword)
    if (index !== -1 && (firstMatchIndex === -1 || index < firstMatchIndex)) {
      firstMatchIndex = index
      matchedKeyword = keyword
    }
  }

  if (firstMatchIndex === -1) return text.substring(0, maxLength) + '...'

  // 计算片段的开始和结束位置
  const start = Math.max(0, firstMatchIndex - Math.floor(maxLength / 2))
  const end = Math.min(cleanText.length, start + maxLength)

  let snippet = cleanText.substring(start, end)

  // 添加省略号
  if (start > 0) snippet = '...' + snippet
  if (end < cleanText.length) snippet = snippet + '...'

  return snippet
}

// 过滤会话列表
const filteredSessions = computed(() => {
  if (!searchQuery.value.trim()) {
    return chatSessions.value
  }

  // 支持多关键词搜索，用空格分隔
  const keywords = searchQuery.value.toLowerCase().trim().split(/\s+/).filter(k => k.length > 0)

  return chatSessions.value.filter(session => {
    // 搜索会话标题
    const title = session.title || ''
    if (matchesSearchQuery(title, keywords)) {
      return true
    }

    // 搜索模型名称
    const modelName = getModelDisplayName(session.model_id)
    if (matchesSearchQuery(modelName, keywords)) {
      return true
    }

    // 搜索对话内容
    if (session.messages && Array.isArray(session.messages)) {
      return session.messages.some(message => {
        if (!message.content) return false

        // 搜索消息内容
        if (matchesSearchQuery(message.content, keywords)) {
          return true
        }

        // 如果有displayContent，也搜索它（用于@引用等特殊内容）
        if (message.displayContent && matchesSearchQuery(message.displayContent, keywords)) {
          return true
        }

        return false
      })
    }

    return false
  })
})



// ===== 方法定义 =====
const handleBack = () => {
  emit('back')
}

// 加载聊天数据
const loadChatData = async () => {
  if (!props.chatId) {
    // 如果没有传入 chatId，显示历史会话列表
    showSidebar.value = true
    return
  }

  try {
    loadingChat.value = true
    const response = await window.pywebview.api.model_controller.get_chat(props.chatId)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      chatData.value = result.data

      // 设置模型和消息
      const modelId = chatData.value.model_id || chatData.value.model || ''

      // 检查是否已经是新格式的唯一标识符
      const directMatch = availableModels.value.find(m => m.id === modelId)
      if (directMatch) {
        selectedModel.value = modelId
      } else {
        // 可能是旧格式，尝试转换
        const matchingModel = availableModels.value.find(m => m.uniqueId && m.uniqueId.endsWith(':' + modelId))
        if (matchingModel) {
          selectedModel.value = matchingModel.id
          // 更新聊天记录中的模型ID为新格式
          chatData.value.model_id = matchingModel.id
        } else {
          // 找不到匹配的模型，使用默认模型
          if (availableModels.value.length > 0) {
            selectedModel.value = availableModels.value[0].id
            chatData.value.model_id = availableModels.value[0].id
          }
        }
      }

      chatMessages.value = chatData.value.messages || []

      // 设置记忆模式
      chatMemoryEnabled.value = chatData.value.memory_enabled !== false

      // 设置角色
      selectedRoles.value = chatData.value.roles || []

      // 通知父组件更新
      emit('chat-updated', chatData.value)

      // 加载聊天数据时强制滚动到底部
      shouldAutoScroll.value = true
      nextTick(() => {
        scrollToBottom(true)
      })
    } else {
      ElMessage.error(result.message || '加载聊天数据失败')
    }
  } catch (error) {
    console.error('加载聊天数据失败:', error)
    ElMessage.error('加载聊天数据失败')
  } finally {
    loadingChat.value = false
  }
}



// 加载模型列表
const loadModels = async () => {
  try {
    loadingModels.value = true

    // 确保AI提供商配置已加载
    try {
      const { useAIProvidersStore } = await import('@/stores/aiProviders')
      const aiProvidersStore = useAIProvidersStore()

      if (!aiProvidersStore.initialized) {
        console.log('ChatUI: AI提供商配置未初始化，先加载提供商配置')
        await aiProvidersStore.loadProviders()
      }
    } catch (providerError) {
      console.warn('ChatUI: 加载AI提供商配置失败:', providerError)
    }

    // 模型列表现在从 AI 提供商配置中获取，无需单独加载
    console.log('ChatUI: 可用模型数量:', availableModels.value.length)

    // 如果没有选择模型，默认选择第一个
    if (!selectedModel.value && availableModels.value.length) {
      const firstModel = availableModels.value[0]
      selectedModel.value = firstModel.id
    }
  } catch (error) {
    console.error('ChatUI: 加载模型列表失败:', error)
  } finally {
    loadingModels.value = false
  }
}

// 保存聊天数据
const saveChat = async () => {
  if (!chatData.value) return

  try {
    const chatId = chatData.value.chat_id || chatData.value.id
    const response = await window.pywebview.api.model_controller.save_chat(
      chatId,
      {
        ...chatData.value,
        messages: chatMessages.value,
        model_id: selectedModel.value,
        memory_enabled: chatMemoryEnabled.value,
        roles: selectedRoles.value,
        last_updated: Math.floor(Date.now() / 1000)
      }
    )

    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status !== 'success') {
      console.error('保存聊天数据失败:', result.message)
    }

    // 通知父组件更新
    emit('chat-updated', chatData.value)
  } catch (error) {
    console.error('保存聊天数据失败:', error)
  }
}

// ===== 事件处理 =====
const switchModel = (model) => {
  selectedModel.value = model.id
}

// 切换编辑器内容包含状态
const toggleEditorContent = () => {
  includeEditorContent.value = !includeEditorContent.value
  console.log('ChatUI: 编辑器内容包含状态:', includeEditorContent.value)

  // 显示状态提示
  if (includeEditorContent.value) {
    const hasReferences = selectedReferences.value.length > 0
    if (hasReferences) {
      ElMessage.success('已开启编辑器内容包含。注意：编辑器内容中的@符号将被转义，不会与您的@引用功能冲突')
    } else {
      ElMessage.success('已开启编辑器内容包含，AI将能感知到您的代码上下文')
    }
  } else {
    ElMessage.info('已关闭编辑器内容包含')
  }
}

// 获取编辑器内容
const getEditorContent = async () => {
  try {
    // 通过 emit 事件请求父组件提供编辑器内容
    return new Promise((resolve) => {
      // 发送获取编辑器内容的事件
      emit('get-editor-content', (content) => {
        console.log('ChatUI: 获取到编辑器内容长度:', content?.length || 0)
        resolve(content)
      })

      // 设置超时，如果父组件没有响应，返回 null
      setTimeout(() => {
        console.warn('ChatUI: 获取编辑器内容超时')
        resolve(null)
      }, 1000)
    })
  } catch (error) {
    console.error('ChatUI: 获取编辑器内容失败:', error)
    return null
  }
}





// 发送消息
const sendMessage = async () => {

  if (!inputMessage.value.trim() || isGenerating.value) {
    console.log('ChatUI: 发送被阻止 - 输入为空或正在生成')
    return
  }

  try {
    // 如果是新对话，创建一个聊天数据对象
    if (!chatData.value) {
      const chatId = props.chatId || `chat_${Date.now()}`
      chatData.value = {
        id: chatId,
        chat_id: chatId,  // 添加 chat_id 字段
        title: `新对话 ${new Date().toLocaleTimeString()}`,
        model_id: selectedModel.value,
        messages: [],
        roles: selectedRoles.value,
        memory_enabled: chatMemoryEnabled.value,
        created_at: Math.floor(Date.now() / 1000),
        last_updated: Math.floor(Date.now() / 1000)
      }
    }

    // 构建用户消息内容 - 将气泡转换为实际信息
    let messageContent = await convertBubblesToRealContent(inputMessage.value)

    // 处理引用信息
    // if (selectedReferences.value.length > 0) {
    //   let referenceContext = '\n\n---\n**引用内容：**\n'

    //   for (const reference of selectedReferences.value) {
    //     referenceContext += await buildReferenceContext(reference)
    //   }

    //   messageContent = messageContent + referenceContext
    // }

    // 如果启用了编辑器内容包含，添加编辑器内容
    if (includeEditorContent.value) {
      const editorContent = await getEditorContent()
      if (editorContent) {
        // 对编辑器内容进行安全处理，避免与@引用功能冲突
        // 将编辑器内容中的@符号转义，防止被误识别为引用
        const safeEditorContent = editorContent.replace(/@/g, '\\@')

        // 如果同时使用了@引用功能，添加说明
        const hasReferences = selectedReferences.value.length > 0
        const contextLabel = hasReferences
          ? '**当前编辑器内容（已转义@符号避免与引用冲突）：**'
          : '**当前编辑器内容：**'

        messageContent = `${messageContent}\n\n---\n${contextLabel}\n\`\`\`\n${safeEditorContent}\n\`\`\``
      }
    }

    // 添加用户消息 - 保存原始显示内容和转换后的内容
    const userMessage = {
      role: 'user',
      content: messageContent, // 发送给大模型的完整内容
      displayContent: inputMessage.value, // 用于显示的原始内容（包含@引用）
      references: selectedReferences.value.map(ref => ({ // 保存引用映射关系
        id: ref.id,
        type: ref.type,
        displayText: ref.displayText,
        data: ref.data,
        template: ref.template,
        entities: ref.entities,
        volume: ref.volume
      })),
      id: nanoid(),
      timestamp: Math.floor(Date.now() / 1000)
    }

    chatMessages.value.push(userMessage)
    console.log('ChatUI: 添加用户消息，当前消息数量:', chatMessages.value.length)
    console.log('ChatUI: 当前消息列表:', chatMessages.value)

    chatData.value.messages = chatMessages.value
    await saveChat()

    // 清空输入框和引用
    const userMessageContent = inputMessage.value
    inputMessage.value = ''
    selectedReferences.value = []

    // 清空富文本输入框
    if (inputRef.value) {
      inputRef.value.innerHTML = ''
    }

    // 发送消息时强制滚动到底部
    shouldAutoScroll.value = true
    scrollToBottom(true)

    // 开始生成回复
    isGenerating.value = true

    // 准备API消息列表
    const apiMessages = []

    // 如果有系统角色设定，添加系统消息
    if (selectedRoles.value.length > 0) {
      const rolePrompts = selectedRoles.value.map(roleId => {
          const role = availableRoles.value.find(r => r.id === roleId)
        return role?.prompt || ''
      }).filter(Boolean)

      if (rolePrompts.length > 0) {
        apiMessages.push({
          role: 'system',
          content: rolePrompts.join('\n\n')
        })
      }
    }

    // 根据记忆模式决定发送的消息
    if (chatMemoryEnabled.value) {
      // 记忆模式：发送所有历史消息
      chatMessages.value.forEach(msg => {
        if (msg.role !== 'system' && !msg.isSystemNotification) {
          apiMessages.push({
        role: msg.role,
        content: msg.content
          })
        }
      })
    } else {
      // 单次模式：只发送当前用户消息
      apiMessages.push({
        role: 'user',
        content: userMessageContent
      })
    }

    // 获取模型配置
    const modelConfig = getModelConfig(selectedModel.value)

    // 合并配置：模型配置优先，只有stream强制为true
    const finalConfig = {
      stream: true,  // 强制启用流式输出
      ...modelConfig  // 模型配置（包括temperature, top_p, max_tokens等）
    }

    // 调用API
    try {
      const chatId = chatData.value.chat_id || chatData.value.id
      console.log('ChatUI: 准备调用 API')
      console.log('ChatUI: chatId:', chatId)
      console.log('ChatUI: selectedModel:', selectedModel.value)
      console.log('ChatUI: apiMessages:', apiMessages)
      console.log('ChatUI: finalConfig:', finalConfig)

      const response = await window.pywebview.api.model_controller.chat(
        chatId,
        selectedModel.value,
        apiMessages,
        finalConfig
      )

      console.log('ChatUI: API 响应:', response)

      // 如果返回非流式响应
      if (response && typeof response === 'object') {
        const result = typeof response === 'string' ? JSON.parse(response) : response

        if (result.status !== 'success') {
          throw new Error(result.message || '生成回复失败')
        }

        if (!result.data.stream && result.data.content) {
          // 处理非流式响应
          const assistantMessage = {
            role: 'assistant',
            content: result.data.content,
            id: nanoid(),
            timestamp: Math.floor(Date.now() / 1000)
          }

          chatMessages.value.push(assistantMessage)
          chatData.value.messages = chatMessages.value
          await saveChat()
        }
      }
    } catch (error) {
      console.error('生成回复失败:', error)

      // 添加错误消息
      chatMessages.value.push({
        role: 'assistant',
        content: `生成回复时出错: ${error.message || '未知错误'}`,
        id: nanoid(),
        timestamp: Math.floor(Date.now() / 1000),
        isError: true
      })

      chatData.value.messages = chatMessages.value
      await saveChat()

      ElMessage.error('生成回复失败')
    } finally {
      isGenerating.value = false
      smartScroll()
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')
    isGenerating.value = false
  }
}

// 智能滚动系统
const isUserNearBottom = () => {
  if (!messagesContainer.value) return false
  const container = messagesContainer.value
  const threshold = 100 // 距离底部100px内认为是在底部附近
  return container.scrollHeight - container.scrollTop - container.clientHeight <= threshold
}

const shouldAutoScroll = ref(true) // 是否应该自动滚动

// 滚动到底部
const scrollToBottom = (force = false) => {
  nextTick(() => {
    if (messagesContainer.value && (force || shouldAutoScroll.value)) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 智能滚动 - 只有在用户接近底部时才自动滚动
const smartScroll = () => {
  if (isUserNearBottom()) {
    shouldAutoScroll.value = true
    scrollToBottom()
  } else {
    shouldAutoScroll.value = false
  }
}

// 监听用户滚动行为
const handleScroll = () => {
  if (!messagesContainer.value) return

  // 检查用户是否滚动到底部附近
  if (isUserNearBottom()) {
    shouldAutoScroll.value = true
  } else {
    shouldAutoScroll.value = false
  }
}

// 切换记忆模式
const toggleMemoryMode = async () => {
  chatMemoryEnabled.value = !chatMemoryEnabled.value

  if (chatData.value) {
    chatData.value.memory_enabled = chatMemoryEnabled.value
    await saveChat()

    ElMessage.info(`已切换到${chatMemoryEnabled.value ? '记忆' : '单次'}模式`)
  }
}

// 获取角色名称
const getRoleName = (roleId) => {
  const role = availableRoles.value.find(r => r.id === roleId)
  return role?.name || roleId
}



// 处理流式响应
const handleReceiveChunk = (chunk) => {
  try {
    // 解码Base64字符串
    const decodedData = atob(chunk)
    const jsonString = new TextDecoder('utf-8').decode(
      new Uint8Array([...decodedData].map(c => c.charCodeAt(0)))
    )

    const messageData = JSON.parse(jsonString)
    const { chat_id, content, reasoning } = messageData

    console.log('ChatUI: 收到流式数据块')
    console.log('ChatUI: messageData:', messageData)
    console.log('ChatUI: chat_id:', chat_id)
    console.log('ChatUI: props.chatId:', props.chatId)
    console.log('ChatUI: content:', content)

    // 使用当前聊天数据的ID进行匹配，如果没有则使用props.chatId
    const currentChatId = chatData.value?.id || props.chatId
    console.log('ChatUI: currentChatId:', currentChatId)

    if (chat_id === currentChatId) {
      // 一旦收到任何内容（推理或正文），立即关闭"AI 正在思考..."指示器
      if ((content && content.trim()) || (reasoning && reasoning.trim())) {
        isGenerating.value = false
      }

      // 检查是否已有AI回复
      const lastMessage = chatMessages.value[chatMessages.value.length - 1]

      if (!lastMessage || lastMessage.role !== 'assistant') {
        // 创建新的AI回复
        const newMessage = {
          role: 'assistant',
          content: content || '',
          id: nanoid(),
          timestamp: Math.floor(Date.now() / 1000)
        }

        // 添加思考过程
        if (reasoning) {
          newMessage.reasoning = reasoning
          newMessage.reasoningCollapsed = true
          newMessage.reasoningTime = 0 // 初始化为0
          newMessage.reasoningStartTime = Date.now()
        }

        chatMessages.value.push(newMessage)
        console.log('ChatUI: 添加新的AI消息，当前消息数量:', chatMessages.value.length)
      } else {
        // 更新已有AI回复
        if (content) {
          lastMessage.content += content
        }

        // 更新思考过程
        if (reasoning) {
          if (!lastMessage.reasoning) {
            lastMessage.reasoning = ''
            lastMessage.reasoningCollapsed = true
            lastMessage.reasoningTime = 0 // 初始化为0
            lastMessage.reasoningStartTime = Date.now()
          }

          lastMessage.reasoning += reasoning
        }
      }

      // 智能滚动 - 只有在用户接近底部时才自动滚动
      smartScroll()
    }
    } catch (error) {
    console.error('处理消息块失败:', error)
  }
}

// 处理消息完成
const handleMessageComplete = (chat_id) => {
  console.log('ChatUI: 消息完成')
  console.log('ChatUI: chat_id:', chat_id)
  console.log('ChatUI: props.chatId:', props.chatId)

  // 使用当前聊天数据的ID进行匹配，如果没有则使用props.chatId
  const currentChatId = chatData.value?.id || props.chatId
  console.log('ChatUI: currentChatId:', currentChatId)

  if (chat_id === currentChatId) {
    // 确保生成状态已关闭（可能在 handleReceiveChunk 中已经关闭了）
    isGenerating.value = false

    // 更新思考时间
    const lastMessage = chatMessages.value[chatMessages.value.length - 1]
    if (lastMessage && lastMessage.role === 'assistant' && lastMessage.reasoning && lastMessage.reasoningStartTime) {
      const duration = Date.now() - lastMessage.reasoningStartTime
      lastMessage.reasoningTime = duration / 1000 // 存储为秒数（数字）
      delete lastMessage.reasoningStartTime
    }

    // 保存聊天记录
    chatData.value.messages = chatMessages.value
    saveChat()

    // 智能滚动到底部
    smartScroll()
  }
}

// 处理错误消息
const handleChatError = (chunk) => {
  try {
    // 解码Base64字符串
    const decodedData = atob(chunk)
    const jsonString = new TextDecoder('utf-8').decode(
      new Uint8Array([...decodedData].map(c => c.charCodeAt(0)))
    )

    const errorData = JSON.parse(jsonString)
    const { chat_id, error_message } = errorData

    console.log('ChatUI: 收到错误消息:', chat_id, error_message);

    // 始终显示错误消息，无论是否是当前聊天
    ElMessage.error(`AI回复失败: ${error_message}`)

    // 使用当前聊天数据的ID进行匹配，如果没有则使用props.chatId
    const currentChatId = chatData.value?.id || props.chatId

    if (chat_id === currentChatId) {
      isGenerating.value = false

      // 添加错误消息
      chatMessages.value.push({
        role: 'assistant',
        content: `[错误: ${error_message}]`,
        id: nanoid(),
        timestamp: Math.floor(Date.now() / 1000),
        isError: true
      })

      // 保存聊天记录
    chatData.value.messages = chatMessages.value
      saveChat()

      // 智能滚动到底部
      smartScroll()
    }
  } catch (error) {
    console.error('处理错误消息失败:', error)
  }
}

// 复制消息
const copyMessage = async (content) => {
  try {
    await window.pywebview.api.copy_to_clipboard(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 处理 MarkdownBubble 的复制事件
const handleCopyMessage = async (content) => {
  await copyMessage(content)
}

// 重发消息
const resendMessage = async (message) => {
  if (isGenerating.value) return

  // 找到消息索引
  const index = chatMessages.value.findIndex(m => m === message)
  if (index === -1) return

  // 删除该消息之后的所有消息
  chatMessages.value = chatMessages.value.slice(0, index + 1)

  // 设置输入框内容为该消息
  inputMessage.value = message.content

  // 保存更新后的消息列表
      chatData.value.messages = chatMessages.value
      await saveChat()

  // 发送消息
  await sendMessage()
}

// 处理 MarkdownBubble 的重新生成事件
const handleRegenerateMessage = async (message) => {
  await resendMessage(message)
}

// 解析推理时间字符串为数字（秒）
const parseReasoningTime = (reasoningTime) => {
  if (!reasoningTime) return 0
  if (typeof reasoningTime === 'number') return reasoningTime
  if (typeof reasoningTime === 'string') {
    // 解析 "2.5秒" 格式的字符串
    const match = reasoningTime.match(/(\d+\.?\d*)/)
    return match ? parseFloat(match[1]) : 0
  }
  return 0
}

// 插入到编辑器
const insertToEditor = (content) => {
  emit('insert-text', content)
  ElMessage.success('内容已插入到编辑器')
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''

  const date = new Date(timestamp * 1000) // 转换为毫秒
  const now = new Date()
  const diff = now - date

  // 小于1分钟
  if (diff < 60000) {
    return '刚刚'
  }

  // 小于1小时
  if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  }

  // 小于1天
  if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  }

  // 小于7天
  if (diff < 604800000) {
    return `${Math.floor(diff / 86400000)}天前`
  }

  // 超过7天，显示具体日期
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取模型显示名称
const getModelDisplayName = (modelId) => {
  if (!modelId) return '未知模型'

  const model = availableModels.value.find(m => m.id === modelId || m.uniqueId === modelId)
  if (model) {
    return model.name
  }

  // 如果找不到完整匹配，尝试从 uniqueId 中提取模型名称
  if (modelId.includes(':')) {
    const parts = modelId.split(':')
    return parts[parts.length - 1] || modelId
  }

  return modelId
}

// 使用选中文本
const useSelectedText = () => {
  if (props.selectedText) {
    inputMessage.value = props.selectedText
    ElMessage.success('已使用选中文本')
  }
}

// 切换设置面板显示/隐藏
const toggleSettingsPanel = () => {
  isSettingsPanelOpen.value = !isSettingsPanelOpen.value
}

// 选择器事件处理
const onModelChange = async (value, option) => {
  console.log('Model changed:', value, option)
  // 如果聊天数据存在，更新模型并保存
  if (chatData.value) {
    chatData.value.model_id = value
    await saveChat()
  }
}

const onRoleChange = async (values, option) => {
  console.log('Roles changed:', values, option)
  // 更新选中的角色
  selectedRoles.value = values
  // 如果聊天数据存在，更新角色并保存
  if (chatData.value) {
    chatData.value.roles = values
    await saveChat()

    // 显示角色变更提示
    if (values.length > 0) {
      const roleNames = values.map(roleId => {
        const role = availableRoles.value.find(r => r.id === roleId)
        return role ? role.name : roleId
      }).join(', ')
      ElMessage.success(`已选择角色: ${roleNames}`)
    } else {
      ElMessage.info('已清除所有角色设定')
    }
  }
}

// 停止生成
const handleStopGenerating = async () => {
  const currentChatId = chatData.value?.id || props.chatId
  if (!currentChatId || !isGenerating.value) return;

  try {
    const response = await window.pywebview.api.model_controller.stop_chat(currentChatId);
    const result = typeof response === 'string' ? JSON.parse(response) : response;

    if (result.status === 'success') {
      isGenerating.value = false;
      ElMessage.success('已停止生成');
    } else {
      throw new Error(result.message || '停止生成失败');
    }
  } catch (error) {
    console.error('停止对话失败:', error);
    ElMessage.error(error.message || '停止对话失败');
    isGenerating.value = false;
  }
};

// 支持原有界面的方法
const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
}

const newChat = () => {
  // 新建对话功能
  console.log('newChat called')
  createNewSession()
}

// 停止生成
const stopGeneration = () => {
  handleStopGenerating()
}

// 角色下拉框相关方法
const roleSelector = ref(null)

const toggleRoleDropdown = () => {
  if (showRoleDropdown.value) {
    closeRoleDropdown()
  } else {
    openRoleDropdown()
  }
}

const openRoleDropdown = () => {
  if (!roleSelector.value) return

  // 计算下拉框位置
  const rect = roleSelector.value.getBoundingClientRect()
  const dropdownWidth = 220
  let dropdownHeight = Math.min(availableRoles.value.length * 40 + 40, 280) // 根据选项数量计算实际高度，包含底部按钮区域
  const margin = 8

  // 计算是否有足够空间在上方显示
  const spaceAbove = rect.top
  const spaceBelow = window.innerHeight - rect.bottom
  const showAbove = spaceBelow < dropdownHeight && spaceAbove > dropdownHeight

  // 计算水平位置
  let left = rect.left
  const spaceRight = window.innerWidth - rect.left
  if (spaceRight < dropdownWidth) {
    left = rect.right - dropdownWidth
  }

  // 设置下拉框样式
  roleDropdownStyle.value = {
    position: 'fixed',
    top: showAbove ? `${rect.top - dropdownHeight - margin}px` : `${rect.bottom + margin}px`,
    left: `${Math.max(margin, left)}px`,
    width: `${dropdownWidth}px`,
    maxHeight: `${dropdownHeight}px`,
    zIndex: 9999
  }

  showRoleDropdown.value = true
}

const closeRoleDropdown = () => {
  showRoleDropdown.value = false
  roleDropdownStyle.value = {}
}

const selectRole = (role) => {
  // 切换角色选择状态
  const index = selectedRoles.value.indexOf(role.id)
  if (index > -1) {
    // 如果已选中，则取消选择
    selectedRoles.value.splice(index, 1)
  } else {
    // 如果未选中，则添加选择
    selectedRoles.value.push(role.id)
  }

  // 保存聊天数据
  if (chatData.value) {
    chatData.value.roles = selectedRoles.value
    saveChat()
  }

  // 显示提示
  if (selectedRoles.value.length > 0) {
    const roleNames = selectedRoles.value.map(roleId => {
      const role = availableRoles.value.find(r => r.id === roleId)
      return role ? role.name : roleId
    }).join(', ')
    ElMessage.success(`已选择角色: ${roleNames}`)
  } else {
    ElMessage.info('已清除所有角色设定')
  }
}

const clearAllRoles = () => {
  selectedRoles.value = []

  // 保存聊天数据
  if (chatData.value) {
    chatData.value.roles = []
    saveChat()
  }

  ElMessage.info('已清除所有角色设定')
  closeRoleDropdown()
}

// 会话管理方法
// 加载对话历史列表
const loadChatSessions = async () => {
  try {
    loadingChatSessions.value = true
    const response = await window.pywebview.api.model_controller.get_all_chats()
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result && result.status === 'success' && Array.isArray(result.data)) {
      chatSessions.value = result.data.sort((a, b) => {
        return (b.last_updated || 0) - (a.last_updated || 0)
      })
      console.log('ChatUI: 加载到', chatSessions.value.length, '个会话')
    } else {
      throw new Error(result?.message || '加载聊天列表失败')
    }
  } catch (error) {
    console.error('ChatUI: 加载聊天列表失败:', error)
    ElMessage.error('加载聊天列表失败')
  } finally {
    loadingChatSessions.value = false
  }
}

// 刷新对话历史列表
const refreshChatSessions = async () => {
  console.log('ChatUI: 刷新会话列表')
  try {
    await loadChatSessions()
  } catch (error) {
    console.error('ChatUI: 刷新会话列表失败:', error)
    ElMessage.error('刷新失败，请重试')
  }
}

// 切换到指定会话
const switchToSession = async (sessionId) => {
  console.log('ChatUI: switchToSession called:', sessionId)

  if (sessionId === currentSessionId.value) {
    // 如果是当前会话，只关闭侧边栏
    showSidebar.value = false
    return
  }

  try {
    // 保存当前会话（如果有的话）
    if (chatData.value && chatMessages.value.length > 0) {
      await saveChat()
    }

    // 切换到新会话
    currentSessionId.value = sessionId

    // 重新加载聊天数据
    const response = await window.pywebview.api.model_controller.get_chat(sessionId)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      chatData.value = result.data
      chatMessages.value = result.data.messages || []

      // 设置模型
      const modelId = result.data.model_id || result.data.model || ''
      const directMatch = availableModels.value.find(m => m.id === modelId)
      if (directMatch) {
        selectedModel.value = modelId
      } else {
        const matchingModel = availableModels.value.find(m => m.uniqueId && m.uniqueId.endsWith(':' + modelId))
        if (matchingModel) {
          selectedModel.value = matchingModel.id
        } else if (availableModels.value.length > 0) {
          selectedModel.value = availableModels.value[0].id
        }
      }

      // 设置其他配置
      chatMemoryEnabled.value = result.data.memory_enabled !== false
      selectedRoles.value = result.data.roles || []

      // 关闭侧边栏
      showSidebar.value = false

      // 滚动到底部
      nextTick(() => {
        scrollToBottom(true)
      })

      console.log('ChatUI: 成功切换到会话', sessionId)
    } else {
      throw new Error(result.message || '加载会话失败')
    }
  } catch (error) {
    console.error('ChatUI: 切换会话失败:', error)
    ElMessage.error('切换会话失败')
  }
}

// 删除会话
const deleteSession = async (sessionId) => {
  console.log('ChatUI: deleteSession called:', sessionId)

  // 找到要删除的会话信息
  const session = chatSessions.value.find(s => (s.id || s.chat_id) === sessionId)
  const sessionTitle = session?.title || '未命名会话'

  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      `确定要删除会话"${sessionTitle}"吗？\n\n此操作不可撤销，会话中的所有消息都将被永久删除。`,
      '删除会话',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
        cancelButtonClass: 'el-button--default',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '删除中...'
            setTimeout(() => {
              done()
            }, 300)
          } else {
            done()
          }
        }
      }
    )

    // 用户确认后执行删除
    const response = await window.pywebview.api.model_controller.delete_chat(sessionId)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      // 从列表中移除
      chatSessions.value = chatSessions.value.filter(session =>
        (session.id || session.chat_id) !== sessionId
      )

      // 如果删除的是当前会话，智能处理后续状态
      if (sessionId === currentSessionId.value) {
        // 清除当前会话状态
        chatData.value = null
        chatMessages.value = []
        currentSessionId.value = null

        // 如果还有其他会话，切换到最新的会话
        if (chatSessions.value.length > 0) {
          const latestSession = chatSessions.value[0] // 会话列表已按时间排序
          const latestSessionId = latestSession.id || latestSession.chat_id
          console.log('ChatUI: 切换到最新会话:', latestSessionId)
          await switchToSession(latestSessionId)
        } else {
          // 没有其他会话了，显示历史列表（空状态）
          console.log('ChatUI: 没有其他会话，显示空状态')
          showSidebar.value = true
        }
      } else {
        // 删除的不是当前会话，如果当前没有选中会话且有其他会话，显示历史列表
        if (!currentSessionId.value && chatSessions.value.length > 0) {
          showSidebar.value = true
        }
      }

      ElMessage.success('会话删除成功')
    } else {
      throw new Error(result.message || '删除会话失败')
    }
  } catch (error) {
    // 如果是用户取消操作，不显示错误消息
    if (error === 'cancel') {
      console.log('ChatUI: 用户取消删除操作')
      return
    }

    console.error('ChatUI: 删除会话失败:', error)
    ElMessage.error('删除会话失败')
  }
}

// 创建新会话
const createNewSession = async () => {
  try {
    // 生成唯一的聊天ID
    const timestamp = Date.now()
    const chatId = `chat_${timestamp}`

    // 生成递增的对话标题
    let maxIndex = 0
    const titleRegex = /^新对话(\d+)$/

    chatSessions.value.forEach(session => {
      const title = session.title || ''
      const match = title.match(titleRegex)
      if (match && match[1]) {
        const index = parseInt(match[1])
        maxIndex = Math.max(maxIndex, index)
      }
    })

    const newTitle = `新对话${maxIndex + 1}`

    // 创建新会话数据
    const newChatData = {
      id: chatId,
      chat_id: chatId,
      title: newTitle,
      model_id: selectedModel.value || (availableModels.value.length > 0 ? availableModels.value[0].id : ''),
      messages: [],
      roles: selectedRoles.value,
      memory_enabled: chatMemoryEnabled.value,
      created_at: Math.floor(Date.now() / 1000),
      last_updated: Math.floor(Date.now() / 1000)
    }

    // 重置当前聊天状态
    chatData.value = newChatData
    chatMessages.value = []
    currentSessionId.value = chatId

    // 保存到后端
    try {
      const response = await window.pywebview.api.model_controller.save_chat(
        chatId,
        newChatData
      )

      const result = typeof response === 'string' ? JSON.parse(response) : response

      if (result.status === 'success') {
        console.log('ChatUI: 新会话保存成功:', result)
      } else {
        throw new Error(result.message || '保存新会话失败')
      }
    } catch (saveError) {
      console.error('ChatUI: 保存新会话到后端失败:', saveError)
      ElMessage.error('保存新会话失败，但会话已在本地创建')
    }

    // 添加到会话列表
    chatSessions.value.unshift(newChatData)

    // 关闭侧边栏
    showSidebar.value = false

    console.log('ChatUI: 创建新会话', chatId)
  } catch (error) {
    console.error('ChatUI: 创建新会话失败:', error)
    ElMessage.error('创建新会话失败')
  }
}

// 模型下拉框相关方法
const toggleModelDropdown = () => {
  if (showModelDropdown.value) {
    closeModelDropdown()
  } else {
    openModelDropdown()
  }
}

const openModelDropdown = () => {
  if (!modelSelector.value) return

  // 计算下拉框位置
  const rect = modelSelector.value.getBoundingClientRect()
  const dropdownWidth = 200
  let dropdownHeight = Math.min(models.value.length * 40 + 8, 280) // 根据选项数量计算实际高度
  const margin = 8

  // 计算是否有足够空间在上方显示
  const spaceAbove = rect.top
  const spaceBelow = window.innerHeight - rect.bottom

  let top, left

  // 优先在上方显示，确保不覆盖按钮
  if (spaceAbove >= dropdownHeight + margin) {
    // 在上方显示，确保与按钮有间距
    top = rect.top - dropdownHeight - margin
  } else if (spaceBelow >= dropdownHeight + margin) {
    // 在下方显示
    top = rect.bottom + margin
  } else {
    // 空间都不够，选择空间更大的一侧，并调整高度
    if (spaceAbove > spaceBelow) {
      top = margin
      dropdownHeight = spaceAbove - margin * 2
    } else {
      top = rect.bottom + margin
      dropdownHeight = spaceBelow - margin * 2
    }
  }

  // 水平居中对齐
  left = rect.left + rect.width / 2 - dropdownWidth / 2

  // 确保不超出屏幕边界
  left = Math.max(margin, Math.min(left, window.innerWidth - dropdownWidth - margin))

  modelDropdownStyle.value = {
    position: 'fixed',
    top: `${top}px`,
    left: `${left}px`,
    width: `${dropdownWidth}px`,
    maxHeight: `${dropdownHeight}px`,
    zIndex: 9999
  }

  showModelDropdown.value = true

  // 在下一个 tick 中滚动到选中的模型
  nextTick(() => {
    scrollToSelectedModel()
  })
}

// 滚动到选中的模型
const scrollToSelectedModel = () => {
  if (!showModelDropdown.value) return

  // 查找下拉框容器
  const dropdownOverlay = document.querySelector('.model-dropdown-overlay')
  if (!dropdownOverlay) return

  const dropdown = dropdownOverlay.querySelector('.model-dropdown')
  if (!dropdown) return

  // 查找当前选中的模型选项
  const activeOption = dropdown.querySelector('.model-option.active')
  if (!activeOption) return

  // 计算滚动位置
  const dropdownRect = dropdown.getBoundingClientRect()
  const optionRect = activeOption.getBoundingClientRect()

  // 计算选项在下拉框中的相对位置
  const optionTop = activeOption.offsetTop
  const optionHeight = activeOption.offsetHeight
  const dropdownScrollTop = dropdown.scrollTop
  const dropdownHeight = dropdown.clientHeight

  // 检查选项是否在可视区域内
  const isVisible = (
    optionTop >= dropdownScrollTop &&
    optionTop + optionHeight <= dropdownScrollTop + dropdownHeight
  )

  // 如果不在可视区域内，滚动到合适位置
  if (!isVisible) {
    // 将选中的选项滚动到下拉框的中央
    const targetScrollTop = optionTop - (dropdownHeight / 2) + (optionHeight / 2)
    dropdown.scrollTop = Math.max(0, targetScrollTop)
  }
}

const closeModelDropdown = () => {
  showModelDropdown.value = false
  modelDropdownStyle.value = {}
}

const selectModel = (model) => {
  selectedModel.value = model.id
  closeModelDropdown()

  // 如果聊天数据存在，立即保存
  if (chatData.value) {
    chatData.value.model_id = model.id
    saveChat()
  }
}

// ===== EntitySelector 相关方法 =====
// 打开实体选择器
const openEntitySelector = () => {
  console.log('ChatUI: openEntitySelector called, bookId:', props.bookId)

  if (!props.bookId) {
    console.warn('ChatUI: bookId为空，无法打开实体选择器')
    ElMessage.warning('请先选择一个书籍项目')
    return
  }

  // 计算选择器位置（在引用按钮附近）
  const referenceBtn = document.querySelector('.reference-btn')
  if (referenceBtn) {
    const rect = referenceBtn.getBoundingClientRect()
    entitySelectorPosition.value = {
      top: rect.top - 420, // 在按钮上方显示
      left: rect.left - 100
    }
  } else {
    // 默认位置
    entitySelectorPosition.value = {
      top: window.innerHeight / 2 - 200,
      left: window.innerWidth / 2 - 160
    }
  }

  showEntitySelector.value = true
}

// 关闭实体选择器
const closeEntitySelector = () => {
  showEntitySelector.value = false
}

// 处理实体选择
const handleEntitySelect = (selection) => {
  console.log('ChatUI: 选择了实体:', selection)

  if (selection.type === 'multiple') {
    // 处理多选结果
    const items = Array.from(selection.items) // 确保是真正的数组
    console.log('ChatUI: 多选项目数量:', items.length)
    items.forEach((item, index) => {
      console.log(`ChatUI: 处理第${index + 1}个项目:`, item)
      insertReference(item)
    })
  } else {
    // 处理单选结果
    insertReference(selection)
  }

  closeEntitySelector()
}

// 插入引用到输入框
const insertReference = (item) => {

  const inputElement = inputRef.value
  if (!inputElement) {
    console.error('ChatUI: inputRef.value 为空')
    return
  }

  // 创建引用对象
  const reference = createReferenceObject(item)

  // 将引用添加到选中引用列表
  selectedReferences.value.push(reference)

  // 插入引用气泡到输入框
  insertReferenceBubble(reference)
}

// 创建引用对象
const createReferenceObject = (item) => {
  const id = nanoid()

  // 处理多选数据结构（来自toggleItemSelection）
  if (item.data && item.extraData !== undefined) {
    switch (item.type) {
      case 'template':
        return {
          id,
          type: 'template',
          displayText: `@${item.data.name}`,
          data: item.data,
          entities: item.extraData // 模板的实体列表
        }
      case 'entity':
        return {
          id,
          type: 'entity',
          displayText: `@${item.data.name}`,
          data: item.data,
          template: item.extraData // 实体的模板信息
        }
      case 'chapter':
        console.log(toRaw(item))
        return {
          id,
          type: 'chapter',
          displayText: `@${item.data.title}`,
          data: item.data,
          volume: item.extraData // 章节的卷信息
        }
      case 'scenePool':
        return {
          id,
          type: 'scenePool',
          displayText: `@${item.data.name}`,
          data: item.data
        }
      default:
        return {
          id,
          type: 'unknown',
          displayText: '@未知引用',
          data: item.data
        }
    }
  }

  // 处理单选数据结构（来自selectEntity等方法）
  switch (item.type) {
    case 'template':
      return {
        id,
        type: 'template',
        displayText: `@${item.template.name}`,
        data: item.template,
        entities: item.entities
      }
    case 'entity':
      return {
        id,
        type: 'entity',
        displayText: `@${item.entity.name}`,
        data: item.entity,
        template: item.template
      }
    case 'chapter':
      return {
        id,
        type: 'chapter',
        displayText: `@${item.chapter.title}`,
        data: item.chapter,
        volume: item.volume
      }
    case 'scenePool':
      return {
        id,
        type: 'scenePool',
        displayText: `@${item.pool.name}`,
        data: item.pool
      }
    default:
      return {
        id,
        type: 'unknown',
        displayText: '@未知引用',
        data: item
      }
  }
}

// 插入引用气泡到富文本输入框
const insertReferenceBubble = (reference) => {
  console.log('ChatUI: insertReferenceBubble 被调用，reference:', reference)

  const inputElement = inputRef.value
  if (!inputElement) {
    console.error('ChatUI: insertReferenceBubble - inputElement 为空')
    return
  }

  console.log('ChatUI: inputElement:', inputElement)

  // 创建引用气泡元素
  const bubble = document.createElement('span')
  bubble.className = 'reference-bubble'
  bubble.contentEditable = 'false'
  bubble.dataset.referenceId = reference.id
  bubble.dataset.referenceType = reference.type
  bubble.textContent = reference.displayText

  // 添加内联样式确保样式生效 - 调整为更小的尺寸
  bubble.style.cssText = `
    display: inline-flex;
    align-items: center;
    gap: 3px;
    padding: 4px 8px;
    margin: 0 2px;
    color: white;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    cursor: default;
    user-select: none;
    position: relative;
    transition: all 0.2s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    vertical-align: middle;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12);
  `

  // 根据类型设置背景色
  switch (reference.type) {
    case 'template':
      bubble.style.background = 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)'
      break
    case 'entity':
      bubble.style.background = 'linear-gradient(135deg, #ec4899 0%, #f97316 100%)'
      break
    case 'chapter':
      bubble.style.background = 'linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%)'
      break
    case 'scenePool':
      bubble.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
      break
    default:
      bubble.style.background = 'var(--chat-accent)'
  }

  console.log('ChatUI: 创建的气泡元素:', bubble)

  // 添加删除按钮（悬停时显示）
  const deleteBtn = document.createElement('span')
  deleteBtn.className = 'reference-delete'
  deleteBtn.innerHTML = '&times;' // 使用HTML实体
  deleteBtn.title = '删除引用'

  // 添加删除按钮的内联样式 - 调整为更小的尺寸
  deleteBtn.style.cssText = `
    display: none;
    align-items: center;
    justify-content: center;
    width: 14px;
    height: 14px;
    margin-left: 3px;
    background: rgba(255, 255, 255, 0.25);
    border-radius: 50%;
    font-size: 10px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    line-height: 1;
  `

  deleteBtn.onclick = (e) => {
    e.stopPropagation()
    removeReference(reference.id)
  }

  // 添加悬停事件
  bubble.addEventListener('mouseenter', () => {
    deleteBtn.style.display = 'inline-flex'
  })

  bubble.addEventListener('mouseleave', () => {
    deleteBtn.style.display = 'none'
  })

  deleteBtn.addEventListener('mouseenter', () => {
    deleteBtn.style.background = 'rgba(255, 255, 255, 0.4)'
    deleteBtn.style.transform = 'scale(1.1)'
  })

  deleteBtn.addEventListener('mouseleave', () => {
    deleteBtn.style.background = 'rgba(255, 255, 255, 0.25)'
    deleteBtn.style.transform = 'scale(1)'
  })

  bubble.appendChild(deleteBtn)

  // 简化插入逻辑 - 直接添加到末尾
  console.log('ChatUI: 开始插入气泡到输入框')

  // 先确保输入框有焦点
  inputElement.focus()

  // 直接添加到末尾
  inputElement.appendChild(bubble)
  inputElement.appendChild(document.createTextNode(' '))

  console.log('ChatUI: 气泡已添加到输入框末尾')

  // 检查插入后的DOM结构
  console.log('ChatUI: 插入后的inputElement.innerHTML:', inputElement.innerHTML)
  console.log('ChatUI: 插入后的inputElement.childNodes.length:', inputElement.childNodes.length)

  // 设置光标到末尾
  const range = document.createRange()
  const selection = window.getSelection()
  range.selectNodeContents(inputElement)
  range.collapse(false) // 光标移到末尾
  selection.removeAllRanges()
  selection.addRange(range)

  // 触发输入事件
  inputElement.dispatchEvent(new Event('input', { bubbles: true }))

  // 调整输入框高度
  adjustInputHeight()

  console.log('ChatUI: 气泡插入完成')
}

// 移除引用
const removeReference = (referenceId) => {
  // 从选中引用列表中移除
  selectedReferences.value = selectedReferences.value.filter(ref => ref.id !== referenceId)

  // 从DOM中移除气泡
  const bubble = document.querySelector(`[data-reference-id="${referenceId}"]`)
  if (bubble) {
    bubble.remove()
  }

  // 触发输入事件
  const inputElement = inputRef.value
  if (inputElement) {
    inputElement.dispatchEvent(new Event('input', { bubbles: true }))
  }
}





// ===== 富文本输入处理 =====
// 键盘事件处理
const handleKeyDown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  } else if (event.key === 'Backspace') {
    // 处理删除键，确保正确删除引用气泡
    handleBackspaceInRichInput(event)
  } else if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
    // 处理左右箭头键，确保正确跳过引用气泡
    handleArrowKeysInRichInput(event)
  }
}

// 处理富文本输入
const handleRichInput = (event) => {
  console.log('ChatUI: handleRichInput 被调用')
  updateInputMessage()
  adjustInputHeight()
}

// 调整输入框高度 - 简化版本
const adjustInputHeight = () => {
  const inputElement = inputRef.value
  if (!inputElement) {
    console.log('ChatUI: adjustInputHeight - inputElement 为空')
    return
  }

  console.log('ChatUI: adjustInputHeight 被调用')

  // 重置高度以获取真实的scrollHeight
  inputElement.style.height = 'auto'

  // 获取内容的实际高度
  const scrollHeight = inputElement.scrollHeight
  console.log('ChatUI: scrollHeight:', scrollHeight)

  // 定义最小和最大高度（基于行数）
  const minHeight = 66  // 2行高度
  const maxHeight = 150 // 约6行高度

  // 计算目标高度
  let targetHeight = Math.max(scrollHeight, minHeight)

  if (targetHeight > maxHeight) {
    // 超过最大高度时，固定高度并显示滚动条
    targetHeight = maxHeight
    inputElement.style.overflowY = 'auto'
    console.log('ChatUI: 启用滚动条，高度:', targetHeight)
  } else {
    // 未超过最大高度时，隐藏滚动条
    inputElement.style.overflowY = 'hidden'
    console.log('ChatUI: 隐藏滚动条，高度:', targetHeight)
  }

  inputElement.style.height = targetHeight + 'px'
}

// 处理粘贴事件
const handlePaste = (event) => {
  event.preventDefault()

  // 获取粘贴的纯文本
  const text = event.clipboardData.getData('text/plain')

  // 插入纯文本
  const selection = window.getSelection()
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    range.deleteContents()
    range.insertNode(document.createTextNode(text))
    range.collapse(false)
    selection.removeAllRanges()
    selection.addRange(range)
  }

  updateInputMessage()
}

// 处理输入框焦点
const handleInputFocus = () => {
  // 输入框获得焦点时的处理
}

const handleInputBlur = () => {
  // 输入框失去焦点时的处理
}

// 处理退格键在富文本输入中的行为
const handleBackspaceInRichInput = (event) => {
  const selection = window.getSelection()
  if (selection.rangeCount === 0) return

  const range = selection.getRangeAt(0)

  // 如果光标在引用气泡前，删除整个气泡
  if (range.collapsed) {
    const prevNode = range.startContainer.previousSibling
    if (prevNode && prevNode.classList && prevNode.classList.contains('reference-bubble')) {
      event.preventDefault()
      const referenceId = prevNode.dataset.referenceId
      removeReference(referenceId)
      return
    }

    // 检查是否在引用气泡内部
    let currentNode = range.startContainer
    while (currentNode && currentNode !== inputRef.value) {
      if (currentNode.classList && currentNode.classList.contains('reference-bubble')) {
        event.preventDefault()
        const referenceId = currentNode.dataset.referenceId
        removeReference(referenceId)
        return
      }
      currentNode = currentNode.parentNode
    }
  }
}

// 处理箭头键在富文本输入中的行为
const handleArrowKeysInRichInput = (event) => {
  const selection = window.getSelection()
  if (selection.rangeCount === 0) return

  const range = selection.getRangeAt(0)

  if (event.key === 'ArrowLeft') {
    // 向左移动时，如果遇到引用气泡，跳过整个气泡
    const prevNode = range.startContainer.previousSibling
    if (prevNode && prevNode.classList && prevNode.classList.contains('reference-bubble')) {
      event.preventDefault()
      range.setStartBefore(prevNode)
      range.collapse(true)
      selection.removeAllRanges()
      selection.addRange(range)
    }
  } else if (event.key === 'ArrowRight') {
    // 向右移动时，如果遇到引用气泡，跳过整个气泡
    const nextNode = range.startContainer.nextSibling
    if (nextNode && nextNode.classList && nextNode.classList.contains('reference-bubble')) {
      event.preventDefault()
      range.setStartAfter(nextNode)
      range.collapse(true)
      selection.removeAllRanges()
      selection.addRange(range)
    }
  }
}

// 更新输入消息内容
const updateInputMessage = () => {
  const inputElement = inputRef.value
  if (!inputElement) return

  // 提取纯文本内容和引用信息
  let textContent = ''
  let references = []

  const processNode = (node) => {
    if (node.nodeType === Node.TEXT_NODE) {
      textContent += node.textContent
    } else if (node.classList && node.classList.contains('reference-bubble')) {
      const referenceId = node.dataset.referenceId
      const reference = selectedReferences.value.find(ref => ref.id === referenceId)
      if (reference) {
        textContent += reference.displayText
        references.push(reference)
      }
    } else {
      // 递归处理子节点
      for (let child of node.childNodes) {
        processNode(child)
      }
    }
  }

  processNode(inputElement)

  // 更新输入消息
  inputMessage.value = textContent

  // 更新引用列表（清理不存在的引用）
  selectedReferences.value = references
}

// ===== 双向映射系统 =====

// 引用映射标记 - 使用嵌套标记方案，更稳定可靠
const REFERENCE_MARKER_START = '<<<REF_START:'
const REFERENCE_MARKER_END = ':REF_END>>>'
const REFERENCE_CONTENT_START = '<<<REF_CONTENT_START>>>'
const REFERENCE_CONTENT_END = '<<<REF_CONTENT_END>>>'

// 将气泡转换为实际的实体信息内容（显示 → 大模型）
const convertBubblesToRealContent = async (content) => {
  if (!content || !selectedReferences.value.length) return content

  let convertedContent = content

  // 遍历所有引用，将@引用替换为实际信息
  for (const reference of selectedReferences.value) {
    const displayText = reference.displayText
    let realContent = displayText // 默认使用显示文本

    try {
      // 根据引用类型使用已有的完整数据
      switch (reference.type) {
        case 'entity':
          // 使用嵌套标记包装，更稳定可靠
          realContent = `${REFERENCE_MARKER_START}${reference.id}|entity|${reference.data.name}${REFERENCE_MARKER_END}\n`
          realContent += `${REFERENCE_CONTENT_START}\n`
          realContent += `实体信息：\n`
          realContent += `- 名称：${reference.data.name}\n`

          if (reference.data.description) {
            realContent += `- 描述：${reference.data.description}\n`
          }

          if (reference.template && reference.template.name) {
            realContent += `- 所属模板：${reference.template.name}\n`
          }

          // 添加实体的详细属性信息
          if (reference.data.dimensions && typeof reference.data.dimensions === 'object') {
            const dimensions = Object.entries(reference.data.dimensions)
              .filter(([, value]) => value && value !== '未设定' && value.trim())

            if (dimensions.length > 0) {
              realContent += `- 属性信息：\n`
              dimensions.forEach(([key, value]) => {
                realContent += `  - ${key}：${value}\n`
              })
            }
          }

          // 添加其他有用字段（过滤掉技术字段和时间戳）
          Object.keys(reference.data).forEach(key => {
            const value = reference.data[key]
            // 过滤掉不需要的字段
            const skipFields = ['name', 'description', 'id', 'template_id', 'created_at', 'updated_at', 'dimensions']
            if (!skipFields.includes(key) && value !== null && value !== undefined && value !== '') {
              // 处理对象类型的字段
              if (typeof value === 'object') {
                if (Array.isArray(value)) {
                  // 数组类型
                  if (value.length > 0) {
                    realContent += `- ${key}：${value.join(', ')}\n`
                  }
                } else {
                  // 对象类型，简化显示
                  realContent += `- ${key}：${JSON.stringify(value)}\n`
                }
              } else {
                // 基础类型
                realContent += `- ${key}：${value}\n`
              }
            }
          })

          realContent += `\n${REFERENCE_CONTENT_END}`
          break

        case 'chapter':
          try {
            // 获取章节的完整内容 - 需要bookId, volumeId, chapterId
            const chapterResponse = await window.pywebview.api.book_controller.get_chapter(
              props.bookId,
              reference.volume.id,
              reference.data.id
            )
            const chapterResult = typeof chapterResponse === 'string' ? JSON.parse(chapterResponse) : chapterResponse

            // 使用嵌套标记包装
            realContent = `${REFERENCE_MARKER_START}${reference.id}|chapter|${reference.data.title}${REFERENCE_MARKER_END}\n`
            realContent += `${REFERENCE_CONTENT_START}\n`
            realContent += `章节信息：\n`
            realContent += `- 标题：${reference.data.title}\n`

            if (reference.data.order) {
              realContent += `- 章节序号：第${reference.data.order}章\n`
            }

            if (reference.data.summary) {
              realContent += `- 摘要：${reference.data.summary}\n`
            }

            if (reference.volume && reference.volume.title) {
              realContent += `- 所属卷：${reference.volume.title}\n`
            }

            // 添加章节的有用字段（过滤掉技术字段）
            Object.keys(reference.data).forEach(key => {
              const value = reference.data[key]
              const skipFields = ['title', 'order', 'summary', 'id', 'volume_id', 'created_at', 'updated_at']
              if (!skipFields.includes(key) && value !== null && value !== undefined && value !== '') {
                if (typeof value === 'object') {
                  if (Array.isArray(value)) {
                    if (value.length > 0) {
                      realContent += `- ${key}：${JSON.stringify(value, null, 2)}\n`
                    }
                  } else {
                    realContent += `- ${key}：\n${JSON.stringify(value, null, 2)}\n`
                  }
                } else {
                  realContent += `- ${key}：${value}\n`
                }
              }
            })

            // 添加完整章节内容
            if (chapterResult.status === 'success' && chapterResult.data) {
              const chapterData = chapterResult.data
              if (chapterData.content && chapterData.content.trim()) {
                // 移除HTML标签，获取纯文本内容
                const textContent = chapterData.content.replace(/<[^>]+>/g, '').trim()
                if (textContent) {
                  realContent += `- 章节内容：\n${textContent}\n`
                }
              }
            }

            realContent += `\n${REFERENCE_CONTENT_END}`
          } catch (error) {
            console.warn('获取章节完整内容失败:', error)
            // 降级到基本信息
            realContent = `${REFERENCE_MARKER_START}${reference.id}|chapter|${reference.data.title}${REFERENCE_MARKER_END}\n`
            realContent += `${REFERENCE_CONTENT_START}\n`
            realContent += `章节信息：\n`
            realContent += `- 标题：${reference.data.title}\n`

            if (reference.data.order) {
              realContent += `- 章节序号：第${reference.data.order}章\n`
            }

            if (reference.data.summary) {
              realContent += `- 摘要：${reference.data.summary}\n`
            }

            if (reference.volume && reference.volume.title) {
              realContent += `- 所属卷：${reference.volume.title}\n`
            }

            realContent += `${REFERENCE_CONTENT_END}`
          }
          break

        case 'template':
          // 使用嵌套标记包装
          realContent = `${REFERENCE_MARKER_START}${reference.id}|template|${reference.data.name}${REFERENCE_MARKER_END}\n`
          realContent += `${REFERENCE_CONTENT_START}\n`
          realContent += `模板信息：\n`
          realContent += `- 名称：${reference.data.name}\n`

          if (reference.data.description) {
            realContent += `- 描述：${reference.data.description}\n`
          }

          // 添加该模板下的实体信息
          if (reference.entities && reference.entities.length > 0) {
            realContent += `- 包含实体数量：${reference.entities.length}个\n`
            realContent += `- 实体详细信息：\n`
            reference.entities.forEach((entity, index) => {
              realContent += `\n  ${index + 1}. 【实体名称】${entity.name}\n`

              if (entity.description && entity.description.trim()) {
                realContent += `     【实体描述】${entity.description.trim()}\n`
              }

              // 添加维度信息
              if (entity.dimensions && typeof entity.dimensions === 'object') {
                const dimensions = Object.entries(entity.dimensions)
                  .filter(([, value]) => value && value !== '未设定' && value.trim())

                if (dimensions.length > 0) {
                  realContent += `     【属性信息】\n`
                  dimensions.forEach(([key, value]) => {
                    realContent += `     - ${key}：${value}\n`
                  })
                }
              }
            })
          } else {
            realContent += `- 包含实体数量：0个\n`
            realContent += `- 状态：该模板下暂无实体\n`
          }

          realContent += `\n${REFERENCE_CONTENT_END}`
          break

        case 'scenePool':
          // 使用嵌套标记包装
          realContent = `${REFERENCE_MARKER_START}${reference.id}|scenePool|${reference.data.name}${REFERENCE_MARKER_END}\n`
          realContent += `${REFERENCE_CONTENT_START}\n`
          realContent += `场景卡池信息：\n`
          realContent += `- 名称：${reference.data.name}\n`

          if (reference.data.description) {
            realContent += `- 描述：${reference.data.description}\n`
          }

          if (reference.data.scenes && reference.data.scenes.length > 0) {
            realContent += `- 包含场景数量：${reference.data.scenes.length}\n`
            realContent += `- 场景详细信息：\n`
            reference.data.scenes.forEach((scene, index) => {
              realContent += `\n  ${index + 1}. 【场景标题】${scene.title || scene.name}\n`

              if (scene.description && scene.description.trim()) {
                realContent += `     【场景描述】${scene.description.trim()}\n`
              }

              if (scene.tags) {
                realContent += `     【场景标签】${scene.tags}\n`
              }

              
            })
          } else {
            realContent += `- 包含场景数量：0\n`
            realContent += `- 状态：该场景卡池暂无场景\n`
          }

          // 添加场景卡池的有用字段（过滤掉技术字段）
          Object.keys(reference.data).forEach(key => {
            const value = reference.data[key]
            const skipFields = ['id','name', 'createTime', 'updateTime','scenes','inspirations']
            if (!skipFields.includes(key) && value !== null && value !== undefined && value !== '') {
              if (typeof value === 'object') {
                if (Array.isArray(value)) {
                  if (value.length > 0) {
                    realContent += `- ${key}：${JSON.stringify(value, null, 2)}\n`
                  }
                } else {
                  realContent += `- ${key}：\n${JSON.stringify(value, null, 2)}\n`
                }
              } else {
                realContent += `- ${key}：${value}\n`
              }
            }
          })

          realContent += `\n${REFERENCE_CONTENT_END}`
          break

        default:
          // 保持原样
          break
      }
    } catch (error) {
      console.warn('转换引用内容失败:', error)
      // 出错时保持原显示文本
    }

    // 替换内容中的引用
    convertedContent = convertedContent.replace(displayText, realContent)
  }

  console.log('ChatUI: 显示→大模型 转换完成')
  console.log('ChatUI: 原始内容:', content.substring(0, 100) + '...')
  console.log('ChatUI: 转换后内容:', convertedContent.substring(0, 200) + '...')

  return convertedContent
}

// 将大模型内容转换回显示内容（大模型 → 显示）
const convertRealContentToBubbles = (content) => {
  if (!content) return content

  console.log('ChatUI: convertRealContentToBubbles input:', content.substring(0, 300))

  let result = content
  let searchIndex = 0

  // 使用字符串查找，更稳定可靠
  while (true) {
    // 查找引用标记开始位置
    const startIndex = result.indexOf(REFERENCE_MARKER_START, searchIndex)
    if (startIndex === -1) break

    // 查找引用标记结束位置
    const endIndex = result.indexOf(REFERENCE_MARKER_END, startIndex)
    if (endIndex === -1) break

    // 提取标记信息
    const markerContent = result.substring(startIndex + REFERENCE_MARKER_START.length, endIndex)
    const parts = markerContent.split('|')
    if (parts.length !== 3) {
      searchIndex = endIndex + REFERENCE_MARKER_END.length
      continue
    }

    const [referenceId, referenceType, referenceName] = parts

    // 查找内容开始和结束位置
    const contentStartIndex = result.indexOf(REFERENCE_CONTENT_START, endIndex)
    if (contentStartIndex === -1) {
      searchIndex = endIndex + REFERENCE_MARKER_END.length
      continue
    }

    const contentEndIndex = result.indexOf(REFERENCE_CONTENT_END, contentStartIndex)
    if (contentEndIndex === -1) {
      searchIndex = endIndex + REFERENCE_MARKER_END.length
      continue
    }

    console.log('ChatUI: 找到完整引用块:', { referenceId, referenceType, referenceName })

    // 创建显示用的@引用气泡
    const displayText = `@${referenceName}`
    const bubble = `<span class="message-reference-bubble" data-reference-type="${referenceType}" data-reference-id="${referenceId}" style="
      display: inline-flex;
      align-items: center;
      padding: 3px 6px;
      margin: 0 2px;
      color: white;
      border-radius: 10px;
      font-size: 11px;
      font-weight: 500;
      white-space: nowrap;
      vertical-align: middle;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      background: ${getReferenceColor(referenceType)};
    ">${displayText}</span>`

    // 替换整个引用块（从标记开始到内容结束）
    const fullBlockEnd = contentEndIndex + REFERENCE_CONTENT_END.length
    result = result.substring(0, startIndex) + bubble + result.substring(fullBlockEnd)

    // 更新搜索位置
    searchIndex = startIndex + bubble.length
  }

  console.log('ChatUI: convertRealContentToBubbles result:', result.substring(0, 300))
  return result
}



// 构建引用上下文信息
const buildReferenceContext = async (reference) => {
  let context = `\n### ${reference.displayText}\n`

  try {
    switch (reference.type) {
      case 'template':
        context += `**模板信息：**\n`
        context += `- 名称：${reference.data.name}\n`
        if (reference.data.description) {
          context += `- 描述：${reference.data.description}\n`
        }

        // 添加该模板下的实体信息
        if (reference.entities && reference.entities.length > 0) {
          context += `- 包含实体数量：${reference.entities.length}个\n`
          context += `- 实体详细信息：\n`
          reference.entities.forEach((entity, index) => {
            context += `\n  ${index + 1}. 【实体名称】${entity.name}\n`

            if (entity.description && entity.description.trim()) {
              context += `     【实体描述】${entity.description.trim()}\n`
            }

            // 添加维度信息
            if (entity.dimensions && typeof entity.dimensions === 'object') {
              const dimensions = Object.entries(entity.dimensions)
                .filter(([, value]) => value && value !== '未设定' && value.trim())

              if (dimensions.length > 0) {
                context += `     【属性信息】\n`
                dimensions.forEach(([key, value]) => {
                  context += `     - ${key}：${value}\n`
                })
              }
            }
          })
        } else {
          context += `- 包含实体数量：0个\n`
          context += `- 状态：该模板下暂无实体\n`
        }
        break

      case 'entity':
        context += `**实体信息：**\n`
        context += `- 名称：${reference.data.name}\n`
        if (reference.data.description) {
          context += `- 描述：${reference.data.description}\n`
        }
        if (reference.template) {
          context += `- 所属模板：${reference.template.name}\n`
        }

        // 尝试获取实体的详细内容
        try {
          const response = await window.pywebview.api.book_controller.get_entity_content(reference.data.id)
          const result = typeof response === 'string' ? JSON.parse(response) : response
          if (result.status === 'success' && result.data) {
            context += `- 详细内容：\n${result.data}\n`
          }
        } catch (error) {
          console.warn('获取实体详细内容失败:', error)
        }
        break

      case 'chapter':
        context += `**章节信息：**\n`
        context += `- 标题：${reference.data.title}\n`
        context += `- 章节序号：第${reference.data.order}章\n`
        if (reference.data.summary) {
          context += `- 摘要：${reference.data.summary}\n`
        }
        if (reference.volume) {
          context += `- 所属卷：${reference.volume.title}\n`
        }

        // 尝试获取章节内容
        try {
          const response = await window.pywebview.api.book_controller.get_chapter_content(reference.data.id)
          const result = typeof response === 'string' ? JSON.parse(response) : response
          if (result.status === 'success' && result.data) {
            // 限制章节内容长度，避免过长
            const content = result.data.length > 1000 ?
              result.data.substring(0, 1000) + '...' :
              result.data
            context += `- 章节内容：\n${content}\n`
          }
        } catch (error) {
          console.warn('获取章节内容失败:', error)
        }
        break

      case 'scenePool':
        context += `**场景卡池信息：**\n`
        context += `- 名称：${reference.data.name}\n`
        if (reference.data.description) {
          context += `- 描述：${reference.data.description}\n`
        }
        if (reference.data.scenes && reference.data.scenes.length > 0) {
          context += `- 包含场景数量：${reference.data.scenes.length}\n`
          context += `- 场景列表：\n`
          reference.data.scenes.slice(0, 5).forEach(scene => { // 只显示前5个场景
            context += `  - ${scene.name || scene.title}\n`
          })
          if (reference.data.scenes.length > 5) {
            context += `  - 还有${reference.data.scenes.length - 5}个场景...\n`
          }
        }
        break

      default:
        context += `**未知类型引用**\n`
        break
    }
  } catch (error) {
    console.error('构建引用上下文失败:', error)
    context += `**获取引用信息时出错：${error.message}**\n`
  }

  return context
}

// 处理消息内容，将@引用转换为气泡显示
const processMessageContent = (content, messageRole = 'user', message = null) => {
  if (!content) return content

  console.log('ChatUI: processMessageContent called', {
    content: content.substring(0, 200),
    messageRole,
    hasDisplayContent: message?.displayContent ? true : false,
    hasReferences: message?.references?.length > 0
  })

  // 如果是用户消息且有displayContent，优先使用displayContent和references
  if (messageRole === 'user' && message?.displayContent && message?.references) {
    return processDisplayContentWithReferences(message.displayContent, message.references)
  }

  // 首先尝试逆向转换（大模型 → 显示）
  let processedContent = convertRealContentToBubbles(content)

  // 如果没有找到特殊标记，则使用传统的@引用处理
  if (processedContent === content) {
    // 首先将转义的@符号临时替换为占位符，避免被误识别
    const escapedAtPlaceholder = '___ESCAPED_AT_PLACEHOLDER___'
    let tempContent = content.replace(/\\@/g, escapedAtPlaceholder)

    // 使用正则表达式匹配@引用模式（不包括转义的@）
    const referencePattern = /@([^@\s]+)/g

    tempContent = tempContent.replace(referencePattern, (match, referenceName) => {
      // 确定引用类型
      let referenceType = 'entity'
      if (referenceName.includes('章') || referenceName.includes('卷')) {
        referenceType = 'chapter'
      } else if (referenceName.includes('模板') || referenceName.includes('Template')) {
        referenceType = 'template'
      } else if (referenceName.includes('场景') || referenceName.includes('卡池')) {
        referenceType = 'scenePool'
      }

      return `<span class="message-reference-bubble" data-reference-type="${referenceType}" style="
        display: inline-flex;
        align-items: center;
        padding: 3px 6px;
        margin: 0 2px;
        color: white;
        border-radius: 10px;
        font-size: 11px;
        font-weight: 500;
        white-space: nowrap;
        vertical-align: middle;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        background: ${getReferenceColor(referenceType)};
      ">${match}</span>`
    })

    // 将转义的@符号恢复为普通@符号
    processedContent = tempContent.replace(new RegExp(escapedAtPlaceholder, 'g'), '@')
  }

  console.log('ChatUI: processMessageContent result', { processedContent: processedContent.substring(0, 200) })
  return processedContent
}

// 处理带有引用信息的显示内容
const processDisplayContentWithReferences = (displayContent, references) => {
  if (!displayContent || !references || references.length === 0) {
    return displayContent
  }

  let processedContent = displayContent

  // 将每个引用的displayText替换为HTML气泡
  references.forEach(reference => {
    const displayText = reference.displayText
    const bubble = `<span class="message-reference-bubble" data-reference-type="${reference.type}" data-reference-id="${reference.id}" style="
      display: inline-flex;
      align-items: center;
      padding: 3px 6px;
      margin: 0 2px;
      color: white;
      border-radius: 10px;
      font-size: 11px;
      font-weight: 500;
      white-space: nowrap;
      vertical-align: middle;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      background: ${getReferenceColor(reference.type)};
    ">${displayText}</span>`

    // 替换所有匹配的displayText
    processedContent = processedContent.replace(new RegExp(escapeRegExp(displayText), 'g'), bubble)
  })

  return processedContent
}

// 转义正则表达式特殊字符
const escapeRegExp = (string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

// 根据引用类型获取颜色 - 护眼精致配色方案
const getReferenceColor = (type) => {
  // 获取当前主题
  const isDark = theme.value === 'dark'

  switch (type) {
    case 'template':
      // 模板 - 优雅的紫罗兰色系，象征结构和框架
      return isDark
        ? 'linear-gradient(135deg, #8b7cf6 0%, #a78bfa 100%)' // 深色主题：较亮的紫色
        : 'linear-gradient(135deg, #6366f1 0%, #7c3aed 100%)' // 浅色主题：深紫到紫色

    case 'entity':
      // 实体 - 温暖的珊瑚橙色系，象征生命力和个性
      return isDark
        ? 'linear-gradient(135deg, #fb7185 0%, #fbbf24 100%)' // 深色主题：玫瑰到金黄
        : 'linear-gradient(135deg, #f43f5e 0%, #f97316 100%)' // 浅色主题：玫瑰红到橙色

    case 'chapter':
      // 章节 - 宁静的海洋蓝色系，象征知识的深度
      return isDark
        ? 'linear-gradient(135deg, #60a5fa 0%, #34d399 100%)' // 深色主题：天蓝到翠绿
        : 'linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%)' // 浅色主题：蓝色到青色

    case 'scenePool':
      // 场景卡池 - 自然的森林绿色系，象征创意和想象
      return isDark
        ? 'linear-gradient(135deg, #4ade80 0%, #22d3ee 100%)' // 深色主题：翠绿到青色
        : 'linear-gradient(135deg, #10b981 0%, #059669 100%)' // 浅色主题：祖母绿渐变

    case 'volume':
      // 卷 - 典雅的琥珀色系，象征时间的沉淀
      return isDark
        ? 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)' // 深色主题：金黄色系
        : 'linear-gradient(135deg, #d97706 0%, #92400e 100%)' // 浅色主题：琥珀到棕色

    case 'character':
      // 角色 - 活力的薰衣草紫色系，象征人物的魅力
      return isDark
        ? 'linear-gradient(135deg, #c084fc 0%, #e879f9 100%)' // 深色主题：淡紫到粉紫
        : 'linear-gradient(135deg, #8b5cf6 0%, #d946ef 100%)' // 浅色主题：紫色到品红

    case 'location':
      // 地点 - 大地的赭石色系，象征空间和环境
      return isDark
        ? 'linear-gradient(135deg, #fb923c 0%, #fdba74 100%)' // 深色主题：橙色到浅橙
        : 'linear-gradient(135deg, #ea580c 0%, #dc2626 100%)' // 浅色主题：橙红色系

    case 'item':
      // 物品 - 神秘的靛青色系，象征物质和工具
      return isDark
        ? 'linear-gradient(135deg, #818cf8 0%, #a5b4fc 100%)' // 深色主题：靛青到淡靛
        : 'linear-gradient(135deg, #4f46e5 0%, #6366f1 100%)' // 浅色主题：深靛到靛青

    default:
      // 默认 - 中性的石墨色系，低调而专业
      return isDark
        ? 'linear-gradient(135deg, #9ca3af 0%, #d1d5db 100%)' // 深色主题：中灰到浅灰
        : 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)' // 浅色主题：深灰渐变
  }
}

// ===== 测试和调试函数 =====
// 测试双向映射系统
const testBidirectionalMapping = () => {
  console.log('=== 测试双向映射系统 ===')

  // 模拟一个引用
  const testReference = {
    id: 'test-ref-1',
    type: 'entity',
    displayText: '@测试实体',
    data: { name: '测试实体', description: '这是一个测试实体' }
  }

  // 测试显示 → 大模型转换
  const displayContent = '请介绍一下@测试实体的相关信息'
  selectedReferences.value = [testReference]

  convertBubblesToRealContent(displayContent).then(realContent => {
    console.log('显示内容:', displayContent)
    console.log('转换为大模型内容:', realContent)

    // 测试大模型 → 显示转换
    const backToDisplay = convertRealContentToBubbles(realContent)
    console.log('转换回显示内容:', backToDisplay)

    // 清理测试数据
    selectedReferences.value = []
  })
}

// 在开发环境下暴露测试函数
if (process.env.NODE_ENV === 'development') {
  window.testBidirectionalMapping = testBidirectionalMapping
}

// ===== 测试和调试函数 =====
// 测试嵌套标记双向映射系统
const testNestedMarkerMapping = () => {
  console.log('=== 测试嵌套标记双向映射系统 ===')

  // 模拟一个实体引用
  const testReference = {
    id: 'test-ref-123',
    type: 'entity',
    displayText: '@测试实体',
    data: {
      name: '测试实体',
      description: '这是一个测试实体，包含（括号）和其他特殊字符！@#$%'
    },
    template: { name: '测试模板' }
  }

  // 测试显示 → 大模型转换
  const displayContent = '请介绍一下@测试实体的相关信息，以及它的特点。'
  selectedReferences.value = [testReference]

  convertBubblesToRealContent(displayContent).then(realContent => {
    console.log('1. 显示内容:', displayContent)
    console.log('2. 转换为大模型内容:')
    console.log(realContent)
    console.log('---')

    // 测试大模型 → 显示转换
    const backToDisplay = convertRealContentToBubbles(realContent)
    console.log('3. 转换回显示内容:')
    console.log(backToDisplay)
    console.log('---')

    // 验证是否成功
    const success = backToDisplay.includes('message-reference-bubble') &&
                   backToDisplay.includes('@测试实体') &&
                   !backToDisplay.includes('<<<REF_CONTENT_START>>>')

    console.log('4. 测试结果:', success ? '✅ 成功' : '❌ 失败')

    // 清理测试数据
    selectedReferences.value = []
  }).catch(error => {
    console.error('测试失败:', error)
    selectedReferences.value = []
  })
}

// 在开发环境下暴露测试函数到全局
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.testNestedMarkerMapping = testNestedMarkerMapping
  console.log('ChatUI: 测试函数已暴露到 window.testNestedMarkerMapping()')
}

// ===== 监听器 =====
// 监听模型变化
watch(selectedModel, async (newModel, oldModel) => {
  if (newModel !== oldModel && chatData.value) {
    chatData.value.model_id = newModel
    await saveChat()
  }
})

// 监听主题变化，确保组件能够实时响应主题切换
watch(() => configStore.theme, (newTheme, oldTheme) => {
  if (newTheme !== oldTheme) {
    console.log('ChatUI: 主题已切换从', oldTheme, '到', newTheme)
    // 主题变化时，Vue 的响应式系统会自动更新模板中的 theme 计算属性
    // 这里可以添加额外的主题切换逻辑，比如动画效果等
  }
}, { immediate: true })

// 监听chatId变化，重新加载数据
watch(() => props.chatId, async (newChatId) => {
  if (newChatId) {
    await loadChatData()
  }
}, { immediate: true })

// 监听bookId变化
watch(() => props.bookId, (newBookId) => {
  if (!newBookId) {
    // 如果bookId为空，清空引用
    selectedReferences.value = []
    if (inputRef.value) {
      // 清空富文本输入框中的引用气泡
      const bubbles = inputRef.value.querySelectorAll('.reference-bubble')
      bubbles.forEach(bubble => bubble.remove())
    }
  }
})

// 全局点击事件处理
const handleGlobalClick = (event) => {
  // 如果模型下拉框是打开的，检查点击是否在下拉框外部
  if (showModelDropdown.value) {
    const modelDropdown = document.querySelector('.model-dropdown')
    const modelSelectorEl = modelSelector.value

    if (modelDropdown && modelSelectorEl) {
      // 如果点击的不是模型选择器本身，也不是下拉框内部，则关闭下拉框
      if (!modelSelectorEl.contains(event.target) && !modelDropdown.contains(event.target)) {
        closeModelDropdown()
      }
    }
  }

  // 如果角色下拉框是打开的，检查点击是否在下拉框外部
  if (showRoleDropdown.value) {
    const roleDropdown = document.querySelector('.role-dropdown')
    const roleSelectorEl = roleSelector.value

    if (roleDropdown && roleSelectorEl) {
      // 如果点击的不是角色选择器本身，也不是下拉框内部，则关闭下拉框
      if (!roleSelectorEl.contains(event.target) && !roleDropdown.contains(event.target)) {
        closeRoleDropdown()
      }
    }
  }

  // 如果实体选择器是打开的，检查点击是否在选择器外部
  if (showEntitySelector.value) {
    const entitySelector = document.querySelector('.entity-selector')
    const referenceBtn = document.querySelector('.reference-btn')

    if (entitySelector && referenceBtn) {
      // 如果点击的不是引用按钮本身，也不是选择器内部，则关闭选择器
      if (!referenceBtn.contains(event.target) && !entitySelector.contains(event.target)) {
        closeEntitySelector()
      }
    }
  }
}

// 键盘事件处理
const handleGlobalKeydown = (event) => {
  // ESC 键关闭下拉框和选择器
  if (event.key === 'Escape') {
    if (showModelDropdown.value) {
      closeModelDropdown()
      event.preventDefault()
    }
    if (showRoleDropdown.value) {
      closeRoleDropdown()
      event.preventDefault()
    }
    if (showEntitySelector.value) {
      closeEntitySelector()
      event.preventDefault()
    }
  }
}

// ===== 生命周期 =====
onMounted(async () => {
  console.log('ChatUI: 组件挂载，props:', {
    chatId: props.chatId,
    bookId: props.bookId,
    editor: !!props.editor,
    selectedText: props.selectedText
  })

  // 保存原始处理函数
  originalReceiveChunk = window.receiveChunk
  originalOnMessageComplete = window.onMessageComplete
  originalReceiveChatError = window.receiveChatError

  // 替换为本组件的处理函数
  window.receiveChunk = handleReceiveChunk
  window.onMessageComplete = handleMessageComplete
  window.receiveChatError = handleChatError

  // 确保配置已加载，以便主题能够正确应用
  if (!configStore.loaded) {
    await configStore.loadConfig()
  }

  // 加载AI角色
  if (!aiRolesStore.roles.length) {
    await aiRolesStore.loadRoles()
  }

  // 初始化数据
  await loadModels()
  await loadChatSessions()
  await loadChatData()

  // 添加全局事件监听器
  document.addEventListener('click', handleGlobalClick)
  document.addEventListener('keydown', handleGlobalKeydown)

  // 初始化输入框高度和监听器
  nextTick(() => {
    adjustInputHeight()
    setupInputObserver()
  })

  // 设置输入框内容变化监听器
  const setupInputObserver = () => {
    const inputElement = inputRef.value
    if (!inputElement) return

    // 使用MutationObserver监听内容变化
    const observer = new MutationObserver(() => {
      console.log('ChatUI: 内容发生变化，调整高度')
      adjustInputHeight()
    })

    observer.observe(inputElement, {
      childList: true,
      subtree: true,
      characterData: true
    })

    // 也监听键盘输入事件
    inputElement.addEventListener('input', () => {
      console.log('ChatUI: input事件触发')
      adjustInputHeight()
    })
  }

  // 只有在有具体会话时才聚焦输入框
  if (props.chatId) {
    nextTick(() => {
      if (inputRef.value?.focus) {
        inputRef.value.focus()
      }
    })
  }
})

// 组件卸载时的清理
onUnmounted(() => {
  // 恢复原始处理函数
  if (originalReceiveChunk) window.receiveChunk = originalReceiveChunk
  if (originalOnMessageComplete) window.onMessageComplete = originalOnMessageComplete
  if (originalReceiveChatError) window.receiveChatError = originalReceiveChatError

  // 移除全局事件监听器
  document.removeEventListener('click', handleGlobalClick)
  document.removeEventListener('keydown', handleGlobalKeydown)
})


</script>

<style scoped>

.mate-chat {
  /* CSS 变量 - 浅色主题 */
  --chat-bg-primary: #ffffff;
  --chat-bg-secondary: #f8f9fa;
  --chat-bg-tertiary: #e9ecef;
  --chat-border: #dee2e6;
  --chat-text-primary: #212529;
  --chat-text-secondary: #6c757d;
  --chat-text-muted: #adb5bd;
  --chat-accent: #0d6efd;
  --chat-accent-rgb: 13, 110, 253;
  --chat-accent-secondary: #6610f2;
  --chat-accent-hover: #0b5ed7;
  --chat-accent-alpha: rgba(13, 110, 253, 0.1);
  --chat-success: #198754;
  --chat-success-alpha: rgba(25, 135, 84, 0.1);
  --chat-warning: #ffc107;
  --chat-danger: #dc3545;
  --chat-error: #dc3545;
  --chat-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --chat-shadow-alpha: rgba(0, 0, 0, 0.05);
  --chat-radius: 8px;

  /* 滚动条 */
  --chat-scrollbar-track: rgba(0, 0, 0, 0.05);
  --chat-scrollbar-thumb: rgba(0, 0, 0, 0.2);
  --chat-scrollbar-thumb-hover: rgba(0, 0, 0, 0.3);
}

/* 暗色主题 */
.mate-chat.theme-dark {
  --chat-bg-primary: #1a1a1a;
  --chat-bg-secondary: #2d2d2d;
  --chat-bg-tertiary: #404040;
  --chat-border: #555555;
  --chat-text-primary: #ffffff;
  --chat-text-secondary: #cccccc;
  --chat-text-muted: #999999;
  --chat-accent: #4dabf7;
  --chat-accent-rgb: 77, 171, 247;
  --chat-accent-secondary: #7c3aed;
  --chat-accent-hover: #339af0;
  --chat-accent-alpha: rgba(77, 171, 247, 0.15);
  --chat-success: #51cf66;
  --chat-success-alpha: rgba(81, 207, 102, 0.15);
  --chat-warning: #ffd43b;
  --chat-danger: #ff6b6b;
  --chat-error: #ff6b6b;
  --chat-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  --chat-shadow-alpha: rgba(0, 0, 0, 0.2);

  /* 滚动条 */
  --chat-scrollbar-track: rgba(255, 255, 255, 0.05);
  --chat-scrollbar-thumb: rgba(255, 255, 255, 0.2);
  --chat-scrollbar-thumb-hover: rgba(255, 255, 255, 0.3);
}
/* ===== 基础样式 ===== */
.mate-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--chat-bg-primary);
  color: var(--chat-text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  position: relative;
  min-width: 350px; /* 确保底部工具栏不被挤压 */
}
/* ===== 聊天头部 ===== */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: var(--chat-bg-secondary);
  border-bottom: 1px solid var(--chat-border);
  flex-shrink: 0;
  height: 48px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-center {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 菜单按钮 */
.menu-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--chat-text-secondary);
  transition: all 0.2s ease;
}

.menu-btn:hover {
  background: var(--chat-bg-tertiary);
  color: var(--chat-text-primary);
}

.menu-icon {
  width: 16px;
  height: 16px;
}

.mode-option-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}





/* 新建对话按钮 */
.new-chat-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--chat-text-secondary);
  transition: all 0.2s ease;
}

.new-chat-btn:hover {
  background: var(--chat-bg-tertiary);
  color: var(--chat-text-primary);
}

.plus-icon {
  width: 16px;
  height: 16px;
}

/* ===== 消息区域 ===== */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

/* 欢迎界面 */
.welcome-screen {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.welcome-content {
  text-align: center;
  max-width: 500px;
}

.welcome-icon {
  margin-bottom: 24px;
}

.icon-bg {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--chat-accent) 0%, var(--chat-accent-hover) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  margin: 0 auto;
  box-shadow: var(--chat-shadow);
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--chat-text-primary);
  margin: 0 0 12px 0;
}

.welcome-subtitle {
  font-size: 16px;
  color: var(--chat-text-secondary);
  line-height: 1.5;
  margin: 0 0 32px 0;
}



/* 消息列表 */
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 系统消息样式 */
.system-message {
  text-align: center;
  padding: 8px 16px;
  margin: 16px 0;
  background: var(--chat-bg-tertiary);
  border: none;
  border-radius: 16px;
  font-size: 12px;
  color: var(--chat-text-secondary);
  display: inline-block;
  max-width: fit-content;
  align-self: center;
}

.message-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  flex-shrink: 0;
}

.user-avatar {
  background: var(--chat-accent);
  color: white;
}

.ai-avatar-small {
  background: linear-gradient(135deg, var(--chat-accent) 0%, var(--chat-accent-hover) 100%);
  font-size: 16px;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.message-sender {
  font-size: 14px;
  font-weight: 600;
  color: var(--chat-text-primary);
}

.message-time {
  font-size: 12px;
  color: var(--chat-text-muted);
}

.message-content {
  margin-left: 40px;
}

.message-body {
  background: var(--chat-bg-secondary);
  border: 1px solid var(--chat-border);
  border-radius: var(--chat-radius);
  padding: 12px 16px;
  margin-bottom: 8px;
}

.message-user .message-body {
  background: var(--chat-accent);
  color: white;
  border-color: var(--chat-accent);
}

.message-system {
  align-items: center;
  margin: 16px 0;
}

.message-system .message-content {
  margin-left: 0;
  text-align: center;
}

.message-system .message-body {
  background: var(--chat-bg-tertiary);
  border: none;
  padding: 8px 16px;
  border-radius: 16px;
  font-size: 12px;
  color: var(--chat-text-secondary);
  display: inline-block;
}

.message-text {
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
}

.streaming-text {
  font-size: 14px;
  line-height: 1.5;
}

.cursor {
  animation: blink 1s infinite;
}

.message-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.action-btn {
  padding: 4px 8px;
  border: none;
  background: var(--chat-bg-tertiary);
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: var(--chat-text-secondary);
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--chat-bg-primary);
  color: var(--chat-text-primary);
}

/* 加载指示器 */
.loading-indicator {
  display: flex;
  justify-content: center;
  padding: 24px 0;
  margin: 16px 0;
}

.loading-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 18px;
  background: var(--chat-bg-secondary);
  border: 1px solid var(--chat-border);
  border-radius: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 主题适配的微光效果 */
.theme-light .loading-wrapper {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.theme-dark .loading-wrapper {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.25);
  background: rgba(var(--chat-bg-secondary-rgb, 64, 64, 64), 0.8);
}

.loading-spinner-ai {
  position: relative;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid var(--chat-border);
  border-top: 2px solid var(--chat-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background: var(--chat-accent);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse-dot 1.5s ease-in-out infinite;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: 6px;
  line-height: 20px;
}

.text-content {
  font-size: 14px;
  font-weight: 500;
  color: var(--chat-text-primary);
  white-space: nowrap;
  line-height: 20px;
}

.typing-dots {
  display: flex;
  gap: 2px;
  align-items: center;
  height: 20px;
}

.typing-dots span {
  width: 3px;
  height: 3px;
  background: var(--chat-accent);
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
  display: block;
}

.typing-dots span:nth-child(1) { animation-delay: 0s; }
.typing-dots span:nth-child(2) { animation-delay: 0.2s; }
.typing-dots span:nth-child(3) { animation-delay: 0.4s; }

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-indicator {
    margin: 16px 20px;
    padding: 16px 20px;
    max-width: none;
  }

  .loading-avatar {
    width: 36px;
    height: 36px;
  }

  .ai-icon {
    width: 18px;
    height: 18px;
  }

  .loading-text {
    font-size: 13px;
  }
}

/* ===== 输入区域 ===== */
.chat-input {
  flex-shrink: 0;
  background: var(--chat-bg-secondary);
  border-top: 1px solid var(--chat-border);
}

.input-container {
  padding: 16px 20px;
}

.input-wrapper {
  background: var(--chat-bg-primary);
  border: 1px solid var(--chat-border);
  border-radius: var(--chat-radius);
  overflow: hidden;
  transition: border-color 0.2s ease;
}

.input-wrapper:focus-within {
  border-color: var(--chat-accent);
}

.input-main {
  position: relative;
}

.message-input {
  width: 100%;
  min-height: 60px;
  max-height: 120px;
  padding: 12px 16px;
  border: none;
  outline: none;
  background: transparent;
  color: var(--chat-text-primary);
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  font-family: inherit;
}

.message-input::placeholder {
  color: var(--chat-text-muted);
}

/* 输入框底部工具栏 */
.input-bottom-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--chat-bg-primary);
  border-top: 1px solid var(--chat-border);
  min-height: 44px;
  min-width: 320px; /* 确保工具栏不被过度挤压 */
  overflow: hidden; /* 防止内容溢出 */
}

.bottom-left,
.bottom-right {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0; /* 允许收缩 */
}

.bottom-left {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.bottom-right {
  flex-shrink: 0; /* 右侧按钮不收缩 */
}



/* 分隔符 */
.separator {
  width: 1px;
  height: 20px;
  background: var(--chat-border);
  margin: 0 4px;
}

/* 模型选择器 */
.model-selector {
  position: relative;
  display: flex;
  align-items: center;
}

.model-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  color: var(--chat-text-secondary);
  font-size: 12px;
  transition: all 0.2s ease;
  width: 120px; /* 固定宽度 */
  justify-content: flex-start;
}

.model-btn:hover {
  background: var(--chat-bg-secondary);
  color: var(--chat-text-primary);
}

.model-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  flex-shrink: 0; /* 防止图标被压缩 */
}

.model-icon-svg {
  width: 100%;
  height: 100%;
  color: inherit;
}

.model-name {
  font-weight: 500;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0; /* 允许 flex 项目收缩到内容宽度以下 */
}

.model-dropdown-arrow {
  width: 10px;
  height: 10px;
  opacity: 0.6;
  transition: transform 0.2s ease;
  flex-shrink: 0; /* 防止箭头被压缩 */
}

.model-dropdown-arrow.rotated {
  transform: rotate(180deg);
}

/* 模型下拉菜单覆盖层 */
.model-dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  background: transparent;
}

/* 模型下拉菜单 */
.model-dropdown {
  background: var(--chat-bg-primary);
  border: 1px solid var(--chat-border);
  border-radius: 8px;
  box-shadow: var(--chat-shadow);
  padding: 4px;
  overflow-y: auto;
}

/* 优化滚动条样式 */
.model-dropdown::-webkit-scrollbar {
  width: 6px;
}

.model-dropdown::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.model-dropdown::-webkit-scrollbar-thumb {
  background: var(--chat-text-muted);
  border-radius: 3px;
  opacity: 0.4;
  transition: opacity 0.2s ease;
}

.model-dropdown::-webkit-scrollbar-thumb:hover {
  background: var(--chat-text-secondary);
  opacity: 0.7;
}

/* Firefox 滚动条样式 */
.model-dropdown {
  scrollbar-width: thin;
  scrollbar-color: var(--chat-text-muted) transparent;
}



.model-option {
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 1px 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--chat-text-primary);
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.model-option:hover {
  background: var(--chat-bg-secondary);
}

.model-option.active {
  background: var(--chat-accent);
  color: white;
}

.model-option.active:hover {
  background: var(--chat-accent-hover);
}

/* 右侧图标按钮 */
.icon-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: var(--chat-text-muted);
  transition: all 0.2s ease;
}

.icon-btn:hover:not(:disabled) {
  background: var(--chat-bg-secondary);
  color: var(--chat-text-primary);
}

.icon-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-btn.can-send {
  background: var(--chat-accent);
  color: white;
}

.send-btn.can-send:hover {
  background: var(--chat-accent-hover);
}

.send-icon {
  width: 14px;
  height: 14px;
}

.stop-btn {
  background: var(--chat-danger);
  color: white;
}

.stop-btn:hover {
  background: #c82333;
}

/* 章节内容按钮 */
.context-btn {
  font-weight: 600;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.context-btn.active {
  background: var(--chat-accent);
  color: white;
}

.context-btn.active:hover {
  background: var(--chat-accent-hover);
}



/* ===== 动画 ===== */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  } 40% {
    transform: scale(1);
  }
}

/* 新的加载指示器动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: translateY(0);
  }
  30% {
    opacity: 1;
    transform: translateY(-4px);
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* ===== 滚动条样式 ===== */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: var(--chat-bg-secondary);
}

.chat-messages::-webkit-scrollbar-thumb {
  background: var(--chat-border);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: var(--chat-text-muted);
}

/* ===== 响应式设计 ===== */

/* 中等屏幕 - 缩小模型按钮宽度 */
@media (max-width: 900px) {
  .model-btn {
    width: 100px;
  }
}

/* 当面板宽度受限时的优化 */
@media (max-width: 500px) {
  .mate-chat {
    min-width: 320px; /* 进一步减小最小宽度 */
  }

  .input-bottom-bar {
    min-width: 300px;
  }

  .model-name {
    display: none; /* 隐藏模型名称，只显示图标 */
  }



  .separator {
    display: none; /* 隐藏分隔符 */
  }
}

/* 小屏幕 - 进一步缩小 */
@media (max-width: 600px) {
  .model-btn {
    width: 80px;
  }

  .input-bottom-bar {
    padding: 6px 8px;
    gap: 4px;
  }

  .bottom-left,
  .bottom-right {
    gap: 4px;
  }

  .icon-btn {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }
}

/* 移动端 */
@media (max-width: 768px) {
  .chat-header {
    padding: 12px 16px;
  }

  .header-actions {
    gap: 8px;
  }

  .mode-toggle {
    display: none;
  }

  .chat-messages {
    padding: 16px;
  }

  .input-container {
    padding: 12px 16px;
  }

  .welcome-title {
    font-size: 20px;
  }

  .welcome-subtitle {
    font-size: 14px;
  }

  .input-bottom-bar {
    padding: 6px 12px;
  }

  .model-btn {
    width: 40px; /* 移动端缩小宽度 */
  }

  .model-name {
    display: none;
  }

  .separator {
    display: none;
  }
}

/* 极小屏幕 - 最小宽度优化 */
@media (max-width: 400px) {
  .input-bottom-bar {
    padding: 4px 6px;
    min-height: 36px;
    min-width: 280px; /* 极小屏幕的最小宽度 */
  }

  .model-btn {
    width: 32px;
    min-width: 32px; /* 确保最小可点击区域 */
    padding: 2px 4px;
    gap: 2px;
    justify-content: center;
  }

  .model-name {
    display: none;
  }

  .model-dropdown-arrow {
    width: 8px;
    height: 8px;
  }

  .icon-btn {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }



  .separator {
    display: none;
  }

  .bottom-left,
  .bottom-right {
    gap: 2px;
  }
}

/* ===== 会话历史面板 ===== */
.history-panel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--chat-bg-primary);
  backdrop-filter: blur(10px);
  z-index: 100;
  display: flex;
  flex-direction: column;
  border-radius: 0;
  box-shadow: var(--chat-shadow);
}

/* 面板头部 */
.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: var(--chat-bg-secondary);
  border-bottom: 1px solid var(--chat-border);
  flex-shrink: 0;
  height: 48px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.back-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--chat-text-secondary);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.back-btn:hover {
  background: var(--chat-bg-tertiary);
  color: var(--chat-text-primary);
}

.back-icon {
  width: 16px;
  height: 16px;
}

.header-title {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.history-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--chat-text-primary);
  margin: 0;
  line-height: 1.2;
}

.history-count {
  font-size: 11px;
  color: var(--chat-text-muted);
  font-weight: 400;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--chat-text-secondary);
  transition: all 0.2s ease;
  position: relative;
}

.action-btn:hover {
  background: var(--chat-bg-tertiary);
  color: var(--chat-text-primary);
}

.action-btn:active {
  transform: none;
}

.action-icon {
  width: 14px;
  height: 14px;
}

.new-chat-btn:hover {
  background: var(--chat-success);
  color: white;
}

.refresh-btn:hover {
  background: var(--chat-accent);
  color: white;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refresh-btn:disabled:hover {
  background: transparent;
  color: var(--chat-text-secondary);
}

/* 刷新图标旋转动画 */
.refresh-icon {
  transition: transform 0.2s ease;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}











/* 搜索栏 */
.history-search {
  padding: 16px 20px;
  border-bottom: 1px solid var(--chat-border);
  background: var(--chat-bg-primary);
  flex-shrink: 0;
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  width: 16px;
  height: 16px;
  color: var(--chat-text-muted);
  z-index: 1;
}

.search-input {
  width: 100%;
  height: 40px;
  padding: 0 40px 0 40px;
  border: 1px solid var(--chat-border);
  border-radius: 10px;
  background: var(--chat-bg-secondary);
  color: var(--chat-text-primary);
  font-size: 14px;
  transition: all 0.2s ease;
  outline: none;
}

.search-input:focus {
  border-color: var(--chat-accent);
  background: var(--chat-bg-primary);
  box-shadow: 0 0 0 3px var(--chat-accent-alpha);
}

.search-input::placeholder {
  color: var(--chat-text-muted);
}

.clear-search {
  position: absolute;
  right: 8px;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--chat-text-muted);
  transition: all 0.2s ease;
}

.clear-search:hover {
  background: var(--chat-bg-tertiary);
  color: var(--chat-text-secondary);
}

.clear-search svg {
  width: 14px;
  height: 14px;
}

/* 历史内容区域 */
.history-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 加载状态 */
.loading-sessions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--chat-bg-tertiary);
  border-top: 3px solid var(--chat-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}





/* 空状态样式 */
.empty-sessions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  flex: 1;
}

.empty-illustration {
  margin-bottom: 24px;
  opacity: 0.6;
}

.empty-icon {
  width: 80px;
  height: 80px;
  color: var(--chat-text-muted);
}

.empty-content {
  max-width: 280px;
}

.empty-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--chat-text-primary);
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.empty-description {
  font-size: 14px;
  color: var(--chat-text-secondary);
  margin: 0 0 24px 0;
  line-height: 1.4;
}

.empty-action {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--chat-accent);
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px var(--chat-accent-alpha);
}

.empty-action:hover {
  background: var(--chat-accent-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--chat-accent-alpha);
}

.empty-action:active {
  transform: translateY(0);
}

/* 会话容器 */
.sessions-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.sessions-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px 20px;

  /* 自定义滚动条 */
  scrollbar-width: thin;
  scrollbar-color: var(--chat-scrollbar-thumb) var(--chat-scrollbar-track);
}

/* Webkit 滚动条样式 */
.sessions-list::-webkit-scrollbar {
  width: 6px;
}

.sessions-list::-webkit-scrollbar-track {
  background: var(--chat-scrollbar-track);
  border-radius: 3px;
}

.sessions-list::-webkit-scrollbar-thumb {
  background: var(--chat-scrollbar-thumb);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.sessions-list::-webkit-scrollbar-thumb:hover {
  background: var(--chat-scrollbar-thumb-hover);
}

/* 会话项 */
.session-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--chat-bg-secondary);
  border: 2px solid transparent;
  margin-bottom: 8px;
  position: relative;
  overflow: hidden;
}

.session-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--chat-accent-alpha) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.session-item:hover {
  background: var(--chat-bg-tertiary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--chat-shadow-alpha);
}

.session-item:hover::before {
  opacity: 0.1;
}

.session-item.active {
  background: var(--chat-accent-alpha);
  border-color: var(--chat-accent);
  box-shadow: 0 4px 16px var(--chat-accent-alpha);
}

.session-item.active::before {
  opacity: 0.2;
}

.session-item.has-messages .session-avatar {
  background: var(--chat-success-alpha);
  color: var(--chat-success);
}

/* 会话头像 */
.session-avatar {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: var(--chat-bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: var(--chat-text-muted);
  transition: all 0.2s ease;
}

.avatar-icon {
  width: 20px;
  height: 20px;
}

/* 会话内容 */
.session-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.session-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 8px;
}

.session-title {
  font-size: 15px;
  font-weight: 600;
  color: var(--chat-text-primary);
  margin: 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.session-time {
  font-size: 11px;
  color: var(--chat-text-muted);
  font-weight: 500;
  flex-shrink: 0;
  opacity: 0.8;
}

.session-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.message-count {
  color: var(--chat-text-secondary);
  font-weight: 500;
}

.session-model {
  color: var(--chat-text-muted);
  background: var(--chat-bg-tertiary);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* 会话操作 */
.session-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.session-item:hover .session-actions {
  opacity: 1;
}

.delete-btn {
  background: var(--chat-bg-tertiary);
  color: var(--chat-text-muted);
}

.delete-btn:hover {
  background: var(--chat-error);
  color: white;
}

/* ===== 富文本输入框样式 ===== */
.rich-input-container {
  position: relative;
  width: 100%;
}

.rich-message-input {
  width: 100%;
  /* 基于行数的高度设计 - 类似textarea rows属性 */
  min-height: calc(2 * 1.5em + 24px); /* 2行 + padding */
  height: calc(2 * 1.5em + 24px); /* 默认2行高度 */
  max-height: calc(6 * 1.5em + 24px); /* 最多6行 */
  padding: 12px 16px;
  border: 2px solid var(--chat-border);
  border-radius: 12px;
  background: var(--chat-bg-primary);
  color: var(--chat-text-primary);
  font-size: 14px;
  line-height: 1.5; /* 1.5em行高，类似textarea的行高 */
  font-family: inherit;
  resize: none;
  outline: none;
  overflow-y: hidden;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 自定义滚动条 */
.rich-message-input::-webkit-scrollbar {
  width: 4px;
}

.rich-message-input::-webkit-scrollbar-track {
  background: transparent;
}

.rich-message-input::-webkit-scrollbar-thumb {
  background: var(--chat-border);
  border-radius: 2px;
}

.rich-message-input::-webkit-scrollbar-thumb:hover {
  background: var(--chat-text-secondary);
}

.rich-message-input:focus {
  border-color: var(--chat-accent);
  box-shadow: 0 0 0 3px var(--chat-accent-alpha);
}

.rich-message-input.disabled {
  background: var(--chat-bg-secondary);
  color: var(--chat-text-disabled);
  cursor: not-allowed;
}

/* 占位符样式 */
.rich-message-input:empty::before {
  content: attr(data-placeholder);
  color: var(--chat-text-placeholder);
  pointer-events: none;
  position: absolute;
}

/* ===== 引用气泡样式 ===== */
/* 使用更高优先级的选择器，确保样式能应用到动态创建的元素 - 调整为更小尺寸 */
.rich-message-input .reference-bubble,
.reference-bubble {
  display: inline-flex !important;
  align-items: center !important;
  gap: 3px !important;
  padding: 4px 8px !important;
  margin: 0 2px !important;
  background: var(--chat-accent) !important;
  color: white !important;
  border-radius: 12px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  white-space: nowrap !important;
  cursor: default !important;
  user-select: none !important;
  position: relative !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12) !important;
  transition: all 0.2s ease !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  vertical-align: middle !important;
}

.rich-message-input .reference-bubble:hover,
.reference-bubble:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25) !important;
  background: var(--chat-accent-hover) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
}

.rich-message-input .reference-delete,
.reference-delete {
  display: none !important; /* 完全隐藏，只在悬停时显示 */
  align-items: center !important;
  justify-content: center !important;
  width: 14px !important;
  height: 14px !important;
  margin-left: 3px !important;
  background: rgba(255, 255, 255, 0.25) !important;
  border-radius: 50% !important;
  font-size: 10px !important;
  font-weight: bold !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  line-height: 1 !important;
}

/* 悬停时显示删除按钮 */
.rich-message-input .reference-bubble:hover .reference-delete,
.reference-bubble:hover .reference-delete {
  display: inline-flex !important;
}

.rich-message-input .reference-delete:hover,
.reference-delete:hover {
  background: rgba(255, 255, 255, 0.4) !important;
  transform: scale(1.1) !important;
}

/* 不同类型的引用气泡颜色 - 更鲜明的设计，使用高优先级 */
.rich-message-input .reference-bubble[data-reference-type="template"],
.reference-bubble[data-reference-type="template"] {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
  border-color: rgba(99, 102, 241, 0.3) !important;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3) !important;
}

.rich-message-input .reference-bubble[data-reference-type="template"]:hover,
.reference-bubble[data-reference-type="template"]:hover {
  background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%) !important;
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4) !important;
}

.rich-message-input .reference-bubble[data-reference-type="entity"],
.reference-bubble[data-reference-type="entity"] {
  background: linear-gradient(135deg, #ec4899 0%, #f97316 100%) !important;
  border-color: rgba(236, 72, 153, 0.3) !important;
  box-shadow: 0 2px 8px rgba(236, 72, 153, 0.3) !important;
}

.rich-message-input .reference-bubble[data-reference-type="entity"]:hover,
.reference-bubble[data-reference-type="entity"]:hover {
  background: linear-gradient(135deg, #db2777 0%, #ea580c 100%) !important;
  box-shadow: 0 4px 16px rgba(236, 72, 153, 0.4) !important;
}

.rich-message-input .reference-bubble[data-reference-type="chapter"],
.reference-bubble[data-reference-type="chapter"] {
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
}

.rich-message-input .reference-bubble[data-reference-type="chapter"]:hover,
.reference-bubble[data-reference-type="chapter"]:hover {
  background: linear-gradient(135deg, #2563eb 0%, #0891b2 100%) !important;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4) !important;
}

.rich-message-input .reference-bubble[data-reference-type="scenePool"],
.reference-bubble[data-reference-type="scenePool"] {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  border-color: rgba(16, 185, 129, 0.3) !important;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3) !important;
}

.rich-message-input .reference-bubble[data-reference-type="scenePool"]:hover,
.reference-bubble[data-reference-type="scenePool"]:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4) !important;
}

/* 深色主题下的引用气泡适配 */
.theme-dark .reference-bubble {
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.theme-dark .reference-bubble:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.25);
}

/* 深色主题下保持相同的渐变颜色，但调整透明度 */
.theme-dark .reference-bubble[data-reference-type="template"] {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.4);
}

.theme-dark .reference-bubble[data-reference-type="template"]:hover {
  background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%);
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.5);
}

.theme-dark .reference-bubble[data-reference-type="entity"] {
  background: linear-gradient(135deg, #ec4899 0%, #f97316 100%);
  box-shadow: 0 2px 8px rgba(236, 72, 153, 0.4);
}

.theme-dark .reference-bubble[data-reference-type="entity"]:hover {
  background: linear-gradient(135deg, #db2777 0%, #ea580c 100%);
  box-shadow: 0 4px 16px rgba(236, 72, 153, 0.5);
}

.theme-dark .reference-bubble[data-reference-type="chapter"] {
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

.theme-dark .reference-bubble[data-reference-type="chapter"]:hover {
  background: linear-gradient(135deg, #2563eb 0%, #0891b2 100%);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.5);
}

.theme-dark .reference-bubble[data-reference-type="scenePool"] {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
}

.theme-dark .reference-bubble[data-reference-type="scenePool"]:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.5);
}

/* 引用按钮样式增强 */
.reference-btn {
  background: linear-gradient(135deg, var(--chat-accent) 0%, var(--chat-accent-secondary) 100%);
  color: white;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.reference-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.reference-btn:hover::before {
  left: 100%;
}

.reference-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--chat-accent-alpha);
}

/* ===== 消息中的引用气泡样式 ===== */
:deep(.message-reference-bubble) {
  display: inline-flex !important;
  align-items: center !important;
  padding: 3px 6px !important;
  margin: 0 2px !important;
  color: white !important;
  border-radius: 10px !important;
  font-size: 11px !important;
  font-weight: 500 !important;
  white-space: nowrap !important;
  vertical-align: middle !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  transition: all 0.2s ease !important;
  /* 背景色由内联样式设置，根据类型不同 */
}

:deep(.message-reference-bubble):hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

/* 消息中不同类型的引用气泡颜色 - 确保优先级 */
:deep(.message-reference-bubble[data-reference-type="template"]) {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
}

:deep(.message-reference-bubble[data-reference-type="entity"]) {
  background: linear-gradient(135deg, #ec4899 0%, #f97316 100%) !important;
}

:deep(.message-reference-bubble[data-reference-type="chapter"]) {
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%) !important;
}

:deep(.message-reference-bubble[data-reference-type="scenePool"]) {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

/* 深色主题下的消息引用气泡 */
.theme-dark :deep(.message-reference-bubble) {
  border-color: rgba(255, 255, 255, 0.15) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

.theme-dark :deep(.message-reference-bubble):hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4) !important;
}

/* 搜索匹配预览样式 */
.search-match-preview {
  margin-top: 4px;
}

.match-snippet {
  font-size: 12px;
  line-height: 1.4;
  color: var(--text-secondary);
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.match-type {
  font-weight: 600;
  color: var(--primary-color);
  margin-right: 4px;
}

.match-snippet :deep(mark) {
  background-color: var(--primary-color);
  color: white;
  padding: 1px 3px;
  border-radius: 3px;
  font-weight: 500;
  font-size: 11px;
}

.session-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* 深色主题下的搜索匹配样式 */
.theme-dark .match-snippet {
  color: var(--text-secondary);
}

.theme-dark .match-type {
  color: var(--primary-color);
}

.theme-dark .match-snippet :deep(mark) {
  background-color: var(--primary-color);
  color: var(--bg-primary);
}

/* 角色选择器样式 */
.role-selector {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.role-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--chat-text-secondary);
  font-size: 13px;
  min-width: 100px;
  justify-content: space-between;
}

.role-btn:hover {
  background: var(--chat-bg-secondary);
  color: var(--chat-text-primary);
}

.role-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.role-icon-svg {
  width: 100%;
  height: 100%;
}

.role-name {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
}

.role-dropdown-arrow {
  width: 10px;
  height: 10px;
  opacity: 0.6;
  transition: transform 0.2s ease;
  flex-shrink: 0;
}

.role-dropdown-arrow.rotated {
  transform: rotate(180deg);
}

/* 角色下拉框样式 */
.role-dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  background: transparent;
}

.role-dropdown {
  background: var(--chat-bg-primary);
  border: 1px solid var(--chat-border);
  border-radius: 8px;
  box-shadow: var(--chat-shadow);
  padding: 4px;
  overflow-y: auto;
}

/* 优化滚动条样式 */
.role-dropdown::-webkit-scrollbar {
  width: 6px;
}

.role-dropdown::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.role-dropdown::-webkit-scrollbar-thumb {
  background: var(--chat-text-muted);
  border-radius: 3px;
  opacity: 0.4;
  transition: opacity 0.2s ease;
}

.role-dropdown::-webkit-scrollbar-thumb:hover {
  background: var(--chat-text-secondary);
  opacity: 0.7;
}

/* Firefox 滚动条样式 */
.role-dropdown {
  scrollbar-width: thin;
  scrollbar-color: var(--chat-text-muted) transparent;
}

.role-option {
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 1px 0;
  color: var(--chat-text-primary);
}

.role-option:hover {
  background: var(--chat-bg-secondary);
}

.role-option.active {
  background: var(--chat-accent);
  color: white;
}

.role-option.active:hover {
  background: var(--chat-accent-hover);
}

.role-option-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.role-name {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
}

.role-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid var(--chat-border);
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.role-option.active .role-checkbox {
  background: white;
  border-color: white;
}

.role-option:not(.active) .role-checkbox.checked {
  background: var(--chat-accent);
  border-color: var(--chat-accent);
}

.check-icon {
  width: 10px;
  height: 10px;
  color: var(--chat-accent);
}

.role-option.active .check-icon {
  color: var(--chat-accent);
}

.role-description {
  font-size: 12px;
  color: var(--chat-text-secondary);
  line-height: 1.4;
  margin-top: 2px;
}

.role-option.active .role-description {
  color: rgba(255, 255, 255, 0.8);
}

.no-roles {
  padding: 20px;
  text-align: center;
  color: var(--chat-text-secondary);
  font-size: 13px;
}

.role-actions {
  padding: 8px;
  border-top: 1px solid var(--chat-border);
  margin-top: 4px;
}

.clear-all-btn {
  width: 100%;
  padding: 6px 12px;
  background: transparent;
  border: 1px solid var(--chat-border);
  border-radius: 4px;
  color: var(--chat-text-secondary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-all-btn:hover {
  background: var(--chat-bg-secondary);
  color: var(--chat-text-primary);
  border-color: var(--chat-accent);
}


</style>