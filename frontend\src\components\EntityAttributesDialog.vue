<template>
  <el-dialog
      v-model="dialogVisible"
      :title="entity?.name || '角色属性'"
      class="entity-attributes-dialog"
      fullscreen
      destroy-on-close
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      :lock-scroll="true"
      @close="handleClose"
      append-to-body
  >
    <div class="dialog-content-wrapper">
      <!-- 重新设计的顶部标题栏 -->
      <div class="dialog-header">
        <div class="header-background"></div>
        <div class="header-content">
          <div class="entity-info">
            <div class="entity-avatar" v-if="entity" :style="{ background: getEntityColor(entity) }">
              {{ entity.name?.substring(0, 1) || '?' }}
            </div>
            <div class="entity-details">
              <h1 class="entity-name">{{ entity?.name || '未知角色' }}</h1>
              <div class="entity-meta">
                <span class="entity-template" v-if="templateName">
                  <el-icon><Document /></el-icon>
                  {{ templateName }}
                </span>
                <span class="entity-stats" v-if="entity">
                  <el-icon><DataLine /></el-icon>
                  {{ Object.keys(entity.dimensions || {}).length }} 个属性
                </span>
              </div>
            </div>
          </div>

          <!-- 重新设计的操作按钮 -->
          <div class="header-actions">
            <el-button
              @click="showExportModal"
              type="primary"
              class="action-btn export-btn"
              size="large"
            >
              <el-icon><Download /></el-icon>
              <span>导出卡片</span>
            </el-button>
            <el-button
              @click="handleButtonClick"
              class="action-btn close-btn"
              size="large"
            >
              <el-icon><Close /></el-icon>
              <span>关闭</span>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 重新设计的主要内容区域 - 上下布局 -->
      <div class="entity-content" v-if="entity">
        <!-- 基本信息卡片 -->
        <div class="info-section basic-info-section">

          <div class="section-content">
            <div class="basic-info-grid">
              
              <div class="info-item description-item" v-if="entity.description">
                <div class="info-label">
                  <el-icon><Document /></el-icon>
                  <span>角色描述</span>
                </div>
                <div class="info-value description-value">
                  <div class="description-content">{{ entity.description }}</div>
                </div>
              </div>
              <div class="info-item empty-item" v-if="!entity.description">
                <div class="empty-description">
                  <el-icon><Document /></el-icon>
                  <span>暂无描述信息</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 属性维度卡片 -->
        <div class="info-section dimensions-section" v-if="hasDimensions">
          <div class="section-header">
            <div class="section-title">
              <el-icon class="title-icon"><DataLine /></el-icon>
              <h3>属性维度</h3>
              <div class="item-count">{{ Object.keys(entity.dimensions || {}).length }} 项</div>
            </div>
          </div>
          <div class="section-content">
            <div class="dimensions-grid">
              <div v-for="(value, key) in entity.dimensions" :key="key" class="dimension-card">
                <div class="dimension-header">
                  <div class="dimension-label">{{ formatDimensionKey(key) }}</div>
                </div>
                <div class="dimension-content" @dblclick="showDimensionDetail(key, value)" :class="{ 'clickable': isLongText(value) }">
                  <div class="dimension-value" :class="getDimensionValueClass(value)">{{ value }}</div>
                  <div v-if="isLongText(value)" class="double-click-hint">双击查看完整内容</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 关系数据卡片 -->
        <div class="info-section relations-section">
          <div class="section-header">
            <div class="section-title">
              <el-icon class="title-icon"><Connection /></el-icon>
              <h3>相关关系</h3>
              <div class="item-count">{{ entityRelations.length }} 项</div>
            </div>
          </div>
          <div class="section-content">
            <div class="relations-list" v-if="hasRelations">
              <div v-for="relation in entityRelations" :key="relation.id" class="relation-item">
                <div class="relation-main">
                  <div class="relation-direction">
                    <el-icon class="direction-icon">
                      <component :is="relation.source === entity.id ? 'ArrowRight' : 'ArrowLeft'" />
                    </el-icon>
                  </div>
                  <div class="relation-info">
                    <div class="relation-entity-name">
                      {{ getEntityNameById(relation.source === entity.id
                        ? relation.target
                        : relation.source) }}
                    </div>
                    <div class="relation-meta">
                      <el-tag class="relation-type-tag" :type="getRelationTypeColor(relation.type)">
                        {{ relation.type }}
                      </el-tag>
                      <el-tag v-if="relation.bidirectional" size="small" type="info" class="bidirectional-tag">
                        双向
                      </el-tag>
                    </div>
                  </div>
                </div>
                <div class="relation-strength" v-if="relation.strength">
                  <div class="strength-info">
                    <span class="strength-label">关系强度</span>
                    <span class="strength-text">{{ relation.strength }}/5</span>
                  </div>
                  <div class="strength-bar">
                    <div class="strength-fill" :style="{width: `${relation.strength * 20}%`}"></div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="empty-relations">
              <el-icon class="empty-icon"><Connection /></el-icon>
              <div class="empty-text">该角色暂无关系数据</div>
              <div class="empty-hint">建立关系可以丰富角色的背景故事</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-else>
        <el-empty description="未找到角色数据" :image-size="120"></el-empty>
      </div>
    </div>

    <!-- 维度详情对话框 -->
    <div v-if="dimensionDetailVisible" class="custom-modal-overlay" @click.self="closeDimensionDetail">
      <div class="dimension-detail-modal">
        <div class="dimension-detail-header">
          <h3>{{ currentDimension.key }}</h3>
          <button
            type="button"
            class="close-button"
            @click="closeDimensionDetail"
          >
            ×
          </button>
        </div>
        <div class="dimension-detail-body">
          <div class="dimension-detail-content">{{ currentDimension.value }}</div>
        </div>
      </div>
    </div>

    <!-- 导出对话框 - 完全独立实现，不依赖Element Plus对话框 -->
    <div v-if="exportDialogVisible" class="custom-modal-overlay" @click.self="closeExportDialog">
      <div class="custom-modal">
        <div class="custom-modal-header">
          <h3>导出角色卡片</h3>
          <button
            type="button"
            class="close-button"
            @click.stop="closeExportDialog"
            style="position: relative; z-index: 999; cursor: pointer;"
          >
            ×
          </button>
        </div>
        <div class="custom-modal-body">
          <entity-card-exporter
            :entity="entity"
            :entity-relations="entityRelations"
            :template-name="templateName"
            :get-entity-name-by-id="getEntityNameById"
            @export-success="handleExportSuccess"
          />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick, h, onMounted, onBeforeUnmount } from 'vue';
import { useRelationshipStore } from '@/stores/relationship';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Download, Close, Document, DataLine, User, EditPen, Connection, ArrowRight, ArrowLeft } from '@element-plus/icons-vue';
import html2canvas from 'html2canvas';
import EntityCardExporter from './EntityCardExporter.vue';

// 父组件传入的属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  entity: {
    type: Object,
    default: null
  },
  entityList: {
    type: Array,
    default: () => []
  },
  templates: {
    type: Array,
    default: () => []
  },
  relations: {
    type: Array,
    default: () => []
  }
});

// 弹窗状态的本地引用
const dialogVisible = ref(props.modelValue);

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    console.log('EntityAttributesDialog: modelValue prop changed to', newVal);
    dialogVisible.value = newVal;
  }
);

// 监听dialogVisible变化，向父组件发送更新
watch(
  () => dialogVisible.value,
  (newVal) => {
    console.log('EntityAttributesDialog: dialogVisible changed to', newVal);
    // 无论新值是什么，都发送更新事件给父组件
    emit('update:modelValue', newVal);
  }
);

// 定义事件
const emit = defineEmits(['update:modelValue']);

// 获取关系数据
const relationshipStore = useRelationshipStore();

// 修改关系数据获取逻辑
const entityRelations = computed(() => {
  if (!props.entity || !props.entity.id) return [];
  
  // 从传入的所有关系中过滤出与当前实体相关的关系
  return props.relations.filter(relation => 
    relation.source === props.entity.id || relation.target === props.entity.id
  );
});

// 确保关系数据存在的判断
const hasRelations = computed(() => entityRelations.value.length > 0);

// 计算是否有维度数据
const hasDimensions = computed(() => {
  return props.entity && 
         props.entity.dimensions && 
         Object.keys(props.entity.dimensions).length > 0;
});

// 获取模板名称
const templateName = computed(() => {
  if (!props.entity || !props.entity.template_id) return '';
  const template = props.templates.find(t => t.id === props.entity.template_id);
  return template ? template.name : '';
});

// 根据实体ID获取实体名称
const getEntityNameById = (id) => {
  const entity = props.entityList.find(e => e.id === id);
  return entity ? entity.name : '未知角色';
};

// 获取关系类型对应的颜色
const getRelationTypeColor = (type) => {
  const colorMap = {
    '朋友': 'success',
    '敌人': 'danger',
    '恋人': 'warning',
    '家人': 'primary',
    '同事': 'info',
    '师生': 'primary',
    '上下级': 'warning',
    '竞争对手': 'danger',
    '合作伙伴': 'success',
    '陌生人': 'info'
  };
  return colorMap[type] || 'info';
};

// 检测是否为长文本
const isLongText = (text) => {
  if (!text) return false;
  return text.length > 100 || text.split('\n').length > 3;
};

// 获取维度值的样式类
const getDimensionValueClass = (value) => {
  if (!value) return '';

  const classes = [];

  // 检测长文本
  if (isLongText(value)) {
    classes.push('long-text');
  }

  // 检测数字类型
  if (/^\d+(\.\d+)?$/.test(value.toString().trim())) {
    classes.push('number-value');
  }

  // 检测短文本
  else if (value.toString().length <= 20 && !value.toString().includes('\n')) {
    classes.push('short-value');
  }

  return classes.join(' ');
};

// 格式化维度键名，将下划线替换为空格，首字母大写
const formatDimensionKey = (key) => {
  return key
    .replace(/_/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;
  
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 根据实体生成角色颜色
const getEntityColor = (entity) => {
  if (!entity) return '#ccc';
  
  // 简单的字符串转颜色算法
  const str = entity.name || entity.id || '';
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  const hue = hash % 360;
  return `hsl(${hue}, 70%, 45%)`;
};

// 导出卡片相关
const exportDialogVisible = ref(false);

// 维度详情对话框相关
const dimensionDetailVisible = ref(false);
const currentDimension = ref({ key: '', value: '' });

// 修改 showExportModal 方法
const showExportModal = () => {
  if (!props.entity) {
    ElMessage.warning('没有角色数据可供导出');
    return;
  }
  exportDialogVisible.value = true;
  
  // 不对整个body添加modal-open类，而是对当前对话框的容器添加类
  nextTick(() => {
    const modalContainer = document.querySelector('.custom-modal-overlay');
    if (modalContainer) {
      modalContainer.classList.add('modal-active');
    }
  });
};

// 修改 closeExportDialog 方法，确保它只关闭导出弹窗而不影响主弹窗
const closeExportDialog = () => {
  console.log('仅关闭导出对话框，不影响主属性对话框');
  exportDialogVisible.value = false;
  
  // 确保只移除导出对话框相关的类
  const modalContainer = document.querySelector('.custom-modal-overlay');
  if (modalContainer) {
    modalContainer.classList.remove('modal-active');
  }
};



// 处理导出成功后的操作
const handleExportSuccess = (filePath) => {
  console.log('导出成功，文件路径:', filePath);
  // 只关闭导出对话框，不影响主对话框
  closeExportDialog();
};

// 简化的关闭方法
const handleClose = () => {
  console.log('handleClose: 关闭主对话框');

  // 直接关闭主对话框，不检查其他对话框状态
  dialogVisible.value = false;
  emit('update:modelValue', false);
};

// 监听导出对话框状态变化
watch(exportDialogVisible, (newVal) => {
  // 仅处理导出对话框的状态
  if (!newVal) {
    const modalContainer = document.querySelector('.custom-modal-overlay');
    if (modalContainer) {
      modalContainer.classList.remove('modal-active');
    }
  }
});

// 组件挂载时添加键盘事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

// 监听对话框可见性，管理键盘事件
watch(
  () => dialogVisible.value,
  (newVal) => {
    if (newVal) {
      document.addEventListener('keydown', handleKeydown);
    } else {
      document.removeEventListener('keydown', handleKeydown);
    }
  }
);

// 在组件卸载时清理资源
onBeforeUnmount(() => {
  console.log('EntityAttributesDialog: 组件卸载，清理资源');

  // 移除键盘事件监听器
  document.removeEventListener('keydown', handleKeydown);

  // 确保组件销毁时清理所有状态
  exportDialogVisible.value = false;
  dimensionDetailVisible.value = false;

  // 清除所有可能的modal overlay
  const modalOverlays = document.querySelectorAll('.custom-modal-overlay');
  modalOverlays.forEach(overlay => {
    if (document.body.contains(overlay)) {
      document.body.removeChild(overlay);
    }
  });
});

// 显示维度详情
const showDimensionDetail = (key, value) => {
  if (!isLongText(value)) return; // 只有长文本才显示详情

  currentDimension.value = {
    key: formatDimensionKey(key),
    value: value
  };
  dimensionDetailVisible.value = true;
};

// 关闭维度详情对话框
const closeDimensionDetail = () => {
  dimensionDetailVisible.value = false;
  currentDimension.value = { key: '', value: '' };
};

// ESC键事件处理
const handleKeydown = (event) => {
  if (event.key === 'Escape') {
    event.preventDefault();
    event.stopPropagation();

    // 如果维度详情对话框打开，优先关闭它
    if (dimensionDetailVisible.value) {
      closeDimensionDetail();
      return;
    }

    // 如果导出对话框打开，关闭它
    if (exportDialogVisible.value) {
      closeExportDialog();
      return;
    }

    // 否则关闭主对话框
    handleClose();
  }
};

// 关闭按钮的点击事件处理
const handleButtonClick = () => {
  console.log('关闭按钮被点击');

  // 如果有子对话框打开，先关闭子对话框
  if (dimensionDetailVisible.value) {
    closeDimensionDetail();
    return;
  }

  if (exportDialogVisible.value) {
    closeExportDialog();
    return;
  }

  // 否则关闭主对话框
  handleClose();
};
</script>

<style lang="scss" scoped>
.entity-attributes-dialog {
  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    background: var(--bg-color);
  }
  
  :deep(.el-dialog__header) {
    display: none;
  }
  
  :deep(.el-dialog__body) {
    flex: 1;
    padding: 0;
    margin: 0;
    height: calc(100vh - 60px);
    overflow: hidden;
  }
}

.dialog-content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-color);
}

// 重新设计的顶部标题栏
.dialog-header {
  position: relative;
  background: var(--bg-color-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 0;
  flex-shrink: 0;
  overflow: hidden;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      var(--color-primary)10 0%,
      transparent 50%,
      var(--color-primary)05 100%);
    opacity: 0.3;
  }

  .header-content {
    position: relative;
    padding: 24px 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .entity-info {
      display: flex;
      align-items: center;
      gap: 20px;

      .entity-avatar {
        width: 72px;
        height: 72px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 28px;
        font-weight: bold;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        border: 3px solid rgba(255, 255, 255, 0.2);
        position: relative;

        &::before {
          content: '';
          position: absolute;
          inset: -3px;
          border-radius: 50%;
          background: linear-gradient(45deg, var(--color-primary), transparent);
          z-index: -1;
        }
      }

      .entity-details {
        .entity-name {
          margin: 0 0 8px 0;
          font-size: 32px;
          font-weight: 700;
          color: var(--text-color);
          line-height: 1.2;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .entity-meta {
          display: flex;
          gap: 16px;
          align-items: center;

          .entity-template,
          .entity-stats {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: var(--text-color-secondary);
            background: var(--bg-color);
            padding: 6px 12px;
            border-radius: 20px;
            border: 1px solid var(--border-color);

            .el-icon {
              font-size: 16px;
            }
          }
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .action-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        border-radius: 10px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 2px solid transparent;

        .el-icon {
          font-size: 16px;
        }

        &.export-btn {
          background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
          color: white;
          box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(103, 194, 58, 0.4);
            background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
          }
        }

        &.close-btn {
          background: var(--bg-color);
          color: var(--text-color);
          border-color: var(--border-color);

          &:hover {
            background: #f56c6c;
            color: white;
            border-color: #f56c6c;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
          }
        }
      }
    }
  }
}

// 重新设计的内容区域 - 上下布局
.entity-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px 32px;
  background: var(--bg-color);

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--bg-color-secondary);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;

    &:hover {
      background: var(--text-color-secondary);
    }
  }

  .info-section {
    background: var(--card-bg);
    border-radius: 16px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 24px;
    overflow: hidden;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 区块头部样式
.section-header {
  background: linear-gradient(135deg, var(--card-bg) 0%, var(--bg-color-secondary) 100%);
  padding: 20px 28px;
  border-bottom: 1px solid var(--border-color);

  .section-title {
    display: flex;
    align-items: center;
    gap: 12px;

    .title-icon {
      font-size: 22px;
      color: var(--color-primary);
    }

    h3 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: var(--text-color);
      flex: 1;
    }

    .item-count {
      background: var(--color-primary);
      color: white;
      padding: 6px 14px;
      border-radius: 14px;
      font-size: 13px;
      font-weight: 600;
    }
  }
}

// 区块内容样式
.section-content {
  padding: 28px;
}
  
// 基本信息区块样式
.basic-info-section {
  .basic-info-grid {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .info-item {
      .info-label {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 20px;
        font-weight: 600;
        color: var(--text-color-secondary);
        margin-bottom: 12px;

        .el-icon {
          font-size: 18px;
          color: var(--color-primary);
        }
      }

      .info-value {
        &.name-value {
          font-size: 24px;
          font-weight: 700;
          color: var(--text-color);
          background: linear-gradient(135deg, var(--color-primary), var(--color-success));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        &.description-value {
          .description-content {
            background: linear-gradient(135deg,
              var(--bg-color-secondary) 0%,
              rgba(64, 158, 255, 0.03) 50%,
              rgba(103, 194, 58, 0.03) 100%);
            padding: 24px;
            border-radius: 16px;
            line-height: 1.8;
            white-space: pre-line;
            font-size: 19px;
            color: var(--text-color);
            border-left: 5px solid var(--primary-color);
            box-shadow:
              inset 0 1px 3px rgba(0, 0, 0, 0.05),
              0 2px 8px rgba(64, 158, 255, 0.1);
            position: relative;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
            transition: all 0.3s ease;

            // 添加装饰性引用符号
            &::before {
              content: '❝';
              position: absolute;
              top: 8px;
              left: 12px;
              font-size: 24px;
              color: var(--primary-color);
              opacity: 0.3;
              line-height: 1;
            }

            &::after {
              content: '❞';
              position: absolute;
              bottom: 8px;
              right: 12px;
              font-size: 24px;
              color: var(--success-color);
              opacity: 0.3;
              line-height: 1;
            }

            // 悬停效果
            &:hover {
              transform: translateY(-2px);
              box-shadow:
                inset 0 1px 3px rgba(0, 0, 0, 0.05),
                0 4px 16px rgba(64, 158, 255, 0.15);
              border-left-color: var(--success-color);

              &::before, &::after {
                opacity: 0.5;
                transform: scale(1.1);
              }
            }

            // 添加顶部装饰条
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 2px;
              background: linear-gradient(90deg, var(--primary-color), var(--success-color));
              border-radius: 16px 16px 0 0;
              opacity: 0.6;
            }
          }
        }
      }

      .empty-description {
        display: flex;
        align-items: center;
        gap: 10px;
        color: var(--text-color-secondary);
        font-style: italic;
        opacity: 0.7;
        font-size: 16px;
        padding: 20px;
        background: var(--bg-color-secondary);
        border-radius: 12px;
        border: 2px dashed var(--border-color);
      }
    }
  }
}

// 维度区块样式 - 美化的网格方框布局
.dimensions-section {
  .dimensions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;

    .dimension-card {
      background: var(--card-bg);
      border-radius: 16px;
      border: 1px solid var(--border-color);
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      position: relative;

      // 定义主题适配的颜色变量
      --primary-color: #409eff;
      --success-color: #67c23a;
      --warning-color: #e6a23c;
      --danger-color: #f56c6c;

      // 深色主题适配
      .dark & {
        --primary-color: #409eff;
        --success-color: #67c23a;
        --warning-color: #e6a23c;
        --danger-color: #f56c6c;
      }

      // 添加微妙的背景纹理
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 20% 80%, rgba(64, 158, 255, 0.03) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(103, 194, 58, 0.03) 0%, transparent 50%);
        pointer-events: none;
        z-index: 1;
      }

      &:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        border-color: var(--primary-color);

        .dimension-header {
          background: linear-gradient(135deg, rgba(64, 158, 255, 0.15), rgba(103, 194, 58, 0.08));

          .dimension-label::before {
            width: 4px;
            height: 20px;
            background: linear-gradient(45deg, var(--primary-color), var(--success-color));
          }
        }

        .dimension-content {
          background: linear-gradient(135deg, var(--card-bg) 0%, rgba(64, 158, 255, 0.02) 100%);
        }
      }

      .dimension-header {
        background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), var(--bg-color-secondary));
        padding: 16px 20px;
        border-bottom: 1px solid var(--border-color);
        position: relative;
        z-index: 2;

        // 添加顶部装饰条
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: linear-gradient(90deg, var(--primary-color), var(--success-color));
          opacity: 0.8;
        }

        .dimension-label {
          font-size: 20px;
          font-weight: 700;
          color: var(--text-color);
          display: flex;
          align-items: center;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

          &::before {
            content: '';
            width: 4px;
            height: 18px;
            background: linear-gradient(45deg, var(--primary-color), var(--success-color));
            border-radius: 2px;
            margin-right: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          }

          // 添加属性类型图标效果
          &::after {
            content: '●';
            color: var(--primary-color);
            font-size: 8px;
            margin-left: 8px;
            opacity: 0.6;
          }
        }
      }

      .dimension-content {
        padding: 20px;
        background: var(--card-bg);
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;

        &.clickable {
          cursor: pointer;

          &:hover {
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.08), rgba(103, 194, 58, 0.08));

            .double-click-hint {
              opacity: 1;
              transform: translateY(0);
            }
          }
        }

        .double-click-hint {
          position: absolute;
          bottom: 8px;
          right: 12px;
          font-size: 12px;
          color: var(--color-primary);
          background: rgba(64, 158, 255, 0.1);
          padding: 4px 8px;
          border-radius: 12px;
          opacity: 0;
          transform: translateY(10px);
          transition: all 0.3s ease;
          pointer-events: none;
          font-weight: 500;
        }

        .dimension-value {
          font-size: 18px;
          line-height: 1.8;
          color: var(--text-color);
          white-space: pre-line;
          min-height: 80px;
          display: flex;
          align-items: flex-start;
          position: relative;
          font-weight: 500;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);

          // 添加微妙的背景渐变
          background: linear-gradient(135deg,
            rgba(64, 158, 255, 0.02) 0%,
            rgba(103, 194, 58, 0.02) 50%,
            rgba(230, 162, 60, 0.02) 100%);
          padding: 4px 8px;
          border-radius: 8px;
          border-left: 3px solid transparent;
          transition: all 0.3s ease;

          // 悬停效果
          &:hover {
            background: linear-gradient(135deg,
              rgba(64, 158, 255, 0.05) 0%,
              rgba(103, 194, 58, 0.05) 50%,
              rgba(230, 162, 60, 0.05) 100%);
            border-left-color: var(--primary-color);
            transform: translateX(2px);
          }

          // 添加引用样式的装饰
          &::before {
            content: '"';
            position: absolute;
            top: -8px;
            left: -12px;
            font-size: 28px;
            color: var(--primary-color);
            opacity: 0.25;
            font-family: "Times New Roman", serif;
            line-height: 1;
            font-weight: bold;
          }

          &::after {
            content: '"';
            position: absolute;
            bottom: -15px;
            right: -12px;
            font-size: 28px;
            color: var(--success-color);
            opacity: 0.25;
            font-family: "Times New Roman", serif;
            line-height: 1;
            font-weight: bold;
          }

          // 为长文本添加渐变遮罩效果
          &.long-text {
            max-height: 140px;
            overflow: hidden;

            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 30px;
              background: linear-gradient(transparent, var(--card-bg));
              pointer-events: none;
              z-index: 2;
            }
          }

          // 数字类型的特殊样式
          &.number-value {
            font-size: 20px;
            font-weight: 700;
            color: var(--primary-color);
            text-align: center;
            justify-content: center;
            align-items: center;
            background: radial-gradient(circle, rgba(64, 158, 255, 0.1), transparent);

            &::before, &::after {
              display: none;
            }
          }

          // 短文本的特殊样式
          &.short-value {
            font-size: 17px;
            font-weight: 600;
            color: var(--success-color);

            &::before, &::after {
              opacity: 0.15;
            }
          }
        }

        // 添加底部装饰线
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 20px;
          right: 20px;
          height: 2px;
          background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.2), transparent);
        }
      }
    }
  }
}

// 关系区块样式
.relations-section {
  .relations-list {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .relation-item {
      background: var(--bg-color-secondary);
      border-radius: 12px;
      border: 1px solid var(--border-color);
      padding: 20px;
      transition: all 0.2s ease;

      &:hover {
        transform: translateX(4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        border-color: var(--color-primary);
      }

      .relation-main {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 16px;

        .relation-direction {
          .direction-icon {
            font-size: 20px;
            color: var(--color-primary);
            background: var(--color-primary)10;
            padding: 8px;
            border-radius: 50%;
          }
        }

        .relation-info {
          flex: 1;

          .relation-entity-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 8px;
          }

          .relation-meta {
            display: flex;
            gap: 8px;

            .relation-type-tag {
              font-size: 13px;
              font-weight: 500;
            }

            .bidirectional-tag {
              font-size: 12px;
            }
          }
        }
      }

      .relation-strength {
        .strength-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .strength-label {
            font-size: 14px;
            color: var(--text-color-secondary);
            font-weight: 500;
          }

          .strength-text {
            font-size: 14px;
            color: var(--text-color);
            font-weight: 600;
          }
        }

        .strength-bar {
          height: 8px;
          background: var(--border-color);
          border-radius: 4px;
          overflow: hidden;

          .strength-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), var(--color-success));
            border-radius: 4px;
            transition: width 0.3s ease;
          }
        }
      }
    }
  }

  .empty-relations {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-color-secondary);

    .empty-icon {
      font-size: 64px;
      margin-bottom: 20px;
      opacity: 0.4;
    }

    .empty-text {
      font-size: 18px;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .empty-hint {
      font-size: 15px;
      opacity: 0.7;
    }
  }
}

.dimensions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  
  .dimension-item {
    .dimension-label {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-color-secondary);
      margin-bottom: 6px;
    }
    
    .dimension-value {
      font-size: 16px;
      padding: 8px 12px;
      background: rgba(0, 0, 0, 0.03);
      border-radius: 6px;
      min-height: 24px;
      white-space: pre-wrap;
      word-break: break-word;
      line-height: 1.5;
    }
  }
}

.relations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  .relation-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    gap: 16px;
    
    &:hover {
      background: rgba(0, 0, 0, 0.04);
    }
    
    .relation-direction {
      font-size: 20px;
      font-weight: bold;
      width: 24px;
      text-align: center;
    }
    
    .relation-entity {
      flex: 1;
      font-weight: 500;
    }
    
    .relation-type {
      padding: 4px 12px;
      background: var(--primary-light);
      border-radius: 20px;
      font-size: 14px;
      min-width: 80px;
      text-align: center;
    }
    
    .relation-bidirectional {
      margin-left: 10px;
    }
    
    .relation-strength {
      width: 100px;
      
      .strength-bar {
        height: 8px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        overflow: hidden;
        
        .strength-value {
          height: 100%;
          background: var(--primary-color);
          border-radius: 4px;
        }
      }
    }
  }
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 暗黑模式适配 */
html.dark {
  .attribute-card {
    background: var(--panel-bg);
  }
  
  .info-value.description-content,
  .dimension-value {
    background: rgba(255, 255, 255, 0.05);
  }
  
  .relations-list .relation-item {
    background: rgba(255, 255, 255, 0.03);
    
    &:hover {
      background: rgba(255, 255, 255, 0.06);
    }
    
    .relation-strength .strength-bar {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

// 添加预览对话框样式
:deep(.preview-dialog) {
  .el-message-box__content {
    padding: 20px;
    max-height: 80vh;
    overflow-y: auto;
  }
  
  .el-message-box__container {
    margin: 0;
  }
  
  .el-message-box__message {
    padding: 0;
  }
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
}

.export-btn {
  height: 36px;
  padding: 0 16px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }
  
  &:active {
    transform: translateY(1px);
  }
  
  &.is-loading {
    pointer-events: none;
  }
}

.close-btn {
  height: 36px;
  padding: 0 16px;
  font-size: 14px;
  transition: all 0.2s ease;
  border-color: rgba(245, 108, 108, 0.7);
  
  &:hover {
    transform: translateY(-2px);
    background-color: rgba(245, 108, 108, 0.1);
    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.2);
  }
  
  &:active {
    transform: translateY(1px);
  }
}

/* 维度详情对话框样式 */
.dimension-detail-modal {
  width: 800px;
  max-width: 90vw;
  max-height: 80vh;
  background: var(--bg-color, #ffffff);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid var(--border-color, #e4e7ed);
}

.dimension-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--bg-color-secondary, #f5f7fa);
  border-bottom: 1px solid var(--border-color, #e4e7ed);
  flex-shrink: 0;

  h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color, #303133);
  }
}

.dimension-detail-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  min-height: 200px;
  max-height: 60vh;
  background: var(--bg-color, #ffffff);

  &::-webkit-scrollbar {
    width: 12px;
  }

  &::-webkit-scrollbar-track {
    background: var(--bg-color-secondary, #f5f7fa);
    border-radius: 6px;
    margin: 4px 0;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #c0c4cc, #909399);
    border-radius: 6px;
    border: 2px solid var(--bg-color, #ffffff);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(180deg, #909399, #606266);
      transform: scaleX(1.2);
    }

    &:active {
      background: linear-gradient(180deg, #606266, #409eff);
    }
  }

  &::-webkit-scrollbar-corner {
    background: var(--bg-color, #ffffff);
  }
}

.dimension-detail-content {
  font-size: 16px;
  line-height: 1.8;
  color: var(--text-color, #303133);
  white-space: pre-line;
  word-break: break-word;
  background: var(--bg-color-secondary, #f5f7fa);
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid var(--color-primary, #409eff);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 自定义导出对话框样式 */
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.custom-modal {
  width: 1000px;
  max-width: 95vw;
  height: 90vh; /* 固定高度，不是max-height */
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 确保模态框本身不滚动 */
}

.custom-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid;
  flex-shrink: 0; /* 头部不收缩 */

  h3 {
    margin: 0;
    font-size: 18px;
  }
}

.custom-modal-body {
  flex: 1;
  overflow: hidden; /* 让内部的EntityCardExporter组件处理滚动 */
  min-height: 0; /* 重要：允许flex子项收缩 */

  /* 确保EntityCardExporter组件占满整个body区域 */
  :deep(.entity-card-exporter) {
    height: 100%;
  }
}

.close-button {
  background: transparent;
  border: none;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: relative;
  z-index: 10;
  color: #999; /* 默认颜色，防止看不见 */
}

.close-button:hover {
  transform: rotate(90deg);
  background-color: rgba(0, 0, 0, 0.1); /* 默认悬停背景 */
}

/* 动画定义 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes zoomIn {
  from { 
    opacity: 0; 
    transform: scale(0.95);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

/* 去除可能影响其他对话框的全局样式 */
.custom-modal-overlay,
.custom-modal-container,
.custom-modal-body {
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }
}

// 按钮图标样式
.icon {
  margin-right: 6px;
  vertical-align: middle;
}

// 浅色主题适配
.light {
  .custom-modal {
    background-color: #ffffff !important;
    border: 1px solid #e4e7ed !important;

    .custom-modal-header {
      background-color: #ffffff !important;
      border-bottom-color: #e4e7ed !important;

      h3 {
        color: #303133 !important;
      }
    }

    .custom-modal-body {
      background-color: #ffffff !important;
      color: #303133 !important;
    }
  }

  // 单独定义关闭按钮样式，提高优先级
  .custom-modal .close-button {
    color: #909399 !important;

    &:hover {
      background-color: rgba(0, 0, 0, 0.1) !important;
      color: #303133 !important;
    }
  }
}

// 深色主题适配
.dark {
  .dimension-detail-modal {
    background: #1a1a1a !important;
    border-color: #333333 !important;

    .dimension-detail-header {
      background: #2a2a2a !important;
      border-bottom-color: #333333 !important;

      h3 {
        color: #e0e0e0 !important;
      }
    }

    .dimension-detail-body {
      background: #1a1a1a !important;

      &::-webkit-scrollbar-track {
        background: #2a2a2a !important;
      }

      &::-webkit-scrollbar-thumb {
        background: linear-gradient(180deg, #4a4a4a, #606266) !important;
        border-color: #1a1a1a !important;

        &:hover {
          background: linear-gradient(180deg, #606266, #409eff) !important;
        }

        &:active {
          background: linear-gradient(180deg, #409eff, #67c23a) !important;
        }
      }

      &::-webkit-scrollbar-corner {
        background: #1a1a1a !important;
      }

      .dimension-detail-content {
        background: #2a2a2a !important;
        color: #e0e0e0 !important;
        border-left-color: #409eff !important;
      }
    }

    .close-button {
      color: #b0b0b0 !important;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
        color: #e0e0e0 !important;
      }
    }
  }

  .custom-modal {
    background-color: #1a1a1a !important;
    border: 1px solid #333333 !important;

    .custom-modal-header {
      background-color: #1a1a1a !important;
      border-bottom-color: #333333 !important;

      h3 {
        color: #e0e0e0 !important;
      }
    }

    .custom-modal-body {
      background-color: #1a1a1a !important;
      color: #e0e0e0 !important;
    }
  }

  // 单独定义关闭按钮样式，提高优先级
  .custom-modal .close-button {
    color: #b0b0b0 !important;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
      color: #e0e0e0 !important;
    }
  }
}
</style>