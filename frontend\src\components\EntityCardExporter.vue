<template>
  <div class="entity-card-exporter">
    <!-- 主要内容区域 -->
    <div class="exporter-main">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <!-- 主题选择区域 -->
        <div class="config-section">
          <div class="section-header">
            <h3 class="section-title">主题风格</h3>
            <el-button
              size="small"
              type="primary"
              text
              @click="showThemeManager = true"
            >
              管理主题
            </el-button>
          </div>

          <div class="theme-grid">
            <div
              v-for="theme in availableThemes"
              :key="theme.id"
              class="theme-card"
              :class="{ active: currentTheme === theme.id }"
              @click="currentTheme = theme.id"
            >
              <div class="theme-preview" :style="{ background: theme.colors.background }">
                <!-- 背景图案 -->
                <div
                  v-if="theme.backgroundPattern"
                  class="theme-pattern"
                  :style="{ opacity: theme.backgroundPattern.opacity || 0.1 }"
                  v-html="theme.backgroundPattern.svg"
                />
                <div class="preview-avatar" :style="{ background: theme.colors.avatarBackground }">
                  {{ entity?.name?.substring(0, 1) || 'A' }}
                </div>
                <div class="preview-card" :style="{ background: theme.colors.cardBackground }" />
              </div>
              <div class="theme-label">{{ theme.name }}</div>
            </div>
          </div>
        </div>

        <!-- 导出选项区域 -->
        <div class="config-section">
          <div class="section-header">
            <h3 class="section-title">导出选项</h3>
          </div>

          <div class="option-group">
            <el-checkbox v-model="includeRelations" size="large">
              包含角色关系
            </el-checkbox>
            <el-checkbox v-model="includeDimensions" size="large">
              包含属性维度
            </el-checkbox>
          </div>

          <div class="quality-option">
            <label class="option-label">图片质量</label>
            <el-select v-model="imageQuality" size="large">
              <el-option label="标准" value="1" />
              <el-option label="高清" value="2" />
              <el-option label="超清" value="3" />
            </el-select>
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-section">
          <el-button
            type="primary"
            size="large"
            :loading="generatingPreview"
            :disabled="!entity"
            @click="generatePreview"
            class="generate-button"
          >
            {{ generatingPreview ? '生成中...' : (previewUrl ? '重新生成' : '生成预览') }}
          </el-button>

          <el-button
            v-if="previewUrl"
            type="success"
            size="large"
            :loading="exporting"
            @click="exportCard"
            class="export-button"
          >
            {{ exporting ? '导出中...' : '导出卡片' }}
          </el-button>
        </div>
      </div>

      <!-- 右侧预览区域 -->
      <div class="preview-panel">
        <div class="preview-header">
          <h3 class="preview-title">预览效果</h3>
          <div class="preview-info" v-if="entity">
            {{ entity.name || '未命名角色' }}
          </div>
        </div>

        <div class="preview-content" ref="previewContent">
          <div v-if="previewUrl" class="preview-wrapper">
            <img :src="previewUrl" alt="卡片预览" class="preview-image" />
          </div>
          <div v-else class="preview-empty">
            <div class="empty-icon">
              <svg viewBox="0 0 24 24" width="48" height="48">
                <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
              </svg>
            </div>
            <p class="empty-text">点击"生成预览"按钮创建角色卡片</p>
            <p class="empty-hint">选择主题风格和导出选项后开始生成</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 主题管理对话框 -->
    <el-dialog
      v-model="showThemeManager"
      title="主题管理"
      width="700px"
      :close-on-click-modal="false"
      class="theme-dialog"
    >
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="添加主题" name="add">
          <div class="add-theme-form">
            <el-form :model="newTheme" label-width="100px" size="large">
              <el-form-item label="主题名称" required>
                <el-input v-model="newTheme.name" placeholder="请输入主题名称" />
              </el-form-item>
              <el-form-item label="主题ID" required>
                <el-input v-model="newTheme.id" placeholder="请输入主题ID（英文）" />
              </el-form-item>
              <el-form-item label="主题配置" required>
                <el-input
                  v-model="newTheme.json"
                  type="textarea"
                  :rows="12"
                  placeholder="请粘贴主题JSON配置..."
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="addTheme">添加主题</el-button>
                <el-button @click="resetNewTheme">重置</el-button>
                <el-button type="info" @click="showJsonHelp">格式说明</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="管理主题" name="manage">
          <div class="manage-themes">
            <div v-for="theme in customThemes" :key="theme.id" class="theme-manage-item">
              <div class="theme-info">
                <h4 class="theme-name">{{ theme.name }}</h4>
                <p class="theme-id">ID: {{ theme.id }}</p>
              </div>
              <div class="theme-actions">
                <el-button size="small" @click="editTheme(theme)">编辑</el-button>
                <el-button size="small" type="danger" @click="deleteTheme(theme.id)">删除</el-button>
              </div>
            </div>
            <el-empty v-if="customThemes.length === 0" description="暂无自定义主题" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import html2canvas from 'html2canvas';

import { useConfigStore } from '@/stores/config';

const props = defineProps({
  entity: {
    type: Object,
    required: true
  },
  entityRelations: {
    type: Array,
    default: () => []
  },
  templateName: {
    type: String,
    default: ''
  },
  getEntityNameById: {
    type: Function,
    required: true,
    default: (id) => `未知角色 (${id})`
  },
  formatDimensionKey: {
    type: Function,
    default: key => key
  },
  defaultTheme: {
    type: String,
    default: 'classic'
  }
});

const emit = defineEmits(['export-success']);

// 状态变量
const previewUrl = ref('');
const generatingPreview = ref(false);
const exporting = ref(false);
const currentTheme = ref(props.defaultTheme);
const previewContent = ref(null);

// 导出选项
const includeRelations = ref(false);
const includeDimensions = ref(true);
const imageQuality = ref('3');

// 主题管理相关状态
const showThemeManager = ref(false);
const activeTab = ref('add');
const newTheme = ref({
  name: '',
  id: '',
  json: ''
});

// 引入配置存储
const configStore = useConfigStore();

// 获取软件界面主题（检查HTML元素的class）
const isDarkTheme = computed(() => {
  if (typeof document !== 'undefined') {
    return document.documentElement.classList.contains('dark');
  }
  return false;
});

// 定义增强的内置主题
const builtinThemes = [
  {
    id: 'classic',
    name: '经典',
    colors: {
      background: 'linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%)',
      textPrimary: '#333333',
      textSecondary: '#666666',
      textTertiary: '#999999',
      cardBackground: '#ffffff',
      accent: '#409EFF',
      accentLight: '#ecf5ff',
      avatarBackground: '#4a93ff',
      border: '#eaeaea',
      borderLight: '#f0f0f0',
      sectionBackground: '#f9f9f9',
      tagBackground: '#f0f9ff',
      tagText: '#1890ff'
    },
    fonts: {
      primary: "'PingFang SC', 'Microsoft YaHei', sans-serif",
      title: "600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      subtitle: "500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      body: "400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      caption: "400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      dimension: "500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"
    },
    spacing: {
      cardPadding: '24px',
      sectionGap: '20px',
      itemGap: '12px',
      avatarSize: '72px'
    },
    borderRadius: '12px',
    shadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
    cardShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
    backgroundPattern: {
      type: 'geometric',
      opacity: 0.08,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="classicPattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
            <circle cx="30" cy="30" r="20" fill="none" stroke="#409EFF" stroke-width="0.5" opacity="0.3"/>
            <circle cx="30" cy="30" r="10" fill="none" stroke="#409EFF" stroke-width="0.3" opacity="0.5"/>
            <path d="M15,15 L45,45 M45,15 L15,45" stroke="#409EFF" stroke-width="0.2" opacity="0.2"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#classicPattern)"/>
      </svg>`
    }
  },
  {
    id: 'dark',
    name: '暗黑',
    colors: {
      background: 'linear-gradient(135deg, #2d3748 0%, #1a202c 100%)',
      textPrimary: '#e2e8f0',
      textSecondary: '#a0aec0',
      textTertiary: '#718096',
      cardBackground: '#2d3748',
      accent: '#63b3ed',
      accentLight: '#2a4a6b',
      avatarBackground: '#4299e1',
      border: '#4a5568',
      borderLight: '#3a4553',
      sectionBackground: '#283141',
      tagBackground: '#2a4a6b',
      tagText: '#90cdf4'
    },
    fonts: {
      primary: "'PingFang SC', 'Microsoft YaHei', sans-serif",
      title: "600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      subtitle: "500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      body: "400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      caption: "400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",
      dimension: "500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"
    },
    spacing: {
      cardPadding: '24px',
      sectionGap: '20px',
      itemGap: '12px',
      avatarSize: '72px'
    },
    borderRadius: '12px',
    shadow: '0 8px 24px rgba(0, 0, 0, 0.25)',
    cardShadow: '0 2px 12px rgba(0, 0, 0, 0.15)',
    backgroundPattern: {
      type: 'starry',
      opacity: 0.25,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="starryPattern" x="0" y="0" width="120" height="120" patternUnits="userSpaceOnUse">
            <circle cx="20" cy="30" r="1" fill="#63b3ed" opacity="0.6"/>
            <circle cx="80" cy="20" r="0.5" fill="#90cdf4" opacity="0.8"/>
            <circle cx="100" cy="70" r="1.5" fill="#63b3ed" opacity="0.4"/>
            <circle cx="40" cy="90" r="0.8" fill="#90cdf4" opacity="0.7"/>
            <circle cx="60" cy="50" r="0.3" fill="#bee3f8" opacity="0.9"/>
            <path d="M70,40 L72,44 L76,44 L73,47 L74,51 L70,49 L66,51 L67,47 L64,44 L68,44 Z" fill="#63b3ed" opacity="0.3"/>
            <path d="M30,80 L31,82 L33,82 L31.5,83.5 L32,85 L30,84 L28,85 L28.5,83.5 L27,82 L29,82 Z" fill="#90cdf4" opacity="0.5"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#starryPattern)"/>
      </svg>`
    }
  },
  {
    id: 'creative',
    name: '灵感',
    colors: {
      background: 'linear-gradient(135deg, #fdf6fd 0%, #f3e7ff 100%)',
      textPrimary: '#4a2a5d',
      textSecondary: '#7b5a8c',
      textTertiary: '#a78baf',
      cardBackground: '#fcf8ff',
      accent: '#9c6bdf',
      accentLight: '#f0e6ff',
      avatarBackground: '#7956b3',
      border: '#e6d8f8',
      borderLight: '#f0e6ff',
      sectionBackground: '#f8f0ff',
      tagBackground: '#f0e6ff',
      tagText: '#8b5cf6'
    },
    fonts: {
      primary: "'PingFang SC', 'Source Han Sans CN', sans-serif",
      title: "600 28px 'PingFang SC', 'Source Han Sans CN', sans-serif",
      subtitle: "500 16px 'PingFang SC', 'Source Han Sans CN', sans-serif",
      body: "400 15px 'PingFang SC', 'Source Han Sans CN', sans-serif",
      caption: "400 12px 'PingFang SC', 'Source Han Sans CN', sans-serif",
      dimension: "500 15px 'PingFang SC', 'Source Han Sans CN', sans-serif"
    },
    spacing: {
      cardPadding: '28px',
      sectionGap: '24px',
      itemGap: '14px',
      avatarSize: '76px'
    },
    borderRadius: '20px',
    shadow: '0 12px 35px rgba(156, 107, 223, 0.12)',
    cardShadow: '0 4px 16px rgba(156, 107, 223, 0.08)',
    backgroundPattern: {
      type: 'floral',
      opacity: 0.12,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="floralPattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
            <path d="M50,30 Q60,20 70,30 Q60,40 50,30 Q40,20 30,30 Q40,40 50,30" fill="#9c6bdf" opacity="0.15"/>
            <path d="M50,30 Q55,25 60,30 Q55,35 50,30 Q45,25 40,30 Q45,35 50,30" fill="#8b5cf6" opacity="0.2"/>
            <circle cx="50" cy="30" r="3" fill="#7c3aed" opacity="0.3"/>
            <path d="M20,70 Q25,65 30,70 Q25,75 20,70 Q15,65 10,70 Q15,75 20,70" fill="#a855f7" opacity="0.1"/>
            <path d="M80,80 Q85,75 90,80 Q85,85 80,80 Q75,75 70,80 Q75,85 80,80" fill="#9333ea" opacity="0.12"/>
            <path d="M30,20 C35,15 40,20 35,25 C30,30 25,25 30,20" fill="#8b5cf6" opacity="0.08"/>
            <path d="M70,60 C75,55 80,60 75,65 C70,70 65,65 70,60" fill="#7c3aed" opacity="0.1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#floralPattern)"/>
      </svg>`
    }
  },
  {
    id: 'minimalist',
    name: '简约',
    colors: {
      background: '#ffffff',
      textPrimary: '#111111',
      textSecondary: '#555555',
      textTertiary: '#888888',
      cardBackground: '#ffffff',
      accent: '#000000',
      accentLight: '#f5f5f5',
      avatarBackground: '#111111',
      border: '#dddddd',
      borderLight: '#eeeeee',
      sectionBackground: '#f7f7f7',
      tagBackground: '#f0f0f0',
      tagText: '#333333'
    },
    fonts: {
      primary: "'Helvetica Neue', Arial, sans-serif",
      title: "600 28px 'Helvetica Neue', Arial, sans-serif",
      subtitle: "500 16px 'Helvetica Neue', Arial, sans-serif",
      body: "400 14px 'Helvetica Neue', Arial, sans-serif",
      caption: "400 12px 'Helvetica Neue', Arial, sans-serif",
      dimension: "500 15px 'Helvetica Neue', Arial, sans-serif"
    },
    spacing: {
      cardPadding: '20px',
      sectionGap: '16px',
      itemGap: '10px',
      avatarSize: '64px'
    },
    borderRadius: '2px',
    shadow: '0 1px 3px rgba(0, 0, 0, 0.08)',
    cardShadow: '0 1px 6px rgba(0, 0, 0, 0.05)',
    backgroundPattern: {
      type: 'minimal',
      opacity: 0.02,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="minimalPattern" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
            <line x1="0" y1="40" x2="80" y2="40" stroke="#000000" stroke-width="0.5" opacity="0.1"/>
            <line x1="40" y1="0" x2="40" y2="80" stroke="#000000" stroke-width="0.5" opacity="0.1"/>
            <rect x="20" y="20" width="40" height="40" fill="none" stroke="#000000" stroke-width="0.3" opacity="0.08"/>
            <circle cx="40" cy="40" r="15" fill="none" stroke="#000000" stroke-width="0.2" opacity="0.06"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#minimalPattern)"/>
      </svg>`
    }
  },
  {
    id: 'mountain',
    name: '山川',
    colors: {
      background: 'linear-gradient(135deg, #e8f4f8 0%, #d1e7dd 100%)',
      textPrimary: '#2d5016',
      textSecondary: '#52734d',
      textTertiary: '#74a478',
      cardBackground: '#f8fffe',
      accent: '#28a745',
      accentLight: '#d4edda',
      avatarBackground: '#20c997',
      border: '#c3e6cb',
      borderLight: '#e2f3e4',
      sectionBackground: '#f1f8e9',
      tagBackground: '#d1ecf1',
      tagText: '#0c5460'
    },
    fonts: {
      primary: "'Noto Serif SC', 'SimSun', serif",
      title: "600 28px 'Noto Serif SC', 'SimSun', serif",
      subtitle: "500 16px 'Noto Serif SC', 'SimSun', serif",
      body: "400 14px 'Noto Serif SC', 'SimSun', serif",
      caption: "400 12px 'Noto Serif SC', 'SimSun', serif",
      dimension: "500 15px 'Noto Serif SC', 'SimSun', serif"
    },
    spacing: {
      cardPadding: '28px',
      sectionGap: '24px',
      itemGap: '14px',
      avatarSize: '76px'
    },
    borderRadius: '16px',
    shadow: '0 8px 32px rgba(40, 167, 69, 0.15)',
    cardShadow: '0 4px 16px rgba(40, 167, 69, 0.08)',
    backgroundPattern: {
      type: 'mountain',
      opacity: 0.15,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="mountainPattern" x="0" y="0" width="200" height="120" patternUnits="userSpaceOnUse">
            <path d="M0,120 L40,60 L80,80 L120,40 L160,70 L200,50 L200,120 Z" fill="#28a745" opacity="0.1"/>
            <path d="M0,120 L30,80 L60,90 L100,60 L140,85 L180,65 L200,75 L200,120 Z" fill="#20c997" opacity="0.08"/>
            <path d="M0,120 L50,90 L90,100 L130,80 L170,95 L200,85 L200,120 Z" fill="#6f42c1" opacity="0.05"/>
            <circle cx="160" cy="30" r="12" fill="#ffc107" opacity="0.3"/>
            <path d="M150,35 Q160,25 170,35" stroke="#ffc107" stroke-width="1" fill="none" opacity="0.2"/>
            <circle cx="40" cy="25" r="3" fill="#ffffff" opacity="0.4"/>
            <circle cx="120" cy="20" r="2" fill="#ffffff" opacity="0.3"/>
            <circle cx="80" cy="15" r="1.5" fill="#ffffff" opacity="0.5"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#mountainPattern)"/>
      </svg>`
    }
  },
  {
    id: 'moonlight',
    name: '月夜',
    colors: {
      background: 'linear-gradient(135deg, #1a1d29 0%, #2d1b69 100%)',
      textPrimary: '#e2e8f0',
      textSecondary: '#cbd5e0',
      textTertiary: '#a0aec0',
      cardBackground: '#2a2f3a',
      accent: '#805ad5',
      accentLight: '#553c9a',
      avatarBackground: '#667eea',
      border: '#4a5568',
      borderLight: '#3a4553',
      sectionBackground: '#2d3748',
      tagBackground: '#553c9a',
      tagText: '#d6bcfa'
    },
    fonts: {
      primary: "'Noto Sans SC', 'Microsoft YaHei', sans-serif",
      title: "600 28px 'Noto Sans SC', 'Microsoft YaHei', sans-serif",
      subtitle: "500 16px 'Noto Sans SC', 'Microsoft YaHei', sans-serif",
      body: "400 14px 'Noto Sans SC', 'Microsoft YaHei', sans-serif",
      caption: "400 12px 'Noto Sans SC', 'Microsoft YaHei', sans-serif",
      dimension: "500 15px 'Noto Sans SC', 'Microsoft YaHei', sans-serif"
    },
    spacing: {
      cardPadding: '26px',
      sectionGap: '22px',
      itemGap: '13px',
      avatarSize: '74px'
    },
    borderRadius: '18px',
    shadow: '0 12px 40px rgba(128, 90, 213, 0.25)',
    cardShadow: '0 6px 20px rgba(128, 90, 213, 0.15)',
    backgroundPattern: {
      type: 'moonlight',
      opacity: 0.2,
      svg: `<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="moonlightPattern" x="0" y="0" width="150" height="150" patternUnits="userSpaceOnUse">
            <circle cx="75" cy="40" r="20" fill="#f7fafc" opacity="0.8"/>
            <path d="M65,35 Q75,25 85,35 Q75,45 65,35" fill="#2d1b69" opacity="0.3"/>
            <circle cx="30" cy="100" r="1" fill="#e2e8f0" opacity="0.6"/>
            <circle cx="120" cy="80" r="0.8" fill="#cbd5e0" opacity="0.7"/>
            <circle cx="100" cy="120" r="1.2" fill="#e2e8f0" opacity="0.5"/>
            <circle cx="20" cy="60" r="0.5" fill="#f7fafc" opacity="0.8"/>
            <circle cx="140" cy="30" r="0.7" fill="#cbd5e0" opacity="0.6"/>
            <path d="M10,130 Q30,120 50,130 Q70,140 90,130 Q110,120 130,130 Q150,140 150,150 L0,150 Z" fill="#4c51bf" opacity="0.1"/>
            <path d="M0,140 Q20,130 40,140 Q60,150 80,140 Q100,130 120,140 Q140,150 150,140 L150,150 L0,150 Z" fill="#553c9a" opacity="0.08"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#moonlightPattern)"/>
      </svg>`
    }
  }
];

// 获取自定义主题
const customThemes = computed(() => {
  const themes = configStore.state.config.customThemes || [];
  console.log('customThemes computed:', themes);
  return themes;
});

// 合并内置主题和自定义主题
const availableThemes = computed(() => {
  const available = [...customThemes.value, ...builtinThemes];
  console.log('availableThemes computed:', available.map(t => ({ id: t.id, name: t.name })));
  return available;
});

// 在组件挂载时加载配置
onMounted(async () => {
  console.log('EntityCardExporter mounted, configStore.state.config.loaded:', configStore.state.config.loaded);
  console.log('Current customThemes:', configStore.state.config.customThemes);
  console.log('Entity relations:', props.entityRelations);

  if (!configStore.state.config.loaded) {
    try {
      console.log('Loading config...');
      await configStore.loadConfig();
      console.log('Config loaded, customThemes:', configStore.state.config.customThemes);
    } catch (error) {
      console.error('加载配置失败:', error);
      ElMessage.error('加载自定义主题失败');
    }
  }

  // 如果有关系数据，默认启用包含关系选项
  if (props.entityRelations && props.entityRelations.length > 0) {
    includeRelations.value = true;
    console.log('自动启用包含关系选项，关系数量:', props.entityRelations.length);
  }
});

// 默认主题字段
const defaultThemeFields = {
  spacing: {
    cardPadding: '24px',
    sectionGap: '20px',
    itemGap: '12px',
    avatarSize: '72px'
  },
  fonts: {
    primary: "'PingFang SC', 'Microsoft YaHei', sans-serif",
    title: "600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",
    subtitle: "500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",
    body: "400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",
    caption: "400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",
    dimension: "500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"
  },
  borderRadius: '12px',
  shadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
  cardShadow: '0 2px 12px rgba(0, 0, 0, 0.08)'
};

// 获取当前主题配置，确保所有字段都存在
const theme = computed(() => {
  const baseTheme = availableThemes.value.find(t => t.id === currentTheme.value) || availableThemes.value[0];

  // 合并默认字段，确保不会有undefined
  return {
    ...defaultThemeFields,
    ...baseTheme,
    spacing: {
      ...defaultThemeFields.spacing,
      ...(baseTheme.spacing || {})
    },
    fonts: {
      ...defaultThemeFields.fonts,
      ...(baseTheme.fonts || {})
    }
  };
});

// 主题管理方法
const resetNewTheme = () => {
  newTheme.value = {
    name: '',
    id: '',
    json: ''
  };
};

const addTheme = async () => {
  try {
    console.log('Adding theme:', newTheme.value);

    if (!newTheme.value.name || !newTheme.value.id || !newTheme.value.json) {
      ElMessage.warning('请填写完整的主题信息');
      return;
    }

    // 解析JSON
    let themeData;
    try {
      themeData = JSON.parse(newTheme.value.json);
      console.log('Parsed theme data:', themeData);
    } catch (error) {
      console.error('JSON parse error:', error);
      ElMessage.error('主题JSON格式错误');
      return;
    }

    // 验证主题数据结构
    if (!themeData.colors || !themeData.fonts) {
      ElMessage.error('主题JSON缺少必要的colors或fonts配置');
      return;
    }

    // 构建完整的主题对象
    const themeObject = {
      id: newTheme.value.id,
      name: newTheme.value.name,
      ...themeData
    };

    console.log('Theme object to add:', themeObject);

    // 添加到配置
    await configStore.addCustomTheme(themeObject);

    console.log('Theme added successfully, current customThemes:', configStore.state.config.customThemes);

    ElMessage.success('主题添加成功');
    resetNewTheme();
    activeTab.value = 'manage';
  } catch (error) {
    console.error('添加主题失败:', error);
    ElMessage.error('添加主题失败: ' + error.message);
  }
};

const editTheme = (theme) => {
  newTheme.value = {
    name: theme.name,
    id: theme.id,
    json: JSON.stringify({
      colors: theme.colors,
      fonts: theme.fonts,
      borderRadius: theme.borderRadius,
      shadow: theme.shadow,
      pattern: theme.pattern
    }, null, 2)
  };
  activeTab.value = 'add';
};

const deleteTheme = async (themeId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个主题吗？', '确认删除', {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await configStore.deleteCustomTheme(themeId);
    ElMessage.success('主题删除成功');

    // 如果删除的是当前选中的主题，切换到默认主题
    if (currentTheme.value === themeId) {
      currentTheme.value = 'classic';
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除主题失败:', error);
      ElMessage.error('删除主题失败: ' + error.message);
    }
  }
};

const showJsonHelp = () => {
  ElMessageBox.alert(`
<h3>主题JSON格式说明</h3>
<p>主题JSON需要包含以下必要字段：</p>

<h4>1. colors（颜色配置）</h4>
<ul>
  <li><strong>background</strong>: 卡片背景色（支持渐变）</li>
  <li><strong>textPrimary</strong>: 主要文字颜色</li>
  <li><strong>textSecondary</strong>: 次要文字颜色</li>
  <li><strong>cardBackground</strong>: 内容卡片背景色</li>
  <li><strong>accent</strong>: 强调色（用于按钮、链接等）</li>
  <li><strong>avatarBackground</strong>: 头像背景色</li>
  <li><strong>border</strong>: 边框颜色</li>
  <li><strong>sectionBackground</strong>: 区块背景色</li>
</ul>

<h4>2. fonts（字体配置）</h4>
<ul>
  <li><strong>primary</strong>: 主要字体族</li>
  <li><strong>title</strong>: 标题字体样式（包含大小和粗细）</li>
  <li><strong>subtitle</strong>: 副标题字体样式</li>
  <li><strong>body</strong>: 正文字体样式</li>
</ul>

<h4>3. 可选字段</h4>
<ul>
  <li><strong>borderRadius</strong>: 圆角大小（如："12px"）</li>
  <li><strong>shadow</strong>: 阴影效果</li>
  <li><strong>pattern</strong>: SVG背景图案（可选）</li>
</ul>

<p><strong>提示：</strong>可以参考现有的自定义主题格式，或从settings.json中复制现有主题配置。</p>
  `, '主题JSON格式说明', {
    confirmButtonText: '知道了',
    dangerouslyUseHTMLString: true
  });
};

// 生成预览方法
const generatePreview = async () => {
  if (!props.entity) {
    ElMessage.warning('没有实体数据可供导出');
    return;
  }

  try {
    generatingPreview.value = true;

    // 创建临时卡片容器
    const cardContainer = document.createElement('div');
    cardContainer.className = 'entity-card-export';

    // 基本样式
    let containerStyle = `
      width: 600px;
      padding: 30px;
      background: ${theme.value.colors.background};
      border-radius: ${theme.value.borderRadius};
      box-shadow: ${theme.value.shadow};
      position: fixed;
      top: -9999px;
      left: -9999px;
      color: ${theme.value.colors.textPrimary};
      z-index: -1;
      overflow: hidden;
      position: relative;
    `;

    cardContainer.style.cssText = containerStyle;
    document.body.appendChild(cardContainer);

    // 添加背景图案支持
    if (theme.value.backgroundPattern && theme.value.backgroundPattern.svg) {
      const patternContainer = document.createElement('div');
      patternContainer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
        overflow: hidden;
        opacity: ${theme.value.backgroundPattern.opacity || 0.1};
        pointer-events: none;
      `;
      patternContainer.innerHTML = theme.value.backgroundPattern.svg;
      cardContainer.appendChild(patternContainer);
    }

    // 根据用户选择决定是否包含关系和维度
    console.log('关系数据检查:', {
      includeRelations: includeRelations.value,
      entityRelations: props.entityRelations,
      entityRelationsLength: props.entityRelations?.length || 0
    });

    const showRelations = includeRelations.value && props.entityRelations && props.entityRelations.length > 0;
    const showDimensions = includeDimensions.value && props.entity.dimensions &&
                          Object.keys(props.entity.dimensions).length > 0;

    // 根据质量选项设置缩放系数
    const scaleFactors = {
      '1': 1.5,  // 标准
      '2': 2,    // 高清
      '3': 3     // 超清
    };
    const scaleFactor = scaleFactors[imageQuality.value] || 2;

    // 处理维度数据的渲染，将换行符转换为<br>
    const renderDimensionValue = (value) => {
      return value.toString()
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;')
        .replace(/\n/g, '<br>');
    };

    // 重构卡片HTML模板生成 - 使用更安全的样式应用方式
    const contentDiv = document.createElement('div');
    contentDiv.style.cssText = `
      position: relative;
      z-index: 10;
    `;

    // 获取主题配置的便捷访问
    const t = theme.value;

    // 创建样式元素，使用唯一ID确保不会冲突
    const styleId = `entity-card-style-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const styleElement = document.createElement('style');
    styleElement.id = styleId;
    styleElement.textContent = `
      /* 限定作用域的样式重置和基础设置 - 使用唯一类名前缀 */
      .entity-card-generated-${styleId} * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      .entity-card-generated-${styleId} {
        font-family: ${t.fonts.primary};
        line-height: 1.6;
        letter-spacing: 0.015em;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        color: ${t.colors.textPrimary};
      }

      /* 标题样式 */
      .entity-card-generated-${styleId} .card-title {
        font: ${t.fonts.title};
        color: ${t.colors.textPrimary};
        margin: 0;
        line-height: 1.3;
        text-shadow: 0 1px 2px rgba(0,0,0,0.05);
      }

      .entity-card-generated-${styleId} .card-subtitle {
        font: ${t.fonts.subtitle};
        color: ${t.colors.textSecondary};
        margin: 4px 0 0 0;
        opacity: 0.9;
      }

      /* 头像样式 */
      .entity-card-generated-${styleId} .avatar {
        width: ${t.spacing.avatarSize};
        height: ${t.spacing.avatarSize};
        border-radius: 50%;
        background: ${t.colors.avatarBackground};
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: calc(${t.spacing.avatarSize} * 0.4);
        font-weight: 700;
        color: white;
        margin-right: 20px;
        box-shadow: ${t.cardShadow};
        position: relative;
      }

      .entity-card-generated-${styleId} .avatar::before {
        content: '';
        position: absolute;
        inset: -2px;
        border-radius: 50%;
        background: linear-gradient(45deg, ${t.colors.accent}40, transparent);
        z-index: -1;
      }

      /* 卡片容器样式 - 优化背景融合 */
      .entity-card-generated-${styleId} .card-section {
        background: ${t.colors.cardBackground}f0; /* 添加透明度 */
        backdrop-filter: blur(10px); /* 毛玻璃效果 */
        -webkit-backdrop-filter: blur(10px);
        border-radius: ${t.borderRadius};
        padding: ${t.spacing.cardPadding};
        margin-bottom: ${t.spacing.sectionGap};
        box-shadow: ${t.cardShadow}, inset 0 1px 0 rgba(255,255,255,0.1);
        border: 1px solid ${t.colors.border}80; /* 半透明边框 */
        position: relative;
        overflow: hidden;
      }

      .entity-card-generated-${styleId} .card-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, ${t.colors.accent}, ${t.colors.accent}80);
        opacity: 0.8;
      }

      .entity-card-generated-${styleId} .card-section::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
          ${t.colors.cardBackground}20 0%,
          transparent 50%,
          ${t.colors.accent}05 100%);
        pointer-events: none;
      }

      /* 区块标题样式 */
      .entity-card-generated-${styleId} .section-title {
        font: ${t.fonts.subtitle};
        color: ${t.colors.textPrimary};
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 2px solid ${t.colors.borderLight};
        position: relative;
      }

      .entity-card-generated-${styleId} .section-title::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 30px;
        height: 2px;
        background: ${t.colors.accent};
      }

      /* 维度项样式 - 优化背景融合 */
      .entity-card-generated-${styleId} .dimension-item {
        margin-bottom: ${t.spacing.itemGap};
        border-radius: calc(${t.borderRadius} * 0.6);
        overflow: hidden;
        border: 1px solid ${t.colors.borderLight}60;
        transition: all 0.2s ease;
        background: ${t.colors.cardBackground}40; /* 半透明背景 */
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
      }

      .entity-card-generated-${styleId} .dimension-item:hover {
        border-color: ${t.colors.accent}60;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        background: ${t.colors.cardBackground}60;
      }

      .entity-card-generated-${styleId} .dimension-label {
        font: ${t.fonts.dimension};
        color: ${t.colors.textPrimary};
        background: ${t.colors.sectionBackground}80; /* 半透明背景 */
        padding: 8px 12px;
        border-bottom: 1px solid ${t.colors.borderLight}40;
        font-weight: 600;
        position: relative;
      }

      .entity-card-generated-${styleId} .dimension-label::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, ${t.colors.accent}10, transparent);
        pointer-events: none;
      }

      .entity-card-generated-${styleId} .dimension-value {
        font: ${t.fonts.body};
        color: ${t.colors.textPrimary};
        padding: 12px;
        line-height: 1.7;
        white-space: pre-wrap;
        word-break: break-word;
        background: ${t.colors.cardBackground}20; /* 更透明的背景 */
        position: relative;
      }

      /* 关系项样式 - 优化背景融合 */
      .entity-card-generated-${styleId} .relation-item {
        display: flex;
        align-items: center;
        padding: 10px 12px;
        margin-bottom: 8px;
        background: ${t.colors.sectionBackground}60; /* 半透明背景 */
        backdrop-filter: blur(3px);
        -webkit-backdrop-filter: blur(3px);
        border-radius: calc(${t.borderRadius} * 0.6);
        border: 1px solid ${t.colors.borderLight}50;
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
      }

      .entity-card-generated-${styleId} .relation-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 3px;
        height: 100%;
        background: ${t.colors.accent};
        opacity: 0.6;
        transition: opacity 0.2s ease;
      }

      .entity-card-generated-${styleId} .relation-item:hover {
        background: ${t.colors.accentLight}80;
        border-color: ${t.colors.accent}80;
        transform: translateX(4px);
      }

      .entity-card-generated-${styleId} .relation-item:hover::before {
        opacity: 1;
      }

      .entity-card-generated-${styleId} .relation-arrow {
        font-size: 16px;
        color: ${t.colors.accent};
        margin-right: 12px;
        font-weight: bold;
      }

      .entity-card-generated-${styleId} .relation-name {
        flex: 1;
        font: ${t.fonts.body};
        color: ${t.colors.textPrimary};
        font-weight: 500;
      }

      .entity-card-generated-${styleId} .relation-tag {
        padding: 4px 10px;
        background: ${t.colors.tagBackground}90; /* 半透明背景 */
        backdrop-filter: blur(3px);
        -webkit-backdrop-filter: blur(3px);
        color: ${t.colors.tagText};
        border-radius: 12px;
        font: ${t.fonts.caption};
        font-weight: 500;
        border: 1px solid ${t.colors.accent}40;
        position: relative;
        overflow: hidden;
      }

      .entity-card-generated-${styleId} .relation-tag::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, ${t.colors.accent}10, transparent);
        pointer-events: none;
      }

      /* 描述样式 - 优化背景融合 */
      .entity-card-generated-${styleId} .description {
        font: ${t.fonts.body};
        color: ${t.colors.textPrimary};
        background: ${t.colors.sectionBackground}70; /* 半透明背景 */
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        padding: 16px;
        border-radius: calc(${t.borderRadius} * 0.8);
        line-height: 1.7;
        border-left: 4px solid ${t.colors.accent};
        margin: 8px 0;
        position: relative;
        overflow: hidden;
      }

      .entity-card-generated-${styleId} .description::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 60px;
        height: 100%;
        background: linear-gradient(90deg, transparent, ${t.colors.accent}08);
        pointer-events: none;
      }

      /* 页脚样式 */
      .entity-card-generated-${styleId} .footer {
        text-align: center;
        margin-top: 24px;
        font: ${t.fonts.caption};
        color: ${t.colors.textTertiary};
        opacity: 0.8;
      }
    `;

    // 将样式元素添加到head中
    document.head.appendChild(styleElement);

    // 创建内容HTML，使用唯一的类名
    contentDiv.innerHTML = `
      <div class="entity-card-generated-${styleId}">
        <!-- 头部区域 -->
        <div style="display: flex; align-items: center; margin-bottom: 28px;">
          <div class="avatar">
            ${props.entity.name?.substring(0, 1) || '?'}
          </div>
          <div style="flex: 1;">
            <h1 class="card-title">${props.entity.name || '未命名角色'}</h1>
            ${props.templateName ? `<div class="card-subtitle">${props.templateName}</div>` : ''}
          </div>
        </div>

      <!-- 基本信息区域 -->
      <div class="card-section">
        <h2 class="section-title">基本信息</h2>
        ${props.entity.description ? `
          <div class="description">${props.entity.description}</div>
        ` : `
          <div style="color: ${t.colors.textTertiary}; font-style: italic; text-align: center; padding: 20px;">
            暂无描述信息
          </div>
        `}
      </div>

      ${showDimensions ? `
        <!-- 属性维度区域 -->
        <div class="card-section">

          <div style="display: grid; gap: ${t.spacing.itemGap};">
            ${Object.entries(props.entity.dimensions).map(([key, value]) => `
              <div class="dimension-item">
                <div class="dimension-label">${props.formatDimensionKey(key)}</div>
                <div class="dimension-value">${renderDimensionValue(value)}</div>
              </div>
            `).join('')}
          </div>
        </div>
      ` : ''}

      ${showRelations ? `
        <!-- 相关关系区域 -->
        <div class="card-section">
          <h2 class="section-title">相关关系 (${props.entityRelations.length})</h2>
          ${props.entityRelations.slice(0, 5).map(relation => `
            <div class="relation-item">
              <div class="relation-arrow">${relation.source === props.entity.id ? '→' : '←'}</div>
              <div class="relation-name">${props.getEntityNameById(relation.source === props.entity.id ? relation.target : relation.source)}</div>
              <div class="relation-tag">${relation.type}</div>
            </div>
          `).join('')}
          ${props.entityRelations.length > 5 ? `
            <div style="text-align: center; color: ${t.colors.textSecondary}; margin-top: 16px; font: ${t.fonts.caption};">
              <span style="background: ${t.colors.sectionBackground}; padding: 6px 12px; border-radius: 16px; border: 1px solid ${t.colors.borderLight};">
                + ${props.entityRelations.length - 5} 项更多关系
              </span>
            </div>
          ` : ''}
        </div>
      ` : ''}

        <!-- 页脚 -->
        <div class="footer">
          <div style="display: inline-flex; align-items: center; gap: 8px;">
            <span>✨ Powered By PVV</span>
            <span>•</span>
            <span>${new Date().toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' })}</span>
          </div>
        </div>
      </div>
    `;

    cardContainer.appendChild(contentDiv);

    // 等待DOM更新
    await new Promise(resolve => setTimeout(resolve, 100));

    // 使用html2canvas时应用选择的缩放系数
    const canvas = await html2canvas(cardContainer, {
      scale: scaleFactor,
      useCORS: true,
      backgroundColor: null
    });

    // 转换canvas为base64图片数据
    const imageData = canvas.toDataURL('image/png');
    previewUrl.value = imageData;

    // 清理临时元素和样式
    document.body.removeChild(cardContainer);

    // 清理动态创建的样式元素
    const existingStyle = document.getElementById(styleId);
    if (existingStyle) {
      document.head.removeChild(existingStyle);
    }

    // 生成预览后自动滚动到顶部
    if (previewContent.value) {
      previewContent.value.scrollTop = 0;
    }

  } catch (error) {
    console.error('生成预览失败:', error);
    ElMessage.error('生成预览失败：' + error.message);

    // 错误情况下也要清理可能创建的元素
    try {
      if (cardContainer && document.body.contains(cardContainer)) {
        document.body.removeChild(cardContainer);
      }
      const existingStyle = document.getElementById(styleId);
      if (existingStyle) {
        document.head.removeChild(existingStyle);
      }
    } catch (cleanupError) {
      console.warn('清理临时元素时出错:', cleanupError);
    }
  } finally {
    generatingPreview.value = false;
  }
};

// 完善卡片导出功能
const exportCard = async () => {
  try {
    if (!previewUrl.value) {
      ElMessage.warning('请先生成预览');
      return;
    }

    exporting.value = true;

    // 获取文件名（使用角色名称）
    const fileName = `${props.entity?.name || '角色'}.png`;

    // 调用后端API获取保存目录
    const response = await window.pywebview.api.select_directory();

    const result = typeof response === 'string' ? JSON.parse(response) : response;

    // 如果用户取消，则退出
    if (!result || result.status !== 'success' || !result.data) {
      exporting.value = false;
      ElMessage.error("取消导出");
      return;
    }

    // 构建完整的文件路径
    const directory = result.data;
    const filePath = `${directory}/${fileName}`;

    // 使用已有的save_entity_card方法保存卡片
    const saveResponse = await window.pywebview.api.save_entity_card({
      file_path: filePath,
      image_data: previewUrl.value
    });

    // 处理后端返回的JSON字符串
    const saveResult = typeof saveResponse === 'string' ? JSON.parse(saveResponse) : saveResponse;

    if (saveResult && saveResult.status === 'success') {
      ElMessage.success(`卡片已成功导出至: ${filePath}`);

      // 确保通过事件通知父组件
      emit('export-success', filePath);

      // 可选：打开文件所在位置
      try {
        await window.pywebview.api.open_directory(directory);
      } catch (error) {
        console.warn('无法打开文件目录:', error);
      }
    } else {
      ElMessage.error('导出失败：' + (saveResult?.message || '未知错误'));
    }
  } catch (error) {
    console.error('导出卡片时出错:', error);
    ElMessage.error('导出失败：' + (error.message || '未知错误'));
  } finally {
    exporting.value = false;
  }
};

// 监听主题变化，自动更新预览
watch(currentTheme, () => {
  if (previewUrl.value) {
    // 如果已经有预览，切换主题时自动重新生成
    generatePreview();
  }
});

// 监听导出选项变化，自动更新预览
watch([includeRelations, includeDimensions], () => {
  if (previewUrl.value) {
    // 如果已经有预览，切换选项时自动重新生成
    generatePreview();
  }
});
</script>

<!-- 重新设计的现代化样式 -->
<style lang="scss" scoped>
.entity-card-exporter {
  width: 100%;
  height: 100%; /* 占满父容器 */
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  font-family: var(--el-font-family);
}

.exporter-main {
  display: flex;
  flex: 1;
  min-height: 0;
  gap: 1px;
  background: var(--el-border-color-light);
  height: 100%;
}

/* 左侧配置面板 */
.config-panel {
  width: 320px;
  background: var(--el-bg-color);
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--el-border-color-light);
  height: 100%;
  overflow: hidden;
}

.config-section {
  padding: 16px 20px;

  &:not(:last-child) {
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  /* 主题选择区域 - 占据主要空间，可滚动 */
  &:first-child {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 允许收缩 */
  }

  /* 导出选项区域 - 固定高度 */
  &:nth-child(2) {
    flex: 0 0 auto;
    padding: 12px 20px;
  }
}

/* 操作按钮区域 */
.action-section {
  padding: 16px 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  background: var(--el-fill-color-blank);
  flex-shrink: 0; /* 不收缩 */
  margin-top: auto; /* 推到底部 */
  display: flex;
  flex-direction: column;
  gap: 8px; /* 使用gap替代margin */

  .el-button {
    width: 100% !important;
    height: 40px !important; /* 固定按钮高度 */
    margin: 0 !important; /* 强制移除所有margin */
    padding: 0 16px !important; /* 统一内边距 */
    box-sizing: border-box !important;

    /* 移除Element Plus的默认间距 */
    &:not(:last-child) {
      margin-bottom: 0 !important;
      margin-right: 0 !important;
    }
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 主题网格 */
.theme-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px; /* 减小间距 */
  overflow-y: auto;
  flex: 1;
  padding-right: 4px;
  min-height: 0; /* 重要：允许收缩 */

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-fill-color-lighter);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 3px;

    &:hover {
      background: var(--el-border-color-dark);
    }
  }
}

.theme-card {
  cursor: pointer;
  border-radius: 6px;
  padding: 6px; /* 减小内边距 */
  transition: all 0.2s ease;
  border: 2px solid transparent;
  min-height: 70px; /* 减小最小高度 */

  &:hover {
    background: var(--el-fill-color-light);
    transform: translateY(-1px);
  }

  &.active {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
  }
}

.theme-preview {
  width: 100%;
  height: 40px; /* 进一步减小高度 */
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  margin-bottom: 4px; /* 进一步减小间距 */
  border: 1px solid var(--el-border-color-light);
}

.theme-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;

  :deep(svg) {
    width: 100%;
    height: 100%;
  }
}

.preview-avatar {
  position: absolute;
  top: 6px;
  left: 6px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  font-weight: 600;
  z-index: 2;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.preview-card {
  position: absolute;
  bottom: 6px;
  right: 6px;
  width: 24px;
  height: 14px;
  border-radius: 3px;
  z-index: 2;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.theme-label {
  font-size: 11px; /* 稍微减小字体 */
  font-weight: 500;
  color: var(--el-text-color-regular);
  text-align: center;
  line-height: 1.2;
  padding: 0 4px; /* 添加内边距 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* 防止文字换行 */
}

/* 导出选项 */
.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px; /* 减小间距 */
  margin-bottom: 12px; /* 减小底部间距 */
}

.quality-option {
  display: flex;
  flex-direction: column;
  gap: 6px; /* 减小间距 */
}

.option-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* 操作按钮区域 */
.action-section {
  padding: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: auto;
  flex-shrink: 0; /* 防止被压缩 */
  background: var(--el-bg-color);
}

.generate-button,
.export-button {
  width: 100% !important;
  height: 40px !important; /* 确保按钮高度一致 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-sizing: border-box !important; /* 确保padding不影响宽度 */
  margin: 0 !important; /* 强制移除margin */
  padding: 0 16px !important; /* 统一内边距 */
}

/* 右侧预览面板 */
.preview-panel {
  flex: 1;
  background: var(--el-bg-color);
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.preview-header {
  padding: 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--el-bg-color);
}

.preview-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.preview-info {
  font-size: 14px;
  color: var(--el-text-color-regular);
  background: var(--el-fill-color-light);
  padding: 4px 12px;
  border-radius: 12px;
  border: 1px solid var(--el-border-color-lighter);
}

.preview-content {
  flex: 1;
  overflow: auto; /* 支持滚动 */
  padding: 20px;
  min-height: 0;
  background: var(--el-fill-color-blank);
  position: relative; /* 为空状态的绝对定位提供容器 */

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-fill-color-lighter);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 4px;

    &:hover {
      background: var(--el-border-color-dark);
    }
  }
}

.preview-wrapper {
  display: block; /* 改为块级元素，不使用flex */
  width: 100%;
  text-align: center; /* 图片水平居中 */
}

.preview-image {
  max-width: 100%; /* 保持最大宽度限制，防止超出容器 */
  height: auto; /* 保持原始比例 */
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transition: transform 0.2s ease;
  display: block; /* 确保图片正确显示 */

  &:hover {
    transform: scale(1.02);
  }
}

.preview-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--el-text-color-placeholder);
  width: 100%;
  height: 100%;
  min-height: 300px;
  position: absolute; /* 绝对定位，确保居中 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.empty-icon {
  margin-bottom: 16px;
  opacity: 0.6;

  svg {
    color: var(--el-text-color-placeholder);
  }
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-regular);
  margin: 0 0 8px 0;
}

.empty-hint {
  font-size: 14px;
  color: var(--el-text-color-placeholder);
  margin: 0;
}

/* 主题管理对话框样式 */
:deep(.theme-dialog) {
  .el-dialog {
    height: 80vh;
    max-height: 700px;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    padding: 0;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .el-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-tabs__content {
      flex: 1;
      overflow: hidden;
    }

    .el-tab-pane {
      height: 100%;
      overflow-y: auto;
    }
  }
}

.add-theme-form {
  padding: 20px;
}

.manage-themes {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.theme-manage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.2s ease;

  &:hover {
    background: var(--el-fill-color-light);
    border-color: var(--el-border-color-light);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.theme-info {
  flex: 1;
}

.theme-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.theme-id {
  margin: 0;
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  font-family: var(--el-font-family-mono);
}

.theme-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .config-panel {
    width: 280px;
  }

  .theme-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .exporter-main {
    flex-direction: column;
  }

  .config-panel {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .preview-panel {
    min-height: 400px;
  }
}
</style>
