<template>
  <Teleport to="body">
    <div
      v-if="visible"
      ref="selectorRef"
      class="entity-selector"
      :class="`theme-${theme}`"
      :style="{ top: position.top + 'px', left: position.left + 'px' }"
      @click.stop
      @keydown="handleKeydown"
      tabindex="-1"
    >
      <!-- 选择器头部 -->
      <div class="selector-header">
        <!-- 搜索框 -->
        <div class="search-wrapper">
          <div class="search-icon">
            <svg viewBox="0 0 24 24" fill="none">
              <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
              <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <input
            ref="searchInputRef"
            v-model="localSearchQuery"
            type="text"
            class="search-input"
            placeholder="搜索实体、章节、场景..."
            @input="handleSearch"
            @keydown="handleKeydown"
          />
          <button
            v-if="localSearchQuery"
            class="clear-search"
            @click="clearSearch"
            title="清除搜索"
          >
            <svg viewBox="0 0 24 24" fill="none">
              <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>

        <!-- 多选控制区域 -->
        <div class="multi-select-controls" v-if="selectedItems.length > 0">
          <div class="selected-count">
            <span class="count-badge">{{ selectedItems.length }}</span>
            <span class="count-text">项已选择</span>
          </div>
          <div class="control-buttons">
            <button class="control-btn secondary" @click="clearSelection">清空</button>
            <button class="control-btn primary" @click="confirmSelection">确认选择</button>
          </div>
        </div>
      </div>

      <!-- 选择器内容 -->
      <div class="selector-content">
        <!-- 主选项 -->
        <div class="main-options">
          <button
            class="option-item main-option"
            :class="{ active: mode === 'all-entities' }"
            @click="selectAllEntitiesMode"
          >
            <div class="option-icon">
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <span class="option-text">选择模板下的所有实体</span>
          </button>
          <button
            class="option-item main-option"
            :class="{ active: mode === 'single-entity' }"
            @click="selectSingleEntityMode"
          >
            <div class="option-icon">
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M20 21V19A4 4 0 0 0 16 15H8A4 4 0 0 0 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <span class="option-text">选择单个实体</span>
          </button>
          <button
            class="option-item main-option"
            :class="{ active: mode === 'chapters' }"
            @click="selectChapterMode"
          >
            <div class="option-icon">
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M6.5 2H20V22H6.5A2.5 2.5 0 0 1 4 19.5V4.5A2.5 2.5 0 0 1 6.5 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <span class="option-text">选择章节目录</span>
          </button>
          <button
            class="option-item main-option"
            :class="{ active: mode === 'scene-pools' }"
            @click="selectScenePoolMode"
          >
            <div class="option-icon">
              <svg viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <span class="option-text">选择场景卡池</span>
          </button>
        </div>

        <!-- 模板列表（选择所有实体模式） -->
        <div v-if="mode === 'all-entities'" class="content-section template-list">
          <div class="section-title">选择模板</div>
          <div class="items-container">
            <div
              v-for="(template, index) in filteredTemplates"
              :key="template.id"
              :data-index="index"
              class="option-item template-item"
              :class="{
                selected: selectedIndex === index,
                'multi-selected': isItemSelected('template', template.id)
              }"
              @click="toggleItemSelection('template', template, index)"
            >
              <div class="selection-indicator">
                <div class="checkbox" :class="{ checked: isItemSelected('template', template.id) }">
                  <svg v-if="isItemSelected('template', template.id)" viewBox="0 0 24 24" fill="none">
                    <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
              </div>
              <div class="item-icon">
                <svg viewBox="0 0 24 24" fill="none">
                  <path d="M22 19A2 2 0 0 1 20 21H4A2 2 0 0 1 2 19V5A2 2 0 0 1 4 3H9L11 6H20A2 2 0 0 1 22 8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="item-content">
                <span class="item-name">{{ template.name }}</span>
                <span class="item-meta">{{ getEntityCount(template) }}个实体</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 实体列表（选择单个实体模式） -->
        <div v-if="mode === 'single-entity'" class="content-section entity-list">
          <div class="section-title">选择实体</div>
          <div class="items-container">
            <div v-for="template in filteredTemplatesWithEntities" :key="template.id" class="template-group">
              <div
                class="template-header"
                @click="toggleGroupExpanded(`template-${template.id}`)"
              >
                <div class="expand-icon" :class="{ expanded: isGroupExpanded(`template-${template.id}`) }">
                  <svg viewBox="0 0 24 24" fill="none">
                    <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div class="template-icon">
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M22 19A2 2 0 0 1 20 21H4A2 2 0 0 1 2 19V5A2 2 0 0 1 4 3H9L11 6H20A2 2 0 0 1 22 8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <span class="template-name">{{ template.name }}</span>
                <span class="template-count">({{ template.entities.length }})</span>
              </div>
              <div
                v-if="isGroupExpanded(`template-${template.id}`)"
                class="template-entities"
              >
                <div
                  v-for="(entity, entityIndex) in template.entities"
                  :key="entity.id || entity.name"
                  :data-index="getEntityGlobalIndex(template, entityIndex)"
                  class="option-item entity-item"
                  :class="{
                    selected: selectedIndex === getEntityGlobalIndex(template, entityIndex),
                    'multi-selected': isItemSelected('entity', entity.id || entity.name)
                  }"
                  @click="toggleItemSelection('entity', entity, getEntityGlobalIndex(template, entityIndex), template)"
                >
                <div class="selection-indicator">
                  <div class="checkbox" :class="{ checked: isItemSelected('entity', entity.id || entity.name) }">
                    <svg v-if="isItemSelected('entity', entity.id || entity.name)" viewBox="0 0 24 24" fill="none">
                      <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                </div>
                <div class="item-icon">
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M14 2H6A2 2 0 0 0 4 4V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div class="item-content">
                  <div class="item-name-line">
                    <span class="item-name">{{ entity.name }}</span>
                    <span v-if="template.isSearchResult && entity.templateInfo" class="template-badge">
                      {{ entity.templateInfo.name }}
                    </span>
                  </div>
                  <span class="item-desc" v-if="entity.description">{{ entity.description }}</span>
                </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 章节目录列表（选择章节模式） -->
        <div v-if="mode === 'chapters'" class="content-section chapters-list">
          <div class="section-title">选择章节</div>
          <div class="items-container">
            <div v-if="filteredVolumes.length > 0">
              <div v-for="volume in filteredVolumes" :key="volume.id" class="volume-group">
                <div
                  class="volume-header"
                  @click="toggleGroupExpanded(`volume-${volume.id}`)"
                >
                  <div class="expand-icon" :class="{ expanded: isGroupExpanded(`volume-${volume.id}`) }">
                    <svg viewBox="0 0 24 24" fill="none">
                      <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                  <div class="volume-icon">
                    <svg viewBox="0 0 24 24" fill="none">
                      <path d="M22 19A2 2 0 0 1 20 21H4A2 2 0 0 1 2 19V5A2 2 0 0 1 4 3H9L11 6H20A2 2 0 0 1 22 8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                  <span class="volume-name">{{ volume.title }}</span>
                  <span class="volume-count">({{ volume.chapters?.length || 0 }}章)</span>
                </div>
                <div
                  v-if="isGroupExpanded(`volume-${volume.id}`)"
                  class="volume-chapters"
                >
                  <div
                    v-for="(chapter, chapterIndex) in volume.chapters"
                    :key="chapter.id"
                    :data-index="getChapterGlobalIndex(volume, chapterIndex)"
                    class="option-item chapter-item"
                    :class="{
                      selected: selectedIndex === getChapterGlobalIndex(volume, chapterIndex),
                      'multi-selected': isItemSelected('chapter', chapter.id)
                    }"
                    @click="toggleItemSelection('chapter', chapter, getChapterGlobalIndex(volume, chapterIndex), volume)"
                  >
                  <div class="selection-indicator">
                    <div class="checkbox" :class="{ checked: isItemSelected('chapter', chapter.id) }">
                      <svg v-if="isItemSelected('chapter', chapter.id)" viewBox="0 0 24 24" fill="none">
                        <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </div>
                  </div>
                  <div class="item-icon">
                    <svg viewBox="0 0 24 24" fill="none">
                      <path d="M14 2H6A2 2 0 0 0 4 4V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                  <div class="item-content">
                    <div class="item-name-line">
                      <span class="item-name">{{ chapter.title }}</span>
                      <span class="chapter-order">第{{ chapter.order }}章</span>
                    </div>
                    <span class="item-desc" v-if="chapter.summary">{{ chapter.summary }}</span>
                  </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              <div class="empty-icon">
                <svg viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <path d="M12 6V12L16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="empty-text">暂无章节</div>
            </div>
          </div>
        </div>

        <!-- 场景卡池列表（选择场景卡池模式） -->
        <div v-if="mode === 'scene-pools'" class="content-section scene-pools-list">
          <div class="section-title">选择场景卡池</div>
          <div class="items-container">
            <div v-if="filteredScenePools.length > 0">
              <div
                v-for="(pool, poolIndex) in filteredScenePools"
                :key="pool.id"
                :data-index="poolIndex"
                class="option-item scene-pool-item"
                :class="{
                  selected: selectedIndex === poolIndex,
                  'multi-selected': isItemSelected('scenePool', pool.id)
                }"
                @click="toggleItemSelection('scenePool', pool, poolIndex)"
              >
                <div class="selection-indicator">
                  <div class="checkbox" :class="{ checked: isItemSelected('scenePool', pool.id) }">
                    <svg v-if="isItemSelected('scenePool', pool.id)" viewBox="0 0 24 24" fill="none">
                      <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                </div>
                <div class="item-icon">
                  <svg viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div class="item-content">
                  <div class="item-name-line">
                    <span class="item-name">{{ pool.name }}</span>
                    <span class="scene-count">{{ pool.scenes?.length || 0 }}个场景</span>
                  </div>
                  <span class="item-desc" v-if="pool.description">{{ pool.description }}</span>
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              <div class="empty-icon">
                <svg viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <path d="M12 8V12L16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="empty-text">暂无场景卡池</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick, Teleport } from 'vue'
import { ElMessage } from 'element-plus'
import { useConfigStore } from '@/stores/config'

const props = defineProps({
  visible: Boolean,
  bookId: {
    type: [String, Number],
    required: true
  },
  position: {
    type: Object,
    default: () => ({ top: 0, left: 0 })
  },
  searchQuery: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['select', 'close'])

// 配置store
const configStore = useConfigStore()

// 主题计算属性
const theme = computed(() => configStore.theme || 'light')

// 响应式数据
const templates = ref([])
const entities = ref([])
const volumes = ref([]) // 书籍卷和章节
const scenePools = ref([]) // 场景卡池
const mode = ref('') // 'all-entities' | 'single-entity' | 'chapters' | 'scene-pools'
const localSearchQuery = ref('')
const searchInputRef = ref(null)

// 键盘导航状态
const selectedIndex = ref(-1) // 当前选中的项目索引
const selectorRef = ref(null)

// 多选状态
const selectedItems = ref([]) // 存储已选择的项目

// 展开/收起状态
const expandedGroups = ref(new Set()) // 存储展开的组ID

// 计算属性
const filteredTemplates = computed(() => {
  if (!localSearchQuery.value) return templates.value
  const query = localSearchQuery.value.toLowerCase()
  return templates.value.filter(template => 
    template.name.toLowerCase().includes(query)
  )
})

const filteredTemplatesWithEntities = computed(() => {
  const query = localSearchQuery.value.toLowerCase()

  if (!query) {
    // 没有搜索查询时，按模板分组显示
    return templates.value.map(template => {
      const templateEntities = entities.value.filter(entity =>
        entity.template_id === template.id
      )

      return {
        ...template,
        entities: templateEntities
      }
    }).filter(template => template.entities.length > 0)
  } else {
    // 有搜索查询时，全局搜索所有实体，不按模板分组
    const allMatchingEntities = entities.value.filter(entity =>
      entity.name.toLowerCase().includes(query) ||
      (entity.description && entity.description.toLowerCase().includes(query))
    )

    if (allMatchingEntities.length === 0) {
      return []
    }

    // 为每个匹配的实体添加其对应的模板信息
    const entitiesWithTemplates = allMatchingEntities.map(entity => {
      const entityTemplate = templates.value.find(t => t.id === entity.template_id)
      return {
        ...entity,
        templateInfo: entityTemplate
      }
    })

    // 创建一个虚拟的"搜索结果"模板来包含所有匹配的实体
    return [{
      id: 'search-results',
      name: `搜索结果`,
      isSearchResult: true,
      entities: entitiesWithTemplates
    }]
  }
})

// 过滤的卷和章节
const filteredVolumes = computed(() => {
  if (!localSearchQuery.value) return volumes.value
  const query = localSearchQuery.value.toLowerCase()

  return volumes.value.map(volume => {
    // 过滤章节
    const filteredChapters = (volume.chapters || []).filter(chapter =>
      chapter.title.toLowerCase().includes(query) ||
      (chapter.summary && chapter.summary.toLowerCase().includes(query))
    )

    // 如果卷标题匹配或有匹配的章节，则包含此卷
    if (volume.title.toLowerCase().includes(query) || filteredChapters.length > 0) {
      return {
        ...volume,
        chapters: filteredChapters
      }
    }
    return null
  }).filter(Boolean)
})

// 过滤的场景卡池
const filteredScenePools = computed(() => {
  if (!localSearchQuery.value) return scenePools.value
  const query = localSearchQuery.value.toLowerCase()

  return scenePools.value.filter(pool =>
    pool.name.toLowerCase().includes(query) ||
    (pool.description && pool.description.toLowerCase().includes(query))
  )
})

// 获取所有可选择的项目（用于键盘导航）
const selectableItems = computed(() => {
  const items = []

  if (mode.value === 'all-entities') {
    // 模板选择模式
    filteredTemplates.value.forEach(template => {
      items.push({
        type: 'template',
        data: template,
        key: `template-${template.id}`
      })
    })
  } else if (mode.value === 'single-entity') {
    // 实体选择模式
    filteredTemplatesWithEntities.value.forEach(template => {
      template.entities.forEach(entity => {
        items.push({
          type: 'entity',
          data: entity,
          template: template,
          key: `entity-${entity.id || entity.name}`
        })
      })
    })
  } else if (mode.value === 'chapters') {
    // 章节选择模式
    filteredVolumes.value.forEach(volume => {
      (volume.chapters || []).forEach(chapter => {
        items.push({
          type: 'chapter',
          data: chapter,
          volume: volume,
          key: `chapter-${chapter.id}`
        })
      })
    })
  } else if (mode.value === 'scene-pools') {
    // 场景卡池选择模式
    filteredScenePools.value.forEach(pool => {
      items.push({
        type: 'scenePool',
        data: pool,
        key: `pool-${pool.id}`
      })
    })
  }

  return items
})

// 方法
const loadTemplates = async () => {
  try {
    
    if (!props.bookId) {
      ElMessage.error('bookId 未提供')
      return
    }

    const response = await window.pywebview.api.book_controller.get_templates(props.bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    console.log('EntitySelector: 模板加载结果:', result)

    if (result.status === 'success') {
      templates.value = result.data || []
      
    } else {
      ElMessage.error(result.message || '加载模板失败')
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败：' + error.message)
  }
}

const loadEntities = async () => {
  try {
    
    if (!props.bookId) {
      ElMessage.error('bookId 未提供')
      return
    }

    const response = await window.pywebview.api.book_controller.get_entities(props.bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    console.log('EntitySelector: 实体加载结果:', result)

    if (result.status === 'success') {
      entities.value = result.data || []
      
    } else {
      ElMessage.error(result.message || '加载实体失败')
    }
  } catch (error) {
    console.error('加载实体失败:', error)
    ElMessage.error('加载实体失败：' + error.message)
  }
}

const loadVolumes = async () => {
  try {
    
    if (!props.bookId) {
      ElMessage.error('bookId 未提供')
      return
    }

    // 修复：确保加载章节数据，传递include_chapters=true参数
    const response = await window.pywebview.api.book_controller.get_volumes(
      props.bookId,
      1,        // page
      999,      // page_size - 加载所有卷
      true      // include_chapters - 关键修复：包含章节数据
    )
    const result = typeof response === 'string' ? JSON.parse(response) : response
    console.log('EntitySelector: 卷和章节加载结果:', result)

    if (result.status === 'success') {
      // 处理分页响应格式
      const data = result.data
      if (data && data.volumes) {
        // 新的分页格式
        volumes.value = data.volumes || []
      } else if (Array.isArray(data)) {
        // 旧的直接数组格式
        volumes.value = data
      } else {
        volumes.value = []
      }

      
      // 输出章节统计信息用于调试
      const totalChapters = volumes.value.reduce((total, vol) => total + (vol.chapters?.length || 0), 0)
      console.log('EntitySelector: 总章节数量:', totalChapters)
    } else {
      ElMessage.error(result.message || '加载卷和章节失败')
    }
  } catch (error) {
    console.error('加载卷和章节失败:', error)
    ElMessage.error('加载卷和章节失败：' + error.message)
  }
}

const loadScenePools = async () => {
  try {
    
    if (!props.bookId) {
      ElMessage.error('bookId 未提供')
      return
    }

    const response = await window.pywebview.api.book_controller.get_scene_events(props.bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    console.log('EntitySelector: 场景卡池加载结果:', result)

    if (result.status === 'success') {
      const sceneData = result.data || {}
      scenePools.value = sceneData.pools || []
      
    } else {
      ElMessage.error(result.message || '加载场景卡池失败')
    }
  } catch (error) {
    console.error('加载场景卡池失败:', error)
    ElMessage.error('加载场景卡池失败：' + error.message)
  }
}

const getEntityCount = (template) => {
  return entities.value.filter(entity => entity.template_id === template.id).length
}

const selectAllEntitiesMode = () => {
  mode.value = 'all-entities'
}

const selectSingleEntityMode = () => {
  mode.value = 'single-entity'
  nextTick(() => {
    initializeExpandedState()
  })
}

const selectChapterMode = () => {
  mode.value = 'chapters'
  nextTick(() => {
    initializeExpandedState()
  })
}

const selectScenePoolMode = () => {
  mode.value = 'scene-pools'
}

// 多选相关方法
const isItemSelected = (type, id) => {
  return selectedItems.value.some(item => item.type === type && item.id === id)
}

const toggleItemSelection = (type, data, index, extraData = null) => {
  const id = data.id || data.name
  const existingIndex = selectedItems.value.findIndex(item => item.type === type && item.id === id)

  if (existingIndex >= 0) {
    // 已选择，移除
    selectedItems.value.splice(existingIndex, 1)
  } else {
    // 未选择，添加
    let finalExtraData = extraData

    // 如果是模板类型，需要获取该模板下的所有实体
    if (type === 'template') {
      const templateEntities = entities.value.filter(entity => entity.template_id === data.id)
      finalExtraData = templateEntities
    }

    const item = {
      type,
      id,
      data,
      extraData: finalExtraData // 用于存储模板信息、卷信息等
    }
    selectedItems.value.push(item)
  }

  // 更新键盘导航索引
  selectedIndex.value = index
}

const clearSelection = () => {
  selectedItems.value = []
}

const confirmSelection = () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请先选择项目')
    return
  }

  // 发送多选结果
  emit('select', {
    type: 'multiple',
    items: selectedItems.value
  })
}

// 保留原有的单选方法（用于键盘导航）
const selectTemplate = (template) => {
  const templateEntities = entities.value.filter(entity => entity.template_id === template.id)
  emit('select', {
    type: 'template',
    template,
    entities: templateEntities
  })
}

const selectEntity = (entity, template) => {
  // 如果是搜索结果，使用实体自带的模板信息
  const actualTemplate = template.isSearchResult ? entity.templateInfo : template

  emit('select', {
    type: 'entity',
    entity,
    template: actualTemplate
  })
}

const selectChapter = (chapter, volume) => {
  emit('select', {
    type: 'chapter',
    chapter,
    volume
  })
}

const selectScenePool = (pool) => {
  emit('select', {
    type: 'scenePool',
    pool
  })
}

const handleSearch = (value) => {
  localSearchQuery.value = value
  // 重置选中索引
  selectedIndex.value = -1
}

// 清除搜索
const clearSearch = () => {
  localSearchQuery.value = ''
  selectedIndex.value = -1
}

// ===== 展开/收起功能 =====
// 切换组的展开状态
const toggleGroupExpanded = (groupId) => {
  if (expandedGroups.value.has(groupId)) {
    expandedGroups.value.delete(groupId)
  } else {
    expandedGroups.value.add(groupId)
  }
  // 触发响应式更新
  expandedGroups.value = new Set(expandedGroups.value)
}

// 检查组是否展开
const isGroupExpanded = (groupId) => {
  return expandedGroups.value.has(groupId)
}

// 展开所有组
const expandAllGroups = () => {
  const allGroupIds = new Set()

  // 添加所有模板ID
  templates.value.forEach(template => {
    allGroupIds.add(`template-${template.id}`)
  })

  // 添加所有卷ID
  volumes.value.forEach(volume => {
    allGroupIds.add(`volume-${volume.id}`)
  })

  expandedGroups.value = allGroupIds
}

// 收起所有组
const collapseAllGroups = () => {
  expandedGroups.value = new Set()
}

// 初始化展开状态（默认展开第一个组）
const initializeExpandedState = () => {
  // 根据当前模式展开第一个组
  if (mode.value === 'single-entity' && filteredTemplatesWithEntities.value.length > 0) {
    const firstTemplate = filteredTemplatesWithEntities.value[0]
    expandedGroups.value.add(`template-${firstTemplate.id}`)
  } else if (mode.value === 'chapters' && filteredVolumes.value.length > 0) {
    const firstVolume = filteredVolumes.value[0]
    expandedGroups.value.add(`volume-${firstVolume.id}`)
  }
}

// 获取总章节数
const getTotalChapterCount = () => {
  return filteredVolumes.value.reduce((total, volume) => {
    return total + (volume.chapters?.length || 0)
  }, 0)
}

// 获取章节在全局选择列表中的索引
const getChapterGlobalIndex = (volume, index) => {
  if (mode.value !== 'chapters') return index

  let globalIndex = 0

  for (let i = 0; i < filteredVolumes.value.length; i++) {
    const vol = filteredVolumes.value[i]
    if (vol.id === volume.id) {
      return globalIndex + index
    }
    globalIndex += vol.chapters?.length || 0
  }

  return index
}

// 键盘导航处理
const handleKeydown = (event) => {
  const items = selectableItems.value
  if (items.length === 0) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedIndex.value = Math.min(selectedIndex.value + 1, items.length - 1)
      scrollToSelectedItem()
      break

    case 'ArrowUp':
      event.preventDefault()
      selectedIndex.value = Math.max(selectedIndex.value - 1, -1)
      scrollToSelectedItem()
      break

    case 'Enter':
      event.preventDefault()
      if (selectedIndex.value >= 0 && selectedIndex.value < items.length) {
        const selectedItem = items[selectedIndex.value]
        if (selectedItem.type === 'template') {
          selectTemplate(selectedItem.data)
        } else if (selectedItem.type === 'entity') {
          selectEntity(selectedItem.data, selectedItem.template)
        } else if (selectedItem.type === 'chapter') {
          selectChapter(selectedItem.data, selectedItem.volume)
        } else if (selectedItem.type === 'scenePool') {
          selectScenePool(selectedItem.data)
        }
      }
      break

    case 'Escape':
      event.preventDefault()
      emit('close')
      break
  }
}

// 滚动到选中的项目
const scrollToSelectedItem = () => {
  if (selectedIndex.value < 0) return

  nextTick(() => {
    const selectedElement = selectorRef.value?.querySelector(`[data-index="${selectedIndex.value}"]`)
    if (selectedElement) {
      selectedElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      })
    }
  })
}

// 获取实体在全局选择列表中的索引
const getEntityGlobalIndex = (template, entityIndex) => {
  if (mode.value === 'single-entity') {
    let globalIndex = 0
    const templatesWithEntities = filteredTemplatesWithEntities.value

    for (let i = 0; i < templatesWithEntities.length; i++) {
      if (templatesWithEntities[i].id === template.id) {
        return globalIndex + entityIndex
      }
      globalIndex += templatesWithEntities[i].entities.length
    }
  }
  return entityIndex
}

// 监听器
watch(() => props.visible, async (newVal) => {
  console.log('EntitySelector: visible 变化为:', newVal)
  console.log('EntitySelector: position:', props.position)

  if (newVal) {
    await loadTemplates()
    await loadEntities()
    await loadVolumes()
    await loadScenePools()
    mode.value = ''
    localSearchQuery.value = props.searchQuery
    selectedIndex.value = -1 // 重置选中索引
    selectedItems.value = [] // 重置多选状态

    nextTick(() => {
      if (searchInputRef.value) {
        searchInputRef.value.focus()
      }
      // 确保选择器能接收键盘事件
      if (selectorRef.value) {
        selectorRef.value.focus()
      }
    })
  }
})

watch(() => props.searchQuery, (newVal) => {
  localSearchQuery.value = newVal
})
</script>

<style lang="scss" scoped>
/* 使用ChatUI的主题颜色系统 */
.entity-selector {
  /* 继承ChatUI的CSS变量，如果没有则使用默认值 */
  --chat-bg-primary: var(--chat-bg-primary, #ffffff);
  --chat-bg-secondary: var(--chat-bg-secondary, #f8f9fa);
  --chat-bg-tertiary: var(--chat-bg-tertiary, #e9ecef);
  --chat-border: var(--chat-border, #dee2e6);
  --chat-text-primary: var(--chat-text-primary, #212529);
  --chat-text-secondary: var(--chat-text-secondary, #6c757d);
  --chat-text-muted: var(--chat-text-muted, #adb5bd);
  --chat-text-placeholder: var(--chat-text-placeholder, #9ca3af);
  --chat-accent: var(--chat-accent, #0d6efd);
  --chat-accent-hover: var(--chat-accent-hover, #0b5ed7);
  --chat-accent-alpha: var(--chat-accent-alpha, rgba(13, 110, 253, 0.1));
  --chat-success: var(--chat-success, #198754);
  --chat-warning: var(--chat-warning, #ffc107);
  --chat-danger: var(--chat-danger, #dc3545);
  --chat-shadow: var(--chat-shadow, 0 2px 8px rgba(0, 0, 0, 0.1));
  --chat-radius: 8px;

  position: fixed;
  z-index: 9999;
  background: var(--chat-bg-primary);
  border: 1px solid var(--chat-border);
  border-radius: var(--chat-radius);
  box-shadow: var(--chat-shadow);
  width: 360px;
  height: 480px;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  .selector-header {
    padding: 16px;
    border-bottom: 1px solid var(--chat-border);
    flex-shrink: 0;
    background: var(--chat-bg-secondary);
    border-radius: var(--chat-radius) var(--chat-radius) 0 0;

    .search-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      background: var(--chat-bg-primary);
      border: 1px solid var(--chat-border);
      border-radius: 6px;
      transition: all 0.2s ease;

      &:focus-within {
        border-color: var(--chat-accent);
        box-shadow: 0 0 0 3px var(--chat-accent-alpha);
      }

      .search-icon {
        position: absolute;
        left: 12px;
        width: 16px;
        height: 16px;
        color: var(--chat-text-muted);
        pointer-events: none;
        z-index: 1;
      }

      .search-input {
        flex: 1;
        padding: 10px 12px 10px 40px;
        border: none;
        outline: none;
        background: transparent;
        color: var(--chat-text-primary);
        font-size: 14px;
        line-height: 1.4;

        &::placeholder {
          color: var(--chat-text-placeholder);
        }
      }

      .clear-search {
        position: absolute;
        right: 8px;
        width: 20px;
        height: 20px;
        border: none;
        background: none;
        color: var(--chat-text-muted);
        cursor: pointer;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          background: var(--chat-bg-tertiary);
          color: var(--chat-text-primary);
        }

        svg {
          width: 12px;
          height: 12px;
        }
      }
    }

    .multi-select-controls {
      margin-top: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 12px;
      background: var(--chat-accent-alpha);
      border-radius: 6px;
      border: 1px solid var(--chat-accent);
      animation: slideDown 0.2s ease-out;

      .selected-count {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        color: var(--chat-accent);
        font-weight: 500;

        .count-badge {
          background: var(--chat-accent);
          color: white;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11px;
          font-weight: 600;
        }

        .count-text {
          font-size: 12px;
        }
      }

      .control-buttons {
        display: flex;
        gap: 8px;

        .control-btn {
          padding: 6px 12px;
          border: none;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;

          &.secondary {
            background: var(--chat-bg-tertiary);
            color: var(--chat-text-secondary);

            &:hover {
              background: var(--chat-border);
              color: var(--chat-text-primary);
            }
          }

          &.primary {
            background: var(--chat-accent);
            color: white;

            &:hover {
              background: var(--chat-accent-hover);
            }
          }
        }
      }
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  .selector-content {
    flex: 1;
    overflow-y: auto;
    background: var(--chat-bg-primary);

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--chat-bg-secondary);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--chat-border);
      border-radius: 3px;

      &:hover {
        background: var(--chat-text-muted);
      }
    }

    .main-options {
      padding: 12px 8px;
      border-bottom: 1px solid var(--chat-border);
      background: var(--chat-bg-secondary);

      .main-option {
        width: 100%;
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        margin-bottom: 4px;
        border: none;
        background: var(--chat-bg-primary);
        color: var(--chat-text-primary);
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;
        font-weight: 500;
        text-align: left;

        .option-icon {
          width: 18px;
          height: 18px;
          color: var(--chat-accent);
          flex-shrink: 0;
        }

        .option-text {
          flex: 1;
        }

        &:hover {
          background: var(--chat-accent-alpha);
          color: var(--chat-accent);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.active {
          background: var(--chat-accent);
          color: white;
          box-shadow: 0 2px 8px var(--chat-accent-alpha);

          .option-icon {
            color: white;
          }
        }
      }
    }

    .content-section {
      .section-title {
        padding: 12px 16px 8px;
        font-size: 13px;
        font-weight: 600;
        color: var(--chat-text-secondary);
        background: var(--chat-bg-secondary);
        border-bottom: 1px solid var(--chat-border);
        margin: 0;
        position: sticky;
        top: 0;
        z-index: 1;
      }

      .items-container {
        padding: 8px;
      }
    }

    .option-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 10px 12px;
      margin-bottom: 2px;
      cursor: pointer;
      transition: all 0.2s ease;
      border-radius: 6px;
      position: relative;

      &:hover {
        background: var(--chat-bg-secondary);
        transform: translateX(2px);
      }

      &.selected {
        background: var(--chat-accent-alpha);
        border-left: 3px solid var(--chat-accent);
        padding-left: 9px;
      }

      &.multi-selected {
        background: var(--chat-accent-alpha);
        border-left: 3px solid var(--chat-accent);
        padding-left: 9px;

        .checkbox {
          background: var(--chat-accent);
          border-color: var(--chat-accent);
          color: white;
        }
      }

      .selection-indicator {
        flex-shrink: 0;

        .checkbox {
          width: 18px;
          height: 18px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px solid var(--chat-border);
          border-radius: 4px;
          background: var(--chat-bg-primary);
          transition: all 0.2s ease;
          cursor: pointer;

          &.checked {
            background: var(--chat-accent);
            border-color: var(--chat-accent);
            color: white;
          }

          svg {
            width: 12px;
            height: 12px;
          }
        }
      }

      .item-icon {
        width: 18px;
        height: 18px;
        color: var(--chat-text-secondary);
        flex-shrink: 0;
      }

      .item-content {
        flex: 1;
        min-width: 0;

        .item-name-line {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 2px;
        }

        .item-name {
          font-weight: 500;
          color: var(--chat-text-primary);
          font-size: 14px;
        }

        .item-meta, .item-desc {
          font-size: 12px;
          color: var(--chat-text-secondary);
          line-height: 1.4;
        }

        .item-desc {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100%;
        }
      }
    }

    .template-group, .volume-group {
      margin-bottom: 12px;

      .template-header, .volume-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        background: var(--chat-bg-tertiary);
        border-radius: 4px;
        font-size: 13px;
        font-weight: 600;
        color: var(--chat-text-primary);
        margin-bottom: 4px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: var(--chat-border);
          transform: translateY(-1px);
        }

        .expand-icon {
          width: 14px;
          height: 14px;
          color: var(--chat-text-secondary);
          transition: transform 0.2s ease;
          flex-shrink: 0;

          &.expanded {
            transform: rotate(90deg);
            color: var(--chat-accent);
          }
        }

        .template-icon, .volume-icon {
          width: 16px;
          height: 16px;
          color: var(--chat-accent);
          flex-shrink: 0;
        }

        .template-name, .volume-name {
          flex: 1;
        }

        .template-count, .volume-count {
          font-size: 11px;
          color: var(--chat-text-secondary);
          background: var(--chat-bg-secondary);
          padding: 2px 6px;
          border-radius: 10px;
        }
      }

      .template-entities, .volume-chapters {
        animation: slideDown 0.2s ease-out;
        overflow: hidden;
      }

      .entity-item, .chapter-item {
        margin-left: 8px;
        border-left: 2px solid var(--chat-border);
        padding-left: 16px;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: -6px;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 4px;
          background: var(--chat-border);
          border-radius: 50%;
        }

        &:hover::before {
          background: var(--chat-accent);
        }

        &.multi-selected::before {
          background: var(--chat-accent);
        }
      }
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        max-height: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        max-height: 1000px;
        transform: translateY(0);
      }
    }

    .template-badge {
      background: var(--chat-accent-alpha);
      color: var(--chat-accent);
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 8px;
      font-weight: 600;
      flex-shrink: 0;
    }

    .chapter-order {
      background: var(--chat-success);
      color: white;
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 8px;
      font-weight: 600;
      flex-shrink: 0;
    }

    .scene-count {
      background: var(--chat-warning);
      color: white;
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 8px;
      font-weight: 600;
      flex-shrink: 0;
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: var(--chat-text-muted);

      .empty-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 12px;
        opacity: 0.5;
      }

      .empty-text {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

/* 主题特定的变量覆盖 */
.entity-selector.theme-light {
  --chat-bg-primary: #ffffff;
  --chat-bg-secondary: #f8f9fa;
  --chat-bg-tertiary: #e9ecef;
  --chat-border: #dee2e6;
  --chat-text-primary: #212529;
  --chat-text-secondary: #6c757d;
  --chat-text-muted: #adb5bd;
  --chat-text-placeholder: #9ca3af;
  --chat-accent: #0d6efd;
  --chat-accent-hover: #0b5ed7;
  --chat-accent-alpha: rgba(13, 110, 253, 0.1);
  --chat-success: #198754;
  --chat-warning: #ffc107;
  --chat-danger: #dc3545;
  --chat-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.entity-selector.theme-dark {
  --chat-bg-primary: #1a1a1a;
  --chat-bg-secondary: #2d2d2d;
  --chat-bg-tertiary: #404040;
  --chat-border: #404040;
  --chat-text-primary: #ffffff;
  --chat-text-secondary: #b3b3b3;
  --chat-text-muted: #808080;
  --chat-text-placeholder: #666666;
  --chat-accent: #4dabf7;
  --chat-accent-hover: #339af0;
  --chat-accent-alpha: rgba(77, 171, 247, 0.15);
  --chat-success: #51cf66;
  --chat-warning: #ffd43b;
  --chat-danger: #ff6b6b;
  --chat-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}
</style>
