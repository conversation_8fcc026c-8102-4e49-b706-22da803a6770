<template>
  <el-dialog
      v-model="dialogVisible"
      title="关系图谱"
      class="fullscreen-graph-dialog"
      fullscreen
      destroy-on-close
      :show-close="false"
      :lock-scroll="true"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
  >
    <div class="dialog-content-wrapper">
      <!-- 优化后的顶部工具栏 -->
      <div class="graph-toolbar-optimized">
        <!-- 左侧：标题和主要控制 -->
        <div class="toolbar-left">
          <div class="title-area">
            <h2 class="dialog-title">人物关系图谱</h2>
            <div class="book-info" v-if="bookTitle">
              <span class="book-name">{{ bookTitle }}</span>
            </div>
          </div>

          <div class="controls-area">
            <!-- 视图模式 -->
            <div class="control-group">
              <el-radio-group v-model="viewMode" @change="handleModeChange" size="small" class="mode-switch">
                <el-radio-button value="relations">
                  <el-icon><Connection /></el-icon>
                  关系视图
                </el-radio-button>
                <el-radio-button value="templates">
                  <el-icon><InfoFilled /></el-icon>
                  模板筛选
                </el-radio-button>
              </el-radio-group>
            </div>

            <!-- 动态控制区域 -->
            <div class="dynamic-controls">
              <!-- 模板选择 -->
              <div v-if="viewMode === 'templates'" class="control-item">
                <el-select
                  v-model="selectedTemplateIds"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  placeholder="选择角色类型"
                  size="small"
                  class="template-selector"
                  @change="handleTemplateChange"
                  style="min-width: 200px;"
                >
                  <el-option
                    v-for="template in props.templates"
                    :key="template.id"
                    :label="template.name"
                    :value="template.id"
                  >
                    <div class="template-option">
                      <span class="template-color-dot" :style="{ background: getEntityColor(template.name) }"></span>
                      <span>{{ template.name }}</span>
                    </div>
                  </el-option>
                </el-select>
              </div>

              <!-- 中心节点选择 -->
              <div v-if="viewMode === 'relations'" class="control-item center-node-control">
                <el-select
                  v-model="centerNodeId"
                  placeholder="选择中心角色"
                  filterable
                  clearable
                  size="small"
                  class="center-node-selector"
                  @change="handleCenterNodeChange"
                  :disabled="filteredEntityList.length === 0"
                  style="min-width: 180px;"
                >
                  <el-option-group label="主要角色" v-if="filteredMainCharacters.length > 0">
                    <el-option
                      v-for="entity in filteredMainCharacters"
                      :key="entity.id"
                      :label="entity.name"
                      :value="entity.id"
                    >
                      <div class="entity-option">
                        <span class="entity-color-dot" :style="{ background: getEntityColor(entity.name) }"></span>
                        <span>{{ entity.name }}</span>
                      </div>
                    </el-option>
                  </el-option-group>

                  <el-option-group label="所有角色">
                    <el-option
                      v-for="entity in filteredEntityList"
                      :key="entity.id"
                      :label="entity.name"
                      :value="entity.id"
                    >
                      <div class="entity-option">
                        <span class="entity-color-dot" :style="{ background: getEntityColor(entity.name) }"></span>
                        <span>{{ entity.name }}</span>
                      </div>
                    </el-option>
                  </el-option-group>
                </el-select>

                <el-button
                  v-if="centerNodeId"
                  size="small"
                  @click="clearCenterNode"
                  class="clear-center-btn"
                  type="info"
                  plain
                >
                  清除
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：操作按钮 -->
        <div class="toolbar-right">
          <!-- 布局控制 -->
          <div class="layout-control">
            <span class="control-label">布局</span>
            <el-radio-group
              v-model="graphLayout"
              @change="updateGraphLayout"
              size="small"
              class="layout-buttons"
            >
              <el-radio-button value="force">
                力导向
              </el-radio-button>
              <el-radio-button value="hierarchical">
                层次
              </el-radio-button>
              <el-radio-button value="radial">
                辐射
              </el-radio-button>
              <el-radio-button value="circular">
                环形
              </el-radio-button>
              <el-radio-button value="grid">
                网格
              </el-radio-button>
            </el-radio-group>
          </div>

          <!-- 操作按钮组 -->
          <div class="action-buttons">
            <el-button
              @click="addNewRelation"
              type="primary"
              size="small"
              class="add-relation-btn"
            >
              <el-icon><Plus /></el-icon>
              添加关系
            </el-button>

            <el-button-group size="small" class="tool-buttons">
              <el-tooltip content="重置视图" placement="bottom">
                <el-button @click="resetGraph">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip content="适应窗口" placement="bottom">
                <el-button @click="fitView">
                  <el-icon><Position /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip content="显示图例" placement="bottom">
                <el-button @click="showLegend">
                  <el-icon><InfoFilled /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip content="导出图谱" placement="bottom">
                <el-button @click="exportGraph" :loading="exporting">
                  <el-icon><Download /></el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>

            <el-button
              @click="closeDialog"
              size="small"
              type="info"
              class="close-btn"
            >
              <el-icon><Back /></el-icon>
              关闭
            </el-button>
          </div>
        </div>
      </div>

      <!-- 图谱容器 -->
      <div class="fullscreen-graph-container">
        <div ref="graphContainer" class="graph-content"></div>
        
        <!-- 没有数据时的提示 -->
        <div v-if="!hasRelations" class="empty-graph">
          <el-icon class="empty-icon"><Connection /></el-icon>
          <div class="empty-text">暂无关系数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-overlay">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <span>加载中...</span>
        </div>
      </div>
    </div>

    <!-- 图例说明对话框 -->
    <el-dialog
      v-model="legendDialogVisible"
      title="图谱图例说明"
      width="500px"
      align-center
    >
      <div class="graph-legend-dialog">
        <div class="legend-section">
          <h4 class="legend-section-title">关系类型</h4>
          <div class="legend-items">
            <div class="legend-item">
              <div class="connection-line friendly"></div>
              <span>友好关系</span>
            </div>
            <div class="legend-item">
              <div class="connection-line hostile"></div>
              <span>敌对关系</span>
            </div>
            <div class="legend-item">
              <div class="connection-line family"></div>
              <span>血缘关系</span>
            </div>
            <div class="legend-item">
              <div class="connection-line romance"></div>
              <span>恋爱关系</span>
            </div>
            <div class="legend-item">
              <div class="connection-line other"></div>
              <span>其他关系</span>
            </div>
          </div>
        </div>
        
        <div class="legend-section">
          <h4 class="legend-section-title">关系方向</h4>
          <div class="legend-items">
            <div class="legend-item">
              <div class="connection-line unidirectional">
                <div class="arrow-end"></div>
              </div>
              <span>单向关系</span>
            </div>
            <div class="legend-item">
              <div class="connection-line bidirectional">
                <div class="arrow-start"></div>
                <div class="arrow-end"></div>
              </div>
              <span>双向关系</span>
            </div>
          </div>
        </div>
        
        <!-- 模板模式下的节点图例 -->
        <div v-if="viewMode === 'templates' && selectedTemplateIds.length > 0" class="legend-section">
          <h4 class="legend-section-title">模板筛选</h4>
          <div class="legend-items">
            <div class="legend-item">
              <div class="node-sample selected"></div>
              <span>选中模板的实体</span>
            </div>
            <div class="legend-item">
              <div class="node-sample related"></div>
              <span>关联实体</span>
            </div>
          </div>
        </div>
        
        <!-- 添加中心节点说明到图例 -->
        <div v-if="centerNodeId" class="legend-section">
          <h4 class="legend-section-title">中心角色</h4>
          <div class="legend-items">
            <div class="legend-item">
              <div class="node-sample center-node"></div>
              <span>中心角色</span>
            </div>
            <div class="legend-item">
              <div class="node-sample connected-node"></div>
              <span>关联角色</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加导出设置对话框 -->
    <el-dialog
      v-model="exportDialogVisible"
      title="导出设置"
      width="460px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="export-settings">
        <div class="setting-item">
          <span class="setting-label">导出范围</span>
          <el-radio-group v-model="exportSettings.exportMode">
            <el-radio label="visible">可视区域</el-radio>
            <el-radio label="all">全部内容</el-radio>
          </el-radio-group>
        </div>
      
        <div class="setting-item">
          <span class="setting-label">背景颜色</span>
          <el-color-picker v-model="exportSettings.backgroundColor" show-alpha />
        </div>
        
        <div class="setting-item">
          <span class="setting-label">尺寸预设</span>
          <el-select v-model="exportSettings.sizePreset" @change="handleSizePresetChange">
            <el-option label="原始尺寸" value="original" />
            <el-option label="A4 纸张 (横向)" value="a4-landscape" />
            <el-option label="A4 纸张 (纵向)" value="a4-portrait" />
            <el-option label="16:9 屏幕" value="16:9" />
            <el-option label="方形 (1:1)" value="square" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </div>
        
        <div class="setting-item" v-if="exportSettings.sizePreset === 'custom'">
          <div class="custom-size">
            <div class="size-input">
              <span>宽度 (px)</span>
              <el-input-number v-model="exportSettings.width" :min="100" :max="3000" size="small" />
            </div>
            <div class="size-input">
              <span>高度 (px)</span>
              <el-input-number v-model="exportSettings.height" :min="100" :max="3000" size="small" />
            </div>
          </div>
        </div>
        
        <div class="setting-item">
          <span class="setting-label">图片质量</span>
          <el-select v-model="exportSettings.quality">
            <el-option label="标准" value="1" />
            <el-option label="高清" value="2" />
            <el-option label="超清" value="3" />
          </el-select>
        </div>
        
        <div class="setting-item">
          <el-checkbox v-model="exportSettings.fitAllNodes">自动适应所有节点</el-checkbox>
        </div>
        
        <div class="setting-item">
          <span class="setting-label">节点大小</span>
          <div class="slider-with-value">
            <el-slider 
              v-model="exportSettings.nodeScale" 
              :min="0.5" 
              :max="3" 
              :step="0.1" 
              :format-tooltip="val => `${Math.round(val * 100)}%`"
              class="node-scale-slider"
            />
            <div class="scale-value">{{ Math.round(exportSettings.nodeScale * 100) }}%</div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="exportDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="doExportGraph" :loading="exporting">
            导出图片
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick, onUnmounted } from 'vue';
import { useBookStore } from '@/stores/book';
import * as echarts from 'echarts';
import { ElMessage } from 'element-plus';
import html2canvas from 'html2canvas';
import { 
  Refresh, 
  Connection, 
  Loading, 
  Position, 
  Back,
  Plus,
  Download,
  InfoFilled
} from '@element-plus/icons-vue';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  relations: {
    type: Array,
    default: () => []
  },
  entityList: {
    type: Array,
    default: () => []
  },
  bookId: {
    type: String,
    default: ''
  },
  templates: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['update:visible', 'node-click', 'edge-click', 'add-relation']);

// 本地状态
const dialogVisible = ref(props.visible);
const graphContainer = ref(null);
const chart = ref(null);
const loading = ref(false);
const viewMode = ref('relations'); // 默认为关系实体模式
const selectedTemplateIds = ref([]); // 选中的模板IDs
const centerNodeId = ref(null);
const connectedNodeIds = ref(new Set());
const exporting = ref(false);
const exportDialogVisible = ref(false);
const exportSettings = ref({
  backgroundColor: 'rgba(0, 0, 0, 0.9)', // 默认背景色
  sizePreset: 'original',
  width: 1920,
  height: 1080,
  quality: '2',
  fitAllNodes: true,
  nodeScale: 1.5, // 默认节点放大到150%
  exportMode: 'visible'
});
const graphLayout = ref('force'); // 默认为力导向布局
const legendDialogVisible = ref(false);

// Store
const bookStore = useBookStore();

// 计算属性
const hasRelations = computed(() => props.relations && props.relations.length > 0);
const bookTitle = computed(() => {
  const book = bookStore.bookList.find(b => b.id === props.bookId);
  return book ? book.title : '';
});

const filteredEntityList = computed(() => {
  if (!props.entityList || props.entityList.length === 0) {
    return [];
  }

  if (viewMode.value === 'relations') {
    // 关系模式：显示所有实体（不进行过滤）
    console.log(`关系模式：显示所有 ${props.entityList.length} 个实体`);
    return props.entityList;

  } else if (viewMode.value === 'templates') {
    // 模板模式：只显示选中模板类型的实体
    if (selectedTemplateIds.value.length === 0) {
      console.log('模板模式：未选择任何模板');
      return [];
    }

    // 找出所有选中模板类型的实体
    const result = props.entityList.filter(entity =>
      selectedTemplateIds.value.includes(entity.template_id)
    );

    if (result.length === 0) {
      console.log('模板模式：选中的模板没有对应的实体');
      return [];
    }

    console.log(`模板模式：显示 ${result.length} 个选中模板类型的实体`);
    return result;
  }

  return [];
});

const filteredRelations = computed(() => {
  if (!props.relations || props.relations.length === 0) {
    return [];
  }

  if (viewMode.value === 'relations') {
    // 关系模式下显示所有关系
    console.log(`关系模式：显示所有 ${props.relations.length} 个关系`);
    return props.relations;

  } else if (viewMode.value === 'templates') {
    // 模板模式：显示涉及到过滤后实体的所有关系
    if (selectedTemplateIds.value.length === 0) {
      return [];
    }

    // 获取当前过滤后的实体ID集合
    const filteredEntityIds = new Set(filteredEntityList.value.map(e => e.id));

    // 只显示两端都在过滤后实体中的关系
    const result = props.relations.filter(relation =>
      filteredEntityIds.has(relation.source) && filteredEntityIds.has(relation.target)
    );

    console.log(`模板模式：从 ${props.relations.length} 个关系中过滤出 ${result.length} 个相关关系`);
    return result;
  }

  return [];
});

// 识别主要角色（关系数量最多的前5个）
const filteredMainCharacters = computed(() => {
  // 统计每个实体的关系数量
  const entityRelationCounts = {};
  filteredRelations.value.forEach(relation => {
    entityRelationCounts[relation.source] = (entityRelationCounts[relation.source] || 0) + 1;
    entityRelationCounts[relation.target] = (entityRelationCounts[relation.target] || 0) + 1;
  });
  
  // 将实体按关系数量排序并取前5个
  return filteredEntityList.value
    .filter(entity => entityRelationCounts[entity.id] > 0)
    .sort((a, b) => (entityRelationCounts[b.id] || 0) - (entityRelationCounts[a.id] || 0))
    .slice(0, 5);
});

// 简化监听visible变化的逻辑，避免多个监听器相互干扰
watch(
  () => props.visible,
  (newVal) => {
    console.log('父组件visible变化:', newVal);
    dialogVisible.value = newVal;
    if (newVal) {
      // 禁止body滚动
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
      document.body.style.height = '100%';

      // 仅在弹窗打开时初始化图表
      nextTick(() => {
        setTimeout(() => {
          initGraph();
        }, 300);
      });
    } else {
      // 恢复body滚动
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.height = '';
    }
  }
);

// 简化监听dialogVisible的逻辑，只保留一个监听器
watch(
  () => dialogVisible.value,
  (newVal) => {
    console.log('dialogVisible变化:', newVal);
    // 确保将状态同步回父组件
    emit('update:visible', newVal);

    if (newVal) {
      // 禁止body滚动
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
      document.body.style.height = '100%';
    } else {
      // 恢复body滚动
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.height = '';

      // 弹窗关闭时清理资源
      if (chart.value) {
        chart.value.dispose();
        chart.value = null;
      }
      window.removeEventListener('resize', handleResize);
    }
  }
);

// 监听数据变化，实时更新图表
watch(
  [() => props.entityList, () => props.relations],
  ([newEntityList, newRelations], [oldEntityList, oldRelations]) => {
    console.log('数据变化检测:', {
      实体数量变化: `${oldEntityList?.length || 0} -> ${newEntityList?.length || 0}`,
      关系数量变化: `${oldRelations?.length || 0} -> ${newRelations?.length || 0}`
    });

    // 只有在对话框可见且图表已初始化时才更新
    if (dialogVisible.value && chart.value) {
      console.log('数据变化，更新图表');
      updateGraphData();
    }
  },
  {
    deep: true, // 深度监听数组内容变化
    immediate: false // 不立即执行，避免与初始化冲突
  }
);

// 监听模板数据变化
watch(
  () => props.templates,
  (newTemplates, oldTemplates) => {
    console.log('模板数据变化:', {
      模板数量变化: `${oldTemplates?.length || 0} -> ${newTemplates?.length || 0}`
    });

    // 如果在模板模式下且图表已初始化，则更新图表
    if (dialogVisible.value && chart.value && viewMode.value === 'templates') {
      console.log('模板数据变化，更新图表');
      updateGraphData();
    }
  },
  {
    deep: true,
    immediate: false
  }
);

// 修改初始化图表的方法
const initGraph = () => {
  loading.value = true;
  console.log('开始初始化图表');
  
  // 先检查数据是否有效
  if (!props.relations || props.relations.length === 0) {
    console.warn('没有关系数据，无法初始化图表');
    loading.value = false;
    return;
  }

  if (!props.entityList || props.entityList.length === 0) {
    console.warn('没有角色数据，无法初始化图表');
    loading.value = false;
    return;
  }
  
  // 延迟初始化，确保DOM已经完全渲染
  setTimeout(() => {
    if (!graphContainer.value) {
      console.error('图表容器不存在，无法初始化图表');
      loading.value = false;
      return;
    }
    
    // 如果已存在图表，先销毁
    if (chart.value) {
      chart.value.dispose();
      chart.value = null;
    }
    
    // 确保容器有宽高
    const containerWidth = graphContainer.value.clientWidth;
    const containerHeight = graphContainer.value.clientHeight;

    if (containerWidth === 0 || containerHeight === 0) {
      console.warn('图表容器尺寸为0，设置最小尺寸');
      // 设置最小尺寸，确保固定布局
      graphContainer.value.style.width = '100%';
      graphContainer.value.style.height = 'calc(100vh - 64px)'; // 减去工具栏高度
    }

    console.log('图表容器尺寸:', {
      width: containerWidth || graphContainer.value.clientWidth,
      height: containerHeight || graphContainer.value.clientHeight,
      viewportHeight: window.innerHeight
    });
    
    // 初始化图表
    try {
      console.log('创建图表实例，容器尺寸:', {
        width: containerWidth || graphContainer.value.clientWidth,
        height: containerHeight || graphContainer.value.clientHeight
      });
      
      chart.value = echarts.init(graphContainer.value);
      
      // 设置图表配置
      updateGraphData();
      
      // 添加窗口大小变化监听
      window.addEventListener('resize', handleResize);
      
      // 添加额外的事件监听
      chart.value.on('rendered', () => {
        // console.log('图表渲染完成');
      });
      
      loading.value = false;
    } catch (error) {
      console.error('初始化图表时出错:', error);
      loading.value = false;
    }
  }, 500); // 延长延迟时间，确保DOM渲染完成
};

// 防抖处理resize事件
let resizeTimer = null;
const handleResize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }

  resizeTimer = setTimeout(() => {
    if (chart.value && graphContainer.value) {
      // 确保容器尺寸正确
      const containerHeight = window.innerHeight - 64; // 减去工具栏高度
      graphContainer.value.style.height = `${containerHeight}px`;

      // 调整图表大小
      chart.value.resize();

      console.log('窗口大小变化，调整图表尺寸:', {
        containerHeight,
        viewportHeight: window.innerHeight
      });
    }
  }, 100); // 100ms防抖
};

// 修改关闭对话框的方法，确保状态正确更新
const closeDialog = () => {
  console.log('关闭全屏弹窗按钮点击');
  // 先销毁图表实例释放资源
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }

  // 移除事件监听
  window.removeEventListener('resize', handleResize);

  // 恢复body滚动
  document.body.style.overflow = '';
  document.body.style.position = '';
  document.body.style.width = '';
  document.body.style.height = '';

  // 最后设置状态为false
  dialogVisible.value = false;
};

// 统计实体关系数量
const countEntityRelations = (entityId) => {
  return props.relations.filter(
    relation => relation.source === entityId || relation.target === entityId
  ).length;
};

// 根据实体名称生成颜色
const getEntityColor = (name) => {
  if (!name) return '#409EFF';
  
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  const hue = Math.abs(hash) % 360;
  return `hsl(${hue}, 70%, 60%)`;
};

// 处理模式变更
const handleModeChange = () => {
  console.log('模式切换到:', viewMode.value);

  // 清除连接节点状态，但保留中心节点选择
  connectedNodeIds.value = new Set();

  if (viewMode.value === 'relations') {
    // 关系模式：清空模板选择
    selectedTemplateIds.value = [];
    // 重新渲染图谱显示所有关系
    updateGraphData();
  } else if (viewMode.value === 'templates') {
    // 模板模式：如果没有选择模板，显示提示
    if (selectedTemplateIds.value.length === 0) {
      ElMessage.info('请选择角色模板来查看相关关系');
      return;
    }
    // 重新渲染图谱显示选中模板的关系
    updateGraphData();
  }
};

// 处理模板选择变更
const handleTemplateChange = () => {
  console.log('模板选择变更:', selectedTemplateIds.value);

  // 清除中心节点选择
  centerNodeId.value = null;
  connectedNodeIds.value = new Set();

  if (selectedTemplateIds.value.length === 0) {
    // 如果没有选择任何模板，显示提示
    ElMessage.info('请至少选择一个角色模板');
    return;
  }

  // 重新渲染图谱
  updateGraphData();
};

// 添加关系的方法
const addNewRelation = () => {
  emit('add-relation');
};

// 处理中心节点变化
const handleCenterNodeChange = (nodeId) => {
  console.log('中心节点变更:', nodeId, '类型:', typeof nodeId, '当前中心节点:', centerNodeId.value, '类型:', typeof centerNodeId.value);

  // 如果nodeId为null或undefined，说明是清除操作（用户点击了清除按钮）
  if (!nodeId) {
    if (centerNodeId.value) {
      // 只有当前有中心节点时才清除
      clearCenterNode();
    }
    return;
  }

  // 如果选择的是当前中心节点，不做任何操作
  if (nodeId === centerNodeId.value) {
    console.log('选择的是当前中心节点，无需更改');
    // 但是如果连接节点为空，说明需要重新计算连接关系
    if (connectedNodeIds.value.size === 0) {
      console.log('连接节点为空，重新计算连接关系');
    } else {
      return;
    }
  }

  // 设置新的中心节点
  centerNodeId.value = nodeId;

  // 找出与中心节点直接相连的所有节点
  connectedNodeIds.value = new Set();
  const centerNodeRelations = [];

  console.log(`🔍 查找中心节点 ${nodeId} 的连接关系，总关系数: ${filteredRelations.value.length}`);
  console.log('所有关系:', filteredRelations.value);

  filteredRelations.value.forEach(relation => {
    console.log(`检查关系: ${relation.source} -> ${relation.target}`);
    if (relation.source === nodeId) {
      console.log(`✅ 找到出向关系: ${nodeId} -> ${relation.target}`);
      connectedNodeIds.value.add(relation.target);
      centerNodeRelations.push(relation);
    }
    if (relation.target === nodeId) {
      console.log(`✅ 找到入向关系: ${relation.source} -> ${nodeId}`);
      connectedNodeIds.value.add(relation.source);
      centerNodeRelations.push(relation);
    }
  });

  console.log(`中心节点 ${nodeId} 连接了 ${connectedNodeIds.value.size} 个节点，${centerNodeRelations.length} 个关系`);

  // 显示连接信息
  const centerEntity = filteredEntityList.value.find(e => e.id === nodeId);
  if (centerEntity) {
    if (connectedNodeIds.value.size > 0) {
      ElMessage.success(`已选择 "${centerEntity.name}" 为中心节点，高亮显示 ${connectedNodeIds.value.size} 个相关角色`);
    } else {
      ElMessage.info(`已选择 "${centerEntity.name}" 为中心节点，但该角色暂无关系连接`);
    }
  }

  // 重新初始化图表以反映中心节点
  initGraph();
};

// 清除中心节点
const clearCenterNode = () => {
  console.log('清除中心节点');
  centerNodeId.value = null;
  connectedNodeIds.value = new Set();
  ElMessage.info('已清除中心节点选择，显示所有关系');
  initGraph();
};

// 在组件挂载时初始化
onMounted(() => {
  if (dialogVisible.value) {
    // 延迟初始化，确保DOM已渲染
    setTimeout(() => {
      initGraph();
    }, 500);
  }
});

// 组件卸载时清理
onUnmounted(() => {
  console.log('全屏弹窗组件卸载');
  window.removeEventListener('resize', handleResize);
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }

  // 确保恢复body样式
  document.body.style.overflow = '';
  document.body.style.position = '';
  document.body.style.width = '';
  document.body.style.height = '';
});

// 添加回缺失的updateGraphData函数及其依赖
const updateGraphData = () => {
  // 验证数据是否存在
  if (!chart.value) {
    console.error('图表实例不存在，无法更新数据');
    loading.value = false;
    return;
  }
  
  // 检查是否有关系数据
  if (!hasRelations.value) {
    console.warn('没有关系数据可显示');
    loading.value = false;
    return;
  }
  
  // 检查是否有实体数据
  if (!filteredEntityList.value || filteredEntityList.value.length === 0) {
    console.warn('没有角色数据可显示');
    loading.value = false;
    return;
  }
  
  console.log('准备更新图表数据:', {
    实体数量: filteredEntityList.value.length,
    关系数量: filteredRelations.value.length
  });
  
  try {
    // 准备图表数据
    const nodes = prepareNodes();
    const edges = prepareEdges();
    
    // 检查处理后的数据是否有效
    if (!nodes.length || !edges.length) {
      console.warn('处理后的节点或边数据为空，无法绘制图表');
      loading.value = false;
      return;
    }
    
    // 使用新的配置生成函数
    const option = createGraphOption(nodes, edges, graphLayout.value);

    console.log('图表配置:', option);
    console.log('节点标签配置:', option.series[0].label);

    // 设置图表选项
    chart.value.setOption(option);

    // 绑定事件
    bindGraphEvents();
    
    console.log('图表数据更新成功');
    
    // 如果有中心节点，进行额外处理使其居中
    if (centerNodeId.value) {
      // 延迟处理以确保布局已经初始化
      setTimeout(() => {
        // 找到中心节点的图表实例
        const centerNodeIndex = chart.value.getModel().getSeriesByIndex(0).getData().indexOfName(
          props.entityList.find(e => e.id === centerNodeId.value)?.name
        );
        
        if (centerNodeIndex >= 0) {
          const centerNode = chart.value.getModel().getSeriesByIndex(0).getData().getItemGraphicEl(centerNodeIndex);
          
          if (centerNode) {
            // 使用dispatchAction将视图居中到中心节点
            chart.value.dispatchAction({
              type: 'graphRoam',
              zoom: 1.2,
              originX: centerNode.position[0],
              originY: centerNode.position[1]
            });
          }
        }
      }, 1000); // 延迟1秒执行，给力导向图布局足够的时间
    }
  } catch (error) {
    console.error('更新图表数据时发生错误:', error);
    loading.value = false;
  }
};

// 添加回缺失的prepareNodes函数
const prepareNodes = () => {
  console.log('准备节点数据，当前中心节点ID:', centerNodeId.value);
  console.log('连接节点IDs:', Array.from(connectedNodeIds.value));

  const nodes = filteredEntityList.value.map(entity => {
    // 计算节点大小 - 基于关系数量
    const relCount = countEntityRelations(entity.id);
    const symbolSize = Math.max(50, Math.min(80, 50 + relCount * 5));
    
    // 检查是否是选中模板的实体
    const isSelectedTemplateEntity = viewMode.value === 'templates' && 
      selectedTemplateIds.value.includes(entity.template_id);
    
    // 添加中心节点和相连节点的判断逻辑
    const isCenterNode = entity.id === centerNodeId.value;
    const isConnectedNode = connectedNodeIds.value.has(entity.id);

    // 调试ID比较
    if (centerNodeId.value && entity.id.toString() === centerNodeId.value.toString()) {
      console.log(`🔍 ID比较: entity.id=${entity.id} (${typeof entity.id}), centerNodeId=${centerNodeId.value} (${typeof centerNodeId.value}), 严格相等=${entity.id === centerNodeId.value}, 字符串相等=${entity.id.toString() === centerNodeId.value.toString()}`);
    }

    // 调试信息
    if (isCenterNode) {
      console.log(`✅ 中心节点: ${entity.name} (${entity.id}), centerNodeId: ${centerNodeId.value}`);
    }
    if (isConnectedNode) {
      console.log(`🔗 连接节点: ${entity.name} (${entity.id})`);
    }
    if (!isCenterNode && !isConnectedNode && centerNodeId.value) {
      console.log(`⚪ 普通节点: ${entity.name} (${entity.id})`);
    }
    
    // 根据实体类型/名称获取颜色
    const baseColor = getEntityColor(entity.name);
    
    // 调整不同节点类型的尺寸
    let finalSize = symbolSize;
    if (isCenterNode) {
      finalSize = symbolSize * 1.5; // 中心节点更大
    } else if (isConnectedNode) {
      finalSize = symbolSize * 1.2; // 连接节点稍大
    } else if (centerNodeId.value && !isConnectedNode) {
      finalSize = symbolSize * 0.8; // 非连接节点稍小
    }
    
    const nodeData = {
      id: entity.id,
      name: entity.name,
      value: relCount,
      itemStyle: {
        // 调整颜色和边框样式基于节点类型
        color: isCenterNode ? {
          type: 'radial',
          x: 0.5,
          y: 0.5,
          r: 0.5,
          colorStops: [
            { offset: 0, color: '#FFF9C4' },
            { offset: 0.7, color: '#FFC107' },
            { offset: 1, color: '#FF8F00' }
          ]
        } : isConnectedNode ? {
          type: 'radial',
          x: 0.5,
          y: 0.5,
          r: 0.5,
          colorStops: [
            { offset: 0, color: lightenColor(baseColor, 30) },
            { offset: 0.7, color: baseColor },
            { offset: 1, color: darkenColor(baseColor, 15) }
          ]
        } : {
          type: 'radial',
          x: 0.5,
          y: 0.5,
          r: 0.5,
          colorStops: [
            { offset: 0, color: lightenColor(baseColor, 20) },
            { offset: 0.7, color: baseColor },
            { offset: 1, color: darkenColor(baseColor, 20) }
          ]
        },
        borderWidth: isCenterNode ? 4 : (isConnectedNode ? 3 : 2),
        borderColor: isCenterNode ? 'rgba(255, 215, 0, 0.9)' : (isConnectedNode ? 'rgba(255, 255, 255, 0.8)' : 'rgba(255, 255, 255, 0.6)'),
        shadowColor: isCenterNode ? 'rgba(255, 193, 7, 0.6)' : 'rgba(0, 0, 0, 0.3)',
        shadowBlur: isCenterNode ? 20 : 15,
        shadowOffsetX: 2,
        shadowOffsetY: 2
      },
      // 调整节点大小
      symbolSize: finalSize,
      emphasis: {
        itemStyle: {
          borderWidth: isCenterNode ? 5 : (isConnectedNode ? 4 : 3),
          shadowBlur: isCenterNode ? 25 : 20
        },
        scale: true
      },
      entity: entity,
      isSelectedTemplate: isSelectedTemplateEntity,
      isCenterNode: isCenterNode,
      isConnectedNode: isConnectedNode
    };

    // 调试输出节点数据
    if (isCenterNode) {
      console.log(`🎯 中心节点数据:`, nodeData);
    }

    return nodeData;
  });

  console.log('准备节点数据:', nodes.length, '个节点');
  console.log('节点示例:', nodes[0]);

  // 检查中心节点是否正确标记
  const centerNode = nodes.find(n => n.isCenterNode);
  if (centerNode) {
    console.log('找到中心节点:', centerNode.name, centerNode);
  } else if (centerNodeId.value) {
    console.warn('未找到中心节点，但centerNodeId存在:', centerNodeId.value);
  }

  return nodes;
};

// 添加回缺失的prepareEdges函数
const prepareEdges = () => {
  return filteredRelations.value.map(relation => {
    // 检查该关系是否连接到中心节点
    const isConnectedToCenterNode = centerNodeId.value && 
      (relation.source === centerNodeId.value || relation.target === centerNodeId.value);
    
    // 根据关系类型确定线条样式
    let lineStyle = {
      width: Math.max(2, relation.strength || 1),
      // 对于双向关系增加曲率，避免两个箭头重叠
      curveness: relation.bidirectional ? 0.3 : 0.1, 
      type: relation.bidirectional ? 'solid' : 'dashed',
      opacity: 1
    };
    
    // 确定关系类型颜色
    let relationColor = '#909399';
    switch(relation.type) {
      case '友好':
        relationColor = '#67C23A';
        lineStyle.color = relationColor;
        lineStyle.type = 'solid';
        break;
      case '敌对':
        relationColor = '#F56C6C';
        lineStyle.color = relationColor;
        lineStyle.type = relation.bidirectional ? 'solid' : 'dashed';
        break;
      case '血缘':
        relationColor = '#E6A23C';
        lineStyle.color = relationColor;
        lineStyle.width += 1;
        break;
      case '恋爱':
        relationColor = '#FF88A0';
        lineStyle.color = relationColor;
        lineStyle.type = 'solid';
        break;
      case '师徒':
        relationColor = '#409EFF';
        lineStyle.color = relationColor;
        lineStyle.type = 'solid';
        break;
      default:
        lineStyle.color = relationColor;
    }
    
    // 设置线条颜色
    lineStyle.color = relationColor;
    
    // 根据是否连接到中心节点调整线条样式
    if (isConnectedToCenterNode) {
      lineStyle.width += 2;
      lineStyle.shadowBlur = 10;
      lineStyle.shadowColor = relationColor;
      lineStyle.opacity = 1;
    } else if (centerNodeId.value) {
      lineStyle.opacity = 0.4;
    }
    
    return {
      source: relation.source,
      target: relation.target,
      relationId: relation.id,
      value: relation.strength || 1,
      // 增大线条宽度
      lineStyle: lineStyle,
      // 增强箭头显示
      symbol: relation.bidirectional ? ['arrow', 'arrow'] : ['none', 'arrow'],
      // 增大箭头尺寸
      symbolSize: relation.bidirectional ? [10, 15] : [6, 10], 
      // 确保箭头颜色与线条匹配
      itemStyle: {
        color: relationColor
      },
      label: {
        show: true,
        formatter: relation.type || '未知关系',
        fontSize: 14,
        fontWeight: 'bold',
        backgroundColor: `rgba(40, 40, 40, 0.85)`,
        borderColor: relationColor,
        borderWidth: 2,
        borderRadius: 8,
        padding: [6, 10],
        color: '#fff',
        distance: relation.bidirectional ? 10 : 5, // 双向关系标签距离增加
        align: 'center',
        position: relation.bidirectional ? 'middle' : 'middle',
      },
      emphasis: {
        lineStyle: {
          width: lineStyle.width + 2,
          shadowBlur: isConnectedToCenterNode ? 15 : 10,
          shadowColor: relationColor,
          opacity: 1
        },
        label: {
          fontSize: 16,
          backgroundColor: `rgba(40, 40, 40, 0.95)`,
          borderWidth: 2.5,
          padding: [7, 12],
          shadowBlur: 8
        }
      },
      bidirectional: relation.bidirectional,
      isConnectedToCenterNode: isConnectedToCenterNode,
      tooltip: {
        formatter: formatEdgeTooltip(relation)
      }
    };
  });
};

// 格式化节点tooltip
const formatNodeTooltip = (data) => {
  const entity = props.entityList.find(e => e.id === data.id);
  if (!entity) return '';
  
  let html = `<div class="graph-tooltip">
    <div class="tooltip-title">${entity.name}</div>
    <div class="tooltip-content">
      <p>关系数: ${data.value}</p>`;
  
  if (entity.description) {
    html += `<p class="tooltip-desc">${entity.description}</p>`;
  }
  
  html += `</div></div>`;
  return html;
};

// 格式化边tooltip
const formatEdgeTooltip = (relation) => {
  const sourceEntity = props.entityList.find(e => e.id === relation.source);
  const targetEntity = props.entityList.find(e => e.id === relation.target);
  
  if (!sourceEntity || !targetEntity) return '';
  
  let html = `<div class="graph-tooltip">
    <div class="tooltip-title">${relation.type}关系</div>
    <div class="tooltip-content">
      <p>${sourceEntity.name} → ${targetEntity.name}</p>`;
  
  if (relation.bidirectional) {
    html += `<p class="tooltip-badge">双向关系</p>`;
  }
  
  if (relation.strength) {
    html += `<p>关系强度: ${relation.strength}</p>`;
  }
  
  if (relation.description) {
    html += `<p class="tooltip-desc">${relation.description}</p>`;
  }
  
  if (relation.tags && relation.tags.length > 0) {
    html += `<div class="tooltip-tags">
      ${relation.tags.map(tag => `<span class="tooltip-tag">${tag}</span>`).join('')}
    </div>`;
  }
  
  html += `</div></div>`;
  return html;
};

// 添加颜色处理助手函数
const lightenColor = (color, percent) => {
  // 将HSL颜色字符串转换为更亮的颜色
  if (color.startsWith('hsl(')) {
    const match = color.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);
    if (match) {
      const h = parseInt(match[1]);
      const s = parseInt(match[2]);
      const l = Math.min(100, parseInt(match[3]) + percent);
      return `hsl(${h}, ${s}%, ${l}%)`;
    }
  }
  return color;
};

const darkenColor = (color, percent) => {
  // 将HSL颜色字符串转换为更暗的颜色
  if (color.startsWith('hsl(')) {
    const match = color.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);
    if (match) {
      const h = parseInt(match[1]);
      const s = parseInt(match[2]);
      const l = Math.max(0, parseInt(match[3]) - percent);
      return `hsl(${h}, ${s}%, ${l}%)`;
    }
  }
  return color;
};

// 添加此方法
const handleAddRelation = (nodeId) => {
  if (nodeId) {
    emit('add-relation', nodeId); // 传递节点ID给父组件
  }
};

// 添加图表重置方法
const resetGraph = () => {
  if (chart.value) {
    // 销毁并重新初始化图表
    chart.value.dispose();
    chart.value = null;
    
    // 清除中心节点选择
    centerNodeId.value = null;
    connectedNodeIds.value = new Set();
    
    // 延迟重新初始化以确保DOM更新
    setTimeout(() => {
      initGraph();
    }, 300);
  }
};

// 添加自适应视图方法
const fitView = () => {
  if (chart.value) {
    chart.value.dispatchAction({
      type: 'graphRoam',
      zoom: 1, // 重置缩放级别
      originX: 0,
      originY: 0
    });
    
    // 自动适应内容
    setTimeout(() => {
      const option = chart.value.getOption();
      if (option && option.series && option.series[0]) {
        // 保存当前的布局设置
        const currentForce = option.series[0].force;
        
        // 临时更改为居中布局
        chart.value.setOption({
          series: [{
            roam: true,
            zoom: 1.2,
            layout: 'force',
            force: {
              ...currentForce,
              layoutAnimation: true
            }
          }]
        });
      }
    }, 100);
  }
};

// 处理尺寸预设变化
const handleSizePresetChange = () => {
  const preset = exportSettings.value.sizePreset;
  
  switch (preset) {
    case 'a4-landscape':
      exportSettings.value.width = 2480; // A4 横向 (300dpi)
      exportSettings.value.height = 1754;
      break;
    case 'a4-portrait':
      exportSettings.value.width = 1754; // A4 纵向 (300dpi)
      exportSettings.value.height = 2480;
      break;
    case '16:9':
      exportSettings.value.width = 1920;
      exportSettings.value.height = 1080;
      break;
    case 'square':
      exportSettings.value.width = 1500;
      exportSettings.value.height = 1500;
      break;
  }
};

// 简化 doExportGraph 方法，修复nodeScale未定义的错误
const doExportGraph = async () => {
  if (!chart.value) {
    ElMessage.warning('没有可导出的图谱');
    return;
  }
  
  try {
    exporting.value = true;
    
    // 存储原始状态
    const originalSize = {
      width: graphContainer.value.style.width,
      height: graphContainer.value.style.height
    };
    
    // 提前定义nodeScale，避免未定义错误
    const nodeScale = exportSettings.value.nodeScale;
    
    let imageData;
    
    if (exportSettings.value.exportMode === 'all') {
      console.log('使用全部节点导出模式');
      
      // 创建临时容器
      const tempContainer = document.createElement('div');
      tempContainer.style.position = 'absolute';
      tempContainer.style.left = '-9999px';
      tempContainer.style.top = '-9999px';
      tempContainer.style.width = `${exportSettings.value.width}px`;
      tempContainer.style.height = `${exportSettings.value.height}px`;
      document.body.appendChild(tempContainer);
      
      // 初始化临时图表
      const tempChart = echarts.init(tempContainer, null, {
        renderer: 'canvas',
        width: exportSettings.value.width,
        height: exportSettings.value.height
      });
      
      // 复制当前图表配置
      const option = JSON.parse(JSON.stringify(chart.value.getOption()));
      
      // 调整配置以确保所有节点可见
      if (option.series && option.series.length > 0) {
        // 禁用动画以加快渲染
        option.series[0].animation = false;
        option.series[0].animationDurationUpdate = 0;
        
        // 应用节点大小缩放
        const originalSymbolSize = option.series[0].symbolSize;
        
        if (typeof originalSymbolSize === 'function') {
          // 如果symbolSize是函数，则包装它
          const originalFunc = originalSymbolSize;
          option.series[0].symbolSize = (value, params) => {
            const size = originalFunc(value, params);
            return typeof size === 'number' ? size * nodeScale : size;
          };
        } else if (typeof originalSymbolSize === 'number') {
          // 如果是数字，直接缩放
          option.series[0].symbolSize = originalSymbolSize * nodeScale;
        } else {
          // 如果未定义，设置一个默认值
          option.series[0].symbolSize = 30 * nodeScale;
        }
        
        // 根据节点大小调整力导向图参数
        if (option.series[0].force) {
          option.series[0].force.layoutAnimation = false;
          // 使用二次方公式增加斥力，更好地分开大节点
          option.series[0].force.repulsion = 400 * Math.pow(nodeScale, 2);
          // 使用1.5次方增加边长，提供足够间距
          option.series[0].force.edgeLength = 120 * Math.pow(nodeScale, 1.5);
          // 减小引力，防止节点聚集（节点越大，引力越小）
          option.series[0].force.gravity = 0.1 / Math.max(1, nodeScale);
        }
        
        // 禁用交互和调整缩放
        option.series[0].roam = false;
        option.series[0].zoom = 0.5; // 适当缩小以显示更多内容
      }
      
      // 设置图表选项
      tempChart.setOption(option);
      
      // 等待力导向图布局稳定
      const stabilizationTime = Math.round(600 * Math.max(1, nodeScale / 1.5));
      console.log(`等待布局稳定: ${stabilizationTime}ms，节点缩放: ${nodeScale}`);
      await new Promise(resolve => setTimeout(resolve, stabilizationTime));
      
      // 直接获取图表图像数据
      imageData = tempChart.getDataURL({
        type: 'png',
        pixelRatio: parseInt(exportSettings.value.quality, 10),
        backgroundColor: exportSettings.value.backgroundColor
      });
      
      // 清理临时资源
      tempChart.dispose();
      document.body.removeChild(tempContainer);
      
    } else {
      // 可视区域模式
      console.log('使用可视区域导出模式');
      
      // 在导出前调整节点大小
      const originalOption = chart.value.getOption();
      const tempOption = JSON.parse(JSON.stringify(originalOption));
      
      // 应用节点大小缩放
      if (tempOption.series && tempOption.series.length > 0) {
        const originalSymbolSize = tempOption.series[0].symbolSize;
        
        if (typeof originalSymbolSize === 'function') {
          const originalFunc = originalSymbolSize;
          tempOption.series[0].symbolSize = (value, params) => {
            const size = originalFunc(value, params);
            return typeof size === 'number' ? size * nodeScale : size;
          };
        } else if (typeof originalSymbolSize === 'number') {
          tempOption.series[0].symbolSize = originalSymbolSize * nodeScale;
        } else {
          tempOption.series[0].symbolSize = 30 * nodeScale;
        }
      }
      
      // 应用临时配置
      chart.value.setOption(tempOption);
      
      // 调整容器大小（如果需要）
      if (exportSettings.value.sizePreset !== 'original') {
        graphContainer.value.style.width = `${exportSettings.value.width}px`;
        graphContainer.value.style.height = `${exportSettings.value.height}px`;
        chart.value.resize();
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      
      // 直接从当前图表获取图像
      imageData = chart.value.getDataURL({
        type: 'png',
        pixelRatio: parseInt(exportSettings.value.quality, 10),
        backgroundColor: exportSettings.value.backgroundColor
      });
      
      // 恢复原始配置
      chart.value.setOption(originalOption);
      
      // 恢复原始容器大小
      if (exportSettings.value.sizePreset !== 'original') {
        graphContainer.value.style.width = originalSize.width;
        graphContainer.value.style.height = originalSize.height;
        chart.value.resize();
      }
    }
    
    // 关闭对话框
    exportDialogVisible.value = false;
    
    // 保存图像
    await saveImageToFile(imageData);
    
  } catch (error) {
    console.error('导出图谱时出错:', error);
    ElMessage.error('导出失败：' + (error.message || '未知错误'));
  } finally {
    exporting.value = false;
  }
};

// 提取保存文件的逻辑到单独方法
const saveImageToFile = async (imageData) => {
  // 获取文件名（使用书名+角色关系图）
  const fileName = `${bookTitle.value || '未知书籍'}_关系图谱.png`;
  
  // 调用后端API获取保存目录
  const response = await window.pywebview.api.select_directory();
  
  const result = typeof response === 'string' ? JSON.parse(response) : response;
  
  // 如果用户取消，则退出
  if (!result || result.status !== 'success' || !result.data) {
    ElMessage.info('已取消导出');
    return;
  }
  
  // 构建完整的文件路径
  const directory = result.data;
  const filePath = `${directory}/${fileName}`;
  
  // 使用与实体卡片相同的保存方法
  const saveResponse = await window.pywebview.api.save_entity_card({
    file_path: filePath,
    image_data: imageData
  });
  
  // 处理后端返回的JSON字符串
  const saveResult = typeof saveResponse === 'string' ? JSON.parse(saveResponse) : saveResponse;
  
  if (saveResult && saveResult.status === 'success') {
    ElMessage.success(`关系图谱已成功导出至: ${filePath}`);
    
    // 可选：打开文件所在位置
    try {
      await window.pywebview.api.open_directory(directory);
    } catch (error) {
      console.warn('无法打开文件目录:', error);
    }
  } else {
    ElMessage.error('导出失败：' + (saveResult?.message || '未知错误'));
  }
};

// 添加缺失的 exportGraph 函数，用于打开导出设置对话框
const exportGraph = () => {
  if (!chart.value) {
    ElMessage.warning('没有可导出的图谱');
    return;
  }
  
  // 显示导出设置对话框
  exportDialogVisible.value = true;
};

// 更新图表布局
const updateGraphLayout = () => {
  if (!chart.value) {
    console.warn('图表实例不存在，无法更新布局');
    return;
  }

  console.log('切换布局到:', graphLayout.value);
  loading.value = true;

  try {
    // 重新生成完整的图表配置，而不是修改现有配置
    const nodes = prepareNodes();
    const edges = prepareEdges();

    if (!nodes.length || !edges.length) {
      console.warn('没有有效的节点或边数据，无法更新布局');
      loading.value = false;
      return;
    }

    // 根据布局类型创建新的配置
    const newOption = createGraphOption(nodes, edges, graphLayout.value);

    // 应用新配置
    chart.value.setOption(newOption, true); // true表示不合并，完全替换

    // 绑定事件
    bindGraphEvents();

    ElMessage.success(`已切换到${getLayoutDisplayName(graphLayout.value)}`);

  } catch (err) {
    console.error('更新布局时出错:', err);
    ElMessage.error('更新布局失败，请尝试其他布局方式');

    // 回退到力导向布局
    graphLayout.value = 'force';
    setTimeout(() => {
      updateGraphLayout();
    }, 100);
  }

  // 添加短暂延迟以让布局动画完成
  setTimeout(() => {
    loading.value = false;
  }, 800);
};

// 获取布局显示名称
const getLayoutDisplayName = (layout) => {
  const names = {
    'force': '力导向布局',
    'hierarchical': '层次布局',
    'radial': '辐射布局',
    'circular': '环形布局',
    'grid': '网格布局'
  };
  return names[layout] || layout;
};

// 创建图表配置
const createGraphOption = (nodes, edges, layout = 'force') => {
  const baseOption = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        if (params.dataType === 'edge') {
          return formatEdgeTooltip(params.data);
        } else {
          return formatNodeTooltip(params.data);
        }
      },
      backgroundColor: 'rgba(50, 50, 50, 0.8)',
      borderRadius: 8,
      padding: 10,
      textStyle: {
        color: '#fff'
      }
    },
    animation: true,
    animationDuration: 1000,
    animationEasingUpdate: 'quinticInOut',
    series: [{
      type: 'graph',
      draggable: true,
      roam: true,
      zoom: 1.2,
      nodeScaleRatio: 0.6,
      focusNodeAdjacency: true,
      itemStyle: {
        borderWidth: 2,
        borderColor: 'rgba(255, 255, 255, 0.3)',
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 10
      },
      data: nodes,
      links: edges,
      edgeSymbolSize: [6, 10],
      categories: [],
      lineStyle: {
        color: 'source',
        curveness: 0.25,
        width: 3,
        shadowColor: 'rgba(0, 0, 0, 0.2)',
        shadowBlur: 5,
        cap: 'round',
        join: 'round'
      },
      emphasis: {
        focus: 'adjacency',
        lineStyle: {
          width: 6,
          shadowBlur: 10
        },
        itemStyle: {
          borderWidth: 3,
          shadowBlur: 15
        },
        label: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      label: {
        show: true,
        position: 'right',
        distance: 5,
        formatter: '{b}',
        fontSize: 14,
        fontWeight: 'bold',
        color: '#fff',
        backgroundColor: 'rgba(40, 40, 40, 0.7)',
        borderRadius: 4,
        padding: [4, 8],
        textShadowColor: 'rgba(0, 0, 0, 0.5)',
        textShadowBlur: 3,
        textShadowOffsetX: 1,
        textShadowOffsetY: 1
      },
      edgeLabel: {
        show: true,
        fontSize: 14,
        formatter: '{c}',
        position: 'middle',
        backgroundColor: 'rgba(40, 40, 40, 0.85)',
        padding: [6, 10],
        borderRadius: 8,
        color: '#fff',
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 5
      }
    }]
  };

  // 根据布局类型设置特定配置
  switch (layout) {
    case 'hierarchical':
      // 层次布局 - 适合主角-配角-次要角色的层级关系
      baseOption.series[0].layout = 'none';
      arrangeHierarchicalLayout(baseOption.series[0].data);
      baseOption.series[0].lineStyle.curveness = 0.2;
      break;

    case 'radial':
      // 辐射布局 - 以主角为中心的放射状布局
      baseOption.series[0].layout = 'none';
      arrangeRadialLayout(baseOption.series[0].data);
      baseOption.series[0].lineStyle.curveness = 0.3;
      break;

    case 'circular':
      baseOption.series[0].layout = 'circular';
      baseOption.series[0].circular = {
        rotateLabel: true
      };
      break;

    case 'grid':
      baseOption.series[0].layout = 'none';
      // 计算网格位置
      const nodeCount = nodes.length;
      const cols = Math.ceil(Math.sqrt(nodeCount));
      const rows = Math.ceil(nodeCount / cols);

      const containerWidth = graphContainer.value?.clientWidth || 800;
      const containerHeight = graphContainer.value?.clientHeight || 600;
      const gridWidth = containerWidth * 0.8;
      const gridHeight = containerHeight * 0.8;
      const cellWidth = gridWidth / Math.max(cols - 1, 1);
      const cellHeight = gridHeight / Math.max(rows - 1, 1);
      const startX = (containerWidth - gridWidth) / 2;
      const startY = (containerHeight - gridHeight) / 2;

      // 设置节点位置
      baseOption.series[0].data.forEach((node, index) => {
        const row = Math.floor(index / cols);
        const col = index % cols;
        node.x = startX + col * cellWidth;
        node.y = startY + row * cellHeight;
        node.fixed = true;
      });

      baseOption.series[0].animation = false;
      baseOption.series[0].lineStyle.curveness = 0.1;
      break;

    case 'force':
    default:
      baseOption.series[0].layout = 'force';
      baseOption.series[0].force = {
        repulsion: centerNodeId.value ? 2000 : 1500,
        gravity: centerNodeId.value ? 0.1 : 0.2,
        edgeLength: centerNodeId.value ? 300 : 250,
        friction: 0.15,
        layoutAnimation: true
      };

      // 确保节点不固定
      baseOption.series[0].data.forEach(node => {
        node.fixed = false;
        delete node.x;
        delete node.y;
      });
      break;
  }

  return baseOption;
};

// 层次布局 - 根据角色重要性分层
const arrangeHierarchicalLayout = (nodes) => {
  const containerWidth = graphContainer.value?.clientWidth || 800;
  const containerHeight = graphContainer.value?.clientHeight || 600;

  // 根据关系数量和模板类型确定角色重要性
  const sortedNodes = [...nodes].sort((a, b) => {
    // 优先级：主角 > 配角 > 其他，然后按关系数量排序
    const getImportance = (node) => {
      let importance = node.value || 0; // 关系数量

      // 根据模板类型加权
      const entity = props.entityList.find(e => e.id === node.id);
      if (entity?.template_name) {
        if (entity.template_name.includes('主角') || entity.template_name.includes('主要')) {
          importance += 1000;
        } else if (entity.template_name.includes('配角') || entity.template_name.includes('重要')) {
          importance += 500;
        }
      }

      // 中心节点额外加权
      if (node.isCenterNode) {
        importance += 2000;
      }

      return importance;
    };

    return getImportance(b) - getImportance(a);
  });

  // 分层：前30%为第一层，中间40%为第二层，后30%为第三层
  const layer1Count = Math.max(1, Math.floor(sortedNodes.length * 0.3));
  const layer2Count = Math.max(1, Math.floor(sortedNodes.length * 0.4));

  const layer1 = sortedNodes.slice(0, layer1Count);
  const layer2 = sortedNodes.slice(layer1Count, layer1Count + layer2Count);
  const layer3 = sortedNodes.slice(layer1Count + layer2Count);

  // 设置位置
  const layers = [layer1, layer2, layer3];
  const layerHeights = [containerHeight * 0.2, containerHeight * 0.5, containerHeight * 0.8];

  layers.forEach((layer, layerIndex) => {
    const y = layerHeights[layerIndex];
    const spacing = containerWidth * 0.8 / Math.max(layer.length - 1, 1);
    const startX = containerWidth * 0.1;

    layer.forEach((node, nodeIndex) => {
      const originalNode = nodes.find(n => n.id === node.id);
      if (originalNode) {
        originalNode.x = startX + nodeIndex * spacing;
        originalNode.y = y;
        originalNode.fixed = true;
      }
    });
  });
};

// 辐射布局 - 以重要角色为中心的放射状布局
const arrangeRadialLayout = (nodes) => {
  const containerWidth = graphContainer.value?.clientWidth || 800;
  const containerHeight = graphContainer.value?.clientHeight || 600;
  const centerX = containerWidth / 2;
  const centerY = containerHeight / 2;

  // 找到最重要的角色作为中心
  let centerNode = null;
  let maxImportance = -1;

  nodes.forEach(node => {
    let importance = node.value || 0;

    // 中心节点优先
    if (node.isCenterNode) {
      importance += 2000;
    }

    // 根据模板类型加权
    const entity = props.entityList.find(e => e.id === node.id);
    if (entity?.template_name) {
      if (entity.template_name.includes('主角') || entity.template_name.includes('主要')) {
        importance += 1000;
      } else if (entity.template_name.includes('配角') || entity.template_name.includes('重要')) {
        importance += 500;
      }
    }

    if (importance > maxImportance) {
      maxImportance = importance;
      centerNode = node;
    }
  });

  // 设置中心节点位置
  if (centerNode) {
    centerNode.x = centerX;
    centerNode.y = centerY;
    centerNode.fixed = true;
  }

  // 其他节点按重要性分圈排列
  const otherNodes = nodes.filter(n => n !== centerNode);
  const sortedOthers = otherNodes.sort((a, b) => (b.value || 0) - (a.value || 0));

  // 分为内圈和外圈
  const innerCount = Math.min(6, Math.floor(sortedOthers.length * 0.4));
  const innerNodes = sortedOthers.slice(0, innerCount);
  const outerNodes = sortedOthers.slice(innerCount);

  // 内圈
  const innerRadius = Math.min(containerWidth, containerHeight) * 0.15;
  innerNodes.forEach((node, index) => {
    const angle = (2 * Math.PI * index) / innerNodes.length;
    node.x = centerX + innerRadius * Math.cos(angle);
    node.y = centerY + innerRadius * Math.sin(angle);
    node.fixed = true;
  });

  // 外圈
  const outerRadius = Math.min(containerWidth, containerHeight) * 0.3;
  outerNodes.forEach((node, index) => {
    const angle = (2 * Math.PI * index) / outerNodes.length;
    node.x = centerX + outerRadius * Math.cos(angle);
    node.y = centerY + outerRadius * Math.sin(angle);
    node.fixed = true;
  });
};



// 显示图例对话框
const showLegend = () => {
  legendDialogVisible.value = true;
};

// 专门用于处理网格布局的函数
const applyGridLayout = (graphData) => {
  if (!chart.value || !graphData.nodes || graphData.nodes.length === 0) return;
  
  // 首先确保没有任何布局算法在运行
  chart.value.setOption({
    series: [{
      layout: 'none',
      animation: false,
      animationDurationUpdate: 0,
      force: null
    }]
  });
  
  // 暂停一下，确保之前的配置已应用
  setTimeout(() => {
    // 计算网格布局
    const nodeCount = graphData.nodes.length;
    const cols = Math.ceil(Math.sqrt(nodeCount));
    const rows = Math.ceil(nodeCount / cols);
    
    // 设置网格位置
    const width = graphContainer.value.clientWidth;
    const height = graphContainer.value.clientHeight;
    const gridWidth = width * 0.8;
    const gridHeight = height * 0.8;
    const cellWidth = gridWidth / (cols > 1 ? cols - 1 : 1);
    const cellHeight = gridHeight / (rows > 1 ? rows - 1 : 1);
    const startX = (width - gridWidth) / 2;
    const startY = (height - gridHeight) / 2;
    
    // 创建节点数据，设置固定位置
    const nodes = graphData.nodes.map((node, index) => {
      // 处理每个节点
      let nodeData = {
        id: node.id,
        name: node.name,
        itemStyle: getEntityStyle(node.name),  // 应用原有的样式
        symbolSize: node.symbolSize || 36,
      };
      
      // 网格位置计算
      const row = Math.floor(index / cols);
      const col = index % cols;
      nodeData.x = startX + col * cellWidth;
      nodeData.y = startY + row * cellHeight;
      nodeData.fixed = true;  // 固定位置
      
      return {
        ...nodeData
      };
    });
    
    // 创建边数据
    // 过滤和处理边数据
    const links = [];
    graphData.links.forEach(link => {
      // 确保源节点和目标节点都存在
      const sourceExists = nodes.some(node => node.id === link.source);
      const targetExists = nodes.some(node => node.id === link.target);
      
      if (sourceExists && targetExists) {
        // 创建边配置
        const linkStyle = getRelationStyle(link.type);
        links.push({
          source: link.source,
          target: link.target,
          relation: link.name,
          type: link.type,
          lineStyle: {
            ...linkStyle,
            curveness: 0.1,  // 添加弯曲度
          },
          label: {
            show: true,
            formatter: link.name
          }
        });
      }
    });
    
    // 更新图表数据，使用安全的方式
    chart.value.setOption({
      series: [{
        data: nodes,
        links: links
      }]
    });
  }, 100);
};

// 获取实体节点样式
const getEntityStyle = (entityName) => {
  // 根据实体名称生成固定颜色
  const color = getEntityColor(entityName);
  return {
    color: color,
    borderColor: '#fff',
    borderWidth: 2
  };
};

// 获取关系样式
const getRelationStyle = (relationType) => {
  switch (relationType) {
    case 'friendly':
      return { color: '#67C23A', width: 3 };
    case 'hostile':
      return { color: '#F56C6C', width: 3, type: 'dashed' };
    case 'family':
      return { color: '#E6A23C', width: 3 };
    case 'romance':
      return { color: '#FF88A0', width: 3 };
    default:
      return { color: '#909399', width: 2 };
  }
};

// 处理节点点击
const handleNodeClick = (params) => {
  const entityId = params.data.id;
  const entity = props.entityList.find(e => e.id === entityId);
  if (entity) {
    console.log('节点点击:', entity.name);
    emit('node-click', entity);
  }
};

// 处理边点击
const handleEdgeClick = (params) => {
  const relationId = params.data.relationId;
  const relation = props.relations.find(r => r.id === relationId);
  if (relation) {
    console.log('边点击:', relation);
    emit('edge-click', relation);
  }
};

// 绑定图表事件
const bindGraphEvents = () => {
  if (!chart.value) return;

  // 清除之前的事件监听
  chart.value.off('click');
  chart.value.off('restored');

  // 添加必要的事件监听
  chart.value.on('click', (params) => {
    // 节点点击事件
    if (params.dataType === 'node') {
      handleNodeClick(params);
    } else if (params.dataType === 'edge') {
      handleEdgeClick(params);
    }
  });

  // 添加缩放完成事件
  chart.value.on('restored', () => {
    fitView();
  });
};
</script>

<style lang="scss" scoped>
.fullscreen-graph-dialog {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;

  :deep(.el-dialog) {
    margin: 0 !important;
    padding: 0 !important;
    display: flex;
    flex-direction: column;
    overflow: hidden !important;
    height: 100vh !important;
    max-height: 100vh !important;
    width: 100vw !important;
    max-width: 100vw !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    border-radius: 0 !important;
  }

  :deep(.el-dialog__header) {
    display: none !important;
  }

  :deep(.el-dialog__body) {
    flex: 1;
    padding: 0 !important;
    height: 100vh !important;
    overflow: hidden !important;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  // 禁止任何滚动
  :deep(.el-overlay) {
    overflow: hidden !important;
  }
}

// 全屏模式下禁止body滚动
.fullscreen-graph-dialog:deep(.el-overlay) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;
}

// 确保弹窗打开时body不滚动
body:has(.fullscreen-graph-dialog) {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}

// 额外的全局样式确保固定布局
.fullscreen-graph-dialog {
  * {
    box-sizing: border-box;
  }

  // 确保所有子元素不会产生滚动
  :deep(*) {
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.dialog-content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: var(--bg-gradient);
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.graph-toolbar {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  z-index: 10;
  height: 64px;
  flex-shrink: 0;
  flex-wrap: nowrap;
  position: relative;

  // 添加原生应用风格的背景
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg,
      rgba(255, 255, 255, 0.8) 0%,
      rgba(255, 255, 255, 0.4) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: -1;
  }
}

.toolbar-section {
  display: flex;
  align-items: center;
  height: 100%;
}

.title-section {
  flex: 0 0 auto;
  margin-right: 24px;

  .dialog-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }
}

.view-section {
  flex: 1;
  display: flex;
  overflow: hidden;
  gap: 20px;
}

.action-section {
  flex: 0 0 auto;
  margin-left: auto;
  justify-content: flex-end;
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  white-space: nowrap;

  &:last-child {
    margin-right: 0;
  }
}

.dynamic-controls {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--el-text-color-primary);
  white-space: nowrap;
}

.control-label {
  margin-right: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  white-space: nowrap;
  font-weight: 500;
}

.template-selector,
.center-node-selector {
  min-width: 120px;
  max-width: 200px;
  margin-right: 10px;
}

.view-actions {
  margin: 0 16px;
}

.fullscreen-graph-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
  height: calc(100vh - 64px);
  width: 100vw;

  .graph-content {
    flex: 1;
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    max-height: calc(100vh - 64px);
  }

  .empty-graph {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .empty-icon {
      font-size: 64px;
      color: var(--text-color-secondary);
      margin-bottom: 16px;
      opacity: 0.4;
    }

    .empty-text {
      font-size: 16px;
      color: var(--text-color-secondary);
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 5;

    .loading-icon {
      font-size: 32px;
      color: var(--primary-color);
      animation: rotate 1.5s linear infinite;
      margin-bottom: 12px;
    }
  }
}

.graph-legend-dialog {
  padding: 10px 0;
  
  .legend-section {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .legend-section-title {
      font-size: 16px;
      font-weight: 600;
      margin: 0 0 16px 0;
      color: var(--el-text-color-primary);
    }
  }
  
  .legend-items {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    min-width: 120px;
    
    .connection-line {
      width: 36px;
      height: 3px;
      border-radius: 3px;
      position: relative;
      
      &.friendly {
        background: #67C23A;
      }
      
      &.hostile {
        background: #F56C6C;
        border-top: 1px dashed #F56C6C;
      }
      
      &.family {
        background: #E6A23C;
      }
      
      &.romance {
        background: #FF88A0;
      }
      
      &.other {
        background: #909399;
      }
      
      &.unidirectional, &.bidirectional {
        background: #409EFF;
      }
    }
    
    .node-sample {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      
      &.selected {
        background: #409EFF;
      }
      
      &.related {
        background: #B3D8FF;
      }
      
      &.center-node {
        background: #F56C6C;
        border: 2px solid #fff;
        box-shadow: 0 0 0 1px #F56C6C;
      }
      
      &.connected-node {
        background: #FFB6C1;
      }
    }
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 暗黑模式适配 */
html.dark {
  .fullscreen-graph-dialog .loading-overlay {
    background: rgba(0, 0, 0, 0.6);
  }

  .graph-toolbar {
    background: var(--el-bg-color-page);
    border-bottom-color: var(--el-border-color);

    &::before {
      background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.02) 100%);
    }
  }

  .dialog-title {
    color: var(--el-text-color-primary);
  }

  .control-label {
    color: var(--el-text-color-regular);
  }
}

/* Tooltip样式 - 限制在全屏图谱弹窗内 */
.fullscreen-graph-dialog :deep(.graph-tooltip) {
  .tooltip-title {
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  .tooltip-content {
    font-size: 12px;
    
    p {
      margin: 4px 0;
    }
    
    .tooltip-desc {
      font-style: italic;
      opacity: 0.8;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .tooltip-badge {
      display: inline-block;
      background: rgba(64, 158, 255, 0.1);
      color: #409EFF;
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 10px;
    }
  }
  
  .tooltip-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
    
    .tooltip-tag {
      font-size: 10px;
      background: rgba(0, 0, 0, 0.05);
      padding: 1px 5px;
      border-radius: 8px;
    }
  }
}

/* 添加节点样式图例 */
.legend-separator {
  width: 1px;
  height: 20px;
  background: var(--border-color);
  margin: 0 8px;
}

.node-sample {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  
  &.selected {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border: 2px solid rgba(255, 255, 255, 0.9);
  }
  
  &.related {
    background: linear-gradient(135deg, #757575, #424242);
    border: 2px solid rgba(255, 255, 255, 0.6);
  }
  
  &.center-node {
    background: linear-gradient(135deg, #FFC107, #FF8F00);
    border: 3px solid rgba(255, 215, 0, 0.9);
    box-shadow: 0 0 8px rgba(255, 193, 7, 0.5);
  }
  
  &.connected-node {
    background: linear-gradient(135deg, #64B5F6, #1976D2);
    border: 2px solid rgba(255, 255, 255, 0.8);
  }
}

/* 关系线标签样式 */
.connection-line {
  width: 36px;
  height: 3px;
  border-radius: 3px;
  position: relative;
  
  &.unidirectional, &.bidirectional {
    background: #909399;
    display: flex;
    align-items: center;
  }
  
  .arrow-end {
    position: absolute;
    right: -2px;
    width: 0; 
    height: 0; 
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-left: 6px solid #909399;
  }
  
  .arrow-start {
    position: absolute;
    left: -2px;
    width: 0; 
    height: 0; 
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-right: 6px solid #909399;
  }
}

/* 关系标签悬停样式 - 限制在全屏图谱弹窗内 */
.fullscreen-graph-dialog :deep(.el-tooltip__popper) {
  border-radius: 8px !important;
  backdrop-filter: blur(5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 添加"添加关系"按钮样式 */
.add-relation-btn {
  margin-right: 16px;
}

/* 中心节点选择器样式 */
.center-node-selector {
  min-width: 180px;
  margin-left: 16px;
  margin-right: 8px;
}

.clear-center-btn {
  padding: 5px 10px;
  font-size: 12px;
}

/* 添加实体选项样式 */
.entity-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.entity-color-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

/* 优化中心节点选择器样式 */
.center-node-selector {
  min-width: 180px;
  margin-left: 16px;
  margin-right: 8px;
}

.fullscreen-graph-dialog :deep(.el-select-dropdown__item) {
  padding: 0 12px;
}

.fullscreen-graph-dialog :deep(.el-select-group__title) {
  font-size: 12px;
  font-weight: bold;
  padding-top: 8px;
  color: var(--el-text-color-secondary);
}

/* 优化双向关系箭头样式 */
.connection-line {
  &.bidirectional {
    background: #409EFF;
    position: relative;
    
    .arrow-start, .arrow-end {
      border-color: transparent;
      border-width: 5px;
    }
    
    .arrow-start {
      border-right-color: #409EFF;
      left: -6px;
    }
    
    .arrow-end {
      border-left-color: #409EFF;
      right: -6px;
    }
  }
}

/* 导出设置样式 */
.export-settings {
  padding: 10px 0;
  
  .setting-item {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    
    .setting-label {
      width: 80px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    
    .el-select, .el-color-picker {
      width: 220px;
    }
  }
  
  .custom-size {
    display: flex;
    flex: 1;
    gap: 16px;
    
    .size-input {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      span {
        font-size: 13px;
        color: var(--el-text-color-secondary);
      }
      
      .el-input-number {
        width: 100%;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.slider-with-value {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
  
  .node-scale-slider {
    flex: 1;
  }
  
  .scale-value {
    min-width: 45px;
    text-align: right;
    font-size: 13px;
    color: var(--el-text-color-secondary);
  }
}

/* 布局选择器样式 */
.layout-selector {
  display: flex;
  align-items: center;
  margin-right: 16px;
  
  span {
    font-size: 14px;
    margin-right: 8px;
    color: var(--el-text-color-regular);
  }
  
  .el-select {
    width: 130px;
  }
}

/* 添加禁用状态的样式 */
.template-selector:disabled,
.center-node-selector:disabled {
  opacity: 0.7;
}

/* 关闭按钮样式 */
.close-btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  background: var(--el-color-primary);
  border: none;
  color: white;
  transition: all 0.3s ease;

  &:hover {
    background: var(--el-color-primary-light-3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }

  .el-icon {
    margin-right: 4px;
  }
}

/* 优化后的工具栏样式 */
.graph-toolbar-optimized {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  color: white;

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 24px;
    flex: 1;

    .title-area {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .dialog-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .book-info {
        .book-name {
          font-size: 13px;
          color: rgba(255, 255, 255, 0.8);
          background: rgba(255, 255, 255, 0.1);
          padding: 2px 8px;
          border-radius: 12px;
          backdrop-filter: blur(10px);
        }
      }
    }

    .controls-area {
      display: flex;
      align-items: center;
      gap: 20px;

      .control-group {
        .mode-switch {
          .el-radio-button {
            --el-radio-button-checked-bg-color: rgba(255, 255, 255, 0.2);
            --el-radio-button-checked-text-color: white;
            --el-radio-button-checked-border-color: rgba(255, 255, 255, 0.3);

            .el-radio-button__inner {
              background: rgba(255, 255, 255, 0.1);
              border-color: rgba(255, 255, 255, 0.2);
              color: rgba(255, 255, 255, 0.9);
              padding: 8px 16px;
              border-radius: 8px;
              transition: all 0.3s ease;
              backdrop-filter: blur(10px);

              .el-icon {
                margin-right: 6px;
              }

              &:hover {
                background: rgba(255, 255, 255, 0.15);
                transform: translateY(-1px);
              }
            }

            &.is-active .el-radio-button__inner {
              background: rgba(255, 255, 255, 0.25);
              border-color: rgba(255, 255, 255, 0.4);
              color: white;
              font-weight: 500;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }

      .dynamic-controls {
        display: flex;
        align-items: center;
        gap: 12px;

        .control-item {
          display: flex;
          align-items: center;
          gap: 8px;

          &.center-node-control {
            .clear-center-btn {
              background: rgba(255, 255, 255, 0.1);
              border-color: rgba(255, 255, 255, 0.2);
              color: white;

              &:hover {
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.3);
              }
            }
          }
        }

        .template-selector,
        .center-node-selector {
          --el-select-input-color: white;
          --el-select-placeholder-color: rgba(255, 255, 255, 0.7);
          --el-select-input-focus-border-color: rgba(255, 255, 255, 0.4);

          .el-input__wrapper {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);

            &:hover {
              border-color: rgba(255, 255, 255, 0.3);
            }

            &.is-focus {
              border-color: rgba(255, 255, 255, 0.4);
              box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.2);
            }
          }

          .el-input__inner {
            color: white;

            &::placeholder {
              color: rgba(255, 255, 255, 0.7);
            }
          }

          .el-select__tags {
            .el-tag {
              background: rgba(255, 255, 255, 0.15);
              border-color: rgba(255, 255, 255, 0.2);
              color: white;

              .el-tag__close {
                color: rgba(255, 255, 255, 0.8);

                &:hover {
                  background: rgba(255, 255, 255, 0.2);
                  color: white;
                }
              }
            }
          }
        }
      }
    }
  }
}

/* 下拉框弹出层样式优化 - 使用更高优先级 */
.el-select-dropdown.el-popper {
  background: rgba(40, 44, 52, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  border-radius: 8px !important;
}

.el-select-dropdown .el-select-dropdown__item {
  color: rgba(255, 255, 255, 0.9) !important;
  background: transparent !important;
}

.el-select-dropdown .el-select-dropdown__item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
}

.el-select-dropdown .el-select-dropdown__item.selected {
  background: rgba(103, 126, 234, 0.3) !important;
  color: white !important;
  font-weight: 500 !important;
}

.el-select-dropdown .el-select-group__title {
  color: rgba(255, 255, 255, 0.6) !important;
  background: rgba(255, 255, 255, 0.05) !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  padding: 8px 12px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.el-select-dropdown .el-select-group__wrap:not(:last-of-type) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
  margin-bottom: 4px !important;
  padding-bottom: 4px !important;
}

.el-select-dropdown .el-scrollbar__view {
  padding: 4px 0 !important;
}

.el-select-dropdown .el-popper__arrow {
  display: none !important;
}

.el-select-dropdown .entity-option,
.el-select-dropdown .template-option {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.el-select-dropdown .entity-color-dot,
.el-select-dropdown .template-color-dot {
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  flex-shrink: 0 !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.graph-toolbar-optimized .toolbar-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .layout-control {
      display: flex;
      align-items: center;
      gap: 12px;

      .control-label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
      }

      .layout-buttons {
        .el-radio-button {
          --el-radio-button-checked-bg-color: rgba(255, 255, 255, 0.25);
          --el-radio-button-checked-text-color: white;
          --el-radio-button-checked-border-color: rgba(255, 255, 255, 0.4);

          .el-radio-button__inner {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.9);
            padding: 6px 12px;
            border-radius: 6px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-size: 13px;

            &:hover {
              background: rgba(255, 255, 255, 0.15);
              border-color: rgba(255, 255, 255, 0.3);
              transform: translateY(-1px);
            }
          }

          &.is-active .el-radio-button__inner {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          &:first-child .el-radio-button__inner {
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
          }

          &:last-child .el-radio-button__inner {
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      align-items: center;
      gap: 12px;

      .add-relation-btn {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
        font-weight: 500;
        padding: 8px 16px;
        border-radius: 8px;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.25);
          border-color: rgba(255, 255, 255, 0.4);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .el-icon {
          margin-right: 6px;
        }
      }

      .tool-buttons {
        .el-button {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.9);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-1px);
          }

          &.is-loading {
            .el-icon {
              color: white;
            }
          }
        }
      }

      .close-btn {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.3);
          transform: translateY(-1px);
        }
      }
    }
  }
</style> 