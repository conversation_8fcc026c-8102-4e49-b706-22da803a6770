<template>
  <el-dialog
    v-model="dialogVisible"
    title=""
    class="fullscreen-scene-canvas-dialog"
    fullscreen
    destroy-on-close
    :show-close="false"
    :lock-scroll="true"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <!-- 纯画布全屏模式 - 只有画布和右侧控制按钮 -->
    <div class="fullscreen-canvas-wrapper">
      <div
        class="scene-canvas"
        @mousedown="handleCanvasMouseDown"
        @mouseup="handleCanvasMouseUp"
        @mousemove="handleCanvasMouseMove"
        @contextmenu.prevent
        @wheel="handleCanvasWheel"
        ref="canvasRef"
      >
        <!-- 拖动提示 -->
        <div class="drag-hint" :class="{ visible: showDragHint }">
          按住 <span class="key-hint">右键</span> 拖动画布 | 使用 <span class="key-hint">滚轮</span> 缩放
        </div>

        <!-- 无限画布容器 -->
        <div
          class="infinite-canvas"
          :class="{ dragging: isDraggingCanvas }"
          :style="canvasStyle"
          ref="infiniteCanvasRef"
        >
          <!-- 场景卡片 -->
          <div
            v-for="scene in scenes"
            :key="scene.id"
            class="scene-card-wrapper"
            :style="getCardStyle(scene)"
          >
            <div
              class="scene-card-interaction-area"
              @mousedown.stop="handleCardMouseDown(scene.id, $event)"
              @dblclick.stop="openEditDialog(scene)"
              @mouseover.stop="handleCardMouseEnter(scene.id, $event)"
              @mouseleave.stop="handleCardMouseLeave(scene.id)"
            >
              <Card
                :title="scene.title"
                :description="scene.description"
                :tags="scene.tags"
                @delete="$emit('delete-scene', scene)"
                :class="{
                  active: activeCardId === scene.id,
                  dragging: draggingCardId === scene.id
                }"
              />
            </div>
          </div>
        </div>

        <!-- 右侧控制按钮 - 与非全屏模式完全一致 -->
        <div class="canvas-controls">
          <div class="control-btn" @click="zoomIn" title="放大">
            <el-icon><ZoomIn /></el-icon>
          </div>
          <div class="zoom-display">{{ Math.round(canvasScale * 100) }}%</div>
          <div class="control-btn" @click="zoomOut" title="缩小">
            <el-icon><ZoomOut /></el-icon>
          </div>
          <div class="control-btn" @click="autoLayout" title="自动排版">
            <el-icon><Grid /></el-icon>
          </div>
          <div class="control-btn" @click="resetCanvas" title="重置画布">
            <el-icon><Refresh /></el-icon>
          </div>
          <div class="control-btn" @click="closeDialog" title="退出全屏">
            <el-icon><Close /></el-icon>
          </div>
        </div>
      </div>

      <!-- 编辑场景弹窗 - 在全屏画布内部 -->
      <div v-if="editDialogVisible" class="fullscreen-edit-dialog-overlay" @click="handleEditDialogOverlayClick">
        <div class="fullscreen-edit-dialog" @click.stop>
          <div class="edit-dialog-header">
            <h3>编辑场景</h3>
            <button class="edit-dialog-close" @click="closeEditDialog">×</button>
          </div>
          <div class="edit-dialog-body">
            <el-form :model="editForm" label-width="100px" class="scene-form">
              <el-form-item label="场景标题">
                <el-input
                  v-model="editForm.title"
                  placeholder="请输入场景标题"
                  ref="editTitleInput"
                  @keyup.enter.prevent="saveEditedScene"
                  @keyup.esc.prevent="closeEditDialog"
                  autofocus
                />
              </el-form-item>
              <el-form-item label="场景描述">
                <el-input
                  v-model="editForm.description"
                  type="textarea"
                  :rows="6"
                  resize="none"
                  placeholder="请描述场景内容"
                  @keyup.esc.prevent="closeEditDialog"
                />
              </el-form-item>
              <el-form-item label="场景标签">
                <el-select
                  v-model="editForm.tags"
                  multiple
                  filterable
                  allow-create
                  default-first-option
                  placeholder="请选择或创建标签"
                >
                  <el-option
                    v-for="tag in availableTags"
                    :key="tag"
                    :label="tag"
                    :value="tag"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div class="edit-dialog-footer">
            <el-button @click="closeEditDialog">取消</el-button>
            <el-button type="primary" @click="saveEditedScene">保存场景</el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import Card from '@/views/book/Card.vue'
import {
  ZoomIn,
  ZoomOut,
  Grid,
  Refresh,
  Close
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  scenes: {
    type: Array,
    default: () => []
  },
  bookTitle: {
    type: String,
    default: ''
  },
  currentPool: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits([
  'update:visible',
  'create-scene',
  'edit-scene', 
  'delete-scene',
  'random-draw',
  'save-scenes'
])

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 画布相关状态
const canvasRef = ref(null)
const infiniteCanvasRef = ref(null)
const canvasOffsetX = ref(0)
const canvasOffsetY = ref(0)
const canvasScale = ref(1)
const isDraggingCanvas = ref(false)
const dragStartX = ref(0)
const dragStartY = ref(0)
const showDragHint = ref(true)
const activeCardId = ref(null)
const draggingCardId = ref(null)

// 编辑弹窗相关状态
const editDialogVisible = ref(false)
const editTitleInput = ref(null)
const editingScene = ref(null)
const editForm = ref({
  title: '',
  description: '',
  tags: []
})

// 可用标签（从所有场景中提取）
const availableTags = computed(() => {
  const tags = new Set()
  props.scenes.forEach(scene => {
    if (scene.tags && Array.isArray(scene.tags)) {
      scene.tags.forEach(tag => tags.add(tag))
    }
  })
  return Array.from(tags)
})

// 画布样式
const canvasStyle = computed(() => ({
  transform: `translate(${canvasOffsetX.value}px, ${canvasOffsetY.value}px) scale(${canvasScale.value})`,
}))

// 获取卡片样式
const getCardStyle = (scene) => {
  return {
    position: 'absolute',
    left: `${scene.x || 0}px`,
    top: `${scene.y || 0}px`,
    zIndex: scene.zIndex || 1,
    transform: 'translate3d(0, 0, 0)', // 启用硬件加速
  }
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
}

// 画布控制方法
const zoomIn = () => {
  canvasScale.value = Math.min(2, canvasScale.value + 0.1)
}

const zoomOut = () => {
  canvasScale.value = Math.max(0.3, canvasScale.value - 0.1)
}

const resetCanvas = () => {
  canvasOffsetX.value = 0
  canvasOffsetY.value = 0
  canvasScale.value = 1
  centerContent()
}

const autoLayout = () => {
  if (!props.scenes.length) {
    ElMessage.warning('没有场景卡片需要排版')
    return
  }
  
  // 自动排版逻辑
  const cardWidth = 280
  const cardHeight = 280
  const padding = 20
  const margin = 16
  
  const containerWidth = canvasRef.value?.clientWidth || 1200
  
  const availableWidth = containerWidth / canvasScale.value - padding * 2
  const cardsPerRow = Math.floor((availableWidth + margin) / (cardWidth + margin))
  const actualCardsPerRow = Math.max(1, Math.min(cardsPerRow, props.scenes.length))
  
  props.scenes.forEach((scene, index) => {
    const row = Math.floor(index / actualCardsPerRow)
    const col = index % actualCardsPerRow
    
    scene.x = padding + col * (cardWidth + margin)
    scene.y = padding + row * (cardHeight + margin)
  })
  
  emit('save-scenes')
  ElMessage.success(`已自动排版 ${props.scenes.length} 个场景卡片`)
  
  nextTick(() => {
    centerContent()
  })
}

const centerContent = () => {
  if (props.scenes.length > 0 && canvasRef.value) {
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity
    
    props.scenes.forEach(scene => {
      const x = scene.x || 0
      const y = scene.y || 0
      minX = Math.min(minX, x)
      minY = Math.min(minY, y)
      maxX = Math.max(maxX, x + 280)
      maxY = Math.max(maxY, y + 280)
    })
    
    const contentCenterX = (minX + maxX) / 2
    const contentCenterY = (minY + maxY) / 2
    
    const containerRect = canvasRef.value.getBoundingClientRect()
    const containerCenterX = containerRect.width / 2
    const containerCenterY = containerRect.height / 2
    
    canvasOffsetX.value = containerCenterX - contentCenterX * canvasScale.value
    canvasOffsetY.value = containerCenterY - contentCenterY * canvasScale.value
  }
}

// 画布交互方法
const handleCanvasMouseDown = (event) => {
  if (event.button === 2) { // 右键
    isDraggingCanvas.value = true
    dragStartX.value = event.clientX - canvasOffsetX.value
    dragStartY.value = event.clientY - canvasOffsetY.value

    showDragHint.value = true

    if (infiniteCanvasRef.value) {
      infiniteCanvasRef.value.style.cursor = 'grabbing'
    }

    event.preventDefault()
  }
}

const handleCanvasMouseMove = (event) => {
  if (isDraggingCanvas.value) {
    canvasOffsetX.value = event.clientX - dragStartX.value
    canvasOffsetY.value = event.clientY - dragStartY.value
    event.preventDefault()
  }
}

const handleCanvasMouseUp = (event) => {
  if (isDraggingCanvas.value) {
    isDraggingCanvas.value = false

    if (infiniteCanvasRef.value) {
      infiniteCanvasRef.value.style.cursor = 'default'
    }

    setTimeout(() => {
      showDragHint.value = false
    }, 1000)
  }
}

const handleCanvasWheel = (event) => {
  event.preventDefault()

  const rect = canvasRef.value.getBoundingClientRect()
  const mouseX = event.clientX - rect.left
  const mouseY = event.clientY - rect.top

  const beforeZoomX = (mouseX - canvasOffsetX.value) / canvasScale.value
  const beforeZoomY = (mouseY - canvasOffsetY.value) / canvasScale.value

  const scaleFactor = 0.1
  if (event.deltaY < 0) {
    canvasScale.value = Math.min(2, canvasScale.value + scaleFactor)
  } else {
    canvasScale.value = Math.max(0.3, canvasScale.value - scaleFactor)
  }

  const afterZoomX = (mouseX - canvasOffsetX.value) / canvasScale.value
  const afterZoomY = (mouseY - canvasOffsetY.value) / canvasScale.value

  canvasOffsetX.value += (afterZoomX - beforeZoomX) * canvasScale.value
  canvasOffsetY.value += (afterZoomY - beforeZoomY) * canvasScale.value

  showDragHint.value = true
  setTimeout(() => {
    showDragHint.value = false
  }, 1000)
}

// 卡片交互方法
const handleCardMouseDown = (cardId, event) => {
  if (event.button !== 0 || isDraggingCanvas.value) return

  activeCardId.value = cardId
  draggingCardId.value = cardId

  const scene = props.scenes.find(s => s.id === cardId)
  if (scene) {
    const rect = event.currentTarget.getBoundingClientRect()
    scene.mouseOffsetX = (event.clientX - rect.left) * canvasScale.value
    scene.mouseOffsetY = (event.clientY - rect.top) * canvasScale.value
  }

  document.addEventListener('mousemove', handleCardDragMove)
  document.addEventListener('mouseup', handleCardDragEnd)

  event.preventDefault()
}

const handleCardDragMove = (event) => {
  if (draggingCardId.value) {
    const scene = props.scenes.find(s => s.id === draggingCardId.value)
    if (scene) {
      const canvasRect = canvasRef.value.getBoundingClientRect()

      const canvasMouseX = (event.clientX - canvasRect.left - canvasOffsetX.value) / canvasScale.value
      const canvasMouseY = (event.clientY - canvasRect.top - canvasOffsetY.value) / canvasScale.value

      scene.x = canvasMouseX - (scene.mouseOffsetX / canvasScale.value)
      scene.y = canvasMouseY - (scene.mouseOffsetY / canvasScale.value)
    }
  }
}

const handleCardDragEnd = () => {
  if (draggingCardId.value) {
    emit('save-scenes')
    draggingCardId.value = null
  }

  document.removeEventListener('mousemove', handleCardDragMove)
  document.removeEventListener('mouseup', handleCardDragEnd)
}

const handleCardMouseEnter = (cardId) => {
  if (!draggingCardId.value) {
    activeCardId.value = cardId
  }
}

const handleCardMouseLeave = () => {
  if (!draggingCardId.value) {
    activeCardId.value = null
  }
}

// 键盘事件处理
const handleKeyDown = (event) => {
  if (event.key === 'Escape') {
    event.preventDefault()
    event.stopPropagation()

    // 如果编辑弹窗打开，先关闭编辑弹窗
    if (editDialogVisible.value) {
      closeEditDialog()
    } else {
      // 否则关闭全屏画布
      closeDialog()
    }
  }
}

// 初始化和清理
onMounted(() => {
  setTimeout(() => {
    showDragHint.value = false
  }, 3000)

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleCardDragMove)
  document.removeEventListener('mouseup', handleCardDragEnd)
  document.removeEventListener('keydown', handleKeyDown)
})

// 编辑弹窗相关方法
const openEditDialog = (scene) => {
  editingScene.value = scene
  editForm.value = {
    title: scene.title || '',
    description: scene.description || '',
    tags: scene.tags ? [...scene.tags] : []
  }
  editDialogVisible.value = true

  nextTick(() => {
    if (editTitleInput.value) {
      editTitleInput.value.focus()
    }
  })
}

const closeEditDialog = () => {
  editDialogVisible.value = false
  editingScene.value = null
  editForm.value = {
    title: '',
    description: '',
    tags: []
  }
}

const handleEditDialogOverlayClick = () => {
  closeEditDialog()
}

const saveEditedScene = () => {
  if (!editingScene.value) return

  if (!editForm.value.title.trim()) {
    ElMessage.warning('请输入场景标题')
    return
  }

  // 更新场景数据
  editingScene.value.title = editForm.value.title.trim()
  editingScene.value.description = editForm.value.description.trim()
  editingScene.value.tags = editForm.value.tags

  // 触发保存事件
  emit('save-scenes')

  ElMessage.success('场景已更新')
  closeEditDialog()
}

// 监听对话框打开，重置画布状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      resetCanvas()
    })
  }
})
</script>

<style lang="scss" scoped>
.fullscreen-scene-canvas-dialog {
  /* 确保全屏对话框的z-index不会阻挡编辑弹窗 */
  :deep(.el-overlay) {
    z-index: 2000 !important; /* 低于编辑弹窗的99999 */
    padding: 0 !important;
  }

  :deep(.el-dialog) {
    margin: 0 !important;
    padding: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    max-width: none !important;
    max-height: none !important;
    border-radius: 0 !important;
    z-index: 2001 !important; /* 低于编辑弹窗的99999 */
  }

  :deep(.el-dialog__body) {
    padding: 0 !important;
    height: 100vh !important;
    overflow: hidden !important;
    margin: 0 !important;
  }

  :deep(.el-dialog__header) {
    display: none !important;
  }
}

.fullscreen-canvas-wrapper {
  width: 100vw !important;
  height: 100vh !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  background: var(--el-bg-color-page);
  overflow: hidden !important;
}

.scene-canvas {
  width: 100vw !important;
  height: 100vh !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  cursor: default;
  user-select: none;

  /* 原生应用样式 - 禁用选择和拖拽 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}

.drag-hint {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1000;
  pointer-events: none;

  &.visible {
    opacity: 1;
  }

  .key-hint {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: bold;
  }
}

.infinite-canvas {
  position: absolute;
  width: 10000px;
  height: 10000px;
  transform-origin: 0 0;
  cursor: default;
  transition: transform 0.1s ease-out;
  transition-property: transform;

  &.dragging {
    transition: none;
  }
}

.scene-card-wrapper {
  position: absolute;
  user-select: none;
  cursor: move;
  z-index: 1;
  transform-origin: top left;
  width: 240px;
  transition: none !important;

  /* 原生应用样式 - 禁用图片和元素拖拽 */
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;

  .scene-card-interaction-area {
    width: 100%;
    height: 100%;
    cursor: pointer;
    position: relative;

    &:hover {
      transform: translateY(-2px);
      transition: transform 0.2s ease;
    }
  }



  /* 禁用卡片内所有图片和元素的拖拽 */
  img, svg, canvas, video {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    pointer-events: none;
  }
}

/* 画布控制区域 - 与非全屏模式完全一致 */
.canvas-controls {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 100;
  padding: 8px;
  background: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  border: 1px solid var(--el-border-color-lighter);
  backdrop-filter: blur(8px);

  /* 原生应用样式 - 禁用选择和拖拽 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;

  .control-btn {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    background: transparent;

    &:hover {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    .el-icon {
      font-size: 18px;
    }
  }

  .zoom-display {
    padding: 8px 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    color: var(--el-text-color-regular);
    background: var(--el-fill-color-light);
    border-radius: 6px;
    min-width: 40px;
    text-align: center;
  }
}

/* 确保卡片在全屏模式下的样式正确 */
:deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  /* 拖动状态样式 - 与非全屏模式一致但不包含旋转 */
  &.dragging {
    z-index: 1000 !important;
    transform: scale(1.02) !important;
    opacity: 0.9;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15) !important;
    pointer-events: none;
  }
}

/* 确保按钮在全屏模式下可点击 */
:deep(.el-button) {
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

:deep(.card-actions) {
  pointer-events: auto;
}

/* 全屏模式下的编辑弹窗样式 */
.fullscreen-edit-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000; /* 确保在全屏画布之上 */
  padding: 20px;
}

.fullscreen-edit-dialog {
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 600px; /* 与非全屏模式的 custom-dialog-medium 一致 */
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  /* 对话框内容可以交互 - 与非全屏模式一致 */
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.edit-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px; /* 与非全屏模式一致 */
  border-bottom: 1px solid var(--el-border-color);
  background: var(--el-bg-color);

  /* 标题栏不能选择 - 与非全屏模式一致 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  h3 {
    margin: 0;
    font-size: 16px; /* 与非全屏模式一致 */
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.edit-dialog-close {
  background: none;
  border: none;
  font-size: 20px; /* 与非全屏模式一致 */
  color: var(--el-text-color-regular);
  cursor: pointer;
  padding: 4px; /* 与非全屏模式一致 */
  line-height: 1;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    background-color: var(--el-color-danger-light-9); /* 与非全屏模式一致 */
    color: var(--el-color-danger);
  }
}

.edit-dialog-body {
  padding: 20px 24px;
  flex: 1;
  overflow-y: auto;
}

.edit-dialog-footer {
  padding: 16px 20px; /* 与非全屏模式一致 */
  border-top: 1px solid var(--el-border-color);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: var(--el-bg-color); /* 与非全屏模式一致 */

  /* 按钮不能选择 - 与非全屏模式一致 */
  button {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }
}

.scene-form {
  /* 表单标签样式 - 与非全屏模式一致 */
  :deep(.el-form-item__label) {
    user-select: none;
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    padding-right: 24px;
  }

  .el-form-item {
    margin-bottom: 20px;
  }

  .el-input,
  .el-textarea,
  .el-select {
    width: 100%;
  }

  /* 标题输入框样式 - 与非全屏模式一致 */
  .el-input:deep(.el-input__wrapper) {
    padding: 8px 16px;
    font-size: 16px;
    box-shadow: none;
    border: 1px solid var(--el-border-color);
    transition: all 0.3s;
  }

  .el-input:deep(.el-input__wrapper:hover),
  .el-input:deep(.el-input__wrapper.is-focus) {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px var(--el-color-primary-light-8);
  }

  /* 描述文本框样式 - 与非全屏模式一致 */
  .el-textarea:deep(.el-textarea__inner) {
    padding: 16px;
    font-size: 15px;
    line-height: 1.6;
    border: 1px solid var(--el-border-color);
    box-shadow: none;
    transition: all 0.3s;
    resize: none;
  }

  .el-textarea:deep(.el-textarea__inner:hover),
  .el-textarea:deep(.el-textarea__inner:focus) {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px var(--el-color-primary-light-8);
  }

  /* 标签选择器样式 - 与非全屏模式一致 */
  .el-select:deep(.el-select__wrapper) {
    padding: 8px 16px;
    font-size: 15px;
  }
}

/* 按钮样式 - 与非全屏模式一致 */
.edit-dialog-footer .el-button {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 8px;
  transition: all 0.3s;
}

.edit-dialog-footer .el-button:not(.el-button--primary) {
  border: 1px solid var(--el-border-color);
}

.edit-dialog-footer .el-button:not(.el-button--primary):hover {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.edit-dialog-footer .el-button--primary {
  font-weight: 500;
}
</style>
