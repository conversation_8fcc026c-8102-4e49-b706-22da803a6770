<!--
  MarkdownBubble - 通用 Markdown 消息气泡组件

  功能特性：
  - 支持 Markdown 渲染（基于 @matechat/core）
  - 完整的主题系统（light/dark/auto）
  - 自定义 SVG 头像图标
  - 深度思考功能支持
  - 消息操作按钮（复制、重新生成、重发）
  - 打字机效果支持（多种样式：cursor、gradient、color）
  - 流式内容渲染支持
  - 响应式设计
  - 自包含主题变量，可在任何项目中直接使用

  基础用法：
  <MarkdownBubble
    content="# Hello World\n这是一条 **Markdown** 消息"
    messageType="assistant"
    senderName="AI助手"
  />

  完整用法：
  <MarkdownBubble
    :content="message.content"
    :messageType="message.role"           // 'user' | 'assistant' | 'system'
    :isError="message.isError"            // 是否为错误消息
    :loading="message.loading"            // 是否显示加载状态
    :timestamp="message.timestamp"        // 消息时间戳
    :senderName="message.senderName"      // 发送者名称
    :avatarSrc="message.avatarSrc"        // 自定义头像图片URL（可选）
    :useDefaultIcons="true"               // 使用内置SVG图标
    :disabled="isSending"                 // 是否禁用操作按钮
    :theme="'auto'"                       // 'light' | 'dark' | 'auto'
    :variant="'default'"                  // 'default' | 'minimal' | 'card'
    :showAvatar="true"                    // 是否显示头像
    :showHeader="true"                    // 是否显示头部信息
    :showActions="true"                   // 是否显示操作按钮
    :reasoning="message.reasoning"        // 推理内容
    :reasoningTime="message.reasoningTime" // 推理耗时（秒）
    :hasReasoning="message.hasReasoning"  // 是否有推理内容
    :showReasoning="true"                 // 是否显示推理内容
    :loadingText="'正在思考...'"           // 加载状态文本
    :customActions="[]"                   // 自定义操作按钮
    :typing="true"                        // 启用打字机效果
    :typingOptions="typingOptions"        // 打字机配置选项
    :streaming="false"                    // 是否为流式内容
    @copy="handleCopy"                    // 复制事件
    @regenerate="handleRegenerate"        // 重新生成事件
    @resend="handleResend"                // 重发事件
    @action="handleCustomAction"          // 自定义操作事件
    @typingEnd="handleTypingEnd"          // 打字机效果完成事件
  />

  推理功能：
  <MarkdownBubble
    :content="message.content"
    :reasoning="message.reasoning"        // 推理内容（独立字段）
    :reasoningTime="message.reasoningTime" // 实际推理耗时（秒）
    :hasReasoning="message.hasReasoning"  // 是否有推理内容
    :showReasoning="true"                 // 是否显示推理内容
  />

  自定义操作按钮：
  :customActions="[
    {
      key: 'edit',
      title: '编辑',
      class: 'edit-btn',
      icon: () => h('svg', { ... })
    }
  ]"

  打字机效果配置：
  :typing="true"                        // 启用打字机效果
  :typingOptions="{
    step: [1, 5],                       // 每次打字的字符数范围
    interval: 200,                      // 打字间隔（毫秒）
    style: 'cursor'                     // 打字样式：'cursor' | 'gradient' | 'color'
  }"
  @typingEnd="handleTypingEnd"          // 打字机效果完成回调

  流式内容支持：
  :streaming="true"                     // 启用流式内容模式
  :content="streamingContent"           // 动态更新的内容
  @typingEnd="onStreamComplete"         // 流式+打字机完成回调

  主题系统：
  - 组件内置完整的主题变量系统
  - 支持 light/dark 模式自动切换
  - 不依赖外部 CSS 变量，可在任何项目中使用
  - 表格、代码块、引用块等都有完整的主题适配

  依赖：
  - @matechat/core (McMarkdownCard)
  - element-plus (ElMessage)
-->

<template>
  <div
    class="markdown-bubble"
    :class="bubbleClasses"
    :style="bubbleThemeVars"
  >
    <!-- 头像区域 -->
    <div v-if="showAvatar" class="bubble-avatar">
      <img
        v-if="avatarSrc && !useDefaultIcons"
        :src="avatarSrc"
        :alt="senderName"
        class="avatar-image"
      />
      <div v-else-if="useDefaultIcons" class="avatar-icon">
        <!-- 用户图标 -->
        <svg v-if="isUser" class="user-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="8" r="4" fill="currentColor"/>
          <path d="M6 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
        <!-- 助手图标 -->
        <svg v-else class="assistant-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
          <circle cx="12" cy="19" r="2" fill="currentColor"/>
          <path d="M8 19h8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </div>
      <div v-else class="avatar-placeholder">
        {{ senderName.charAt(0).toUpperCase() }}
      </div>
    </div>

    <!-- 消息内容区域 -->
    <div class="bubble-content-wrapper">
      <!-- 顶部信息：发送者和时间 -->
      <div v-if="showHeader" class="bubble-header">
        <span class="sender-name">{{ senderName }}</span>
        <span class="message-time">{{ formatTime(timestamp) }}</span>
      </div>

      <!-- 主要内容区域 -->
      <div class="bubble-content" :class="contentClasses">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-content">
          <div class="loading-dots">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </div>
          <span class="loading-text">{{ loadingText }}</span>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="isError" class="error-content">
          <div class="error-icon">⚠️</div>
          <div class="error-message">{{ content }}</div>
        </div>

        <!-- 正常内容 -->
        <div v-else class="message-content">
          <!-- 推理过程切换按钮 -->
          <div
            v-if="showReasoningToggle"
            class="reasoning-toggle"
            @click="toggleReasoning"
          >
            <span class="reasoning-icon"></span>
            <span class="reasoning-text">{{ reasoningButtonText }}</span>
            <span class="reasoning-arrow" :class="{ expanded: isReasoningExpanded }">▼</span>
          </div>

          <!-- 推理内容 -->
          <div
            v-if="showReasoningToggle && isReasoningExpanded && reasoning"
            class="reasoning-content"
          >
            <McMarkdownCard
              :content="reasoning"
              :theme="currentTheme"
              :typing="typing"
              :typingOptions="typingOptions"
              @typingEnd="handleTypingEnd"
            />
          </div>

          <!-- 主要内容渲染 -->
          <McMarkdownCard
            :content="content"
            :theme="currentTheme"
            :typing="typing"
            :typingOptions="typingOptions"
            @typingEnd="handleTypingEnd"
          />
        </div>
      </div>

      <!-- 底部操作区域 -->
      <div v-if="showActions" class="bubble-actions">
        <button
          v-for="action in availableActions"
          :key="action.key"
          class="action-btn"
          :class="action.class"
          @click="handleAction(action)"
          :disabled="disabled"
          :title="action.title"
        >
          <component :is="action.icon" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted, h, inject, watch } from 'vue'
import { McMarkdownCard } from '@matechat/core'

// Props 定义 - 更清晰的接口设计
const props = defineProps({
  // 基础内容
  content: {
    type: String,
    required: true
  },
  
  // 消息类型和状态
  messageType: {
    type: String,
    default: 'assistant', // 'user' | 'assistant' | 'system'
    validator: (value) => ['user', 'assistant', 'system'].includes(value)
  },
  
  isError: {
    type: Boolean,
    default: false
  },
  
  loading: {
    type: Boolean,
    default: false
  },
  
  disabled: {
    type: Boolean,
    default: false
  },

  // 显示配置
  showAvatar: {
    type: Boolean,
    default: true
  },
  
  showHeader: {
    type: Boolean,
    default: true
  },
  
  showActions: {
    type: Boolean,
    default: true
  },

  // 发送者信息
  senderName: {
    type: String,
    default: () => ''
  },

  avatarSrc: {
    type: String,
    default: ''
  },

  // 是否使用默认图标
  useDefaultIcons: {
    type: Boolean,
    default: true
  },

  // 时间戳
  timestamp: {
    type: Number,
    default: () => Date.now()
  },

  // 主题配置
  theme: {
    type: String,
    default: 'auto', // 'light' | 'dark' | 'auto'
    validator: (value) => ['light', 'dark', 'auto'].includes(value)
  },

  // 自定义样式
  variant: {
    type: String,
    default: 'default', // 'default' | 'minimal' | 'card'
    validator: (value) => ['default', 'minimal', 'card'].includes(value)
  },

  // 推理功能
  reasoning: {
    type: String,
    default: ''
  },

  reasoningTime: {
    type: Number,
    default: 0
  },

  hasReasoning: {
    type: Boolean,
    default: false
  },

  showReasoning: {
    type: Boolean,
    default: true
  },

  // 自定义操作按钮
  customActions: {
    type: Array,
    default: () => []
  },

  // 加载文本
  loadingText: {
    type: String,
    default: '正在思考...'
  },

  // 打字机效果配置
  typing: {
    type: Boolean,
    default: false
  },

  typingOptions: {
    type: Object,
    default: () => ({})
  },

  // 流式内容支持
  streaming: {
    type: Boolean,
    default: false
  },

  // 字体大小控制
  fontSize: {
    type: Number,
    default: 1.0, // 字体缩放比例，0.5-2.0
    validator: (value) => value >= 0.5 && value <= 2.0
  }
})

// 事件定义
const emit = defineEmits([
  'copy',
  'regenerate',
  'resend',
  'action', // 通用操作事件
  'typingEnd', // 打字机效果完成事件
  'streamingUpdate' // 流式内容更新事件
])

// 注入配置存储
const configStore = inject('configStore', null)

// 响应式状态
const isDarkMode = ref(false)
const isReasoningExpanded = ref(true)

// 计算属性
const currentTheme = computed(() => {
  if (props.theme === 'auto') {
    return isDarkMode.value ? 'dark' : 'light'
  }
  return props.theme
})

const isUser = computed(() => props.messageType === 'user')
const isAssistant = computed(() => props.messageType === 'assistant')

// 获取字体大小设置，优先使用 props，然后是配置存储，最后是默认值
const effectiveFontSize = computed(() => {
  // 如果 props 中明确指定了字体大小，使用 props 的值
  if (props.fontSize && props.fontSize !== 1.0) {
    return props.fontSize
  }

  // 否则从配置存储中获取
  if (configStore?.chat?.fontSize && typeof configStore.chat.fontSize === 'number') {
    return configStore.chat.fontSize
  }

  // 默认值
  return 1.0
})

const bubbleClasses = computed(() => [
  `bubble-${props.messageType}`,
  `bubble-${props.variant}`,
  `theme-${currentTheme.value}`,
  {
    'bubble-loading': props.loading,
    'bubble-error': props.isError,
    'bubble-disabled': props.disabled
  }
])

const contentClasses = computed(() => [
  'content-main',
  {
    'content-user': isUser.value,
    'content-assistant': isAssistant.value
  }
])

// 主题变量 - 完整的主题色彩系统
const bubbleThemeVars = computed(() => {
  const isDark = currentTheme.value === 'dark'

  // 定义完整的主题色彩系统
  const lightTheme = {
    // 基础颜色
    '--bubble-primary': '#409eff',
    '--bubble-primary-light-3': '#79bbff',
    '--bubble-primary-light-5': '#a0cfff',
    '--bubble-primary-light-7': '#c6e2ff',
    '--bubble-primary-light-8': '#d9ecff',
    '--bubble-primary-light-9': '#ecf5ff',

    '--bubble-success': '#67c23a',
    '--bubble-success-light-8': '#e1f3d8',
    '--bubble-success-light-9': '#f0f9ff',

    '--bubble-warning': '#e6a23c',
    '--bubble-warning-light': '#f0c78a',

    '--bubble-danger': '#f56c6c',
    '--bubble-danger-light-3': '#f89898',
    '--bubble-danger-light-7': '#fcd3d3',
    '--bubble-danger-light-9': '#fef0f0',

    // 背景色 - 优化后的颜色
    '--bubble-bg-color': '#f0eee6',        // 助手气泡背景颜色
    '--bubble-bg-color-page': '#f5f7fa',
    '--bubble-bg-color-overlay': '#ffffff',
    '--bubble-user-bg': '#f0eee6',         // 用户气泡背景颜色
    '--bubble-code-bg': '#fdfcfa',         // 代码背景颜色

    // 文字颜色
    '--bubble-text-color-primary': '#303133',
    '--bubble-text-color-regular': '#606266',
    '--bubble-text-color-secondary': '#909399',
    '--bubble-text-color-placeholder': '#a8abb2',

    // 边框颜色
    '--bubble-border-color': '#dcdfe6',
    '--bubble-border-color-light': '#e4e7ed',
    '--bubble-border-color-lighter': '#ebeef5',
    '--bubble-border-color-extra-light': '#f2f6fc',

    // 填充色
    '--bubble-fill-color': '#f0f2f5',
    '--bubble-fill-color-light': '#f5f7fa',
    '--bubble-fill-color-lighter': '#fafafa',
    '--bubble-fill-color-extra-light': '#fafcff',
    '--bubble-fill-color-dark': '#ebedf0',
    '--bubble-fill-color-darker': '#e6e8eb',
    '--bubble-fill-color-blank': '#ffffff',

    // 阴影
    '--bubble-shadow': '0 2px 8px rgba(0, 0, 0, 0.1)',
    '--bubble-shadow-light': '0 1px 3px rgba(0, 0, 0, 0.1)',
    '--bubble-shadow-dark': '0 4px 12px rgba(0, 0, 0, 0.15)'
  }

  const darkTheme = {
    // 基础颜色
    '--bubble-primary': '#4080ff',
    '--bubble-primary-light-3': '#5c93ff',
    '--bubble-primary-light-5': '#79a6ff',
    '--bubble-primary-light-7': '#96b9ff',
    '--bubble-primary-light-8': '#2d4b6d',
    '--bubble-primary-light-9': '#1c2b3d',

    '--bubble-success': '#67c23a',
    '--bubble-success-light-8': '#2d4a22',
    '--bubble-success-light-9': '#1f2f1a',

    '--bubble-warning': '#e6a23c',
    '--bubble-warning-light': '#b88230',

    '--bubble-danger': '#f56c6c',
    '--bubble-danger-light-3': '#c45656',
    '--bubble-danger-light-7': '#4a2626',
    '--bubble-danger-light-9': '#2e1a1a',

    // 背景色 - 优化后的颜色
    '--bubble-bg-color': '#262624',        // 助手气泡背景颜色
    '--bubble-bg-color-page': '#242424',
    '--bubble-bg-color-overlay': '#2a2a2a',
    '--bubble-user-bg': '#262624',         // 用户气泡背景颜色
    '--bubble-code-bg': '#2b2b29',         // 代码背景颜色

    // 文字颜色
    '--bubble-text-color-primary': '#e0e0e0',
    '--bubble-text-color-regular': '#b0b0b0',
    '--bubble-text-color-secondary': '#808080',
    '--bubble-text-color-placeholder': '#606060',

    // 边框颜色
    '--bubble-border-color': '#333333',
    '--bubble-border-color-light': '#3d3d3d',
    '--bubble-border-color-lighter': '#474747',
    '--bubble-border-color-extra-light': '#515151',

    // 填充色
    '--bubble-fill-color': '#2a2a2a',
    '--bubble-fill-color-light': '#303030',
    '--bubble-fill-color-lighter': '#363636',
    '--bubble-fill-color-extra-light': '#3d3d3d',
    '--bubble-fill-color-dark': '#242424',
    '--bubble-fill-color-darker': '#1f1f1f',
    '--bubble-fill-color-blank': '#1a1a1a',

    // 阴影
    '--bubble-shadow': '0 2px 8px rgba(0, 0, 0, 0.3)',
    '--bubble-shadow-light': '0 1px 3px rgba(0, 0, 0, 0.3)',
    '--bubble-shadow-dark': '0 4px 12px rgba(0, 0, 0, 0.4)'
  }

  const theme = isDark ? darkTheme : lightTheme

  // 根据消息类型设置特定的背景和文字颜色
  const vars = { ...theme }

  if (isUser.value) {
    vars['--bubble-bg'] = theme['--bubble-user-bg']
    // 根据主题设置用户消息的文字颜色
    vars['--bubble-text'] = isDark ? '#ffffff' : '#2c2c2c'
  } else {
    vars['--bubble-bg'] = theme['--bubble-bg-color']
    vars['--bubble-text'] = theme['--bubble-text-color-primary']
  }

  vars['--bubble-border'] = theme['--bubble-border-color-light']
  vars['--avatar-size'] = '36px'
  vars['--bubble-radius'] = '12px'

  // 字体大小控制 - 直接使用数字值作为缩放比例
  const fontScale = effectiveFontSize.value || 1.0
  vars['--bubble-font-scale'] = fontScale.toString()

  return vars
})

// 推理相关
const showReasoningToggle = computed(() => {
  return isAssistant.value && props.hasReasoning && props.showReasoning
})

const reasoningButtonText = computed(() => {
  if (props.reasoningTime > 0) {
    return `推理过程 (用时${props.reasoningTime.toFixed(1)}秒)`
  }
  return '推理过程'
})

// 操作按钮配置
const defaultActions = computed(() => {
  const actions = [
    {
      key: 'copy',
      title: '复制消息',
      class: 'copy-btn',
      icon: () => h('svg', {
        xmlns: 'http://www.w3.org/2000/svg',
        width: '14',
        height: '14',
        viewBox: '0 0 24 24',
        fill: 'none',
        stroke: 'currentColor',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round'
      }, [
        h('rect', { x: '9', y: '9', width: '13', height: '13', rx: '2', ry: '2' }),
        h('path', { d: 'M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1' })
      ])
    }
  ]

  if (isAssistant.value) {
    actions.push({
      key: 'regenerate',
      title: '重新生成',
      class: 'regenerate-btn',
      icon: () => h('svg', {
        xmlns: 'http://www.w3.org/2000/svg',
        width: '14',
        height: '14',
        viewBox: '0 0 24 24',
        fill: 'none',
        stroke: 'currentColor',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round'
      }, [
        h('path', { d: 'M21 2v6h-6' }),
        h('path', { d: 'M3 12a9 9 0 0 1 15-6.7L21 8' }),
        h('path', { d: 'M3 22v-6h6' }),
        h('path', { d: 'M21 12a9 9 0 0 1-15 6.7L3 16' })
      ])
    })
  }

  if (isUser.value) {
    actions.push({
      key: 'resend',
      title: '重发消息',
      class: 'resend-btn',
      icon: () => h('svg', {
        xmlns: 'http://www.w3.org/2000/svg',
        width: '14',
        height: '14',
        viewBox: '0 0 24 24',
        fill: 'none',
        stroke: 'currentColor',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round'
      }, [
        h('path', { d: 'M21 2v6h-6' }),
        h('path', { d: 'M3 12a9 9 0 0 1 15-6.7L21 8' }),
        h('path', { d: 'M3 22v-6h6' }),
        h('path', { d: 'M21 12a9 9 0 0 1-15 6.7L3 16' })
      ])
    })
  }

  return actions
})

const availableActions = computed(() => {
  return [...defaultActions.value, ...props.customActions]
})

// 方法定义
const setupThemeWatcher = () => {
  if (typeof window === 'undefined') return

  // 初始化主题状态
  isDarkMode.value = document.documentElement.classList.contains('dark')

  // 创建 MutationObserver 监听主题变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        const newIsDark = document.documentElement.classList.contains('dark')
        if (newIsDark !== isDarkMode.value) {
          isDarkMode.value = newIsDark
        }
      }
    })
  })

  // 开始观察
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  // 组件卸载时清理
  onUnmounted(() => {
    observer.disconnect()
  })
}

const toggleReasoning = () => {
  isReasoningExpanded.value = !isReasoningExpanded.value
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  // 如果是今天，只显示时间
  if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 否则显示日期和时间
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleCopy = async () => {
  try {
    const selectedText = window.getSelection().toString()
    const textToCopy = selectedText || props.content

    // 只发送事件，不在组件内部处理复制逻辑和提示
    emit('copy', textToCopy)
  } catch (error) {
    console.error('复制失败:', error)
    emit('copy', null, error)
  }
}

const handleTypingEnd = () => {
  emit('typingEnd')
}

const handleAction = (action) => {
  switch (action.key) {
    case 'copy':
      handleCopy()
      break
    case 'regenerate':
      emit('regenerate')
      break
    case 'resend':
      emit('resend')
      break
    default:
      emit('action', action)
      break
  }
}

// 生命周期
onMounted(() => {
  setupThemeWatcher()
})
</script>

<style lang="scss" scoped>
/* 主容器样式 */
.markdown-bubble {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  max-width: 100%;
  font-size: calc(14px * var(--bubble-font-scale, 1));
  line-height: 1.6;

  /* 确保文本可以被选中 */
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  cursor: text;

  /* 用户消息布局 */
  &.bubble-user {
    flex-direction: row-reverse;
    justify-content: flex-start;

    .bubble-content-wrapper {
      align-items: flex-end;
    }

    .bubble-header {
      text-align: right;
    }

    .bubble-content {
      background: var(--bubble-bg);
      color: var(--bubble-text);

      .message-content {
        color: var(--bubble-text);

        :deep(.mc-markdown-render) {
          color: var(--bubble-text) !important;

          * {
            color: var(--bubble-text) !important;
          }

          /* 用户消息中的表格样式 */
          table {
            background: rgba(255, 255, 255, 0.1) !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
          }

          th, td {
            border-color: rgba(255, 255, 255, 0.3) !important;
            background: rgba(255, 255, 255, 0.05) !important;
            color: white !important;
          }

          th {
            background: rgba(255, 255, 255, 0.15) !important;
            font-weight: 600 !important;
          }

          tr:nth-child(even) td {
            background: rgba(255, 255, 255, 0.08) !important;
          }

          tr:hover td {
            background: rgba(255, 255, 255, 0.2) !important;
          }

          /* 用户消息中的代码块 */
          code:not(.hljs) {
            background: rgba(0, 0, 0, 0.1) !important;
            color: var(--bubble-text) !important;
            border: 1px solid rgba(0, 0, 0, 0.15) !important;

            /* 用户消息中的代码换行处理 */
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            word-break: break-all !important;
            white-space: pre-wrap !important;
          }

          /* 用户消息中的引用块 */
          blockquote {
            border-left-color: rgba(0, 0, 0, 0.3) !important;
            background: rgba(0, 0, 0, 0.05) !important;
            color: var(--bubble-text) !important;
          }
        }
      }
    }
  }

  /* 助手消息布局 */
  &.bubble-assistant {
    flex-direction: row;

    .bubble-content {
      background: var(--bubble-bg);
      color: var(--bubble-text);
      border: 1px solid var(--bubble-border);
    }
  }

  /* 系统消息样式 */
  &.bubble-system {
    justify-content: center;

    .bubble-content {
      background: var(--bubble-fill-color-light);
      color: var(--bubble-text-color-secondary);
      border: 1px dashed var(--bubble-border-color);
      text-align: center;
      font-style: italic;
    }
  }

  /* 变体样式 */
  &.bubble-minimal {
    .bubble-content {
      background: transparent;
      border: none;
      box-shadow: none;
      padding: 8px 0;
    }
  }

  &.bubble-card {
    .bubble-content {
      box-shadow: var(--bubble-shadow);
      border-radius: var(--bubble-radius);
    }
  }

  /* 状态样式 */
  &.bubble-loading {
    .bubble-content {
      opacity: 0.8;
    }
  }

  &.bubble-error {
    .bubble-content {
      border-color: var(--el-color-danger-light-5);
      background: var(--el-color-danger-light-9);
    }
  }

  &.bubble-disabled {
    /* 移除整体透明度，保持消息内容清晰可见 */
    /* opacity: 0.6; */

    /* 只禁用操作按钮区域的交互 */
    .bubble-actions {
      pointer-events: none;

      .bubble-action-btn {
        opacity: 0.4;
        cursor: not-allowed;

        &:hover {
          opacity: 0.4;
          transform: none;
          box-shadow: none;
        }
      }
    }

    /* 保持消息内容完全可见和清晰 */
    .bubble-content,
    .bubble-header,
    .bubble-avatar {
      opacity: 1;
      filter: none;
    }
  }
}

/* 头像样式 */
.bubble-avatar {
  flex-shrink: 0;
  width: var(--avatar-size);
  height: var(--avatar-size);

  /* 头像不应该被选中 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  .avatar-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--bubble-border);
  }

  .avatar-icon {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--bubble-border);
    transition: all 0.3s ease;

    .user-icon {
      width: 20px;
      height: 20px;
      color: var(--bubble-primary);
    }

    .assistant-icon {
      width: 18px;
      height: 18px;
      color: var(--bubble-success);
    }
  }

  .avatar-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: var(--bubble-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    border: 2px solid var(--bubble-border);
  }
}

/* 用户消息的头像样式 */
.bubble-user .bubble-avatar {
  .avatar-icon {
    background: var(--bubble-primary-light-9);

    .user-icon {
      color: var(--bubble-primary);
    }
  }
}

/* 助手消息的头像样式 */
.bubble-assistant .bubble-avatar {
  .avatar-icon {
    background: var(--bubble-success-light-9);

    .assistant-icon {
      color: var(--bubble-success);
    }
  }
}

/* 头像悬停效果 */
.bubble-avatar .avatar-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 内容包装器 */
.bubble-content-wrapper {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 头部信息 */
.bubble-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--bubble-text-color-secondary);
  padding: 0 4px;

  /* 头部信息可以被选中 */
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  cursor: text;

  .sender-name {
    font-weight: 600;
    color: var(--bubble-text-color-primary);
    font-size: calc(13px * var(--bubble-font-scale, 1));
  }

  .message-time {
    opacity: 0.7;
    font-size: calc(11px * var(--bubble-font-scale, 1));
  }
}

/* 主要内容区域 */
.bubble-content {
  background: var(--bubble-bg);
  color: var(--bubble-text);
  border-radius: var(--bubble-radius);
  padding: 12px 16px;
  border: 1px solid var(--bubble-border);
  transition: all 0.3s ease;
  word-wrap: break-word;
  overflow-wrap: break-word;

  /* 确保内容可以被选中 */
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  cursor: text;

  &:hover {
    box-shadow: var(--bubble-shadow);
  }
}

/* 加载状态 */
.loading-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;

  .loading-dots {
    display: flex;
    gap: 4px;

    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--bubble-primary);
      animation: loading-bounce 1.4s infinite ease-in-out;

      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }

  .loading-text {
    color: var(--bubble-text-color-secondary);
    font-size: calc(13px * var(--bubble-font-scale, 1));
  }
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 错误状态 */
.error-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--el-color-danger-light-9);
  border: 1px solid var(--el-color-danger-light-7);
  border-left: 4px solid var(--el-color-danger);
  border-radius: 8px;
  color: var(--el-color-danger);

  .error-icon {
    font-size: 16px;
    flex-shrink: 0;
  }

  .error-message {
    font-weight: 500;
    line-height: 1.5;
  }
}

/* 推理过程切换 */
.reasoning-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin-bottom: 12px;
  background: var(--bubble-fill-color-light);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: calc(13px * var(--bubble-font-scale, 1));
  color: var(--bubble-text-color-regular);
  width: fit-content;

  /* 推理切换按钮不应该被选中 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  &:hover {
    background: var(--bubble-fill-color);
    color: var(--bubble-text-color-primary);
  }

  .reasoning-icon {
    font-size: calc(14px * var(--bubble-font-scale, 1));

    &:empty::before {
      content: "🧠";
    }
  }

  .reasoning-text {
    font-weight: 500;
  }

  .reasoning-arrow {
    font-size: calc(10px * var(--bubble-font-scale, 1));
    transition: transform 0.2s ease;

    &.expanded {
      transform: rotate(180deg);
    }
  }
}

/* 推理内容区域 */
.reasoning-content {
  margin-bottom: 16px;
  padding: 16px 20px;
  background: var(--bubble-fill-color-extra-light);
  border-left: 4px solid var(--bubble-warning);
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: var(--bubble-text-color-secondary);
  box-shadow: var(--bubble-shadow-light);

  /* 确保推理内容可以被选中 */
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  cursor: text;
}

/* 操作按钮区域 */
.bubble-actions {
  display: flex;
  gap: 6px;
  margin-top: 8px;
  padding: 0 4px;
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateY(-4px);

  /* 操作按钮不应该被选中 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  .action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 6px;
    background: var(--el-fill-color-light);
    color: var(--el-text-color-regular);
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    svg {
      flex-shrink: 0;
      width: 14px;
      height: 14px;
    }
  }
}

/* 鼠标悬停显示操作按钮 */
.markdown-bubble:hover .bubble-actions {
  opacity: 1;
  transform: translateY(0);
}

.bubble-actions:hover {
  opacity: 1;
  transform: translateY(0);
}

/* 深色模式下用户消息的操作按钮样式 */
.theme-dark .bubble-user .bubble-actions {
  .action-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      color: white;
      border-color: rgba(255, 255, 255, 0.5);
    }

    svg {
      color: white;
    }
  }
}

/* 深色模式适配 */
.theme-dark {
  --bubble-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

  .bubble-actions .action-btn {
    background: var(--el-fill-color);
    color: var(--el-text-color-regular);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

    &:hover {
      background: var(--el-color-primary-light-8);
      color: var(--el-color-primary-light-3);
      box-shadow: 0 2px 8px rgba(64, 128, 255, 0.2);
    }
  }

  .error-content {
    background: rgba(245, 108, 108, 0.15);
    border-color: rgba(245, 108, 108, 0.3);
    color: var(--el-color-danger-light-3);
    box-shadow: 0 2px 8px rgba(245, 108, 108, 0.2);
  }

  .reasoning-toggle {
    background: var(--bubble-fill-color-dark);
    border: 1px solid var(--bubble-border-color);

    &:hover {
      background: var(--bubble-fill-color);
      border-color: var(--bubble-border-color-light);
    }
  }

  .reasoning-content {
    background: var(--bubble-fill-color-darker);
    border-left-color: var(--bubble-warning-light);
    color: var(--bubble-text-color-regular);
    box-shadow: var(--bubble-shadow-light);
  }

  /* 深色模式下的头像样式 */
  .bubble-avatar .avatar-icon {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    }
  }

  .bubble-user .bubble-avatar .avatar-icon {
    background: var(--el-color-primary-light-8);
  }

  .bubble-assistant .bubble-avatar .avatar-icon {
    background: var(--el-color-success-light-8);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-bubble {
    gap: 8px;
    margin-bottom: 12px;

    --avatar-size: 32px;

    .bubble-content {
      padding: 10px 12px;
      font-size: calc(13px * var(--bubble-font-scale, 1));
    }

    .bubble-header {
      font-size: calc(11px * var(--bubble-font-scale, 1));

      .sender-name {
        font-size: calc(12px * var(--bubble-font-scale, 1));
      }
    }

    .bubble-actions .action-btn {
      width: 24px;
      height: 24px;

      svg {
        width: 12px;
        height: 12px;
      }
    }
  }
}

/* Markdown 内容样式增强 */
.message-content {
  /* 确保消息内容可以被选中 */
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  cursor: text;

  :deep(.mc-markdown-render) {
    font-size: calc(14px * var(--bubble-font-scale, 1));
    line-height: 1.6;
    color: inherit;

    /* 确保 Markdown 渲染内容可以被选中 */
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    cursor: text;

    p {
      margin: 0.8em 0;
      line-height: 1.7;
    }

    h1, h2, h3, h4, h5, h6 {
      margin: 1.2em 0 0.8em 0;
      font-weight: 600;
      line-height: 1.3;
    }

    ul, ol {
      margin: 0.8em 0;
      padding-left: 2em;
    }

    li {
      margin: 0.3em 0;
      line-height: 1.6;
    }

    code:not(.hljs) {
      background: var(--bubble-code-bg);
      color: var(--bubble-primary);
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: calc(0.9em * var(--bubble-font-scale, 1));
      border: 1px solid var(--bubble-border-color-lighter);

      /* 行内代码换行处理 */
      word-wrap: break-word;
      overflow-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
    }

    blockquote {
      border-left: 4px solid var(--el-color-primary);
      background: var(--el-fill-color-extra-light);
      padding: 12px 16px;
      margin: 1.2em 0;
      border-radius: 0 6px 6px 0;
      font-style: italic;
    }

    /* 禁止代码块 header 和工具栏被选中 */
    .mc-code-block-header,
    .mc-code-lang,
    .mc-code-block-actions,
    .mc-action-btn,
    .mc-toggle-btn,
    .mc-copy-btn {
      user-select: none !important;
      -webkit-user-select: none !important;
      -moz-user-select: none !important;
      -ms-user-select: none !important;
    }

    /* 使用 :deep() 确保样式穿透到子组件 */
    :deep(.mc-code-block-header),
    :deep(.mc-code-lang),
    :deep(.mc-code-block-actions),
    :deep(.mc-action-btn),
    :deep(.mc-toggle-btn),
    :deep(.mc-copy-btn) {
      user-select: none !important;
      -webkit-user-select: none !important;
      -moz-user-select: none !important;
      -ms-user-select: none !important;
    }

    /* 代码块容器样式 */
    pre {
      background: var(--bubble-code-bg);
      border: 1px solid var(--bubble-border-color-lighter);
      border-radius: 8px;
      padding: 16px;
      margin: 1.2em 0;
      overflow-x: auto;

      /* 换行和文本处理 */
      white-space: pre-wrap;
      word-wrap: break-word;
      overflow-wrap: break-word;
      word-break: break-all;

      /* 限制最大宽度，防止撑开容器 */
      max-width: 100%;
      box-sizing: border-box;

      /* 确保代码块可以被选中 */
      user-select: text;
      -webkit-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      cursor: text;

      code {
        background: transparent !important;
        border: none !important;
        padding: 0 !important;

        /* 继承父元素的换行样式 */
        white-space: inherit;
        word-wrap: inherit;
        overflow-wrap: inherit;
        word-break: inherit;

        /* 确保代码内容可以被选中 */
        user-select: text;
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        cursor: text;
      }
    }

    



    /* 表格样式 */
    table {
      border-collapse: collapse;
      margin: 1.2em 0;
      width: 100%;
      background: var(--el-bg-color);
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      /* 确保表格内容可以被选中 */
      user-select: text;
      -webkit-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      cursor: text;
    }

    th, td {
      border: 1px solid var(--el-border-color-light);
      padding: 12px 16px;
      text-align: left;
      background: var(--el-bg-color);

      /* 确保表格单元格内容可以被选中 */
      user-select: text;
      -webkit-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      cursor: text;
    }

    th {
      background: var(--el-fill-color-light);
      font-weight: 600;
      color: var(--el-text-color-primary);
      font-size: calc(13px * var(--bubble-font-scale, 1));
    }

    tr:nth-child(even) td {
      background: var(--el-fill-color-extra-light);
    }

    tr:hover td {
      background: var(--el-fill-color-light);
    }
  }
}

/* 深色模式下的样式适配 */
.theme-dark {

  .message-content :deep(.mc-markdown-render) {
    /* 深色模式下的表格样式 */
    table {
      background: var(--el-fill-color-dark);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    th, td {
      border-color: var(--el-border-color);
      background: var(--el-fill-color-dark);
    }

    th {
      background: var(--el-fill-color);
      color: var(--el-text-color-primary);
    }

    tr:nth-child(even) td {
      background: var(--el-fill-color-darker);
    }

    tr:hover td {
      background: var(--el-fill-color);
    }

    /* 深色模式下的代码块 */
    code:not(.hljs) {
      background: var(--bubble-code-bg);
      color: var(--bubble-primary-light-3);
      border: 1px solid var(--bubble-border-color);

      /* 深色模式下的代码换行处理 */
      word-wrap: break-word;
      overflow-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
    }

    /* 深色模式下的引用块 */
    blockquote {
      background: var(--bubble-fill-color-darker);
      border-left-color: var(--bubble-primary-light-3);
      color: var(--bubble-text-color-regular);
      box-shadow: var(--bubble-shadow-light);
    }

    /* 深色模式下的代码块容器 */
    pre {
      background: var(--bubble-code-bg);
      border-color: var(--bubble-border-color);
      box-shadow: var(--bubble-shadow-light);
    }
  }

  /* 深色模式下的用户消息特殊样式 */
  .bubble-user {
    .bubble-content {
      .message-content {
        color: #ffffff !important;

        :deep(.mc-markdown-render) {
          color: #ffffff !important;

          * {
            color: #ffffff !important;
          }

          /* 深色模式下用户消息中的代码块 */
          code:not(.hljs) {
            background: rgba(255, 255, 255, 0.15) !important;
            color: rgba(255, 255, 255, 0.95) !important;
            border: 1px solid rgba(255, 255, 255, 0.25) !important;

            /* 深色模式下用户消息中的代码换行处理 */
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            word-break: break-all !important;
            white-space: pre-wrap !important;
          }

          /* 深色模式下用户消息中的引用块 */
          blockquote {
            border-left-color: rgba(255, 255, 255, 0.6) !important;
            background: rgba(255, 255, 255, 0.1) !important;
            color: rgba(255, 255, 255, 0.95) !important;
          }
        }
      }
    }
  }
}
</style>
