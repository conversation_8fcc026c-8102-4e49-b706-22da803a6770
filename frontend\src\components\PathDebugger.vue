<template>
  <div class="path-debugger">
    <h2>路径调试信息</h2>
    
    <div class="debug-section">
      <h3>系统信息</h3>
      <div v-if="pathInfo" class="info-grid">
        <div class="info-item">
          <label>操作系统:</label>
          <span>{{ pathInfo.system }}</span>
        </div>
        <div class="info-item">
          <label>是否打包:</label>
          <span>{{ pathInfo.is_frozen ? '是' : '否' }}</span>
        </div>
        <div class="info-item">
          <label>可执行文件路径:</label>
          <span>{{ pathInfo.executable_path }}</span>
        </div>
        <div class="info-item">
          <label>当前工作目录:</label>
          <span>{{ pathInfo.current_working_dir }}</span>
        </div>
      </div>
    </div>

    <div class="debug-section">
      <h3>应用路径</h3>
      <div v-if="pathInfo" class="info-grid">
        <div class="info-item">
          <label>应用程序目录:</label>
          <span>{{ pathInfo.application_dir }}</span>
        </div>
        <div class="info-item">
          <label>数据基础目录:</label>
          <span>{{ pathInfo.base_dir }}</span>
        </div>
        <div class="info-item">
          <label>配置目录:</label>
          <span>{{ pathInfo.config_dir }}</span>
        </div>
      </div>
    </div>

    <div class="debug-section">
      <h3>操作</h3>
      <div class="button-group">
        <button @click="refreshPathInfo" :disabled="loading">
          {{ loading ? '刷新中...' : '刷新路径信息' }}
        </button>
        <button @click="openBaseDir">打开数据目录</button>
        <button @click="openConfigDir">打开配置目录</button>
      </div>
    </div>

    <div v-if="error" class="error-message">
      {{ error }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'PathDebugger',
  data() {
    return {
      pathInfo: null,
      loading: false,
      error: null
    }
  },
  mounted() {
    this.refreshPathInfo()
  },
  methods: {
    async refreshPathInfo() {
      this.loading = true
      this.error = null
      
      try {
        const response = await window.pywebview.api.get_path_info()
        
        if (response.status === 'success') {
          this.pathInfo = response.data
        } else {
          this.error = response.message || '获取路径信息失败'
        }
      } catch (err) {
        this.error = `请求失败: ${err.message}`
      } finally {
        this.loading = false
      }
    },
    
    async openBaseDir() {
      if (!this.pathInfo) return
      
      try {
        await window.pywebview.api.open_directory(this.pathInfo.base_dir)
      } catch (err) {
        this.error = `打开目录失败: ${err.message}`
      }
    },
    
    async openConfigDir() {
      if (!this.pathInfo) return
      
      try {
        await window.pywebview.api.open_directory(this.pathInfo.config_dir)
      } catch (err) {
        this.error = `打开目录失败: ${err.message}`
      }
    }
  }
}
</script>

<style scoped>
.path-debugger {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.debug-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.debug-section h3 {
  margin-top: 0;
  color: #333;
}

.info-grid {
  display: grid;
  gap: 10px;
}

.info-item {
  display: grid;
  grid-template-columns: 150px 1fr;
  gap: 10px;
  align-items: center;
}

.info-item label {
  font-weight: bold;
  color: #555;
}

.info-item span {
  font-family: monospace;
  background: #fff;
  padding: 5px 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
  word-break: break-all;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.button-group button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button-group button:hover:not(:disabled) {
  background: #0056b3;
}

.button-group button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.error-message {
  padding: 10px;
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  margin-top: 20px;
}
</style>
