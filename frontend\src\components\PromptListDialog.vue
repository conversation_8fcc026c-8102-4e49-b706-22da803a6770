<template>
  <el-dialog
    v-model="visible"
    :title="`${ruleName} - 提示词列表`"
    width="90%"
    class="prompts-dialog"
    :close-on-press-escape="true"
    :destroy-on-close="false"
    :modal-append-to-body="true"
    :fullscreen="true"
  >
    <div class="prompt-list-container">
      <!-- 现代化顶部导航栏 -->
      <div class="prompt-list-header">
        <div class="header-left">
          <el-button @click="visible = false" class="close-button" text>
            <el-icon><ArrowLeft /></el-icon>
            <span>返回</span>
          </el-button>
          <div class="header-title-section">
            <h2 class="dialog-title">{{ ruleName }}</h2>
            <div class="dialog-subtitle">{{ filteredPromptsList.length }} 个提示词</div>
          </div>
        </div>
        <div class="header-right">
          <el-input
            v-model="searchQuery"
            placeholder="搜索提示词内容..."
            class="prompt-search"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 主内容区域 - 左右分栏布局 -->
      <div class="prompt-list-content">
        <!-- 左侧提示词列表 -->
        <div class="prompt-sidebar">
          <div class="sidebar-header">
            <div class="sidebar-title">提示词列表</div>
            <div class="sidebar-count">{{ filteredPromptsList.length }}</div>
          </div>
          <div class="sidebar-content">
            <div class="prompt-list" v-if="filteredPromptsList.length > 0">
              <div
                v-for="(prompt, index) in filteredPromptsList"
                :key="prompt.id"
                class="prompt-card"
                :class="{ 'active': selectedPromptIndex === index }"
                @click="selectPrompt(index)"
              >
                <div class="prompt-card-header">
                  <div class="prompt-card-title">
                    {{ prompt.name || '未命名提示词' }}
                  </div>
                  <div class="prompt-card-time">
                    {{ formatDate(prompt.timestamp) }}
                  </div>
                </div>
                <div class="prompt-card-preview">
                  {{ getPromptPreview(prompt.content) }}
                </div>
                <div class="prompt-card-footer">
                  <div class="prompt-card-length">
                    {{ getContentLength(prompt.content) }} 字符
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              <el-empty
                description="暂无匹配的提示词"
                :image-size="120"
              >
                <template #image>
                  <el-icon :size="48" class="empty-icon"><Search /></el-icon>
                </template>
              </el-empty>
            </div>
          </div>
        </div>

        <!-- 右侧内容预览区域 -->
        <div class="prompt-content-view">
          <template v-if="selectedPrompt">
            <div class="content-header">
              <div class="content-title-section">
                <h3 class="content-title">{{ selectedPrompt.name || '未命名提示词' }}</h3>
                <div class="content-meta">
                  <span class="content-length">{{ getContentLength(selectedPrompt.content) }} 字符</span>
                  <span class="content-time">{{ formatDate(selectedPrompt.timestamp) }}</span>
                </div>
              </div>
              <div class="content-actions">
                <el-button
                  type="primary"
                  @click="copyPromptContent(selectedPrompt.content)"
                  class="copy-button"
                  :class="{ 'copied': isCopied }"
                >
                  <el-icon>
                    <component :is="isCopied ? Check : Document" />
                  </el-icon>
                  {{ isCopied ? '已复制' : '复制内容' }}
                </el-button>
              </div>
            </div>
            <div class="content-body">
              <div class="content-wrapper">
                <pre class="prompt-content-text">{{ selectedPrompt.content }}</pre>
              </div>
            </div>
          </template>
          <div v-else class="content-empty">
            <el-empty
              description="请从左侧选择一个提示词查看详情"
              :image-size="160"
            >
              <template #image>
                <el-icon :size="64" class="empty-icon"><Document /></el-icon>
              </template>
            </el-empty>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Check, ArrowLeft, Search } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  ruleName: {
    type: String,
    required: true
  },
  prompts: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'use-prompt'])

// 内部状态
const visible = ref(false)
const searchQuery = ref('')
const selectedPromptIndex = ref(0)
const isCopied = ref(false)

// 监听visible变化
watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 监听modelValue变化
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    // 打开时重置状态
    searchQuery.value = ''
    selectedPromptIndex.value = props.prompts.length > 0 ? 0 : -1
  }
})

// 筛选后的提示词列表
const filteredPromptsList = computed(() => {
  if (!props.prompts) return []
  
  if (!searchQuery.value) return props.prompts
  
  const query = searchQuery.value.toLowerCase()
  return props.prompts.filter(prompt => {
    return (
      (prompt.name && prompt.name.toLowerCase().includes(query)) ||
      (prompt.content && prompt.content.toLowerCase().includes(query))
    )
  })
})

// 当前选中的提示词
const selectedPrompt = computed(() => {
  if (filteredPromptsList.value.length === 0) return null
  if (selectedPromptIndex.value < 0 || selectedPromptIndex.value >= filteredPromptsList.value.length) {
    return filteredPromptsList.value[0]
  }
  return filteredPromptsList.value[selectedPromptIndex.value]
})

// 选择提示词
const selectPrompt = (index) => {
  selectedPromptIndex.value = index
}

// 获取提示词预览
const getPromptPreview = (content) => {
  if (!content) return '无内容'
  const trimmed = content.replace(/\s+/g, ' ').trim()
  return trimmed.length > 80 ? trimmed.substring(0, 80) + '...' : trimmed
}

// 获取内容长度
const getContentLength = (content) => {
  if (!content) return 0
  return content.length
}

// 复制提示词内容
const copyPromptContent = (content) => {
  window.pywebview.api.copy_to_clipboard(content)
    .then(() => {
      isCopied.value = true
      setTimeout(() => {
        isCopied.value = false
      }, 2000)
      ElMessage({
        message: '内容已复制到剪贴板',
        type: 'success',
        duration: 2000,
        offset: 80
      })
    })
    .catch(() => {
      ElMessage.error('复制失败')
    })
}

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style lang="scss" scoped>
.prompts-dialog {
  /* 全局字体设置 */
  :deep(*) {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 添加全局用户选择控制 */
  :deep(.el-dialog__header),
  :deep(.el-dialog__title),
  .prompt-list-header,
  .header-left {
    user-select: none;
  }

  :deep(.el-dialog) {
    margin: 0 !important;
    position: fixed;
    top: 16px;
    right: 16px;
    bottom: 16px;
    left: 16px;
    height: calc(100vh - 32px);
    display: flex;
    flex-direction: column;
    background-color: var(--el-bg-color);
    border-radius: 12px;
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.1),
      0 8px 16px rgba(0, 0, 0, 0.06);
    max-height: calc(100vh - 32px);
    border: 1px solid var(--el-border-color-lighter);

    .el-dialog__header {
      display: none;
    }

    .el-dialog__body {
      flex: 1;
      padding: 0;
      margin: 0;
      overflow: hidden;
      height: 100%;
    }
  }

  .prompt-list-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .prompt-list-header {
      height: 72px;
      padding: 0 32px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      background: linear-gradient(to bottom,
        var(--el-bg-color) 0%,
        var(--el-bg-color-overlay) 100%);
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      z-index: 10;
      backdrop-filter: blur(8px);

      .header-left {
        display: flex;
        align-items: center;
        gap: 20px;

        .close-button {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 15px;
          height: 44px;
          padding: 0 20px;
          border-radius: 10px;
          transition: all 0.2s ease;
          color: var(--el-color-white);
          background: var(--el-color-primary);
          border: 1px solid var(--el-color-primary);
          box-shadow: 0 2px 4px rgba(var(--el-color-primary-rgb), 0.2);

          &:hover {
            background: var(--el-color-primary-dark-2);
            border-color: var(--el-color-primary-dark-2);
            transform: translateX(-2px);
            box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.3);
          }

          &:active {
            transform: translateX(-1px);
            box-shadow: 0 2px 4px rgba(var(--el-color-primary-rgb), 0.4);
          }

          .el-icon {
            font-size: 16px;
            transition: transform 0.2s ease;
          }

          span {
            font-weight: 500;
          }

          &:hover .el-icon {
            transform: translateX(-1px);
          }
        }

        .header-title-section {
          .dialog-title {
            margin: 0 0 2px 0;
            font-size: 22px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            line-height: 1.2;
          }

          .dialog-subtitle {
            font-size: 13px;
            color: var(--el-text-color-secondary);
            font-weight: 500;
          }
        }
      }

      .header-right {
        .prompt-search {
          width: 320px;

          :deep(.el-input__wrapper) {
            border-radius: 10px;
            height: 44px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid var(--el-border-color-light);
            transition: all 0.2s ease;

            &:hover {
              border-color: var(--el-color-primary-light-5);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            }

            &.is-focus {
              border-color: var(--el-color-primary);
              box-shadow: 0 0 0 3px rgba(var(--el-color-primary-rgb), 0.1);
            }
          }

          :deep(.el-input__inner) {
            font-size: 15px;
            padding: 0 16px;
          }
        }
      }
    }

    .prompt-list-content {
      display: flex;
      position: absolute;
      top: 72px;
      left: 0;
      right: 0;
      bottom: 0;
      overflow: hidden;

      .prompt-sidebar {
        width: 380px;
        min-width: 320px;
        max-width: 480px;
        border-right: 1px solid var(--el-border-color-lighter);
        background-color: var(--el-bg-color-page);
        display: flex;
        flex-direction: column;

        .sidebar-header {
          padding: 20px 24px 16px;
          border-bottom: 1px solid var(--el-border-color-lighter);
          background: var(--el-bg-color);
          display: flex;
          justify-content: space-between;
          align-items: center;

          .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }

          .sidebar-count {
            background: var(--el-color-primary-light-8);
            color: var(--el-color-primary);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 13px;
            font-weight: 600;
          }
        }

        .sidebar-content {
          flex: 1;
          overflow-y: auto;
          padding: 8px;

          .prompt-list {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .prompt-card {
              background: var(--el-bg-color);
              border: 1px solid var(--el-border-color-lighter);
              border-radius: 12px;
              padding: 16px;
              cursor: pointer;
              transition: all 0.2s ease;
              position: relative;

              &:hover {
                border-color: var(--el-color-primary-light-5);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
                transform: translateY(-1px);
              }

              &.active {
                border-color: var(--el-color-primary);
                background: var(--el-color-primary-light-9);
                box-shadow:
                  0 4px 16px rgba(var(--el-color-primary-rgb), 0.15),
                  0 0 0 1px var(--el-color-primary-light-7);

                &::before {
                  content: '';
                  position: absolute;
                  left: 0;
                  top: 0;
                  bottom: 0;
                  width: 4px;
                  background: var(--el-color-primary);
                  border-radius: 2px 0 0 2px;
                }
              }

              .prompt-card-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 12px;

                .prompt-card-title {
                  font-weight: 600;
                  font-size: 15px;
                  color: var(--el-text-color-primary);
                  line-height: 1.4;
                  flex: 1;
                  margin-right: 12px;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                }

                .prompt-card-time {
                  font-size: 12px;
                  color: var(--el-text-color-secondary);
                  white-space: nowrap;
                  flex-shrink: 0;
                }
              }

              .prompt-card-preview {
                font-size: 13px;
                color: var(--el-text-color-regular);
                line-height: 1.5;
                margin-bottom: 12px;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }

              .prompt-card-footer {
                .prompt-card-length {
                  font-size: 12px;
                  color: var(--el-text-color-secondary);
                  background: var(--el-fill-color-light);
                  padding: 2px 8px;
                  border-radius: 6px;
                  display: inline-block;
                }
              }
            }
          }

          .empty-state {
            padding: 60px 20px;
            text-align: center;

            .empty-icon {
              color: var(--el-text-color-placeholder);
            }
          }
        }
      }

      .prompt-content-view {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: var(--el-bg-color);

        .content-header {
          padding: 24px 32px 20px;
          border-bottom: 1px solid var(--el-border-color-lighter);
          background: var(--el-bg-color);
          display: flex;
          justify-content: space-between;
          align-items: flex-start;

          .content-title-section {
            flex: 1;
            margin-right: 24px;

            .content-title {
              margin: 0 0 8px 0;
              font-size: 24px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              line-height: 1.3;
              word-break: break-word;
            }

            .content-meta {
              display: flex;
              gap: 16px;

              .content-length,
              .content-time {
                font-size: 13px;
                color: var(--el-text-color-secondary);
                background: var(--el-fill-color-light);
                padding: 4px 10px;
                border-radius: 8px;
                font-weight: 500;
              }
            }
          }

          .content-actions {
            .copy-button {
              height: 44px;
              padding: 0 20px;
              font-size: 15px;
              font-weight: 500;
              border-radius: 10px;
              transition: all 0.2s ease;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.2);
              }

              &.copied {
                background-color: var(--el-color-success);
                border-color: var(--el-color-success);

                &:hover {
                  background-color: var(--el-color-success-dark-2);
                  border-color: var(--el-color-success-dark-2);
                }
              }
            }
          }
        }

        .content-body {
          flex: 1;
          position: relative;
          overflow: hidden;

          .content-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
            padding: 32px;

            .prompt-content-text {
              white-space: pre-wrap;
              line-height: 1.7;
              font-size: 16px;
              color: var(--el-text-color-primary);
              background: var(--el-bg-color-page);
              padding: 24px;
              border-radius: 12px;
              border: 1px solid var(--el-border-color-lighter);
              font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;

              /* 代码块样式 */
              code {
                background-color: var(--el-fill-color-light);
                padding: 0.2em 0.4em;
                border-radius: 4px;
                font-size: 0.9em;
              }

              /* 选中文本样式 */
              &::selection {
                background-color: var(--el-color-primary-light-8);
              }
            }
          }
        }

        .content-empty {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 60px 40px;

          .empty-icon {
            color: var(--el-text-color-placeholder);
          }
        }
      }
    }
  }
}

/* 自定义滚动条样式 */
.sidebar-content,
.content-wrapper {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.15);
    border-radius: 3px;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.25);
    }
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .prompts-dialog {
    :deep(.el-dialog) {
      top: 8px;
      right: 8px;
      bottom: 8px;
      left: 8px;
      height: calc(100vh - 16px);
      border-radius: 8px;
    }

    .prompt-list-container {
      .prompt-list-header {
        height: 64px;
        padding: 0 20px;

        .header-left {
          gap: 16px;

          .close-button {
            height: 40px;
            padding: 0 16px;
            border-radius: 8px;

            .el-icon {
              font-size: 15px;
            }
          }

          .header-title-section {
            .dialog-title {
              font-size: 20px;
            }
          }
        }

        .header-right {
          .prompt-search {
            width: 280px;
          }
        }
      }

      .prompt-list-content {
        top: 64px;

        .prompt-sidebar {
          width: 320px;
          min-width: 280px;
        }

        .prompt-content-view {
          .content-header {
            padding: 20px 24px 16px;

            .content-title-section {
              .content-title {
                font-size: 22px;
              }
            }
          }

          .content-body {
            .content-wrapper {
              padding: 24px;

              .prompt-content-text {
                padding: 20px;
                font-size: 15px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .prompts-dialog {
    :deep(.el-dialog) {
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      height: 100vh;
      border-radius: 0;
    }

    .prompt-list-container {
      .prompt-list-header {
        height: 56px;
        padding: 0 16px;

        .header-left {
          gap: 12px;

          .close-button {
            height: 36px;
            padding: 0 12px;
            font-size: 14px;
            border-radius: 8px;

            .el-icon {
              font-size: 14px;
            }
          }

          .header-title-section {
            .dialog-title {
              font-size: 18px;
            }

            .dialog-subtitle {
              font-size: 12px;
            }
          }
        }

        .header-right {
          .prompt-search {
            width: 200px;

            :deep(.el-input__wrapper) {
              height: 36px;
            }
          }
        }
      }

      .prompt-list-content {
        top: 56px;
        flex-direction: column;

        .prompt-sidebar {
          width: 100%;
          height: 40%;
          border-right: none;
          border-bottom: 1px solid var(--el-border-color-lighter);

          .sidebar-content {
            padding: 4px;

            .prompt-list {
              .prompt-card {
                padding: 12px;

                .prompt-card-header {
                  margin-bottom: 8px;

                  .prompt-card-title {
                    font-size: 14px;
                  }

                  .prompt-card-time {
                    font-size: 11px;
                  }
                }

                .prompt-card-preview {
                  font-size: 12px;
                  margin-bottom: 8px;
                  -webkit-line-clamp: 1;
                }
              }
            }
          }
        }

        .prompt-content-view {
          height: 60%;

          .content-header {
            padding: 16px 20px 12px;

            .content-title-section {
              margin-right: 16px;

              .content-title {
                font-size: 18px;
              }

              .content-meta {
                gap: 8px;

                .content-length,
                .content-time {
                  font-size: 11px;
                  padding: 2px 6px;
                }
              }
            }

            .content-actions {
              .copy-button {
                height: 36px;
                padding: 0 16px;
                font-size: 14px;
              }
            }
          }

          .content-body {
            .content-wrapper {
              padding: 16px;

              .prompt-content-text {
                padding: 16px;
                font-size: 14px;
                line-height: 1.6;
              }
            }
          }
        }
      }
    }
  }
}

/* 暗色主题适配 */
html.dark {
  .prompts-dialog {
    :deep(.el-dialog) {
      background-color: var(--el-bg-color);
      border-color: var(--el-border-color);
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 8px 16px rgba(0, 0, 0, 0.2);
    }

    .prompt-list-container {
      .prompt-list-header {
        background: linear-gradient(to bottom,
          var(--el-bg-color) 0%,
          var(--el-bg-color-overlay) 100%);
        border-bottom-color: var(--el-border-color);

        .header-left {
          .close-button {
            background: var(--el-color-primary);
            border-color: var(--el-color-primary);
            color: var(--el-color-white);
            box-shadow: 0 2px 4px rgba(var(--el-color-primary-rgb), 0.3);

            &:hover {
              background: var(--el-color-primary-light-3);
              border-color: var(--el-color-primary-light-3);
              box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.4);
            }

            &:active {
              box-shadow: 0 2px 4px rgba(var(--el-color-primary-rgb), 0.5);
            }
          }
        }
      }

      .prompt-list-content {
        .prompt-sidebar {
          background-color: var(--el-bg-color-page);
          border-right-color: var(--el-border-color);

          .sidebar-header {
            background: var(--el-bg-color);
            border-bottom-color: var(--el-border-color);

            .sidebar-count {
              background: var(--el-color-primary-light-8);
              color: var(--el-color-primary-light-3);
            }
          }

          .sidebar-content {
            /* 暗色主题滚动条 */
            &::-webkit-scrollbar-thumb {
              background: rgba(255, 255, 255, 0.2);

              &:hover {
                background: rgba(255, 255, 255, 0.3);
              }
            }

            .prompt-list {
              .prompt-card {
                background: var(--el-bg-color);
                border-color: var(--el-border-color);

                &:hover {
                  border-color: var(--el-color-primary-light-5);
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }

                &.active {
                  background: var(--el-color-primary-light-9);
                  border-color: var(--el-color-primary);
                  box-shadow:
                    0 4px 16px rgba(var(--el-color-primary-rgb), 0.2),
                    0 0 0 1px var(--el-color-primary-light-7);
                }

                .prompt-card-footer {
                  .prompt-card-length {
                    background: var(--el-fill-color);
                  }
                }
              }
            }
          }
        }

        .prompt-content-view {
          background: var(--el-bg-color);

          .content-header {
            background: var(--el-bg-color);
            border-bottom-color: var(--el-border-color);

            .content-title-section {
              .content-meta {
                .content-length,
                .content-time {
                  background: var(--el-fill-color);
                }
              }
            }
          }

          .content-body {
            .content-wrapper {
              /* 暗色主题滚动条 */
              &::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.2);

                &:hover {
                  background: rgba(255, 255, 255, 0.3);
                }
              }

              .prompt-content-text {
                background: var(--el-bg-color-page);
                border-color: var(--el-border-color);
              }
            }
          }
        }
      }
    }
  }
}
</style> 