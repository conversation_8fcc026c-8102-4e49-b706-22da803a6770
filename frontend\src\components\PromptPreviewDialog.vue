<template>
  <el-dialog
    v-model="visible"
    title="提示词"
    style="user-select: none;"
    width="70%"
    class="prompt-preview-dialog"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    :fullscreen="false"
    :modal-class="'prompt-preview-modal'"
  >
    <div class="preview-content">
      <div class="preview-header">
        <div class="preview-info">
          <h3 class="preview-title">{{ promptName }}</h3>
          <p v-if="promptDescription" class="preview-description">{{ promptDescription }}</p>
        </div>
        <div class="preview-actions">
          <el-button 
            type="primary" 
            @click="handleRegenerate"
            :loading="isRegenerating"
          >
            <el-icon><Refresh /></el-icon>
            重新生成
          </el-button>
          <el-button type="primary" @click="copyContent">
            <el-icon><Document /></el-icon>
            复制内容
          </el-button>
          <el-button type="success" @click="showSaveDialog">
            <el-icon><Plus /></el-icon>
            提示词保存
          </el-button>
        </div>
      </div>
      
      <div class="preview-body">
        <div class="edit-hint">
          <el-icon><Edit /></el-icon>
          <span>您可以直接编辑下方的提示词内容</span>
        </div>
        <el-input
          v-model="editedContent"
          type="textarea"
          :rows="18"
          class="prompt-content-editor"
          :spellcheck="false"
          resize="none"
        />
      </div>
    </div>
    
    <!-- 添加到规则的表单对话框 -->
    <el-dialog
      v-model="saveDialogVisible"
      title="提示词保存"
      width="500px"
      append-to-body
      class="save-prompt-dialog"
      @opened="focusNameInput"
    >
      <el-form 
        :model="saveForm" 
        ref="saveFormRef" 
        :rules="saveRules" 
        label-position="top"
        @submit.prevent="handleSaveToRule"
      >
        <el-form-item label="提示词名称" prop="name">
          <el-input 
            v-model="saveForm.name" 
            placeholder="请输入提示词名称"
            ref="nameInputRef" 
            @keyup.enter="handleSaveToRule"
          />
        </el-form-item>
        <el-form-item label="提示词描述" prop="description">
          <el-input
            v-model="saveForm.description"
            type="textarea"
            :rows="2"
            placeholder="请输入提示词描述（可选）"
          />
        </el-form-item>
        <el-form-item label="提示词内容" class="content-preview">
          <pre class="content-preview-text">{{ editedContent }}</pre>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="saveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveToRule">确认添加</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Check, Plus, Refresh, Edit } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  promptName: {
    type: String,
    default: ''
  },
  promptDescription: {
    type: String,
    default: ''
  },
  promptContent: {
    type: String,
    default: ''
  },
  previewContent: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'save-to-rule', 'regenerate'])

const visible = ref(false)
const editedContent = ref('')

// 监听visible变化
watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 监听modelValue变化
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    editedContent.value = props.previewContent || props.promptContent
  }
})

// 监听内容变化，更新编辑内容
watch(() => props.previewContent, (val) => {
  if (val) {
    editedContent.value = val
  }
})

watch(() => props.promptContent, (val) => {
  if (val && !props.previewContent) {
    editedContent.value = val
  }
})

// 添加重新生成状态
const isRegenerating = ref(false)

// 处理重新生成
const handleRegenerate = async () => {
  if (isRegenerating.value) return
  
  try {
    isRegenerating.value = true
    await emit('regenerate')
  } catch (error) {
    console.error('重新生成失败:', error)
    ElMessage.error('重新生成失败，请重试')
  } finally {
    isRegenerating.value = false
  }
}

// 复制内容
const copyContent = () => {
  if (!editedContent.value) {
    ElMessage.warning('没有可复制的内容')
    return
  }
  
  window.pywebview.api.copy_to_clipboard(editedContent.value)
    .then(() => {
      ElMessage.success('复制成功')
    })
    .catch(() => {
      ElMessage.error('复制失败')
    })
}

// 保存对话框相关
const saveDialogVisible = ref(false)
const saveFormRef = ref(null)
const saveForm = ref({
  name: '',
  description: ''
})

// 表单验证规则
const saveRules = {
  name: [
    { required: true, message: '请输入提示词名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 创建引用
const nameInputRef = ref(null)

// 显示保存对话框
const showSaveDialog = () => {
  saveForm.value = {
    name: `提示词 ${new Date().toLocaleString('zh-CN', {
      month: 'numeric',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    })}`,
    description: ''
  }
  saveDialogVisible.value = true
}

// 处理保存到规则
const handleSaveToRule = async () => {
  if (!saveFormRef.value) return
  
  try {
    await saveFormRef.value.validate()
    
    emit('save-to-rule', {
      name: saveForm.value.name,
      description: saveForm.value.description,
      content: editedContent.value,
      timestamp: new Date().toISOString()
    })
    
    saveDialogVisible.value = false
    visible.value = false
    
    ElMessage.success('提示词已添加到规则')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 修改focusNameInput函数
const focusNameInput = () => {
  nextTick(() => {
    if (nameInputRef.value) {
      nameInputRef.value.focus()
    }
  })
}
</script>

<style lang="scss" scoped>
.prompt-preview-dialog {
  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: 80vh;
    max-width: 80vw;
  }
  
  :deep(.el-dialog__header) {
    padding: 16px 20px;
    margin: 0;
    border-bottom: 1px solid var(--el-border-color-light);
  }
  
  :deep(.el-dialog__body) {
    padding: 0;
    overflow: hidden;
    flex: 1;
  }
  
  :deep(.el-dialog__footer) {
    padding: 12px 20px;
    border-top: 1px solid var(--el-border-color-light);
  }
  
  .preview-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: calc(80vh - 120px); // 减去头部和底部的高度
    
    .preview-header {
      padding: 6px 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--el-border-color-light);
      flex-shrink: 0;
      
      .preview-info {
        .preview-title {
          margin: 0 0 4px 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          user-select: none;
        }
        
        .preview-description {
          margin: 0;
          font-size: 14px;
          color: var(--el-text-color-secondary);
          user-select: none;
        }
      }
      
      .preview-actions {
        display: flex;
        gap: 12px;
        
        .el-button {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 8px 16px;
          
          .el-icon {
            font-size: 16px;
          }
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
          
          &:active {
            transform: translateY(0);
          }
        }
      }
    }
    
    .preview-body {
      flex: 1;
      padding: 20px;
      overflow: hidden;
      background-color: var(--el-bg-color-page);
      display: flex;
      flex-direction: column;
      
      .edit-hint {
        margin-bottom: 12px;
        padding: 8px 12px;
        background-color: rgba(var(--el-color-primary-rgb), 0.1);
        border-radius: 6px;
        color: var(--el-color-primary);
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        user-select: none;
        
        .el-icon {
          font-size: 16px;
        }
      }
      
      .prompt-content-editor {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0; // 重要：允许flex子项收缩

        :deep(.el-textarea) {
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        :deep(.el-textarea__inner) {
          font-size: 18px;
          line-height: 1.8;
          background-color: var(--el-bg-color);
          border-radius: 8px;
          padding: 16px;
          height: 100% !important; // 确保占满容器
          resize: none;
          background: #cfe8cc;
          color: #000000;
          font-weight: 500;
          letter-spacing: -0.2px;
          opacity: 0.95;
          overflow-y: auto; // 确保垂直滚动
          box-sizing: border-box;
        }
      }
    }
  }
}

:deep(.prompt-preview-modal) {
  overflow: hidden;
}

/* 保存对话框样式 */
.save-prompt-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
    
    .el-dialog__header {
      margin: 0;
      padding: 20px;
      border-bottom: 1px solid var(--el-border-color-light);
      background: linear-gradient(135deg, var(--el-color-success-light-8), var(--el-color-success-light-9));
      
      .el-dialog__title {
        font-weight: 600;
        color: var(--el-color-success-dark-2);
      }
    }
    
    .el-dialog__body {
      padding: 20px;
    }
  }
  
  .content-preview {
    .content-preview-text {
      white-space: pre-wrap;
      
      font-size: 18px;
      line-height: 1.5;
      padding: 12px;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
      max-height: 150px;
      overflow-y: auto;
      color: var(--el-text-color-regular);
    }
  }
}

/* 暗色模式适配 */
html.dark {
  .prompt-preview-dialog {
    .preview-content {
      .preview-header {
        .preview-actions {
          .el-button {
            &:hover {
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            }
          }
        }
      }
      
      .preview-body {
        .edit-hint {
          background-color: rgba(var(--el-color-primary-rgb), 0.2);
        }
        
        .prompt-content-editor {
          :deep(.el-textarea__inner) {
            font-size: 18px;
            line-height: 1.8;
            background: #cfe8cc;
            font-weight: 550;
            color: #000000;
            opacity: 0.97;
            overflow-y: auto; // 确保暗色模式下也有滚动
            box-sizing: border-box;
          }
        }
      }
    }
  }
}

/* 添加全局用户选择控制 */
:deep(.el-dialog__header),
:deep(.el-dialog__title),
.preview-header,
.preview-actions,
.edit-hint {
  user-select: none;
}
</style> 