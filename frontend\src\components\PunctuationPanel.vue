<template>
  <div>
    <!-- 标点符号面板 -->
    <div
      v-if="visible"
      class="punctuation-panel"
      :style="panelStyle"
      @mousedown="handleMouseDown"
    >
      <!-- 面板头部 -->
      <div class="panel-header">
        <div class="panel-title">
          <el-icon><Edit /></el-icon>
          <span>标点符号</span>
        </div>
        <div class="panel-controls">
          <el-tooltip content="拖拽移动" placement="top">
            <el-icon class="drag-handle" @mousedown="initDrag"><Rank /></el-icon>
          </el-tooltip>
          <el-tooltip content="关闭面板" placement="top">
            <el-icon class="close-btn" @click="$emit('close')"><Close /></el-icon>
          </el-tooltip>
        </div>
      </div>

      <!-- 标点符号内容区域 -->
      <div class="panel-content">
        <!-- 中文常用 -->
        <div class="punctuation-category">
          <div class="category-title">中文常用</div>
          <div class="punctuation-grid">
            <button
              v-for="punct in chinesePunctuations"
              :key="punct.symbol"
              :class="['punctuation-btn', { 'pair-punctuation': punct.type === 'pair' }]"
              :title="punct.name + (punct.type === 'pair' ? ' (成对插入)' : '')"
              @click="insertPunctuation(punct)"
            >
              {{ punct.symbol }}
            </button>
          </div>
        </div>



        <!-- 功能按键 -->
        <div class="punctuation-category">
          <div class="category-title">功能按键</div>
          <div class="punctuation-grid">
            <button
              v-for="key in functionKeys"
              :key="key.symbol"
              :class="['punctuation-btn', 'function-key']"
              :title="key.name"
              @click="insertFunctionKey(key)"
            >
              {{ key.symbol }}
            </button>
          </div>
          <!-- 快捷键提示 -->
          <div class="shortcut-tip">
            <el-icon><Edit /></el-icon>
            <span>快捷键：Ctrl + / 插入默认JSON</span>
          </div>
        </div>
      </div>
    </div>

    <!-- JSON插入弹窗 -->
  <el-dialog
    v-model="jsonDialogVisible"
    title="插入JSON占位符"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    class="json-placeholder-dialog"
    append-to-body
  >
    <div class="json-dialog-content">
      <!-- 表单区域 -->
      <div class="form-section">
        <div class="form-grid">
          <div class="form-item">
            <label class="form-label">标记目的</label>
            <el-select
              v-model="jsonFormData.标记目的"
              placeholder="选择或输入目的"
              filterable
              allow-create
              class="form-select"
            >
              <el-option
                v-for="option in jsonOptions.标记目的"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
          </div>

          <div class="form-item">
            <label class="form-label">需要信息</label>
            <el-select
              v-model="jsonFormData.需要信息"
              placeholder="选择或输入信息类型"
              filterable
              allow-create
              class="form-select"
            >
              <el-option
                v-for="option in jsonOptions.需要信息"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
          </div>

          <div class="form-item">
            <label class="form-label">字数限制</label>
            <el-select
              v-model="jsonFormData.字数限制"
              placeholder="选择或输入字数"
              filterable
              allow-create
              class="form-select"
            >
              <el-option
                v-for="option in jsonOptions.字数限制"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
          </div>

          <div class="form-item">
            <label class="form-label">风格要求</label>
            <el-select
              v-model="jsonFormData.风格要求"
              placeholder="选择或输入风格"
              filterable
              allow-create
              class="form-select"
            >
              <el-option
                v-for="option in jsonOptions.风格要求"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
          </div>

          <div class="form-item">
            <label class="form-label">内容种类</label>
            <el-select
              v-model="jsonFormData.内容种类"
              placeholder="选择或输入种类"
              filterable
              allow-create
              class="form-select"
            >
              <el-option
                v-for="option in jsonOptions.内容种类"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
          </div>

          <div class="form-item">
            <label class="form-label">优先级</label>
            <el-select
              v-model="jsonFormData.优先级"
              placeholder="选择优先级"
              class="form-select"
            >
              <el-option
                v-for="option in jsonOptions.优先级"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 快速模板区域 -->
      <div class="template-section">
        <div class="template-header">
          <el-icon class="template-icon"><Document /></el-icon>
          <span class="template-title">快速模板</span>
        </div>
        <div class="template-grid">
          <el-button
            v-for="template in jsonTemplates"
            :key="template.name"
            @click="applyTemplate(template)"
            class="template-btn"
            type="primary"
            plain
          >
            <span>{{ template.name }}</span>
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeJsonDialog" class="cancel-btn">取消</el-button>
        <el-button @click="insertJsonPlaceholder" type="primary" class="confirm-btn">
          <el-icon><Plus /></el-icon>
          <span>插入</span>
        </el-button>
      </div>
    </template>
  </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { Edit, Rank, Close, Document, Plus } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editor: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'insert'])

// 面板位置状态
const panelPosition = ref({ x: 100, y: 100 })
const isDragging = ref(false)
const dragOffset = ref({ x: 0, y: 0 })

// JSON弹窗状态
const jsonDialogVisible = ref(false)
const jsonFormData = ref({
  标记目的: '',
  需要信息: '',
  字数限制: '',
  风格要求: '',
  内容种类: '',
  优先级: '中'
})

// 标点符号数据
const chinesePunctuations = ref([
  // 基础标点
 
  
  

 
  
  // 中文引号和括号
  
  { symbol: '\'\’', name: '中文单引号', type: 'pair', left: '\‘', right: '\’' },
  { symbol: '（）', name: '圆括号', type: 'pair', left: '（', right: '）' },
  { symbol: '【】', name: '方括号', type: 'pair', left: '【', right: '】' },
  { symbol: '《》', name: '书名号', type: 'pair', left: '《', right: '》' },
  { symbol: '〈〉', name: '单书名号', type: 'pair', left: '〈', right: '〉' },
  { symbol: '——', name: '破折号', type: 'single' },
  { symbol: '·', name: '间隔号', type: 'single' },
   { symbol: '、', name: '顿号', type: 'single' },

  // 方便常用区域
  { symbol: '…', name: '省略号', type: 'single' },
  { symbol: '；', name: '分号', type: 'single' },
    { symbol: '，', name: '逗号', type: 'single' },
  { symbol: '。', name: '句号', type: 'single' },
  { symbol: '？', name: '问号', type: 'single' },
  { symbol: '！', name: '感叹号', type: 'single' },
  { symbol: '：', name: '冒号', type: 'single' },
  { symbol: '“”', name: '中文双引号', type: 'pair', left: '“', right: '”' }
])



const functionKeys = ref([
  { symbol: '删除', name: '删除键 (Backspace)', type: 'function', action: 'backspace' },
  { symbol: '回车', name: '回车键 (Enter)', type: 'function', action: 'enter' },
  { symbol: 'JSON', name: '插入JSON占位符', type: 'function', action: 'json_placeholder' },
  { symbol: '撤销', name: '撤销操作 (Undo)', type: 'function', action: 'undo' }
])

// JSON表单预设选项
const jsonOptions = ref({
  标记目的: ['描述场景', '刻画人物', '推进情节', '表达情感', '营造氛围', '补充细节'],
  需要信息: ['外貌描写', '心理活动', '对话内容', '动作描述', '环境描写', '背景信息'],
  字数限制: ['50字以内', '100-200字', '200-500字', '500字以上', '不限制'],
  风格要求: ['古风雅致', '现代简洁', '幽默风趣', '严肃正式', '诗意浪漫', '紧张刺激'],
  内容种类: ['对话', '景物描写', '人物描写', '心理描写', '动作描写', '情节发展'],
  优先级: ['高', '中', '低']
})

// 快速模板
const jsonTemplates = ref([
  {
    name: '对话模板',
    data: {
      标记目的: '表达情感',
      需要信息: '对话内容',
      字数限制: '100-200字',
      风格要求: '符合人物性格',
      内容种类: '对话',
      优先级: '高'
    }
  },
  {
    name: '描写模板',
    data: {
      标记目的: '营造氛围',
      需要信息: '环境描写',
      字数限制: '200-500字',
      风格要求: '诗意浪漫',
      内容种类: '景物描写',
      优先级: '中'
    }
  },
  {
    name: '情节模板',
    data: {
      标记目的: '推进情节',
      需要信息: '背景信息',
      字数限制: '500字以上',
      风格要求: '紧张刺激',
      内容种类: '情节发展',
      优先级: '高'
    }
  }
])

// 计算面板样式
const panelStyle = computed(() => ({
  left: `${panelPosition.value.x}px`,
  top: `${panelPosition.value.y}px`
}))

// 插入标点符号
const insertPunctuation = (punctuation) => {
  if (!props.editor || !props.editor.commands) return

  if (punctuation.type === 'pair') {
    // 成对标点符号：插入一对并将光标定位在中间
    const content = punctuation.left + punctuation.right
    props.editor.commands.insertContent(content)

    // 将光标向左移动一个字符，定位在两个符号中间
    const { state, dispatch } = props.editor.view
    const { tr } = state
    const newPos = state.selection.head - 1
    const newSelection = state.selection.constructor.near(state.doc.resolve(newPos))
    dispatch(tr.setSelection(newSelection))

    emit('insert', content)
  } else {
    // 单个标点符号：直接插入
    props.editor.commands.insertContent(punctuation.symbol)
    emit('insert', punctuation.symbol)
  }

  props.editor.commands.focus()
}

// 模拟键盘事件函数
const simulateKeyboardEvent = (keyCode) => {
  if (!props.editor || !props.editor.view) return

  const view = props.editor.view

  // 创建键盘事件，使用更完整的事件属性
  const event = new KeyboardEvent('keydown', {
    key: keyCode,
    code: keyCode === 'Backspace' ? 'Backspace' : 'Enter',
    keyCode: getKeyCodeNumber(keyCode),
    which: getKeyCodeNumber(keyCode),
    bubbles: true,
    cancelable: true,
    composed: true
  })

  // 直接通过编辑器视图处理键盘事件
  // 这样能确保编辑器的所有插件和处理器都能正确响应
  view.dom.dispatchEvent(event)

  // 如果事件没有被处理，尝试手动触发编辑器的键盘处理
  if (!event.defaultPrevented) {
    // 对于 ProseMirror 编辑器，直接调用 handleKeyDown
    if (view.someProp && typeof view.someProp === 'function') {
      view.someProp('handleKeyDown', (f) => f(view, event))
    }
  }
}

// 获取键码数字
const getKeyCodeNumber = (keyCode) => {
  const keyCodes = {
    'Backspace': 8,
    'Enter': 13
  }
  return keyCodes[keyCode] || 0
}

// 插入功能按键
const insertFunctionKey = (key) => {
  if (!props.editor || !props.editor.commands) return

  switch (key.action) {
    case 'backspace':
      // 使用编辑器原生的删除命令，完全模拟键盘行为
      const { from, empty } = props.editor.state.selection

      if (!empty) {
        // 如果有选中内容，删除选中内容
        props.editor.commands.deleteSelection()
      } else {
        // 使用 joinBackward 命令，这个命令会智能处理：
        // 1. 在段落开头时：合并到上一个段落
        // 2. 在段落中间时：删除前一个字符
        // 3. 在列表项开头时：合并到上一个列表项或退出列表
        if (!props.editor.commands.joinBackward()) {
          // 如果 joinBackward 失败（比如在文档开头），尝试删除前一个字符
          if (from > 0) {
            props.editor.commands.deleteRange({ from: from - 1, to: from })
          }
        }
      }
      props.editor.commands.focus()
      emit('insert', key.symbol)
      break
    case 'enter':
      // 使用编辑器原生的回车命令
      props.editor.commands.splitBlock()
      props.editor.commands.focus()
      emit('insert', key.symbol)
      break
    case 'json_placeholder':
      // 打开JSON插入弹窗
      openJsonDialog()
      break
    case 'undo':
      // 执行撤销操作
      if (props.editor && props.editor.commands && props.editor.commands.undo) {
        props.editor.commands.undo()
        props.editor.commands.focus()
        emit('insert', key.symbol)
      }
      break
  }
}

// 拖拽功能
const initDrag = (event) => {
  event.preventDefault()
  isDragging.value = true
  
  const rect = event.currentTarget.closest('.punctuation-panel').getBoundingClientRect()
  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }
  
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
}

const handleDrag = (event) => {
  if (!isDragging.value) return
  
  panelPosition.value = {
    x: event.clientX - dragOffset.value.x,
    y: event.clientY - dragOffset.value.y
  }
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// JSON弹窗相关方法
const openJsonDialog = () => {
  // 重置表单数据
  jsonFormData.value = {
    标记目的: '',
    需要信息: '',
    字数限制: '',
    风格要求: '',
    内容种类: '',
    优先级: '中'
  }
  jsonDialogVisible.value = true
}

const closeJsonDialog = () => {
  jsonDialogVisible.value = false
}

const applyTemplate = (template) => {
  jsonFormData.value = { ...template.data }
}

const insertJsonPlaceholder = () => {
  if (!props.editor || !props.editor.commands) return

  // 构建JSON对象，过滤空值
  const jsonData = {}
  Object.keys(jsonFormData.value).forEach(key => {
    if (jsonFormData.value[key] && jsonFormData.value[key].trim()) {
      jsonData[key] = jsonFormData.value[key].trim()
    }
  })

  // 添加固定的类型字段
  jsonData.类型 = '占位符'

  // 格式化为单行JSON字符串，逗号后添加空格
  const jsonString = JSON.stringify(jsonData).replace(/,/g, ',    ')

  // 插入到编辑器（作为单行段落）
  props.editor.commands.insertContent(jsonString)
  props.editor.commands.focus()

  // 关闭弹窗
  closeJsonDialog()

  emit('insert', jsonString)
}

// 插入默认JSON占位符（用于快捷键调用）
const insertDefaultJsonPlaceholder = () => {
  if (!props.editor || !props.editor.commands) return

  // 创建默认的JSON占位符
  const defaultJsonData = {
    标记目的: '描述场景',
    需要信息: '人物、情况、状态......',
    字数限制: '80-150字',
    风格要求: '正常',
    内容种类: ' ',
    优先级: '60/100',
    类型: '占位符'
  }

  // 格式化为单行JSON字符串，逗号后添加空格
  const jsonString = JSON.stringify(defaultJsonData).replace(/,/g, ', ')

  // 插入到编辑器（作为单行段落）
  props.editor.commands.insertContent(jsonString)
  props.editor.commands.focus()

  emit('insert', jsonString)
}

// 防止面板被选中
const handleMouseDown = (event) => {
  event.preventDefault()
}

// 处理键盘快捷键
const handleKeydown = (event) => {
  // 只在面板可见时处理快捷键
  if (!props.visible) return

  // Ctrl + / 快捷键插入默认JSON
  if (event.ctrlKey && event.key === '/') {
    event.preventDefault()
    insertDefaultJsonPlaceholder()
  }
}

// 监听面板可见性，动态添加/移除键盘事件监听
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    document.addEventListener('keydown', handleKeydown)
  } else {
    document.removeEventListener('keydown', handleKeydown)
  }
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('keydown', handleKeydown)
})

// 初始化面板位置
onMounted(() => {
  // 设置初始位置在屏幕中央偏右
  panelPosition.value = {
    x: window.innerWidth - 320,
    y: 150
  }

  // 如果面板初始就是可见的，添加键盘事件监听
  if (props.visible) {
    document.addEventListener('keydown', handleKeydown)
  }
})
</script>

<style lang="scss" scoped>
.punctuation-panel {
  position: fixed;
  width: 300px;
  max-height: 520px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
  z-index: 9999;
  user-select: none;
  overflow: hidden;
  animation: panelFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: contrast(1.05) brightness(0.98);

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: linear-gradient(135deg,
      var(--el-fill-color-light) 0%,
      var(--el-fill-color) 100%);
    border-bottom: 1px solid var(--el-border-color-lighter);
    cursor: move;
    backdrop-filter: blur(4px);

    .panel-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 15px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      letter-spacing: 0.5px;

      .el-icon {
        font-size: 18px;
        color: var(--el-color-primary);
      }
    }

    .panel-controls {
      display: flex;
      align-items: center;
      gap: 8px;

      .el-icon {
        font-size: 16px;
        color: var(--el-text-color-regular);
        cursor: pointer;
        padding: 2px;
        border-radius: 4px;
        transition: all 0.2s;

        &:hover {
          color: var(--el-color-primary);
          background: var(--el-fill-color);
        }

        &.drag-handle {
          cursor: move;
        }
      }
    }
  }

  .panel-content {
    padding: 16px;
    max-height: 440px;
    overflow-y: auto;
    background: var(--el-bg-color-page);

    .punctuation-category {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .category-title {
        font-size: 13px;
        color: var(--el-text-color-regular);
        margin-bottom: 12px;
        font-weight: 600;
        letter-spacing: 0.3px;
        text-transform: uppercase;
        opacity: 0.8;
      }

      .punctuation-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;

        // 功能按键区域的特殊布局
        &:has(.function-key) {
          grid-template-columns: repeat(4, 1fr);
          gap: 8px;
          justify-items: center;
        }

        .punctuation-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 46px;
          height: 34px;
          background: linear-gradient(135deg,
            var(--el-fill-color-lighter) 0%,
            var(--el-fill-color-light) 100%);
          border: 1px solid var(--el-border-color-lighter);
          border-radius: 8px;
          font-size: 20px;
          font-weight: 600;
          color: #000000;
          cursor: pointer;
          transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          // 成对标点符号的特殊样式
          &.pair-punctuation {
            background: linear-gradient(135deg,
              rgba(76, 175, 80, 0.1) 0%,
              rgba(76, 175, 80, 0.05) 100%);
            border-color: rgba(76, 175, 80, 0.3);
          }

          // 功能按键的特殊样式
          &.function-key {
            font-size: 12px;
            font-weight: 500;
            background: linear-gradient(135deg,
              rgba(255, 193, 7, 0.1) 0%,
              rgba(255, 193, 7, 0.05) 100%);
            border-color: rgba(255, 193, 7, 0.3);
            color: rgba(255, 193, 7, 0.9);

            // 撤销按钮的特殊样式
            &[title*="撤销"] {
              background: linear-gradient(135deg,
                rgba(156, 39, 176, 0.1) 0%,
                rgba(156, 39, 176, 0.05) 100%);
              border-color: rgba(156, 39, 176, 0.3);
              color: rgba(156, 39, 176, 0.9);
              position: relative;

              &::before {
                content: '↶';
                font-size: 16px;
                font-weight: bold;
              }
            }
          }

          &:hover {
            background: #e6f3ff;
            border-color: #b3d9ff;
            color: #000000;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 128, 255, 0.25);

            &.pair-punctuation {
              background: #d9f2d9;
              border-color: #99d699;
              color: #000000;
            }

            &.function-key {
              background: rgba(255, 193, 7, 0.2);
              border-color: rgba(255, 193, 7, 0.5);
              color: rgba(255, 193, 7, 1);
              box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);

              &[title*="撤销"] {
                background: rgba(156, 39, 176, 0.2);
                border-color: rgba(156, 39, 176, 0.5);
                color: rgba(156, 39, 176, 1);
                box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
              }
            }
          }

          &:active {
            transform: translateY(-1px);
            background: #cce6ff;
            color: #000000;
            box-shadow: 0 2px 6px rgba(64, 128, 255, 0.3);

            &.pair-punctuation {
              background: #ccebcc;
              color: #000000;
            }

            &.function-key {
              background: rgba(255, 193, 7, 0.25);
              color: rgba(255, 193, 7, 1);
              box-shadow: 0 2px 6px rgba(255, 193, 7, 0.4);

              &[title*="撤销"] {
                background: rgba(156, 39, 176, 0.25);
                color: rgba(156, 39, 176, 1);
                box-shadow: 0 2px 6px rgba(156, 39, 176, 0.4);
              }
            }
          }


        }
      }

      // 快捷键提示样式
      .shortcut-tip {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-top: 12px;
        padding: 8px 12px;
        background: linear-gradient(135deg,
          var(--el-color-info-light-9) 0%,
          var(--el-color-info-light-8) 100%);
        border: 1px solid var(--el-color-info-light-7);
        border-radius: 8px;
        font-size: 12px;
        color: var(--el-color-info);
        text-align: center;
        opacity: 0.8;
        transition: all 0.3s ease;

        .el-icon {
          font-size: 14px;
          color: var(--el-color-info);
        }

        span {
          font-weight: 500;
          letter-spacing: 0.3px;
        }

        &:hover {
          opacity: 1;
          background: linear-gradient(135deg,
            var(--el-color-info-light-8) 0%,
            var(--el-color-info-light-7) 100%);
          border-color: var(--el-color-info-light-6);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
        }
      }
    }
  }

  // 滚动条样式
  :deep(.panel-content::-webkit-scrollbar) {
    width: 8px;
  }

  :deep(.panel-content::-webkit-scrollbar-track) {
    background: var(--el-fill-color-lighter);
    border-radius: 4px;
    margin: 4px 0;
  }

  :deep(.panel-content::-webkit-scrollbar-thumb) {
    background: var(--el-border-color-light);
    border-radius: 4px;
    border: 1px solid var(--el-border-color-lighter);
    transition: all 0.2s;

    &:hover {
      background: var(--el-border-color);
    }
  }
}

@keyframes panelFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 暗色主题样式 */
html.dark .punctuation-panel {
  background: rgba(26, 26, 26, 0.95);
  border-color: #333333;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.2);
  filter: contrast(1.1) brightness(0.95) hue-rotate(-5deg);

  .panel-header {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.08) 0%,
      rgba(255, 255, 255, 0.04) 100%);
    border-bottom-color: #2a2a2a;
  }

  .panel-content {
    background: rgba(26, 26, 26, 0.6);

    .category-title {
      color: #e0e0e0;
      opacity: 0.9;
    }

    .punctuation-btn {
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.04) 100%) !important;
      border-color: #333333 !important;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
      color: #ffffff !important;

      &.pair-punctuation {
        background: linear-gradient(135deg,
          rgba(255, 255, 255, 0.12) 0%,
          rgba(255, 255, 255, 0.08) 100%);
        border-color: rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.9);

        &::after {
          background: rgba(64, 158, 255, 0.8);
          color: rgba(255, 255, 255, 0.95);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
        }
      }

      &:hover {
        background: linear-gradient(135deg,
          rgba(64, 128, 255, 0.2) 0%,
          rgba(64, 128, 255, 0.15) 100%) !important;
        box-shadow: 0 4px 12px rgba(64, 128, 255, 0.3) !important;
        color: #ffffff !important;

        &.pair-punctuation {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.18) 0%,
            rgba(255, 255, 255, 0.14) 100%);
          border-color: rgba(64, 158, 255, 0.4);
          color: rgba(255, 255, 255, 0.95);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }
      }

      &:active {
        background: linear-gradient(135deg,
          rgba(64, 128, 255, 0.25) 0%,
          rgba(64, 128, 255, 0.2) 100%) !important;
        color: #ffffff !important;

        &.pair-punctuation {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.15) 0%,
            rgba(255, 255, 255, 0.12) 100%);
          border-color: rgba(64, 158, 255, 0.3);
          box-shadow: 0 2px 6px rgba(64, 158, 255, 0.25);
        }
      }

      &.function-key {
        background: linear-gradient(135deg,
          rgba(255, 193, 7, 0.15) 0%,
          rgba(255, 193, 7, 0.1) 100%);
        border-color: rgba(255, 193, 7, 0.3);
        color: rgba(255, 193, 7, 0.9);

        &::after {
          background: rgba(255, 193, 7, 0.8);
          color: rgba(0, 0, 0, 0.8);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
        }

        // 撤销按钮的特殊样式
        &[title*="撤销"] {
          background: linear-gradient(135deg,
            rgba(156, 39, 176, 0.15) 0%,
            rgba(156, 39, 176, 0.1) 100%);
          border-color: rgba(156, 39, 176, 0.3);
          color: rgba(156, 39, 176, 0.9);

          &::before {
            content: '↶';
            font-size: 16px;
            font-weight: bold;
          }
        }

        &:hover {
          background: linear-gradient(135deg,
            rgba(255, 193, 7, 0.2) 0%,
            rgba(255, 193, 7, 0.15) 100%);
          box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);

          &[title*="撤销"] {
            background: linear-gradient(135deg,
              rgba(156, 39, 176, 0.2) 0%,
              rgba(156, 39, 176, 0.15) 100%);
            box-shadow: 0 4px 12px rgba(156, 39, 176, 0.2);
            color: rgba(156, 39, 176, 1);
          }
        }

        &:active {
          background: linear-gradient(135deg,
            rgba(255, 193, 7, 0.25) 0%,
            rgba(255, 193, 7, 0.2) 100%);
          box-shadow: 0 2px 6px rgba(255, 193, 7, 0.25);

          &[title*="撤销"] {
            background: linear-gradient(135deg,
              rgba(156, 39, 176, 0.25) 0%,
              rgba(156, 39, 176, 0.2) 100%);
            box-shadow: 0 2px 6px rgba(156, 39, 176, 0.25);
            color: rgba(156, 39, 176, 1);
          }
        }
      }
    }

    // 暗色主题下的快捷键提示样式
    .shortcut-tip {
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.04) 100%);
      border-color: rgba(255, 255, 255, 0.15);
      color: rgba(255, 255, 255, 0.7);

      .el-icon {
        color: rgba(255, 255, 255, 0.6);
      }

      &:hover {
        background: linear-gradient(135deg,
          rgba(255, 255, 255, 0.12) 0%,
          rgba(255, 255, 255, 0.08) 100%);
        border-color: rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.9);
        box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);

        .el-icon {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  :deep(.panel-content::-webkit-scrollbar-thumb) {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.05);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

/* JSON弹窗样式 */
.json-placeholder-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(8px);
  }

  :deep(.el-dialog__header) {
    padding: 24px 28px 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: linear-gradient(135deg, var(--el-fill-color-lighter) 0%, var(--el-fill-color-light) 100%);

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      letter-spacing: 0.5px;
    }
  }

  :deep(.el-dialog__body) {
    padding: 28px;
    height: 380px;
    overflow: hidden;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 28px 24px;
    border-top: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-lighter);
  }

  .json-dialog-content {
    display: flex;
    gap: 32px;
    height: 100%;
  }

  .form-section {
    flex: 1;

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px 24px;
      height: 100%;
      align-content: start;
    }

    .form-item {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .form-label {
        font-size: 14px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        letter-spacing: 0.3px;
        margin-bottom: 4px;
      }

      .form-select {
        :deep(.el-input__wrapper) {
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }

          &.is-focus {
            box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
          }
        }
      }
    }
  }

  .template-section {
    width: 160px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;

    .template-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 20px;
      padding: 12px 16px;
      background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-primary-light-8) 100%);
      border-radius: 12px;
      border: 1px solid var(--el-color-primary-light-7);

      .template-icon {
        font-size: 16px;
        color: var(--el-color-primary);
      }

      .template-title {
        font-size: 14px;
        font-weight: 600;
        color: var(--el-color-primary);
        letter-spacing: 0.3px;
      }
    }

    .template-grid {
      display: flex;
      flex-direction: column;
      gap: 12px;
      flex: 1;

      .template-btn {
        width: 100%;
        height: 44px;
        font-size: 13px;
        font-weight: 500;
        border-radius: 10px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        margin: 0 !important; /* 强制移除默认margin */

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(64, 158, 255, 0.2);
        }

        span {
          letter-spacing: 0.3px;
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 16px;

    .cancel-btn {
      padding: 10px 24px;
      font-size: 14px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .confirm-btn {
      padding: 10px 24px;
      font-size: 14px;
      font-weight: 600;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
      }

      .el-icon {
        margin-right: 6px;
      }

      span {
        letter-spacing: 0.3px;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .json-placeholder-dialog {
    :deep(.el-dialog) {
      width: 95vw !important;
      margin: 3vh auto;
    }

    :deep(.el-dialog__body) {
      padding: 20px;
      height: auto;
      max-height: 70vh;
      overflow-y: auto;
    }

    .json-dialog-content {
      flex-direction: column;
      gap: 24px;
      height: auto;
    }

    .form-section {
      .form-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }

    .template-section {
      width: 100%;

      .template-header {
        justify-content: center;
        margin-bottom: 16px;
      }

      .template-grid {
        flex-direction: row;
        gap: 12px;

        .template-btn {
          flex: 1;
          height: 40px;
          font-size: 12px;
          margin: 0 !important; /* 强制移除默认margin */
        }
      }
    }

    .dialog-footer {
      gap: 12px;

      .cancel-btn,
      .confirm-btn {
        padding: 8px 20px;
        font-size: 13px;
      }
    }
  }
}

/* 暗色主题适配 */
html.dark .json-placeholder-dialog {
  :deep(.el-dialog) {
    background: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
    border-bottom-color: var(--el-border-color);
  }

  :deep(.el-dialog__footer) {
    background: rgba(255, 255, 255, 0.02);
    border-top-color: var(--el-border-color);
  }

  .form-section {
    .form-item {
      .form-label {
        color: var(--el-text-color-primary);
      }

      .form-select {
        :deep(.el-input__wrapper) {
          background: rgba(255, 255, 255, 0.05);
          border-color: var(--el-border-color);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

          &:hover {
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
          }

          &.is-focus {
            background: rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
          }
        }
      }
    }
  }

  .template-section {
    .template-header {
      background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(64, 158, 255, 0.1) 100%);
      border-color: rgba(64, 158, 255, 0.3);

      .template-icon,
      .template-title {
        color: rgba(64, 158, 255, 0.9);
      }
    }

    .template-grid {
      .template-btn {
        background: rgba(255, 255, 255, 0.05);
        border-color: var(--el-border-color);
        color: var(--el-text-color-primary);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        margin: 0 !important; /* 强制移除默认margin */

        &:hover {
          background: rgba(64, 158, 255, 0.15);
          border-color: rgba(64, 158, 255, 0.4);
          color: rgba(64, 158, 255, 0.9);
          box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }

  .dialog-footer {
    .cancel-btn {
      background: rgba(255, 255, 255, 0.05);
      border-color: var(--el-border-color);
      color: var(--el-text-color-primary);

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }

    .confirm-btn {
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);

      &:hover {
        box-shadow: 0 6px 16px rgba(64, 158, 255, 0.5);
      }
    }
  }
}
</style>
