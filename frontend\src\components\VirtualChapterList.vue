<template>
  <div class="virtual-chapter-list">
    <!-- 搜索结果 -->
    <div v-if="showSearchResults" class="search-results">
      <VirtualScrollList
        :items="searchResults"
        :item-height="60"
        :container-height="containerHeight"
        :key-field="'id'"
        :loading="searchLoading"
        @item-click="handleSearchResultClick"
      >
        <template #default="{ item }">
          <div class="search-result-item">
            <div class="volume-info">
              <el-icon><FolderOpened /></el-icon>
              <span class="volume-title">{{ item.volume.title }}</span>
            </div>
            <div class="chapter-info">
              <el-icon><Document /></el-icon>
              <span class="chapter-title">{{ item.chapter.title }}</span>
            </div>
          </div>
        </template>
        <template #empty>
          <el-empty description="未找到匹配的章节" />
        </template>
      </VirtualScrollList>
    </div>

    <!-- 卷和章节列表 -->
    <div v-else class="volumes-list">
      <VirtualScrollList
        :items="flattenedItems"
        :item-height="ITEM_HEIGHT"
        :container-height="containerHeight"
        :key-field="'key'"
        :class-field="getItemClass"
        :loading="loading"
        :infinite-scroll="true"
        @item-click="handleItemClick"
        @load-more="handleLoadMore"
      >
        <template #default="{ item }">
          <!-- 卷标题 -->
          <div v-if="item.type === 'volume'" class="tree-node volume-node">
            <div class="node-content">
              <!-- 展开/收起图标 -->
              <div class="expand-icon">
                <el-icon :class="{ 'expanded': isVolumeExpanded(item.data.id) }">
                  <ArrowRight />
                </el-icon>
              </div>

              <!-- 文件夹图标 -->
              <div class="node-icon">
                <el-icon><FolderOpened /></el-icon>
              </div>

              <!-- 标题 -->
              <div class="node-label">{{ item.data.title }}</div>

              <!-- 章节数量徽章 -->
              <div class="node-badge">{{ item.data.chapter_count || 0 }}</div>

              <!-- 操作按钮 -->
              <div class="node-actions" @click.stop>
                <el-dropdown
                  trigger="click"
                  @command="handleVolumeCommand($event, item.data)"
                  placement="bottom-end"
                >
                  <div class="action-button">
                    <el-icon><More /></el-icon>
                  </div>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="rename">
                        <el-icon><Edit /></el-icon>重命名
                      </el-dropdown-item>
                      <el-dropdown-item command="add-chapter">
                        <el-icon><Plus /></el-icon>新建章节
                      </el-dropdown-item>
                      <el-dropdown-item command="delete" class="danger">
                        <el-icon><Delete /></el-icon>删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>

          <!-- 章节项 -->
          <div v-else-if="item.type === 'chapter'" class="tree-node chapter-node" :class="{ 'active': item.data.id === activeChapterId }">
            <div class="node-content">
              <!-- 缩进占位 -->
              <div class="indent-spacer"></div>

              <!-- 文档图标 -->
              <div class="node-icon">
                <el-icon><Document /></el-icon>
              </div>

              <!-- 标题 -->
              <div class="node-label">{{ item.data.title || '未命名章节' }}</div>



              <!-- 操作按钮 -->
              <div class="node-actions" @click.stop>
                <el-dropdown
                  trigger="click"
                  @command="handleChapterCommand($event, item.volumeId, item.data)"
                  placement="bottom-end"
                >
                  <div class="action-button">
                    <el-icon><More /></el-icon>
                  </div>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="rename">
                        <el-icon><Edit /></el-icon>重命名
                      </el-dropdown-item>
                      <el-dropdown-item command="duplicate">
                        <el-icon><CopyDocument /></el-icon>复制
                      </el-dropdown-item>
                      <el-dropdown-item command="move">
                        <el-icon><Rank /></el-icon>移动
                      </el-dropdown-item>
                      <el-dropdown-item command="delete" class="danger">
                        <el-icon><Delete /></el-icon>删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>

          <!-- 加载占位符 -->
          <div v-else-if="item.type === 'loading'" class="loading-item">
            <el-skeleton :rows="1" animated />
          </div>
        </template>
        <template #empty>
          <div class="empty-state">
            <el-icon><FolderOpened /></el-icon>
            <span>暂无卷册</span>
            <el-button type="primary" link size="small" @click="$emit('create-volume')">
              新建卷
            </el-button>
          </div>
        </template>
      </VirtualScrollList>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import VirtualScrollList from './VirtualScrollList.vue'
import { 
  FolderOpened, 
  Document, 
  ArrowRight, 
  More, 
  Edit, 
  Plus, 
  Delete, 
  CopyDocument, 
  Rank 
} from '@element-plus/icons-vue'

const props = defineProps({
  // 卷列表数据
  volumes: {
    type: Array,
    default: () => []
  },
  // 展开的卷ID集合
  expandedVolumeIds: {
    type: Set,
    default: () => new Set()
  },
  // 当前活跃的章节ID
  activeChapterId: {
    type: String,
    default: ''
  },
  // 搜索结果
  searchResults: {
    type: Array,
    default: () => []
  },
  // 是否显示搜索结果
  showSearchResults: {
    type: Boolean,
    default: false
  },
  // 搜索加载状态
  searchLoading: {
    type: Boolean,
    default: false
  },
  // 容器高度
  containerHeight: {
    type: Number,
    default: 600
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'chapter-click',
  'volume-command',
  'chapter-command',
  'toggle-volume',
  'create-volume',
  'load-more'
])

// 将嵌套的卷和章节数据扁平化为虚拟滚动可用的格式
const flattenedItems = computed(() => {
  const items = []
  
  props.volumes.forEach(volume => {
    // 添加卷项
    items.push({
      key: `volume-${volume.id}`,
      type: 'volume',
      data: volume
    })
    
    // 如果卷是展开的，添加其章节
    if (props.expandedVolumeIds.has(volume.id)) {
      if (volume.chapters && volume.chapters.length > 0) {
        volume.chapters.forEach(chapter => {
          items.push({
            key: `chapter-${chapter.id}`,
            type: 'chapter',
            data: chapter,
            volumeId: volume.id
          })
        })
      }
    }
  })
  
  return items
})

// 使用统一的项目高度（舒适易读布局）
const ITEM_HEIGHT = 36

// 获取项目CSS类
const getItemClass = (item) => {
  const classes = [item.type]
  
  if (item.type === 'chapter' && item.data.id === props.activeChapterId) {
    classes.push('active')
  }
  
  return classes.join(' ')
}



// 检查卷是否展开
const isVolumeExpanded = (volumeId) => {
  return props.expandedVolumeIds.has(volumeId)
}

// 处理项目点击事件
const handleItemClick = (item, event) => {
  // 检查是否点击了操作按钮区域
  if (event?.target?.closest('.node-actions')) {
    return // 如果点击的是操作按钮，不处理
  }

  if (item.type === 'volume') {
    toggleVolume(item.data.id)
  } else if (item.type === 'chapter') {
    emit('chapter-click', item.volumeId, item.data.id)
  }
}

// 切换卷展开状态
const toggleVolume = (volumeId) => {
  emit('toggle-volume', volumeId)
}

// 处理卷命令
const handleVolumeCommand = (command, volume) => {
  emit('volume-command', command, volume)
}

// 处理章节命令
const handleChapterCommand = (command, volumeId, chapter) => {
  emit('chapter-command', command, volumeId, chapter)
}

// 处理搜索结果点击
const handleSearchResultClick = (item) => {
  emit('chapter-click', item.volume.id, item.chapter.id)
}

// 处理加载更多
const handleLoadMore = () => {
  emit('load-more')
}
</script>

<style lang="scss" scoped>
/* 现代简洁的目录树样式 */
.virtual-chapter-list {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  background: var(--el-bg-color);
}

/* 基础树节点样式 - 整行可点击 */
.tree-node {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.15s ease;

  &:hover {
    background-color: var(--el-fill-color-light);

    .node-actions {
      opacity: 1;
    }
  }

  .node-content {
    display: flex;
    align-items: center;
    height: 36px;
    padding: 0 8px;
    gap: 8px;
  }
}

/* 卷节点样式 - 一行式简洁布局 */
.volume-node {
  .node-content {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .expand-icon {
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .el-icon {
      font-size: 14px;
      color: var(--el-text-color-secondary);
      transition: transform 0.15s ease;

      &.expanded {
        transform: rotate(90deg);
      }
    }
  }

  .node-icon {
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .el-icon {
      font-size: 16px;
      color: var(--el-color-warning);
    }
  }

  .node-label {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
  }

  .node-badge {
    padding: 2px 8px;
    font-size: 11px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    background-color: var(--el-fill-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 12px;
    min-width: 20px;
    text-align: center;
    line-height: 1.3;
    flex-shrink: 0;
  }

  .node-actions {
    opacity: 0;
    transition: opacity 0.15s ease;
    flex-shrink: 0;
  }
}

/* 章节节点样式 - 一行式简洁布局 */
.chapter-node {
  .node-content {
    font-weight: 400;
    color: var(--el-text-color-regular);
  }

  .indent-spacer {
    width: 24px;
    flex-shrink: 0;
  }

  .node-icon {
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .el-icon {
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }

  .node-label {
    flex: 1;
    font-size: 13px;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
  }

  .node-badge {
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 500;
    color: var(--el-text-color-regular);
    background-color: var(--el-fill-color-light);
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 10px;
    line-height: 1.2;
    flex-shrink: 0;
    min-width: 24px;
    text-align: center;
  }

  .node-actions {
    opacity: 0;
    transition: opacity 0.15s ease;
    flex-shrink: 0;
  }

  /* 活跃状态 - 简洁高亮 */
  &.active {
    .node-content {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }

    .node-icon .el-icon {
      color: var(--el-color-primary);
    }

    .node-label {
      color: var(--el-color-primary);
      font-weight: 500;
    }

    .node-badge {
      background-color: var(--el-color-primary-light-7);
      border-color: var(--el-color-primary-light-5);
      color: var(--el-color-primary);
      font-weight: 500;
    }
  }
}

/* 操作按钮样式 - 融入行中的自然设计 */
.action-button {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.15s ease;

  &:hover {
    background-color: var(--el-fill-color);
  }

  .el-icon {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

/* 搜索结果样式 */
.search-result-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);

  .volume-info {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: var(--el-text-color-secondary);

    .el-icon {
      font-size: 12px;
    }

    .volume-title {
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .chapter-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-icon {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }

    .chapter-title {
      flex: 1;
      font-weight: 500;
      font-size: 13px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      min-width: 0;
    }

    .chapter-meta {
      flex-shrink: 0;
      font-size: 11px;
      color: var(--el-text-color-secondary);
      background: var(--el-fill-color-lighter);
      padding: 1px 4px;
      border-radius: 8px;
    }
  }
}

/* 加载状态 */
.loading-item {
  padding: 8px 12px;
  height: 32px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 60px 20px;
  color: var(--el-text-color-secondary);
  text-align: center;

  .el-icon {
    font-size: 48px;
    color: var(--el-text-color-placeholder);
  }

  span {
    font-size: 14px;
    font-weight: 500;
  }

  .el-button {
    margin-top: 8px;
  }
}

/* 下拉菜单危险项样式 */
:deep(.el-dropdown-menu__item.danger) {
  color: var(--el-color-danger);

  &:hover {
    background-color: var(--el-color-danger-light-9);
    color: var(--el-color-danger);
  }
}
</style>
