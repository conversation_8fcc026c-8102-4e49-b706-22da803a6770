/**
 * 格式化工具函数
 */

/**
 * 格式化日期时间
 * @param {number|Date} timestamp - 时间戳或日期对象
 * @param {string} format - 格式化模式
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatDateTime(timestamp, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!timestamp) return ''
  
  const date = new Date(timestamp)
  if (isNaN(date.getTime())) return ''
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化持续时间
 * @param {number} duration - 持续时间（毫秒）
 * @returns {string} 格式化后的持续时间字符串
 */
export function formatDuration(duration) {
  if (!duration || duration < 0) return '0秒'
  
  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    return `${days}天${hours % 24}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小字符串
 */
export function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化相对时间
 * @param {number|Date} timestamp - 时间戳或日期对象
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(timestamp) {
  if (!timestamp) return ''
  
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else if (diff < 604800000) { // 7天内
    return `${Math.floor(diff / 86400000)}天前`
  } else {
    return formatDateTime(timestamp, 'MM-DD')
  }
}

/**
 * 格式化数字
 * @param {number} num - 数字
 * @param {number} precision - 精度
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumber(num, precision = 0) {
  if (typeof num !== 'number' || isNaN(num)) return '0'
  
  if (num >= 1000000) {
    return (num / 1000000).toFixed(precision) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(precision) + 'K'
  } else {
    return num.toFixed(precision)
  }
}
