export const menuList = [
  {
    title: '首页',
    path: '/dashboard',
    icon: 'House',
    component: () => import('@/views/dashboard/index.vue')
  },
  {
    title: '书籍',
    name: 'bookGroup',
    path: '/book',
    icon: 'Notebook',
    children: [
      {
        title: '写作',
        name: 'bookWriting',
        path: '/book/writing',
        component: () => import('@/views/book/写作.vue'),
        meta: { keepAlive: true }
      },
      {
        title: '设定',
        name: 'bookSettings',
        path: '/book/settings/:id',
        component: () => import('@/views/book/设定.vue'),
        hidden: true,
        meta: { keepAlive: true }
      },
      {
        title: '大纲',
        name: 'bookOutline',
        path: '/book/outline',
        component: () => import('@/views/book/outline.vue')
      },
      {
        title: 'A2B',
        name: 'bookBridge',
        path: '/book/bridge',
        component: () => import('@/views/book/A2B.vue')
      },
      {
        title: '场景卡',
        name: 'bookSceneCards',
        path: '/book/cards',
        component: () => import('@/views/book/SceneCards.vue')
      },
      {
        title: 'AI提示词',
        name: 'bookAiPrompt',
        path: '/book/aiprompt',
        component: () => import('@/views/book/AiPrompt.vue')
      },
      {
        title: '关系图谱',
        name: 'bookRelation',
        path: '/book/relation',
        component: () => import('@/views/book/RelationShip.vue')
      },
      {
        title: '故事抽卡',
        name: 'bookStoryInspiration',
        path: '/book/inspiration',
        component: () => import('@/views/book/StoryInspiration.vue')
      },
      {
        title: '剧情抽卡',
        name: 'bookPilot',
        path: '/book/pilot',
        component: () => import('@/views/book/pilot.vue')
      },
      {
        title: '人设抽卡',
        name: 'bookCharacter',
        path: '/book/character',
        component: () => import('@/views/book/CharacterInspiration.vue')
      },
      {
        title: '汉语词典',
        name: 'bookDictionary',
        path: '/book/dictionary',
        icon: 'ReadOutlined',
        component: () => import('@/views/book/ChineseDictionary.vue'),
        meta: {
          keepAlive: false
        }
      },
      {
        title: '自定义卡池',
        name: 'bookCustomPool',
        path: '/book/custompool',
        component: () => import('@/views/book/CustomPool.vue')
      },
    ]
  },
  {
    title: '大模型',
    name: 'aiGroup',
    path: '/ai',
    icon: 'ChatSquare',
    children: [
      {
        title: '在线',
        name: 'chatOnline',
        path: '/chat/list',
        component: () => import('@/views/chat/chat.vue')
      
      }
    ]
  },
  {
    title: '万能工具箱',
    name: 'toolboxGroup',
    path: '/box',
    icon: 'Box',
    children: [

      {
        title: '高级Markdown编辑器',
        name: 'advancedMarkdownEditor',
        path: '/box/advanced-markdown',
        component: () => import('@/views/workers/AdvancedMarkdownEditor.vue')
      }
    ]
  },
  {
    title: '项目',
    name: 'projectGroup',
    path: '/inject_project',
    icon: 'Platform',
    children: [
      {
        title: '项目管理',
        name: 'projectManagement',
        path: '/inject_project/management',
        component: () => import('@/views/inject_project/项目.vue')
      },
    ]
  },
  {
    title: '本地化',
    name: 'downloadGroup',
    path: '/download',
    icon: 'Download',
    children: [
      {
        title: '阅读器',
        name: 'downloadReader',
        path: '/download/reader',
        component: () => import('@/views/tools/阅读器.vue')
      },
      {
        title: '小说下载',
        name: 'downloadNovel',
        path: '/download/novel',
        component: () => import('@/views/download/小说下载.vue')
      },
      {
        title: '知乎下载',
        name: 'downloadZhihu',
        path: '/download/zhihu',
        component: () => import('@/views/download/知乎下载.vue')
      },
      {
        title: '工作下载',
        name: 'downloadWork',
        path: '/download/work',
        component: () => import('@/views/download/工作.vue')
      },
    ]
  },
]