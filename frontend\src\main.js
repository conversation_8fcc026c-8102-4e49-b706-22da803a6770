import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import './theme.css'

// 引入 MateChat 组件库
import MateChat from '@matechat/core'
import '@devui-design/icons/icomoon/devui-icon.css'


import { useConfigStore } from './stores/config'



const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(ElementPlus, {
  size: 'default',
  zIndex: 3000
})
app.use(MateChat)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(router)






// 初始化自动备份
async function initializeAutoBackup() {
  try {
      await window.pywebview.api.check_auto_backup()
  } catch (error) {
    console.error('初始化自动备份出错:', error)
  }
}

// 等待pywebview API加载完成的函数
function waitForPywebviewAPI() {
  return new Promise((resolve) => {
    // 如果API已经加载，直接返回
    if (window.pywebview && window.pywebview.hasOwnProperty('api')) {
      resolve()
      return
    }
    
    // 创建一个计时器反复检查API是否就绪
    const intervalId = setInterval(() => {
      console.log("浏览器初始化中，等待pywebview API...")
      if (window.pywebview && window.pywebview.hasOwnProperty('api')) {
        clearInterval(intervalId) // 清除计时器
        resolve() // 继续执行
      }
    }, 500) // 每500毫秒检查一次
  })
}

// 应用主题的函数
const applyTheme = (theme) => {
  document.querySelector('html').className = theme
  console.log('应用主题:', theme)


}

// 预加载主题配置
const preloadTheme = async () => {
  try {
    // 先应用默认主题，避免白屏
    applyTheme('dark')

    // 尝试从 pywebview 获取主题配置
    if (window.pywebview && window.pywebview.api) {
      const result = await window.pywebview.api.get_settings()
      const config = typeof result === 'string' ? JSON.parse(result) : result

      if (config && config.theme) {
        applyTheme(config.theme)
        console.log('预加载主题成功:', config.theme)
      }
    }
  } catch (error) {
    console.warn('预加载主题失败，使用默认主题:', error)
    applyTheme('dark')
  }
}

// 初始化应用
const initializeApp = async () => {
  // 在挂载应用之前预加载主题
  await preloadTheme()

  // 挂载应用
  app.mount('#app')



  // 等待 DOM 完全渲染
  await new Promise(resolve => {
    if (document.readyState === 'complete') {
      resolve()
    } else {
      window.addEventListener('load', resolve, { once: true })
    }
  })



  // 加载配置
  const configStore = useConfigStore()
  try {
    await configStore.loadConfig()
    console.log('配置加载完成')

    // 配置加载完成后再次应用主题，确保使用最新的主题设置
    applyTheme(configStore.theme)

    // 检查是否需要初始化自动备份
    if (configStore.backup?.autoBackup) {
      await initializeAutoBackup()
      console.log('自动备份已初始化')
    }
  } catch (error) {
    console.error('配置加载失败:', error)
  }
}

// 启动应用
async function startApp() {
  await waitForPywebviewAPI()
  initializeApp()
}

startApp()
