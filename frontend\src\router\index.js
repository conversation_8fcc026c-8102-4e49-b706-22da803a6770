import { createRouter, createWebHashHistory } from 'vue-router'
import AppLayout from '@/components/layout/AppLayout.vue'
import { menuList } from '@/config/menuConfig'
import { useUserStore } from '@/stores/user'
import { useConfigStore } from '@/stores/config'

function generateRoutes(menus) {
  const routes = []

  menus.forEach(menu => {
    const route = {
      path: menu.path,
      name: menu.name || menu.path.replace(/[\/\:]/g, '').replace(/[\u4e00-\u9fa5]/g, ''),
      meta: {
        title: menu.title,
        icon: menu.icon,
        hidden: menu.hidden,
        requiresAuth: true,
        ...(menu.meta || {})
      }
    }

    // 只有叶子节点才设置 component
    if (menu.component && !menu.children) {
      route.component = menu.component
    }

    if (menu.children) {
      route.children = generateRoutes(menu.children)
    }

    routes.push(route)
  })

  return routes
}

const routes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/book/editor/:id',
    name: 'bookEditor',
    component: () => import('@/views/book/editor.vue'),
    meta: {
      title: '书籍编辑',
      requiresAuth: true,
      layout: 'editor'
    }
  },
  {
    path: '/',
    component: AppLayout,
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      ...generateRoutes(menuList),
      // 设定路由已在 menuConfig 中定义，这里移除重复
      {
        path: 'book/timeline/:id',
        name: 'bookTimeline',
        component: () => import('@/views/book/时间线.vue'),
        meta: {
          title: '时间线',
          requiresAuth: true,
          hidden: true
        }
      },
      // 故事创作灵感路由已在 menuConfig 中定义，这里移除重复
      {
        path: 'settings/app',
        name: 'settings',
        component: () => import('@/views/settings/application.vue'),
        meta: {
          title: '应用管理',
          icon: 'Setting',
          requiresAuth: true
        }
      },
      // 汉语词典路由已在 menuConfig 中定义，这里移除重复
    ]
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 全局前置守卫，用于权限控制
router.beforeEach(async (to, from, next) => {
  // 获取用户存储
  const userStore = useUserStore()
  
  // 检查该路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  
  // 如果路由需要认证
  if (requiresAuth) {
    // 检查用户是否已登录
    if (!userStore.isLoggedIn) {
      // 如果未登录，尝试自动登录
      try {
        const autoLoginSuccess = await userStore.autoLogin()
        
        if (!autoLoginSuccess) {
          // 自动登录失败，重定向到登录页
          next({
            path: '/login',
            query: { redirect: to.fullPath, loginError: '请先登录系统' } 
          })
          return
        }
      } catch (error) {
        console.error('路由守卫中的自动登录失败:', error)
        next({
          path: '/login',
          query: { redirect: to.fullPath, loginError: encodeURIComponent('登录验证失败，请重新登录') }
        })
        return
      }
    }
    
    // 如果用户已登录，检查激活状态是否过期
    // 如果上次检查时间超过30分钟，重新检查
    const lastChecked = userStore.lastChecked || 0
    const thirtyMinutes = 30 * 60 * 1000
    const needsRecheck = Date.now() - lastChecked > thirtyMinutes

    if (needsRecheck && !userStore.isCheckingActivation) {
      try {
        console.log('路由守卫：检查激活状态...')
        const activationStatus = await userStore.checkActivation(false) // 不强制退出
        if (!activationStatus || !activationStatus.valid) {
          // 激活状态无效，重定向到登录页
          next({
            path: '/login',
            query: {
              redirect: to.fullPath,
              loginError: encodeURIComponent('登录状态已过期，请重新登录')
            }
          })
          return
        }
      } catch (error) {
        console.error('路由守卫：检查激活状态失败:', error)
        // 网络错误不阻止路由跳转，但记录错误
      }
    }
  }
  
  // 允许访问
  next()
})

export default router
