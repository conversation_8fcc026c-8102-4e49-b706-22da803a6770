/* 应用容器样式 - 确保界面不滚动，保持原生应用体验 */
.application-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--el-bg-color-page);
  user-select: none; /* 整体界面不可选择 */
}

.application-wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 12px;
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* 标签页样式 */
.application-tabs {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  :deep(.el-tabs__header) {
    margin: 0;
    padding: 12px 16px 0;
    border-bottom: 1px solid var(--el-border-color-light);
    flex-shrink: 0;
    background-color: var(--el-bg-color);
    width: 100%;
    overflow: visible;

    .el-tabs__nav-wrap {
      width: 100%;
      overflow: visible;

      &::after {
        display: none;
      }

      .el-tabs__nav-scroll {
        overflow: visible;

        .el-tabs__nav {
          width: 100%;
          display: flex;
          flex-wrap: nowrap;
          overflow: visible;
        }
      }
    }

    .el-tabs__item {
      font-size: 14px;
      padding: 0 20px;
      height: 40px;
      line-height: 40px;
      transition: all 0.3s ease;
      color: var(--el-text-color-regular);
      flex-shrink: 0;
      white-space: nowrap;

      &.is-active {
        font-weight: 600;
        color: var(--el-color-primary);
      }

      &:hover {
        color: var(--el-color-primary);
      }
    }
  }

  :deep(.el-tabs__content) {
    flex: 1;
    overflow: hidden;
    padding: 0;

    .el-tab-pane {
      height: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
  }
}

/* 标签页内容区域 */
.tab-content {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 通用设置面板样式 */
.settings-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.settings-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--el-fill-color-lighter);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-darker);
    border-radius: 3px;
    
    &:hover {
      background: var(--el-border-color-dark);
    }
  }
}

/* 区域标题样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
  
  &.compact {
    margin-bottom: 12px;
  }
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }
  
  .header-actions {
    display: flex;
    gap: 8px;
  }
}

/* 设置卡片样式 */
.settings-card {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: var(--el-border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &.table-container {
    padding: 0;
    overflow: hidden;
    
    :deep(.el-table) {
      border-radius: 8px;
      border: none;
      
      .el-table__header {
        background-color: var(--el-fill-color-lighter);
      }
    }
  }
}

/* 路径输入组样式 */
.path-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
  
  .el-input {
    flex: 1;
  }
  
  .el-button {
    flex-shrink: 0;
  }
}

/* 带提示的输入框样式 */
.input-with-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .el-input-number {
    width: 120px;
  }
}

/* 滑块输入组合样式 */
.slider-with-input {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .unit-label {
    color: var(--el-text-color-regular);
    font-size: 14px;
    white-space: nowrap;
  }
}

/* 子区域标题样式 */
.subsection {
  margin: 24px 0;
  
  .subsection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

.subsection-title {
  font-size: 16px;
  font-weight: 600;
  margin: 20px 0 16px 0;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 8px;
}

/* 表单项提示样式 */
.form-item-tip {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
  line-height: 1.4;
  
  &.warning-tip {
    color: var(--el-color-warning);
  }
}

/* 空状态样式 */
.empty-providers,
.empty-models,
.empty-keys,
.empty-history {
  text-align: center;
  padding: 40px 20px;
  
  .empty-actions {
    margin-top: 12px;
    
    .el-button {
      margin: 0 4px;
    }
  }
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 12px;
  
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

/* 按钮组样式 */
.el-button-group {
  .el-button {
    margin: 0;
  }
}

/* 表格容器样式 */
.table-container {
  border-radius: 8px;
  overflow: hidden;
  
  :deep(.el-table) {
    border-radius: 8px;
    
    .el-table__header-wrapper {
      .el-table__header {
        background-color: var(--el-fill-color-lighter);
      }
    }
    
    .el-table__body-wrapper {
      .el-table__row {
        &:hover {
          background-color: var(--el-fill-color-light);
        }
      }
    }
  }
}

/* 卡片标题样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .application-container {
    border-radius: 0;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    
    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }
  
  .path-input-group {
    flex-direction: column;
    
    .el-input {
      width: 100%;
    }
  }
}

/* 加载状态样式 */
.el-loading-mask {
  border-radius: 12px;
}

/* 消息提示样式优化 */
:deep(.el-message) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 确保所有文本内容可以正确选择（除了界面控件） */
.settings-card {
  .el-input__inner,
  .el-textarea__inner,
  pre,
  code {
    user-select: text;
  }
}

/* 修复标签页导航宽度问题 */
.el-tabs--top .el-tabs__nav-wrap::after {
  display: none !important;
}

.el-tabs__nav-wrap {
  overflow: visible !important;
}

.el-tabs__nav-scroll {
  overflow: visible !important;
}

.el-tabs__nav {
  width: 100% !important;
  display: flex !important;
  flex-wrap: nowrap !important;
}

.el-tabs__item {
  flex-shrink: 0 !important;
  white-space: nowrap !important;
}
