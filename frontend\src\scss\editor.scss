
/* 响应式滚动条优化 */
@media (max-width: 768px) {
  * {
    &::-webkit-scrollbar {
      width: 6px; /* 移动端更窄 */
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      min-height: 16px;
      min-width: 16px;
      border-radius: 3px;
    }
  }
}

/* 减少动画偏好用户的滚动条优化 */
@media (prefers-reduced-motion: reduce) {
  * {
    &::-webkit-scrollbar-thumb {
      transition: none;
      transform: none;

      &:hover {
        transform: none;
      }

      &:active {
        transform: none;
      }
    }
  }
}

.editor-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.background-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -999;
  pointer-events: none;

  /* 优化背景图片渲染性能 */
  transform: translateZ(0); /* 启用硬件加速 */
  will-change: transform; /* 提示浏览器优化变换 */
  backface-visibility: hidden; /* 隐藏背面，减少渲染负担 */
  contain: strict; /* 严格限制重排和重绘范围 */
}

.book-editor {
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: transparent;
  z-index: 1;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  padding: 8px;
  background-color: rgba(var(--el-bg-color-rgb), 0.55); /* 增加不透明度让背景更清晰 */
  backdrop-filter: blur(3px); /* 减少背景模糊效果 */
  -webkit-backdrop-filter: blur(6px); /* Safari 兼容性 */
  border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.2); /* 更柔和的底部边框 */
  user-select: none;
  position: relative;
  z-index: 10;
  height: 50px; /* 固定高度避免变形 */
  overflow: visible; /* 允许下拉菜单溢出 */

  /* 左侧工具容器 */
  .left-tools {
    display: flex;
    align-items: center;
    flex-shrink: 1; /* 允许收缩 */
    min-width: 0; /* 允许内容压缩 */
    overflow: hidden; /* 超出部分隐藏 */

    /* 按钮组自适应 */
    .el-button-group {
      display: flex;
      flex-shrink: 0; /* 不收缩 */
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }

      /* 基本按钮保持一致的尺寸 */
      :deep(.el-button) {
        padding: 8px 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        min-width: 40px; /* 设置最小宽度 */

        /* 让按钮更好地融入背景 */
        background-color: rgba(var(--el-bg-color-rgb), 0.6) !important;
        border: 1px solid rgba(var(--el-border-color-rgb), 0.3) !important;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba(var(--el-bg-color-rgb), 0.8) !important;
          border-color: rgba(var(--el-border-color-rgb), 0.5) !important;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }
      }
    }

    /* TTS控制区域 */
    .tts-controls {
      display: flex;
      align-items: center;
      gap: 4px;
      overflow: hidden;
      flex-shrink: 1; /* 允许收缩 */

      /* TTS按钮样式 */
      .tts-btn {
        background-color: rgba(var(--el-bg-color-rgb), 0.6) !important;
        border: 1px solid rgba(var(--el-border-color-rgb), 0.3) !important;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        transition: all 0.3s ease;
        /* 固定按钮尺寸，防止内容变化时按钮大小改变 */
        min-width: 100px !important;
        width: 100px !important;
        height: 32px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 0 8px !important;
        box-sizing: border-box !important;

        /* 按钮内容布局 */
        .el-icon {
          margin-right: 4px !important;
          flex-shrink: 0 !important;
        }

        span {
          white-space: nowrap !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          flex: 1 !important;
          text-align: left !important;
        }

        &:hover {
          background-color: rgba(var(--el-bg-color-rgb), 0.8) !important;
          border-color: rgba(var(--el-border-color-rgb), 0.5) !important;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }

        &.is-active {
          background-color: rgba(var(--el-color-primary-rgb), 0.7) !important;
          border-color: rgba(var(--el-color-primary-rgb), 0.5) !important;
          color: var(--el-color-primary-light-3) !important;
        }

        /* 播放状态样式 - 红色停止按钮，更加明显 */
        &.is-playing {
          background: linear-gradient(135deg, #ff4757, #ff3742) !important;
          border-color: #ff4757 !important;
          color: white !important;
          box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.4) !important;
          animation: tts-pulse 2s infinite;

          &:hover {
            background: linear-gradient(135deg, #ff3742, #ff2f3a) !important;
            border-color: #ff3742 !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(255, 71, 87, 0.5), 0 0 0 3px rgba(255, 71, 87, 0.4) !important;
          }

          .el-icon {
            animation: tts-icon-pulse 1.5s infinite;
          }
        }

        &.stop-btn.can-stop {
          background-color: rgba(var(--el-color-danger-rgb), 0.7) !important;
          border-color: rgba(var(--el-color-danger-rgb), 0.5) !important;
          color: var(--el-color-danger-light-3) !important;
        }
      }

      @media (max-width: 1280px) {
        /* 小屏幕时隐藏按钮文本，只显示图标 */
        .tts-btn span {
          display: none;
        }

        .tts-btn {
          /* 小屏幕时调整为正方形按钮 */
          min-width: 32px !important;
          width: 32px !important;
          height: 32px !important;
          padding: 0 !important;

          .el-icon {
            margin-right: 0 !important;
          }
        }
      }

      @media (max-width: 960px) {
        /* 更小的屏幕时隐藏一些次要控件 */
        .tts-btn:not(.stop-btn) {
          display: none;
        }
      }
    }

    /* 分隔线 */
    .el-divider--vertical {
      margin: 0 8px;
      flex-shrink: 0; /* 不收缩 */

      @media (max-width: 960px) {
        /* 小屏隐藏部分分隔线 */
        display: none;
      }
    }

    /* 实体和场景按钮区 */
    .entity-btn, .scene-create-btn {
      flex-shrink: 0; /* 不收缩 */
      background-color: rgba(var(--el-bg-color-rgb), 0.6) !important;
      border: 1px solid rgba(var(--el-border-color-rgb), 0.3) !important;
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(var(--el-bg-color-rgb), 0.8) !important;
        border-color: rgba(var(--el-border-color-rgb), 0.5) !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      }

      @media (max-width: 960px) {
        /* 小屏幕时隐藏按钮文本，只显示图标 */
        span {
          display: none;
        }
      }
    }

    /* 为所有独立按钮添加统一的融入背景样式 */
    :deep(.el-button):not(.el-button-group .el-button):not(.tts-btn):not(.entity-btn):not(.scene-create-btn) {
      background-color: rgba(var(--el-bg-color-rgb), 0.6) !important;
      border: 1px solid rgba(var(--el-border-color-rgb), 0.3) !important;
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(var(--el-bg-color-rgb), 0.8) !important;
        border-color: rgba(var(--el-border-color-rgb), 0.5) !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      }

      &.is-active {
        background-color: rgba(var(--el-color-primary-rgb), 0.7) !important;
        border-color: rgba(var(--el-color-primary-rgb), 0.5) !important;
        color: var(--el-color-primary-light-3) !important;
      }
    }
  }

  /* 拖拽区 */
  .drag-area {
    flex: 1;
    height: 100%;
    -webkit-app-region: drag;
    user-select: none;
    min-width: 50px; /* 确保至少有一点空间可拖拽 */
  }

  /* 右侧工具栏 */
  .right-tools {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0; /* 不收缩 */

    /* 右侧按钮样式 */
    :deep(.el-button) {
      background-color: rgba(var(--el-bg-color-rgb), 0.6) !important;
      border: 1px solid rgba(var(--el-border-color-rgb), 0.3) !important;
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(var(--el-bg-color-rgb), 0.8) !important;
        border-color: rgba(var(--el-border-color-rgb), 0.5) !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      }

      /* 保存按钮特殊样式 */
      &.el-button--primary {
        background-color: rgba(var(--el-color-primary-rgb), 0.8) !important;
        border-color: rgba(var(--el-color-primary-rgb), 0.6) !important;

        &:hover {
          background-color: rgba(var(--el-color-primary-rgb), 0.9) !important;
          border-color: rgba(var(--el-color-primary-rgb), 0.8) !important;
        }
      }
    }

    @media (max-width: 960px) {
      /* 小屏幕时隐藏按钮文本，只显示图标 */
      :deep(.el-button span) {
        display: none;
      }

      :deep(.el-button) {
        padding: 8px !important;
        min-width: unset !important;
      }
    }
  }

  /* 响应式工具栏最小窗口下的处理 */
  @media (max-width: 768px) {
    padding: 4px;

    .left-tools, .right-tools {
      gap: 4px;
    }

    /* 小屏下为更多按钮提供下拉菜单 */
    .toolbar-dropdown {
      display: block;
    }

    /* 隐藏普通屏幕下的按钮 */
    .hide-on-small {
      display: none;
    }
  }
}

/* 添加更多按钮下拉菜单样式 */
.toolbar-dropdown {
  display: none; /* 默认隐藏，只在小屏下显示 */

  .toolbar-dropdown-btn {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.el-dropdown-menu__item) {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-icon {
      margin-right: 4px;
    }
  }
}

.editor-container {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
  padding-right: 0;
}

.chapters-sidebar {
  flex: 0 0 280px; /* 固定宽度且不收缩，避免被聊天侧栏挤压 */
  min-width: 280px;

  width: 280px;
  background: rgba(var(--el-bg-color-rgb), 0.7); /* 使用半透明背景 */
  border-right: 1px solid rgba(var(--el-border-color-rgb), 0.3); /* 柔和边框 */
  display: flex;
  flex-direction: column;
  height: calc(100% - 40px); /* 减去底部状态栏高度 */
  max-height: calc(100% - 40px);
  overflow: hidden; /* 防止整体溢出 */
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1); /* 更流畅的缓动函数 */
  position: relative;
  border-radius: 10px;
  backdrop-filter: blur(12px); /* 增强背景模糊效果 */
  -webkit-backdrop-filter: blur(12px); /* Safari 兼容性 */
  transform-origin: left center;

  /* 确保搜索头部不占用太多空间 */
  .sidebar-header {
    padding: 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-bg-color-overlay);
    position: sticky;
    top: 0;
    z-index: 10;
    flex-shrink: 0; /* 不允许搜索区域压缩 */
    transition: opacity 0.3s ease, transform 0.3s ease;

    .search-container {
      margin-bottom: 12px;

      :deep(.el-input) {
        .el-input__wrapper {
          box-shadow: 0 0 0 1px var(--el-border-color) inset;

          &:hover {
            box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px var(--el-color-primary) inset;
          }
        }
      }
    }

    .actions {
      display: flex;
      gap: 8px;

      .el-button-group {
        width: 100%;
        display: flex;

        .el-button {
          flex: 1;
          justify-content: center;

          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }
  }

  .search-results {
    flex: 1;
    overflow-y: auto;
    height: 100%;
    padding: 12px;
    overflow-x: hidden;
    transition: opacity 0.3s ease, transform 0.3s ease;

    .search-volume {
      margin-bottom: 16px;
      background: var(--el-fill-color-light);
      border-radius: 6px;
      overflow: hidden;

      .volume-header {
        padding: 10px 12px;
        font-weight: 500;
        color: var(--el-color-primary);
        background: var(--el-fill-color);
        border-bottom: 1px solid var(--el-border-color-lighter);
      }

      .chapter-list {
        max-height: none; /* 移除最大高度限制 */
        overflow-y: auto;
        padding: 6px;

        .chapter-item {
          padding: 8px 12px;
          margin: 4px 0;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: var(--el-fill-color);
          }

          &.is-active {
            background: var(--el-color-primary-light-9);
            color: var(--el-color-primary);
            font-weight: 500;
          }

          .chapter-title {
            font-size: 14px;
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
          }
        }
      }
    }

    .no-results {
      padding: 20px;
      text-align: center;
    }
  }

  .chapters-tree {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 8px;
    height: 100%;
    transition: opacity 0.3s ease, transform 0.3s ease;

    /* 美化滚动条 - 使用全局样式，保持一致性 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color-light);
      border-radius: 3px;
      transition: all 0.2s ease;

      &:hover {
        background: var(--el-border-color-dark);
        transform: scaleX(1.2);
      }
    }

    /* 卷节点样式 */
    .volume-node {
      margin-bottom: 8px;
      border-radius: 8px;
      background: var(--el-fill-color-light);
      border: 1px solid var(--el-border-color-lighter);
      transition: all 0.3s ease;
      overflow: hidden;

      &:hover {
        border-color: var(--el-color-primary-light-7);
        box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.1);
      }

      &.expanded {
        background: var(--el-bg-color-overlay);
      }

      .volume-header {
        display: flex;
        align-items: center;
        height: 48px;
        padding: 0 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;

        &:hover {
          background: var(--el-fill-color);
        }

        .volume-expand-icon {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 8px;

          .expand-arrow {
            font-size: 14px;
            color: var(--el-text-color-secondary);
            transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), color 0.2s ease;
            transform-origin: center;

            &.expanded {
              transform: rotate(90deg);
              color: var(--el-color-primary);
            }

            &:hover {
              color: var(--el-color-primary);
            }
          }
        }

        .volume-icon {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 10px;

          .el-icon {
            font-size: 18px;
            color: var(--el-color-primary);
          }
        }

        .volume-title {
          flex: 1;
          font-weight: 600;
          font-size: 15px;
          color: var(--el-text-color-primary);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-right: 12px;
        }

        .volume-info {
          display: flex;
          align-items: center;
          margin-right: 8px;

          .chapter-count {
            background: var(--el-color-primary-light-8);
            color: var(--el-color-primary);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            min-width: 20px;
            text-align: center;
          }
        }

        .volume-actions {
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.2s ease;

          .action-btn {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: var(--el-color-primary-light-9);
              color: var(--el-color-primary);
            }

            .el-icon {
              font-size: 16px;
            }
          }
        }

        &:hover .volume-actions {
          opacity: 1;
        }
      }

      /* 章节容器 */
      .chapters-container {
        border-top: 1px solid var(--el-border-color-lighter);
        background: var(--el-bg-color-page);

        .chapter-node {
          display: flex;
          align-items: center;
          height: 42px;
          padding: 0 16px 0 44px;
          cursor: pointer;
          transition: all 0.2s ease;
          border-bottom: 1px solid var(--el-border-color-extra-light);
          position: relative;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            background: var(--el-fill-color-light);
          }

          &.active {
            background: var(--el-color-primary-light-9);
            color: var(--el-color-primary);
            font-weight: 500;

            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 0;
              bottom: 0;
              width: 3px;
              background: var(--el-color-primary);
            }
          }

          &.dragging {
            opacity: 0.5;
            transform: scale(0.98);
          }

          .chapter-content {
            display: flex;
            align-items: center;
            width: 100%;

            .chapter-icon {
              width: 16px;
              height: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 10px;

              .el-icon {
                font-size: 14px;
                color: var(--el-text-color-secondary);
              }
            }

            .chapter-title {
              flex: 1;
              font-size: 14px;
              color: var(--el-text-color-primary);
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-right: 12px;
            }

            .chapter-info {
              display: flex;
              align-items: center;
              margin-right: 8px;

              .word-count {
                font-size: 12px;
                color: var(--el-text-color-secondary);
                background: var(--el-fill-color);
                padding: 2px 6px;
                border-radius: 8px;
              }
            }

            .chapter-actions {
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              opacity: 0;
              transition: opacity 0.2s ease;

              .action-btn {
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                  background: var(--el-color-primary-light-9);
                  color: var(--el-color-primary);
                }

                .el-icon {
                  font-size: 14px;
                }
              }
            }
          }

          &:hover .chapter-actions {
            opacity: 1;
          }
        }

        .empty-chapters {
          padding: 24px 16px;
          text-align: center;
          color: var(--el-text-color-secondary);

          .el-icon {
            font-size: 32px;
            margin-bottom: 8px;
            color: var(--el-text-color-disabled);
          }

          span {
            display: block;
            margin-bottom: 12px;
            font-size: 14px;
          }
        }
      }
    }

    .empty-volumes {
      padding: 48px 16px;
      text-align: center;
      color: var(--el-text-color-secondary);

      .el-icon {
        font-size: 48px;
        margin-bottom: 16px;
        color: var(--el-text-color-disabled);
      }

      span {
        display: block;
        margin-bottom: 16px;
        font-size: 16px;
      }
    }
  }

  /* 卷展开动画 - 更流畅的展开效果 */
  .volume-expand-enter-active {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    overflow: hidden;
  }

  .volume-expand-leave-active {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
    overflow: hidden;
  }

  .volume-expand-enter-from {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }

  .volume-expand-leave-to {
    max-height: 0;
    opacity: 0;
    transform: translateY(-5px);
  }

  .volume-expand-enter-to,
  .volume-expand-leave-from {
    max-height: 500px;
    opacity: 1;
    transform: translateY(0);
  }
}

.action-icon {
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--el-text-color-secondary);

  &:hover {
    background: var(--el-fill-color);
    color: var(--el-text-color-primary);
  }
}

:deep(.el-dropdown-menu) {
  padding: 6px;
  min-width: 120px;

  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 4px;
    margin-bottom: 2px;

    .el-icon {
      font-size: 14px;
    }

    &:hover {
      background-color: var(--el-color-primary-light-9);
    }

    &.danger {
      color: var(--el-color-danger);

      &:hover {
        background-color: var(--el-color-danger-light-9);
      }
    }
  }
}

.custom-dropdown {
  .danger {
    color: var(--el-color-danger);
  }
}

// 侧边栏过渡动画 - 更流畅的进入和离开效果
.sidebar-enter-active {
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* 弹性缓动 */
}

.sidebar-leave-active {
  transition: all 0.35s cubic-bezier(0.55, 0.055, 0.675, 0.19); /* 快速离开 */
}

.sidebar-enter-from {
  transform: translateX(-100%) scale(0.9);
  opacity: 0;
}

.sidebar-leave-to {
  transform: translateX(-100%) scale(0.95);
  opacity: 0;
}

.sidebar-enter-to,
.sidebar-leave-from {
  transform: translateX(0) scale(1);
  opacity: 1;
}

/* 为侧边栏添加硬件加速 */
.chapters-sidebar {
  will-change: transform, opacity, width;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 移除了旧的 el-menu 相关样式，因为已改用自定义目录组件 */

.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  margin-left: 0;
  z-index: 1;
  background-color: transparent;
  width: 100%;
  padding-bottom: 40px; /* 状态栏高度 */

  .editor-content {
    min-height: 100%;
    padding: 20px;
    padding-bottom: 50vh;
    cursor: text;

    :deep(.ProseMirror) {
      cursor: text;

      p {
        cursor: text;
      }
    }
  }
}
.is-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent !important;
  z-index: 1500; /* 确保全屏模式显示在最上层 */

  .chapters-sidebar {
    transform: translateX(-280px);
  }

  .editor-main {
    margin-left: 0;
  }

  /* 全屏下保持编辑器内容区样式 */
  :deep(.ProseMirror) {
    background-color: var(--el-bg-color-rgb); /* 保持一致的透明度 */

  }


}
/* 非全屏状态下的样式 */
.book-editor:not(.is-fullscreen) {
  .editor-main {
    margin-left: 0;
  }
}

.editor-content {
  flex: 1;
  overflow-y: auto;
  background-color: transparent;
  position: relative;
  z-index: 1;
  padding-bottom: 60px !important; /* 确保底部状态栏不遮挡内容 */

  /* 优化滚动性能 */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
  will-change: scroll-position; /* 提示浏览器优化滚动 */

  /* 编辑器内容区域的滚动条样式 - 继承全局样式但稍作调整 */
  // &::-webkit-scrollbar {
  //   width: 4px !important; /* 编辑器区域稍宽一些，便于操作 */
  //   background: transparent !important;
  //   /* 优化滚动条渲染性能 */
  //   will-change: opacity;
  //   transform: translateZ(0); /* 启用硬件加速 */
  // }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-light) !important;
    border-radius: 5px;
    border: 1px solid transparent;
    background-clip: content-box;
    cursor: pointer;
    min-height: 10px; /* 便于拖拽 */
    max-height: 10px;
    transition: all 0.2s ease;

    &:hover {
      background: var(--el-border-color-dark) !important;
      cursor: pointer;
      transform: scaleX(1.2);
    }

    &:active {
      background: var(--el-border-color-darker) !important;
      cursor: grabbing;
      transform: scaleX(1.1);
      transition: none;
    }
  }

  // &::-webkit-scrollbar-track {
  //   background: transparent;
  //   border-radius: 5px;
  // }

  // &::-webkit-scrollbar-button {
  //   display: none;
  // }

  // &::-webkit-scrollbar-corner {
  //   background: transparent;
  // }

  /* 移动端滚动条优化 */
  @media (max-width: 768px) {
    &::-webkit-scrollbar {
      width: 6px !important;
    }

    &::-webkit-scrollbar-thumb {
      min-height: 10px;
      border-radius: 3px;
    }
  }

  /* 暗色主题下的编辑器滚动条 */
  html.dark & {
    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2) !important;

      &:hover {
        background: rgba(255, 255, 255, 0.3) !important;
      }

      &:active {
        background: rgba(255, 255, 255, 0.4) !important;
      }
    }
  }

  @media (prefers-reduced-motion: reduce) {
    /* 为偏好减少动画的用户禁用滚动条过渡 */
    &::-webkit-scrollbar-thumb {
      transition: none;
      transform: none;

      &:hover {
        transform: none;
      }

      &:active {
        transform: none;
      }
    }
  }
}

.chapter-header {
  padding: 40px 40px 30px;
  background-color: transparent; /* 完全透明，让背景图片清晰显示 */
  position: relative;
  margin-bottom: 20px;
  z-index: 1; /* 确保标题区域在上层 */

  .chapter-title-input {
    z-index: 1000;
    font-family: var(--editor-font-family);
    font-size: 36px;
    font-weight: 500;
    color: var(--editor-font-color, var(--el-text-color-primary));
    background: transparent;
    border: none;
    width: 100%;
    outline: none;
    padding: 0;
    margin: 0;

    /* 添加文字阴影确保在任何背景上都清晰可见 */
    text-shadow:
      0 1px 3px rgba(0, 0, 0, 0.3),
      0 2px 6px rgba(0, 0, 0, 0.2),
      1px 1px 0 rgba(255, 255, 255, 0.1),
      -1px -1px 0 rgba(255, 255, 255, 0.1);

    -webkit-appearance: none;
    appearance: none; /* 添加标准属性 */

    &::placeholder {
      color: var(--el-text-color-placeholder);
      opacity: 0.5;
      text-shadow:
        0 1px 2px rgba(0, 0, 0, 0.2),
        1px 1px 0 rgba(255, 255, 255, 0.1);
    }
  }
}

.bg-preview {
  width: 100%;
  height: 200px;
  margin-bottom: 16px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .preview-label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 4px 8px;
    font-size: 12px;
    text-align: center;
  }
}

.font-settings-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
  }

  :deep(.el-dialog) {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    max-height: 90vh;
    overflow-y: auto;

    /* 确保弹窗显示在屏幕中心 */
    &.el-dialog--center {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.settings-content {
  display: flex;
  flex-direction: column;
}

.settings-section {
  .setting-group {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .setting-item {
    .setting-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .setting-label {
        font-weight: 500;
        color: var(--el-text-color-primary);
      }

      .setting-value {
        color: var(--el-text-color-secondary);
        font-size: 0.9em;
      }
    }

    .font-select {
      width: 100%;
    }

    .custom-slider {
      margin: 8px 0;
    }
  }
}

.dialog-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--el-border-color-light);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: var(--el-bg-color);
}

.chapter-list {
  /* 确保容器可以滚动 */
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;

  /* 滚动条整体部分 */
  &::-webkit-scrollbar {
    width: 14px; /* 增加宽度 */
    background: rgba(0, 0, 0, 0.02);
    border-radius: 7px;
    cursor: pointer;
  }

  /* 滚动条滑块 - 使用主题适配的颜色 */
  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-light);
    border-radius: 4px;
    border: 1px solid transparent;
    background-clip: content-box;
    cursor: pointer;
    min-height: 20px;
    transition: all 0.2s ease;

    /* 鼠标悬停时的样式 */
    &:hover {
      background: var(--el-border-color-dark);
      cursor: pointer;
      transform: scaleX(1.2);
    }

    /* 鼠标按下时的样式 */
    &:active {
      background: var(--el-border-color-darker);
      cursor: grabbing;
      transform: scaleX(1.1);
      transition: none;
    }
  }

  /* 滚动条轨道 */
  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  /* 滚动条两端按钮 */
  &::-webkit-scrollbar-button {
    display: none;
  }

  /* 滚动条角落 */
  &::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* 当容器内容较少时，隐藏滚动条 */
  &.no-scroll {
    overflow: hidden;
  }

  /* 暗色主题下的章节列表滚动条 */
  html.dark & {
    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.15);

      &:hover {
        background: rgba(255, 255, 255, 0.25);
      }

      &:active {
        background: rgba(255, 255, 255, 0.35);
      }
    }
  }
}

/* 移除了旧的章节和卷样式定义，新样式在 chapters-tree 中定义 */

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);

  .actions {
    display: flex;
    gap: 8px;

    :deep(.el-button) {
      flex: 1;
    }
  }
}

.tts-controls {
  display: flex;
  gap: 8px;
  align-items: center;
  margin: 0 12px;

  .tts-btn {
    position: relative;

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }

    &.is-active {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary);
    }

    .network-error-indicator {
      position: absolute;
      right: -3px;
      top: -3px;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: var(--el-color-danger);
      border: 1px solid white;
    }
  }
}

.writing-stats-panel {
  position: fixed;
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
  width: 180px;
  z-index: 5;
  user-select: none;
  backdrop-filter: blur(10px);
  border: 1px solid var(--el-border-color-light);

  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    cursor: move;

    .title {
      font-size: 13px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .drag-handle {
      cursor: move;
      color: var(--el-text-color-secondary);
      font-size: 14px;

      &:hover {
        color: var(--el-text-color-primary);
      }
    }
  }

  .stats-content {
    padding: 10px;

    .stat-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .stat-item {
      width: 100%;

      .stat-label {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        margin-bottom: 2px;
      }

      .stat-value {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}



.entity-dialog {
  :deep(.el-dialog__body) {
    padding: 20px 32px;
  }

  .entity-form {
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 16px;

    .el-divider {
      margin: 24px 0;

      .el-divider__text {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    .template-select {
      width: 100%;
    }

    .el-form-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}



.scene-create-dialog {
  :deep(.el-dialog__body) {
    padding: 20px 32px;
  }

  .scene-form {
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 16px;

    .el-divider {
      margin: 24px 0;

      .el-divider__text {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    .template-select {
      width: 100%;
    }

    .el-form-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}



.editor-status-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  z-index: 101; /* 确保在聊天侧边栏之上 */
  background-color: rgba(var(--el-bg-color-rgb), 0.8); /* 降低不透明度 */
  backdrop-filter: blur(12px); /* 增强背景模糊效果 */
  -webkit-backdrop-filter: blur(12px); /* Safari 兼容性 */
  border-top: 1px solid rgba(var(--el-border-color-rgb), 0.2); /* 更柔和的边框 */
  display: flex;
  justify-content: space-between;
  align-items: center;

  /* 当有写作统计时调整布局 */
  &:has(.status-center) {
    justify-content: flex-start;

    .status-left {
      flex: 0 0 auto;
    }

    .status-center {
      flex: 1;
      justify-content: center;
    }

    .status-right {
      flex: 0 0 auto;
    }
  }

  /* 基本样式声明 */
  padding: 0 16px;
  font-size: 13px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  user-select: none;

  /* 响应式布局优化 - 保持一行显示 */
  @media (max-width: 1200px) {
    font-size: 12px; /* 减小字体 */
    padding: 0 12px; /* 减小内边距 */

    &:has(.status-center) {
      .status-left {
        gap: 16px; /* 减小间距 */
      }

      .status-center {
        gap: 12px; /* 减小统计项目间距 */

        .writing-stats-item {
          font-size: 11px;
          padding: 2px 6px;
        }
      }
    }
  }

  @media (max-width: 900px) {
    font-size: 11px; /* 进一步减小字体 */
    padding: 0 8px; /* 进一步减小内边距 */

    &:has(.status-center) {
      .status-left {
        gap: 12px; /* 进一步减小间距 */
      }

      .status-center {
        gap: 6px; /* 进一步减小统计项目间距 */
        padding: 4px 8px; /* 减小内边距 */

        .writing-stats-item {
          font-size: 10px;
          padding: 1px 3px;

          .status-label {
            display: none; /* 隐藏标签，只显示数值 */
          }
        }
      }
    }
  }

  @media (max-width: 600px) {
    font-size: 10px; /* 最小字体 */
    padding: 0 6px; /* 最小内边距 */

    .status-left {
      gap: 8px; /* 最小间距 */

      .status-item {
        .status-label {
          display: none; /* 隐藏左侧标签 */
        }
      }
    }

    &:has(.status-center) {
      .status-center {
        gap: 4px; /* 最小统计项目间距 */
        padding: 2px 6px; /* 最小内边距 */

        .writing-stats-item {
          font-size: 9px;
          padding: 1px 2px;

          .status-value {
            /* 进一步压缩数值显示 */
            letter-spacing: -0.5px;
          }
        }
      }
    }
  }

  /* 极小屏幕下隐藏部分统计项目 */
  @media (max-width: 480px) {
    &:has(.status-center) {
      .status-center {
        gap: 3px;
        padding: 2px 4px;

        /* 只显示前两个统计项目 */
        .writing-stats-item:nth-child(n+3) {
          display: none;
        }

        .writing-stats-item {
          font-size: 8px;
          padding: 1px;
        }
      }
    }
  }
}

.status-left, .status-center, .status-right {
  display: flex;
  align-items: center;
}

.status-left {
  gap: 24px;
  flex-shrink: 1; /* 允许收缩 */
  min-width: 0; /* 允许内容压缩 */
}

.status-center {
  gap: 16px; /* 减少间距以适应更多项目 */
  margin: 0 auto; /* 居中显示 */
  flex-wrap: nowrap; /* 禁止换行 */
  justify-content: center; /* 居中对齐 */
  cursor: pointer; /* 整个区域可点击 */
  padding: 6px 12px; /* 增加点击区域 */
  border-radius: 6px; /* 圆角 */
  border: 1px solid rgba(var(--el-border-color-rgb), 0.2); /* 微妙的边框 */
  transition: all 0.2s ease;
  overflow: hidden; /* 隐藏溢出内容 */

  .writing-stats-item {
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 4px 8px;
    white-space: nowrap; /* 防止文字换行 */
    pointer-events: none; /* 禁用子元素的点击事件，让父元素处理 */
  }

  /* 整个区域的悬停效果 */
  &:hover {
    background-color: rgba(var(--el-color-primary-rgb), 0.05);
    border-color: rgba(var(--el-color-primary-rgb), 0.4); /* 悬停时边框更明显 */
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .writing-stats-item {
      .status-value {
        color: var(--el-color-primary);
      }

      /* 为不同的统计项目添加不同的悬停颜色 */
      &:nth-child(1) .status-value {
        color: var(--el-color-success); /* 本次码字 - 绿色 */
      }

      &:nth-child(2) .status-value {
        color: var(--el-color-warning); /* 码字速率 - 橙色 */
      }

      &:nth-child(3) .status-value {
        color: var(--el-color-primary); /* 码字时间 - 蓝色 */
      }

      &:nth-child(4) .status-value {
        color: var(--el-color-info); /* 空闲时间 - 灰色 */
      }
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

.status-right {
  gap: 16px;
  flex-shrink: 1; /* 允许收缩 */
  min-width: 0; /* 允许内容压缩 */

  .time-item {
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 4px 8px;

    &:hover {
      background-color: rgba(var(--el-color-info-rgb), 0.08);

      .status-value {
        color: var(--el-color-info);
      }
    }
  }
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 12px;
  position: relative;
  font-weight: 400;
  letter-spacing: 0.2px;

  .status-label {
    color: var(--el-text-color-regular);
    font-size: 13px;
  }

  .status-value {
    font-weight: 500;
    color: var(--el-text-color-primary);
    font-size: 13px;
  }

  html.dark & {
    .status-label {
      color: var(--el-text-color-secondary);
    }

    .status-value {
      color: var(--el-text-color-primary);
      opacity: 0.95;
    }
  }
}

.status-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 16px;
  width: 1px;
  background-color: var(--el-border-color-lighter);
  opacity: 0.8;

  html.dark & {
    opacity: 0.2;
  }
}

.status-item.time {
  border-left: none;
}

.is-fullscreen .editor-status-bar {
  background-color: rgba(var(--el-bg-color-rgb), 0.85); /* 全屏模式下稍微提高不透明度 */
}

.status-item {
  transition: background-color 0.2s ease;
  border-radius: 4px;

  &:hover {
    background-color: rgba(var(--el-color-primary-rgb), 0.04);

    .status-value {
      color: var(--el-color-primary);
    }
  }
}

.status-right .status-item {
  .status-value {

    font-feature-settings: "tnum";
  }
}

.background-settings-dialog {
  :deep(.el-dialog) {
    height: 550px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    margin-top: 5vh !important;

    .el-dialog__header {
      padding: 16px 20px;
      margin-right: 0;
      border-bottom: 1px solid var(--el-border-color-lighter);
      flex-shrink: 0;
    }

    .el-dialog__body {
      padding: 20px;
      flex: 1;
      overflow: hidden;
    }

    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid var(--el-border-color-lighter);
      flex-shrink: 0;
    }
  }

  .bg-settings-form {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0;
    margin: 0;

    .el-form-item:first-child {
      margin-bottom: 16px;
      flex-shrink: 0;
    }

    .el-form-item:nth-child(2) {
      flex: 1;
      margin-bottom: 0;
      overflow: hidden;
      min-height: 0;

      :deep(.el-form-item__content) {
        height: 100%;
      }

      .el-tabs {
        height: 100%;
        display: flex;
        flex-direction: column;

        :deep(.el-tabs__header) {
          margin-bottom: 16px;
          flex-shrink: 0;
        }

        :deep(.el-tabs__content) {
          flex: 1;
          overflow: hidden !important;
        }

        :deep(.el-tab-pane) {
          overflow: hidden !important;
          height: 100%;
          display: flex;
          flex-direction: column;
        }
      }
    }

    .el-form-item:last-child {
      margin-bottom: 0;
      padding-top: 16px;
      border-top: 1px solid var(--el-border-color-lighter);
      flex-shrink: 0;
    }
  }

  .history-bg-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;

    &.scrollable-container {
      height: auto;
    }
  }

  .scrollable-content {
    flex: 1;
    overflow-y: auto !important;
    min-height: 0;
    padding: 12px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color-light);
      border-radius: 3px;
      transition: all 0.2s ease;

      &:hover {
        background: var(--el-border-color-dark);
        transform: scaleX(1.2);
      }
    }
  }

  .history-bg-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    padding-bottom: 16px;
  }
}

/* 移除了空的 scrollable-container 规则集 */

.bg-preview-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;

  .bg-preview {
    width: 100%;
    height: 300px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .no-bg-selected {
    height: 300px;
  }
}

.no-background-selected {
  width: 100%;
  padding: 16px;
  border: 1px dashed var(--el-border-color);
  border-radius: 4px;
  background-color: var(--el-bg-color-overlay);
  color: var(--el-text-color-secondary);
  text-align: center;
  margin-bottom: 16px;

  .el-alert {
    margin-bottom: 16px;
  }
}

.fixed-height-dialog {
  :deep(.el-dialog__body) {
    max-height: 70vh;
    overflow-y: auto;
  }
}

.bg-settings-form {
  padding: 20px;
}

.fixed-height-dialog {
  :deep(.el-dialog) {
    height: 80vh;
    display: flex;
    flex-direction: column;
    margin-top: 5vh !important;

    .el-dialog__header {
      padding: 16px 20px;
      margin-right: 0;
      border-bottom: 1px solid var(--el-border-color-lighter);
    }

    .el-dialog__body {
      padding: 20px;
      flex: 1;
      overflow: hidden;
    }
  }
}

.bg-settings-form {
  height: 100%;
  display: flex;
  flex-direction: column;

  .el-form-item:last-child {
    margin-bottom: 0;
  }

  .el-form-item:nth-child(2) {
    flex: 1;
    margin-bottom: 20px;

    :deep(.el-form-item__content) {
      height: 100%;
    }

    .el-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;

      :deep(.el-tabs__header) {
        margin-bottom: 15px;
      }

      :deep(.el-tabs__content) {
        flex: 1;
        overflow: hidden;
      }

      :deep(.el-tab-pane) {
        height: 100%;
      }
    }
  }
}

.history-bg-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .history-bg-loading, .empty-history {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-secondary);

    .el-icon {
      font-size: 40px;
      margin-bottom: 12px;
    }
  }
}

.scrollable-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-light);
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      background: var(--el-border-color-dark);
      transform: scaleX(1.2);
    }
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }
}

.history-bg-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding-bottom: 20px;

  .history-bg-item {
    cursor: pointer;
    border-radius: 6px;
    overflow: hidden;
    border: 2px solid transparent;
    background-color: var(--el-bg-color);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    position: relative;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);

      .bg-thumb-img {
        transform: scale(1.05);
      }
    }

    &.active {
      outline: 3px solid var(--el-color-primary);
      box-shadow: 0 0 0 3px rgba(var(--el-color-primary-rgb), 0.3),
                 0 8px 20px rgba(0, 0, 0, 0.2);
      z-index: 2;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background-color: var(--el-color-primary);
        z-index: 3;
      }

      .bg-info {
        background-color: rgba(var(--el-color-primary-rgb), 0.1);
        color: var(--el-color-primary);
        font-weight: 500;
      }

      .active-indicator {
        opacity: 1;
        transform: scale(1);
      }
    }

    .bg-thumb {
      position: relative;
      width: 100%;
      height: 120px;
      background-color: #f5f5f5;
      overflow: hidden;

      .bg-thumb-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;

        &.loading {
          opacity: 0;
        }
      }
    }
  }
}

.no-background-selected {
  position: sticky;
  bottom: 0;
  background-color: var(--el-bg-color);
  padding: 10px 0;
  margin-top: 10px;
  z-index: 5;
  box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.bg-preview-wrapper {
  margin-bottom: 20px;

  .bg-preview, .no-bg-selected {
    border-radius: 6px;
    overflow: hidden;
  }
}

.background-settings-dialog {
  :deep(.el-dialog) {
    height: 550px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    margin-top: 5vh !important;

    .el-dialog__header {
      padding: 16px 20px;
      margin-right: 0;
      border-bottom: 1px solid var(--el-border-color-lighter);
      flex-shrink: 0;
    }

    .el-dialog__body {
      padding: 20px;
      flex: 1;
      overflow: hidden;
    }

    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid var(--el-border-color-lighter);
      flex-shrink: 0;
    }
  }

  .history-bg-container {
    height: 300px !important;
    overflow: hidden !important;
    display: block !important;
  }

  .scrollable-content {
    height: 100% !important;
    overflow-y: auto !important;
    display: block !important;
  }

  .history-bg-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    padding: 16px;
  }

  .history-bg-item {
    cursor: pointer;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .bg-thumb {
      height: 140px;
      position: relative;

      .bg-thumb-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .bg-info {
      padding: 8px;
      font-size: 12px;
    }

    &.active {
      border: 2px solid var(--el-color-primary);
    }
  }
}

.background-settings-dialog {
  .el-dialog__body {
    padding: 20px;
    height: 450px;
    overflow: hidden;
  }

  .bg-settings-form {
    height: 100%;
  }

  .bg-history-wrapper {
    height: 300px;
    position: relative;
  }

  .loading-state, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--el-text-color-secondary);

    .el-icon {
      font-size: 32px;
      margin-bottom: 16px;
    }
  }

  .bg-list-container {
    height: 100%;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 10px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--el-border-color);
      border-radius: 4px;
    }
  }

  .bg-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    padding: 8px;
  }

  .bg-item {
    cursor: pointer;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    &.active {
      border: 2px solid var(--el-color-primary);
    }

    .bg-preview {
      height: 140px;
      position: relative;

      .bg-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .loading-placeholder {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
      }
    }

    .bg-info {
      padding: 8px;
      font-size: 12px;
      background: #f9f9f9;
      border-top: 1px solid #eee;
    }
  }
}

.background-settings-dialog {
  .bg-item {
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    background-color: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.25s ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
      border-color: var(--el-border-color-darker);
    }

    &.active {
      border: 2px solid var(--el-color-primary);
      box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);

      .bg-preview::after {
        content: '';
        position: absolute;
        top: 10px;
        right: 10px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: var(--el-color-primary);
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
        background-size: 14px;
        background-position: center;
        background-repeat: no-repeat;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        z-index: 2;
      }
    }

    .bg-preview {
      height: 160px;
      position: relative;
      background-color: var(--el-fill-color-light);

      .bg-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .bg-info {
      padding: 8px 12px;
      font-size: 13px;
      background-color: var(--el-bg-color);
      color: var(--el-text-color-regular);
      border-top: 1px solid var(--el-border-color-lighter);

      .bg-date {
        display: flex;
        align-items: center;
        gap: 6px;

        &::before {
          content: '';
          display: inline-block;
          width: 14px;
          height: 14px;
          opacity: 0.7;
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23909399'%3E%3Cpath d='M19 4h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V10h14v10zM9 14H7v-2h2v2zm4 0h-2v-2h2v2zm4 0h-2v-2h2v2zm-8 4H7v-2h2v2zm4 0h-2v-2h2v2zm4 0h-2v-2h2v2z'/%3E%3C/svg%3E");
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
        }
      }
    }
  }

  .bg-list-container {
    padding: 4px;

    &::-webkit-scrollbar-thumb {
      background-color: var(--el-border-color-darker);
    }
  }

  .bg-list {
    gap: 16px;
    padding: 12px;
  }
}

.font-color-picker {
  width: 100%;
  margin-top: 8px;

  :deep(.el-color-picker__trigger) {
    width: 100%;
    height: 36px;
    padding: 4px;
    border-radius: 4px;
  }

  :deep(.el-color-picker__color) {
    width: 100%;
    height: 100%;
    border-radius: 2px;
  }
}

.bg-item {
  .bg-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;

    .bg-date {
      flex: 1;
    }

    .bg-actions {
      opacity: 0;
      transition: opacity 0.2s;

      .el-button {
        padding: 4px;

        &:hover {
          color: var(--el-color-danger) !important;
        }
      }
    }
  }

  &:hover {
    .bg-actions {
      opacity: 1;
    }
  }

  &.active {
    .bg-actions {
      opacity: 1;
    }
  }
}

// TTS设置对话框 - 重构为固定大小，不可整体滚动
.tts-settings-dialog {
  z-index: 3000 !important; /* 设置合适的z-index，低于下拉框 */

  /* 禁止选择和拖拽 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  :deep(.el-dialog) {
    width: 800px !important;
    height: 600px !important;
    max-height: 600px !important;
    min-height: 600px !important;
    border-radius: 12px;
    overflow: visible !important; /* 改为visible，允许下拉框溢出 */
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(var(--el-bg-color-rgb), 0.95);
    border: 1px solid rgba(var(--el-border-color-rgb), 0.3);
    position: fixed;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    display: flex;
    flex-direction: column;

    .el-dialog__header {
      padding: 20px 24px 16px;
      background: rgba(var(--el-bg-color-rgb), 0.8);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.2);
      flex-shrink: 0;
      position: relative;
      z-index: 10;

      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: '🎤';
          font-size: 18px;
        }
      }
    }

    .el-dialog__body {
      padding: 0;
      flex: 1;
      overflow: visible; /* 允许下拉框溢出 */
      display: flex;
      flex-direction: column;
      background: transparent;
    }

    .el-dialog__headerbtn {
      top: 20px;
      right: 20px;
      width: 32px;
      height: 32px;
      border-radius: 8px;
      background: rgba(var(--el-fill-color-rgb), 0.8);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(var(--el-color-danger-rgb), 0.1);
        transform: scale(1.1);

        .el-dialog__close {
          color: var(--el-color-danger);
        }
      }

      .el-dialog__close {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-secondary);
        transition: color 0.3s ease;
      }
    }

    .el-dialog__footer {
      display: none;
    }
  }

  /* 响应式设计 - 保持固定尺寸但适应小屏幕 */
  @media (max-width: 900px) {
    :deep(.el-dialog) {
      width: 90vw !important;
      height: 80vh !important;
      max-height: 80vh !important;
      min-height: 500px !important;
    }
  }

  @media (max-width: 600px) {
    :deep(.el-dialog) {
      width: 95vw !important;
      height: 85vh !important;
      max-height: 85vh !important;
      min-height: 450px !important;
      border-radius: 8px;
    }
  }

  @media (max-width: 480px) {
    :deep(.el-dialog) {
      width: 100vw !important;
      height: 100vh !important;
      max-height: 100vh !important;
      min-height: 100vh !important;
      border-radius: 0;
      top: 0 !important;
      left: 0 !important;
      transform: none !important;
      position: fixed;
    }
  }
}

.tts-settings-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: hidden; /* 禁止垂直滚动 */
  overflow-x: visible; /* 允许水平溢出（下拉框） */

  /* 禁止文本选择 - 使用!important确保优先级 */
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;

  /* 禁止拖拽 */
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;

  /* 禁止双击选择 */
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;

  /* 禁止长按选择（移动端） */
  -webkit-touch-callout: none !important;

  // 设置区域
  .settings-section {
    flex-shrink: 0; /* 防止压缩 */
    padding: 20px 24px;
    border-bottom: 1px solid var(--el-border-color-lighter);

    /* 禁止选择和拖拽 - 使用!important确保优先级 */
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;

    &:last-child {
      border-bottom: none;
    }

    /* 禁止图片和图标拖拽 */
    img, svg, .el-icon {
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
      pointer-events: none;
    }

    /* 禁止所有文本元素选择 */
    .section-title, .section-header, .param-label, .param-name, .param-value,
    .voice-stats, .stats-text, .filter-info, .tip-title, .tip-desc,
    span, p, div, h1, h2, h3, h4, h5, h6, label {
      -webkit-user-select: none !important;
      -moz-user-select: none !important;
      -ms-user-select: none !important;
      user-select: none !important;
    }

    &.voice-select-section {
      flex: 1; /* 语音选择区域占据剩余空间 */
      display: flex;
      flex-direction: column;
      min-height: 0; /* 允许内容压缩 */
      overflow: visible; /* 允许下拉框溢出 */

      .section-content {
        flex: 1;
        overflow: visible; /* 允许下拉框溢出 */
        display: flex;
        flex-direction: column;

        &.voice-select-content {
          .voice-select-container {
            flex: 1;
            overflow: visible; /* 允许下拉框溢出 */
            display: flex;
            flex-direction: column;

            .voice-select {
              flex-shrink: 0; /* 选择框不压缩 */
              margin-bottom: 16px;
            }

            /* 状态提示区域可以滚动 */
            .status-tip {
              flex: 1;
              overflow-y: auto;
              max-height: 200px;
            }
          }
        }
      }
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      flex-shrink: 0;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        display: flex;
        align-items: center;
        gap: 8px;

        .section-icon {
          font-size: 18px;
          color: var(--el-color-primary);
        }
      }

      .voice-stats {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: var(--el-text-color-secondary);

        .stats-text {
          background: var(--el-color-primary-light-9);
          color: var(--el-color-primary);
          padding: 2px 8px;
          border-radius: 12px;
          font-weight: 500;
        }

        .filter-info {
          background: var(--el-color-success-light-9);
          color: var(--el-color-success);
          padding: 2px 8px;
          border-radius: 12px;
          font-weight: 500;
        }
      }
    }

    .section-content {
      flex-shrink: 0;
    }
  }

  // 语音过滤区域
  .voice-filter-section {
    .section-content {
      .voice-filter-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;
        padding: 16px;
        background: rgba(var(--el-bg-color-page-rgb), 0.8);
        border-radius: 12px;
        border: 1px solid rgba(var(--el-border-color-light-rgb), 0.6);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);

        .gender-filter {
          flex: 1;
          display: flex;
          gap: 0;

          .native-filter-btn {
            flex: 1;
            padding: 10px 20px;
            font-weight: 500;
            border: 1px solid rgba(var(--el-border-color-rgb), 0.3);
            background: rgba(var(--el-bg-color-rgb), 0.8);
            color: var(--el-text-color-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            outline: none;
            position: relative;

            /* 禁止文本选择 */
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;

            /* 禁止拖拽 */
            -webkit-user-drag: none;
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;

            &:hover {
              background: rgba(var(--el-color-primary-rgb), 0.1);
              border-color: rgba(var(--el-color-primary-rgb), 0.3);
            }

            &.active {
              background: var(--el-color-primary);
              border-color: var(--el-color-primary);
              color: white;
              box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.3);
            }

            &:first-child {
              border-top-left-radius: 8px;
              border-bottom-left-radius: 8px;
              border-right: none;
            }

            &:last-child {
              border-top-right-radius: 8px;
              border-bottom-right-radius: 8px;
              border-left: none;
            }

            &:not(:first-child):not(:last-child) {
              border-left: none;
              border-right: none;
            }

            &:active {
              transform: translateY(1px);
            }

            &:focus {
              outline: none;
              box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
              background: rgba(var(--el-bg-color-rgb), 0.5);
              color: var(--el-text-color-disabled);
            }
          }
        }

        .refresh-btn {
          flex-shrink: 0;
          padding: 10px 20px;
          font-weight: 500;
          border-radius: 8px;
          background: rgba(var(--el-color-primary-rgb), 0.1);
          border: 1px solid rgba(var(--el-color-primary-rgb), 0.3);
          color: var(--el-color-primary);
          transition: all 0.3s ease;

          &:hover {
            background: var(--el-color-primary);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
          }

          &:active {
            transform: translateY(0);
          }

          .btn-text {
            margin-left: 4px;
          }
        }
      }
    }
  }



  // 语音选择区域
  .voice-select-section {
    .voice-select-container {
      position: relative;
      display: flex;
      flex-direction: column;
      height: 100%;

      // 语音选择下拉框
      .voice-select {
        width: 100%;
        flex-shrink: 0;

        :deep(.el-select__wrapper) {
          min-height: 48px;
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(var(--el-border-color-rgb), 0.3);
          background: rgba(var(--el-bg-color-rgb), 0.9);
          backdrop-filter: blur(8px);
          -webkit-backdrop-filter: blur(8px);
          transition: all 0.3s ease;

          &:hover {
            border-color: rgba(var(--el-color-primary-rgb), 0.5);
            box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.1);
          }

          &.is-focused {
            border-color: var(--el-color-primary);
            box-shadow: 0 0 0 3px rgba(var(--el-color-primary-rgb), 0.2);
          }
        }

        :deep(.el-select__placeholder) {
          color: var(--el-text-color-placeholder);
          font-size: 14px;
        }

        :deep(.el-select__input) {
          font-size: 14px;
          color: var(--el-text-color-primary);
        }
      }

      // 语音选项样式
      .voice-option {
        display: flex;
        flex-direction: column;
        gap: 4px;
        padding: 8px 0;

        .voice-main {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .voice-name {
            font-weight: 500;
            color: var(--el-text-color-primary);
            font-size: 14px;
            line-height: 1.4;
          }

          .voice-tags {
            display: flex;
            gap: 6px;

            .voice-tag {
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 11px;
              font-weight: 500;
              line-height: 1.2;

              &.gender-tag {
                background: rgba(var(--el-color-primary-rgb), 0.1);
                color: var(--el-color-primary);

                &.male {
                  background: rgba(var(--el-color-info-rgb), 0.1);
                  color: var(--el-color-info);
                }

                &.female {
                  background: rgba(var(--el-color-success-rgb), 0.1);
                  color: var(--el-color-success);
                }
              }

              &.locale-tag {
                background: rgba(var(--el-color-warning-rgb), 0.1);
                color: var(--el-color-warning);
              }
            }
          }
        }
      }

      // 状态提示
      .status-tip {
        margin-top: 16px;
        padding: 16px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;
        transition: all 0.3s ease;

        &.error-tip {
          background: rgba(var(--el-color-danger-rgb), 0.1);
          border: 1px solid rgba(var(--el-color-danger-rgb), 0.3);

          .tip-icon {
            color: var(--el-color-danger);
            font-size: 20px;
          }

          .tip-content {
            flex: 1;

            .tip-title {
              font-weight: 600;
              color: var(--el-color-danger);
              font-size: 14px;
              margin-bottom: 2px;
            }

            .tip-desc {
              color: var(--el-text-color-secondary);
              font-size: 12px;
            }
          }

          .tip-action {
            flex-shrink: 0;
          }
        }

        &.empty-tip {
          background: rgba(var(--el-color-info-rgb), 0.1);
          border: 1px solid rgba(var(--el-color-info-rgb), 0.3);

          .tip-icon {
            color: var(--el-color-info);
            font-size: 20px;
          }

          .tip-content {
            flex: 1;

            .tip-title {
              font-weight: 600;
              color: var(--el-color-info);
              font-size: 14px;
              margin-bottom: 2px;
            }

            .tip-desc {
              color: var(--el-text-color-secondary);
              font-size: 12px;
            }
          }

          .tip-action {
            flex-shrink: 0;
          }
        }
      }
    }
  }

  // 语音参数区域
  .voice-params-section {
    .section-content {
      .params-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 24px;

        .param-item {
          .param-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .param-label {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 14px;
              font-weight: 500;
              color: var(--el-text-color-primary);

              .param-icon {
                font-size: 16px;
                color: var(--el-color-primary);
              }

              .param-name {
                font-weight: 600;
              }
            }

            .param-value {
              font-weight: 600;
              color: var(--el-color-primary);
              font-size: 14px;
              background: rgba(var(--el-color-primary-rgb), 0.1);
              padding: 4px 12px;
              border-radius: 12px;
              border: 1px solid rgba(var(--el-color-primary-rgb), 0.3);
            }
          }

          .param-control {
            .param-slider {
              padding: 0 8px;

              /* 禁止选择 */
              -webkit-user-select: none;
              -moz-user-select: none;
              -ms-user-select: none;
              user-select: none;

              :deep(.el-slider__runway) {
                height: 8px;
                border-radius: 4px;
                background: rgba(var(--el-border-color-light-rgb), 0.8);
                backdrop-filter: blur(4px);
                -webkit-backdrop-filter: blur(4px);
              }

              :deep(.el-slider__bar) {
                border-radius: 4px;
                background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-primary-light-3));
              }

              :deep(.el-slider__button) {
                width: 20px;
                height: 20px;
                border: 3px solid var(--el-color-primary);
                background: white;
                box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.3);
                transition: all 0.3s ease;

                &:hover {
                  transform: scale(1.1);
                  box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.4);
                }

                &:active {
                  transform: scale(1.05);
                }
              }

              :deep(.el-slider__button-wrapper) {
                &:hover .el-slider__button {
                  border-color: var(--el-color-primary-light-3);
                }
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 600px) {
    .settings-section {
      padding: 16px 20px;
    }

    .voice-filter-section {
      .section-content {
        .voice-filter-container {
          flex-direction: column;
          align-items: stretch;
          gap: 16px;
          padding: 12px;

          .gender-filter {
            display: flex;
            justify-content: center;
            gap: 8px;

            .native-filter-btn {
              flex: 1;
              padding: 8px 12px;
              font-size: 12px;
              text-align: center;
              min-width: 0;
            }
          }

          .refresh-btn {
            padding: 12px 16px;
            font-size: 14px;
            align-self: center;
            min-width: 120px;

            .btn-text {
              display: inline;
            }
          }
        }
      }
    }

    .voice-select-section {
      .voice-select-container {
        .voice-select {
          :deep(.el-select__wrapper) {
            min-height: 44px;
          }
        }

        .status-tip {
          flex-direction: column;
          text-align: center;
          gap: 12px;

          .tip-content {
            .tip-title {
              font-size: 13px;
            }

            .tip-desc {
              font-size: 11px;
            }
          }
        }
      }
    }

    .voice-params-section {
      .section-content {
        .params-grid {
          gap: 20px;

          .param-item {
            .param-header {
              .param-label {
                font-size: 13px;

                .param-name {
                  font-size: 13px;
                }
              }

              .param-value {
                font-size: 12px;
                padding: 3px 8px;
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .settings-section {
      padding: 12px 16px;

      .section-header {
        margin-bottom: 12px;

        .section-title {
          font-size: 14px;

          .section-icon {
            font-size: 16px;
          }
        }

        .voice-stats {
          font-size: 10px;

          .stats-text, .filter-info {
            padding: 1px 6px;
          }
        }
      }
    }

    .voice-filter-section {
      .section-content {
        .voice-filter-container {
          padding: 8px;
          gap: 12px;

          .refresh-btn {
            padding: 10px 12px;
            font-size: 12px;
            min-width: 100px;

            .btn-text {
              display: none; /* 小屏幕隐藏文字 */
            }
          }
        }
      }
    }

    .voice-params-section {
      .section-content {
        .params-grid {
          gap: 16px;

          .param-item {
            .param-header {
              .param-label {
                font-size: 12px;

                .param-icon {
                  font-size: 14px;
                }

                .param-name {
                  font-size: 12px;
                }
              }

              .param-value {
                font-size: 11px;
                padding: 2px 6px;
              }
            }

            .param-control {
              .param-slider {
                padding: 0 4px;

                :deep(.el-slider__runway) {
                  height: 6px;
                }

                :deep(.el-slider__button) {
                  width: 16px;
                  height: 16px;
                  border-width: 2px;
                }
              }
            }
          }
        }
      }
    }
  }
}

/* TTS语音选择下拉框样式 - 确保显示在最上层 */
.tts-voice-select-dropdown {
  z-index: 9999 !important; /* 确保在对话框之上 */
  max-height: 300px !important;
  overflow-y: auto !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;

  /* 禁止选择和拖拽 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 确保下拉框容器有足够的空间 */
  .el-select-dropdown__wrap {
    max-height: 300px !important;
  }

  .el-scrollbar__view {
    padding: 0 !important;
  }

  /* 重置Element Plus的默认样式 */
  .el-select-dropdown__list {
    padding: 0 !important;
  }

  .el-select-dropdown__item {
    padding: 12px 16px !important;
    border-bottom: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    transition: all 0.2s ease;
    min-height: 60px !important;
    height: auto !important;
    line-height: normal !important;
    display: block !important;
    position: relative !important;
    z-index: 1 !important;
    margin-bottom: 0 !important;
    box-sizing: border-box !important;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: rgba(var(--el-color-primary-rgb), 0.1) !important;
      color: var(--el-color-primary) !important;
      z-index: 2 !important;
    }

    &.selected {
      background: var(--el-color-primary) !important;
      color: white !important;
      font-weight: 600;
      z-index: 3 !important;

      .voice-tag {
        background: rgba(255, 255, 255, 0.2) !important;
        color: rgba(255, 255, 255, 0.9) !important;
      }
    }
  }

  /* 语音选项内容样式 */
  .voice-option {
    width: 100%;
    display: block;
    min-height: 36px;
    padding: 0;

    /* 禁止选择和拖拽 */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    .voice-main {
      width: 100%;
      display: block;

      .voice-name {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 6px;
        line-height: 1.4;
        display: block;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .voice-tags {
        display: flex;
        gap: 6px;
        flex-wrap: wrap;
        margin-top: 0;

        .voice-tag {
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 500;
          line-height: 1.2;
          transition: all 0.2s ease;
          display: inline-block;

          /* 禁止选择 */
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;

          &.gender-tag {
            &.male {
              background: rgba(var(--el-color-info-rgb), 0.15);
              color: var(--el-color-info);
            }

            &.female {
              background: rgba(var(--el-color-success-rgb), 0.15);
              color: var(--el-color-success);
            }
          }

          &.locale-tag {
            background: rgba(var(--el-color-warning-rgb), 0.15);
            color: var(--el-color-warning);
          }
        }
      }
    }
  }

  /* 滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(var(--el-fill-color-rgb), 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(var(--el-border-color-rgb), 0.6);
    border-radius: 4px;
    transition: background 0.3s ease;

    &:hover {
      background: rgba(var(--el-border-color-rgb), 0.8);
    }
  }

  /* 加载状态 */
  .el-select-dropdown__loading {
    padding: 20px;
    text-align: center;
    color: var(--el-text-color-secondary);
  }

  /* 空状态 */
  .el-select-dropdown__empty {
    padding: 20px;
    text-align: center;
    color: var(--el-text-color-placeholder);
    font-size: 13px;
  }
}

:deep(.el-dialog),
:deep(.el-drawer),
:deep(.el-message-box),
:deep(.el-message),
:deep(.el-popover),
:deep(.el-overlay) {
  z-index: 2500 !important;
}

.context-menu-container {
  z-index: 2500 !important;
}

/* 右键菜单配置抽屉样式 */
.context-menu-settings-drawer {
  .el-drawer__header {
    display: none; // 隐藏默认标题，使用自定义头部
  }

  .el-drawer__body {
    padding: 0;
    overflow: hidden;
    background: var(--el-bg-color);

    // 禁用网页行为，模拟原生应用
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    // 禁用右键菜单
    * {
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -khtml-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }

    // 允许输入框和文本域的文本选择
    input, textarea, .el-input__inner, .el-textarea__inner {
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
    }

    // 允许AI响应内容的文本选择
    .message-bubble,
    .message-bubble *,
    .markdown-body,
    .markdown-body *,
    .message-content,
    .message-content * {
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
    }
  }

  // 抽屉动画优化
  &.el-drawer {
    .el-drawer__container {
      backdrop-filter: blur(4px);
      -webkit-backdrop-filter: blur(4px);
    }
  }
}



.writing-stats-panel {
  background-color: var(--el-bg-color);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
  z-index: 5;
}

:deep(.ProseMirror) {

  font-family: var(--editor-font-family);
  font-size: var(--editor-font-size);
  width: var(--editor-content-width);
  margin: 0 auto;
  min-height: calc(100vh - 200px);
  outline: none;
  padding: 0 40px 40px;
  color: var(--editor-font-color, var(--el-text-color-primary));
  background-color: var(--el-bg-color-rgb);
  /* 减少文本阴影以提升滚动性能 */
  text-shadow: 0 0 2px rgba(var(--el-bg-color-rgb), 0.5);

  /* 优化编辑器滚动性能 */
  transform: translateZ(0); /* 启用硬件加速 */
  will-change: contents; /* 提示浏览器优化内容变化 */
  contain: layout style paint; /* 限制重排和重绘范围 */

  /* 优化ProseMirror编辑器滚动条性能 */
  &::-webkit-scrollbar {
    width: 20px; /* 与主编辑区域保持一致 */
    background: rgba(0, 0, 0, 0.02);
    border-radius: 10px;
    /* 优化滚动条渲染 */
    will-change: opacity;
    transform: translateZ(0);
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
      rgba(144, 147, 153, 0.6) 0%,
      rgba(144, 147, 153, 0.5) 50%,
      rgba(144, 147, 153, 0.6) 100%);
    border-radius: 10px;
    border: 3px solid transparent;
    background-clip: content-box;
    cursor: pointer;
    min-height: 40px;
    /* 优化滑块性能 */
    will-change: background-color;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      background: linear-gradient(180deg,
        rgba(144, 147, 153, 0.8) 0%,
        rgba(144, 147, 153, 0.7) 50%,
        rgba(144, 147, 153, 0.8) 100%);
      cursor: pointer;
      transform: scaleX(1.1);
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    }

    &:active {
      background: linear-gradient(180deg,
        rgba(144, 147, 153, 0.9) 0%,
        rgba(144, 147, 153, 0.8) 50%,
        rgba(144, 147, 153, 0.9) 100%);
      cursor: grabbing;
      transform: scaleX(1.05);
      transition: none; /* 拖拽时禁用过渡 */
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 10px;
    will-change: auto;
  }

  /* 隐藏滚动条按钮以减少渲染负担 */
  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
    border-radius: 10px;
  }

  /* ProseMirror响应式滚动条优化 */
  @media (max-width: 768px) {
    &::-webkit-scrollbar {
      width: 12px; /* 移动设备使用适中的滚动条 */
    }

    &::-webkit-scrollbar-thumb {
      border: 2px solid transparent;
      min-height: 30px;
    }
  }

  @media (prefers-reduced-motion: reduce) {
    /* 为偏好减少动画的用户禁用滚动条过渡 */
    &::-webkit-scrollbar-thumb {
      transition: none;
      transform: none;

      &:hover {
        transform: none;
      }

      &:active {
        transform: none;
      }
    }
  }

  p {
    margin: 0;
    padding: 0;
    line-height: var(--editor-line-height) !important;
    text-indent: 2em;

    /* 优化段落渲染性能 */
    contain: layout style; /* 限制重排和重绘范围 */

    &.is-empty {
      text-indent: 0;
    }
  }

  /* 查找和替换功能样式 - 高亮样式 */
  .find-result {
    background-color: rgba(255, 230, 0, 0.4) !important;
    border-radius: 2px !important;
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: 1px dashed rgba(255, 150, 0, 0.6) !important;
  }

  .find-result-active {
    background-color: rgba(255, 150, 0, 0.6) !important;
    border-radius: 2px !important;
    box-shadow: 0 0 0 1px rgba(255, 150, 0, 0.7) !important;
    border-bottom: 1px solid rgba(255, 100, 0, 0.8) !important;
  }
}

.book-editor.auto-spacing {
  :deep(.ProseMirror) {
    p:not(:first-child):not(:empty) {
      margin-top: 1.1em;
    }
  }
}

.fullscreen-bg-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -998;
  pointer-events: none;
}

.is-fullscreen {
  :deep(.ProseMirror) {
    background-color: rgba(var(--el-bg-color-rgb), 0.4);
    backdrop-filter: none;
  }
}

.dark-theme :deep(.ProseMirror) {
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.7);
}

.light-theme :deep(.ProseMirror) {
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.7);
}

:deep(.ProseMirror p) {
  background: linear-gradient(to right,
    rgba(var(--el-bg-color-rgb), 0.3),
    rgba(var(--el-bg-color-rgb), 0.2),
    rgba(var(--el-bg-color-rgb), 0.3)
  );
  padding: 2px 0;
  border-radius: 3px;
}

:deep(.entity-create-window),
:deep(.scene-create-window),
.entity-dialog,
.scene-create-dialog {
  z-index: 3000 !important;
}

:deep(.el-dialog),
:deep(.el-drawer),
:deep(.el-message-box),
:deep(.el-message),
:deep(.el-popover),
:deep(.el-overlay) {
  z-index: 2500 !important;
}

.entity-create-window,
.scene-create-window {
  position: relative;
  z-index: 3000 !important;
}

:deep(.el-overlay) {
  z-index: 2000 !important;
}

.global-popup-container {
  position: relative;
  z-index: 9999 !important;
}

.create-volume-dialog,
.create-chapter-dialog {
  position: fixed !important;
  z-index: 9999 !important;

  :deep(.el-dialog__wrapper) {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    margin: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.el-dialog) {
    margin: 0 auto !important;
    position: relative !important;
    top: auto !important;
    left: auto !important;
    transform: none !important;
  }
}

:deep(.el-overlay) {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9000;
  height: 100%;
  width: 100%;
  background-color: var(--el-overlay-color);
}

/* 移除了旧的 el-menu 过渡动画样式 */

/* 移除了重复的卷和章节样式定义，新样式在 chapters-tree 中统一管理 */

/* 移除了更多无用的 el-menu 样式 */

/* 保留搜索结果的滚动条样式 */
.search-results {
  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-darker);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  &:hover::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-darker);
  }
}

/* 移除了孤立的 chapter-title-container 样式，新样式在 chapters-tree 中统一管理 */

:deep(.el-message-box__input) {
  .el-input__inner {
    &:focus {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }
}

/* 聊天侧边栏相关样式 */

/* 聊天侧边栏容器样式 */
:root {
  --chat-sidebar-width: 400px; /* 默认宽度 */
}

/* 搜索面板容器样式 */
:root {
  --search-panel-width: 400px; /* 默认宽度 */
}

/* 聊天面板打开时的编辑器主区域样式：保持占据剩余空间，不与目录耦合 */
.editor-main.with-chat-sidebar {
  flex: 1 1 auto;
  /* 移除过渡，让布局立即调整，避免滚动条跳跃 */
  transition: none;
}

/* 搜索面板打开时的编辑器主区域样式 */
.editor-main.with-search-panel {
  /* 搜索面板为绝对定位覆盖层，不再挤压编辑区域宽度，避免与聊天侧栏叠加导致主体被双重收缩 */
  width: auto;
}


/* 确保按钮等交互元素保持正确的光标 */
.message-bubble button,
.message-bubble .action-button,
.message-bubble .hljs-code-actions button {
  cursor: pointer;
  user-select: none;
}

/* 快捷键提示 */
.shortcut-key {
  display: inline-block;
  padding: 2px 4px;
  font-size: 12px;
  background-color: var(--el-fill-color-light);
  border-radius: 3px;
  color: var(--el-text-color-regular);
  margin-left: 4px;
}

/* 最近使用的颜色样式 */
.color-settings {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 5px;
}

.recent-colors {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.recent-colors-title {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.recent-colors-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.recent-color-item {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #e4e7ed;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

/* 字体设置抽屉样式 */
.font-settings-drawer {
  .el-drawer__header {
    margin-bottom: 16px;
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .el-drawer__body {
    padding: 0 20px;
    overflow-y: auto;
  }

  .settings-content {
    .settings-section {
      margin-bottom: 20px;
    }

    .setting-group {
      .setting-item {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 10px;
        }

        .setting-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .setting-label {
            font-weight: 500;
            color: var(--el-text-color-primary);
          }

          .setting-value {
            font-size: 13px;
            color: var(--el-text-color-secondary);
          }
        }

        .font-select, .el-slider {
          width: 100%;
        }

        .font-color-picker {
          margin-right: 16px;
        }
      }
    }
  }

  .drawer-footer {
    position: sticky;
    bottom: 0;
    width: 100%;
    padding: 16px 20px;
    background-color: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-lighter);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    text-align: right;
    margin-top: 20px;
  }
}

/* 改进TipTap编辑器的拼写检查样式 */
.ProseMirror {
  [spellcheck="false"] {
    text-decoration: none !important;
  }

  // 隐藏浏览器默认的拼写错误下划线
  *[data-spell-error] {
    text-decoration: none !important;
  }

  // 禁用拼写检查的红色下划线
  .spell-error,
  .ProseMirror-spellerror {
    text-decoration: none !important;
    background-image: none !important;
  }
}

/* 侧边栏切换按钮 */
.sidebar-toggle-btn {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 40px;
  // background-color: var(--el-color-primary-light-8);
  border-radius: 0 4px 4px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  border: 1px solid var(--el-border-color-light);
  border-left: none;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);

  &:hover {
    background-color: var(--el-color-primary-light-5);
    color: var(--el-color-white);
  }

  .el-icon {
    font-size: 14px;
    color: var(--el-color-primary);
  }
}

/* 侧边栏折叠时的编辑器主区域：不强制宽度，交给 flex 布局，避免与聊天侧栏/搜索面板冲突 */
.editor-main.sidebar-collapsed {
  margin-left: 0;
}

/* 编辑器内容区域，添加左侧内边距以避免内容被切换按钮遮挡 */
.editor-main .editor-content {
  padding-left: 40px !important;
}

/* 折叠状态下的左侧目录 */
.chapters-sidebar.collapsed {
  width: 0;
  flex: 0 0 0; /* 折叠时不占用任何空间 */
  min-width: 0;
  padding: 0;
  border-right: none;
  opacity: 0;
  pointer-events: none;
  transform: translateX(-20px) scale(0.95); /* 添加轻微的位移和缩放效果 */

  /* 内容元素的过渡 */
  .sidebar-header,
  .chapters-tree,
  .search-results {
    opacity: 0;
    transform: translateX(-15px) scale(0.98);
    transition: opacity 0.25s ease, transform 0.25s ease;
  }
}

/* 展开状态的内容元素 */
.chapters-sidebar:not(.collapsed) {
  .sidebar-header,
  .chapters-tree,
  .search-results {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* TTS控制按钮样式 - 适配主题 */
.tts-controls {
  display: flex;
  gap: 6px;
  align-items: center;

  .tts-btn {
    position: relative;
    transition: all 0.2s ease;
    border-radius: 4px;
    font-size: 12px;
    /* 固定按钮尺寸，防止内容变化时按钮大小改变 */
    min-width: 100px !important;
    width: 100px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 8px !important;
    box-sizing: border-box !important;
    /* 使用CSS变量适配主题 */
    border: 1px solid rgba(var(--el-border-color-rgb), 0.3) !important;
    background: rgba(var(--el-bg-color-rgb), 0.6) !important;
    color: var(--el-text-color-primary) !important;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);

    /* 按钮内容布局 */
    .el-icon {
      margin-right: 4px !important;
      flex-shrink: 0 !important;
    }

    span {
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      flex: 1 !important;
      text-align: left !important;
    }

    &:hover {
      background: rgba(var(--el-bg-color-rgb), 0.8) !important;
      border-color: rgba(var(--el-border-color-rgb), 0.5) !important;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    // 播放状态样式 - 红色停止按钮，更加明显
    &.is-playing {
      background: linear-gradient(135deg, #ff4757, #ff3742) !important;
      border: 2px solid #ff4757 !important;
      color: white !important;
      box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.4), 0 4px 12px rgba(255, 71, 87, 0.3) !important;
      animation: tts-pulse 2s infinite;
      font-weight: 600;

      &:hover {
        background: linear-gradient(135deg, #ff3742, #ff2f3a) !important;
        border-color: #ff3742 !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.5), 0 0 0 3px rgba(255, 71, 87, 0.4) !important;
      }

      .el-icon {
        animation: tts-icon-pulse 1.5s infinite;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
      }

      span {
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }

    // 加载状态样式
    &.is-loading {
      background: linear-gradient(135deg, #3742fa, #2f3af2);
      border-color: #3742fa;
      color: white;

      &:hover {
        background: linear-gradient(135deg, #2f3af2, #2732eb);
        border-color: #2f3af2;
      }
    }

    // 禁用状态样式
    &.is-disabled {
      opacity: 0.4;
      cursor: not-allowed;

      &:hover {
        transform: none;
        background: rgba(var(--el-bg-color-rgb), 0.6) !important;
        border-color: rgba(var(--el-border-color-rgb), 0.3) !important;
        box-shadow: none;
      }
    }

    // 选中播放按钮 - 绿色
    &.selected-play-btn {
      &:not(.is-playing):not(.is-loading) {
        &:hover {
          background: linear-gradient(135deg, #2ed573, #26d466);
          border-color: #2ed573;
          color: white;
        }
      }
    }

    // 全文播放按钮 - 蓝色
    &.full-play-btn {
      &:not(.is-playing):not(.is-loading) {
        &:hover {
          background: linear-gradient(135deg, #1e90ff, #0080ff);
          border-color: #1e90ff;
          color: white;
        }
      }
    }

    // 图标样式
    .el-icon {
      margin-right: 3px;
      font-size: 13px;
      transition: all 0.2s ease;
    }

    // 文本样式
    span {
      font-weight: 500;
      font-size: 12px;
    }

    // 响应式设计 - 小屏幕时调整为正方形按钮
    @media (max-width: 1280px) {
      min-width: 32px !important;
      width: 32px !important;
      height: 32px !important;
      padding: 0 !important;

      .el-icon {
        margin-right: 0 !important;
      }

      span {
        display: none;
      }
    }
  }
}

// TTS播放脉冲动画 - 更加明显的效果
@keyframes tts-pulse {
  0%, 100% {
    box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.4), 0 4px 12px rgba(255, 71, 87, 0.3);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(255, 71, 87, 0.2), 0 6px 16px rgba(255, 71, 87, 0.4);
  }
}

// TTS图标脉冲动画 - 更加明显的缩放效果
@keyframes tts-icon-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.15);
    opacity: 0.9;
  }
}

/* 聊天侧边栏容器样式 */
.chat-sidebar-container {
  position: relative; /* 作为 flex 子项参与布局，不覆盖编辑区域 */
  height: calc(100% - 46px);
  width: var(--chat-sidebar-width);
  flex: 0 0 var(--chat-sidebar-width);
  z-index: 10;
  display: flex;
  flex-direction: row; /* 水平布局，支持拖拽条 */
    /* 使用 transform 实现滑入效果，避免宽度变化导致布局跳跃 */
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background: var(--el-bg-color);
  border-left: 1px solid var(--el-border-color-light);
  /* 限定最小/最大宽度以避免抖动 */
  min-width: 300px;
  max-width: 800px;
  /* 初始状态从右侧滑入 */
  transform-origin: right center;



  /* 拖拽调整宽度的分隔条 */
  .chat-resize-handle {
    width: 4px;
    background: transparent;
    cursor: col-resize;
    position: relative;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: rgba(64, 158, 255, 0.1);

      .resize-line {
        background: #409eff;
        opacity: 1;
      }
    }

    .resize-line {
      width: 2px;
      height: 40px;
      background: rgba(64, 158, 255, 0.3);
      border-radius: 1px;
      opacity: 0;
      transition: all 0.2s ease;
    }

    /* 双击重置提示 */
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: rgba(64, 158, 255, 0.2);
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    &:hover::after {
      opacity: 1;
    }
  }

  .chat-ui-wrapper {
    flex: 1;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .chat-close-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-secondary);
    transition: all 0.2s ease;
    z-index: 101;

    &:hover {
      color: var(--el-text-color-primary);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    .el-icon {
      font-size: 16px;
    }
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    --chat-sidebar-width: 350px;
  }

  @media (max-width: 768px) {
    position: fixed; /* 移动端覆盖在内容之上 */
    left: 0;
    right: 0;
    top: 50px;
    bottom: 40px;
    --chat-sidebar-width: 100vw;

    .chat-resize-handle {
      display: none; /* 移动端隐藏拖拽条 */
    }
  }
}

/* 拖拽时的全局样式 */
body.chat-resizing {
  cursor: col-resize !important;
  user-select: none !important;

  /* 拖拽过程中禁用相关区域过渡与动画，消除抖动 */
  .chat-sidebar-container,
  .editor-main,
  .editor-main.with-chat-sidebar {
    transition: none !important;
    animation: none !important;
  }

  /* 拖拽时也要禁用编辑区的 flex 过渡 */
  .editor-main.with-chat-sidebar {
    transition: none !important;
  }

  * {
    cursor: col-resize !important;
    user-select: none !important;
  }
}

/* 简单的右侧滑入动画，避免布局跳跃 */
@keyframes chat-slide-in-simple {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}





