import { defineStore } from 'pinia'
import { ref, computed, reactive } from 'vue'

export const useNovelRulesStore = defineStore('novelRules', () => {
  // 状态定义
  const state = reactive({
    rules: {},
    loaded: false,
    loading: false,
    error: null
  })

  // 计算属性
  const rules = computed(() => state.rules)
  const loaded = computed(() => state.loaded)
  const loading = computed(() => state.loading)
  const hasError = computed(() => !!state.error)
  const error = computed(() => state.error)

  // 获取规则列表（作为数组）
  const rulesList = computed(() => {
    return Object.entries(state.rules).map(([id, rule]) => ({
      id,
      ...rule
    }))
  })

  // 根据ID获取规则
  const getRuleById = computed(() => {
    return (ruleId) => state.rules[ruleId] || null
  })

  // 默认规则ID列表
  const defaultRuleIds = ['qidian', 'fanqie', 'feilu', 'ciweimao', 'qimao']

  // 判断是否为默认规则
  const isDefaultRule = (ruleId) => {
    return defaultRuleIds.includes(ruleId)
  }

  // Actions
  async function loadRules() {
    // 如果已经加载过且没有错误，直接返回
    if (state.loaded && !state.error) {
      console.log('小说规则已加载，跳过重复加载')
      return state.rules
    }

    try {
      state.loading = true
      state.error = null
      console.log('开始加载小说规则...')

      const result = await window.pywebview.api.drssion_controller.get_novel_rules()
      const response = typeof result === 'string' ? JSON.parse(result) : result

      if (response.status === 'success') {
        // 确保 rules 是对象而不是字符串
        const rules = typeof response.rules === 'string' ? JSON.parse(response.rules) : response.rules
        state.rules = rules || {}
        state.loaded = true
        console.log('小说规则加载成功', state.rules)
        return state.rules
      } else {
        throw new Error(response.message || '加载规则失败')
      }
    } catch (error) {
      console.error('加载小说规则失败:', error)
      state.error = error.message
      // 即使失败也标记为已加载，避免无限重试，但使用空对象
      state.loaded = true
      state.rules = {}
      throw error
    } finally {
      state.loading = false
    }
  }

  // 保存小说规则
  async function saveRule(rule) {
    try {
      state.loading = true
      state.error = null

      const result = await window.pywebview.api.drssion_controller.save_novel_rule(rule)
      const response = typeof result === 'string' ? JSON.parse(result) : result
      
      if (response.status === 'success') {
        // 更新本地状态
        state.rules[rule.id] = { ...rule }
        console.log('规则保存成功:', rule.id)
        return response
      } else {
        throw new Error(response.message || '保存规则失败')
      }
    } catch (error) {
      console.error('保存规则失败:', error)
      state.error = error.message
      throw error
    } finally {
      state.loading = false
    }
  }

  // 删除小说规则
  async function deleteRule(ruleId) {
    try {
      state.loading = true
      state.error = null

      const result = await window.pywebview.api.drssion_controller.delete_novel_rule(ruleId)
      const response = typeof result === 'string' ? JSON.parse(result) : result

      if (response.status === 'success') {
        // 从本地状态中删除
        delete state.rules[ruleId]
        console.log('规则删除成功:', ruleId)
        return response
      } else {
        throw new Error(response.message || '删除规则失败')
      }
    } catch (error) {
      console.error('删除规则失败:', error)
      state.error = error.message
      throw error
    } finally {
      state.loading = false
    }
  }

  // 测试小说规则
  async function testRule(data) {
    try {
      state.loading = true
      state.error = null

      const result = await window.pywebview.api.drssion_controller.test_novel_rule(data)
      const response = typeof result === 'string' ? JSON.parse(result) : result

      if (response.status === 'success') {
        console.log('规则测试成功')
        return response
      } else {
        throw new Error(response.message || '测试规则失败')
      }
    } catch (error) {
      console.error('测试规则失败:', error)
      state.error = error.message
      throw error
    } finally {
      state.loading = false
    }
  }

  // 强制重新加载规则
  async function reloadRules() {
    console.log('强制重新加载小说规则...')
    state.loaded = false
    state.rules = {}
    state.error = null
    return await loadRules()
  }

  // 重置状态
  function resetState() {
    state.rules = {}
    state.loaded = false
    state.loading = false
    state.error = null
  }

  return {
    // 状态
    state,
    
    // 计算属性
    rules,
    loaded,
    loading,
    hasError,
    error,
    rulesList,
    getRuleById,
    
    // 工具方法
    isDefaultRule,
    
    // Actions
    loadRules,
    saveRule,
    deleteRule,
    testRule,
    reloadRules,
    resetState
  }
})
