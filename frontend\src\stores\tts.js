import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useTTSStore = defineStore('tts', () => {
  // 状态
  const config = ref({
    voice: 'zh-CN-XiaoxiaoNeural',
    rate: 0,
    volume: 0,
    pitch: 0
  })
  
  const voices = ref([])
  const isLoading = ref(false)
  const error = ref(null)
  const serviceAvailable = ref(false)
  
  // 计算属性
  const currentVoice = computed(() => {
    if (!config.value.voice || !voices.value.length) {
      return null
    }
    return voices.value.find(voice => voice.ShortName === config.value.voice)
  })
  
  const currentVoiceDisplayName = computed(() => {
    if (!currentVoice.value) {
      return config.value.voice || ''
    }
    return currentVoice.value.FriendlyName || currentVoice.value.Name || currentVoice.value.ShortName
  })
  
  // 方法
  async function loadConfig() {
    try {
      isLoading.value = true
      error.value = null
      
      const result = await window.pywebview.api.get_tts_config()
      const response = typeof result === 'string' ? JSON.parse(result) : result
      
      if (response.status === 'success') {
        const data = response.data
        
        // 解析配置值（后端可能保存的是字符串格式）
        const parseConfigValue = (value, defaultValue = 0) => {
          if (typeof value === 'number') return value
          if (typeof value === 'string') {
            // 处理 "+50%" 或 "-20Hz" 这样的格式
            const numMatch = value.match(/([+-]?\d+)/)
            return numMatch ? parseInt(numMatch[1]) : defaultValue
          }
          return defaultValue
        }
        
        config.value = {
          voice: data.voice || 'zh-CN-XiaoxiaoNeural',
          rate: parseConfigValue(data.rate, 0),
          volume: parseConfigValue(data.volume, 0),
          pitch: parseConfigValue(data.pitch, 0)
        }
        
        
        return config.value
      } else {
        throw new Error(response.message || '获取TTS配置失败')
      }
    } catch (e) {
      error.value = e.message
      console.error('加载TTS配置失败:', e)
      
      // 使用默认配置
      config.value = {
        voice: 'zh-CN-XiaoxiaoNeural',
        rate: 0,
        volume: 0,
        pitch: 0
      }
      
      throw e
    } finally {
      isLoading.value = false
    }
  }
  
  async function updateConfig(key, value) {
    try {
      isLoading.value = true
      error.value = null
      

      let configValue = value
      
      // 只对数值类型的配置进行转换
      if (key !== 'voice') {
        configValue = parseInt(value) || 0
      }
      
      // 更新本地配置
      config.value[key] = configValue
      
      // 保存到后端
      const backendConfig = { [key]: configValue }
      const result = await window.pywebview.api.update_tts_config(backendConfig)
      const response = typeof result === 'string' ? JSON.parse(result) : result
      
      if (response.status === 'success') {
        
        return response
      } else {
        throw new Error(response.message || '保存TTS配置失败')
      }
    } catch (e) {
      error.value = e.message
      console.error('更新TTS配置失败:', e)
      throw e
    } finally {
      isLoading.value = false
    }
  }
  
  async function loadVoices(timeout = 3000) {
    try {
      isLoading.value = true
      error.value = null

      // 这里使用前端的edgeTTS服务获取语音列表
      // 因为后端的语音获取功能已被注释
      const edgeTTSService = (await import('@/utils/edgeTts.js')).default

      if (!edgeTTSService) {
        throw new Error('无法加载TTS服务模块')
      }

      const voicesList = await edgeTTSService.getVoices(timeout)
      voices.value = voicesList || []
      serviceAvailable.value = voicesList && voicesList.length > 0

      console.log(`语音列表加载成功，共 ${voices.value.length} 个语音`)

      // 验证当前配置的语音是否在列表中
      if (config.value.voice && voices.value.length > 0) {
        const isVoiceAvailable = voices.value.some(voice => voice.ShortName === config.value.voice)
        if (!isVoiceAvailable) {
          console.warn(`当前配置的语音 ${config.value.voice} 不在可用列表中`)
          // 可以选择使用第一个可用语音或保持当前配置
        }
      }

      return voices.value
    } catch (e) {
      error.value = e.message
      serviceAvailable.value = false
      console.error('加载语音列表失败:', e)
      throw e
    } finally {
      isLoading.value = false
    }
  }
  
  async function refreshVoices(timeout = 3000) {
    try {
      isLoading.value = true

      // 清空当前列表
      voices.value = []

      // 重新加载
      await loadVoices(timeout)

      return voices.value
    } catch (e) {
      error.value = e.message
      throw e
    }
  }
  
  function clearError() {
    error.value = null
  }
  
  // 重置配置到默认值
  function resetConfig() {
    config.value = {
      voice: 'zh-CN-XiaoxiaoNeural',
      rate: 0,
      volume: 0,
      pitch: 0
    }
  }
  
  return {
    // 状态
    config,
    voices,
    isLoading,
    error,
    serviceAvailable,
    
    // 计算属性
    currentVoice,
    currentVoiceDisplayName,
    
    // 方法
    loadConfig,
    updateConfig,
    loadVoices,
    refreshVoices,
    clearError,
    resetConfig
  }
})
