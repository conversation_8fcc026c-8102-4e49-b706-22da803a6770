/**
 * 智能滚动功能测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { SmartScroll } from '../utils/smartScroll.js'

describe('SmartScroll', () => {
  let container
  let smartScroll
  
  beforeEach(() => {
    // 创建模拟的DOM容器
    container = {
      scrollHeight: 1000,
      scrollTop: 0,
      clientHeight: 400,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    }
    
    smartScroll = new SmartScroll(container, { threshold: 100 })
  })
  
  describe('isUserNearBottom', () => {
    it('should return true when user is at the bottom', () => {
      container.scrollTop = 600 // scrollHeight(1000) - clientHeight(400) = 600
      expect(smartScroll.isUserNearBottom()).toBe(true)
    })
    
    it('should return true when user is near the bottom (within threshold)', () => {
      container.scrollTop = 550 // 50px from bottom, within 100px threshold
      expect(smartScroll.isUserNearBottom()).toBe(true)
    })
    
    it('should return false when user is far from bottom', () => {
      container.scrollTop = 200 // 400px from bottom, beyond 100px threshold
      expect(smartScroll.isUserNearBottom()).toBe(false)
    })
    
    it('should return false when container is null', () => {
      smartScroll.container = null
      expect(smartScroll.isUserNearBottom()).toBe(false)
    })
  })
  
  describe('scrollToBottom', () => {
    it('should scroll to bottom when shouldAutoScroll is true', () => {
      smartScroll.shouldAutoScroll = true
      smartScroll.scrollToBottom()
      expect(container.scrollTop).toBe(1000)
    })
    
    it('should not scroll when shouldAutoScroll is false', () => {
      smartScroll.shouldAutoScroll = false
      container.scrollTop = 200
      smartScroll.scrollToBottom()
      expect(container.scrollTop).toBe(200)
    })
    
    it('should force scroll when force parameter is true', () => {
      smartScroll.shouldAutoScroll = false
      container.scrollTop = 200
      smartScroll.scrollToBottom(true)
      expect(container.scrollTop).toBe(1000)
    })
  })
  
  describe('smartScroll', () => {
    it('should enable auto scroll when user is near bottom', () => {
      container.scrollTop = 550 // near bottom
      smartScroll.shouldAutoScroll = false
      smartScroll.smartScroll()
      expect(smartScroll.shouldAutoScroll).toBe(true)
      expect(container.scrollTop).toBe(1000)
    })
    
    it('should disable auto scroll when user is far from bottom', () => {
      container.scrollTop = 200 // far from bottom
      smartScroll.shouldAutoScroll = true
      smartScroll.smartScroll()
      expect(smartScroll.shouldAutoScroll).toBe(false)
      expect(container.scrollTop).toBe(200) // should not scroll
    })
  })
  
  describe('handleScroll', () => {
    it('should update shouldAutoScroll based on scroll position', () => {
      // User scrolls to bottom
      container.scrollTop = 600
      smartScroll.handleScroll()
      expect(smartScroll.shouldAutoScroll).toBe(true)
      
      // User scrolls up
      container.scrollTop = 200
      smartScroll.handleScroll()
      expect(smartScroll.shouldAutoScroll).toBe(false)
    })
  })
  
  describe('initialization', () => {
    it('should add scroll event listener on init', () => {
      expect(container.addEventListener).toHaveBeenCalledWith('scroll', expect.any(Function))
    })
    
    it('should start with shouldAutoScroll as true', () => {
      expect(smartScroll.shouldAutoScroll).toBe(true)
    })
  })
  
  describe('destroy', () => {
    it('should remove scroll event listener', () => {
      smartScroll.destroy()
      expect(container.removeEventListener).toHaveBeenCalledWith('scroll', expect.any(Function))
    })
  })
  
  describe('edge cases', () => {
    it('should handle container with no scroll', () => {
      container.scrollHeight = 400
      container.clientHeight = 400
      container.scrollTop = 0
      
      expect(smartScroll.isUserNearBottom()).toBe(true)
      smartScroll.smartScroll()
      expect(smartScroll.shouldAutoScroll).toBe(true)
    })
    
    it('should handle very small containers', () => {
      container.scrollHeight = 50
      container.clientHeight = 100
      container.scrollTop = 0
      
      expect(smartScroll.isUserNearBottom()).toBe(true)
    })
    
    it('should handle custom threshold', () => {
      const customSmartScroll = new SmartScroll(container, { threshold: 200 })
      container.scrollTop = 450 // 150px from bottom
      
      expect(customSmartScroll.isUserNearBottom()).toBe(true)
      
      container.scrollTop = 350 // 250px from bottom
      expect(customSmartScroll.isUserNearBottom()).toBe(false)
    })
  })
})

describe('Integration scenarios', () => {
  let container
  let smartScroll
  
  beforeEach(() => {
    container = {
      scrollHeight: 1000,
      scrollTop: 0,
      clientHeight: 400,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    }
    
    smartScroll = new SmartScroll(container)
  })
  
  it('should handle typical chat scenario', () => {
    // User starts at bottom
    container.scrollTop = 600
    expect(smartScroll.isUserNearBottom()).toBe(true)
    
    // New message arrives, should auto scroll
    smartScroll.smartScroll()
    expect(smartScroll.shouldAutoScroll).toBe(true)
    
    // User scrolls up to read history
    container.scrollTop = 200
    smartScroll.handleScroll()
    expect(smartScroll.shouldAutoScroll).toBe(false)
    
    // New message arrives, should not auto scroll
    smartScroll.smartScroll()
    expect(container.scrollTop).toBe(200) // position unchanged
    
    // User scrolls back to bottom
    container.scrollTop = 600
    smartScroll.handleScroll()
    expect(smartScroll.shouldAutoScroll).toBe(true)
    
    // Next message should auto scroll
    smartScroll.smartScroll()
    expect(container.scrollTop).toBe(1000)
  })
  
  it('should handle rapid message updates', () => {
    // User at bottom
    container.scrollTop = 600
    smartScroll.handleScroll()
    
    // Simulate rapid AI response chunks
    for (let i = 0; i < 10; i++) {
      container.scrollHeight += 50 // Content grows
      smartScroll.smartScroll()
      expect(container.scrollTop).toBe(container.scrollHeight) // Should follow
    }
  })
  
  it('should handle user sending message', () => {
    // User scrolled up
    container.scrollTop = 200
    smartScroll.handleScroll()
    expect(smartScroll.shouldAutoScroll).toBe(false)
    
    // User sends message (force scroll)
    smartScroll.scrollToBottom(true)
    expect(container.scrollTop).toBe(1000)
    expect(smartScroll.shouldAutoScroll).toBe(true)
  })
})
