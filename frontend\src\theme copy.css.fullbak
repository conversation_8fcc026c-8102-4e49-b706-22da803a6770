/* src/assets/main.css */

@font-face {
  /* 1. 定义字体名称 */
  font-family: 'MyWebAppFont'; 

  /* 2. 引用你的 WOFF2 文件路径 (确保文件名和路径正确) */
  src: url('/fonts/NotoSansSC-Regular.woff2') format('woff2');


}

/* ========================================
   企业级主题变量定义 - 明亮主题
   ======================================== */
html.light {
    /* ===== 主色调系统 ===== */
    --el-color-primary: #201dba;
    --el-color-primary-light-1: #3a37c4;
    --el-color-primary-light-2: #5451ce;
    --el-color-primary-light-3: #6e6bd8;
    --el-color-primary-light-4: #8885e2;
    --el-color-primary-light-5: #a29fec;
    --el-color-primary-light-6: #bcb9f6;
    --el-color-primary-light-7: #d6d3ff;
    --el-color-primary-light-8: #e8e6ff;
    --el-color-primary-light-9: #f4f3ff;
    --el-color-primary-dark-1: #1c1aa7;
    --el-color-primary-dark-2: #181694;

    /* ===== 功能色系统 ===== */
    /* 成功色 */
    --el-color-success: #67c23a;
    --el-color-success-light-1: #75c947;
    --el-color-success-light-2: #83d054;
    --el-color-success-light-3: #91d761;
    --el-color-success-light-4: #9fde6e;
    --el-color-success-light-5: #ade57b;
    --el-color-success-light-6: #bbec88;
    --el-color-success-light-7: #c9f395;
    --el-color-success-light-8: #d7faa2;
    --el-color-success-light-9: #e5ffaf;
    --el-color-success-dark-1: #5daf34;
    --el-color-success-dark-2: #529b2e;

    /* 警告色 */
    --el-color-warning: #e6a23c;
    --el-color-warning-light-1: #e9ab49;
    --el-color-warning-light-2: #ecb456;
    --el-color-warning-light-3: #efbd63;
    --el-color-warning-light-4: #f2c670;
    --el-color-warning-light-5: #f5cf7d;
    --el-color-warning-light-6: #f8d88a;
    --el-color-warning-light-7: #fbe197;
    --el-color-warning-light-8: #feeaa4;
    --el-color-warning-light-9: #fff3b1;
    --el-color-warning-dark-1: #cf9236;
    --el-color-warning-dark-2: #b88230;

    /* 危险色 */
    --el-color-danger: #f56c6c;
    --el-color-danger-light-1: #f67979;
    --el-color-danger-light-2: #f78686;
    --el-color-danger-light-3: #f89393;
    --el-color-danger-light-4: #f9a0a0;
    --el-color-danger-light-5: #faadad;
    --el-color-danger-light-6: #fbbaba;
    --el-color-danger-light-7: #fcc7c7;
    --el-color-danger-light-8: #fdd4d4;
    --el-color-danger-light-9: #fee1e1;
    --el-color-danger-dark-1: #dd6161;
    --el-color-danger-dark-2: #c45656;

    /* 信息色 */
    --el-color-info: #909399;
    --el-color-info-light-1: #9a9ea6;
    --el-color-info-light-2: #a4a9b3;
    --el-color-info-light-3: #aeb4c0;
    --el-color-info-light-4: #b8bfcd;
    --el-color-info-light-5: #c2cada;
    --el-color-info-light-6: #ccd5e7;
    --el-color-info-light-7: #d6e0f4;
    --el-color-info-light-8: #e0ebff;
    --el-color-info-light-9: #eaf6ff;
    --el-color-info-dark-1: #82848a;
    --el-color-info-dark-2: #73767a;

    /* ===== 背景色系统 ===== */
    --el-bg-color: #ffffff;
    --el-bg-color-page: #f2f3f5;
    --el-bg-color-overlay: #ffffff;
    --el-bg-color-light: #fafafa;
    --el-bg-color-lighter: #f5f5f5;
    --el-bg-color-extra-light: #fafcff;

    /* ===== 文字颜色系统 ===== */
    --el-text-color-primary: #303133;
    --el-text-color-regular: #606266;
    --el-text-color-secondary: #909399;
    --el-text-color-placeholder: #a8abb2;
    --el-text-color-disabled: #c0c4cc;

    /* ===== 边框颜色系统 ===== */
    --el-border-color: #dcdfe6;
    --el-border-color-light: #e4e7ed;
    --el-border-color-lighter: #ebeef5;
    --el-border-color-extra-light: #f2f6fc;
    --el-border-color-dark: #d4d7de;
    --el-border-color-darker: #cdd0d6;

    /* ===== 填充颜色系统 ===== */
    --el-fill-color: #f0f2f5;
    --el-fill-color-light: #f5f7fa;
    --el-fill-color-lighter: #fafafa;
    --el-fill-color-extra-light: #fafcff;
    --el-fill-color-dark: #ebedf0;
    --el-fill-color-darker: #e6e8eb;
    --el-fill-color-blank: #ffffff;

    /* ===== 阴影系统 ===== */
    --el-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
    --el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.12);
    --el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.12);
    --el-box-shadow-dark: 0px 16px 48px 16px rgba(0, 0, 0, 0.08), 0px 12px 32px rgba(0, 0, 0, 0.12), 0px 8px 16px -8px rgba(0, 0, 0, 0.16);

    /* ===== 遮罩颜色 ===== */
    --el-mask-color: rgba(0, 0, 0, 0.8);
    --el-mask-color-extra-light: rgba(0, 0, 0, 0.3);

    /* ===== 组件尺寸系统 ===== */
    --el-component-size-large: 40px;
    --el-component-size: 32px;
    --el-component-size-small: 24px;

    /* ===== 字体系统 ===== */
    --el-font-size-extra-large: 20px;
    --el-font-size-large: 18px;
    --el-font-size-medium: 16px;
    --el-font-size-base: 14px;
    --el-font-size-small: 13px;
    --el-font-size-extra-small: 12px;
    --el-font-weight-primary: 500;
    --el-font-weight-secondary: 400;
    --el-font-weight-bold: 600;
    --el-font-family: 'MyWebAppFont','Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    --el-font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

    /* ===== 圆角系统 ===== */
    --el-border-radius-base: 4px;
    --el-border-radius-small: 2px;
    --el-border-radius-round: 20px;
    --el-border-radius-circle: 100%;

    /* ===== 间距系统 ===== */
    --el-space-xs: 4px;
    --el-space-sm: 8px;
    --el-space-md: 12px;
    --el-space-lg: 16px;
    --el-space-xl: 20px;
    --el-space-xxl: 24px;

    /* ===== 过渡动画系统 ===== */
    --el-transition-duration: 0.3s;
    --el-transition-duration-fast: 0.2s;
    --el-transition-function-ease-in-out-bezier: cubic-bezier(0.645, 0.045, 0.355, 1);
    --el-transition-function-fast-bezier: cubic-bezier(0.23, 1, 0.32, 1);
    --el-transition-all: all var(--el-transition-duration) var(--el-transition-function-ease-in-out-bezier);
    --el-transition-fade: opacity var(--el-transition-duration) var(--el-transition-function-fast-bezier);
    --el-transition-md-fade: transform var(--el-transition-duration) var(--el-transition-function-fast-bezier), opacity var(--el-transition-duration) var(--el-transition-function-fast-bezier);
    --el-transition-fade-linear: opacity var(--el-transition-duration-fast) linear;
    --el-transition-border: border-color var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);
    --el-transition-box-shadow: box-shadow var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);
    --el-transition-color: color var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);

    /* ===== 层级系统 ===== */
    --el-index-normal: 1;
    --el-index-top: 1000;
    --el-index-popper: 2000;

    /* ===== 透明度系统 ===== */
    --el-opacity-disabled: 0.5;
    --el-opacity-hover: 0.8;
    --el-opacity-active: 0.9;

    /* ===== 业务专用颜色 ===== */
    /* 聊天气泡专用颜色 */
    --chat-bubble-user-bg: #409eff;
    --chat-bubble-user-text: #ffffff;
    --chat-bubble-assistant-bg: #f4f4f5;
    --chat-bubble-assistant-text: #303133;

    /* 编辑器专用颜色 */
    --editor-main-bg: #f0f0f2;
    --editor-sidebar-bg: #ffffff;
    --editor-toolbar-bg: #fafafa;
    --editor-border: #e4e7ed;

    /* 状态颜色 */
    --status-online: #67c23a;
    --status-offline: #909399;
    --status-busy: #e6a23c;
    --status-away: #f56c6c;

    /* 等级颜色 */
    --level-beginner: #67c23a;
    --level-intermediate: #e6a23c;
    --level-advanced: #f56c6c;
    --level-expert: #201dba;
}

/* ========================================
   企业级主题变量定义 - 暗色主题
   ======================================== */
html.dark {
    /* ===== 主色调系统 ===== */
    --el-color-primary: #4080ff;
    --el-color-primary-light-1: #5088ff;
    --el-color-primary-light-2: #6090ff;
    --el-color-primary-light-3: #7098ff;
    --el-color-primary-light-4: #80a0ff;
    --el-color-primary-light-5: #90a8ff;
    --el-color-primary-light-6: #a0b0ff;
    --el-color-primary-light-7: #b0b8ff;
    --el-color-primary-light-8: #2d4b6d;
    --el-color-primary-light-9: #1c2b3d;
    --el-color-primary-dark-1: #3a73e6;
    --el-color-primary-dark-2: #3366cc;

    /* ===== 功能色系统 ===== */
    /* 成功色 */
    --el-color-success: #67c23a;
    --el-color-success-light-1: #6bc247;
    --el-color-success-light-2: #6fc254;
    --el-color-success-light-3: #73c261;
    --el-color-success-light-4: #77c26e;
    --el-color-success-light-5: #7bc27b;
    --el-color-success-light-6: #7fc288;
    --el-color-success-light-7: #83c295;
    --el-color-success-light-8: #529a2e;
    --el-color-success-light-9: #3e7523;
    --el-color-success-dark-1: #5daf34;
    --el-color-success-dark-2: #529b2e;

    /* 警告色 */
    --el-color-warning: #e6a23c;
    --el-color-warning-light-1: #e8a849;
    --el-color-warning-light-2: #eaae56;
    --el-color-warning-light-3: #ecb463;
    --el-color-warning-light-4: #eeba70;
    --el-color-warning-light-5: #f0c07d;
    --el-color-warning-light-6: #f2c68a;
    --el-color-warning-light-7: #f4cc97;
    --el-color-warning-light-8: #b88230;
    --el-color-warning-light-9: #8a6224;
    --el-color-warning-dark-1: #cf9236;
    --el-color-warning-dark-2: #b88230;

    /* 危险色 */
    --el-color-danger: #f56c6c;
    --el-color-danger-light-1: #f67575;
    --el-color-danger-light-2: #f77e7e;
    --el-color-danger-light-3: #f88787;
    --el-color-danger-light-4: #f99090;
    --el-color-danger-light-5: #fa9999;
    --el-color-danger-light-6: #fba2a2;
    --el-color-danger-light-7: #fcabab;
    --el-color-danger-light-8: #c45656;
    --el-color-danger-light-9: #934141;
    --el-color-danger-dark-1: #dd6161;
    --el-color-danger-dark-2: #c45656;

    /* 信息色 */
    --el-color-info: #909399;
    --el-color-info-light-1: #969ba3;
    --el-color-info-light-2: #9ca3ad;
    --el-color-info-light-3: #a2abb7;
    --el-color-info-light-4: #a8b3c1;
    --el-color-info-light-5: #aebccb;
    --el-color-info-light-6: #b4c4d5;
    --el-color-info-light-7: #baccdf;
    --el-color-info-light-8: #73757a;
    --el-color-info-light-9: #56575c;
    --el-color-info-dark-1: #82848a;
    --el-color-info-dark-2: #73767a;

    /* ===== 背景色系统 ===== */
    --el-bg-color: #1a1a1a;
    --el-bg-color-page: #141414;
    --el-bg-color-overlay: #1d1e1f;
    --el-bg-color-light: #202020;
    --el-bg-color-lighter: #262626;
    --el-bg-color-extra-light: #2c2c2c;

    /* ===== 文字颜色系统 ===== */
    --el-text-color-primary: #e5eaf3;
    --el-text-color-regular: #cfd3dc;
    --el-text-color-secondary: #a3a6ad;
    --el-text-color-placeholder: #8d9095;
    --el-text-color-disabled: #6c6e72;

    /* ===== 边框颜色系统 ===== */
    --el-border-color: #4c4d4f;
    --el-border-color-light: #414243;
    --el-border-color-lighter: #363637;
    --el-border-color-extra-light: #2b2b2c;
    --el-border-color-dark: #58585b;
    --el-border-color-darker: #636466;

    /* ===== 填充颜色系统 ===== */
    --el-fill-color: #303030;
    --el-fill-color-light: #262727;
    --el-fill-color-lighter: #1d1d1d;
    --el-fill-color-extra-light: #191919;
    --el-fill-color-dark: #39393a;
    --el-fill-color-darker: #424243;
    --el-fill-color-blank: #141414;

    /* ===== 阴影系统 ===== */
    --el-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.36), 0px 8px 20px rgba(0, 0, 0, 0.72);
    --el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.72);
    --el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.72);
    --el-box-shadow-dark: 0px 16px 48px 16px rgba(0, 0, 0, 0.72), 0px 12px 32px rgba(0, 0, 0, 0.84), 0px 8px 16px -8px rgba(0, 0, 0, 0.96);

    /* ===== 遮罩颜色 ===== */
    --el-mask-color: rgba(0, 0, 0, 0.8);
    --el-mask-color-extra-light: rgba(0, 0, 0, 0.3);

    /* ===== 组件尺寸系统 ===== */
    --el-component-size-large: 40px;
    --el-component-size: 32px;
    --el-component-size-small: 24px;

    /* ===== 字体系统 ===== */
    --el-font-size-extra-large: 20px;
    --el-font-size-large: 18px;
    --el-font-size-medium: 16px;
    --el-font-size-base: 14px;
    --el-font-size-small: 13px;
    --el-font-size-extra-small: 12px;
    --el-font-weight-primary: 500;
    --el-font-weight-secondary: 400;
    --el-font-weight-bold: 600;
    --el-font-family: 'MyWebAppFont','Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    --el-font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

    /* ===== 圆角系统 ===== */
    --el-border-radius-base: 4px;
    --el-border-radius-small: 2px;
    --el-border-radius-round: 20px;
    --el-border-radius-circle: 100%;

    /* ===== 间距系统 ===== */
    --el-space-xs: 4px;
    --el-space-sm: 8px;
    --el-space-md: 12px;
    --el-space-lg: 16px;
    --el-space-xl: 20px;
    --el-space-xxl: 24px;

    /* ===== 过渡动画系统 ===== */
    --el-transition-duration: 0.3s;
    --el-transition-duration-fast: 0.2s;
    --el-transition-function-ease-in-out-bezier: cubic-bezier(0.645, 0.045, 0.355, 1);
    --el-transition-function-fast-bezier: cubic-bezier(0.23, 1, 0.32, 1);
    --el-transition-all: all var(--el-transition-duration) var(--el-transition-function-ease-in-out-bezier);
    --el-transition-fade: opacity var(--el-transition-duration) var(--el-transition-function-fast-bezier);
    --el-transition-md-fade: transform var(--el-transition-duration) var(--el-transition-function-fast-bezier), opacity var(--el-transition-duration) var(--el-transition-function-fast-bezier);
    --el-transition-fade-linear: opacity var(--el-transition-duration-fast) linear;
    --el-transition-border: border-color var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);
    --el-transition-box-shadow: box-shadow var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);
    --el-transition-color: color var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);

    /* ===== 层级系统 ===== */
    --el-index-normal: 1;
    --el-index-top: 1000;
    --el-index-popper: 2000;

    /* ===== 透明度系统 ===== */
    --el-opacity-disabled: 0.5;
    --el-opacity-hover: 0.8;
    --el-opacity-active: 0.9;

    /* ===== 菜单和悬停状态 ===== */
    --el-menu-active-bg-color: #2d4b6d;
    --el-menu-hover-bg-color: #2a2a2a;

    /* ===== 业务专用颜色 ===== */
    /* 聊天气泡专用颜色 */
    --chat-bubble-user-bg: #4080ff;
    --chat-bubble-user-text: #ffffff;
    --chat-bubble-assistant-bg: #2a2a2a;
    --chat-bubble-assistant-text: #e5eaf3;

    /* 编辑器专用颜色 */
    --editor-main-bg: #2d2d2d;
    --editor-sidebar-bg: #1a1a1a;
    --editor-toolbar-bg: #262727;
    --editor-border: #4c4d4f;

    /* 状态颜色 */
    --status-online: #67c23a;
    --status-offline: #909399;
    --status-busy: #e6a23c;
    --status-away: #f56c6c;

    /* 等级颜色 */
    --level-beginner: #67c23a;
    --level-intermediate: #e6a23c;
    --level-advanced: #f56c6c;
    --level-expert: #4080ff;

    /* 卡片特殊效果 */
    --card-shadow-color: rgba(0, 0, 0, 0.2);
    --card-hover-shadow: rgba(64, 128, 255, 0.1);

    /* 应用背景色 */
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
}

/* ========================================
   企业级扩展变量定义
   ======================================== */

/* ===== 响应式断点系统 ===== */
:root {
    --breakpoint-xs: 480px;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-xxl: 1600px;
}

/* ===== 容器最大宽度 ===== */
:root {
    --container-max-width-xs: 100%;
    --container-max-width-sm: 540px;
    --container-max-width-md: 720px;
    --container-max-width-lg: 960px;
    --container-max-width-xl: 1140px;
    --container-max-width-xxl: 1320px;
}

/* ===== 网格系统 ===== */
:root {
    --grid-columns: 24;
    --grid-gutter-width: 20px;
    --grid-gutter-width-sm: 16px;
    --grid-gutter-width-xs: 12px;
}

/* ===== 表单组件专用变量 ===== */
:root {
    --form-item-margin-bottom: 18px;
    --form-label-font-size: var(--el-font-size-base);
    --form-label-color: var(--el-text-color-regular);
    --form-input-height: var(--el-component-size);
    --form-input-height-large: var(--el-component-size-large);
    --form-input-height-small: var(--el-component-size-small);
}

/* ===== 表格组件专用变量 ===== */
:root {
    --table-header-bg: var(--el-fill-color-light);
    --table-header-text-color: var(--el-text-color-primary);
    --table-row-hover-bg: var(--el-fill-color-lighter);
    --table-border-color: var(--el-border-color-lighter);
    --table-stripe-bg: var(--el-fill-color-extra-light);
}

/* ===== 导航组件专用变量 ===== */
:root {
    --nav-height: 60px;
    --nav-height-small: 48px;
    --sidebar-width: 240px;
    --sidebar-width-collapsed: 64px;
    --breadcrumb-font-size: var(--el-font-size-small);
    --breadcrumb-separator-color: var(--el-text-color-placeholder);
}

/* ===== 卡片组件专用变量 ===== */
:root {
    --card-padding: 20px;
    --card-padding-small: 16px;
    --card-padding-large: 24px;
    --card-header-padding: 18px 20px;
    --card-border-radius: 8px;
    --card-shadow: var(--el-box-shadow-light);
    --card-shadow-hover: var(--el-box-shadow);
}

/* ===== 按钮组件专用变量 ===== */
:root {
    --button-padding-horizontal: 15px;
    --button-padding-vertical: 8px;
    --button-padding-horizontal-large: 19px;
    --button-padding-vertical-large: 12px;
    --button-padding-horizontal-small: 11px;
    --button-padding-vertical-small: 5px;
    --button-font-weight: var(--el-font-weight-primary);
}

/* ===== 消息提示专用变量 ===== */
:root {
    --message-padding: 15px 16px;
    --message-border-radius: 4px;
    --message-font-size: var(--el-font-size-base);
    --notification-width: 330px;
    --notification-padding: 14px 26px 14px 13px;
}

/* ===== 对话框专用变量 ===== */
:root {
    --dialog-padding-primary: 20px 24px 10px;
    --dialog-padding-large: 30px 40px 20px;
    --dialog-border-radius: 8px;
    --dialog-box-shadow: var(--el-box-shadow-dark);
    --drawer-padding: 30px 24px;
}

/* ===== 加载状态专用变量 ===== */
:root {
    --loading-spinner-size: 28px;
    --loading-spinner-size-large: 42px;
    --loading-spinner-size-small: 20px;
    --skeleton-color: var(--el-fill-color);
    --skeleton-to-color: var(--el-fill-color-darker);
}

/* ===== 进度条专用变量 ===== */
:root {
    --progress-border-radius: 100px;
    --progress-height: 6px;
    --progress-height-large: 8px;
    --progress-height-small: 4px;
    --progress-text-font-size: var(--el-font-size-base);
}

/* ===== 标签页专用变量 ===== */
:root {
    --tabs-header-height: 40px;
    --tabs-content-padding: 20px 0;
    --tabs-item-padding: 0 20px;
    --tabs-border-color: var(--el-border-color-light);
}

/* ===== 步骤条专用变量 ===== */
:root {
    --steps-icon-size: 24px;
    --steps-icon-size-large: 32px;
    --steps-icon-size-small: 20px;
    --steps-title-font-size: var(--el-font-size-base);
    --steps-description-font-size: var(--el-font-size-small);
}

/* ===== 时间轴专用变量 ===== */
:root {
    --timeline-node-size: 12px;
    --timeline-node-size-large: 14px;
    --timeline-node-size-small: 10px;
    --timeline-tail-width: 2px;
    --timeline-tail-color: var(--el-border-color-light);
}

/* ===== 树形控件专用变量 ===== */
:root {
    --tree-node-padding: 4px 0;
    --tree-node-content-padding: 0 8px;
    --tree-expand-icon-size: 16px;
    --tree-indent: 16px;
}

/* ===== 穿梭框专用变量 ===== */
:root {
    --transfer-panel-width: 200px;
    --transfer-panel-height: 246px;
    --transfer-panel-header-height: 40px;
    --transfer-panel-body-padding: 10px 0;
    --transfer-panel-footer-height: 40px;
}

/* ===== 上传组件专用变量 ===== */
:root {
    --upload-dragger-height: 180px;
    --upload-dragger-border: 2px dashed var(--el-border-color);
    --upload-dragger-border-hover: 2px dashed var(--el-color-primary);
    --upload-dragger-bg: var(--el-fill-color-extra-light);
    --upload-dragger-bg-hover: var(--el-color-primary-light-9);
}

/* ===== 日期选择器专用变量 ===== */
:root {
    --date-picker-width: 220px;
    --date-picker-editor-width: 220px;
    --date-picker-editor-width-range: 350px;
    --date-table-td-height: 32px;
    --date-table-td-width: 32px;
}

/* ===== 颜色选择器专用变量 ===== */
:root {
    --color-picker-size: 40px;
    --color-picker-size-large: 48px;
    --color-picker-size-small: 32px;
    --color-picker-alpha-bg-a: #ccc;
    --color-picker-alpha-bg-b: #fff;
}

/* ========================================
   企业级工具类和全局样式
   ======================================== */

/* ===== 文字工具类 ===== */
.text-primary { color: var(--el-color-primary) !important; }
.text-success { color: var(--el-color-success) !important; }
.text-warning { color: var(--el-color-warning) !important; }
.text-danger { color: var(--el-color-danger) !important; }
.text-info { color: var(--el-color-info) !important; }

.text-regular { color: var(--el-text-color-regular) !important; }
.text-secondary { color: var(--el-text-color-secondary) !important; }
.text-placeholder { color: var(--el-text-color-placeholder) !important; }
.text-disabled { color: var(--el-text-color-disabled) !important; }

.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

.text-nowrap { white-space: nowrap !important; }
.text-wrap { white-space: normal !important; }
.text-break { word-break: break-all !important; }

.text-lowercase { text-transform: lowercase !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-capitalize { text-transform: capitalize !important; }

.font-weight-light { font-weight: 300 !important; }
.font-weight-normal { font-weight: var(--el-font-weight-secondary) !important; }
.font-weight-medium { font-weight: var(--el-font-weight-primary) !important; }
.font-weight-bold { font-weight: var(--el-font-weight-bold) !important; }

.font-size-xs { font-size: var(--el-font-size-extra-small) !important; }
.font-size-sm { font-size: var(--el-font-size-small) !important; }
.font-size-base { font-size: var(--el-font-size-base) !important; }
.font-size-md { font-size: var(--el-font-size-medium) !important; }
.font-size-lg { font-size: var(--el-font-size-large) !important; }
.font-size-xl { font-size: var(--el-font-size-extra-large) !important; }

/* ===== 背景工具类 ===== */
.bg-primary { background-color: var(--el-color-primary) !important; }
.bg-success { background-color: var(--el-color-success) !important; }
.bg-warning { background-color: var(--el-color-warning) !important; }
.bg-danger { background-color: var(--el-color-danger) !important; }
.bg-info { background-color: var(--el-color-info) !important; }

.bg-light { background-color: var(--el-fill-color-light) !important; }
.bg-lighter { background-color: var(--el-fill-color-lighter) !important; }
.bg-extra-light { background-color: var(--el-fill-color-extra-light) !important; }
.bg-dark { background-color: var(--el-fill-color-dark) !important; }
.bg-darker { background-color: var(--el-fill-color-darker) !important; }

.bg-transparent { background-color: transparent !important; }
.bg-white { background-color: var(--el-bg-color) !important; }
.bg-page { background-color: var(--el-bg-color-page) !important; }

/* ===== 边框工具类 ===== */
.border { border: 1px solid var(--el-border-color) !important; }
.border-light { border: 1px solid var(--el-border-color-light) !important; }
.border-lighter { border: 1px solid var(--el-border-color-lighter) !important; }
.border-primary { border: 1px solid var(--el-color-primary) !important; }
.border-success { border: 1px solid var(--el-color-success) !important; }
.border-warning { border: 1px solid var(--el-color-warning) !important; }
.border-danger { border: 1px solid var(--el-color-danger) !important; }

.border-0 { border: 0 !important; }
.border-top-0 { border-top: 0 !important; }
.border-right-0 { border-right: 0 !important; }
.border-bottom-0 { border-bottom: 0 !important; }
.border-left-0 { border-left: 0 !important; }

.rounded { border-radius: var(--el-border-radius-base) !important; }
.rounded-sm { border-radius: var(--el-border-radius-small) !important; }
.rounded-lg { border-radius: 8px !important; }
.rounded-xl { border-radius: 12px !important; }
.rounded-circle { border-radius: var(--el-border-radius-circle) !important; }
.rounded-0 { border-radius: 0 !important; }

/* ===== 间距工具类 ===== */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--el-space-xs) !important; }
.m-2 { margin: var(--el-space-sm) !important; }
.m-3 { margin: var(--el-space-md) !important; }
.m-4 { margin: var(--el-space-lg) !important; }
.m-5 { margin: var(--el-space-xl) !important; }
.m-6 { margin: var(--el-space-xxl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--el-space-xs) !important; }
.mt-2 { margin-top: var(--el-space-sm) !important; }
.mt-3 { margin-top: var(--el-space-md) !important; }
.mt-4 { margin-top: var(--el-space-lg) !important; }
.mt-5 { margin-top: var(--el-space-xl) !important; }
.mt-6 { margin-top: var(--el-space-xxl) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: var(--el-space-xs) !important; }
.mr-2 { margin-right: var(--el-space-sm) !important; }
.mr-3 { margin-right: var(--el-space-md) !important; }
.mr-4 { margin-right: var(--el-space-lg) !important; }
.mr-5 { margin-right: var(--el-space-xl) !important; }
.mr-6 { margin-right: var(--el-space-xxl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--el-space-xs) !important; }
.mb-2 { margin-bottom: var(--el-space-sm) !important; }
.mb-3 { margin-bottom: var(--el-space-md) !important; }
.mb-4 { margin-bottom: var(--el-space-lg) !important; }
.mb-5 { margin-bottom: var(--el-space-xl) !important; }
.mb-6 { margin-bottom: var(--el-space-xxl) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: var(--el-space-xs) !important; }
.ml-2 { margin-left: var(--el-space-sm) !important; }
.ml-3 { margin-left: var(--el-space-md) !important; }
.ml-4 { margin-left: var(--el-space-lg) !important; }
.ml-5 { margin-left: var(--el-space-xl) !important; }
.ml-6 { margin-left: var(--el-space-xxl) !important; }

.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: var(--el-space-xs) !important; margin-right: var(--el-space-xs) !important; }
.mx-2 { margin-left: var(--el-space-sm) !important; margin-right: var(--el-space-sm) !important; }
.mx-3 { margin-left: var(--el-space-md) !important; margin-right: var(--el-space-md) !important; }
.mx-4 { margin-left: var(--el-space-lg) !important; margin-right: var(--el-space-lg) !important; }
.mx-5 { margin-left: var(--el-space-xl) !important; margin-right: var(--el-space-xl) !important; }
.mx-6 { margin-left: var(--el-space-xxl) !important; margin-right: var(--el-space-xxl) !important; }

.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: var(--el-space-xs) !important; margin-bottom: var(--el-space-xs) !important; }
.my-2 { margin-top: var(--el-space-sm) !important; margin-bottom: var(--el-space-sm) !important; }
.my-3 { margin-top: var(--el-space-md) !important; margin-bottom: var(--el-space-md) !important; }
.my-4 { margin-top: var(--el-space-lg) !important; margin-bottom: var(--el-space-lg) !important; }
.my-5 { margin-top: var(--el-space-xl) !important; margin-bottom: var(--el-space-xl) !important; }
.my-6 { margin-top: var(--el-space-xxl) !important; margin-bottom: var(--el-space-xxl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--el-space-xs) !important; }
.p-2 { padding: var(--el-space-sm) !important; }
.p-3 { padding: var(--el-space-md) !important; }
.p-4 { padding: var(--el-space-lg) !important; }
.p-5 { padding: var(--el-space-xl) !important; }
.p-6 { padding: var(--el-space-xxl) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--el-space-xs) !important; }
.pt-2 { padding-top: var(--el-space-sm) !important; }
.pt-3 { padding-top: var(--el-space-md) !important; }
.pt-4 { padding-top: var(--el-space-lg) !important; }
.pt-5 { padding-top: var(--el-space-xl) !important; }
.pt-6 { padding-top: var(--el-space-xxl) !important; }

.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: var(--el-space-xs) !important; }
.pr-2 { padding-right: var(--el-space-sm) !important; }
.pr-3 { padding-right: var(--el-space-md) !important; }
.pr-4 { padding-right: var(--el-space-lg) !important; }
.pr-5 { padding-right: var(--el-space-xl) !important; }
.pr-6 { padding-right: var(--el-space-xxl) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--el-space-xs) !important; }
.pb-2 { padding-bottom: var(--el-space-sm) !important; }
.pb-3 { padding-bottom: var(--el-space-md) !important; }
.pb-4 { padding-bottom: var(--el-space-lg) !important; }
.pb-5 { padding-bottom: var(--el-space-xl) !important; }
.pb-6 { padding-bottom: var(--el-space-xxl) !important; }

.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: var(--el-space-xs) !important; }
.pl-2 { padding-left: var(--el-space-sm) !important; }
.pl-3 { padding-left: var(--el-space-md) !important; }
.pl-4 { padding-left: var(--el-space-lg) !important; }
.pl-5 { padding-left: var(--el-space-xl) !important; }
.pl-6 { padding-left: var(--el-space-xxl) !important; }

.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: var(--el-space-xs) !important; padding-right: var(--el-space-xs) !important; }
.px-2 { padding-left: var(--el-space-sm) !important; padding-right: var(--el-space-sm) !important; }
.px-3 { padding-left: var(--el-space-md) !important; padding-right: var(--el-space-md) !important; }
.px-4 { padding-left: var(--el-space-lg) !important; padding-right: var(--el-space-lg) !important; }
.px-5 { padding-left: var(--el-space-xl) !important; padding-right: var(--el-space-xl) !important; }
.px-6 { padding-left: var(--el-space-xxl) !important; padding-right: var(--el-space-xxl) !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: var(--el-space-xs) !important; padding-bottom: var(--el-space-xs) !important; }
.py-2 { padding-top: var(--el-space-sm) !important; padding-bottom: var(--el-space-sm) !important; }
.py-3 { padding-top: var(--el-space-md) !important; padding-bottom: var(--el-space-md) !important; }
.py-4 { padding-top: var(--el-space-lg) !important; padding-bottom: var(--el-space-lg) !important; }
.py-5 { padding-top: var(--el-space-xl) !important; padding-bottom: var(--el-space-xl) !important; }
.py-6 { padding-top: var(--el-space-xxl) !important; padding-bottom: var(--el-space-xxl) !important; }

/* ===== 显示工具类 ===== */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-table { display: table !important; }
.d-table-row { display: table-row !important; }
.d-table-cell { display: table-cell !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* ===== Flexbox 工具类 ===== */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }
.align-content-end { align-content: flex-end !important; }
.align-content-center { align-content: center !important; }
.align-content-between { align-content: space-between !important; }
.align-content-around { align-content: space-around !important; }
.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }
.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

.flex-fill { flex: 1 1 auto !important; }
.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

/* ===== 位置工具类 ===== */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

.top-0 { top: 0 !important; }
.top-50 { top: 50% !important; }
.top-100 { top: 100% !important; }
.bottom-0 { bottom: 0 !important; }
.bottom-50 { bottom: 50% !important; }
.bottom-100 { bottom: 100% !important; }
.start-0 { left: 0 !important; }
.start-50 { left: 50% !important; }
.start-100 { left: 100% !important; }
.end-0 { right: 0 !important; }
.end-50 { right: 50% !important; }
.end-100 { right: 100% !important; }

/* ===== 宽度和高度工具类 ===== */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

.mw-100 { max-width: 100% !important; }
.mh-100 { max-height: 100% !important; }
.min-vw-100 { min-width: 100vw !important; }
.min-vh-100 { min-height: 100vh !important; }
.vw-100 { width: 100vw !important; }
.vh-100 { height: 100vh !important; }

/* ===== 溢出工具类 ===== */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }
.overflow-x-auto { overflow-x: auto !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-x-visible { overflow-x: visible !important; }
.overflow-x-scroll { overflow-x: scroll !important; }
.overflow-y-auto { overflow-y: auto !important; }
.overflow-y-hidden { overflow-y: hidden !important; }
.overflow-y-visible { overflow-y: visible !important; }
.overflow-y-scroll { overflow-y: scroll !important; }

/* ===== 阴影工具类 ===== */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--el-box-shadow-lighter) !important; }
.shadow { box-shadow: var(--el-box-shadow-light) !important; }
.shadow-lg { box-shadow: var(--el-box-shadow) !important; }
.shadow-xl { box-shadow: var(--el-box-shadow-dark) !important; }

/* ===== 透明度工具类 ===== */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* ===== 层级工具类 ===== */
.z-index-0 { z-index: 0 !important; }
.z-index-1 { z-index: 1 !important; }
.z-index-2 { z-index: 2 !important; }
.z-index-3 { z-index: 3 !important; }
.z-index-auto { z-index: auto !important; }
.z-index-top { z-index: var(--el-index-top) !important; }
.z-index-popper { z-index: var(--el-index-popper) !important; }

/* ===== 用户选择工具类 ===== */
.user-select-all { user-select: all !important; }
.user-select-auto { user-select: auto !important; }
.user-select-none { user-select: none !important; }

/* ===== 指针事件工具类 ===== */
.pe-none { pointer-events: none !important; }
.pe-auto { pointer-events: auto !important; }

/* ===== 可见性工具类 ===== */
.visible { visibility: visible !important; }
.invisible { visibility: hidden !important; }

/* ===== 游标工具类 ===== */
.cursor-pointer { cursor: pointer !important; }
.cursor-default { cursor: default !important; }
.cursor-not-allowed { cursor: not-allowed !important; }
.cursor-wait { cursor: wait !important; }
.cursor-text { cursor: text !important; }
.cursor-move { cursor: move !important; }
.cursor-help { cursor: help !important; }

/* ===== 动画工具类 ===== */
.transition-all { transition: var(--el-transition-all) !important; }
.transition-fade { transition: var(--el-transition-fade) !important; }
.transition-none { transition: none !important; }

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-ping {
    animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: translateY(0);
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes ping {
    75%, 100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* ===== 响应式工具类 ===== */
@media (max-width: 575.98px) {
    .d-xs-none { display: none !important; }
    .d-xs-inline { display: inline !important; }
    .d-xs-inline-block { display: inline-block !important; }
    .d-xs-block { display: block !important; }
    .d-xs-flex { display: flex !important; }
    .d-xs-inline-flex { display: inline-flex !important; }

    .text-xs-left { text-align: left !important; }
    .text-xs-center { text-align: center !important; }
    .text-xs-right { text-align: right !important; }

    .flex-xs-row { flex-direction: row !important; }
    .flex-xs-column { flex-direction: column !important; }
    .justify-content-xs-start { justify-content: flex-start !important; }
    .justify-content-xs-center { justify-content: center !important; }
    .justify-content-xs-end { justify-content: flex-end !important; }
    .justify-content-xs-between { justify-content: space-between !important; }
}

@media (min-width: 576px) and (max-width: 767.98px) {
    .d-sm-none { display: none !important; }
    .d-sm-inline { display: inline !important; }
    .d-sm-inline-block { display: inline-block !important; }
    .d-sm-block { display: block !important; }
    .d-sm-flex { display: flex !important; }
    .d-sm-inline-flex { display: inline-flex !important; }

    .text-sm-left { text-align: left !important; }
    .text-sm-center { text-align: center !important; }
    .text-sm-right { text-align: right !important; }

    .flex-sm-row { flex-direction: row !important; }
    .flex-sm-column { flex-direction: column !important; }
    .justify-content-sm-start { justify-content: flex-start !important; }
    .justify-content-sm-center { justify-content: center !important; }
    .justify-content-sm-end { justify-content: flex-end !important; }
    .justify-content-sm-between { justify-content: space-between !important; }
}

@media (min-width: 768px) and (max-width: 991.98px) {
    .d-md-none { display: none !important; }
    .d-md-inline { display: inline !important; }
    .d-md-inline-block { display: inline-block !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }
    .d-md-inline-flex { display: inline-flex !important; }

    .text-md-left { text-align: left !important; }
    .text-md-center { text-align: center !important; }
    .text-md-right { text-align: right !important; }

    .flex-md-row { flex-direction: row !important; }
    .flex-md-column { flex-direction: column !important; }
    .justify-content-md-start { justify-content: flex-start !important; }
    .justify-content-md-center { justify-content: center !important; }
    .justify-content-md-end { justify-content: flex-end !important; }
    .justify-content-md-between { justify-content: space-between !important; }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
    .d-lg-none { display: none !important; }
    .d-lg-inline { display: inline !important; }
    .d-lg-inline-block { display: inline-block !important; }
    .d-lg-block { display: block !important; }
    .d-lg-flex { display: flex !important; }
    .d-lg-inline-flex { display: inline-flex !important; }

    .text-lg-left { text-align: left !important; }
    .text-lg-center { text-align: center !important; }
    .text-lg-right { text-align: right !important; }

    .flex-lg-row { flex-direction: row !important; }
    .flex-lg-column { flex-direction: column !important; }
    .justify-content-lg-start { justify-content: flex-start !important; }
    .justify-content-lg-center { justify-content: center !important; }
    .justify-content-lg-end { justify-content: flex-end !important; }
    .justify-content-lg-between { justify-content: space-between !important; }
}

@media (min-width: 1200px) {
    .d-xl-none { display: none !important; }
    .d-xl-inline { display: inline !important; }
    .d-xl-inline-block { display: inline-block !important; }
    .d-xl-block { display: block !important; }
    .d-xl-flex { display: flex !important; }
    .d-xl-inline-flex { display: inline-flex !important; }

    .text-xl-left { text-align: left !important; }
    .text-xl-center { text-align: center !important; }
    .text-xl-right { text-align: right !important; }

    .flex-xl-row { flex-direction: row !important; }
    .flex-xl-column { flex-direction: column !important; }
    .justify-content-xl-start { justify-content: flex-start !important; }
    .justify-content-xl-center { justify-content: center !important; }
    .justify-content-xl-end { justify-content: flex-end !important; }
    .justify-content-xl-between { justify-content: space-between !important; }
}

/* ===== 打印样式 ===== */
@media print {
    .d-print-none { display: none !important; }
    .d-print-inline { display: inline !important; }
    .d-print-inline-block { display: inline-block !important; }
    .d-print-block { display: block !important; }
    .d-print-flex { display: flex !important; }
    .d-print-inline-flex { display: inline-flex !important; }
}

/* ===== 全局样式重置和优化 ===== */
* {
    box-sizing: border-box;
}

html {
    font-family: var(--el-font-family);
    font-size: var(--el-font-size-base);
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

body {
    margin: 0;
    padding: 0;
    /* background-color: var(--el-bg-color-page); */
    /*color: var(--el-text-color-primary);*/
    transition: background-color var(--el-transition-duration), color var(--el-transition-duration);
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--el-fill-color-lighter);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--el-border-color-dark);
    border-radius: 4px;
    transition: background var(--el-transition-duration);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--el-border-color-darker);
}

/* Firefox 滚动条样式 */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--el-border-color-dark) var(--el-fill-color-lighter);
}

/* 选择文本样式 */
::selection {
    background-color: var(--el-color-primary-light-8);
    color: var(--el-color-primary);
}

::-moz-selection {
    background-color: var(--el-color-primary-light-8);
    color: var(--el-color-primary);
}

/* 焦点样式优化 */
:focus-visible {
    outline: 2px solid var(--el-color-primary);
    outline-offset: 2px;
}

/* 禁用状态样式 */
[disabled], .is-disabled {
    opacity: var(--el-opacity-disabled);
    cursor: not-allowed;
}

/* 加载状态样式 */
.is-loading {
    position: relative;
    pointer-events: none;
}

.is-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--el-mask-color-extra-light);
    z-index: var(--el-index-top);
}