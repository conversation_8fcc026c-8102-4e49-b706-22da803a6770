// API测试工具
export const testAPI = {
  // 测试Git检测
  async testGitCheck() {
    try {
      console.log('测试Git检测API...')
      const response = await window.pywebview.api.check_git_installation()
      const result = typeof response === 'string' ? JSON.parse(response) : response
      console.log('Git检测结果:', result)
      return result
    } catch (error) {
      console.error('Git检测失败:', error)
      return { status: 'error', message: error.message }
    }
  },

  // 测试备份历史获取
  async testBackupHistory(targetDir = '') {
    try {
      console.log('测试备份历史获取API...')
      const response = await window.pywebview.api.get_backup_history({
        target_dir: targetDir
      })
      const result = typeof response === 'string' ? JSON.parse(response) : response
      console.log('备份历史结果:', result)
      return result
    } catch (error) {
      console.error('获取备份历史失败:', error)
      return { status: 'error', message: error.message }
    }
  },

  // 测试目录选择
  async testDirectorySelect() {
    try {
      console.log('测试目录选择API...')
      const response = await window.pywebview.api.select_directory()
      const result = typeof response === 'string' ? JSON.parse(response) : response
      console.log('目录选择结果:', result)
      return result
    } catch (error) {
      console.error('目录选择失败:', error)
      return { status: 'error', message: error.message }
    }
  }
}

// 在开发环境下暴露到全局
if (process.env.NODE_ENV === 'development') {
  window.testAPI = testAPI
}
