import { ElMessage, ElNotification } from 'element-plus'

class EdgeTTSService {
  constructor() {
    this.isPlaying = false
    this.audioContext = null
    this.currentAudio = null
    this.audioQueue = []
    this.voices = []
    this.voicesLoaded = false
    this.ws = null
    this.abortController = null
    this.config = {
      voice: 'zh-CN-XiaoxiaoNeural',
      rate: 0,
      volume: 0,
      pitch: 0
    }

    // Edge TTS 常量 - 基于 Python 版本
    this.TRUSTED_CLIENT_TOKEN = '6A5AA1D4EAFF4E9FB37E23D68491D6F4'
    this.BASE_URL = 'speech.platform.bing.com/consumer/speech/synthesize/readaloud'
    this.VOICES_URL = `https://${this.BASE_URL}/voices/list?trustedclienttoken=${this.TRUSTED_CLIENT_TOKEN}`
    this.WSS_URL = `wss://${this.BASE_URL}/edge/v1`
    this.CHROMIUM_FULL_VERSION = '130.0.2849.68'
    this.CHROMIUM_MAJOR_VERSION = this.CHROMIUM_FULL_VERSION.split('.')[0]
    this.SEC_MS_GEC_VERSION = `1-${this.CHROMIUM_FULL_VERSION}`

    // 时钟偏移校正
    this.clockSkewSeconds = 0.0

    // WebSocket 状态管理
    this.wsState = {
      connecting: false,
      connected: false,
      closing: false,
      closed: true
    }

    // 初始化音频上下文
    this.initAudioContext()
  }

  // 初始化音频上下文
  initAudioContext() {
    try {
      // 检查浏览器支持
      const AudioContextClass = window.AudioContext || window.webkitAudioContext
      if (!AudioContextClass) {
        throw new Error('浏览器不支持Web Audio API')
      }

      this.audioContext = new AudioContextClass()

    } catch (error) {
      console.error('初始化音频上下文失败:', error)
      this.audioContext = null
      throw error
    }
  }

  // 生成连接 ID（无连字符的 UUID）
  connectId() {
    return 'xxxxxxxx-xxxx-xxxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    }).replace(/-/g, '')
  }

  // 获取 Unix 时间戳（带时钟偏移校正）
  getUnixTimestamp() {
    return Date.now() / 1000 + this.clockSkewSeconds
  }

  // 生成 Sec-MS-GEC 令牌
  async generateSecMsGec() {
    const WIN_EPOCH = 11644473600
    const S_TO_NS = 1e9

    // 获取当前时间戳并应用时钟偏移校正
    let ticks = this.getUnixTimestamp()

    // 切换到 Windows 文件时间纪元
    ticks += WIN_EPOCH

    // 向下舍入到最近的 5 分钟（300 秒）
    ticks -= ticks % 300

    // 转换为 100 纳秒间隔（Windows 文件时间格式）
    ticks *= S_TO_NS / 100

    // 创建要哈希的字符串
    const strToHash = `${Math.floor(ticks)}${this.TRUSTED_CLIENT_TOKEN}`

    // 计算 SHA256 哈希并返回大写的十六进制摘要
    const hash = await this.sha256(strToHash)
    return hash.toUpperCase()
  }

  // 简单的 SHA256 实现（用于浏览器环境）
  async sha256(message) {
    const msgBuffer = new TextEncoder().encode(message)
    const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  // JavaScript 风格的日期字符串
  dateToString() {
    return new Date().toUTCString().replace('GMT', 'GMT+0000 (Coordinated Universal Time)')
  }

  // 获取可用的语音列表
  async getVoices(timeout = 3000) {
    if (this.voicesLoaded && this.voices.length > 0) {
      return this.voices
    }

    try {
      // 创建 AbortController 用于超时控制
      const controller = new AbortController()
      const timeoutId = setTimeout(() => {
        controller.abort()
      }, timeout)

      const response = await fetch(this.VOICES_URL, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0',
          'Accept': '*/*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate, br',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        signal: controller.signal
      })

      // 清除超时定时器
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (!Array.isArray(data)) {
        throw new Error('语音列表格式错误')
      }

      this.voices = data.map(voice => {
        // 清理不需要的字段
        const cleanedVoice = { ...voice }
        delete cleanedVoice.VoiceTag
        delete cleanedVoice.SuggestedCodec
        delete cleanedVoice.Status
        return cleanedVoice
      })

      this.voicesLoaded = true
      return this.voices
    } catch (error) {
      console.error('获取语音列表失败:', error)
      this.voicesLoaded = false
      this.voices = []

      // 处理不同类型的错误
      let errorMessage = '获取语音列表失败'
      if (error.name === 'AbortError') {
        errorMessage = `请求超时（${timeout/1000}秒），请检查网络连接`
      } else if (error.message.includes('HTTP')) {
        errorMessage = `网络错误: ${error.message}`
      } else if (error.message.includes('Failed to fetch')) {
        errorMessage = '网络连接失败，请检查网络设置'
      } else {
        errorMessage = `获取语音列表失败: ${error.message}`
      }

      // 只在有通知组件时显示通知
      if (typeof this.showNotification === 'function') {
        this.showNotification({
          title: 'TTS服务提示',
          message: errorMessage,
          type: 'warning',
          duration: 4000
        })
      }
      throw new Error(errorMessage)
    }
  }

  // 格式化音调参数
  formatPitch(pitch) {
    const numPitch = typeof pitch === 'number' ? pitch : parseInt(pitch) || 0
    const clampedPitch = Math.max(-100, Math.min(100, numPitch))
    return clampedPitch >= 0 ? `+${clampedPitch}Hz` : `${clampedPitch}Hz`
  }

  // 格式化语速参数
  formatRate(rate) {
    const numRate = typeof rate === 'number' ? rate : parseInt(rate) || 0
    const clampedRate = Math.max(-100, Math.min(100, numRate))
    return clampedRate >= 0 ? `+${clampedRate}%` : `${clampedRate}%`
  }

  // 格式化音量参数
  formatVolume(volume) {
    const numVolume = typeof volume === 'number' ? volume : parseInt(volume) || 0
    const clampedVolume = Math.max(-100, Math.min(100, numVolume))
    return clampedVolume >= 0 ? `+${clampedVolume}%` : `${clampedVolume}%`
  }

  // 移除不兼容字符
  removeIncompatibleCharacters(text) {
    const chars = Array.from(text)
    for (let i = 0; i < chars.length; i++) {
      const code = chars[i].charCodeAt(0)
      if ((code >= 0 && code <= 8) || (code >= 11 && code <= 12) || (code >= 14 && code <= 31)) {
        chars[i] = ' '
      }
    }
    return chars.join('')
  }

  // 转义 XML 特殊字符
  escapeXml(text) {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;')
  }

  // 生成 SSML（基于 Python 版本）
  mkssml(voice, rate, volume, pitch, escapedText) {
    return `<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='en-US'>` +
           `<voice name='${voice}'>` +
           `<prosody pitch='${pitch}' rate='${rate}' volume='${volume}'>` +
           `${escapedText}` +
           `</prosody>` +
           `</voice>` +
           `</speak>`
  }

  // 生成 SSML 头部和数据
  ssmlHeadersPlusData(requestId, timestamp, ssml) {
    return `X-RequestId:${requestId}\r\n` +
           `Content-Type:application/ssml+xml\r\n` +
           `X-Timestamp:${timestamp}Z\r\n` +
           `Path:ssml\r\n\r\n` +
           `${ssml}`
  }

  // 更新配置
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
  }

  // 获取当前配置
  getConfig() {
    return { ...this.config }
  }

  // 文本转语音 - 非流式版本
  async speak(text, options = {}) {
    if (!text || !text.trim()) {
      throw new Error('文本不能为空')
    }

    // 确保音频上下文已初始化
    if (!this.audioContext) {
      this.initAudioContext()
    }

    if (!this.audioContext) {
      throw new Error('音频上下文初始化失败，请检查浏览器支持')
    }

    // 如果正在播放，先停止
    if (this.isPlaying) {
      this.stop()
    }

    // 创建 AbortController 用于取消操作
    this.abortController = new AbortController()

    try {
      const config = { ...this.config, ...options }
      const voice = config.voice || this.config.voice
      const rate = this.formatRate(config.rate !== undefined ? config.rate : this.config.rate)
      const volume = this.formatVolume(config.volume !== undefined ? config.volume : this.config.volume)
      const pitch = this.formatPitch(config.pitch !== undefined ? config.pitch : this.config.pitch)

      this.isPlaying = true
      this.audioQueue = []

      // 清理和转义文本
      const cleanedText = this.removeIncompatibleCharacters(text)
      const escapedText = this.escapeXml(cleanedText)

      // 建立 WebSocket 连接并获取音频数据
      const audioData = await this._synthesizeAudio(voice, rate, volume, pitch, escapedText)

      // 检查是否被取消
      if (this.abortController && this.abortController.signal.aborted) {
        throw new Error('操作被取消')
      }

      // 播放音频
      await this.playAudioBuffer(audioData)

      this.isPlaying = false
      return { status: 'success', message: '播放完成' }

    } catch (error) {
      this.isPlaying = false
      console.error('TTS播放失败:', error)
      throw new Error('TTS播放失败: ' + error.message)
    } finally {
      this.abortController = null
    }
  }

  // 内部方法：合成音频数据
  async _synthesizeAudio(voice, rate, volume, pitch, escapedText) {
    return new Promise(async (resolve, reject) => {
      let localWs = null
      let localAudioQueue = []
      let turnEndReceived = false
      let audioDataComplete = false

      try {
        const connectionId = this.connectId()
        const secMsGec = await this.generateSecMsGec()

        // 构建 WebSocket URL
        const wsUrl = `${this.WSS_URL}?TrustedClientToken=${this.TRUSTED_CLIENT_TOKEN}&Sec-MS-GEC=${secMsGec}&Sec-MS-GEC-Version=${this.SEC_MS_GEC_VERSION}&ConnectionId=${connectionId}`

        // 使用局部变量而不是实例变量
        localWs = new WebSocket(wsUrl)

        const timeout = setTimeout(() => {
          if (localWs && localWs.readyState !== WebSocket.CLOSED) {
            localWs.close()
          }
          reject(new Error('WebSocket connection timeout'))
        }, 30000)

        localWs.onopen = () => {
          clearTimeout(timeout)

          try {
            // 发送配置消息
            const timestamp = this.dateToString()
            const configMessage = `X-Timestamp:${timestamp}\r\n` +
                                 `Content-Type:application/json; charset=utf-8\r\n` +
                                 `Path:speech.config\r\n\r\n` +
                                 `{"context":{"synthesis":{"audio":{"metadataoptions":{"sentenceBoundaryEnabled":"false","wordBoundaryEnabled":"true"},"outputFormat":"audio-24khz-48kbitrate-mono-mp3"}}}}\r\n`
            localWs.send(configMessage)

            // 生成并发送 SSML
            const ssml = this.mkssml(voice, rate, volume, pitch, escapedText)
            const ssmlMessage = this.ssmlHeadersPlusData(connectionId, timestamp, ssml)
            localWs.send(ssmlMessage)
          } catch (error) {
            console.error('发送消息失败:', error)
            reject(error)
          }
        }

        localWs.onmessage = async (event) => {
          try {
            // 检查是否被取消
            if (this.abortController && this.abortController.signal.aborted) {
              localWs.close()
              return
            }

            if (event.data instanceof Blob) {
              // 处理二进制消息（音频数据）
              const arrayBuffer = await event.data.arrayBuffer()

              // 消息太短，无法包含头部长度
              if (arrayBuffer.byteLength < 2) {
                return
              }

              // 前两个字节包含头部长度
              const headerLength = new DataView(arrayBuffer).getUint16(0, false) // big endian
              if (headerLength > arrayBuffer.byteLength) {
                return
              }

              // 解析头部和数据
              const headerData = arrayBuffer.slice(2, headerLength + 2)
              const audioData = arrayBuffer.slice(headerLength + 2)

              // 解析头部
              const headerText = new TextDecoder().decode(headerData)
              const headers = {}
              for (const line of headerText.split('\r\n')) {
                const colonIndex = line.indexOf(':')
                if (colonIndex > 0) {
                  const key = line.substring(0, colonIndex)
                  const value = line.substring(colonIndex + 1)
                  headers[key] = value
                }
              }

              // 检查是否是音频路径
              if (headers['Path'] !== 'audio') {
                return
              }

              // 检查内容类型
              const contentType = headers['Content-Type']
              if (contentType && contentType !== 'audio/mpeg') {
                return
              }

              // 如果有音频数据，添加到本地队列
              if (audioData.byteLength > 0) {
                localAudioQueue.push(audioData)
              } else {
                // 空音频块通常表示音频流结束
                audioDataComplete = true

                // 如果已经收到 turn.end 且音频数据完成，则处理结果
                if (turnEndReceived) {
                  setTimeout(() => {
                    if (localAudioQueue.length > 0) {
                      // 合并所有音频数据
                      const totalLength = localAudioQueue.reduce((sum, buffer) => sum + buffer.byteLength, 0)
                      const combinedBuffer = new ArrayBuffer(totalLength)
                      const combinedView = new Uint8Array(combinedBuffer)

                      let offset = 0
                      for (const buffer of localAudioQueue) {
                        combinedView.set(new Uint8Array(buffer), offset)
                        offset += buffer.byteLength
                      }

                      localWs.close()
                      resolve(combinedBuffer)
                    } else {
                      localWs.close()
                      reject(new Error('没有收到音频数据'))
                    }
                  }, 100) // 短暂延迟确保所有数据都已处理
                }
              }

            } else if (typeof event.data === 'string') {
              // 处理文本消息
              const headerEndIndex = event.data.indexOf('\r\n\r\n')

              if (headerEndIndex === -1) {
                return
              }

              // 解析头部
              const headerText = event.data.substring(0, headerEndIndex)
              const headers = {}
              for (const line of headerText.split('\r\n')) {
                const colonIndex = line.indexOf(':')
                if (colonIndex > 0) {
                  const key = line.substring(0, colonIndex)
                  const value = line.substring(colonIndex + 1)
                  headers[key] = value
                }
              }

              const path = headers['Path']

              if (path === 'turn.end') {
                // 标记 turn.end 已收到，但不立即关闭连接
                turnEndReceived = true

                // 如果音频数据也已完成，则处理结果
                if (audioDataComplete) {
                  setTimeout(() => {
                    if (localAudioQueue.length > 0) {
                      // 合并所有音频数据
                      const totalLength = localAudioQueue.reduce((sum, buffer) => sum + buffer.byteLength, 0)
                      const combinedBuffer = new ArrayBuffer(totalLength)
                      const combinedView = new Uint8Array(combinedBuffer)

                      let offset = 0
                      for (const buffer of localAudioQueue) {
                        combinedView.set(new Uint8Array(buffer), offset)
                        offset += buffer.byteLength
                      }

                      localWs.close()
                      resolve(combinedBuffer)
                    } else {
                      localWs.close()
                      reject(new Error('没有收到音频数据'))
                    }
                  }, 100) // 短暂延迟确保所有数据都已处理
                }
              } else if (path === 'audio.metadata') {
                // 忽略元数据
              } else if (path === 'response') {
                // 忽略响应消息
              } else if (path === 'turn.start') {
                // 忽略开始消息
              }
            }
          } catch (error) {
            console.error('处理 WebSocket 消息失败:', error)
            reject(error)
          }
        }

        localWs.onerror = (error) => {
          clearTimeout(timeout)
          console.error('WebSocket 错误:', error)
          reject(new Error('WebSocket connection failed'))
        }

        localWs.onclose = (event) => {
          clearTimeout(timeout)

          // 如果连接异常关闭且还没有处理音频数据，则报错
          if (event.code !== 1000 && !turnEndReceived && localAudioQueue.length === 0) {
            reject(new Error(`WebSocket 异常关闭: ${event.code} ${event.reason}`))
          }
        }

      } catch (error) {
        console.error('建立 WebSocket 连接失败:', error)
        reject(error)
      }
    })
  }

  // 流式播放（真正的流式处理）
  async streamSpeak(text, options = {}) {
    if (!text || !text.trim()) {
      throw new Error('文本不能为空')
    }

    // 确保音频上下文已初始化
    if (!this.audioContext) {
      this.initAudioContext()
    }

    if (!this.audioContext) {
      throw new Error('音频上下文初始化失败，请检查浏览器支持')
    }

    // 如果正在播放，先停止
    if (this.isPlaying) {
      this.stop()
    }

    // 创建 AbortController 用于取消操作
    this.abortController = new AbortController()

    try {
      const config = { ...this.config, ...options }
      const voice = config.voice || this.config.voice
      const rate = this.formatRate(config.rate !== undefined ? config.rate : this.config.rate)
      const volume = this.formatVolume(config.volume !== undefined ? config.volume : this.config.volume)
      const pitch = this.formatPitch(config.pitch !== undefined ? config.pitch : this.config.pitch)

      this.isPlaying = true

      // 将长文本分割成较小的块（使用简单的句子分割）
      const chunks = this.splitText(text, 500) // 每块最多500字符，更适合中文

      if (chunks.length === 0) {
        throw new Error('文本分割后为空')
      }

      // 真正的流式播放：并发生成和播放
      const audioQueue = [] // 音频缓冲队列
      let currentPlayingIndex = 0
      let generationComplete = false
      let playbackComplete = false

      // 音频生成器（并发生成所有音频块）
      const generateAudio = async () => {
        for (let i = 0; i < chunks.length; i++) {
          if ((this.abortController && this.abortController.signal.aborted) || !this.isPlaying) {
            break
          }

          const chunk = chunks[i]
          if (!chunk || !chunk.trim()) {
            audioQueue[i] = null // 占位
            continue
          }

          try {
            // 清理和转义文本
            const cleanedText = this.removeIncompatibleCharacters(chunk)
            const escapedText = this.escapeXml(cleanedText)

            // 合成音频
            const audioData = await this._synthesizeAudio(voice, rate, volume, pitch, escapedText)

            // 将音频数据放入队列
            audioQueue[i] = audioData

          } catch (error) {
            console.error(`第 ${i + 1} 块生成失败:`, error)
            audioQueue[i] = null // 标记为失败
          }
        }
        generationComplete = true
      }

      // 音频播放器（按顺序播放队列中的音频）
      const playAudio = async () => {
        while (currentPlayingIndex < chunks.length) {
          if ((this.abortController && this.abortController.signal.aborted) || !this.isPlaying) {
            break
          }

          // 等待当前音频块生成完成
          while (audioQueue[currentPlayingIndex] === undefined && !generationComplete) {
            await new Promise(resolve => setTimeout(resolve, 50)) // 等待50ms
          }

          const audioData = audioQueue[currentPlayingIndex]
          if (audioData) {
            try {
              await this.playAudioBuffer(audioData)
            } catch (error) {
              console.error(`第 ${currentPlayingIndex + 1} 块播放失败:`, error)
            }
          }

          currentPlayingIndex++

          // 短暂间隔，避免音频间的突兀感
          if (currentPlayingIndex < chunks.length && this.isPlaying) {
            await new Promise(resolve => setTimeout(resolve, 50))
          }
        }
        playbackComplete = true
      }

      // 并发执行生成和播放
      await Promise.all([
        generateAudio(),
        playAudio()
      ])

      this.isPlaying = false
      return { status: 'success', message: '流式播放完成' }

    } catch (error) {
      this.isPlaying = false
      console.error('流式TTS播放失败:', error)
      throw new Error('流式TTS播放失败: ' + error.message)
    } finally {
      this.abortController = null
    }
  }

  // 播放音频缓冲区
  async playAudioBuffer(buffer) {
    // 确保音频上下文已初始化
    if (!this.audioContext) {
      this.initAudioContext()
    }

    if (!this.audioContext) {
      throw new Error('音频上下文初始化失败')
    }

    try {
      // 如果音频上下文被暂停，恢复它
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }

      // 处理不同类型的音频数据
      let arrayBuffer
      if (buffer instanceof ArrayBuffer) {
        arrayBuffer = buffer
      } else if (buffer instanceof Buffer) {
        arrayBuffer = buffer.buffer.slice(buffer.byteOffset, buffer.byteOffset + buffer.byteLength)
      } else if (buffer instanceof Uint8Array) {
        arrayBuffer = buffer.buffer.slice(buffer.byteOffset, buffer.byteOffset + buffer.byteLength)
      } else {
        throw new Error('不支持的音频数据格式')
      }

      // 检查音频数据是否有效
      if (arrayBuffer.byteLength === 0) {
        throw new Error('音频数据为空')
      }

      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer.slice())

      // 检查是否被取消
      if (this.abortController && this.abortController.signal.aborted) {
        throw new Error('操作被取消')
      }

      const source = this.audioContext.createBufferSource()
      source.buffer = audioBuffer
      source.connect(this.audioContext.destination)

      this.currentAudio = source

      return new Promise((resolve, reject) => {
        let isResolved = false

        const cleanup = () => {
          if (this.currentAudio === source) {
            this.currentAudio = null
          }
        }

        source.onended = () => {
          if (!isResolved) {
            isResolved = true
            cleanup()
            resolve()
          }
        }

        source.onerror = (error) => {
          if (!isResolved) {
            isResolved = true
            console.error('音频播放出错:', error)
            cleanup()
            reject(new Error('音频播放出错: ' + (error.message || 'Unknown error')))
          }
        }

        // 添加超时保护
        const timeout = setTimeout(() => {
          if (!isResolved) {
            isResolved = true
            try {
              source.stop()
            } catch (e) {
              // 忽略停止时的错误
            }
            cleanup()
            reject(new Error('音频播放超时'))
          }
        }, (audioBuffer.duration + 5) * 1000) // 音频时长 + 5秒缓冲

        try {
          source.start(0)
        } catch (error) {
          clearTimeout(timeout)
          if (!isResolved) {
            isResolved = true
            cleanup()
            reject(new Error('启动音频播放失败: ' + error.message))
          }
        }

        // 清理超时
        source.addEventListener('ended', () => clearTimeout(timeout))
        source.addEventListener('error', () => clearTimeout(timeout))
      })
    } catch (error) {
      console.error('音频播放失败:', error)
      throw new Error('音频播放失败: ' + error.message)
    }
  }

  // 停止播放
  stop() {
    this.isPlaying = false

    // 取消当前操作
    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }

    // 关闭 WebSocket 连接
    if (this.ws && !this.wsState.closed) {
      try {
        this.wsState.closing = true
        this.ws.close(1000, 'User stopped')
        this.ws = null
      } catch (error) {
        console.error('关闭 WebSocket 失败:', error)
      }
    }

    // 重置 WebSocket 状态
    this.wsState = { connecting: false, connected: false, closing: false, closed: true }

    // 停止当前音频
    if (this.currentAudio) {
      try {
        this.currentAudio.stop()
        this.currentAudio = null
      } catch (error) {
        console.error('停止音频播放失败:', error)
      }
    }

    // 清空音频队列
    this.audioQueue = []

    return { status: 'success', message: '播放已停止' }
  }

  // 按字节长度分割文本（基于 Python 版本的逻辑）
  splitTextByByteLength(text, maxByteLength = 1000) {
    if (!text || !text.trim()) {
      return []
    }

    const chunks = []
    let textBytes = new TextEncoder().encode(text)

    while (textBytes.length > maxByteLength) {
      // 寻找最后一个换行符或空格
      let splitAt = this._findLastNewlineOrSpaceWithinLimit(textBytes, maxByteLength)

      if (splitAt < 0) {
        // 没有找到换行符或空格，寻找安全的 UTF-8 分割点
        splitAt = this._findSafeUtf8SplitPoint(textBytes.slice(0, maxByteLength))
      }

      // 调整分割点以避免切断 XML 实体
      splitAt = this._adjustSplitPointForXmlEntity(textBytes, splitAt)

      if (splitAt <= 0) {
        // 如果分割点无效，强制分割
        splitAt = Math.min(maxByteLength, textBytes.length)
      }

      // 提取块
      const chunkBytes = textBytes.slice(0, splitAt)
      const chunkText = new TextDecoder().decode(chunkBytes).trim()

      if (chunkText) {
        chunks.push(chunkText)
      }

      // 准备下一次迭代
      textBytes = textBytes.slice(splitAt)
    }

    // 添加剩余部分
    if (textBytes.length > 0) {
      const remainingText = new TextDecoder().decode(textBytes).trim()
      if (remainingText) {
        chunks.push(remainingText)
      }
    }

    return chunks
  }

  // 辅助方法：在限制范围内查找最后一个换行符或空格
  _findLastNewlineOrSpaceWithinLimit(textBytes, limit) {
    // 优先查找换行符
    let splitAt = -1
    for (let i = limit - 1; i >= 0; i--) {
      if (textBytes[i] === 0x0A) { // \n
        splitAt = i
        break
      }
    }

    // 如果没有找到换行符，查找空格
    if (splitAt < 0) {
      for (let i = limit - 1; i >= 0; i--) {
        if (textBytes[i] === 0x20) { // space
          splitAt = i
          break
        }
      }
    }

    return splitAt
  }

  // 辅助方法：查找安全的 UTF-8 分割点
  _findSafeUtf8SplitPoint(textSegment) {
    let splitAt = textSegment.length
    while (splitAt > 0) {
      try {
        new TextDecoder().decode(textSegment.slice(0, splitAt))
        return splitAt
      } catch (error) {
        splitAt--
      }
    }
    return splitAt
  }

  // 辅助方法：调整分割点以避免切断 XML 实体
  _adjustSplitPointForXmlEntity(textBytes, splitAt) {
    const textStr = new TextDecoder().decode(textBytes.slice(0, splitAt))
    const lastAmpersand = textStr.lastIndexOf('&')

    if (lastAmpersand >= 0) {
      const afterAmpersand = textStr.slice(lastAmpersand)
      if (!afterAmpersand.includes(';')) {
        // 未终止的实体，移动分割点到 & 之前
        const ampersandBytes = new TextEncoder().encode(textStr.slice(0, lastAmpersand))
        return ampersandBytes.length
      }
    }

    return splitAt
  }

  // 简单的文本分割方法（备用）
  splitText(text, maxLength = 200) {
    if (!text || !text.trim()) {
      return []
    }

    // 更精确的句子分割，保留原始标点符号
    const sentences = text.split(/([。！？；\n])/).filter(s => s.trim())
    const chunks = []
    let currentChunk = ''

    for (let i = 0; i < sentences.length; i += 2) {
      const sentence = sentences[i] || ''
      const punctuation = sentences[i + 1] || ''
      const fullSentence = sentence + punctuation

      if (currentChunk.length + fullSentence.length <= maxLength) {
        currentChunk += fullSentence
      } else {
        if (currentChunk.trim()) {
          chunks.push(currentChunk.trim())
        }
        currentChunk = fullSentence
      }
    }

    // 确保最后一个块被添加
    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim())
    }

    // 如果没有分割出任何块，返回原始文本
    if (chunks.length === 0 && text.trim()) {
      chunks.push(text.trim())
    }

    return chunks
  }

  // 显示通知
  showNotification(options) {
    try {
      if (typeof ElNotification !== 'undefined') {
        ElNotification({
          ...options,
          position: 'bottom-right'
        })
      } else {
        // 如果 ElNotification 不可用，使用 console 输出
        console.warn(`TTS通知: ${options.title} - ${options.message}`)
      }
    } catch (error) {
      console.warn(`显示通知失败: ${options.title} - ${options.message}`, error)
    }
  }

  // 检查服务是否可用
  async isServiceAvailable() {
    try {
      await this.getVoices()
      return this.voices.length > 0
    } catch (error) {
      return false
    }
  }


  // 检查是否正在播放
  isCurrentlyPlaying() {
    return this.isPlaying
  }

  // 检查 WebSocket 连接状态
  getConnectionState() {
    return {
      ...this.wsState,
      isPlaying: this.isPlaying
    }
  }

  // 清理资源
  destroy() {
    this.stop()

    if (this.audioContext && this.audioContext.state !== 'closed') {
      try {
        this.audioContext.close()
        this.audioContext = null
      } catch (error) {
        console.error('关闭音频上下文失败:', error)
      }
    }

    // 清理资源
    this.ws = null
    this.abortController = null
    this.voices = []
    this.voicesLoaded = false
    this.wsState = { connecting: false, connected: false, closing: false, closed: true }
  }
}

// 创建单例实例
const edgeTTSService = new EdgeTTSService()

export default edgeTTSService
