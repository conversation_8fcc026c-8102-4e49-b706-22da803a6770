/**
 * 智能滚动工具类
 * 提供智能滚动功能，只有当用户在底部附近时才自动滚动
 */

export class SmartScroll {
  constructor(container, options = {}) {
    this.container = container
    this.threshold = options.threshold || 100 // 距离底部多少像素内认为是在底部附近
    this.shouldAutoScroll = true
    this.callbacks = {
      onScrollStateChange: options.onScrollStateChange || (() => {})
    }
    
    this.init()
  }
  
  init() {
    if (this.container) {
      this.container.addEventListener('scroll', this.handleScroll.bind(this))
    }
  }
  
  /**
   * 检查用户是否在底部附近
   */
  isUserNearBottom() {
    if (!this.container) return false
    const { scrollHeight, scrollTop, clientHeight } = this.container
    return scrollHeight - scrollTop - clientHeight <= this.threshold
  }
  
  /**
   * 处理滚动事件
   */
  handleScroll() {
    const wasAutoScrolling = this.shouldAutoScroll
    this.shouldAutoScroll = this.isUserNearBottom()
    
    // 如果状态发生变化，触发回调
    if (wasAutoScrolling !== this.shouldAutoScroll) {
      this.callbacks.onScrollStateChange(this.shouldAutoScroll)
    }
  }
  
  /**
   * 滚动到底部
   * @param {boolean} force - 是否强制滚动，忽略当前状态
   */
  scrollToBottom(force = false) {
    if (!this.container) return
    
    if (force || this.shouldAutoScroll) {
      this.container.scrollTop = this.container.scrollHeight
      this.shouldAutoScroll = true
    }
  }
  
  /**
   * 智能滚动 - 只有在用户接近底部时才自动滚动
   */
  smartScroll() {
    if (this.isUserNearBottom()) {
      this.shouldAutoScroll = true
      this.scrollToBottom()
    } else {
      this.shouldAutoScroll = false
    }
  }
  
  /**
   * 获取当前是否应该自动滚动
   */
  getShouldAutoScroll() {
    return this.shouldAutoScroll
  }
  
  /**
   * 设置是否应该自动滚动
   */
  setShouldAutoScroll(value) {
    this.shouldAutoScroll = value
  }
  
  /**
   * 销毁实例，移除事件监听器
   */
  destroy() {
    if (this.container) {
      this.container.removeEventListener('scroll', this.handleScroll.bind(this))
    }
  }
}

/**
 * Vue 3 组合式 API 的智能滚动 Hook
 */
export function useSmartScroll(containerRef, options = {}) {
  const { ref, onMounted, onUnmounted, nextTick } = require('vue')
  
  const shouldAutoScroll = ref(true)
  const smartScrollInstance = ref(null)
  
  const isUserNearBottom = () => {
    if (!containerRef.value) return false
    const container = containerRef.value
    const threshold = options.threshold || 100
    return container.scrollHeight - container.scrollTop - container.clientHeight <= threshold
  }
  
  const handleScroll = () => {
    if (!containerRef.value) return
    shouldAutoScroll.value = isUserNearBottom()
  }
  
  const scrollToBottom = (force = false) => {
    nextTick(() => {
      if (containerRef.value && (force || shouldAutoScroll.value)) {
        containerRef.value.scrollTop = containerRef.value.scrollHeight
        if (force) shouldAutoScroll.value = true
      }
    })
  }
  
  const smartScroll = () => {
    if (isUserNearBottom()) {
      shouldAutoScroll.value = true
      scrollToBottom()
    } else {
      shouldAutoScroll.value = false
    }
  }
  
  onMounted(() => {
    if (containerRef.value) {
      containerRef.value.addEventListener('scroll', handleScroll)
    }
  })
  
  onUnmounted(() => {
    if (containerRef.value) {
      containerRef.value.removeEventListener('scroll', handleScroll)
    }
  })
  
  return {
    shouldAutoScroll,
    scrollToBottom,
    smartScroll,
    isUserNearBottom
  }
}
