<template>
  <div class="smart-scroll-demo">
    <h1>智能滚动演示</h1>
    <p>这个演示展示了智能滚动功能：</p>
    <ul>
      <li>当用户在底部附近时，新消息会自动滚动到底部</li>
      <li>当用户滚动到上方查看历史消息时，新消息不会强制滚动</li>
      <li>当用户不在底部时，会显示"滚动到底部"按钮</li>
    </ul>
    
    <div class="demo-container">
      <div class="messages-container" ref="messagesContainer" @scroll="handleScroll">
        <div v-for="(message, index) in messages" :key="index" class="message">
          <div class="message-content" :class="{ 'user': message.isUser, 'ai': !message.isUser }">
            {{ message.content }}
          </div>
          <div class="message-time">{{ formatTime(message.timestamp) }}</div>
        </div>
        
        <!-- 滚动到底部按钮 -->
        <div v-if="!shouldAutoScroll" class="scroll-to-bottom-btn" @click="scrollToBottom(true)">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="7 13 12 18 17 13"></polyline>
            <polyline points="7 6 12 11 17 6"></polyline>
          </svg>
          <span>滚动到底部</span>
        </div>
      </div>
      
      <div class="controls">
        <button @click="addMessage(false)" :disabled="isSimulating">添加AI消息</button>
        <button @click="addMessage(true)" :disabled="isSimulating">添加用户消息</button>
        <button @click="simulateConversation" :disabled="isSimulating">模拟对话</button>
        <button @click="clearMessages">清空消息</button>
        <div class="status">
          <span :class="{ 'auto-scroll': shouldAutoScroll, 'manual-scroll': !shouldAutoScroll }">
            {{ shouldAutoScroll ? '自动滚动：开启' : '自动滚动：关闭' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted } from 'vue'

const messagesContainer = ref(null)
const messages = ref([])
const shouldAutoScroll = ref(true)
const isSimulating = ref(false)

// 智能滚动逻辑
const isUserNearBottom = () => {
  if (!messagesContainer.value) return false
  const container = messagesContainer.value
  const threshold = 100
  return container.scrollHeight - container.scrollTop - container.clientHeight <= threshold
}

const handleScroll = () => {
  if (!messagesContainer.value) return
  shouldAutoScroll.value = isUserNearBottom()
}

const scrollToBottom = (force = false) => {
  nextTick(() => {
    if (messagesContainer.value && (force || shouldAutoScroll.value)) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
      if (force) shouldAutoScroll.value = true
    }
  })
}

const smartScroll = () => {
  if (isUserNearBottom()) {
    shouldAutoScroll.value = true
    scrollToBottom()
  } else {
    shouldAutoScroll.value = false
  }
}

// 消息管理
const addMessage = (isUser = false) => {
  const messageCount = messages.value.length + 1
  const content = isUser 
    ? `用户消息 ${messageCount}：这是一条用户发送的消息。`
    : `AI消息 ${messageCount}：这是一条AI回复的消息，可能会比较长，用来测试滚动效果。AI正在思考如何回答用户的问题，并提供有用的信息。`
  
  messages.value.push({
    content,
    isUser,
    timestamp: Date.now()
  })
  
  // 如果是用户消息，强制滚动到底部
  if (isUser) {
    shouldAutoScroll.value = true
    scrollToBottom(true)
  } else {
    // AI消息使用智能滚动
    nextTick(smartScroll)
  }
}

const clearMessages = () => {
  messages.value = []
  shouldAutoScroll.value = true
}

const simulateConversation = async () => {
  isSimulating.value = true
  
  // 添加用户消息
  addMessage(true)
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 模拟AI流式回复
  const aiMessage = {
    content: '',
    isUser: false,
    timestamp: Date.now()
  }
  messages.value.push(aiMessage)
  
  const fullResponse = "这是一条模拟的AI流式回复。我会逐字逐句地显示这条消息，就像真实的AI对话一样。这样可以测试在流式输出过程中的滚动行为。当用户在底部时，会自动跟随滚动；当用户在上方查看历史消息时，不会被强制拉到底部。"
  
  for (let i = 0; i < fullResponse.length; i++) {
    aiMessage.content += fullResponse[i]
    nextTick(smartScroll)
    await new Promise(resolve => setTimeout(resolve, 50))
  }
  
  isSimulating.value = false
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 初始化一些示例消息
onMounted(() => {
  messages.value = [
    { content: "欢迎使用智能滚动演示！", isUser: false, timestamp: Date.now() - 5000 },
    { content: "这个功能很棒！", isUser: true, timestamp: Date.now() - 4000 },
    { content: "是的，它可以让用户在查看历史消息时不被新消息打断。", isUser: false, timestamp: Date.now() - 3000 },
    { content: "让我试试滚动到上面看看", isUser: true, timestamp: Date.now() - 2000 },
    { content: "当你滚动到上面时，新消息不会强制你回到底部。", isUser: false, timestamp: Date.now() - 1000 }
  ]
  
  nextTick(() => {
    scrollToBottom(true)
  })
})
</script>

<style lang="scss" scoped>
.smart-scroll-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  
  h1 {
    color: var(--el-color-primary);
    margin-bottom: 16px;
  }
  
  p, ul {
    margin-bottom: 16px;
    color: var(--el-text-color-regular);
  }
  
  li {
    margin-bottom: 8px;
  }
}

.demo-container {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
  background: var(--el-bg-color);
}

.messages-container {
  height: 400px;
  overflow-y: auto;
  padding: 16px;
  position: relative;
  background: var(--el-bg-color-page);
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 3px;
  }
}

.message {
  margin-bottom: 16px;
  
  .message-content {
    padding: 12px 16px;
    border-radius: 12px;
    max-width: 80%;
    word-wrap: break-word;
    
    &.user {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
      margin-left: auto;
      text-align: right;
    }
    
    &.ai {
      background: var(--el-bg-color);
      color: var(--el-text-color-primary);
      border: 1px solid var(--el-border-color-light);
    }
  }
  
  .message-time {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
    text-align: right;
    
    .message-content.ai + & {
      text-align: left;
    }
  }
}

.scroll-to-bottom-btn {
  position: absolute;
  bottom: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--el-color-primary);
  color: white;
  border-radius: 20px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: 500;
  z-index: 10;
  
  &:hover {
    background: var(--el-color-primary-dark-2);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
  
  svg {
    animation: bounce 2s infinite;
  }
  
  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-3px); }
    60% { transform: translateY(-1px); }
  }
}

.controls {
  padding: 16px;
  border-top: 1px solid var(--el-border-color-light);
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
  
  button {
    padding: 8px 16px;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    background: var(--el-bg-color);
    color: var(--el-text-color-primary);
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover:not(:disabled) {
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  .status {
    margin-left: auto;
    
    span {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      
      &.auto-scroll {
        background: var(--el-color-success-light-9);
        color: var(--el-color-success);
      }
      
      &.manual-scroll {
        background: var(--el-color-warning-light-9);
        color: var(--el-color-warning);
      }
    }
  }
}
</style>
