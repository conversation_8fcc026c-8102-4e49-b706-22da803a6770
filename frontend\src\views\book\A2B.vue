<template>
  <div class="a2b-container">
    <div class="app-header">
      <div class="header-top">
        <div class="header-title">
          <div class="title-main">
            <h2>剧情无痕设计</h2>
            <div class="title-subtitle">A2B 升级优化设计工具</div>
          </div>
          <el-popover
            placement="bottom-start"
            :width="380"
            trigger="click"
            popper-class="history-design-popover"
          >
            <template #reference>
              <span class="design-info">
                {{ currentConfig.name || '未命名设计' }}
                <el-icon class="selector-icon"><ArrowDown /></el-icon>
              </span>
            </template>
            
            <div class="design-history-panel">

              
              <div class="panel-header">
                <span class="panel-title">历史设计</span>
                <el-button type="primary" size="small" plain @click="createNewDesign">
                  <el-icon><Plus /></el-icon> 新建设计
                </el-button>
              </div>
              
              <div class="history-search">
                <el-input 
                  v-model="designSearchQuery" 
                  placeholder="搜索设计..." 
                  prefix-icon="Search"
                  clearable
                />
              </div>
              
              <div class="history-list-container">
                <div v-if="filteredDesigns.length === 0" class="empty-history">
                  <el-empty description="暂无历史设计" :image-size="60" />
                </div>
                
                <div v-else class="history-list">
                  <div 
                    v-for="item in filteredDesigns" 
                    :key="item.id" 
                    class="history-item"
                    :class="{'active': item.id === currentConfigId}"
                    @click="loadHistoryDesign(item.id)"
                  >
                    <div class="item-content">
                      <div class="item-title">{{ item.name || '未命名设计' }}</div>
                      <div class="item-meta">
                        <div class="item-timestamp">
                          <el-icon><Calendar /></el-icon>
                          <span>{{ formatDate(item.timestamp) }}</span>
                        </div>
                        <div class="item-info">
                          <el-tag size="small" effect="plain">
                            {{ item.guidelines?.length || 0 }} 个铺垫
                          </el-tag>
                        </div>
                      </div>
                    </div>
                    <div class="item-actions">
                      <el-button 
                        type="danger" 
                        size="small" 
                        circle 
                        plain
                        @click.stop="confirmDeleteDesign(item.id)"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-popover>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="createNewDesign" size="default" class="action-btn">
            <el-icon><Plus /></el-icon> 新建
          </el-button>
          <el-button type="primary" @click="saveSceneDesign(true)" size="default" class="action-btn">
            <el-icon><Download /></el-icon> 保存
          </el-button>
          <el-button type="warning" @click="exchangeABData" size="default" class="action-btn">
            <el-icon><Switch /></el-icon> 交换剧情
          </el-button>
          <el-button type="success" @click="copySceneContent" size="default" class="action-btn">
            <el-icon><CopyDocument /></el-icon> 复制
          </el-button>
          <el-dropdown trigger="click" @command="handleExtraActions">
            <el-button type="info" size="default" class="action-btn">
              <el-icon><More /></el-icon> 更多
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="copyJson">
                  <el-icon><Document /></el-icon> 复制设计JSON
                </el-dropdown-item>
                <el-dropdown-item command="importDesign">
                  <el-icon><Upload /></el-icon> 导入设计JSON
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-popover
            placement="bottom-start"
            :width="380"
            trigger="click"
            popper-class="config-manager-popover"
          >
            <template #reference>
              <el-button type="info" size="default" class="action-btn">
                <el-icon><Setting /></el-icon> 模板
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
            </template>
            
            <div class="config-manager-panel">
              <div class="panel-header">
                <span class="panel-title">模板管理</span>
                <div class="panel-actions">
                  <el-button type="primary" size="small" plain @click="createNewConfig">
                    <el-icon><Plus /></el-icon> 新建模板
                  </el-button>
                  <el-button type="success" size="small" plain @click="importConfigData">
                    <el-icon><Document /></el-icon> 导入模板
                  </el-button>
                </div>
              </div>
              
              <div class="config-search">
                <el-input 
                  v-model="configSearchQuery" 
                  placeholder="搜索模板..." 
                  prefix-icon="Search"
                  clearable
                />
              </div>
              
              <div class="config-list-container">
                <div v-if="filteredConfigs.length === 0" class="empty-config">
                  <el-empty description="暂无模板" :image-size="60" />
                </div>
                
                <div v-else class="config-list">
                  <div 
                    v-for="config in filteredConfigs" 
                    :key="config.id" 
                    class="config-item"
                    :class="{'active': currentConfig.id === config.id}"
                    @click="selectConfig(config.id)"
                  >
                    <div class="item-content">
                      <div class="item-title">{{ config.name || '未命名模板' }}</div>
                      <div class="item-meta">
                        <div class="item-description">{{ config.description || '无描述' }}</div>
                        <div class="item-info">
                          <el-tag size="small" effect="plain">
                            {{ Object.keys(config.bridgeA || {}).length }} 个字段
                          </el-tag>
                        </div>
                      </div>
                    </div>
                    <div class="item-actions">
                      <el-button 
                        type="success" 
                        size="small" 
                        circle 
                        plain
                        @click.stop="selectConfigForNewDesign(config.id)"
                        title="基于此模板创建新设计"
                      >
                        <el-icon><Plus /></el-icon>
                      </el-button>
                      <el-button 
                        type="primary" 
                        size="small" 
                        circle 
                        plain
                        @click.stop="openConfigEditor(config.id)"
                        title="编辑模板"
                      >
                        <el-icon><Edit /></el-icon>
                      </el-button>
                      <el-button 
                        type="info" 
                        size="small" 
                        circle 
                        plain
                        @click.stop="exportConfigData(config.id)"
                        title="导出模板"
                      >
                        <el-icon><Download /></el-icon>
                      </el-button>
                      <el-button 
                        type="danger" 
                        size="small" 
                        circle 
                        plain
                        @click.stop="confirmDeleteConfig(config.id)"
                        title="删除模板"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-popover>
          <el-button v-if="hasUnsavedChanges" type="danger" size="default" plain class="unsaved-indicator">
            <el-icon><Edit /></el-icon> 未保存
          </el-button>
        </div>
      </div>
    </div>

    <div class="app-content" v-loading="isLoading">
      <!-- 工作流程指引 -->
      <div class="workflow-guide" v-if="!drawerVisible">
        <div class="guide-content">
          <div class="guide-step">
            <span class="step-number">1</span>
            <span class="step-text">选择模板，定义剧情结构维度</span>
          </div>
          <div class="guide-arrow">→</div>
          <div class="guide-step">
            <span class="step-number">2</span>
            <span class="step-text">填写当前剧情(A)和目标剧情(B)</span>
          </div>
          <div class="guide-arrow">→</div>
          <div class="guide-step">
            <span class="step-number">3</span>
            <span class="step-text">设计无痕铺垫，实现A到B的升级</span>
          </div>
        </div>
      </div>

      <!-- 左右布局主内容区 -->
      <div class="bridge-layout">
        <!-- 左侧：当前剧情A区域 -->
        <div class="scene-panel scene-a">
          <div class="panel-header">
            <div class="panel-title-group">
              <h3>当前剧情 (A)</h3>
              <div class="panel-subtitle">现有的剧情设计</div>
            </div>
            <div class="panel-indicator">
              <span class="indicator-dot current"></span>
            </div>
          </div>
          <div class="panel-content">
            <!-- 动态渲染JSON结构 -->
            <div class="json-editor">
              <div v-for="(value, key) in currentConfig.bridgeA" :key="`bridgeA-${key}`" class="json-field">
                <div class="field-header">{{ key }}</div>
                <div class="field-content">
                  <!-- 字符串类型 -->
                  <el-input
                    v-if="typeof value === 'string'"
                    v-model="currentConfig.bridgeA[key]"
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 10 }"
                    placeholder="请输入内容"
                  ></el-input>
                  
                  <!-- 简单数组类型 -->
                  <div v-else-if="Array.isArray(value) && (value.length === 0 || typeof value[0] !== 'object')" class="array-editor">
                    <div v-for="(item, index) in value" :key="`bridgeA-${key}-${index}`" class="array-item">
                      <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 8 }" v-model="currentConfig.bridgeA[key][index]" placeholder="请输入内容"></el-input>
                      <el-button @click="removeArrayItem('bridgeA', key, index)" circle size="small" v-if="isArrayType(key)">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                    <el-button  @click="addArrayItem('bridgeA', key)" class="add-item-btn" v-if="isArrayType(key)">
                      <el-icon><Plus /></el-icon> 添加项目
                    </el-button>
                  </div>
                  
                  <!-- 对象数组类型 -->
                  <div v-else-if="Array.isArray(value) && (value.length === 0 || typeof value[0] === 'object')" class="object-array-editor">
                    <!-- 空数组提示 -->
                    <div v-if="value.length === 0 && isArrayType(key)" class="empty-array-tip">
                      <el-empty description="暂无项目" :image-size="60">
                        <el-button @click="addObjectItem('bridgeA', key)" type="primary" size="small">
                          <el-icon><Plus /></el-icon> 添加第一个项目
                        </el-button>
                      </el-empty>
                    </div>
                    <!-- 数组项目列表 -->
                    <div v-for="(obj, index) in value" :key="`bridgeA-${key}-obj-${index}`" class="object-item">
                      <div class="object-header">
                        <span>项目 {{ index + 1 }}</span>
                        <el-button @click="removeObjectItem('bridgeA', key, index)" circle size="small" v-if="isArrayType(key)">
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </div>
                      <div v-for="(propValue, propKey) in obj" :key="`bridgeA-${key}-obj-${index}-${propKey}`" class="object-prop">
                        <div class="prop-header">
                          <div class="prop-title">{{ propKey }}</div>
                        </div>
                        <el-input
                          type="textarea"
                          :autosize="{ minRows: 2, maxRows: 8 }"
                          v-model="currentConfig.bridgeA[key][index][propKey]"
                          placeholder="请输入内容"
                        ></el-input>
                      </div>
                    </div>
                    <!-- 添加项目按钮 -->
                    <el-button @click="addObjectItem('bridgeA', key)" class="add-item-btn" v-if="isArrayType(key) && value.length > 0">
                      <el-icon><Plus /></el-icon> 添加项目
                    </el-button>
                  </div>
                  
                  <!-- 纯对象类型 -->
                  <div v-else-if="typeof value === 'object' && value !== null && !Array.isArray(value)" class="object-editor">
                    <div v-for="(propValue, propKey) in value" :key="`bridgeA-${key}-prop-${propKey}`" class="object-prop">
                      <div class="prop-header">
                        <div class="prop-title">{{ propKey }}</div>
                        <div class="prop-actions" v-if="isUserAddedField('bridgeA', key, propKey)">
                          <el-button @click="removeObjectProp('bridgeA', key, propKey)" circle size="small" type="danger">
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                      </div>
                      <el-input
                        type="textarea"
                        :autosize="{ minRows: 2, maxRows: 8 }"
                        v-model="currentConfig.bridgeA[key][propKey]"
                        placeholder="请输入内容"
                      ></el-input>
                    </div>
                    <el-button @click="addObjectProp('bridgeA', key)" class="add-item-btn" v-if="isArrayType(key)">
                      <el-icon><Plus /></el-icon> 添加属性
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 中间连接器 -->
        <div class="bridge-connector">
          <div class="connector-line"></div>
          <div class="connector-icon">
            <el-icon><ArrowRight /></el-icon>
          </div>
          <div class="connector-text">升级优化</div>
        </div>

        <!-- 右侧：目标剧情B区域 -->
        <div class="scene-panel scene-b">
          <div class="panel-header">
            <div class="panel-title-group">
              <h3>目标剧情 (B)</h3>
              <div class="panel-subtitle">升级优化后的剧情设计</div>
            </div>
            <div class="panel-indicator">
              <span class="indicator-dot target"></span>
            </div>
          </div>
          <div class="panel-content">
            <!-- 动态渲染JSON结构 -->
            <div class="json-editor">
              <div v-for="(value, key) in currentConfig.bridgeB" :key="`bridgeB-${key}`" class="json-field">
                <div class="field-header">{{ key }}</div>
                <div class="field-content">
                  <!-- 字符串类型 -->
                  <el-input
                    v-if="typeof value === 'string'"
                    v-model="currentConfig.bridgeB[key]"
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 10 }"
                    placeholder="请输入内容"
                  ></el-input>
                  
                  <!-- 简单数组类型 -->
                  <div v-else-if="Array.isArray(value) && (value.length === 0 || typeof value[0] !== 'object')" class="array-editor">
                    <div v-for="(item, index) in value" :key="`bridgeB-${key}-${index}`" class="array-item">
                      <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 8 }" v-model="currentConfig.bridgeB[key][index]" placeholder="请输入内容"></el-input>
                      <el-button @click="removeArrayItem('bridgeB', key, index)" circle size="small" v-if="isArrayType(key)">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                    <el-button @click="addArrayItem('bridgeB', key)" class="add-item-btn" v-if="isArrayType(key)">
                      <el-icon><Plus /></el-icon> 添加项目
                    </el-button>
                  </div>
                  
                  <!-- 对象数组类型 -->
                  <div v-else-if="Array.isArray(value) && (value.length === 0 || typeof value[0] === 'object')" class="object-array-editor">
                    <!-- 空数组提示 -->
                    <div v-if="value.length === 0 && isArrayType(key)" class="empty-array-tip">
                      <el-empty description="暂无项目" :image-size="60">
                        <el-button @click="addObjectItem('bridgeB', key)" type="primary" size="small">
                          <el-icon><Plus /></el-icon> 添加第一个项目
                        </el-button>
                      </el-empty>
                    </div>
                    <!-- 数组项目列表 -->
                    <div v-for="(obj, index) in value" :key="`bridgeB-${key}-obj-${index}`" class="object-item">
                      <div class="object-header">
                        <span>项目 {{ index + 1 }}</span>
                        <el-button @click="removeObjectItem('bridgeB', key, index)" circle size="small" v-if="isArrayType(key)">
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </div>
                      <div v-for="(propValue, propKey) in obj" :key="`bridgeB-${key}-obj-${index}-${propKey}`" class="object-prop">
                        <div class="prop-header">
                          <div class="prop-title">{{ propKey }}</div>
                        </div>
                        <el-input
                          type="textarea"
                          :autosize="{ minRows: 2, maxRows: 8 }"
                          v-model="currentConfig.bridgeB[key][index][propKey]"
                          placeholder="请输入内容"
                        ></el-input>
                      </div>
                    </div>
                    <!-- 添加项目按钮 -->
                    <el-button @click="addObjectItem('bridgeB', key)" class="add-item-btn" v-if="isArrayType(key) && value.length > 0">
                      <el-icon><Plus /></el-icon> 添加项目
                    </el-button>
                  </div>
                  
                  <!-- 纯对象类型 -->
                  <div v-else-if="typeof value === 'object' && value !== null && !Array.isArray(value)" class="object-editor">
                    <div v-for="(propValue, propKey) in value" :key="`bridgeB-${key}-prop-${propKey}`" class="object-prop">
                      <div class="prop-header">
                        <div class="prop-title">{{ propKey }}</div>
                        <div class="prop-actions" v-if="isUserAddedField('bridgeB', key, propKey)">
                          <el-button @click="removeObjectProp('bridgeB', key, propKey)" circle size="small" type="danger">
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                      </div>
                      <el-input
                        type="textarea"
                        :autosize="{ minRows: 2, maxRows: 8 }"
                        v-model="currentConfig.bridgeB[key][propKey]"
                        placeholder="请输入内容"
                      ></el-input>
                    </div>
                    <el-button @click="addObjectProp('bridgeB', key)" class="add-item-btn" v-if="isArrayType(key)">
                      <el-icon><Plus /></el-icon> 添加属性
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 铺垫设计区域切换 -->
      <div class="drawer-toggle" @click="toggleDrawer">
        <div class="toggle-content">
          <div class="toggle-icon">
            <el-icon :class="{ 'is-rotate': drawerVisible }"><ArrowUp /></el-icon>
          </div>
          <div class="toggle-text">
            <span class="toggle-title">{{ drawerVisible ? '收起铺垫设计' : '展开铺垫设计' }}</span>
            <span class="toggle-subtitle">设计从A到B的无痕过渡</span>
          </div>
          <div class="toggle-badge" v-if="currentConfig.guidelines && currentConfig.guidelines.length > 0">
            {{ currentConfig.guidelines.length }}
          </div>
        </div>
      </div>

      <!-- 卷帘式无痕铺垫区域 -->
      <div class="drawer-panel" :class="{ 'drawer-visible': drawerVisible }">
        <div class="drawer-content">
          <!-- 抽屉面板头部 -->
          <div class="drawer-header">
            <div class="section-title">
              <div class="title-icon">
                <el-icon><Compass /></el-icon>
              </div>
              <div class="title-content">
                <h3>无痕铺垫设计</h3>
                <div class="title-description">设计从当前剧情(A)到目标剧情(B)的升级路径</div>
              </div>
            </div>

            <!-- 添加铺垫指南按钮 -->
            <el-button
              type="primary"
              size="default"
              class="add-guideline-btn"
              @click="openAddGuidelineDialog"
            >
              <el-icon><Plus /></el-icon> 添加铺垫指南
            </el-button>
          </div>
          
          <!-- 铺垫指南列表 -->
          <div class="guidelines-content">
            <div 
              v-for="(guideline, index) in currentConfig.guidelines" 
              :key="index" 
              class="guideline-item"
              :class="{ 'high-priority': guideline.priority === 'high' }"
            >
              <div class="guideline-header">
                <span class="guideline-title">{{ guideline.title }}</span>
                <el-tag 
                  :type="guideline.priority === 'high' ? 'danger' : guideline.priority === 'medium' ? 'warning' : 'info'"
                  size="small"
                >
                  {{ guideline.priority === 'high' ? '高优先级' : guideline.priority === 'medium' ? '中优先级' : '低优先级' }}
                </el-tag>
              </div>
              <div class="guideline-description">{{ guideline.description }}</div>
              <!-- 添加操作区域 -->
              <div class="guideline-actions">
                <el-button 
                  type="primary" 
                  size="small" 
                  text 
                  @click="editGuideline(index)"
                >
                  <el-icon><Edit /></el-icon> 编辑
                </el-button>
                <el-button 
                  type="danger" 
                  size="small" 
                  text 
                  @click="removeGuideline(index)"
                >
                  <el-icon><Delete /></el-icon> 删除
                </el-button>
              </div>
            </div>
            <!-- 空状态提示 -->
            <el-empty 
              v-if="!currentConfig.guidelines || currentConfig.guidelines.length === 0" 
              description="暂无铺垫指南，点击添加按钮创建"
              :image-size="120"
            ></el-empty>
          </div>
        </div>
      </div>
    </div>

    <!-- 导入配置弹窗 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入剧情配置"
      width="600px"
      :lock-scroll="true"
      :destroy-on-close="false"
    >
      <div class="import-dialog-content">
        <p class="import-tip">请将剧情配置的JSON字符串粘贴到下方文本框中:</p>
        <el-input
          v-model="importJsonText"
          type="textarea"
          :autosize="{ minRows: 8, maxRows: 20 }"
          placeholder="粘贴JSON配置文本..."
        ></el-input>
        
        <div class="import-help">
          <p><i class="el-icon-info"></i> 提示：导入会替换当前所有配置内容</p>
        </div>
        
        <div v-if="importError" class="import-error">
          <el-alert
            :title="importError"
            type="error"
            show-icon
            :closable="false"
          ></el-alert>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false" plain>取消</el-button>
          <el-button type="primary" @click="processImportData" :loading="importLoading">
            导入
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加铺垫弹窗 -->
    <el-dialog
      v-model="addGuidelineDialogVisible"
      :title="isEditMode ? '编辑无痕铺垫' : '添加无痕铺垫'"
      width="800px"
      top="5vh"
      class="guideline-dialog"
      :modal-append-to-body="true"
      destroy-on-close
      :close-on-click-modal="false"
      :show-close="false"
      fullscreen
    >
      <template #header="{ titleId, titleClass }">
        <div class="dialog-custom-header">
          <h4 :id="titleId" :class="titleClass">{{ isEditMode ? '编辑无痕铺垫' : '添加无痕铺垫' }}</h4>
          <el-button class="dialog-close-btn" @click="addGuidelineDialogVisible = false" icon="Close"></el-button>
        </div>
      </template>
      
      <div class="guideline-form">
        <div class="form-header-section">
          <el-form :model="newGuideline" label-position="top">
            <el-row :gutter="20">
              <el-col :span="16">
                <el-form-item label="铺垫标题">
                  <el-input v-model="newGuideline.title" placeholder="为铺垫添加简短标题"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="铺垫类型">
                  <el-select v-model="newGuideline.type" placeholder="选择铺垫类型" class="full-width">
                    <el-option label="线索铺垫" value="线索铺垫"></el-option>
                    <el-option label="人物特征" value="人物特征"></el-option>
                    <el-option label="情感基调" value="情感基调"></el-option>
                    <el-option label="视角处理" value="视角处理"></el-option>
                    <el-option label="细节设计" value="细节设计"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        
        <!-- 内容可滚动区域 -->
        <div class="form-scrollable-content">
          <!-- 引导式提问表格 -->
          <div class="form-section">
            <h3 class="section-title">铺垫设计思考</h3>
            <div class="guideline-dimensions">
              <div v-for="question in dimensionQuestions" :key="question.key" class="dimension-row">
                <div class="dimension-header">
                  <div class="dimension-name">{{ question.dimension }}</div>
                  <div class="dimension-question">{{ question.question }}</div>
                </div>
                <div class="dimension-content">
                  <el-input
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 6 }"
                    v-model="newGuideline.answers[question.key]"
                    :placeholder="question.placeholder"
                  ></el-input>
                </div>
              </div>
            </div>
          </div>
          
          <div class="form-section">
            <h3 class="section-title">完整描述</h3>
            <el-form :model="newGuideline" label-position="top">
              <el-form-item label="">
                <el-input
                  v-model="newGuideline.description"
                  type="textarea"
                  :autosize="{ minRows: 3, maxRows: 8 }"
                  placeholder="根据上面的思考，总结完整的铺垫描述"
                ></el-input>
              </el-form-item>
              <el-form-item class="priority-checkbox">
                <el-checkbox v-model="newGuideline.priority" true-label="high" false-label="normal">
                  <span class="priority-label">标记为高优先级</span>
                </el-checkbox>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-custom-footer">
          <el-button @click="addGuidelineDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveNewGuideline">{{ isEditMode ? '更新' : '保存' }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新建设计对话框 -->
    <el-dialog
      v-model="newDesignDialogVisible"
      title="基于模板创建新设计"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @opened="handleNewDesignDialogOpened"
    >
      <div class="template-design-info" style="margin-bottom: 15px;">
        <el-alert
          type="info"
          :closable="false"
          show-icon
          class="theme-adaptive-alert"
        >
          <p>您正在基于模板创建新的设计。这将不会修改模板本身，而是创建一个独立的设计保存到您的设计列表中。</p>
        </el-alert>
      </div>
      <el-form :model="newDesignForm" label-position="top" @submit.prevent="confirmNewDesign">
        <el-form-item label="设计名称" required>
          <el-input
            ref="designNameInputRef"
            v-model="newDesignForm.name"
            placeholder="请输入设计名称"
            maxlength="50"
            show-word-limit
            @keyup.enter="confirmNewDesign"
          />
        </el-form-item>
        <el-form-item label="选择配置模板">
          <el-select v-model="newDesignForm.configId" placeholder="请选择配置模板" class="full-width">
            <el-option
              v-for="config in configs"
              :key="config.id"
              :label="config.name || config.id"
              :value="config.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="newDesignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmNewDesign" :disabled="!newDesignForm.name">
            创建设计
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 保存设计对话框 -->
    <el-dialog
      v-model="saveDesignDialogVisible"
      title="保存设计"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @opened="handleSaveDesignDialogOpened"
    >
      <el-form :model="saveDesignForm" label-position="top" @submit.prevent="confirmSaveDesign">
        <el-form-item label="设计名称" required>
          <el-input
            ref="saveNameInputRef"
            v-model="saveDesignForm.name"
            placeholder="请输入设计名称"
            maxlength="50"
            show-word-limit
            @keyup.enter="confirmSaveDesign"
          />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="saveDesignForm.description"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 6 }"
            placeholder="请输入设计描述（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="saveDesignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSaveDesign" :disabled="!saveDesignForm.name">
            确认保存
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 导入设计对话框 -->
    <el-dialog
      v-model="importDesignDialogVisible"
      title="导入设计JSON"
      width="600px"
      :close-on-click-modal="false"
      :destroy-on-close="false"
    >
      <div class="import-dialog-content">
        <p class="import-tip">请将设计的JSON字符串粘贴到下方文本框中:</p>
        <el-input
          v-model="importDesignJsonText"
          type="textarea"
          :autosize="{ minRows: 8, maxRows: 20 }"
          placeholder="粘贴设计JSON文本..."
        ></el-input>
        
        <div class="import-help">
          <p><i class="el-icon-info"></i> 提示：导入会创建一个新的设计</p>
        </div>
        
        <div v-if="importDesignError" class="import-error">
          <el-alert
            :title="importDesignError"
            type="error"
            show-icon
            :closable="false"
          ></el-alert>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDesignDialogVisible = false" plain>取消</el-button>
          <el-button type="primary" @click="processImportDesign" :loading="importDesignLoading">
            导入
          </el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 配置编辑弹窗 -->
    <el-dialog
      v-model="configEditorVisible"
      title="编辑剧情配置"
      width="800px"
      top="5vh"
      class="config-editor-dialog"
      :modal-append-to-body="true"
      destroy-on-close
      :close-on-click-modal="false"
      :show-close="false"
      fullscreen
    >
      <template #header="{ titleId, titleClass }">
        <div class="dialog-custom-header">
          <h4 :id="titleId" :class="titleClass">编辑剧情配置</h4>
          <el-button class="dialog-close-btn" @click="configEditorVisible = false" icon="Close"></el-button>
        </div>
      </template>
      
      <div class="config-editor-form">
        <div class="form-header-section">
          <el-form :model="editingConfig" label-position="top">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="配置名称">
                  <el-input v-model="editingConfig.name" placeholder="为配置添加名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="配置ID">
                  <el-input v-model="editingConfig.id" placeholder="配置唯一标识" disabled></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="配置描述">
              <el-input v-model="editingConfig.description" type="textarea" :autosize="{ minRows: 2, maxRows: 6 }" placeholder="添加配置描述"></el-input>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 内容可滚动区域 -->
        <div class="form-scrollable-content">
          <!-- 剧情结构编辑区 -->
          <div class="form-section">
            <h3 class="section-title">剧情结构定义</h3>
            <p class="section-description">
              此处定义的结构模板将同时应用于A/B两个剧情，支持一层复杂的嵌套结构,暂时不更新。
            </p>
            
            <!-- 添加根字段按钮 -->
            <el-button 
              type="primary" 
              size="small" 
              @click="addRootField"
              style="margin-bottom: 15px;"
            >
              <el-icon><Plus /></el-icon> 添加根字段
            </el-button>
            
            <!-- 字段列表 -->
            <div class="fields-list">
              <div 
                v-for="(field, key, index) in structureFields" 
                :key="`root-${key}`" 
                class="field-card"
              >
                <div class="field-order-badge">{{ index + 1 }}</div>
                <div class="field-header">
                  <div class="field-name-type">
                    <el-input 
                      v-model="field.name" 
                      placeholder="字段名称" 
                      size="small"
                      @change="updateFieldName(key, field.name)"
                    />
                    <el-select 
                      v-model="field.type" 
                      placeholder="选择类型" 
                      size="small"
                      @change="updateFieldType(key, field.type)"
                    >
                      <el-option label="字符串" value="string" />
                      <el-option label="数字" value="number" />
                      <el-option label="布尔值" value="boolean" />
                      <el-option label="对象" value="object" />
                      <el-option label="数组" value="array" />
                    </el-select>
                  </div>
                  <div class="field-actions">
                    <!-- 添加上移和下移按钮 -->
                    <el-button 
                      size="small" 
                      type="info" 
                      @click="moveFieldUp(key)" 
                      circle
                      :disabled="index === 0"
                    >
                      <el-icon><ArrowUp /></el-icon>
                    </el-button>
                    <el-button 
                      size="small" 
                      type="info" 
                      @click="moveFieldDown(key)" 
                      circle
                      :disabled="index === Object.keys(structureFields).length - 1"
                    >
                      <el-icon><ArrowDown /></el-icon>
                    </el-button>
                    <template v-if="field.type === 'object' || field.type === 'array'">
                      <el-button 
                        size="small" 
                        type="primary" 
                        @click="addChildField(key)" 
                        circle
                      >
                        <el-icon><Plus /></el-icon>
                      </el-button>
                    </template>
                    <el-button 
                      size="small" 
                      type="danger" 
                      @click="removeField(key)" 
                      circle
                    >
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
                
                <!-- 子字段区域 -->
                <div 
                  v-if="(field.type === 'object' || field.type === 'array') && field.children" 
                  class="field-children"
                >
                  <div 
                    v-if="Object.keys(field.children).length === 0" 
                    class="empty-children"
                  >
                    <span>暂无子字段</span>
                    <el-button 
                      size="small" 
                      type="primary" 
                      @click="addChildField(key)"
                    >
                      添加子字段
                    </el-button>
                  </div>
                                      <div 
                    v-for="(child, childKey, childIndex) in field.children" 
                    :key="`child-${key}-${childKey}`"
                    class="child-field"
                  >
                      <div class="field-order-badge child-badge">{{ childIndex + 1 }}</div>
                    <div class="field-header">
                      <div class="field-name-type">
                        <el-input 
                          v-model="child.name" 
                          placeholder="字段名称" 
                          size="small"
                          @change="updateChildFieldName(key, childKey, child.name)"
                        />
                        <el-select 
                          v-model="child.type" 
                          placeholder="选择类型" 
                          size="small"
                          @change="updateChildFieldType(key, childKey, child.type)"
                        >
                          <el-option label="字符串" value="string" />
                          <el-option label="数字" value="number" />
                          <el-option label="布尔值" value="boolean" />
                          <el-option label="对象" value="object" />
                          <el-option label="数组" value="array" />
                        </el-select>
                      </div>
                      <div class="field-actions">
                        <!-- 添加上移和下移按钮 -->
                        <el-button 
                          size="small" 
                          type="info" 
                          @click="moveChildFieldUp(key, childKey)" 
                          circle
                          :disabled="childIndex === 0"
                        >
                          <el-icon><ArrowUp /></el-icon>
                        </el-button>
                        <el-button 
                          size="small" 
                          type="info" 
                          @click="moveChildFieldDown(key, childKey)" 
                          circle
                          :disabled="childIndex === Object.keys(field.children).length - 1"
                        >
                          <el-icon><ArrowDown /></el-icon>
                        </el-button>
                        <template v-if="child.type === 'object' || child.type === 'array'">
                          <el-button 
                            size="small" 
                            type="primary" 
                            @click="addNestedChildField(key, childKey)" 
                            circle
                          >
                            <el-icon><Plus /></el-icon>
                          </el-button>
                        </template>
                        <el-button 
                          size="small" 
                          type="danger" 
                          @click="removeChildField(key, childKey)" 
                          circle
                        >
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </div>
                    </div>
                    
                    <!-- 嵌套子字段区域 -->
                    <div 
                      v-if="(child.type === 'object' || child.type === 'array') && child.children" 
                      class="nested-children"
                    >
                      <div 
                        v-if="Object.keys(child.children).length === 0" 
                        class="empty-children"
                      >
                        <span>暂无嵌套字段</span>
                        <el-button 
                          size="small" 
                          type="primary" 
                          @click="addNestedChildField(key, childKey)"
                        >
                          添加嵌套字段
                        </el-button>
                      </div>
                                              <div 
                        v-for="(nestedChild, nestedKey, nestedIndex) in child.children" 
                        :key="`nested-${key}-${childKey}-${nestedKey}`"
                        class="nested-field"
                      >
                          <div class="field-order-badge nested-badge">{{ nestedIndex + 1 }}</div>
                        <div class="field-header">
                          <div class="field-name-type">
                            <el-input 
                              v-model="nestedChild.name" 
                              placeholder="字段名称" 
                              size="small"
                              @change="updateNestedFieldName(key, childKey, nestedKey, nestedChild.name)"
                            />
                            <el-select 
                              v-model="nestedChild.type" 
                              placeholder="选择类型" 
                              size="small"
                              @change="updateNestedFieldType(key, childKey, nestedKey, nestedChild.type)"
                            >
                              <el-option label="字符串" value="string" />
                              <el-option label="数字" value="number" />
                              <el-option label="布尔值" value="boolean" />
                            </el-select>
                          </div>
                          <div class="field-actions">
                            <!-- 添加上移和下移按钮 -->
                            <el-button 
                              size="small" 
                              type="info" 
                              @click="moveNestedFieldUp(key, childKey, nestedKey)" 
                              circle
                              :disabled="nestedIndex === 0"
                            >
                              <el-icon><ArrowUp /></el-icon>
                            </el-button>
                            <el-button 
                              size="small" 
                              type="info" 
                              @click="moveNestedFieldDown(key, childKey, nestedKey)" 
                              circle
                              :disabled="nestedIndex === Object.keys(child.children).length - 1"
                            >
                              <el-icon><ArrowDown /></el-icon>
                            </el-button>
                            <el-button 
                              size="small" 
                              type="danger" 
                              @click="removeNestedField(key, childKey, nestedKey)" 
                              circle
                            >
                              <el-icon><Delete /></el-icon>
                            </el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 空状态 -->
              <el-empty 
                v-if="Object.keys(structureFields).length === 0" 
                description="暂无字段结构"
                :image-size="80"
              >
                <el-button 
                  type="primary" 
                  @click="addRootField"
                >
                  开始添加字段
                </el-button>
              </el-empty>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-custom-footer">
          <el-button @click="configEditorVisible = false">取消</el-button>
          <el-button type="primary" @click="saveConfigEdits">保存配置</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, reactive, onBeforeUnmount, nextTick } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Download, Document, Compass, Delete, Plus, ArrowUp, ArrowRight, SwitchButton, Setting, Edit, CopyDocument, ArrowDown, Calendar, Search, More, Upload } from '@element-plus/icons-vue'

// 初始化状态
const isLoading = ref(false)
// 配置相关状态
const configs = ref([])
const currentConfigId = ref(null)
const hasUnsavedChanges = ref(false)
const configSearchQuery = ref('')
const currentConfig = reactive({
  id: 'scene_default',
  name: '剧情无痕设计',
  description: '',
  bridgeA: {
    "读者感受/效果":[{"效果":"","手段":""}],
    "人物":[],
    "行动":"",
    "行动影响":[],
    "反转":"",
    "反套路":[]
  },
  bridgeB: {
    "读者感受/效果":[{"效果":"","手段":""}],
    "人物":[],
    "行动":"",
    "行动影响":[],
    "反转":"",
    "反套路":[]
  },
  guidelines: []
})

// 过滤后的配置列表
const filteredConfigs = computed(() => {
  if (!configSearchQuery.value.trim()) {
    return configs.value;
  }
  
  const query = configSearchQuery.value.toLowerCase().trim();
  return configs.value.filter(config => {
    // 搜索名称和描述
    const nameMatch = (config.name || '').toLowerCase().includes(query);
    const descMatch = (config.description || '').toLowerCase().includes(query);
    
    // 搜索字段名
    const fieldsMatch = Object.keys(config.bridgeA || {}).some(key => 
      key.toLowerCase().includes(query)
    );
    
    return nameMatch || descMatch || fieldsMatch;
  });
})

// 历史设计相关状态
const designHistory = ref([])
const selectedHistoryId = ref('')
const designSearchQuery = ref('')

// 过滤后的设计列表
const filteredDesigns = computed(() => {
  if (!designSearchQuery.value.trim()) {
    return designHistory.value;
  }
  
  const query = designSearchQuery.value.toLowerCase().trim();
  return designHistory.value.filter(design => {
    // 搜索名称和描述
    const nameMatch = (design.name || '').toLowerCase().includes(query);
    const descMatch = (design.description || '').toLowerCase().includes(query);
    
    // 搜索铺垫内容
    const guidelinesMatch = (design.guidelines || []).some(guideline => 
      (guideline.title || '').toLowerCase().includes(query) || 
      (guideline.description || '').toLowerCase().includes(query)
    );
    
    return nameMatch || descMatch || guidelinesMatch;
  });
})

// 新建设计对话框相关状态
const newDesignDialogVisible = ref(false)
const designNameInputRef = ref(null)
const newDesignForm = reactive({
  name: '',
  configId: ''
})

// 保存设计对话框相关状态
const saveDesignDialogVisible = ref(false)
const saveNameInputRef = ref(null)
const saveDesignForm = reactive({
  name: '',
  description: ''
})

// 配置模板编辑弹窗相关状态
const configEditorVisible = ref(false)
const editingConfig = reactive({
  id: '',
  name: '',
  description: '',
  bridgeA: {},
  bridgeB: {}
})

// 使用普通对象来简化字段管理
const structureFields = reactive({})

// 卷帘式抽屉状态
const drawerVisible = ref(false)

// 切换抽屉显示状态
function toggleDrawer() {
  drawerVisible.value = !drawerVisible.value
}

// 添加铺垫相关状态
const addGuidelineDialogVisible = ref(false)
const isEditMode = ref(false) // 添加编辑模式标志
const editingGuidelineIndex = ref(-1) // 添加正在编辑的铺垫索引
const newGuideline = reactive({
  type: '线索铺垫',
  title: '',
  description: '',
  priority: 'normal',
  answers: {
    physicalCondition: '',
    motivation: '',
    obstacle: '',
    reasonableness: '',
    implementation: ''
  }
})

// 铺垫维度问题
const dimensionQuestions = [
  {
    key: 'physicalCondition',
    dimension: '物理条件',
    question: '道具/环境是否就位？',
    placeholder: '例：太监服必须存在且可获取'
  },
  {
    key: 'motivation',
    dimension: '行为动机+时机',
    question: '何必须现在做？',
    placeholder: '例：为什么角色此时必须这样做？'
  },
  {
    key: 'obstacle',
    dimension: '阻碍清除方案',
    question: '为何不能选其他方案？',
    placeholder: '例：不穿太监服无法混入皇宫'
  },
  {
    key: 'reasonableness',
    dimension: '对比设计',
    question: '如何让读者认同？',
    placeholder: '例：对比其他选择的劣势'
  },
  {
    key: 'implementation',
    dimension: '如何达成',
    question: '具体如何铺垫实现？',
    placeholder: '例：在前文提及太监服的获取途径'
  }
]

// 打开添加铺垫对话框
function openAddGuidelineDialog() {
  // 重置表单
  newGuideline.type = '线索铺垫'
  newGuideline.title = ''
  newGuideline.description = ''
  newGuideline.priority = 'normal'
  
  // 重置所有答案
  Object.keys(newGuideline.answers).forEach(key => {
    newGuideline.answers[key] = ''
  })
  
  // 设置为添加模式
  isEditMode.value = false
  editingGuidelineIndex.value = -1
  
  // 显示对话框
  addGuidelineDialogVisible.value = true
}

// 保存新的铺垫
function saveNewGuideline() {
  // 验证必填项
  if (!newGuideline.title.trim()) {
    ElMessage.warning('请输入铺垫标题')
    return
  }
  
  if (!newGuideline.description.trim()) {
    ElMessage.warning('请输入完整描述')
    return
  }
  
  // 汇总答案内容
  const answersContent = Object.values(newGuideline.answers)
    .filter(answer => answer.trim())
    .join('\n\n');
    
  // 如果描述为空但有答案内容，自动填充
  if (newGuideline.description.trim() === '' && answersContent) {
    newGuideline.description = answersContent;
  }
  
  // 创建新的铺垫对象
  const guideline = {
    type: newGuideline.type,
    title: newGuideline.title,
    description: newGuideline.description,
    priority: newGuideline.priority,
    // 保存结构化答案
    answers: {...newGuideline.answers}
  }
  
  if (isEditMode.value && editingGuidelineIndex.value >= 0) {
    // 更新现有铺垫
    currentConfig.guidelines[editingGuidelineIndex.value] = guideline
    ElMessage.success('铺垫更新成功')
  } else {
    // 添加新铺垫
    currentConfig.guidelines.push(guideline)
    ElMessage.success('铺垫添加成功')
  }
  
  // 自动保存配置
  saveCurrentConfig()
  
  // 关闭对话框
  addGuidelineDialogVisible.value = false
}

// 对话框状态
const importDialogVisible = ref(false)
const importJsonText = ref('')
const importError = ref('')
const importLoading = ref(false)

// 导入设计对话框状态
const importDesignDialogVisible = ref(false)
const importDesignJsonText = ref('')
const importDesignError = ref('')
const importDesignLoading = ref(false)

// 添加自动保存定时器引用
const autoSaveTimer = ref(null)

// 添加一个标志来跟踪是否是初始化阶段
const isInitializing = ref(true)

// 自动保存功能
watch(
  () => [currentConfig.bridgeA, currentConfig.bridgeB, currentConfig.guidelines],
  () => {
    // 如果是初始化阶段，不设置未保存状态
    if (isInitializing.value) {
      return
    }

    hasUnsavedChanges.value = true
    // 添加自动保存延迟定时器
    if (autoSaveTimer.value) {
      clearTimeout(autoSaveTimer.value)
    }
    autoSaveTimer.value = setTimeout(async () => {
      await saveCurrentConfig()
    }, 5000) // 延迟5秒自动保存
  },
  { deep: true }
)

// 生命周期钩子，组件卸载时清理定时器
onBeforeUnmount(() => {
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value)
  }
})

// 格式化日期
function formatDate(timestamp) {
  if (!timestamp) return '未知时间';
  
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
}

// 创建新设计
function createNewDesign() {
  // 重置表单
  newDesignForm.name = '';
  
  // 默认选择当前配置模板
  newDesignForm.configId = currentConfig.id;
  
  // 显示对话框
  newDesignDialogVisible.value = true;
}

// 处理新建设计对话框打开事件
function handleNewDesignDialogOpened() {
  // 自动聚焦到名称输入框
  nextTick(() => {
    if (designNameInputRef.value) {
      designNameInputRef.value.focus();
    }
  });
}

// 确认创建新设计
async function confirmNewDesign() {
  if (!newDesignForm.name.trim()) {
    ElMessage.warning('请输入设计名称');
    return;
  }
  
  try {
    isLoading.value = true;
    
    // 获取选择的配置模板
    const selectedConfig = configs.value.find(config => config.id === newDesignForm.configId) || configs.value[0];
    
    if (!selectedConfig) {
      ElMessage.error('未找到有效的配置模板');
      isLoading.value = false;
      return;
    }
    
    // 创建新设计对象（注意：这是创建一个设计，而不是修改模板）
    const newDesign = {
      id: 'design_' + Date.now(), // 使用design_前缀明确区分这是一个设计而非配置
      name: newDesignForm.name.trim(),
      description: '',
      timestamp: Date.now(),
      bridgeA: JSON.parse(JSON.stringify(selectedConfig.bridgeA || selectedConfig.sceneA || {})),
      bridgeB: JSON.parse(JSON.stringify(selectedConfig.bridgeB || selectedConfig.sceneB || {})),
      guidelines: [],
      isDesign: true // 添加标记，标识这是一个设计而非配置模板
    };
    
    // 更新当前配置
    Object.assign(currentConfig, newDesign);
    currentConfigId.value = newDesign.id;
    
    // 保存到历史 - 只保存设计，不影响配置模板
    await saveSceneDesign(true);
    
    // 关闭对话框
    newDesignDialogVisible.value = false;
    
    ElMessage.success(`新设计"${newDesign.name}"已创建并保存到您的设计列表中`);
  } catch (error) {
    console.error('创建新设计失败', error);
    ElMessage.error('创建新设计失败: ' + (error.message || '未知错误'));
  } finally {
    isLoading.value = false;
  }
}

// 处理保存设计对话框打开事件
function handleSaveDesignDialogOpened() {
  // 预填充表单
  saveDesignForm.name = currentConfig.name || '';
  saveDesignForm.description = currentConfig.description || '';
  
  // 自动聚焦到名称输入框
  nextTick(() => {
    if (saveNameInputRef.value) {
      saveNameInputRef.value.focus();
    }
  });
}

// 确认保存设计
async function confirmSaveDesign() {
  if (!saveDesignForm.name.trim()) {
    ElMessage.warning('请输入设计名称');
    return;
  }
  
  try {
    // 更新当前配置的名称和描述
    currentConfig.name = saveDesignForm.name.trim();
    currentConfig.description = saveDesignForm.description.trim();
    
    // 保存设计
    await saveSceneDesign(true);
    
    // 关闭对话框
    saveDesignDialogVisible.value = false;
    
    ElMessage.success('设计已保存');
  } catch (error) {
    console.error('保存设计失败', error);
    ElMessage.error('保存设计失败: ' + (error.message || '未知错误'));
  }
}

// 加载历史设计列表
async function loadDesignHistory() {
  try {
    isLoading.value = true;
    
    // 调用后端API加载所有设计
    const response = await window.pywebview.api.book_controller.get_scene_designs();
    
    if (response.success && response.data) {
      // 按时间戳排序，最新的在前面
      designHistory.value = response.data.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
      console.log('从后端API加载历史设计列表成功:', designHistory.value);
    } else {
      console.warn('后端API返回的设计历史为空:', response);
      designHistory.value = [];
    }
  } catch (error) {
    console.error('加载历史设计列表失败', error);
    ElMessage.error('加载历史设计列表失败: ' + (error.message || '未知错误'));
    designHistory.value = [];
  } finally {
    isLoading.value = false;
  }
}



// 确认删除设计
async function confirmDeleteDesign(designId) {
  try {
    await ElMessageBox.confirm('确定要删除这个设计吗？此操作不可恢复。', '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    await deleteHistoryDesign(designId);
  } catch (error) {
    if (error === 'cancel') {
      return; // 用户取消删除操作
    }
    console.error('确认删除设计失败:', error);
  }
}

// 删除历史设计
async function deleteHistoryDesign(designId) {
  try {
    // 调用后端API删除设计
    const response = await window.pywebview.api.book_controller.delete_scene_design(designId);
    
    if (!response.success) {
      throw new Error(response.message || '删除失败：API调用出错');
    }
    
    // 重新加载历史记录
    await loadDesignHistory();
    
    // 如果删除的是当前正在编辑的设计，则加载另一个设计（如果有）
    if (designId === currentConfigId.value) {
      if (designHistory.value.length > 0) {
        await loadHistoryDesign(designHistory.value[0].id);
      } else {
        // 如果没有其他设计了，重置为默认状态
        resetToDefaultConfig();
      }
    }
    
    ElMessage.success('设计已删除');
  } catch (error) {
    console.error('删除设计失败:', error);
    ElMessage.error(error.message || '删除设计失败');
  }
}

// 重置为默认配置
function resetToDefaultConfig() {
  currentConfig.id = 'scene_default';
  currentConfig.name = '剧情无痕设计';
  currentConfig.description = '';
  currentConfig.bridgeA = {
    "读者感受/效果":[{"效果":"","手段":""}],
    "人物":[],
    "行动":"",
    "行动影响":[],
    "反转":"",
    "反套路":[]
  };
  currentConfig.bridgeB = {
    "读者感受/效果":[{"效果":"","手段":""}],
    "人物":[],
    "行动":"",
    "行动影响":[],
    "反转":"",
    "反套路":[]
  };
  currentConfig.guidelines = [];
  currentConfigId.value = 'scene_default';
  hasUnsavedChanges.value = false;
  isInitializing.value = false;
}

// 创建新配置模板
function createNewConfig() {
  // 生成唯一名称
  let baseName = '新建模板';
  let name = baseName;
  let counter = 1;
  
  // 检查是否有重名，如果有则添加数字后缀
  while (configs.value.some(config => config.name === name)) {
    name = `${baseName} ${counter}`;
    counter++;
  }
  
  // 创建新配置对象
  const newConfig = {
    id: 'config_' + Date.now(),
    name: name,
    description: '用于剧情设计的结构模板',
    bridgeA: JSON.parse(JSON.stringify(currentConfig.bridgeA)),
    bridgeB: JSON.parse(JSON.stringify(currentConfig.bridgeB))
  };
  
  // 打开编辑器进行编辑
  openConfigEditor(newConfig.id, newConfig);
}

// 选择配置
function selectConfig(configId) {
  try {
    // 如果有未保存的更改，提示用户
    if (hasUnsavedChanges.value) {
      ElMessageBox.confirm(
        '当前有未保存的更改，切换配置将丢失这些更改。是否继续？',
        '未保存的更改',
        {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 确认后加载选中的配置
        loadConfig(configId);
      }).catch(() => {
        // 用户取消操作
      });
    } else {
      // 没有未保存的更改，直接加载
      loadConfig(configId);
    }
  } catch (error) {
    console.error('选择配置失败:', error);
    ElMessage.error('选择配置失败: ' + (error.message || '未知错误'));
  }
}

// 加载配置
function loadConfig(configId) {
  const config = configs.value.find(c => c.id === configId);
  if (!config) return;
  
  // 更新当前配置
  Object.assign(currentConfig, {
    id: config.id,
    name: config.name,
    description: config.description,
    bridgeA: JSON.parse(JSON.stringify(config.bridgeA || {})),
    bridgeB: JSON.parse(JSON.stringify(config.bridgeB || {})),
    guidelines: [] // 重置铺垫
  });
  
  currentConfigId.value = config.id;
  hasUnsavedChanges.value = false;
  isInitializing.value = false;

  ElMessage.success(`已加载模板: ${config.name}`);
}

// 此函数用于选择模板创建新设计
async function selectConfigForNewDesign(configId) {
  try {
    // 查找模板
    const templateConfig = configs.value.find(c => c.id === configId);
    if (!templateConfig) {
      ElMessage.error('未找到指定的模板');
      return;
    }
    
    // 显示新建设计对话框
    newDesignForm.configId = configId;
    // 清空名称字段，让用户输入新的设计名称
    newDesignForm.name = '';
    // 更新提示文本，让用户明确知道是在创建设计而不是修改模板
    ElMessage.info(`您正在基于模板"${templateConfig.name || '未命名模板'}"创建新的设计`);
    newDesignDialogVisible.value = true;
  } catch (error) {
    console.error('选择模板失败:', error);
    ElMessage.error('选择模板失败: ' + (error.message || '未知错误'));
  }
}

// 确认删除模板
async function confirmDeleteConfig(configId) {
  try {
    await ElMessageBox.confirm('确定要删除这个模板吗？此操作不可恢复。', '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    await deleteConfig(configId);
  } catch (error) {
    if (error === 'cancel') {
      return; // 用户取消删除操作
    }
    console.error('确认删除模板失败:', error);
  }
}

// 删除模板
async function deleteConfig(configId) {
  try {
    // 检查是否是默认模板
    if (configId === 'scene_default') {
      ElMessage.warning('无法删除默认模板');
      return;
    }
    
    // 如果删除的是当前正在使用的模板，需要先切换到默认模板
    const isCurrentTemplate = configId === currentConfigId.value;
    if (isCurrentTemplate) {
      // 提示用户将切换到默认模板
      ElMessage.info('删除当前使用的模板后将切换到默认模板');
    }
    
    // 调用后端API删除配置
    // 注意: delete_bridge_config方法需要两个参数: book_id(可选), config_id
    const response = await window.pywebview.api.book_controller.delete_bridge_config(null, configId);
    
    // 处理响应
    const result = typeof response === 'string' ? JSON.parse(response) : response;
    
    if (result.success || result.status === 'success') {
      // 从本地列表中移除
      configs.value = configs.value.filter(c => c.id !== configId);
      
      // 如果删除的是当前使用的模板，切换到默认模板
      if (configId === currentConfigId.value) {
        const defaultConfig = configs.value.find(c => c.id === 'scene_default') || configs.value[0];
        if (defaultConfig) {
          loadConfig(defaultConfig.id);
        } else {
          // 如果没有任何模板，重置为默认状态
          resetToDefaultConfig();
        }
      }
      
      ElMessage.success('模板已删除');
    } else {
      throw new Error(result.message || '删除失败');
    }
  } catch (error) {
    console.error('删除配置失败:', error);
    ElMessage.error('删除配置失败: ' + (error.message || '未知错误'));
  }
}

// 加载历史设计
async function loadHistoryDesign(designId) {
  if (!designId) return;
  
  try {
    isLoading.value = true;
    
    // 如果有未保存的更改，提示用户
    if (hasUnsavedChanges.value) {
      const confirmed = await ElMessageBox.confirm(
        '当前设计有未保存的更改，切换将丢失这些更改。是否继续？',
        '未保存的更改',
        {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).catch(() => false);
      
      if (!confirmed) {
        isLoading.value = false;
        return;
      }
    }
    
    // 调用后端API加载设计
    const response = await window.pywebview.api.book_controller.load_scene_design(designId);
    
    if (!response.success) {
      ElMessage.error(response.message || '加载设计失败');
      isLoading.value = false;
      return;
    }
    
    const designItem = response.data;
    
    // 更新当前配置
    const normalizedDesign = {
      ...designItem,
      bridgeA: designItem.bridgeA || designItem.sceneA || {},
      bridgeB: designItem.bridgeB || designItem.sceneB || {}
    };
    
    Object.assign(currentConfig, normalizedDesign);
    currentConfigId.value = normalizedDesign.id;
    selectedHistoryId.value = normalizedDesign.id;
    
    // 重置未保存标志
    hasUnsavedChanges.value = false;
    isInitializing.value = false;

    ElMessage.success(`已加载设计: ${normalizedDesign.name || normalizedDesign.id}`);
    console.log('加载历史设计成功:', normalizedDesign);
  } catch (error) {
    console.error('加载历史设计失败', error);
    ElMessage.error('加载历史设计失败: ' + (error.message || '未知错误'));
  } finally {
    isLoading.value = false;
  }
}

// 生命周期
onMounted(async () => {
  try {
    // 尝试加载配置
    let response = await window.pywebview.api.book_controller.get_bridge_configs()

    // 处理后端可能返回的JSON字符串
    if (typeof response === 'string') {
      try {
        response = JSON.parse(response)
      } catch (err) {
        console.error('解析后端响应失败:', err)
        // 即使解析失败，继续使用默认配置，但不自动保存覆盖
      }
    }

    if ((response.success || response.status === 'success') && response.data && response.data.length > 0) {
      configs.value = response.data

      // 加载第一个配置
      const firstConfig = response.data[0]

      // 确保结构一致性
      const normalizedConfig = {
        ...firstConfig,
        // 优先使用bridgeX，如果没有则使用sceneX
        bridgeA: firstConfig.bridgeA || firstConfig.sceneA || {},
        bridgeB: firstConfig.bridgeB || firstConfig.sceneB || {}
      }

      Object.assign(currentConfig, normalizedConfig)
      currentConfigId.value = normalizedConfig.id

      console.log('加载配置成功:', currentConfig)
    } else {
      // 没有现有配置，但不自动保存覆盖
      // 改为提示用户
      console.log('未检测到现有配置，使用默认配置')
      ElMessage.info('未检测到现有配置，使用默认配置')
      // 不再调用 saveCurrentConfig()
    }

    // 加载历史设计列表
    await loadDesignHistory();

    // 加载最新的设计 (如果有)
    if (designHistory.value.length > 0) {
      const latestDesign = designHistory.value[0];
      selectedHistoryId.value = latestDesign.id;
      await loadHistoryDesign(latestDesign.id);
    } else {
      // 如果没有历史设计，加载保存的设计 (如果有)
      await loadSavedDesign();
    }
  } catch (error) {
    console.error('加载配置失败', error)
    // 出错时不再自动保存默认配置，避免覆盖
    ElMessage.warning('加载配置失败，使用默认配置')
    // 不再调用 saveCurrentConfig()
  }

  // 确保数据格式兼容
  ensureCompatibleFormat()

  // 初始化完成，开始监听变化
  setTimeout(() => {
    isInitializing.value = false
  }, 1000) // 延迟1秒确保所有初始化完成

  // 添加键盘快捷键监听
  document.addEventListener('keydown', handleKeyDown)

  // 添加页面离开提醒
  window.addEventListener('beforeunload', handleBeforeUnload)
})

// 在组件卸载时移除监听器
onBeforeUnmount(() => {
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value)
  }
  
  // 移除键盘快捷键监听
  document.removeEventListener('keydown', handleKeyDown)
  
  // 移除页面离开提醒
  window.removeEventListener('beforeunload', handleBeforeUnload)
})

// 处理键盘快捷键
function handleKeyDown(event) {
  // 检查是否是Ctrl+S
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault() // 阻止浏览器默认保存行为
    saveSceneDesign(true) // 调用保存功能，传入true参数直接保存，不显示对话框
  }
}

// 保存当前配置
async function saveCurrentConfig() {
  try {
    isLoading.value = true
    
    // 如果当前是设计而不是配置模板，不应该保存到配置中
    // 检查ID前缀或isDesign标记
    if (currentConfig.isDesign || (currentConfig.id && currentConfig.id.startsWith('design_'))) {
      console.log('当前是设计而非配置模板，跳过保存到配置中')
      // 改为调用saveSceneDesign来保存设计
      return await saveSceneDesign(true)
    }
    
    // 深拷贝确保所有嵌套对象都被正确传递
    const configToSave = JSON.parse(JSON.stringify(currentConfig))
    
    // 确保前后端数据结构一致
    // 同时保留两套数据，防止任何不兼容问题
    configToSave.sceneA = configToSave.bridgeA || {}
    configToSave.sceneB = configToSave.bridgeB || {}
    
    // 获取配置ID
    const configId = configToSave.id || null
    console.log('准备保存配置:', configId, configToSave)
    
    // 调用后端API保存配置
    // 确保参数顺序与后端方法定义匹配: config_id, config_data
    let response = await window.pywebview.api.book_controller.save_bridge_config(configId, configToSave)
    
    // 处理后端可能返回的JSON字符串
    if (typeof response === 'string') {
      try {
        response = JSON.parse(response)
      } catch (err) {
        console.error('解析后端响应失败:', err)
        throw new Error('后端响应格式不正确')
      }
    }
    
    if (response.success || response.status === 'success') {
      // 提取数据，兼容不同的返回格式
      const responseData = response.data || {}
      
      // 更新本地缓存
      const index = configs.value.findIndex(c => c.id === (responseData.id || configId))
      if (index !== -1) {
        configs.value[index] = { ...responseData }
      } else {
        configs.value.push({ ...responseData })
      }
      
      // 更新当前配置ID
      if (responseData.id) {
        currentConfigId.value = responseData.id
        currentConfig.id = responseData.id
      }
      
      // 确保本地数据结构与响应保持一致
      if (responseData.sceneA) {
        currentConfig.sceneA = responseData.sceneA
        currentConfig.bridgeA = responseData.sceneA
      }
      
      if (responseData.sceneB) {
        currentConfig.sceneB = responseData.sceneB
        currentConfig.bridgeB = responseData.sceneB
      }
      
      hasUnsavedChanges.value = false
      isInitializing.value = false

      console.log('配置保存成功:', responseData)
      return true
    } else {
      console.error('保存配置失败:', response)
      ElMessage.error(`保存失败: ${response.message || '未知错误'}`)
      return false
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`)
    isLoading.value = false
    return false
  } finally {
    isLoading.value = false
  }
}

// 导出配置数据到剪贴板
async function exportConfigData(configId) {
  try {
    let exportConfig;
    
    if (configId && configId !== currentConfig.id) {
      // 导出指定配置
      const config = configs.value.find(c => c.id === configId);
      if (!config) {
        throw new Error('未找到指定的配置');
      }
      exportConfig = { ...config };
    } else {
      // 导出当前配置，先保存
      await saveCurrentConfig();
      exportConfig = { ...currentConfig };
    }
    
    const jsonStr = JSON.stringify(exportConfig, null, 2);
    
    // 复制到剪贴板
    await window.pywebview.api.copy_to_clipboard(jsonStr);
    
    ElMessage({
      message: `配置 "${exportConfig.name}" 已复制到剪贴板`,
      type: 'success',
      duration: 2000
    });
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败: ' + (error.message || '复制到剪贴板失败，请检查浏览器权限'));
  }
}

// 导入配置数据
function importConfigData() {
  importJsonText.value = ''
  importError.value = ''
  importDialogVisible.value = true
}

// 处理导入数据
async function processImportData() {
  importError.value = ''
  importLoading.value = true
  
  try {
    // 验证输入
    if (!importJsonText.value.trim()) {
      importError.value = '请输入JSON配置'
      importLoading.value = false
      return
    }
    
    // 解析JSON
    const importData = JSON.parse(importJsonText.value)
    
    // 验证导入数据格式
    if (!importData.bridgeA && !importData.sceneA && !importData.bridgeB && !importData.sceneB) {
      importError.value = '导入的数据格式不正确，缺少必要的剧情信息'
      importLoading.value = false
      return
    }
    
    // 确保数据结构一致性
    const normalizedData = {
      ...importData,
      // 优先使用bridgeX字段，如果没有则使用sceneX
      bridgeA: importData.bridgeA || importData.sceneA || {},
      bridgeB: importData.bridgeB || importData.sceneB || {},
      // 同时确保sceneX字段存在，用于后端兼容
      sceneA: importData.sceneA || importData.bridgeA || {},
      sceneB: importData.sceneB || importData.bridgeB || {}
    }
    
    // 更新当前配置
    Object.assign(currentConfig, normalizedData)
    
    // 如果没有ID，生成一个
    if (!currentConfig.id) {
      currentConfig.id = 'scene_' + Date.now()
    }
    
    // 保存导入的配置
    const saved = await saveCurrentConfig()
    
    if (saved) {
      // 关闭导入对话框
      importDialogVisible.value = false
      ElMessage.success('配置导入成功')
    } else {
      importError.value = '保存导入的配置失败，请检查数据格式'
    }
    
    importLoading.value = false
  } catch (error) {
    console.error('导入失败:', error)
    importError.value = error.message || '无法解析JSON数据'
    importLoading.value = false
  }
}

// 处理向后兼容性，将老数据格式转换为新格式
function ensureCompatibleFormat() {
  // 如果存在老版本的sceneA和sceneB数据但没有bridgeA和bridgeB
  if (currentConfig.sceneA && !currentConfig.bridgeA) {
    currentConfig.bridgeA = currentConfig.sceneA
  }
  
  if (currentConfig.sceneB && !currentConfig.bridgeB) {
    currentConfig.bridgeB = currentConfig.sceneB
  }
  
  // 确保有正确的基本结构
  if (!currentConfig.bridgeA) {
    currentConfig.bridgeA = {
      "读者感受/效果":[{"效果":"","手段":""}],
      "人物":[],
      "行动":"",
      "行动影响":[],
      "反转":"",
      "反套路":[]
    }
  }
  
  if (!currentConfig.bridgeB) {
    currentConfig.bridgeB = {
      "读者感受/效果":[{"效果":"","手段":""}],
      "人物":[],
      "行动":"",
      "行动影响":[],
      "反转":"",
      "反套路":[]
    }
  }
  
  // 确保guidelines字段存在
  if (!currentConfig.guidelines) {
    currentConfig.guidelines = []
  }
}

// 添加简单数组项目
function addArrayItem(scene, key) {
  if (Array.isArray(currentConfig[scene][key])) {
    currentConfig[scene][key].push('')
  }
}

// 删除简单数组项目
function removeArrayItem(scene, key, index) {
  if (Array.isArray(currentConfig[scene][key])) {
    currentConfig[scene][key].splice(index, 1)
  }
}

// 添加对象数组项目
function addObjectItem(scene, key) {
  if (Array.isArray(currentConfig[scene][key]) && currentConfig[scene][key].length > 0) {
    // 复制第一个对象的结构，但所有值设为空字符串
    const templateObj = currentConfig[scene][key][0]
    const newObj = {}
    
    for (const prop in templateObj) {
      newObj[prop] = ''
    }
    
    currentConfig[scene][key].push(newObj)
  }
}

// 删除对象数组项目
function removeObjectItem(scene, key, index) {
  if (Array.isArray(currentConfig[scene][key])) {
    currentConfig[scene][key].splice(index, 1)
    
    // 如果删除了最后一个元素，确保保留至少一个空对象作为模板
    if (currentConfig[scene][key].length === 0 && key === "读者感受/效果") {
      currentConfig[scene][key].push({"效果":"","手段":""})
    }
  }
}

// 添加对象属性
function addObjectProp(scene, key) {
  if (typeof currentConfig[scene][key] === 'object' && currentConfig[scene][key] !== null && !Array.isArray(currentConfig[scene][key])) {
    // 弹出输入框让用户输入属性名
    ElMessageBox.prompt('请输入属性名', '添加属性', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /\S+/,
      inputErrorMessage: '属性名不能为空'
    }).then(({ value }) => {
      // 确保属性名不重复
      if (value in currentConfig[scene][key]) {
        ElMessage.warning('属性名已存在');
        return;
      }
      
      // 使用Vue.set确保响应式更新
      currentConfig[scene][key][value] = '';
      
      ElMessage({
        type: 'success',
        message: '属性添加成功'
      });
    }).catch(() => {
      // 用户取消操作
    });
  }
}

// 删除对象属性
function removeObjectProp(scene, key, propKey) {
  if (typeof currentConfig[scene][key] === 'object' && currentConfig[scene][key] !== null && !Array.isArray(currentConfig[scene][key])) {
    // 确认删除
    ElMessageBox.confirm(
      `确定要删除属性 "${propKey}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      // 删除属性
      delete currentConfig[scene][key][propKey];
      
      // 如果删除后对象为空，添加一个默认属性
      if (Object.keys(currentConfig[scene][key]).length === 0) {
        currentConfig[scene][key]['默认'] = '';
      }
      
      ElMessage({
        type: 'success',
        message: '属性已删除'
      });
    }).catch(() => {
      // 用户取消操作
    });
  }
}

// 判断字段是否为数组类型（支持用户添加新项目）
function isArrayType(key) {
  // 对于数组类型的字段，都应该支持添加新项目
  // 检查当前配置中该字段是否为数组
  const bridgeAValue = currentConfig.bridgeA && currentConfig.bridgeA[key]
  const bridgeBValue = currentConfig.bridgeB && currentConfig.bridgeB[key]

  return Array.isArray(bridgeAValue) || Array.isArray(bridgeBValue)
}

// 判断是否为用户添加的字段（可以删除）
function isUserAddedField(scene, key, propKey) {
  // 获取当前使用的配置模板
  const currentTemplate = configs.value.find(config => config.id === currentConfig.id)

  if (!currentTemplate) {
    return true // 如果找不到模板，默认允许删除
  }

  // 检查该字段是否在原始模板中存在
  const templateValue = currentTemplate[scene] && currentTemplate[scene][key]

  if (!templateValue || typeof templateValue !== 'object' || Array.isArray(templateValue)) {
    return true // 如果模板中不是对象类型，允许删除
  }

  // 如果属性在模板中不存在，说明是用户添加的
  return !(propKey in templateValue)
}

// 交换A和B剧情数据
function exchangeABData() {
  ElMessageBox.confirm(
    '确定要交换A和B剧情的数据吗？此操作将互换两侧的所有内容。',
    '交换剧情数据',
    {
      confirmButtonText: '确认交换',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      // 交换bridgeA和bridgeB
      const tempA = JSON.parse(JSON.stringify(currentConfig.bridgeA))
      const tempB = JSON.parse(JSON.stringify(currentConfig.bridgeB))
      
      currentConfig.bridgeA = tempB
      currentConfig.bridgeB = tempA
      
      // 自动保存配置
      saveCurrentConfig()
      
      // 显示成功提示
      ElMessage({
        type: 'success',
        message: 'A/B剧情数据交换成功',
        duration: 2000
      })
    })
    .catch(() => {
      // 用户取消操作
      ElMessage({
        type: 'info',
        message: '已取消交换操作',
        duration: 1500
      })
    })
}

// 打开配置编辑器
function openConfigEditor(configId, configData = null) {
  // 如果传入了配置ID，查找对应配置；否则使用当前配置
  let config = configData;
  
  if (!config && configId) {
    config = configs.value.find(c => c.id === configId);
  }
  
  if (!config) {
    config = currentConfig;
  }
  
  // 深拷贝配置，避免直接修改
  editingConfig.id = config.id;
  editingConfig.name = config.name;
  editingConfig.description = config.description || '';
  
  // 如果是新配置，设置桥接数据
  if (configData) {
    editingConfig.bridgeA = configData.bridgeA || {};
    editingConfig.bridgeB = configData.bridgeB || {};
  } else {
    editingConfig.bridgeA = {};
    editingConfig.bridgeB = {};
  }
  
  // 初始化结构字段
  initStructureFields(config);
  
  // 显示编辑器
  configEditorVisible.value = true;
}

// 初始化结构字段
function initStructureFields(config) {
  // 清空原有字段
  Object.keys(structureFields).forEach(key => {
    delete structureFields[key];
  });
  
  // 获取源数据结构
  const sourceStructure = 
    Object.keys(config.bridgeA || {}).length > 0 
    ? config.bridgeA 
    : config.bridgeB;
  
  // 将数据结构转换为字段
  let fieldId = 1;
  Object.keys(sourceStructure || {}).forEach(key => {
    const value = sourceStructure[key];
    const fieldKey = `field_${fieldId++}`;
    
    if (typeof value === 'string') {
      structureFields[fieldKey] = {
        name: key,
        type: 'string'
      };
    } 
    else if (typeof value === 'number') {
      structureFields[fieldKey] = {
        name: key,
        type: 'number'
      };
    }
    else if (typeof value === 'boolean') {
      structureFields[fieldKey] = {
        name: key,
        type: 'boolean'
      };
    }
    else if (Array.isArray(value)) {
      structureFields[fieldKey] = {
        name: key,
        type: 'array',
        children: {}
      };
      
      // 如果数组有对象元素，添加子字段
      if (value.length > 0 && typeof value[0] === 'object') {
        let childId = 1;
        Object.keys(value[0] || {}).forEach(propKey => {
          const childKey = `child_${childId++}`;
          const propValue = value[0][propKey];
          structureFields[fieldKey].children[childKey] = {
            name: propKey,
            type: typeof propValue
          };
        });
      }
    }
    else if (typeof value === 'object' && value !== null) {
      structureFields[fieldKey] = {
        name: key,
        type: 'object',
        children: {}
      };
      
      // 处理对象的子字段
      let childId = 1;
      Object.keys(value).forEach(propKey => {
        const childKey = `child_${childId++}`;
        const propValue = value[propKey];
        structureFields[fieldKey].children[childKey] = {
          name: propKey,
          type: typeof propValue === 'object' && propValue !== null && !Array.isArray(propValue) ? 'object' : 
                Array.isArray(propValue) ? 'array' : typeof propValue
        };
        
        // 如果子字段也是对象，递归处理其子字段
        if (typeof propValue === 'object' && propValue !== null && !Array.isArray(propValue)) {
          structureFields[fieldKey].children[childKey].children = {};
          let nestedId = 1;
          
          Object.keys(propValue).forEach(nestedKey => {
            const nestedChildKey = `nested_${nestedId++}`;
            const nestedValue = propValue[nestedKey];
            structureFields[fieldKey].children[childKey].children[nestedChildKey] = {
              name: nestedKey,
              type: typeof nestedValue
            };
          });
        }
        
        // 如果子字段是数组且包含对象，处理第一个对象的结构
        if (Array.isArray(propValue) && propValue.length > 0 && typeof propValue[0] === 'object') {
          structureFields[fieldKey].children[childKey].children = {};
          let nestedId = 1;
          
          Object.keys(propValue[0]).forEach(nestedKey => {
            const nestedChildKey = `nested_${nestedId++}`;
            const nestedValue = propValue[0][nestedKey];
            structureFields[fieldKey].children[childKey].children[nestedChildKey] = {
              name: nestedKey,
              type: typeof nestedValue
            };
          });
        }
      });
    }
  });
}

// 添加根字段
function addRootField() {
  // 生成唯一ID
  const fieldId = `field_${Date.now()}`;
  
  // 添加新字段
  structureFields[fieldId] = {
    name: '新字段',
    type: 'string'
  };
}

// 更新字段名称
function updateFieldName(fieldId, newName) {
  if (structureFields[fieldId]) {
    structureFields[fieldId].name = newName;
  }
}

// 更新字段类型
function updateFieldType(fieldId, newType) {
  if (structureFields[fieldId]) {
    const field = structureFields[fieldId];
    const oldType = field.type;
    field.type = newType;
    
    // 对象或数组类型需要添加children属性
    if (newType === 'object' || newType === 'array') {
      if (!field.children) {
        field.children = {};
      }
      
      // 自动添加一个默认子字段
      if (Object.keys(field.children).length === 0) {
        const childId = `child_${Date.now()}`;
        field.children[childId] = {
          name: newType === 'object' ? '默认字段' : '项目',
          type: 'string'
        };
        
        // 显示提示消息
        ElMessage({
          message: `已自动添加一个默认${newType === 'object' ? '字段' : '项目'}，您可以根据需要修改`,
          type: 'info',
          duration: 3000
        });
      }
    } else {
      // 其他类型删除children属性
      if (oldType === 'object' || oldType === 'array') {
        // 如果之前是复杂类型，提示用户数据会丢失
        ElMessageBox.confirm(
          '切换为简单类型将丢失所有子字段数据，确定要继续吗？',
          '类型转换警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          // 用户确认后删除children
          delete field.children;
        }).catch(() => {
          // 用户取消，恢复原来的类型
          field.type = oldType;
        });
      } else {
        delete field.children;
      }
    }
  }
}

// 移除字段
function removeField(fieldId) {
  // 确认删除
  ElMessageBox.confirm(
    `确定删除此字段吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 执行删除
    delete structureFields[fieldId];
    
    ElMessage({
      message: '字段已删除',
      type: 'success',
      duration: 1500
    });
  });
}

// 添加子字段
function addChildField(parentId) {
  if (structureFields[parentId] && 
      (structureFields[parentId].type === 'object' || 
       structureFields[parentId].type === 'array')) {
    
    // 确保有children属性
    if (!structureFields[parentId].children) {
      structureFields[parentId].children = {};
    }
    
    // 生成唯一ID
    const childId = `child_${Date.now()}`;
    
    // 添加子字段
    structureFields[parentId].children[childId] = {
      name: '子字段',
      type: 'string'
    };
  }
}

// 更新子字段名称
function updateChildFieldName(parentId, childId, newName) {
  if (structureFields[parentId] && 
      structureFields[parentId].children && 
      structureFields[parentId].children[childId]) {
    
    structureFields[parentId].children[childId].name = newName;
  }
}

// 更新子字段类型
function updateChildFieldType(parentId, childId, newType) {
  if (structureFields[parentId] && 
      structureFields[parentId].children && 
      structureFields[parentId].children[childId]) {
    
    const child = structureFields[parentId].children[childId];
    child.type = newType;
    
    // 对象或数组类型需要添加children属性
    if (newType === 'object' || newType === 'array') {
      if (!child.children) {
        child.children = {};
      }
    } else {
      // 其他类型删除children属性
      delete child.children;
    }
  }
}

// 删除子字段
function removeChildField(parentId, childId) {
  if (structureFields[parentId] && 
      structureFields[parentId].children && 
      structureFields[parentId].children[childId]) {
    
    // 执行删除
    delete structureFields[parentId].children[childId];
    
    ElMessage({
      message: '子字段已删除',
      type: 'success',
      duration: 1500
    });
  }
}

// 添加深度合并对象的函数
function deepMerge(target, source) {
  // 处理基本类型或者source为null的情况
  if (typeof source !== 'object' || source === null) {
    return source;
  }
  
  // 处理数组
  if (Array.isArray(source)) {
    return [...source]; // 返回数组的拷贝
  }
  
  // 处理对象
  const merged = { ...(typeof target === 'object' && target !== null ? target : {}) };
  
  for (const key in source) {
    // 如果目标对象中也有这个键，并且两者都是对象，进行递归合并
    if (typeof merged[key] === 'object' && merged[key] !== null &&
        typeof source[key] === 'object' && source[key] !== null &&
        !Array.isArray(source[key]) && !Array.isArray(merged[key])) {
      merged[key] = deepMerge(merged[key], source[key]);
    } else {
      // 否则直接覆盖
      merged[key] = source[key];
    }
  }
  
  return merged;
}

// 保存配置编辑
async function saveConfigEdits() {
  // 验证配置
  const trimmedName = editingConfig.name.trim();
  if (!trimmedName) {
    ElMessage.warning('请输入模板名称');
    return;
  }
  
  // 检查名称是否重复
  const isDuplicate = configs.value.some(config => 
    config.name === trimmedName && config.id !== editingConfig.id
  );
  
  if (isDuplicate) {
    ElMessage.warning('模板名称已存在，请使用其他名称');
    return;
  }
  
  // 构建A和B剧情结构
  const structureA = {};
  const structureB = {};
  
  try {
    // 将字段信息转换为实际数据结构
    processStructureFields(structureFields, structureA, structureB);
    
    // 创建配置对象
    const configToSave = {
      id: editingConfig.id,
      name: trimmedName,  // 使用已验证的名称
      description: editingConfig.description.trim(),
      bridgeA: structureA,
      bridgeB: structureB
    };
    
    // 如果是新配置或者非当前配置，直接保存
    if (configToSave.id !== currentConfig.id) {
      // 保存到后端
      const response = await window.pywebview.api.book_controller.save_bridge_config(configToSave.id, configToSave);
      
      // 处理响应
      const result = typeof response === 'string' ? JSON.parse(response) : response;
      
      if (result.success || result.status === 'success') {
        // 更新本地列表
        const index = configs.value.findIndex(c => c.id === configToSave.id);
        if (index !== -1) {
          configs.value[index] = { ...configToSave };
        } else {
          configs.value.push({ ...configToSave });
        }
        
        configEditorVisible.value = false;
        ElMessage.success('配置保存成功');
      } else {
        throw new Error(result.message || '保存失败');
      }
    } else {
      // 如果是当前配置，更新当前配置并保存
      currentConfig.name = trimmedName;
      currentConfig.description = editingConfig.description.trim();
      
      // 处理 bridgeA
      // 不再使用深度合并，而是直接替换配置，这样删除的字段会确实被移除
      // 保留原始数据中用户已经输入的内容
      const updatedBridgeA = {};
      const updatedBridgeB = {};
      
      // 遍历新结构中的每个字段
      Object.keys(structureA).forEach(key => {
        if (typeof structureA[key] === 'object' && !Array.isArray(structureA[key])) {
          // 对于对象类型，初始化为新结构，但保留原始值
          updatedBridgeA[key] = {};
          Object.keys(structureA[key]).forEach(subKey => {
            // 如果原始配置中有该字段，则保留其值
            updatedBridgeA[key][subKey] = currentConfig.bridgeA && 
                                        currentConfig.bridgeA[key] && 
                                        currentConfig.bridgeA[key][subKey] !== undefined ? 
                                        currentConfig.bridgeA[key][subKey] : 
                                        structureA[key][subKey];
          });
        } else if (Array.isArray(structureA[key])) {
          // 对于数组类型，保留原始数组如果存在
          updatedBridgeA[key] = currentConfig.bridgeA && 
                              currentConfig.bridgeA[key] ? 
                              [...currentConfig.bridgeA[key]] : 
                              [...structureA[key]];
        } else {
          // 简单类型值
          updatedBridgeA[key] = currentConfig.bridgeA && 
                              currentConfig.bridgeA[key] !== undefined ? 
                              currentConfig.bridgeA[key] : 
                              structureA[key];
        }
      });
      
      // 同样处理 bridgeB
      Object.keys(structureB).forEach(key => {
        if (typeof structureB[key] === 'object' && !Array.isArray(structureB[key])) {
          // 对于对象类型，初始化为新结构，但保留原始值
          updatedBridgeB[key] = {};
          Object.keys(structureB[key]).forEach(subKey => {
            // 如果原始配置中有该字段，则保留其值
            updatedBridgeB[key][subKey] = currentConfig.bridgeB && 
                                        currentConfig.bridgeB[key] && 
                                        currentConfig.bridgeB[key][subKey] !== undefined ? 
                                        currentConfig.bridgeB[key][subKey] : 
                                        structureB[key][subKey];
          });
        } else if (Array.isArray(structureB[key])) {
          // 对于数组类型，保留原始数组如果存在
          updatedBridgeB[key] = currentConfig.bridgeB && 
                              currentConfig.bridgeB[key] ? 
                              [...currentConfig.bridgeB[key]] : 
                              [...structureB[key]];
        } else {
          // 简单类型值
          updatedBridgeB[key] = currentConfig.bridgeB && 
                              currentConfig.bridgeB[key] !== undefined ? 
                              currentConfig.bridgeB[key] : 
                              structureB[key];
        }
      });
      
      // 更新配置
      currentConfig.bridgeA = updatedBridgeA;
      currentConfig.bridgeB = updatedBridgeB;
      
      // 保存配置
      const success = await saveCurrentConfig();
      
      if (success) {
        configEditorVisible.value = false;
        ElMessage.success('配置保存成功');
      } else {
        ElMessage.error('保存配置失败');
      }
    }
  } catch (error) {
    console.error('处理配置数据失败', error);
    ElMessage.error('配置保存失败: ' + (error.message || '配置格式错误，请检查字段设置'));
  }
}

// 递归处理字段结构转换为数据
function processStructureFields(fields, targetA, targetB) {
  // 使用有序的处理方式确保字段顺序保持一致
  const orderedFields = Object.entries(fields).map(([key, field]) => {
    return { key, ...field };
  });
  
  // 处理每个字段
  orderedFields.forEach(field => {
    if (!field.name || field.name.trim() === '') {
      return; // 跳过没有名称的字段
    }
    
    const fieldName = field.name.trim();
    
    if (field.type === 'string') {
      targetA[fieldName] = '';
      targetB[fieldName] = '';
    } 
    else if (field.type === 'number') {
      targetA[fieldName] = 0;
      targetB[fieldName] = 0;
    }
    else if (field.type === 'boolean') {
      targetA[fieldName] = false;
      targetB[fieldName] = false;
    }
    else if (field.type === 'object') {
      // 创建空对象
      targetA[fieldName] = {};
      targetB[fieldName] = {};
      
      // 递归处理子字段
      if (field.children && Object.keys(field.children).length > 0) {
        processStructureFields(field.children, targetA[fieldName], targetB[fieldName]);
      }
      
      // 如果对象没有子字段，添加一个默认字段防止空对象被忽略
      if (Object.keys(targetA[fieldName]).length === 0) {
        targetA[fieldName]['默认'] = '';
        targetB[fieldName]['默认'] = '';
      }
    }
    else if (field.type === 'array') {
      // 初始化为空数组
      targetA[fieldName] = [];
      targetB[fieldName] = [];
      
      // 如果有子字段，创建一个包含这些字段的对象
      if (field.children && Object.keys(field.children).length > 0) {
        const itemObjA = {};
        const itemObjB = {};
        
        // 递归处理子字段
        processStructureFields(field.children, itemObjA, itemObjB);
        
        // 添加到数组中
        if (Object.keys(itemObjA).length > 0) {
          targetA[fieldName].push(itemObjA);
          targetB[fieldName].push(itemObjB);
        } else {
          // 如果没有子字段，添加一个空字符串作为默认值
          targetA[fieldName].push('');
          targetB[fieldName].push('');
        }
      }
    }
  });
}





// 编辑铺垫指南 - 使用铺垫弹窗
const editGuideline = (index) => {
  const guideline = currentConfig.guidelines[index]
  
  // 填充表单数据
  newGuideline.title = guideline.title || ''
  newGuideline.description = guideline.description || ''
  newGuideline.type = guideline.type || '线索铺垫'
  newGuideline.priority = guideline.priority || 'normal'
  
  // 填充答案数据（如果存在）
  if (guideline.answers) {
    Object.keys(newGuideline.answers).forEach(key => {
      newGuideline.answers[key] = guideline.answers[key] || ''
    })
  } else {
    // 如果没有结构化答案，清空所有答案
    Object.keys(newGuideline.answers).forEach(key => {
      newGuideline.answers[key] = ''
    })
  }
  
  // 设置为编辑模式
  isEditMode.value = true
  editingGuidelineIndex.value = index
  
  // 显示对话框
  addGuidelineDialogVisible.value = true
}

// 删除铺垫指南
const removeGuideline = (index) => {
  ElMessageBox.confirm(
    '确定要删除这条铺垫指南吗？',
    '删除确认',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    currentConfig.guidelines.splice(index, 1)
    ElMessage.success('铺垫指南已删除')
  }).catch(() => {})
}

// 添加嵌套子字段
function addNestedChildField(parentId, childId) {
  if (structureFields[parentId] && 
      structureFields[parentId].children && 
      structureFields[parentId].children[childId]) {
    
    const childField = structureFields[parentId].children[childId];
    
    // 确保子字段是对象或数组类型
    if (childField.type !== 'object' && childField.type !== 'array') {
      return;
    }
    
    // 确保有children属性
    if (!childField.children) {
      childField.children = {};
    }
    
    // 生成唯一ID
    const nestedChildId = `nested_${Date.now()}`;
    
    // 添加嵌套子字段
    childField.children[nestedChildId] = {
      name: '嵌套字段',
      type: 'string'
    };
  }
}

// 更新嵌套子字段名称
function updateNestedFieldName(parentId, childId, nestedKey, newName) {
  if (structureFields[parentId] && 
      structureFields[parentId].children && 
      structureFields[parentId].children[childId] && 
      structureFields[parentId].children[childId].children && 
      structureFields[parentId].children[childId].children[nestedKey]) {
    
    structureFields[parentId].children[childId].children[nestedKey].name = newName;
  }
}

// 更新嵌套子字段类型
function updateNestedFieldType(parentId, childId, nestedKey, newType) {
  if (structureFields[parentId] && 
      structureFields[parentId].children && 
      structureFields[parentId].children[childId] && 
      structureFields[parentId].children[childId].children && 
      structureFields[parentId].children[childId].children[nestedKey]) {
    
    structureFields[parentId].children[childId].children[nestedKey].type = newType;
  }
}

// 删除嵌套子字段
function removeNestedField(parentId, childId, nestedKey) {
  if (structureFields[parentId] && 
      structureFields[parentId].children && 
      structureFields[parentId].children[childId] && 
      structureFields[parentId].children[childId].children && 
      structureFields[parentId].children[childId].children[nestedKey]) {
    
    // 执行删除
    delete structureFields[parentId].children[childId].children[nestedKey];
    
    ElMessage({
      message: '嵌套子字段已删除',
      type: 'success',
      duration: 1500
    });
  }
}

// 加载保存的设计
async function loadSavedDesign() {
  try {
    isLoading.value = true
    
    // 调用后端API加载设计
    let response = await window.pywebview.api.book_controller.load_scene_design(currentConfig.id)
    
    // 处理后端可能返回的JSON字符串
    if (typeof response === 'string') {
      try {
        response = JSON.parse(response)
      } catch (err) {
        console.error('解析后端响应失败:', err)
        isLoading.value = false
        return
      }
    }
    
    if (response.success && response.data) {
      // 更新当前配置
      if (response.data.bridgeA) {
        currentConfig.bridgeA = response.data.bridgeA
      } else if (response.data.sceneA) {
        currentConfig.bridgeA = response.data.sceneA
      }
      
      if (response.data.bridgeB) {
        currentConfig.bridgeB = response.data.bridgeB
      } else if (response.data.sceneB) {
        currentConfig.bridgeB = response.data.sceneB
      }
      
      if (response.data.guidelines) {
        currentConfig.guidelines = response.data.guidelines
      }
      
      ElMessage.success('设计已加载')
    }
  } catch (error) {
    console.error('加载设计失败', error)
    ElMessage.error('加载设计失败: ' + (error.message || '未知错误'))
  } finally {
    isLoading.value = false
  }
}

// 复制剧情内容
async function copySceneContent() {
  try {
    // 生成包含所有内容的文本
    let contentText = `# ${currentConfig.name || '剧情无痕设计'}\n\n`;
    
    // 添加A剧情内容
    contentText += `## 当前剧情 (A)\n\n`;
    Object.entries(currentConfig.bridgeA).forEach(([key, value]) => {
      contentText += `### ${key}\n`;
      
      if (typeof value === 'string') {
        contentText += `${value || '(空)'}\n\n`;
      } 
      else if (Array.isArray(value)) {
        if (value.length === 0) {
          contentText += `(空列表)\n\n`;
        } 
        else if (typeof value[0] === 'object') {
          // 对象数组
          value.forEach((item, index) => {
            contentText += `- 项目${index + 1}:\n`;
            Object.entries(item).forEach(([propKey, propValue]) => {
              contentText += `  * ${propKey}: ${propValue || '(空)'}\n`;
            });
            contentText += '\n';
          });
        } 
        else {
          // 简单数组
          value.forEach((item) => {
            contentText += `- ${item || '(空项)'}\n`;
          });
          contentText += '\n';
        }
      }
    });
    
    // 添加B剧情内容
    contentText += `## 目标剧情 (B)\n\n`;
    Object.entries(currentConfig.bridgeB).forEach(([key, value]) => {
      contentText += `### ${key}\n`;
      
      if (typeof value === 'string') {
        contentText += `${value || '(空)'}\n\n`;
      } 
      else if (Array.isArray(value)) {
        if (value.length === 0) {
          contentText += `(空列表)\n\n`;
        } 
        else if (typeof value[0] === 'object') {
          // 对象数组
          value.forEach((item, index) => {
            contentText += `- 项目${index + 1}:\n`;
            Object.entries(item).forEach(([propKey, propValue]) => {
              contentText += `  * ${propKey}: ${propValue || '(空)'}\n`;
            });
            contentText += '\n';
          });
        } 
        else {
          // 简单数组
          value.forEach((item) => {
            contentText += `- ${item || '(空项)'}\n`;
          });
          contentText += '\n';
        }
      }
    });
    
    // 添加铺垫内容
    contentText += `## 无痕铺垫\n\n`;
    if (currentConfig.guidelines && currentConfig.guidelines.length > 0) {
      currentConfig.guidelines.forEach((guideline, index) => {
        contentText += `### 铺垫 ${index + 1}: ${guideline.title || '无标题'}\n`;
        contentText += `- 类型: ${guideline.type || '未分类'}\n`;
        contentText += `- 优先级: ${guideline.priority === 'high' ? '高' : '普通'}\n`;
        contentText += `- 描述: ${guideline.description || '(无描述)'}\n\n`;
      });
    } else {
      contentText += '(暂无铺垫)\n\n';
    }
    
    // 复制到剪贴板
    await window.pywebview.api.copy_to_clipboard(contentText);
    
    ElMessage.success('内容已复制到剪贴板');
  } catch (error) {
    console.error('复制内容失败', error);
    ElMessage.error('复制到剪贴板失败，请检查浏览器权限');
  }
}

// 保存剧情设计
async function saveSceneDesign(skipDialog = false) {
  // 如果没有名称且尝试直接保存，则显示保存对话框
  if (!currentConfig.name && skipDialog) {
    saveDesignForm.name = '';
    saveDesignForm.description = currentConfig.description || '';
    saveDesignDialogVisible.value = true;
    return;
  }
  
  // 如果不跳过对话框，则显示保存对话框
  if (!skipDialog) {
    saveDesignForm.name = currentConfig.name || '';
    saveDesignForm.description = currentConfig.description || '';
    saveDesignDialogVisible.value = true;
    return;
  }
  
  try {
    isLoading.value = true;
    
    // 确保有时间戳和ID
    if (!currentConfig.timestamp) {
      currentConfig.timestamp = Date.now();
    }
    
    if (!currentConfig.id || currentConfig.id === 'scene_default') {
      currentConfig.id = 'design_' + Date.now();
    }
    
    // 构建要保存的数据
    const designData = {
      id: currentConfig.id,
      name: currentConfig.name || `设计_${new Date().toLocaleDateString()}`,
      description: currentConfig.description || '',
      bridgeA: currentConfig.bridgeA,
      bridgeB: currentConfig.bridgeB,
      guidelines: currentConfig.guidelines,
      timestamp: Date.now()
    };
    
    // 调用后端API保存设计
    const response = await window.pywebview.api.book_controller.save_scene_design(designData);
    
    if (!response.success) {
      throw new Error(response.message || '调用后端API保存设计失败');
    }
    
    // 更新当前配置ID
    currentConfigId.value = designData.id;
    selectedHistoryId.value = designData.id;
    
    // 重新加载历史设计列表
    await loadDesignHistory();
    
    // 重置未保存标志
    hasUnsavedChanges.value = false;
    isInitializing.value = false;

    ElMessage.success('设计已保存');
  } catch (error) {
    console.error('保存设计失败', error);
    ElMessage.error('保存失败: ' + (error.message || '未知错误'));
  } finally {
    isLoading.value = false;
  }
}

// 处理页面离开事件
function handleBeforeUnload(event) {
  if (hasUnsavedChanges.value) {
    // 提示用户有未保存的更改
    const message = '您有未保存的更改，确定离开吗？'
    event.returnValue = message // 标准
    return message // 兼容
  }
}

// 上移字段
function moveFieldUp(fieldId) {
  // 获取所有字段的键
  const keys = Object.keys(structureFields);
  const index = keys.indexOf(fieldId);
  
  if (index > 0) {
    // 获取前一个键
    const prevKey = keys[index - 1];
    
    // 创建一个新对象来重新排序
    const newStructureFields = {};
    
    // 遍历所有键并重新排序
    keys.forEach((key, i) => {
      if (i === index - 1) {
        // 前一个位置放当前字段
        newStructureFields[key] = {...structureFields[fieldId]};
      } else if (i === index) {
        // 当前位置放前一个字段
        newStructureFields[key] = {...structureFields[prevKey]};
      } else {
        // 其他字段保持不变
        newStructureFields[key] = structureFields[key];
      }
    });
    
    // 清空原对象
    Object.keys(structureFields).forEach(key => {
      delete structureFields[key];
    });
    
    // 填充新排序
    Object.keys(newStructureFields).forEach(key => {
      structureFields[key] = newStructureFields[key];
    });
    
    ElMessage.success('字段已上移');
  }
}

// 下移字段
function moveFieldDown(fieldId) {
  // 获取所有字段的键
  const keys = Object.keys(structureFields);
  const index = keys.indexOf(fieldId);
  
  if (index < keys.length - 1) {
    // 获取后一个键
    const nextKey = keys[index + 1];
    
    // 创建一个新对象来重新排序
    const newStructureFields = {};
    
    // 遍历所有键并重新排序
    keys.forEach((key, i) => {
      if (i === index) {
        // 当前位置放后一个字段
        newStructureFields[key] = {...structureFields[nextKey]};
      } else if (i === index + 1) {
        // 后一个位置放当前字段
        newStructureFields[key] = {...structureFields[fieldId]};
      } else {
        // 其他字段保持不变
        newStructureFields[key] = structureFields[key];
      }
    });
    
    // 清空原对象
    Object.keys(structureFields).forEach(key => {
      delete structureFields[key];
    });
    
    // 填充新排序
    Object.keys(newStructureFields).forEach(key => {
      structureFields[key] = newStructureFields[key];
    });
    
    ElMessage.success('字段已下移');
  }
}

// 上移子字段
function moveChildFieldUp(parentId, childId) {
  const parent = structureFields[parentId];
  if (!parent || !parent.children) return;
  
  // 获取所有子字段的键
  const keys = Object.keys(parent.children);
  const index = keys.indexOf(childId);
  
  if (index > 0) {
    // 获取前一个键
    const prevKey = keys[index - 1];
    
    // 创建一个新对象来重新排序
    const newChildren = {};
    
    // 遍历所有键并重新排序
    keys.forEach((key, i) => {
      if (i === index - 1) {
        // 前一个位置放当前字段
        newChildren[key] = {...parent.children[childId]};
      } else if (i === index) {
        // 当前位置放前一个字段
        newChildren[key] = {...parent.children[prevKey]};
      } else {
        // 其他字段保持不变
        newChildren[key] = parent.children[key];
      }
    });
    
    // 清空原对象
    Object.keys(parent.children).forEach(key => {
      delete parent.children[key];
    });
    
    // 填充新排序
    Object.keys(newChildren).forEach(key => {
      parent.children[key] = newChildren[key];
    });
    
    ElMessage.success('子字段已上移');
  }
}

// 下移子字段
function moveChildFieldDown(parentId, childId) {
  const parent = structureFields[parentId];
  if (!parent || !parent.children) return;
  
  // 获取所有子字段的键
  const keys = Object.keys(parent.children);
  const index = keys.indexOf(childId);
  
  if (index < keys.length - 1) {
    // 获取后一个键
    const nextKey = keys[index + 1];
    
    // 创建一个新对象来重新排序
    const newChildren = {};
    
    // 遍历所有键并重新排序
    keys.forEach((key, i) => {
      if (i === index) {
        // 当前位置放后一个字段
        newChildren[key] = {...parent.children[nextKey]};
      } else if (i === index + 1) {
        // 后一个位置放当前字段
        newChildren[key] = {...parent.children[childId]};
      } else {
        // 其他字段保持不变
        newChildren[key] = parent.children[key];
      }
    });
    
    // 清空原对象
    Object.keys(parent.children).forEach(key => {
      delete parent.children[key];
    });
    
    // 填充新排序
    Object.keys(newChildren).forEach(key => {
      parent.children[key] = newChildren[key];
    });
    
    ElMessage.success('子字段已下移');
  }
}

// 上移嵌套子字段
function moveNestedFieldUp(parentId, childId, nestedKey) {
  const parent = structureFields[parentId];
  if (!parent || !parent.children) return;
  
  const child = parent.children[childId];
  if (!child || !child.children) return;
  
  // 获取所有嵌套字段的键
  const keys = Object.keys(child.children);
  const index = keys.indexOf(nestedKey);
  
  if (index > 0) {
    // 获取前一个键
    const prevKey = keys[index - 1];
    
    // 创建一个新对象来重新排序
    const newNestedChildren = {};
    
    // 遍历所有键并重新排序
    keys.forEach((key, i) => {
      if (i === index - 1) {
        // 前一个位置放当前字段
        newNestedChildren[key] = {...child.children[nestedKey]};
      } else if (i === index) {
        // 当前位置放前一个字段
        newNestedChildren[key] = {...child.children[prevKey]};
      } else {
        // 其他字段保持不变
        newNestedChildren[key] = child.children[key];
      }
    });
    
    // 清空原对象
    Object.keys(child.children).forEach(key => {
      delete child.children[key];
    });
    
    // 填充新排序
    Object.keys(newNestedChildren).forEach(key => {
      child.children[key] = newNestedChildren[key];
    });
    
    ElMessage.success('嵌套字段已上移');
  }
}

// 下移嵌套子字段
function moveNestedFieldDown(parentId, childId, nestedKey) {
  const parent = structureFields[parentId];
  if (!parent || !parent.children) return;
  
  const child = parent.children[childId];
  if (!child || !child.children) return;
  
  // 获取所有嵌套字段的键
  const keys = Object.keys(child.children);
  const index = keys.indexOf(nestedKey);
  
  if (index < keys.length - 1) {
    // 获取后一个键
    const nextKey = keys[index + 1];
    
    // 创建一个新对象来重新排序
    const newNestedChildren = {};
    
    // 遍历所有键并重新排序
    keys.forEach((key, i) => {
      if (i === index) {
        // 当前位置放后一个字段
        newNestedChildren[key] = {...child.children[nextKey]};
      } else if (i === index + 1) {
        // 后一个位置放当前字段
        newNestedChildren[key] = {...child.children[nestedKey]};
      } else {
        // 其他字段保持不变
        newNestedChildren[key] = child.children[key];
      }
    });
    
    // 清空原对象
    Object.keys(child.children).forEach(key => {
      delete child.children[key];
    });
    
    // 填充新排序
    Object.keys(newNestedChildren).forEach(key => {
      child.children[key] = newNestedChildren[key];
    });
    
    ElMessage.success('嵌套字段已下移');
  }
}

// 处理更多操作下拉菜单
function handleExtraActions(command) {
  switch (command) {
    case 'copyJson':
      copyDesignJson();
      break;
    case 'importDesign':
      showImportDesignDialog();
      break;
    default:
      break;
  }
}

// 复制设计JSON到剪贴板
async function copyDesignJson() {
  try {
    // 准备要复制的设计数据
    const designData = {
      id: currentConfig.id,
      name: currentConfig.name,
      description: currentConfig.description || '',
      bridgeA: currentConfig.bridgeA,
      bridgeB: currentConfig.bridgeB,
      guidelines: currentConfig.guidelines,
      timestamp: Date.now()
    };
    
    // 转换为JSON字符串，使用2个空格缩进美化格式
    const jsonStr = JSON.stringify(designData, null, 2);
    
    // 复制到剪贴板
    await window.pywebview.api.copy_to_clipboard(jsonStr);
    
    ElMessage({
      message: '设计JSON已复制到剪贴板',
      type: 'success',
      duration: 2000
    });
  } catch (error) {
    console.error('复制设计JSON失败', error);
    ElMessage.error('复制到剪贴板失败，请检查浏览器权限');
  }
}

// 显示导入设计对话框
function showImportDesignDialog() {
  importDesignJsonText.value = '';
  importDesignError.value = '';
  importDesignDialogVisible.value = true;
}

// 处理导入设计JSON
async function processImportDesign() {
  importDesignError.value = '';
  importDesignLoading.value = true;
  
  try {
    // 验证输入
    if (!importDesignJsonText.value.trim()) {
      importDesignError.value = '请输入JSON设计数据';
      importDesignLoading.value = false;
      return;
    }
    
    // 解析JSON
    const importData = JSON.parse(importDesignJsonText.value);
    
    // 验证导入数据格式
    if (!importData.bridgeA && !importData.sceneA && !importData.bridgeB && !importData.sceneB) {
      importDesignError.value = '导入的数据格式不正确，缺少必要的剧情信息';
      importDesignLoading.value = false;
      return;
    }
    
    // 确保数据结构一致性
    const normalizedData = {
      ...importData,
      // 生成新的ID和时间戳，确保作为新设计
      id: 'design_' + Date.now(),
      timestamp: Date.now(),
      // 优先使用bridgeX字段，如果没有则使用sceneX
      bridgeA: importData.bridgeA || importData.sceneA || {},
      bridgeB: importData.bridgeB || importData.sceneB || {},
      // 确保guidelines字段存在
      guidelines: importData.guidelines || []
    };
    
    // 更新当前配置
    Object.assign(currentConfig, normalizedData);
    currentConfigId.value = normalizedData.id;
    
    // 保存导入的设计
    await saveSceneDesign(true);
    
    // 关闭导入对话框
    importDesignDialogVisible.value = false;
    
    ElMessage.success('设计导入成功并已保存');
    
  } catch (error) {
    console.error('导入设计失败:', error);
    importDesignError.value = error.message || '无法解析JSON数据';
  } finally {
    importDesignLoading.value = false;
  }
}
</script>

<style lang="scss" scoped>
// 自定义CSS变量
:root {
  --app-border-radius: 12px;
  --app-border-radius-small: 8px;
  --app-shadow-light: 0 4px 12px rgba(0, 0, 0, 0.08);
  --app-shadow-medium: 0 8px 24px rgba(0, 0, 0, 0.12);
  --app-shadow-heavy: 0 12px 32px rgba(0, 0, 0, 0.16);
  --app-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --app-font-size-large: 18px;
  --app-font-size-medium: 15px;
  --app-font-size-small: 14px;
}

.a2b-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background: linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-bg-color-page) 100%);
  color: var(--el-text-color-primary);
  position: relative; /* 用于定位抽屉面板 */
  user-select: none; /* 添加禁止选择文本 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: var(--app-font-size-medium);
  line-height: 1.6;

  // 强制应用 textarea 字体大小
  .el-textarea .el-textarea__inner {
    font-size: 20px !important;
    font-family: inherit !important;
  }

  // 确保所有 textarea 都应用大字体
  textarea {
    font-size: 20px !important;
    font-family: inherit !important;
  }

  // 自定义滚动条样式
  * {
    scrollbar-width: thin;
    scrollbar-color: var(--el-border-color) transparent;
  }

  *::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  *::-webkit-scrollbar-track {
    background: transparent;
  }

  *::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 4px;
    transition: background 0.3s ease;
  }

  *::-webkit-scrollbar-thumb:hover {
    background: var(--el-border-color-dark);
  }

  /* 工作流程指引样式 */
  .workflow-guide {
    padding: 20px 24px;
    background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-success-light-9) 100%);
    border-bottom: 1px solid var(--el-border-color-lighter);
    flex-shrink: 0;
    backdrop-filter: blur(10px);
    position: relative;

    // 为深色主题添加覆盖层
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--el-bg-color-overlay);
      opacity: 0.8;
      pointer-events: none;
      z-index: 0;
    }

    .guide-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 24px;
      max-width: 900px;
      margin: 0 auto;
      position: relative;
      z-index: 1;
    }

    .guide-step {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 20px;
      background: var(--el-bg-color-overlay);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 24px;
      box-shadow: var(--el-box-shadow-light);
      transition: all 0.3s ease;
      backdrop-filter: blur(8px);

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--el-box-shadow);
        border-color: var(--el-border-color);
        background: var(--el-bg-color);
      }

      .step-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
        color: white;
        border-radius: 50%;
        font-size: 14px;
        font-weight: 700;
        box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.3);
      }

      .step-text {
        font-size: 15px;
        color: var(--el-text-color-primary);
        font-weight: 600;
      }
    }

    .guide-arrow {
      color: var(--el-color-primary);
      font-size: 20px;
      font-weight: 700;
      opacity: 0.8;
    }
  }

  .app-header {
    display: flex;
    flex-direction: column;
    padding: 0;
    background: var(--el-bg-color-overlay);
    border-bottom: 1px solid var(--el-border-color-light);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .header-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      
      .header-title {
        display: flex;
        align-items: center;
        gap: 15px;

        .title-main {
          h2 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-success));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.5px;
          }

          .title-subtitle {
            font-size: 15px;
            color: var(--el-text-color-secondary);
            margin-top: 4px;
            font-weight: 500;
          }
        }
        
        h2 {
          margin: 0;
          font-size: 22px;
          color: var(--el-text-color-primary);
        }
        
        .history-dropdown {
          margin-left: 10px;
          
          .design-info {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 6px 12px;
            border-radius: 4px;
            background-color: var(--el-color-primary-light-9);
            color: var(--el-color-primary);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid var(--el-color-primary-light-5);
            
            &:hover {
              background-color: var(--el-color-primary-light-8);
            }
            
            .el-icon {
              margin-left: 4px;
              font-size: 14px;
            }
          }
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;

        .action-btn {
          padding: 12px 20px;
          font-weight: 600;
          font-size: 15px;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          border: none;
          
          .el-icon {
            margin-right: 4px;
            font-size: 16px;
          }
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
          
          &:active {
            transform: translateY(0);
          }
        }
      }
    }
    
    .config-dropdown {
      .el-dropdown-menu__item {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 14px;
        
        .el-icon {
          font-size: 15px;
        }
      }
    }
    
    .unsaved-indicator {
      margin-left: 5px;
      
      .el-icon {
        margin-right: 3px;
      }
    }
    
    /* 响应式布局 */
    @media (max-width: 768px) {
      .header-top {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        
        .header-title {
          width: 100%;
          flex-direction: column;
          align-items: flex-start;
          gap: 10px;
          
          h2 {
            margin-bottom: 5px;
          }
          
          .history-dropdown {
            margin-left: 0;
            width: 100%;
            
            .design-info {
              width: 100%;
              justify-content: space-between;
            }
          }
        }
        
        .header-actions {
          width: 100%;
          flex-wrap: wrap;
        }
      }
    }
  }
  
  .app-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    padding: 20px;
    padding-bottom: 40px; /* 为抽屉按钮预留空间，减少高度 */
  }
  
  .bridge-layout {
    display: flex;
    flex: 1;
    gap: 16px;
    margin-bottom: 24px;
    position: relative; /* 添加相对定位以支持按钮定位 */
    
    @media (max-width: 768px) {
      flex-direction: column;
    }
  
    .scene-panel {
      flex: 1;
      min-width: 0;
      background: var(--el-bg-color-overlay);
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      display: flex;
      flex-direction: column;
      position: relative;
      overflow: hidden;
      border: 1px solid var(--el-border-color-lighter);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
      }

      &:before {
        content: '';
        position: absolute;
        top: 0;
        height: 4px;
        left: 0;
        right: 0;
        z-index: 1;
      }

      &.scene-a:before {
        background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
      }

      &.scene-b:before {
        background: linear-gradient(135deg, var(--el-color-success), var(--el-color-success-light-3));
      }
      
      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        border-bottom: 1px solid var(--el-border-color-lighter);
        background: linear-gradient(135deg, var(--el-fill-color-extra-light), var(--el-fill-color-lighter));

        .panel-title-group {
          h3 {
            margin: 0;
            font-size: 22px;
            font-weight: 700;
            color: var(--el-text-color-primary);
            letter-spacing: -0.3px;
          }

          .panel-subtitle {
            font-size: 14px;
            color: var(--el-text-color-secondary);
            margin-top: 4px;
            font-weight: 500;
          }
        }

        .panel-indicator {
          .indicator-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;

            &.current {
              background: var(--el-color-primary);
              box-shadow: 0 0 8px rgba(var(--el-color-primary-rgb), 0.4);
            }

            &.target {
              background: var(--el-color-success);
              box-shadow: 0 0 8px rgba(var(--el-color-success-rgb), 0.4);
            }
          }
        }

        // 兼容旧的h3样式
        h3 {
          margin: 0;
          font-size: 20px;
        }
      }
      
      .panel-content {
        flex: 1;
        padding: 24px;
        overflow: auto;
        background: var(--el-bg-color);
      }

      .json-editor {
        display: flex;
        flex-direction: column;
        gap: 24px;

        .json-field {
          border: 1px solid var(--el-border-color-lighter);
          border-radius: 12px;
          padding: 20px;
          background: var(--el-bg-color-overlay);
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            border-color: var(--el-border-color);
            transform: translateY(-1px);
          }

          .field-header {
            font-weight: 700;
            margin-bottom: 16px;
            font-size: 18px;
            color: var(--el-color-primary);
            display: flex;
            align-items: center;
            gap: 8px;

            &:before {
              content: '';
              width: 4px;
              height: 18px;
              background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
              border-radius: 2px;
            }
          }
          
          .field-content {
            margin-top: 8px;
            white-space: pre-wrap; /* 支持换行显示 */
            word-break: break-word; /* 在单词内换行 */
            overflow-wrap: break-word; /* 确保长单词也能换行 */

            .el-textarea__inner {
              user-select: text; /* 允许选择文本区域内容 */
              font-size: 20px !important; /* 设置为20px并强制应用 */
              line-height: 1.6;
              padding: 16px;
              border-radius: 8px;
              border: 1px solid var(--el-border-color-lighter);
              background: var(--el-bg-color);
              transition: all 0.3s ease;

              &:hover {
                border-color: var(--el-border-color);
                background: var(--el-fill-color-extra-light);
              }

              &:focus {
                border-color: var(--el-color-primary);
                box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
              }
            }

            .el-input__inner {
              user-select: text; /* 允许选择输入框内容 */
              padding: 12px 16px;
              border-radius: 8px;
              border: 1px solid var(--el-border-color-lighter);
              transition: all 0.3s ease;

              &:hover {
                border-color: var(--el-border-color);
              }

              &:focus {
                border-color: var(--el-color-primary);
                box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
              }
            }
          }
          
          .array-editor {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .array-item {
              display: flex;
              gap: 12px;
              align-items: center;
              padding: 12px;
              background: var(--el-fill-color-extra-light);
              border: 1px solid var(--el-border-color-lighter);
              border-radius: var(--app-border-radius-small);
              transition: var(--app-transition);

              &:hover {
                background: var(--el-fill-color-lighter);
                border-color: var(--el-border-color);
                box-shadow: var(--app-shadow-light);
              }

              .el-input {
                flex: 1;
              }

              .el-button {
                flex-shrink: 0;
                width: 32px;
                height: 32px;
                padding: 0;
                border-radius: 50%;
                background: var(--el-color-danger-light-8);
                border-color: var(--el-color-danger-light-5);
                color: var(--el-color-danger);

                &:hover {
                  background: var(--el-color-danger);
                  border-color: var(--el-color-danger);
                  color: white;
                  transform: scale(1.1);
                }
              }
            }
            
            .add-item-btn {
              margin-top: 8px;
              align-self: flex-start;
            }
          }
          
          .object-array-editor {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .empty-array-tip {
              padding: 20px;
              text-align: center;
              border: 2px dashed var(--el-border-color-lighter);
              border-radius: var(--app-border-radius);
              background: var(--el-fill-color-extra-light);

              .el-empty {
                padding: 20px 0;
              }
            }

            .object-item {
              border: 1px solid var(--el-border-color-lighter);
              border-radius: var(--app-border-radius);
              padding: 20px;
              background: linear-gradient(135deg, var(--el-bg-color-overlay) 0%, var(--el-fill-color-extra-light) 100%);
              box-shadow: var(--app-shadow-light);
              transition: var(--app-transition);
              position: relative;

              &:hover {
                border-color: var(--el-border-color);
                box-shadow: var(--app-shadow-medium);
                transform: translateY(-2px);
              }

              &:before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(135deg, var(--el-color-warning-light-5), var(--el-color-warning-light-3));
                border-radius: var(--app-border-radius) var(--app-border-radius) 0 0;
              }

              .object-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;
                padding: 12px 16px;
                background: var(--el-bg-color-overlay);
                border-radius: var(--app-border-radius-small);
                border: 1px solid var(--el-border-color-lighter);
                font-weight: 700;
                font-size: 16px;
                color: var(--el-color-warning);

                .el-button {
                  width: 28px;
                  height: 28px;
                  padding: 0;
                  border-radius: 50%;
                  background: var(--el-color-danger-light-8);
                  border-color: var(--el-color-danger-light-5);
                  color: var(--el-color-danger);

                  &:hover {
                    background: var(--el-color-danger);
                    border-color: var(--el-color-danger);
                    color: white;
                    transform: scale(1.1);
                  }
                }
              }

              .object-prop {
                margin-bottom: 12px;
                padding: 12px;
                background: var(--el-fill-color-extra-light);
                border-radius: 6px;
                border: 1px solid var(--el-border-color-lighter);
                transition: var(--app-transition);

                &:hover {
                  background: var(--el-fill-color-lighter);
                  border-color: var(--el-border-color);
                }

                .prop-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-start;
                  margin-bottom: 8px;

                  .prop-title {
                    font-size: 14px;
                    font-weight: 500;
                    color: var(--el-text-color-primary);
                    line-height: 1.4;
                    flex: 1;
                    margin-right: 8px;
                  }

                  .prop-actions {
                    display: flex;
                    gap: 4px;
                    flex-shrink: 0;
                  }
                }

                .el-input {
                  width: 100%;
                }
              }
            }
            
            .add-item-btn {
              margin-top: 12px;
              align-self: flex-start;
              padding: 8px 16px;
              font-size: 13px;
              font-weight: 500;
              border-radius: 6px;
              background: linear-gradient(135deg, var(--el-color-primary-light-8), var(--el-color-primary-light-9));
              border: 1px dashed var(--el-color-primary-light-5);
              color: var(--el-color-primary);
              transition: var(--app-transition);
              display: flex;
              align-items: center;
              gap: 6px;

              &:hover {
                background: linear-gradient(135deg, var(--el-color-primary-light-7), var(--el-color-primary-light-8));
                border-color: var(--el-color-primary);
                transform: translateY(-1px);
                box-shadow: 0 3px 8px rgba(var(--el-color-primary-rgb), 0.2);
              }

              .el-icon {
                font-size: 14px;
                transition: var(--app-transition);
              }

              &:hover .el-icon {
                transform: rotate(90deg);
              }
            }
          }
          
          /* 纯对象编辑器样式 */
          .object-editor {
            display: flex;
            flex-direction: column;
            gap: 16px;
            padding: 20px;
            border: 1px solid var(--el-border-color-lighter);
            border-radius: var(--app-border-radius);
            background: linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-fill-color-extra-light) 100%);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
            position: relative;

            &:before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 3px;
              background: linear-gradient(135deg, var(--el-color-primary-light-5), var(--el-color-success-light-5));
              border-radius: var(--app-border-radius) var(--app-border-radius) 0 0;
            }
            
            .object-prop {
              display: flex;
              flex-direction: column;
              gap: 8px;
              margin-bottom: 16px;
              padding: 16px;
              background: var(--el-fill-color-extra-light);
              border: 1px solid var(--el-border-color-lighter);
              border-radius: var(--app-border-radius-small);
              transition: var(--app-transition);
              position: relative;

              &:hover {
                border-color: var(--el-border-color);
                background: var(--el-fill-color-lighter);
                box-shadow: var(--app-shadow-light);
              }

              .prop-actions {
                position: absolute;
                top: 8px;
                right: 8px;
                opacity: 0;
                transition: var(--app-transition);

                .el-button {
                  width: 24px;
                  height: 24px;
                  padding: 0;
                  border-radius: 50%;

                  &:hover {
                    transform: scale(1.1);
                  }

                  .el-icon {
                    font-size: 12px;
                  }
                }
              }

              &:hover .prop-actions {
                opacity: 1;
              }

              .el-input {
                .el-input__inner {
                  border-radius: 6px;
                  border: 1px solid var(--el-border-color-lighter);
                  background: var(--el-bg-color);
                  transition: var(--app-transition);

                  &:hover {
                    border-color: var(--el-border-color);
                  }

                  &:focus {
                    border-color: var(--el-color-primary);
                    box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
                  }
                }
              }
            }
            
            .add-item-btn {
              margin-top: 16px;
              align-self: flex-start;
              padding: 10px 20px;
              font-size: 14px;
              font-weight: 600;
              border-radius: var(--app-border-radius-small);
              background: linear-gradient(135deg, var(--el-color-success-light-8), var(--el-color-success-light-9));
              border: 1px dashed var(--el-color-success-light-5);
              color: var(--el-color-success);
              transition: var(--app-transition);
              display: flex;
              align-items: center;
              gap: 8px;

              &:hover {
                background: linear-gradient(135deg, var(--el-color-success-light-7), var(--el-color-success-light-8));
                border-color: var(--el-color-success);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(var(--el-color-success-rgb), 0.2);
              }

              .el-icon {
                font-size: 16px;
                transition: var(--app-transition);
              }

              &:hover .el-icon {
                transform: rotate(90deg);
              }
            }
          }
        }
      }
    }
  }

  /* 桥接连接器样式 */
  .bridge-connector {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    position: relative;

    .connector-line {
      width: 2px;
      height: 60px;
      background: linear-gradient(to bottom, var(--el-color-primary), var(--el-color-success));
      border-radius: 1px;
    }

    .connector-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 32px;
      height: 32px;
      background: var(--el-bg-color);
      border: 2px solid var(--el-color-primary);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--el-color-primary);
      font-size: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .connector-text {
      position: absolute;
      bottom: -25px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      font-weight: 500;
      white-space: nowrap;
    }
  }

  /* 抽屉面板样式 */
  .drawer-toggle {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 48px;
    background: var(--el-bg-color-overlay);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 10;
    border-top: 1px solid var(--el-border-color);
    transition: all 0.3s;

    .toggle-content {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 16px;
      border-radius: 24px;
      background: var(--el-color-primary-light-9);
      border: 1px solid var(--el-color-primary-light-5);
      transition: all 0.3s;

      &:hover {
        background: var(--el-color-primary-light-8);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
      }
    }

    .toggle-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      background: var(--el-color-primary);
      color: white;
      border-radius: 50%;
      font-size: 14px;
    }

    .toggle-text {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .toggle-title {
        font-size: 14px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .toggle-subtitle {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        margin-top: 1px;
      }
    }

    .toggle-badge {
      background: var(--el-color-primary);
      color: white;
      font-size: 12px;
      font-weight: 600;
      padding: 2px 8px;
      border-radius: 10px;
      min-width: 20px;
      text-align: center;
    }
    user-select: none;
    color: var(--el-text-color-primary); /* 使用主文本颜色 */
    
    &:hover {
      background: var(--el-color-primary-light-9); /* 更柔和的悬停颜色 */
      color: var(--el-color-primary); /* 悬停时使用主题色 */
    }
    
    span {
      margin-left: 8px;
      font-size: 15px;
    }
    
    .el-icon {
      transition: transform 0.3s;
      font-size: 18px; /* 减小图标大小 */
      
      &.is-rotate {
        transform: rotate(180deg);
      }
    }
  }
  
  .drawer-panel {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 0;
    background: var(--el-bg-color-overlay);
    z-index: 9;
    transition: height 0.3s ease-in-out;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    
    &.drawer-visible {
      height: 50vh; /* 增加展开高度为视口高度的50% */
      max-height: 600px; /* 增加最大高度 */
    }
    
    .drawer-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      padding: 0 20px;
    }
    
    .drawer-header {
      padding: 16px 0;
      border-bottom: 1px solid var(--el-border-color-light);
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .section-title {
        display: flex;
        align-items: center;
        gap: 12px;

        .title-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          background: var(--el-color-primary-light-9);
          border: 2px solid var(--el-color-primary-light-5);
          border-radius: 50%;
          color: var(--el-color-primary);
          font-size: 18px;
        }

        .title-content {
          h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }

          .title-description {
            font-size: 13px;
            color: var(--el-text-color-secondary);
            margin-top: 2px;
          }
        }


      }
      
      .add-guideline-btn {
        margin-left: auto;
      }
    }
  }
  
  .guidelines-content {
    padding: 16px 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow-y: auto;
    max-height: calc(50vh - 80px); /* 调整为与抽屉面板高度相适应 */
    position: relative; /* 添加相对定位，用于空状态的绝对定位 */
    min-height: 200px; /* 确保有足够的高度显示空状态 */
    
    @media (max-width: 480px) {
      padding: 12px 0;
    }
    
    .guideline-item {
      border: 1px solid var(--el-border-color);
      border-radius: 8px; /* 增加圆角 */
      padding: 16px; /* 增加内边距 */
      background: var(--el-bg-color);
      transition: all 0.3s;
      width: 100%;
      position: relative; /* 添加相对定位 */
      
      &:hover {
        box-shadow: var(--el-box-shadow);
        transform: translateY(-2px);
        border-color: var(--el-color-primary-light-5); /* 悬停时改变边框颜色 */
      }
      
      &.high-priority {
        border-left: 4px solid var(--el-color-danger);
        background-color: rgba(var(--el-color-danger-rgb), 0.02); /* 高优先级项目的背景色 */
      }
      
      .guideline-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 12px;
        
        .guideline-title {
          font-weight: 600;
          font-size: 17px;
          color: var(--el-color-primary-dark-2); /* 标题颜色加深 */
          flex: 1; /* 让标题占据剩余空间 */
        }
        
        .el-tag {
          font-size: 13px;
          padding: 0 10px;
          height: 26px;
          line-height: 26px;
          font-weight: 500;
        }
      }
      
      .guideline-description {
        color: var(--el-text-color-regular);
        margin-bottom: 5px;
        font-size: 15px;
        line-height: 1.6;
        padding-left: 2px;
        white-space: pre-line; /* 保留换行符 */
        user-select: text; /* 允许选择描述文本 */
      }
      
      /* 添加底部操作区 */
      .guideline-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 10px;
        opacity: 0;
        transition: opacity 0.2s;
      }
      
      &:hover .guideline-actions {
        opacity: 1;
      }
    }
    
    .el-empty {
      padding: 20px;
      position: absolute; /* 绝对定位 */
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%); /* 居中对齐 */
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: transparent;
      
      .empty-icon {
        font-size: 60px;
        color: var(--el-text-color-placeholder);
        margin-bottom: 10px; /* 增加图标与文字的间距 */
      }
      
      .el-empty__description {
        margin-top: 10px;
        font-size: 15px;
      }
    }
  }
  
  .import-dialog-content {
    .import-tip {
      margin-bottom: 12px;
      color: var(--el-text-color-secondary);
      font-size: 15px;
    }
    
    .import-help {
      margin-top: 12px;
      font-size: 15px;
      color: var(--el-text-color-secondary);
      
      p {
        margin: 4px 0;
      }
    }
    
    .import-error {
      margin-top: 12px;
    }
  }
  
  // 添加新的字段编辑相关样式
  .fields-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    
    .field-card {
      border: 1px solid var(--el-border-color);
      border-radius: 8px;
      padding: 15px;
      background-color: var(--el-bg-color);
      transition: all 0.2s;
      position: relative;
      
      &:hover {
        box-shadow: var(--el-box-shadow-light);
        border-color: var(--el-color-primary-light-7);
      }
      
      .field-order-badge {
        position: absolute;
        top: -10px;
        left: -10px;
        width: 24px;
        height: 24px;
        background-color: var(--el-color-primary);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        z-index: 1;
        
        &.child-badge {
          background-color: var(--el-color-success);
          width: 20px;
          height: 20px;
          font-size: 11px;
          top: -8px;
          left: -8px;
        }
        
        &.nested-badge {
          background-color: var(--el-color-warning);
          width: 18px;
          height: 18px;
          font-size: 10px;
          top: -6px;
          left: -6px;
        }
      }
      
      .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        
        .field-name-type {
          display: flex;
          gap: 10px;
          flex: 1;
          
          .el-input {
            max-width: 180px;
          }
          
          .el-select {
            width: 120px;
          }
        }
        
              .field-actions {
        display: flex;
        gap: 5px;
        
        .el-button {
          padding: 5px;
          
          &.el-button--info {
            background-color: var(--el-color-info-light-8);
            border-color: var(--el-color-info-light-5);
            color: var(--el-color-info);
            
            &:hover {
              background-color: var(--el-color-info-light-7);
              border-color: var(--el-color-info-light-4);
            }
            
            &:disabled {
              background-color: var(--el-fill-color-light);
              border-color: var(--el-border-color-lighter);
              color: var(--el-text-color-placeholder);
              cursor: not-allowed;
            }
          }
        }
      }
      }
      
      .field-children {
        margin-top: 15px;
        padding: 10px;
        background-color: var(--el-fill-color-light);
        border-radius: 6px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        
        .empty-children {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px;
          color: var(--el-text-color-secondary);
        }
        
        .child-field {
          background-color: var(--el-bg-color);
          border: 1px solid var(--el-border-color-lighter);
          border-radius: 4px;
          padding: 10px;
          position: relative;
          
          &:hover {
            border-color: var(--el-color-primary-light-7);
          }
          
          .nested-children {
            margin-top: 15px;
            padding: 10px;
            background-color: var(--el-fill-color);
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            

            
            .nested-field {
              background-color: var(--el-bg-color);
              border: 1px dashed var(--el-border-color);
              border-radius: 4px;
              padding: 10px;
              position: relative;
              
              &:hover {
                border-color: var(--el-color-primary-light-7);
              }
            }
          }
        }
      }
    }
  }
}

.guideline-form {
  .el-form-item {
    margin-bottom: 18px;
  }
  
  .full-width {
    width: 100%;
  }
  
  .form-header-section {
    margin-bottom: 24px;
    border-bottom: 1px solid var(--el-border-color-light);
    padding-bottom: 16px;
  }
  
  .form-section {
    margin-bottom: 24px;
  }
  
  .section-title {
    font-size: 18px;
    margin-bottom: 16px;
    color: var(--el-color-primary);
    position: relative;
    padding-left: 14px;
    
    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 4px;
      height: 18px;
      width: 4px;
      background: var(--el-color-primary);
      border-radius: 2px;
    }
  }
  

  

}

.el-button {
  font-size: 15px;
  font-weight: 500;
  border-radius: 8px;
  padding: 12px 20px;
  transition: all 0.3s ease;
  border: 1px solid transparent;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &.el-button--primary {
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
    border: none;

    &:hover {
      background: linear-gradient(135deg, var(--el-color-primary-dark-2), var(--el-color-primary));
    }
  }

  &.el-button--success {
    background: linear-gradient(135deg, var(--el-color-success), var(--el-color-success-light-3));
    border: none;

    &:hover {
      background: linear-gradient(135deg, var(--el-color-success-dark-2), var(--el-color-success));
    }
  }

  &.el-button--warning {
    background: linear-gradient(135deg, var(--el-color-warning), var(--el-color-warning-light-3));
    border: none;

    &:hover {
      background: linear-gradient(135deg, var(--el-color-warning-dark-2), var(--el-color-warning));
    }
  }

  &.el-button--danger {
    background: linear-gradient(135deg, var(--el-color-danger), var(--el-color-danger-light-3));
    border: none;

    &:hover {
      background: linear-gradient(135deg, var(--el-color-danger-dark-2), var(--el-color-danger));
    }
  }
}

.import-dialog-content {
  .import-tip {
    margin-bottom: 12px;
    color: var(--el-text-color-secondary);
    font-size: 15px;
  }
  
  .import-help {
    margin-top: 12px;
    font-size: 15px;
    color: var(--el-text-color-secondary);
    
    p {
      margin: 4px 0;
    }
  }
}

.el-empty__description {
  font-size: 15px;
}

.dialog-footer {
  .el-button {
    font-size: 15px;
    padding: 12px 24px;
    min-width: 100px;
  }
}

.section-description {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin: -8px 0 16px 14px;
  line-height: 1.5;
}

.form-section {
  margin: 20px 0;
  
  .section-title {
    font-size: 18px;
    margin-bottom: 16px;
    color: var(--el-color-primary);
    position: relative;
    padding-left: 14px;
    
    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 4px;
      height: 18px;
      width: 4px;
      background: var(--el-color-primary);
      border-radius: 2px;
    }
  }
  
  .section-description {
    color: var(--el-text-color-secondary);
    font-size: 14px;
    margin: -8px 0 16px 14px;
    line-height: 1.5;
  }
  
  .structure-editor {
    display: flex;
    flex-direction: column;
    gap: 24px;
    
    .bridge-section {
      border: 1px solid var(--el-border-color);
      border-radius: 6px;
      overflow: hidden;
      
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background-color: var(--el-color-primary-light-9);
        border-bottom: 1px solid var(--el-border-color);
        
        span {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-color-primary);
        }
      }
      
      .structure-tree {
        padding: 16px;
        

        
        .nested-field {
          margin-bottom: 16px;
          border: 1px solid var(--el-border-color-light);
          border-radius: 6px;
          padding: 12px;
          
          &:hover {
            box-shadow: var(--el-box-shadow-light);
          }
          
          .field-header {
            display: flex;
            gap: 10px;
            margin-bottom: 12px;
            align-items: center;
            
            .el-input {
              flex: 3;
            }
            
            .field-type-select {
              flex: 2;
            }
            
            .field-actions {
              display: flex;
              gap: 8px;
              
              .el-button {
                margin-left: 0;
              }
            }
          }
          
          .field-value {
            padding: 0 8px;
          }
          
          .field-children {
            margin-top: 12px;
            padding: 12px;
            border: 1px dashed var(--el-border-color);
            border-radius: 4px;
            
            .nested-field {
              margin-left: 12px;
              
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
}
</style>

<!-- 添加一个没有scoped的style块来处理弹窗样式 -->
<style lang="scss">
.guideline-dialog {
  display: flex;
  flex-direction: column;
  
  .el-dialog__wrapper {
    overflow: hidden !important;
  }
  
  .el-dialog {
    height: 90vh;
    margin: 5vh auto !important;
    display: flex;
    flex-direction: column;
    max-height: 700px;
    overflow: hidden;
    border-radius: 8px;
  }
  
  .el-dialog__header {
    padding: 0;
    margin: 0;
  }
  
  .dialog-custom-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-light);
    
    h4 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
    }
    
    .dialog-close-btn {
      padding: 5px;
      border: none;
      font-size: 18px;
    }
  }
  
  .el-dialog__body {
    flex: 1;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .guideline-form {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .form-header-section {
      padding: 20px 20px 16px;
      border-bottom: 1px solid var(--el-border-color-light);
      flex-shrink: 0;
    }
    
    .form-scrollable-content {
      flex: 1;
      overflow-y: auto;
      padding: 0 20px;
      
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: var(--el-border-color);
        border-radius: 3px;
      }
    }
  }
  
  .el-dialog__footer {
    padding: 0;
    margin: 0;
  }
  
  .dialog-custom-footer {
    display: flex;
    justify-content: flex-end;
    padding: 14px 20px;
    background: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-light);
    
    .el-button {
      font-size: 15px;
      padding: 10px 20px;
    }
  }
}

.guideline-form {
  .el-form-item {
    margin-bottom: 18px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .el-form-item__label {
      font-size: 16px;
      font-weight: 500;
      padding-bottom: 6px;
    }
    
    .el-input__inner, .el-textarea__inner {
      padding: 8px 12px;
      font-size: 20px !important; /* 设置为20px并强制应用 */
    }
  }
  
  .full-width {
    width: 100%;
  }
  
  .form-section {
    margin: 20px 0;
    
    &:first-child {
      margin-top: 20px;
    }
    
    &:last-child {
      margin-bottom: 20px;
    }
  }
  
  .section-title {
    font-size: 18px;
    margin-bottom: 16px;
    color: var(--el-color-primary);
    position: relative;
    padding-left: 14px;
    
    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 4px;
      height: 18px;
      width: 4px;
      background: var(--el-color-primary);
      border-radius: 2px;
    }
  }
  
  .priority-checkbox {
    margin-top: 6px;
    
    .priority-label {
      color: var(--el-color-danger);
      font-weight: 500;
      font-size: 15px;
    }
    
    .el-checkbox__label {
      font-size: 15px;
    }
  }
  
  .guideline-dimensions {
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 4px;
    overflow: hidden;
    
    .dimension-row {
      display: flex;
      flex-direction: column;
      border-bottom: 1px solid var(--el-border-color-lighter);
      
      &:last-child {
        border-bottom: none;
      }
      
      &:nth-child(odd) {
        background-color: rgba(var(--el-color-primary-rgb), 0.02);
      }
      
      .dimension-header {
        padding: 14px 16px 6px;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        
        .dimension-name {
          font-weight: 600;
          color: var(--el-color-primary);
          min-width: 120px;
          font-size: 16px;
        }
        
        .dimension-question {
          color: var(--el-text-color-secondary);
          font-size: 15px;
          flex: 1;
        }
      }
      
      .dimension-content {
        padding: 0 16px 14px;
        
        .el-textarea__inner {
          border-color: transparent;
          background-color: rgba(var(--el-color-primary-rgb), 0.02);
          transition: all 0.3s;
          resize: none;
          font-size: 20px !important; /* 设置为20px并强制应用 */
          line-height: 1.6;
          padding: 10px;
          
          &:hover, &:focus {
            background-color: var(--el-fill-color-lighter);
            border-color: var(--el-border-color);
          }
          
          &::placeholder {
            color: var(--el-text-color-placeholder);
            font-size: 15px;
          }
        }
      }
    }
  }
}

/* 历史设计面板样式 */
.design-info {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  border-radius: 4px;
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid var(--el-color-primary-light-5);
  
  &:hover {
    background-color: var(--el-color-primary-light-8);
  }
}

.selector-icon {
  margin-left: 4px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  transition: transform 0.3s;
}

.design-history-panel {
  padding: 12px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.panel-title {
  font-weight: 600;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.history-search {
  margin-bottom: 16px;
}

.history-list-container {
  max-height: 350px;
  overflow-y: auto;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  padding: 14px 16px;
  border-radius: 8px;
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-lighter);
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.history-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: transparent;
  transition: all 0.25s;
}

.history-item:hover {
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-5);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.history-item:hover::before {
  background-color: var(--el-color-primary);
}

.history-item.active {
  background: var(--el-color-primary-light-8);
  border-color: var(--el-color-primary-light-5);
  box-shadow: 0 4px 10px rgba(var(--el-color-primary-rgb), 0.15);
}

.history-item.active::before {
  background-color: var(--el-color-primary);
}

.item-content {
  flex: 1;
  overflow: hidden;
  padding-right: 16px;
}

.item-title {
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
  font-size: 15px;
  line-height: 1.4;
}

.item-meta {
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.item-timestamp {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.item-timestamp .el-icon {
  font-size: 14px;
  color: var(--el-color-info);
}

.item-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 2px;
}

.item-info .el-tag {
  font-size: 11px;
  padding: 0 6px;
  height: 20px;
  line-height: 18px;
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color-light);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  transition: all 0.3s;
}

.history-item:hover .item-info .el-tag {
  background-color: var(--el-color-primary-light-8);
  border-color: var(--el-color-primary-light-5);
  color: var(--el-color-primary-dark-2);
}

.item-actions {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  align-self: flex-start;
  display: flex;
  gap: 5px;
}

.item-actions .el-button {
  border-radius: 50%;
  width: 28px;
  height: 28px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.history-item:hover .item-actions,
.config-item:hover .item-actions {
  opacity: 1;
}

.item-actions .el-button:hover {
  transform: scale(1.1);
}

.history-item .item-actions .el-button:hover {
  background-color: var(--el-color-danger);
  color: white;
  border-color: var(--el-color-danger);
}

.config-item .item-actions .el-button.el-button--danger:hover {
  background-color: var(--el-color-danger);
  color: white;
  border-color: var(--el-color-danger);
}

.config-item .item-actions .el-button.el-button--primary:hover {
  background-color: var(--el-color-primary);
  color: white;
  border-color: var(--el-color-primary);
}

.config-item .item-actions .el-button.el-button--success:hover {
  background-color: var(--el-color-success);
  color: white;
  border-color: var(--el-color-success);
}



/* 使用deep修改el-popover样式 */
:deep(.history-design-popover),
:deep(.config-manager-popover) {
  padding: 0;
  width: 380px !important; /* 强制固定宽度 */
  border-radius: 8px;
}

/* 配置管理面板样式 */
.config-manager-panel {
  padding: 12px;
}





.config-manager-panel .panel-actions {
  display: flex;
  gap: 8px;
}

.config-search {
  margin-bottom: 16px;
}

.config-list-container {
  max-height: 350px;
  overflow-y: auto;
}

.config-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-item {
  padding: 14px 16px;
  border-radius: 8px;
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-lighter);
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.config-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: transparent;
  transition: all 0.25s;
}

.config-item:hover {
  background: var(--el-color-info-light-9);
  border-color: var(--el-color-info-light-3);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.config-item:hover::before {
  background-color: var(--el-color-info);
}

.config-item.active {
  background: var(--el-color-info-light-8);
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 10px rgba(var(--el-color-primary-rgb), 0.15);
}

.config-item.active::before {
  background-color: var(--el-color-primary);
}

/* 黑暗主题适配 */
html.dark .config-item {
  background: var(--el-bg-color-overlay);
  border-color: var(--el-border-color-light);
}

html.dark .config-item:hover {
  background: var(--el-color-info-dark);
  border-color: var(--el-color-info);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

html.dark .config-item.active {
  background: var(--el-color-info-dark);
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

html.dark .config-item .item-title {
  color: var(--el-text-color-primary);
}

html.dark .config-item .item-description {
  color: var(--el-text-color-secondary);
}

/* 主题自适应提示框 */
.theme-adaptive-alert.el-alert {
  background-color: var(--el-color-info-light-9) !important;
  border: 1px solid var(--el-color-info-light-5) !important;
}

html.dark .theme-adaptive-alert.el-alert {
  background-color: var(--el-color-info-dark-2) !important;
  border-color: var(--el-color-info-dark) !important;
  color: var(--el-color-white) !important;
}

.theme-adaptive-alert.el-alert .el-alert__icon {
  color: var(--el-color-info) !important;
}

html.dark .theme-adaptive-alert.el-alert .el-alert__icon {
  color: var(--el-color-info-light-3) !important;
}

.theme-adaptive-alert.el-alert p {
  color: var(--el-text-color-primary) !important;
  line-height: 1.6;
  margin: 0;
  font-size: 14px;
}

html.dark .theme-adaptive-alert.el-alert p {
  color: var(--el-color-white) !important;
}



.config-item .item-description {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 220px;
  font-size: 13px;
  color: var(--el-text-color-secondary);
  margin-bottom: 5px;
}

.config-item .item-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-item .item-info .el-tag {
  font-size: 11px;
  padding: 0 6px;
  height: 20px;
  line-height: 18px;
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color-light);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  transition: all 0.3s;
}

.config-item:hover .item-info .el-tag {
  background-color: var(--el-color-info-light-8);
  border-color: var(--el-color-info-light-5);
  color: var(--el-color-info-dark-2);
}





/* 通用滚动条样式 */
.history-list-container,
.config-list-container {
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color) transparent;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color);
    border-radius: 2px;
  }
}

/* 通用空状态样式 */
.empty-history,
.empty-config,
.empty-template {
  padding: 30px 0;
  text-align: center;
}

.full-width {
  width: 100%;
}

/* 配置编辑器样式 */
.config-editor-dialog {
  display: flex;
  flex-direction: column;
  
  .el-dialog__wrapper {
    overflow: hidden !important;
  }
  
  .el-dialog {
    height: 90vh;
    margin: 5vh auto !important;
    display: flex;
    flex-direction: column;
    max-height: 700px;
    overflow: hidden;
    border-radius: 8px;
  }
  
  .el-dialog__header {
    padding: 0;
    margin: 0;
  }
  
  .dialog-custom-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-light);
    
    h4 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
    }
    
    .dialog-close-btn {
      padding: 5px;
      border: none;
      font-size: 18px;
    }
  }
  
  .el-dialog__body {
    flex: 1;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .config-editor-form {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .form-header-section {
      padding: 20px 20px 16px;
      border-bottom: 1px solid var(--el-border-color-light);
      flex-shrink: 0;
    }
    
    .form-scrollable-content {
      flex: 1;
      overflow-y: auto;
      padding: 0 20px;
      
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: var(--el-border-color);
        border-radius: 3px;
      }
    }
  }
  
  .el-dialog__footer {
    padding: 0;
    margin: 0;
  }
  
  .dialog-custom-footer {
    display: flex;
    justify-content: flex-end;
    padding: 14px 20px;
    background: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-light);
    
    .el-button {
      font-size: 15px;
      padding: 10px 20px;
    }
  }
  
  .form-scrollable-content {
    .fields-list {
      .el-button {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

// 响应式设计改进
@media (max-width: 1200px) {
  .a2b-container {
    font-size: 14px;

    .app-header .header-top {
      padding: 12px 16px;

      .title-main h2 {
        font-size: 24px;
      }

      .title-subtitle {
        font-size: 13px;
      }
    }

    .workflow-guide {
      padding: 16px 20px;

      .guide-step {
        padding: 10px 16px;

        .step-text {
          font-size: 14px;
        }
      }
    }

    .scene-panel {
      .panel-header {
        padding: 16px 20px;

        h3 {
          font-size: 20px;
        }
      }

      .panel-content {
        padding: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .a2b-container {
    font-size: 13px;

    .app-header .header-top {
      padding: 10px 12px;
      flex-direction: column;
      gap: 12px;

      .title-main h2 {
        font-size: 20px;
      }

      .header-actions {
        gap: 8px;

        .action-btn {
          padding: 8px 12px;
          font-size: 13px;
        }
      }
    }

    .workflow-guide {
      padding: 12px 16px;

      .guide-content {
        flex-direction: column;
        gap: 12px;
      }

      .guide-arrow {
        transform: rotate(90deg);
      }
    }

    .main-content {
      flex-direction: column;
      gap: 16px;
    }

    .scene-panel {
      .panel-header {
        padding: 12px 16px;

        h3 {
          font-size: 18px;
        }
      }

      .panel-content {
        padding: 16px;
      }

      .json-field {
        padding: 16px;

        .field-header {
          font-size: 16px;
        }
      }
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .a2b-container {
    .workflow-guide {
      // 深色主题下调整背景渐变
      background: linear-gradient(135deg,
        rgba(var(--el-color-primary-rgb), 0.15) 0%,
        rgba(var(--el-color-success-rgb), 0.15) 100%);

      &::before {
        opacity: 0.9;
        background: var(--el-bg-color);
      }

      .guide-step {
        background: var(--el-bg-color);
        border-color: var(--el-border-color);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

        &:hover {
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
          background: var(--el-bg-color-overlay);
        }

        .step-number {
          box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.5);
        }
      }

      .guide-arrow {
        opacity: 0.9;
      }
    }

    .scene-panel {
      &:hover {
        box-shadow: 0 8px 32px rgba(255, 255, 255, 0.1);
      }
    }

    .json-field {
      &:hover {
        box-shadow: 0 4px 16px rgba(255, 255, 255, 0.08);
      }
    }

    .el-button {
      &:hover {
        box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
      }
    }
  }
}

// Element Plus 深色主题适配
.dark {
  .workflow-guide {
    // Element Plus 深色主题下的特殊处理
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.12) 0%,
      rgba(var(--el-color-success-rgb), 0.12) 100%) !important;

    &::before {
      opacity: 0.95 !important;
    }

    .guide-step {
      background: var(--el-bg-color) !important;
      border-color: var(--el-border-color) !important;

      &:hover {
        background: var(--el-bg-color-overlay) !important;
        border-color: var(--el-border-color-dark) !important;
      }
    }
  }
}
</style>
