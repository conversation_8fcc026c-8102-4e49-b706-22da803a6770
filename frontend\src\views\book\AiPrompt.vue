<template>
  <div class="ai-prompt-manager glass-bg">
    <!-- 顶部标题和导航 -->
    <div class="header-section">
      <div class="left-section">
        <h1 class="page-title">AI提示词</h1>
        
        <!-- 将书籍选择器放到标题旁边 -->
        <el-select
          v-model="selectedBookId"
          placeholder="选择书籍"
          class="book-selector"
          @change="handleBookChange"
        >
          <el-option
            v-for="book in bookStore.bookList"
            :key="book.id"
            :label="book.title"
            :value="book.id"
          />
        </el-select>
  </div>
      
      <div class="tab-buttons">
        <el-button
          class="tab-button"
          :class="{ active: activeTab === 'promptList' }"
          @click="activeTab = 'promptList'"
        >
          规则列表
        </el-button>
        <el-button
          class="tab-button"
          :class="{ active: activeTab === 'generator' }"
          @click="activeTab = 'generator'"
        >
          规则生成器
        </el-button>
      </div>
      

    </div>

    <!-- 主要内容区域 -->
    <div class="main-content" v-if="selectedBookId">
      <!-- 提示词列表 -->
      <div v-show="activeTab === 'promptList'" class="prompts-container glass-bg">
        <div class="section-header">
          <div class="header-content">
            <h2>AI提示词规则列表</h2>
            <el-input
              v-model="searchQuery"
              placeholder="搜索规则"
              class="search-input"
              clearable
              @clear="handleSearchClear"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          
          <!-- 添加新建规则按钮 -->
          <div class="header-actions">
            <el-button type="primary" @click="createNewRule">
              <el-icon><Plus /></el-icon>
              新建规则
            </el-button>
          </div>
        </div>

        <div class="prompt-list-wrapper">
          <el-empty
            v-if="promptList.length === 0"
            description="暂无提示词，点击新建按钮创建"
          />
          
          <el-table
            v-else
            :data="filteredPrompts"
            style="width: 100%"
            :header-cell-style="{
              background: 'var(--el-bg-color-overlay)',
              color: 'var(--el-text-color-primary)',
              fontWeight: '600'
            }"
            :row-class-name="tableRowClassName"
          >
            <el-table-column prop="name" label="规则名称" min-width="180">
              <template #default="{ row }">
                <div class="prompt-name-cell">
                  <span class="prompt-name">{{ row.name }}</span>
                  <el-tag 
                    v-if="row.type" 
                    size="small" 
                    :type="getTagType(row.type)"
                    class="prompt-type-tag"
                  >
                    {{ row.type }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="description" label="描述" min-width="220">
              <template #default="{ row }">
                <div class="prompt-description">
                  {{ row.description || '暂无描述' }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="提示词" width="120" align="center">
              <template #default="{ row }">
                <div class="prompt-count">
                  <el-badge
                    :value="row.rule?.prompts?.length || 0"
                    :type="row.rule?.prompts?.length ? 'primary' : 'info'"
                    class="prompt-badge"
                  />
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="updateTime" label="更新时间" width="160">
              <template #default="{ row }">
                <div class="update-time">
                  {{ formatDate(row.updateTime) }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <div class="operation-buttons">
                  <el-button-group>
                    <el-tooltip content="查看提示词" placement="top">
                      <el-button
                        type="primary"
                        link
                        @click="viewPrompts(row)"
                      >
                        <el-icon><View /></el-icon>
                      </el-button>
                    </el-tooltip>
                    
                    <el-tooltip content="编辑规则" placement="top">
                      <el-button
                        type="primary"
                        link
                        @click="editPrompt(row)"
                      >
                        <el-icon><Edit /></el-icon>
                      </el-button>
                    </el-tooltip>
                    
                    <el-tooltip content="删除规则" placement="top">
                      <el-button
                        type="danger"
                        link
                        @click="deletePrompt(row)"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </el-tooltip>
                  </el-button-group>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页器 -->
          <div class="pagination-container">
            <el-pagination
              :current-page="currentPage"
              @update:current-page="handleCurrentChange"
              :page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="filteredPrompts.length"
              :pager-count="5"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
            />
          </div>
        </div>
      </div>

      <!-- 提示词生成器 -->
      <div v-show="activeTab === 'generator'" class="generator-container glass-bg">
        <div class="section-header">
          <div class="header-content">
            <h2>规则生成器</h2>
          </div>
          <div class="button-group">
            <el-button
              type="primary"
              @click="generatePrompt"
              :disabled="!canGenerate"
            >
              <el-icon><Refresh /></el-icon>
              重新生成
            </el-button>
            <el-button
              type="success"
              @click="saveGeneratedPrompt"
              :disabled="!generatedPromptName || !generatedPromptContent"
            >
              <el-icon><Check /></el-icon>
              保存规则
            </el-button>
          </div>
        </div>

        <!-- 生成器内容区域 -->
        <div class="generator-content">
          <!-- 左侧面板：基本信息和生成结果 -->
          <div class="generator-left-panel">
            <!-- 提示词信息 -->
            <div class="panel-item info-panel">
              <h3 class="panel-title">提示词规则</h3>
              <el-form :model="generatedPrompt" label-width="80px">
                <el-form-item label="规则名称" required>
                  <el-input
                    v-model="generatedPromptName"
                    placeholder="输入提示词规则名称"
                  />
                </el-form-item>
                <el-form-item label="描述">
                  <el-input
                    v-model="generatedPromptDescription"
                    type="textarea"
                    :rows="2"
                    placeholder="描述这个生成规则的用途"
                  />
                </el-form-item>
              </el-form>
            </div>

            <!-- 生成结果 -->
            <div class="panel-item result-panel">
              <h3 class="panel-title">提示词编辑</h3>
              <div class="result-actions">
                <el-button-group>
                  <!-- 移除预览按钮 -->
                  <!-- <el-tooltip content="预览完整提示词" placement="top">
                    <el-button size="small" type="info" @click="previewPrompt" :disabled="!generatedPromptContent">
                      <el-icon><View /></el-icon>
                      预览
                    </el-button>
                  </el-tooltip> -->
                  <el-tooltip content="复制到剪贴板" placement="top">
                    <el-button size="small" @click="copyResult" :disabled="!generatedPromptContent">
                      <el-icon><Document /></el-icon>
                      复制
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="清空当前内容" placement="top">
                    <el-button size="small" type="danger" @click="clearResult">
                      <el-icon><Delete /></el-icon>
                      清空
                    </el-button>
                  </el-tooltip>
                </el-button-group>
              </div>
              <div class="result-content-wrapper">
                <el-input
                  v-model="generatedPromptContent"
                  type="textarea"
                  :rows="12"
                  placeholder="在此编辑提示词，可以插入模板和场景占位符"
                />
              </div>
            </div>
          </div>

          <!-- 右侧面板：规则配置 -->
          <div class="generator-right-panel">
            <!-- 规则容器 -->
            <div class="rules-container">
              <!-- 模板规则 -->
              <div class="rule-section">
                <div class="rule-header">
                  <h4>模板规则</h4>
                  <el-switch v-model="promptRule.template.enabled" active-text="启用" />
                </div>
                <div class="rule-content">
                  <el-scrollbar>
                    <template v-if="promptRule.template.enabled">
                      <el-form class="rule-form" label-position="top">
                        <el-form-item label="使用模式">
                          <el-radio-group v-model="promptRule.template.mode">
                            <el-radio-button label="single">单个模板</el-radio-button>
                            <el-radio-button label="entity">单个实体</el-radio-button>
                            <el-radio-button label="all">所有实体</el-radio-button>
                          </el-radio-group>
                        </el-form-item>
                        
                        <!-- 根据选择的模式显示不同的配置 -->
                        <template v-if="promptRule.template.mode === 'single'">
                          <el-form-item label="选择模板">
                            <el-select 
                              v-model="promptRule.template.selectedTemplateId"
                              filterable
                              placeholder="选择模板"
                            >
                              <el-option
                                v-for="template in templateList"
                                :key="template.id"
                                :label="template.name"
                                :value="template.id"
                              >
                                <span>{{ template.name }}</span>
                                <span class="option-count">{{ getEntityCount(template) }}个实体</span>
                              </el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item>
                            <el-button 
                              type="primary" 
                              @click="insertSingleTemplatePlaceholder" 
                              :disabled="!promptRule.template.selectedTemplateId"
                            >
                              <el-icon><Plus /></el-icon>
                              插入此模板
                            </el-button>

                          </el-form-item>
                        </template>
                        
                        <template v-else-if="promptRule.template.mode === 'entity'">
                          <!-- 选择模板 -->
                          <el-form-item label="选择模板">
                            <el-select 
                              v-model="promptRule.template.selectedTemplateId"
                              filterable
                              placeholder="选择模板"
                              @change="handleTemplateChange"
                            >
                              <el-option
                                v-for="template in templateList"
                                :key="template.id"
                                :label="template.name"
                                :value="template.id"
                              >
                                <span>{{ template.name }}</span>
                                <span class="option-count">{{ getEntityCount(template) }}个实体</span>
                              </el-option>
                            </el-select>
                          </el-form-item>

                          <!-- 选择实体 -->
                          <el-form-item label="选择实体">
                            <el-select
                              v-model="promptRule.template.selectedEntityId"
                              filterable
                              placeholder="选择实体"
                            >
                              <el-option
                                v-for="entity in currentTemplateEntities"
                                :key="entity.id"
                                :label="entity.name"
                                :value="entity.id"
                              >
                                <span>{{ entity.name }}</span>
                                <span v-if="entity.type" class="entity-type">({{ entity.type }})</span>
                              </el-option>
                            </el-select>
                          </el-form-item>

                          <!-- 输出格式配置 -->
                          <el-form-item label="输出格式">
                            <el-radio-group v-model="promptRule.template.entityOutputFormat">
                              <el-radio-button label="text">文本格式</el-radio-button>
                              <el-radio-button label="json">JSON格式</el-radio-button>
                            </el-radio-group>
                          </el-form-item>

                          <!-- 排除维度配置 -->
                          <el-form-item label="排除维度">
                            <el-checkbox-group v-model="promptRule.template.entityExcludedDimensions">
                              <el-checkbox
                                v-for="dim in allTemplateDimensionsForEntity"
                                :key="dim.name"
                                :label="dim.name"
                              >
                                {{ dim.name }}
                              </el-checkbox>
                            </el-checkbox-group>
                          </el-form-item>

                          <!-- 插入按钮组 -->
                          <el-form-item>
                            <div class="button-group" style="display: flex; gap: 12px;">
                              <el-button 
                                type="primary" 
                                @click="insertSingleEntityPlaceholder" 
                                :disabled="!promptRule.template.selectedEntityId"
                              >
                                <el-icon><Plus /></el-icon>
                                插入此实体
                              </el-button>
                              <el-button 
                                type="primary" 
                                @click="insertRandomEntityPlaceholder" 
                                :disabled="!promptRule.template.selectedTemplateId || !currentTemplateEntities.length"
                              >
                                <el-icon><Refresh /></el-icon>
                                插入随机实体
                              </el-button>
                            </div>
                          </el-form-item>
                        </template>
                        
                        <template v-else>
                          <el-form-item label="选择模板">
                            <el-select 
                              v-model="promptRule.template.selectedTemplateId"
                              filterable
                              placeholder="选择模板"
                            >
                              <el-option
                                v-for="template in templateList"
                                :key="template.id"
                                :label="template.name"
                                :value="template.id"
                              >
                                <span>{{ template.name }}</span>
                                <span class="option-count">{{ getEntityCount(template) }}个实体</span>
                              </el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item label="输出格式">
                            <el-radio-group v-model="promptRule.template.allEntitiesOutputFormat">
                              <el-radio-button label="text">文本格式</el-radio-button>
                              <el-radio-button label="json">JSON格式</el-radio-button>
                            </el-radio-group>
                          </el-form-item>
                          <el-form-item label="排除维度">
                            <el-checkbox-group v-model="promptRule.template.excludedDimensions">
                              <el-checkbox
                                v-for="dim in allTemplateDimensions"
                                :key="dim.name"
                                :label="dim.name"
                              >
                                {{ dim.name }}
                              </el-checkbox>
                            </el-checkbox-group>
                          </el-form-item>
                          <el-form-item>
                            <el-button 
                              type="primary" 
                              @click="insertAllEntitiesPlaceholder" 
                              :disabled="!promptRule.template.selectedTemplateId"
                            >
                              <el-icon><Plus /></el-icon>
                              插入所有实体
                            </el-button>
                          </el-form-item>
                        </template>
                      </el-form>
                    </template>
                  </el-scrollbar>
                </div>
              </div>

              <!-- 场景规则 -->
              <div class="rule-section">
                <div class="rule-header">
                  <h4>场景规则</h4>
                  <el-switch v-model="promptRule.scene.enabled" active-text="启用" />
                </div>
                <div class="rule-content">
                  <el-scrollbar>
                    <template v-if="promptRule.scene.enabled">
                      <el-form class="rule-form" label-position="top">
                        <el-form-item label="场景模式">
                          <el-radio-group v-model="promptRule.scene.mode">
                            <el-radio-button label="manual">指定场景</el-radio-button>
                            <el-radio-button label="random">随机抽取</el-radio-button>
                          </el-radio-group>
                        </el-form-item>

                        <template v-if="promptRule.scene.mode === 'manual'">
                          <el-form-item label="选择场景">
                            <el-select
                              v-model="promptRule.scene.selectedSceneIds"
                              multiple
                              filterable
                              placeholder="选择场景"
                              class="scene-selector"
                            >
                              <el-option-group
                                v-for="pool in sceneData.pools"
                                :key="pool.id"
                                :label="pool.name"
                              >
                                <el-option
                                  v-for="scene in pool.scenes"
                                  :key="scene.id"
                                  :label="scene.title"
                                  :value="scene.id"
                                >
                                  <div class="scene-option">
                                    <div class="scene-title">{{ scene.title }}</div>
                                    <div class="scene-description">{{ scene.description }}</div>
                                  </div>
                                </el-option>
                              </el-option-group>
                            </el-select>
                          </el-form-item>
                        </template>

                        <template v-else>
                          <el-form-item label="场景池">
                            <el-select v-model="promptRule.scene.selectedPoolId">
                              <el-option
                                v-for="pool in sceneData.pools"
                                :key="pool.id"
                                :label="pool.name"
                                :value="pool.id"
                              />
                            </el-select>
                          </el-form-item>

                          <el-form-item label="抽取数量">
                            <el-input-number
                              v-model="promptRule.scene.randomCount"
                              :min="1"
                              :max="getMaxSceneCount"
                            />
                          </el-form-item>
                        </template>

                        <!-- 添加插入按钮 -->
                        <el-form-item>
                          <el-button 
                            type="primary" 
                            @click="insertScenePlaceholder" 
                            :disabled="(promptRule.scene.mode === 'manual' && (!promptRule.scene.selectedSceneIds || promptRule.scene.selectedSceneIds.length === 0)) || 
                                       (promptRule.scene.mode === 'random' && !promptRule.scene.selectedPoolId)"
                          >
                            <el-icon><Plus /></el-icon>
                            插入场景描述
                          </el-button>
                        </el-form-item>
                      </el-form>
                    </template>
                  </el-scrollbar>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 未选择书籍时显示提示 -->
    <div v-else class="empty-state glass-bg">
      <el-empty description="请先选择一本书籍" :image-size="200">
        <template #image>
          <el-icon :size="64" class="empty-icon"><Edit /></el-icon>
        </template>
      </el-empty>
    </div>

    <!-- 提示词编辑对话框 -->
    <el-dialog
      v-model="promptDialogVisible"
      :title="editingPrompt.id ? '编辑提示词' : '新建提示词'"
      width="800px"
      :close-on-click-modal="false"
      class="prompt-edit-dialog"
    >
      <el-form :model="editingPrompt" label-width="80px">
        <el-form-item label="名称" required>
          <el-input
            v-model="editingPrompt.name"
            placeholder="输入提示词名称"
          />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="editingPrompt.description"
            type="textarea"
            :rows="3"
            placeholder="输入提示词描述"
          />
        </el-form-item>
        <el-form-item label="内容" required>
          <el-input
            v-model="editingPrompt.content"
            type="textarea"
            :rows="10"
            placeholder="输入提示词内容"
          />
          <div class="placeholder-helper">
            <span>可用占位符格式：</span>
            <el-tag size="small" effect="plain">{{123}}</el-tag>
            <el-tag size="small" effect="plain">{{123}}</el-tag>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="promptDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePrompt" :disabled="!canSavePrompt">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 提示词详情对话框 -->
    <el-dialog
      v-model="promptDetailVisible"
      :title="viewingPrompt?.name || '未命名提示词'"
      width="800px"
      :close-on-click-modal="true"
      class="prompt-detail-dialog"
    >
      <div class="prompt-detail">
        <div class="prompt-detail-item">
          <h3>提示词名称</h3>
          <p>{{ viewingPrompt?.name || '未命名提示词' }}</p>
        </div>
        <div class="prompt-detail-item">
          <h3>描述</h3>
          <p>{{ viewingPrompt?.description || '无描述' }}</p>
        </div>
        <div class="prompt-detail-item">
          <h3>内容</h3>
          <div class="prompt-content-box">
            <pre>{{ viewingPrompt?.content || '无内容' }}</pre>
          </div>
        </div>
        <div class="prompt-detail-item">
          <h3>占位符</h3>
          <div class="placeholder-tags">
            <el-tag
              v-for="placeholder in getPlaceholders(viewingPrompt?.content || '')"
              :key="placeholder"
              size="large"
              class="placeholder-tag"
              effect="light"
            >
              {{ placeholder }}
            </el-tag>
            <el-empty
              v-if="getPlaceholders(viewingPrompt?.content || '').length === 0"
              description="此提示词中没有占位符"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="promptDetailVisible = false">关闭</el-button>
          <el-button type="primary" @click="editPrompt(viewingPrompt)">编辑</el-button>
          <el-button type="success" @click="copyPromptToClipboard(viewingPrompt)">复制到剪贴板</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量生成对话框 -->
    <el-dialog
      v-model="batchGenerateDialogVisible"
      title="批量生成提示词"
      width="600px"
      class="batch-generate-dialog"
    >
      <el-form :model="batchGenerateConfig" label-position="top">
        <el-form-item label="生成数量">
          <el-input-number
            v-model="batchGenerateConfig.count"
            :min="1"
            :max="20"
            :step="1"
          />
        </el-form-item>
        <el-form-item label="生成结果">
          <div class="batch-results">
            <el-scrollbar height="300px">
              <div
                v-for="(result, index) in batchResults"
                :key="index"
                class="batch-result-item"
              >
                <div class="result-header">
                  <span class="result-index">结果 #{{ index + 1 }}</span>
                  <el-button-group>
                    <el-button size="small" @click="useBatchResult(result)">
                      使用
                    </el-button>
                    <el-button size="small" @click="copyBatchResult(result)">
                      复制
                    </el-button>
                  </el-button-group>
                </div>
                <div class="result-content">{{ result }}</div>
              </div>
            </el-scrollbar>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchGenerateDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="executeBatchGenerate" :loading="generating">
            开始生成
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 使用预览对话框组件 -->
    <PromptPreviewDialog
      v-model="previewDialogVisible"
      :prompt-name="generatedPromptName || '生成的提示词'"
      :prompt-description="generatedPromptDescription"
      :prompt-content="previewPromptContent"
      @save-to-rule="handleSaveToRule"
      @regenerate="handleRegeneratePreview"
    />

    <!-- 模板选择对话框 -->
    <el-dialog
      v-model="templateSelectorVisible"
      title="选择模板"
      width="50%"
    >
      <el-form>
        <el-form-item label="选择模板">
          <el-select 
            v-model="selectedTemplateForPlaceholder"
            filterable
            placeholder="选择模板"
            style="width: 100%"
          >
            <el-option
              v-for="template in templateList"
              :key="template.id"
              :label="template.name"
              :value="template.id"
            >
              <span>{{ template.name }}</span>
              <span class="option-count">{{ getEntityCount(template) }}个实体</span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="templateSelectorVisible = false">取消</el-button>
          <el-button type="primary" @click="insertTemplatePlaceholder" :disabled="!selectedTemplateForPlaceholder">
            插入
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 实体选择对话框 -->
    <el-dialog
      v-model="entitySelectorVisible"
      title="选择实体"
      width="50%"
    >
      <el-form>
        <el-form-item label="选择模板">
          <el-select 
            v-model="selectedTemplateForEntity"
            filterable
            placeholder="选择模板"
            @change="handleEntitySelectorTemplateChange"
            style="width: 100%"
          >
            <el-option
              v-for="template in templateList"
              :key="template.id"
              :label="template.name"
              :value="template.id"
            >
              <span>{{ template.name }}</span>
              <span class="option-count">{{ getEntityCount(template) }}个实体</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择实体" v-if="selectedTemplateForEntity">
          <el-select
            v-model="selectedEntityForPlaceholder"
            filterable
            placeholder="选择实体"
            style="width: 100%"
          >
            <el-option
              v-for="entity in getEntitiesForTemplate(selectedTemplateForEntity)"
              :key="entity.id"
              :label="entity.name"
              :value="entity.id"
            >
              <span>{{ entity.name }}</span>
              <span v-if="entity.type" class="entity-type">({{ entity.type }})</span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="entitySelectorVisible = false">取消</el-button>
          <el-button type="primary" @click="insertEntityPlaceholder" :disabled="!selectedEntityForPlaceholder">
            插入
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 在 generatedPromptContent 下方添加 -->
    <div class="rule-saved-prompts" v-if="promptRule.savedPrompts && promptRule.savedPrompts.length > 0">
      <h3 class="panel-title">规则已保存提示词 ({{ promptRule.savedPrompts.length }})</h3>
      
      <el-table :data="promptRule.savedPrompts" style="width: 100%" size="small">
        <el-table-column label="保存时间" width="180">
          <template #default="scope">
            {{ new Date(scope.row.timestamp).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="预览内容">
          <template #default="scope">
            <div class="prompt-preview-text">{{ scope.row.previewContent || scope.row.content }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="viewSavedPrompt(scope.row)">查看</el-button>
            <el-button size="small" type="danger" @click="removeSavedPrompt(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>


    <!-- 添加导入规则对话框 -->
    <el-dialog
      v-model="importRuleDialogVisible"
      title="导入规则提示词"
      width="500px"
    >
      <el-form>
        <el-form-item label="选择规则">
          <el-select v-model="selectedRuleToImport" filterable placeholder="选择规则">
            <el-option
              v-for="prompt in promptList.filter(p => p.type === 'generator')"
              :key="prompt.id"
              :label="prompt.name"
              :value="prompt.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importRuleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleImportRule" :disabled="!selectedRuleToImport">
            导入
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 规则下提示词列表对话框 -->
    <PromptListDialog
      v-model="promptsDialogVisible"
      :rule-name="currentRuleName"
      :prompts="currentRulePrompts"
      @use-prompt="usePrompt"
    />
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, Document, Edit, Delete, Search, Check, InfoFilled, Refresh, Files, View, ArrowDown, FolderAdd, Download } from '@element-plus/icons-vue'
import { useBookStore } from '@/stores/book'
import PromptPreviewDialog from '@/components/PromptPreviewDialog.vue'
import PromptListDialog from '@/components/PromptListDialog.vue'

const route = useRoute()
const router = useRouter()
const bookStore = useBookStore()


// 使用选择的书籍ID
const selectedBookId = ref('')
const bookTitle = computed(() => {
  const selectedBook = bookStore.bookList.find(book => book.id === selectedBookId.value)
  return selectedBook?.title || 'AI提示词'
})

// 场景卡池数据
const sceneData = ref({
  pools: [],
  currentPoolId: null
})
const scenes = ref([])

// 场景搜索
const sceneSearchQuery = ref('')

// 过滤场景列表
const filteredScenes = computed(() => {
  let scenes = []
  // 收集所有场景
  sceneData.value.pools.forEach(pool => {
    if (pool.scenes) {
      scenes.push(...pool.scenes)
    }
  })
  
  // 搜索过滤
  if (sceneSearchQuery.value) {
    const query = sceneSearchQuery.value.toLowerCase()
    scenes = scenes.filter(scene => 
      (scene.title && scene.title.toLowerCase().includes(query)) ||
      (scene.description && scene.description.toLowerCase().includes(query))
    )
  }
  
  return scenes
})

// 选择场景
const selectScene = (scene) => {
  selectedSceneId.value = scene.id
}

// 计算当前卡池
const currentPool = computed(() => {
  if (!sceneData.value.currentPoolId) return null
  if (sceneData.value.currentPoolId === 'all') return allScenesPool.value
  return sceneData.value.pools.find(pool => pool.id === sceneData.value.currentPoolId)
})

// 全部场景虚拟卡池
const allScenesPool = computed(() => {
  const allScenes = []
  
  // 从所有卡池中收集场景，添加更严格的检查
  if (sceneData.value.pools && Array.isArray(sceneData.value.pools)) {
    sceneData.value.pools.forEach(pool => {
      if (pool && pool.scenes && Array.isArray(pool.scenes)) {
        // 为每个场景添加源卡池信息
        const scenesWithSource = pool.scenes.map(scene => ({
          ...scene,
          sourcePool: { id: pool.id, name: pool.name }
        }))
        allScenes.push(...scenesWithSource)
      }
    })
  }
  
  return {
    id: 'all',
    name: '全部场景',
    scenes: allScenes,
    isVirtual: true
  }
})

// 在提示词生成中添加场景选择
const selectedSceneId = ref(null)
const selectedScene = computed(() => {
  return scenes.value.find(scene => scene.id === selectedSceneId.value)
})

// 视图状态
const activeTab = ref('promptList')


// 提示词相关状态
const promptList = ref([])
const searchQuery = ref('')


// 过滤提示词列表
const filteredPrompts = computed(() => {
  if (!promptList.value) return []
  
  if (!searchQuery.value) return promptList.value
  
  const query = searchQuery.value.toLowerCase()
  return promptList.value.filter(prompt => 
    (prompt.name && prompt.name.toLowerCase().includes(query)) ||
    (prompt.description && prompt.description.toLowerCase().includes(query))
  )
})

// 分页提示词
const paginatedPrompts = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredPrompts.value.slice(start, end)
})

// 分页相关
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handlePageChange = (page) => {
  currentPage.value = page
}

// 搜索相关
const handleSearchClear = () => {
  currentPage.value = 1
}

// 获取占位符
const getPlaceholders = (content) => {
  if (!content) return []
  
  const placeholderRegex = /\{\{([^}]+)\}\}/g
  const matches = []
  let match
  
  while ((match = placeholderRegex.exec(content)) !== null) {
    matches.push(match[1].trim())
  }
  
  return Array.from(new Set(matches)) // 去重
}

// 提示词对话框相关
const promptDialogVisible = ref(false)
const promptDetailVisible = ref(false)
const isEditing = ref(false)
const editingPrompt = ref({
  id: '',
  name: '',
  description: '',
  content: ''
})
const currentPrompt = ref(null)

// 在现有变量声明部分添加viewingPrompt
const viewingPrompt = ref({
  id: '',
  name: '',
  description: '',
  content: ''
})

// 修改showPromptDetail函数，将其内容复制到viewingPrompt中
const showPromptDetail = (row) => {
  if (!row) return
  currentPrompt.value = { ...row }
  viewingPrompt.value = { 
    id: row.id || '',
    name: row.name || '未命名提示词',
    description: row.description || '',
    content: row.content || ''
  }
  detailDialogVisible.value = true
}

// 提示词操作
const showPromptDialog = () => {
  // 重置编辑ID
  currentEditingPromptId.value = null;
  
  // 重置表单
  generatedPromptName.value = '';
  generatedPromptDescription.value = '';
  generatedPromptContent.value = '';
  
  // 重置规则配置
  promptRule.value = {
    template: {
      enabled: false,
      mode: 'single',
      selectedTemplateId: null,
      selectedDimensions: [],
      excludedDimensions: [],
      entityExcludedDimensions: [],
      entityOutputFormat: 'text',
      allEntitiesOutputFormat: 'text'
    },
    scene: {
      enabled: false,
      mode: 'manual',
      selectedSceneIds: [],
      randomCount: 1,
      selectedPoolId: null,
      placeholderText: '{{场景描述}}'
    },
    prompts: []
  };
  
  // 切换到生成器选项卡
  activeTab.value = 'generator';
  
  promptDialogVisible.value = true
}

// 修改编辑提示词函数，设置当前编辑ID
const editPrompt = (rule) => {
  // 设置当前编辑的规则ID
  currentEditingPromptId.value = rule.id;
  
  // 加载规则数据到编辑表单
  generatedPromptName.value = rule.name;
  generatedPromptDescription.value = rule.description;
  
  // 加载规则的提示词编辑器配置
  if (rule.rule) {
    promptRule.value = JSON.parse(JSON.stringify(rule.rule));
    
    // 如果有提示词内容，也加载到编辑器
    if (rule.rule.content) {
      generatedPromptContent.value = rule.rule.content;
    }
    
    // 如果有其他编辑器配置，也一并加载
    if (rule.rule.editorConfig) {
      // 加载编辑器的其他配置
      Object.assign(editorConfig.value, rule.rule.editorConfig);
    }
  } else {
    // 如果是新规则，初始化默认值
    promptRule.value = {
      prompts: [],
      content: '',
      editorConfig: { ...editorConfig.value } // 使用默认编辑器配置
    };
  }
  
  // 切换到生成器视图
  activeTab.value = 'generator';
};

// 复制提示词到剪贴板
const copyPromptToClipboard = async (prompt) => {
  if (!prompt || !prompt.content) return
  
  try {
    await window.pywebview.api.copy_to_clipboard(prompt.content)
    ElMessage.success('提示词已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 删除提示词
const deletePrompt = (prompt) => {
  ElMessageBox.confirm(
    `确定要删除提示词 "${prompt.name}" 吗？此操作不可撤销。`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      customClass: 'delete-confirm'
    }
  ).then(async () => {
    try {
      loading.value = true;
      
      // 调用后端API删除提示词
      const response = await window.pywebview.api.book_controller.delete_prompt(
        selectedBookId.value, 
        prompt.id
      );
      
      const result = typeof response === 'string' ? JSON.parse(response) : response;
      
      if (result.status === 'success') {
        ElMessage.success('删除成功');
        // 从本地列表中移除
        promptList.value = promptList.value.filter(item => item.id !== prompt.id);
      } else {
        console.error('删除失败:', result.message);
        ElMessage.error(`删除失败: ${result.message}`);
      }
    } catch (error) {
      console.error('删除操作异常:', error);
      ElMessage.error('删除失败: ' + (error.message || '未知错误'));
    } finally {
      loading.value = false;
    }
  }).catch(() => {
    // 用户取消删除
  });
}

// 提示词生成器相关
const generatedPrompt = ref({})
const generatedPromptName = ref('')
const generatedPromptDescription = ref('')
const generatedPromptContent = ref('')
const selectorType = ref('templates')

// 模板和实体相关状态
const templateList = ref([])
const allEntities = ref([])
const entityTypes = ref([])
const selectedEntityType = ref('all')
const entitySearchQuery = ref('')
const templateSearchQuery = ref('')
const selectedTemplateId = ref(null)
const selectedEntityId = ref(null)
const selectedDimension = ref(null)

// 提示词生成规则
const promptRule = ref({
  template: {
    enabled: false,
    mode: 'single',
    selectedTemplateId: null,
    selectedDimensions: [],
    excludedDimensions: [],
    entityExcludedDimensions: [],
    entityOutputFormat: 'text',
    allEntitiesOutputFormat: 'text'
  },
  scene: {
    enabled: false,
    mode: 'manual',
    selectedSceneIds: [],
    randomCount: 1,
    selectedPoolId: null,
    placeholderText: '{{场景描述}}'
  },
  prompts: [] // 确保这个数组被初始化
})

// 获取模板数据
const loadTemplates = async () => {
  loading.value = true
  try {
    const response = await window.pywebview.api.book_controller.get_templates(selectedBookId.value)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      templateList.value = result.data || []
      // 加载所有实体
      await loadAllEntities()
    } else {
      ElMessage.error(result.message || '加载模板失败')
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 加载所有实体
const loadAllEntities = async () => {
  try {
    const response = await window.pywebview.api.book_controller.get_entities(selectedBookId.value)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      allEntities.value = result.data || []
    } else {
      ElMessage.error(result.message || '加载实体失败')
    }
  } catch (error) {
    console.error('加载实体失败：', error)
    ElMessage.error('加载实体失败：' + error.message)
  }
}

// 获取模板中实体数量
const getEntityCount = (template) => {
  if (!template || !template.id) return 0
  return allEntities.value.filter(entity => entity.template_id === template.id).length
}

// 获取当前选中模板的实体列表
const currentTemplateEntities = computed(() => {
  if (!promptRule.value.template.selectedTemplateId) return []
  return allEntities.value.filter(entity => 
    entity.template_id === promptRule.value.template.selectedTemplateId
  )
})

// 获取当前选中模板的维度
const currentTemplateDimensions = computed(() => {
  if (!promptRule.value.template.selectedTemplateId) return []
  const template = templateList.value.find(t => t.id === promptRule.value.template.selectedTemplateId)
  return template?.dimensions || []
})

// 获取当前选中实体的维度
const currentEntityDimensions = computed(() => {
  if (!promptRule.value.template.selectedEntityId || !promptRule.value.template.selectedTemplateId) return []
  
  const entity = allEntities.value.find(e => 
    e.id === promptRule.value.template.selectedEntityId && 
    e.template_id === promptRule.value.template.selectedTemplateId
  )
  
  if (!entity || !entity.dimensions) return []
  
  // 将对象形式的dimensions转换为数组形式
  return Object.entries(entity.dimensions).map(([name, value]) => ({
    name,
    value
  }))
})

// 获取所有可能的维度名称（用于排除）
const allTemplateDimensions = computed(() => {
  if (!promptRule.value.template.selectedTemplateId) return []
  
  // 获取当前模板下所有实体
  const templateEntities = allEntities.value.filter(e => 
    e.template_id === promptRule.value.template.selectedTemplateId
  )
  
  // 收集所有实体中出现的维度名称
  const dimensionNames = new Set()
  templateEntities.forEach(entity => {
    if (entity.dimensions) {
      Object.keys(entity.dimensions).forEach(dimName => {
        dimensionNames.add(dimName)
      })
    }
  })
  
  return Array.from(dimensionNames).map(name => ({ name }))
})

// 转换场景数据为transfer组件格式
const allScenes = computed(() => {
  const scenes = []
  sceneData.value.pools.forEach(pool => {
    if (pool.scenes) {
      scenes.push(...pool.scenes.map(scene => ({
        key: scene.id,
        label: scene.title,
        description: scene.description
      })))
    }
  })
  return scenes
})

// 修复生成按钮可点击状态的计算属性
const canGenerate = computed(() => {
  // 情况1：已有内容可以预览
  if (generatedPromptContent.value && generatedPromptContent.value.trim()) {
    return true
  }
  
  // 情况2：根据规则生成新内容
  if (!selectedBookId.value) {
    return false
  }
  
  // 检查模板规则
  if (promptRule.value.template.enabled) {
    switch (promptRule.value.template.mode) {
      case 'single':
        if (!promptRule.value.template.selectedTemplateId) {
          return false
        }
        break
      case 'entity':
        if (!promptRule.value.template.selectedEntityId) {
          return false
        }
        break
      case 'all':
        if (!promptRule.value.template.selectedTemplateId) {
          return false
        }
        break
    }
  }
  
  // 检查场景规则
  if (promptRule.value.scene.enabled) {
    if (promptRule.value.scene.mode === 'manual') {
      if (!promptRule.value.scene.selectedSceneIds?.length) {
        return false
      }
    } else {
      if (!promptRule.value.scene.selectedPoolId) {
        return false
      }
    }
  }
  
  // 至少有一项规则启用
  return promptRule.value.template.enabled || promptRule.value.scene.enabled
})

// 添加生成状态变量
const generating = ref(false)

// 添加设置生成状态的函数
const setGenerating = (status) => {
  generating.value = status
}

// 修正生成提示词函数
const generatePrompt = async () => {
  if (!selectedBookId.value) {
    ElMessage.warning('请先选择一本书')
    return
  }
  
  try {
    setGenerating(true)
    
    // 如果提示词编辑区已有内容，直接预览
    if (generatedPromptContent.value && generatedPromptContent.value.trim()) {
      console.log('使用现有内容预览')
      await previewPrompt()
      return
    }
    
    // 否则根据规则生成新内容
    console.log('根据规则生成新内容')
    
    let promptParts = []
     
    // 处理模板部分
    if (promptRule.value.template.enabled) {
      switch (promptRule.value.template.mode) {
        case 'single':
          if (promptRule.value.template.selectedTemplateId) {
            const template = templateList.value.find(t => t.id === promptRule.value.template.selectedTemplateId)
            if (template) {
              // 使用占位符格式
              promptParts.push(`{{模板:${template.id}【${template.name || '未命名模板'}】}}`)
            }
          }
          break
          
        case 'entity':
          if (promptRule.value.template.selectedEntityId) {
            const entity = allEntities.value.find(e => e.id === promptRule.value.template.selectedEntityId)
            if (entity) {
              // 使用占位符格式，包含输出格式和排除维度信息
              const excludedDims = promptRule.value.template.entityExcludedDimensions || [];
              promptParts.push(
                `{{实体:${entity.id}:${promptRule.value.template.entityOutputFormat}:${excludedDims.join(',')}【${entity.name || '未命名实体'}】}}`
              )
            }
          }
          break
          
        case 'all':
          if (promptRule.value.template.selectedTemplateId) {
            const template = templateList.value.find(t => t.id === promptRule.value.template.selectedTemplateId)
            if (template) {
              // 使用占位符格式，包含输出格式和排除维度信息
              const excludedDims = promptRule.value.template.excludedDimensions || [];
              promptParts.push(
                `{{所有实体:${template.id}:${promptRule.value.template.allEntitiesOutputFormat}:${excludedDims.join(',')}【${template.name || '未命名模板'}的所有实体】}}`
              )
            }
          }
          break
      }
    }
    
    // 处理场景部分
    if (promptRule.value.scene.enabled) {
      if (promptRule.value.scene.mode === 'manual') {
        if (promptRule.value.scene.selectedSceneIds?.length > 0) {
          // 使用占位符格式
          promptParts.push(`{{场景:manual:${promptRule.value.scene.selectedSceneIds.join(',')}}}`)
        }
      } else {
        if (promptRule.value.scene.selectedPoolId) {
          // 使用占位符格式
          promptParts.push(
            `{{场景:random:${promptRule.value.scene.selectedPoolId}:${promptRule.value.scene.randomCount}}}`
          )
        }
      }
    }
    
    // 合并所有部分
    generatedPromptContent.value = promptParts.join('\n\n')
    
    // 添加到历史记录
    generationHistory.value.unshift({
      timestamp: new Date(),
      content: generatedPromptContent.value,
      rule: JSON.parse(JSON.stringify(promptRule.value))
    })
    
    // 限制历史记录数量
    if (generationHistory.value.length > 50) {
      generationHistory.value.pop()
    }
    
    ElMessage.success('提示词生成成功')
  } catch (error) {
    console.error('生成提示词失败:', error)
    ElMessage.error('生成提示词失败: ' + error.message)
  }
}

// 辅助函数
const filterTemplateContent = (template) => {
  if (!template || !template.dimensions) {
    console.warn('模板没有维度信息')
    return ''
  }
  
  const selectedDimensions = promptRule.value.template.selectedDimensions
  console.log('选中的维度:', selectedDimensions)
  
  if (!selectedDimensions || selectedDimensions.length === 0) {
    console.warn('没有选中任何维度')
    return ''
  }
  
  const content = []
  for (const dim of template.dimensions) {
    if (selectedDimensions.includes(dim.name)) {
      content.push(`${dim.name}：${dim.value}`)
    }
  }
  
  return content.join('\n')
}

// 修改 filterEntityContent 函数
const filterEntityContent = (entity) => {
  if (!entity || !entity.dimensions) {
    console.warn('实体没有维度信息')
    return ''
  }
  
  const excludedDimensions = promptRule.value.template.excludedDimensions || []
  console.log('排除的维度:', excludedDimensions)
  
  const content = []
  // 遍历对象形式的 dimensions
  for (const dimName in entity.dimensions) {
    if (!excludedDimensions.includes(dimName)) {
      content.push(`${dimName}：${entity.dimensions[dimName]}`)
    }
  }
  
  return content.join('\n')
}

const findSceneById = (sceneId) => {
  for (const pool of sceneData.value.pools) {
    const scene = pool.scenes?.find(s => s.id === sceneId)
    if (scene) return scene
  }
  return null
}

const formatSceneContent = (scene) => {
  if (!scene) return ''
  return `【场景：${scene.title || '未命名场景'}】\n${scene.description || '无描述'}`
}

const getRandomScenes = (scenes, count) => {
  if (!Array.isArray(scenes) || scenes.length === 0) return []
  const shuffled = [...scenes].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, Math.min(count, scenes.length))
}

// 复制生成结果
const copyResult = () => {
  if (!generatedPromptContent.value) {
    ElMessage.warning('没有可复制的内容')
    return
  }
  
  window.pywebview.api.copy_to_clipboard(generatedPromptContent.value)
    .then(() => ElMessage.success('已复制到剪贴板'))
    .catch(() => ElMessage.error('复制失败'))
}

// 清空生成结果
const clearResult = () => {
  generatedPromptContent.value = ''
  ElMessage.success('已清空生成结果')
}

// 重置生成器
const resetGenerator = () => {
  promptRule.value = {
    template: {
      enabled: false,
      mode: 'single',
      selectedTemplateId: null,
      selectedDimensions: [],
      excludedDimensions: []
    },
    scene: {
      enabled: false,
      mode: 'manual',
      selectedSceneIds: [],
      randomCount: 1,
      selectedPoolId: null
    }
  }
  
  generatedPromptName.value = ''
  generatedPromptDescription.value = ''
  generatedPromptContent.value = ''
  
  ElMessage.success('已重置生成器')
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知日期'
  
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '无效日期'
  
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 处理书籍变更
const handleBookChange = async (bookId) => {
  try {
    // 重置数据
    promptList.value = []
    templateList.value = []
    allEntities.value = []
    sceneData.value = { pools: [], currentPoolId: null }
    scenes.value = []
    
    // 并行加载新书籍的所有必要数据
    if (bookId) {
      loading.value = true
      await Promise.all([
        loadPrompts(),
        loadTemplates(),
        loadAllEntities(),
        loadSceneData()
      ])
      console.log('数据加载完成:', {
        templates: templateList.value,
        entities: allEntities.value
      }) // 调试日志
      ElMessage.success('书籍数据加载完成')
    }
  } catch (error) {
    console.error('加载书籍数据失败:', error)
    ElMessage.error('加载书籍数据失败，请重试')
  } finally {
    loading.value = false
  }
}

// 加载提示词
const loadPrompts = async () => {
  if (!selectedBookId.value) return;
  
  try {
    setLoading(true);
    
    // 调用后端API获取提示词列表
    const response = await window.pywebview.api.book_controller.get_prompts(selectedBookId.value);
    const result = typeof response === 'string' ? JSON.parse(response) : response;
    
    if (result.status === 'success') {
      // 更新提示词列表
      promptList.value = result.data || [];
    } else {
      console.error('加载提示词失败:', result.message);
    }
  } catch (error) {
    console.error('加载提示词失败:', error);
  } finally {
    setLoading(false);
  }
}

// 添加加载状态变量
const loading = ref(false)

// 添加设置加载状态的函数
const setLoading = (status) => {
  loading.value = status
}

// 加载场景数据
const loadSceneData = async () => {
  if (!selectedBookId.value) return

  try {
    const response = await window.pywebview.api.book_controller.get_scene_events(selectedBookId.value)
    const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response

    if (parsedResponse.status === "success") {
      // 确保数据结构完整
      const data = parsedResponse.data || {}
      sceneData.value = {
        pools: Array.isArray(data.pools) ? data.pools : [],
        currentPoolId: data.currentPoolId || null
      }

      // 如果没有当前卡池，创建一个默认卡池
      if (!sceneData.value.pools.length) {
        // 这里可能需要调用创建卡池的方法
        console.warn('没有找到场景卡池')
      }
    } else {
      throw new Error(parsedResponse.message || '加载失败')
    }
  } catch (error) {
    console.error('加载场景失败:', error)
    ElMessage.error(`加载场景失败: ${error.message}`)
    // 重置数据为有效的初始状态
    sceneData.value = { pools: [], currentPoolId: null }
  }
}

// 更新场景列表，添加更严格的空值检查
const updateScenes = () => {
  if (currentPool.value && currentPool.value.scenes && Array.isArray(currentPool.value.scenes)) {
    scenes.value = currentPool.value.scenes
  } else {
    scenes.value = []
  }
}

// 监听当前卡池变化
watch(() => sceneData.value.currentPoolId, updateScenes)

// 在生成提示词时插入场景内容
const insertSceneContent = () => {
  if (!selectedScene.value) return
  
  const sceneContent = `场景：${selectedScene.value.title || '未命名'}\n描述：${selectedScene.value.description || '无描述'}`
  
  // 在光标位置插入场景内容
  const textarea = document.querySelector('.generator-content textarea')
  if (textarea) {
    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd
    const textBefore = generatedPromptContent.value.substring(0, startPos)
    const textAfter = generatedPromptContent.value.substring(endPos)
    
    generatedPromptContent.value = `${textBefore}${sceneContent}${textAfter}`
    
    // 重新设置光标位置
    nextTick(() => {
      textarea.focus()
      const newCursorPos = startPos + sceneContent.length
      textarea.setSelectionRange(newCursorPos, newCursorPos)
    })
  } else {
    // 如果找不到textarea，直接追加
    generatedPromptContent.value += `\n${sceneContent}`
  }
}

// 过滤模板
const filteredTemplates = computed(() => {
  console.log('当前模板列表:', templateList.value) // 调试日志
  if (!templateList.value || !Array.isArray(templateList.value) || templateList.value.length === 0) {
    return []
  }
  
  if (!templateSearchQuery.value && !selectedDimension.value) {
    return templateList.value
  }
  
  return templateList.value.filter(template => {
    let matchesSearch = true
    let matchesDimension = true
    
    if (templateSearchQuery.value) {
      const query = templateSearchQuery.value.toLowerCase()
      matchesSearch = template && (
        (template.name && template.name.toLowerCase().includes(query)) ||
        (template.description && template.description.toLowerCase().includes(query))
      )
    }
    
    if (selectedDimension.value && template.dimensions) {
      matchesDimension = template.dimensions.some(dim => dim.name === selectedDimension.value)
    }
    
    return matchesSearch && matchesDimension
  })
})

// 过滤实体
const filteredEntities = computed(() => {
  console.log('当前实体列表:', allEntities.value) // 调试日志
  if (!allEntities.value || !Array.isArray(allEntities.value) || allEntities.value.length === 0) {
    return []
  }
  
  let filtered = allEntities.value
  
  if (selectedEntityType.value && selectedEntityType.value !== 'all') {
    filtered = filtered.filter(entity => entity.type === selectedEntityType.value)
  }
  
  if (entitySearchQuery.value) {
    const query = entitySearchQuery.value.toLowerCase()
    filtered = filtered.filter(entity => entity && (
      (entity.name && entity.name.toLowerCase().includes(query)) ||
      (entity.description && entity.description.toLowerCase().includes(query))
    ))
  }
  
  return filtered
})

// 选择模板
const selectTemplate = (template) => {
  selectedTemplateId.value = template.id
  console.log('选择模板:', template) // 调试日志
}

// 选择实体
const selectEntity = (entity) => {
  selectedEntityId.value = entity.id
  console.log('选择实体:', entity) // 调试日志
}

// 计算当前选择的实体或模板
const selectedEntityOrTemplate = computed(() => {
  if (selectorType.value === 'templates' && selectedTemplateId.value) {
    return templateList.value.find(t => t.id === selectedTemplateId.value)
  } else if (selectorType.value === 'entities' && selectedEntityId.value) {
    return allEntities.value.find(e => e.id === selectedEntityId.value)
  }
  return null
})

// 预览内容，应用占位符高亮
const previewContent = computed(() => {
  return generatedPromptContent.value
})

// 高亮占位符
const highlightedPreview = computed(() => {
  if (!previewContent.value) return ''
  
  // 用HTML标签替换占位符进行高亮
  return previewContent.value.replace(/\{\{([^}]+)\}\}/g, (match, content) => {
    return `<span class="placeholder-highlight">${match}</span>`
  })
})

// 占位符常量
const PLACEHOLDERS = {
  SINGLE_TEMPLATE: '{{单个模板}}',
  SINGLE_ENTITY: '{{单个实体}}',
  ALL_ENTITIES: '{{所有实体}}',
  SCENE: '{{场景描述}}'
}

// 插入占位符
const handleInsertCommand = (command) => {
  if (command === 'template') {
    // 显示模板选择对话框
    selectedTemplateForPlaceholder.value = null
    templateSelectorVisible.value = true
  } else if (command === 'entity') {
    // 显示实体选择对话框
    selectedTemplateForEntity.value = null
    selectedEntityForPlaceholder.value = null
    entitySelectorVisible.value = true
  } else if (command === 'scene') {
    // 直接插入场景占位符
    insertScenePlaceholder()
  }
}

// 保存生成的提示词规则
const saveGeneratedPrompt = async () => {
  try {
    // 表单验证
    if (!generatedPromptName.value || !generatedPromptName.value.trim()) {
      ElMessage.warning('请输入提示词规则名称');
      return;
    }
    
    loading.value = true;
    
    // 准备保存的数据
    const promptData = {
      id: currentEditingPromptId.value || generateUUID(),
      name: generatedPromptName.value,
      description: generatedPromptDescription.value || '',
      rule: {
        ...promptRule.value,
        content: generatedPromptContent.value, // 保存编辑器内容
        editorConfig: editorConfig.value, // 保存编辑器配置
        prompts: promptRule.value.prompts || [], // 确保prompts数组存在
        updateTime: new Date().toISOString()
      },
      type: 'generator'
    };
    
    // 调用后端API保存
    const response = await window.pywebview.api.book_controller.add_prompt(
      selectedBookId.value, 
      promptData
    );
    
    const result = typeof response === 'string' ? JSON.parse(response) : response;
    
    if (result.status === 'success') {
      ElMessage.success(currentEditingPromptId.value ? '规则更新成功' : '规则创建成功');
      
      // 更新本地列表
      if (currentEditingPromptId.value) {
        const index = promptList.value.findIndex(p => p.id === currentEditingPromptId.value);
        if (index !== -1) {
          promptList.value[index] = promptData;
        }
      } else {
        promptList.value.push(promptData);
      }
      
      return true;
    } else {
      ElMessage.error(result.message || '保存失败');
      return false;
    }
  } catch (error) {
    console.error('保存提示词规则失败:', error);
    ElMessage.error('保存失败: ' + error.message);
    return false;
  } finally {
    loading.value = false;
  }
};

// 添加保存到规则的处理函数
const handleSaveToRule = async (prompt) => {
  try {
    // 确保有选择的规则
    if (!promptRule.value) {
      ElMessage.warning('请先创建或选择一个规则');
      return;
    }
    
    // 确保有prompts数组
    if (!promptRule.value.prompts) {
      promptRule.value.prompts = [];
    }
    
    // 添加到当前规则的提示词列表
    promptRule.value.prompts.push({
      id: generateUUID(),
      name: prompt.name,
      description: prompt.description,
      content: prompt.content,
      timestamp: prompt.timestamp
    });
    
    // 保存更新后的规则并确保UI更新
    const saved = await saveGeneratedPrompt();
    
    if (saved) {
      // 当前规则ID
      const ruleId = currentEditingPromptId.value;
      
      // 手动更新本地列表中的规则
      if (ruleId) {
        const index = promptList.value.findIndex(p => p.id === ruleId);
        if (index !== -1) {
          // 更新本地列表中的prompts数组
          promptList.value[index].rule = JSON.parse(JSON.stringify(promptRule.value));
        }
      }
      
      // 关闭预览对话框
      previewDialogVisible.value = false;
      
      // 提示成功
      ElMessage.success('内容已添加到当前规则');
    }
  } catch (error) {
    console.error('添加到规则失败:', error);
    ElMessage.error('添加失败: ' + error.message);
  }
};

// 生成唯一ID的辅助函数
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// 加载提示词规则
const loadPromptRule = (prompt) => {
  if (!prompt.rule) {
    ElMessage.warning('此提示词不是生成规则类型')
    return
  }

  try {
    // 恢复规则配置
    promptRule.value = {
      template: {
        enabled: prompt.rule.template.enabled,
        mode: prompt.rule.template.mode || 'single',
        selectedTemplateId: prompt.rule.template.selectedTemplateId,
        selectedEntityId: prompt.rule.template.selectedEntityId,
        selectedDimensions: prompt.rule.template.selectedDimensions || [],
        excludedDimensions: prompt.rule.template.excludedDimensions || [],
        entityExcludedDimensions: prompt.rule.template.entityExcludedDimensions || [],
        entityOutputFormat: prompt.rule.template.entityOutputFormat || 'text',
        allEntitiesOutputFormat: prompt.rule.template.allEntitiesOutputFormat || 'text'
      },
      scene: {
        enabled: prompt.rule.scene.enabled ?? false,
        mode: prompt.rule.scene.mode || 'manual',
        selectedSceneIds: prompt.rule.scene.selectedSceneIds || [],
        randomCount: prompt.rule.scene.randomCount || 1,
        selectedPoolId: prompt.rule.scene.selectedPoolId
      }
    }

    // 设置基本信息
    generatedPromptName.value = prompt.name
    generatedPromptDescription.value = prompt.description
    generatedPromptContent.value = prompt.content

    // 切换到生成器视图
    activeTab.value = 'generator'
    
    ElMessage.success('已加载生成规则')
  } catch (error) {
    console.error('加载生成规则失败:', error)
    ElMessage.error('加载规则失败')
  }
}

// 生命周期钩子
onMounted(async () => {
  try {
    // 确保书籍列表已加载
    await bookStore.loadBooks()
    
    // 如果有书籍列表，默认选择第一本书
    if (bookStore.bookList?.length > 0) {
      selectedBookId.value = bookStore.bookList[0].id
      await handleBookChange(selectedBookId.value)
    }
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('初始化失败，请刷新页面重试')
  }

  await Promise.all([


  ])
})

// 在提示词列表中显示规则类型标记
const getPromptTypeTag = (prompt) => {
  return prompt.type === 'generator' ? '生成规则' : '固定内容'
}

// 结果相关
const activeResultTab = ref('current')
const generationHistory = ref([])
const batchGenerateDialogVisible = ref(false)
const batchGenerateConfig = ref({
  count: 5
})
const batchResults = ref([])

// 批量生成对话框
const showBatchGenerateDialog = () => {
  batchResults.value = []
  batchGenerateDialogVisible.value = true
}

const executeBatchGenerate = async () => {
  if (!canGenerate.value) {
    ElMessage.warning('请先配置生成规则')
    return
  }
  
  generating.value = true
  batchResults.value = []
  
  try {
    for (let i = 0; i < batchGenerateConfig.value.count; i++) {
      const result = await generatePrompt()
      if (result) {
        batchResults.value.push(result)
      }
    }
    ElMessage.success(`成功生成 ${batchResults.value.length} 个提示词`)
  } catch (error) {
    console.error('批量生成失败:', error)
    ElMessage.error('批量生成失败')
  } finally {
    generating.value = false
  }
}

// 从历史记录恢复
const restoreFromHistory = (historyItem) => {
  generatedPromptContent.value = historyItem.content
  if (historyItem.rule) {
    promptRule.value = JSON.parse(JSON.stringify(historyItem.rule))
  }
  activeResultTab.value = 'current'
  ElMessage.success('已恢复历史生成结果')
}

// 复制历史内容
const copyHistoryContent = (historyItem) => {
  window.pywebview.api.copy_to_clipboard(historyItem.content)
    .then(() => ElMessage.success('已复制到剪贴板'))
    .catch(() => ElMessage.error('复制失败'))
}

// 使用批量生成结果
const useBatchResult = (result) => {
  generatedPromptContent.value = result
  batchGenerateDialogVisible.value = false
  ElMessage.success('已使用所选结果')
}

// 复制批量生成结果
const copyBatchResult = (result) => {
  window.pywebview.api.copy_to_clipboard(result)
    .then(() => ElMessage.success('已复制到剪贴板'))
    .catch(() => ElMessage.error('复制失败'))
}

// 处理模板变更
const handleTemplateChange = () => {
  // 清空实体选择
  promptRule.value.template.selectedEntityId = null
  // 清空维度选择
  promptRule.value.template.selectedDimensions = []
}

// 新增：过滤单个实体内容
const filterSingleEntityContent = (entity) => {
  if (!entity || !entity.dimensions) {
    console.warn('实体没有维度信息')
    return ''
  }
  
  const selectedDimensions = promptRule.value.template.selectedDimensions
  console.log('选中的维度:', selectedDimensions)
  
  if (!selectedDimensions || selectedDimensions.length === 0) {
    console.warn('没有选中任何维度')
    return ''
  }
  
  const content = []
  for (const dimName of selectedDimensions) {
    if (entity.dimensions[dimName]) {
      content.push(`${dimName}：${entity.dimensions[dimName]}`)
    }
  }
  
  return content.join('\n')
}

// 预览对话框
const previewDialogVisible = ref(false)

// 重写预览函数，完全替换当前的预览逻辑
const previewPrompt = async () => {
  try {
    if (!generatedPromptContent.value) {
      ElMessage.warning('没有内容可预览');
      return;
    }
    
    let previewContent = generatedPromptContent.value;
    console.log("开始预览处理，原始内容:", previewContent);
    
    // 1. 处理模板占位符 {{模板:templateId【模板名称】}}
    const templateRegex = /{{模板:([^【}]+)(?:【([^】]+)】)?}}/g;
    let templateMatch;
    
    while ((templateMatch = templateRegex.exec(previewContent)) !== null) {
      const templateId = templateMatch[1];
      const templateNameInPlaceholder = templateMatch[2]; // 从占位符中提取的名称
      console.log("匹配到模板占位符:", templateMatch[0], "模板ID:", templateId, "占位符中名称:", templateNameInPlaceholder);
      
      const template = templateList.value.find(t => t.id === templateId);
      const templateName = template ? template.name : (templateNameInPlaceholder || '未知模板');
      
      if (template) {
        // 格式化模板结构
        const templateStructure = {
          name: template.name,
          dimensions: Array.isArray(template.dimensions) ? template.dimensions : 
                     Object.entries(template.dimensions || {}).map(([name, value]) => ({ name, value }))
        };
        
        const templateContent = `【模板：${templateName}】\n${JSON.stringify(templateStructure, null, 2)}`;
        previewContent = previewContent.replace(templateMatch[0], templateContent);
      } else {
        previewContent = previewContent.replace(templateMatch[0], `【未找到ID为 ${templateId} 的模板】`);
      }
    }
    
    // 2. 处理随机实体占位符 {{实体:random:templateId:format:excludedDims【名称】}}
    const randomEntityRegex = /{{实体:random:([^:【}]+)(?::([^:【}]+))?(?::([^:【}]*))?(?:【([^】]+)】)?}}/g;
    let randomMatch;
    
    while ((randomMatch = randomEntityRegex.exec(previewContent)) !== null) {
      const templateId = randomMatch[1];
      const format = randomMatch[2] || 'text';
      const excludedDimsStr = randomMatch[3] || '';
      const nameInPlaceholder = randomMatch[4]; // 从占位符中提取的名称
      
      console.log("匹配到随机实体占位符:", randomMatch[0], 
                  "模板ID:", templateId, 
                  "格式:", format, 
                  "排除维度:", excludedDimsStr,
                  "占位符中名称:", nameInPlaceholder);
      
      const excludedDims = excludedDimsStr ? excludedDimsStr.split(',').filter(Boolean) : [];
      
      // 从该模板中随机选择一个实体
      const templateEntities = allEntities.value.filter(e => e.template_id === templateId);
      console.log(`找到${templateEntities.length}个实体属于模板${templateId}`);
      
      if (templateEntities.length > 0) {
        const randomIndex = Math.floor(Math.random() * templateEntities.length);
        const entity = templateEntities[randomIndex];
        
        console.log("随机选择实体:", entity.name, entity.id);
        let entityContent = formatEntityContent(entity, format, excludedDims);
        previewContent = previewContent.replace(randomMatch[0], entityContent);
      } else {
        const templateName = templateList.value.find(t => t.id === templateId)?.name || 
                            (nameInPlaceholder ? nameInPlaceholder.replace(/^随机/, '').replace(/实体$/, '') : '未知模板');
        previewContent = previewContent.replace(randomMatch[0], `【无法找到"${templateName}"模板的实体】`);
      }
    }
    
    // 3. 处理普通实体占位符 {{实体:entityId:format:excludedDims【名称】}}
    const entityRegex = /{{实体:(?!random:)([^:【}]+)(?::([^:【}]*))?(?::([^:【}]*))?(?:【([^】]+)】)?:?}}/g;
    let entityMatch;
    
    while ((entityMatch = entityRegex.exec(previewContent)) !== null) {
      const entityId = entityMatch[1];
      const format = entityMatch[2] || 'text';
      const excludedDimsStr = entityMatch[3] || '';
      const nameInPlaceholder = entityMatch[4]; // 从占位符中提取的名称
      
      console.log("匹配到实体占位符:", entityMatch[0], 
                  "实体ID:", entityId, 
                  "格式:", format, 
                  "排除维度:", excludedDimsStr,
                  "占位符中名称:", nameInPlaceholder);
      
      const excludedDimensions = excludedDimsStr ? excludedDimsStr.split(',').filter(Boolean) : [];
      
      // 查找实体
      const entity = allEntities.value.find(e => e.id === entityId);
      console.log("找到实体:", entity ? entity.name : "未找到");
      
      if (entity) {
        let entityContent = formatEntityContent(entity, format, excludedDimensions);
        previewContent = previewContent.replace(entityMatch[0], entityContent);
      } else {
        const entityName = nameInPlaceholder || '未知实体';
        previewContent = previewContent.replace(entityMatch[0], `【无法找到"${entityName}"实体】`);
      }
    }
    
    // 4. 处理所有实体占位符 {{所有实体:templateId:format:excludedDims【名称】}}
    const allEntitiesRegex = /{{所有实体:([^:【}]+)(?::([^:【}]*))?(?::([^:【}]*))?(?:【([^】]+)】)?:?}}/g;
    let allEntitiesMatch;
    
    while ((allEntitiesMatch = allEntitiesRegex.exec(previewContent)) !== null) {
      const templateId = allEntitiesMatch[1];
      const format = allEntitiesMatch[2] || 'text';
      const excludedDimsStr = allEntitiesMatch[3] || '';
      const nameInPlaceholder = allEntitiesMatch[4]; // 从占位符中提取的名称
      
      console.log("匹配到所有实体占位符:", allEntitiesMatch[0], 
                  "模板ID:", templateId, 
                  "格式:", format, 
                  "排除维度:", excludedDimsStr,
                  "占位符中名称:", nameInPlaceholder);
      
      const excludedDimensions = excludedDimsStr ? excludedDimsStr.split(',').filter(Boolean) : [];
      
      // 获取该模板下的所有实体
      const templateEntities = allEntities.value.filter(e => e.template_id === templateId);
      console.log(`找到${templateEntities.length}个实体属于模板${templateId}`);
      
      if (templateEntities.length > 0) {
        if (format === 'json') {
          // JSON格式输出
          const entitiesArray = templateEntities.map(entity => {
            const filteredDimensions = {};
            for (const [key, value] of Object.entries(entity.dimensions || {})) {
              if (!excludedDimensions.includes(key)) {
                filteredDimensions[key] = value;
              }
            }
            
            return {
              id: entity.id,
              name: entity.name,
              type: entity.type,
              description: entity.description,
              dimensions: filteredDimensions
            };
          });
          
          const allEntitiesContent = `【模板"${templateList.value.find(t => t.id === templateId)?.name || '未知模板'}"的所有实体】\n` + 
                                   JSON.stringify({
            template_id: templateId,
            template_name: templateList.value.find(t => t.id === templateId)?.name || '未知模板',
            entity_count: templateEntities.length,
            entities: entitiesArray
          }, null, 2);
          
          previewContent = previewContent.replace(allEntitiesMatch[0], allEntitiesContent);
        } else {
          // 文本格式输出 - 改进标题显示
          const templateName = templateList.value.find(t => t.id === templateId)?.name || '未知模板';
          const entitiesContent = `【${templateName}的所有实体 (共${templateEntities.length}个)】\n\n` + 
            templateEntities.map(entity => {
              const parts = [`🔹 ${entity.name || '未命名实体'}`];
              if (entity.type) parts.push(`类型：${entity.type}`);
              if (entity.description) parts.push(`描述：${entity.description}`);
              
              for (const [key, value] of Object.entries(entity.dimensions || {})) {
                if (!excludedDimensions.includes(key)) {
                  parts.push(`${key}：${value}`);
                }
              }
              
              return parts.join('\n');
            }).join('\n\n');
          
          previewContent = previewContent.replace(allEntitiesMatch[0], entitiesContent);
        }
      } else {
        previewContent = previewContent.replace(allEntitiesMatch[0], `【无法找到模板ID为 ${templateId} 的实体】`);
      }
    }
    
    // 5. 处理场景占位符 {{场景:mode:ids/poolId:count【名称】}}
    const sceneRegex = /{{场景:([^:【}]+):([^:【}]+)(?::([^:【}]*))?(?:【([^】]+)】)?}}/g;
    let sceneMatch;
    
    while ((sceneMatch = sceneRegex.exec(previewContent)) !== null) {
      const mode = sceneMatch[1]; // manual 或 random
      const ids = sceneMatch[2]; // manual模式下是sceneIds，random模式下是poolId
      const count = sceneMatch[3] || '1'; // random模式下的数量
      const nameInPlaceholder = sceneMatch[4]; // 从占位符中提取的名称
      
      console.log("匹配到场景占位符:", sceneMatch[0], 
                  "模式:", mode, 
                  "ID值:", ids, 
                  "数量:", count,
                  "占位符中名称:", nameInPlaceholder);
      
      let scenesContent = '';
      
      if (mode === 'manual') {
        // 手动指定的场景
        const sceneIds = ids.split(',').filter(Boolean);
        const scenes = [];
        
        // 从所有场景池中查找指定ID的场景
        for (const pool of sceneData.value.pools) {
          if (pool.scenes) {
            for (const scene of pool.scenes) {
              if (sceneIds.includes(scene.id)) {
                scenes.push(scene);
              }
            }
          }
        }
        
        console.log(`找到${scenes.length}个指定场景`);
        
        if (scenes.length > 0) {
          scenesContent = scenes.map(scene => 
            `【场景：${scene.title || '未命名'}】\n${scene.description || '无描述'}`
          ).join('\n\n');
        } else {
          scenesContent = '【未找到指定的场景】';
        }
      } else if (mode === 'random') {
        // 随机场景
        const poolId = ids;
        const numCount = parseInt(count, 10) || 1;
        
        // 查找指定的场景池
        const pool = sceneData.value.pools.find(p => p.id === poolId);
        
        if (pool && pool.scenes && pool.scenes.length > 0) {
          // 随机抽取场景
          const shuffled = [...pool.scenes].sort(() => 0.5 - Math.random());
          const randomScenes = shuffled.slice(0, Math.min(numCount, shuffled.length));
          
          console.log(`从场景池随机抽取了${randomScenes.length}个场景`);
          
          scenesContent = randomScenes.map(scene => 
            `【场景：${scene.title || '未命名'}】\n${scene.description || '无描述'}`
          ).join('\n\n');
        } else {
          scenesContent = `【未找到ID为 ${poolId} 的场景池或其中没有场景】`;
        }
      } else {
        scenesContent = `【不支持的场景模式：${mode}】`;
      }
      
      previewContent = previewContent.replace(sceneMatch[0], scenesContent);
    }
    
    console.log("预览处理完成");
    
    // 显示预览对话框
    previewDialogVisible.value = true;
    previewPromptContent.value = previewContent;
  } catch (error) {
    console.error('生成预览内容失败:', error);
    ElMessage.error('生成预览内容失败: ' + error.message);
  }
}

// 处理将提示词保存到规则
const handleSavePromptToRule = (promptData) => {
  // 确保 prompts 数组存在
  if (!promptRule.value.prompts) {
    promptRule.value.prompts = [];
  }
  
  // 确保提示词有唯一ID
  if (!promptData.id) {
    promptData.id = generateUniqueId();
  }
  
  // 添加到规则的提示词列表
  promptRule.value.prompts.push(promptData);
  ElMessage.success('提示词已添加到规则');
}

// 确保格式化函数正确处理实体内容
const formatEntityContent = (entity, format, excludedDimensions) => {
  if (!entity) {
    console.warn('formatEntityContent: 实体为空');
    return '【无效实体】';
  }
  
  try {
    console.log("格式化实体:", entity.name, "格式:", format, "排除维度:", excludedDimensions);
    
  if (format === 'json') {
    // JSON格式输出
      const filteredDimensions = {};
    for (const [key, value] of Object.entries(entity.dimensions || {})) {
      if (!excludedDimensions.includes(key)) {
          filteredDimensions[key] = value;
      }
    }
      
    return JSON.stringify({
        id: entity.id,
      name: entity.name,
      type: entity.type,
        description: entity.description,
      dimensions: filteredDimensions
      }, null, 2);
  } else {
    // 文本格式输出
      const parts = [`【实体：${entity.name || '未命名实体'}】`];
      if (entity.type) parts.push(`类型：${entity.type}`);
      if (entity.description) parts.push(`描述：${entity.description}`);
      
      if (entity.dimensions) {
        for (const [key, value] of Object.entries(entity.dimensions)) {
      if (!excludedDimensions.includes(key)) {
            parts.push(`${key}：${value}`);
      }
    }
  }
      
      return parts.join('\n');
}
  } catch (error) {
    console.error('格式化实体内容失败:', error);
    return `【格式化失败：${error.message}】`;
  }
};

// 使用排除维度逻辑过滤单个实体内容，但保留description
const filterEntityContentWithExclusion = (entity, excludedDimensions) => {
  if (!entity || !entity.dimensions) {
    console.warn('实体没有维度信息')
    return ''
  }
  
  excludedDimensions = excludedDimensions || []
  console.log('排除的维度:', excludedDimensions)
  
  const content = []
  
  // 首先添加实体描述（如果有）
  if (entity.description) {
    content.push(`描述：${entity.description}`)
  }
  
  // 然后添加其他未被排除的维度
  for (const dimName in entity.dimensions) {
    if (!excludedDimensions.includes(dimName)) {
      content.push(`${dimName}：${entity.dimensions[dimName]}`)
    }
  }
  
  return content.join('\n')
}

// 处理实体模板变更
const handleEntityTemplateChange = () => {
  // 清空实体选择
  promptRule.value.template.selectedEntityId = null
  // 清空维度选择
  promptRule.value.template.selectedDimensions = []
}

// 获取实体列表
const getEntitiesForTemplate = (templateId) => {
  return allEntities.value.filter(entity => entity.template_id === templateId)
}

// 获取维度列表
const getDimensionsForEntity = (templateId, entityId) => {
  return allEntities.value.find(entity => entity.template_id === templateId && entity.id === entityId)?.dimensions || []
}

// 获取所有维度
const getAllDimensionsForTemplate = (templateId) => {
  return allEntities.value.filter(entity => entity.template_id === templateId).flatMap(entity => Object.values(entity.dimensions))
}

// 生成单个模板内容
const generateSingleTemplateContent = async () => {
  try {
    const templateId = promptRule.value.template.selectedTemplateId
    if (!templateId) {
      console.warn('未选择模板')
      return null
    }
    
    const template = templateList.value.find(t => t.id === templateId)
    if (!template) {
      console.warn('未找到选中的模板')
      return null
    }
    
    console.log('找到单个模板:', template.name)
    
    // 创建一个干净的模板结构副本
    const templateStructure = {
      name: template.name,
      dimensions: Array.isArray(template.dimensions) ? template.dimensions : 
                 Object.entries(template.dimensions || {}).map(([name, value]) => ({ name, value }))
    }
    
    // 转换为格式化的JSON字符串
    return JSON.stringify(templateStructure, null, 2)
  } catch (error) {
    console.error('生成单个模板内容失败:', error)
    return null
  }
}

// 生成单个实体内容
const generateSingleEntityContent = async () => {
  try {
    const templateId = promptRule.value.template.selectedTemplateId
    const entityId = promptRule.value.template.selectedEntityId
    
    if (!templateId || !entityId) {
      console.warn('未选择模板或实体')
      return null
    }
    
    const entity = allEntities.value.find(e => 
      e.id === entityId && e.template_id === templateId
    )
    
    if (!entity) {
      console.warn('未找到选中的实体')
      return null
    }
    
    console.log('找到单个实体:', entity.name)
    
    if (promptRule.value.template.entityOutputFormat === 'json') {
      // JSON格式输出
      const excludedDimensions = promptRule.value.template.entityExcludedDimensions || []
      const filteredDimensions = {}
      
      // 排除选中的维度
      for (const dimName in entity.dimensions) {
        if (!excludedDimensions.includes(dimName)) {
          filteredDimensions[dimName] = entity.dimensions[dimName]
        }
      }
      
      // 创建实体的JSON结构
      const entityStructure = {
        name: entity.name,
        type: entity.type,
        description: entity.description,
        dimensions: filteredDimensions
      }
      
      // 转换为格式化的JSON字符串
      return JSON.stringify(entityStructure, null, 2)
    } else {
      // 文本格式输出
      return filterEntityContentWithExclusion(
        entity, 
        promptRule.value.template.entityExcludedDimensions
      )
    }
  } catch (error) {
    console.error('生成单个实体内容失败:', error)
    return null
  }
}

// 生成所有实体内容
const generateAllEntitiesContent = async () => {
  try {
    const templateId = promptRule.value.template.allEntitiesTemplate.selectedTemplateId
    
    if (!templateId) {
      console.warn('未选择模板')
      return null
    }
    
    const template = templateList.value.find(t => t.id === templateId)
    if (!template) {
      console.warn('未找到选中的模板')
      return null
    }
    
    console.log('找到模板(所有实体):', template.name)
    
    const entities = allEntities.value.filter(e => e.template_id === templateId)
    console.log(`模板包含 ${entities.length} 个实体`)
    
    if (entities.length === 0) {
      console.warn('该模板下没有实体')
      return null
    }
    
    if (promptRule.value.template.allEntitiesTemplate.outputFormat === 'json') {
      // JSON格式输出
      const excludedDimensions = promptRule.value.template.allEntitiesTemplate.excludedDimensions || []
      const entitiesArray = []
      
      for (const entity of entities) {
        if (!entity || !entity.dimensions) continue
        
        // 过滤掉排除的维度
        const filteredDimensions = {}
        for (const dimName in entity.dimensions) {
          if (!excludedDimensions.includes(dimName)) {
            filteredDimensions[dimName] = entity.dimensions[dimName]
          }
        }
        
        entitiesArray.push({
          name: entity.name,
          type: entity.type,
          description: entity.description, // 添加描述字段
          dimensions: filteredDimensions
        })
      }
      
      // 创建包含所有实体的JSON结构
      const entitiesStructure = {
        template: template.name,
        entities: entitiesArray
      }
      
      // 转换为格式化的JSON字符串
      return JSON.stringify(entitiesStructure, null, 2)
    } else {
      // 文本格式输出
      const entityParts = []
      for (const entity of entities) {
        const filteredContent = filterEntityContentWithExclusion(
          entity, 
          promptRule.value.template.allEntitiesTemplate.excludedDimensions
        )
        if (filteredContent) {
          entityParts.push(`【实体：${entity.name}】\n${filteredContent}`)
        }
      }
      
      return entityParts.join('\n\n')
    }
  } catch (error) {
    console.error('生成所有实体内容失败:', error)
    return null
  }
}

// 生成场景内容
const generateSceneContent = async () => {
  try {
    let selectedScenes = []
    
    if (promptRule.value.scene.mode === 'manual') {
      // 手动选择场景
      const sceneIds = promptRule.value.scene.selectedSceneIds || []
      if (sceneIds.length === 0) {
        console.warn('未选择任何场景')
        return null
      }
      
      // 从所有场景池中查找选中的场景
      for (const pool of sceneData.value.pools) {
        for (const scene of pool.scenes) {
          if (sceneIds.includes(scene.id)) {
            selectedScenes.push(scene)
          }
        }
      }
    } else {
      // 随机抽取场景
      const poolId = promptRule.value.scene.selectedPoolId
      if (!poolId) {
        console.warn('未选择场景池')
        return null
      }
      
      const pool = sceneData.value.pools.find(p => p.id === poolId)
      if (!pool || !pool.scenes || pool.scenes.length === 0) {
        console.warn('场景池为空或不存在')
        return null
      }
      
      // 随机抽取指定数量的场景
      const count = Math.min(promptRule.value.scene.randomCount, pool.scenes.length)
      const shuffled = [...pool.scenes].sort(() => 0.5 - Math.random())
      selectedScenes = shuffled.slice(0, count)
    }
    
    console.log('选中的场景:', selectedScenes)
    
    // 生成场景内容
    const sceneParts = []
    for (const scene of selectedScenes) {
      sceneParts.push(`【场景：${scene.title}】\n${scene.description || '无描述'}`)
    }
    
    return sceneParts.join('\n\n')
  } catch (error) {
    console.error('生成场景内容失败:', error)
    return null
  }
}

// 在 AiPrompt.vue 中添加预览内容的状态
const previewPromptContent = ref('')

// 插入模板占位符
const insertTemplatePlaceholder = (command) => {
  if (command === 'single') {
    promptRule.value.template.singleTemplate.placeholderText = '{{单个模板}}'
  } else if (command === 'entity') {
    promptRule.value.template.entityTemplate.placeholderText = '{{单个实体}}'
  } else if (command === 'all') {
    promptRule.value.template.allEntitiesTemplate.placeholderText = '{{所有实体}}'
  }
  generatedPromptContent.value += `\n${promptRule.value.template[command].placeholderText}`
}


// 模板选择对话框
const templateSelectorVisible = ref(false)
const selectedTemplateForPlaceholder = ref(null)

// 实体选择对话框
const entitySelectorVisible = ref(false)
const selectedTemplateForEntity = ref(null)
const selectedEntityForPlaceholder = ref(null)

// 处理实体选择器中的模板变更
const handleEntitySelectorTemplateChange = () => {
  selectedEntityForPlaceholder.value = null
}

// 插入实体占位符
const insertEntityPlaceholder = () => {
  if (!selectedEntityForPlaceholder.value) return
  
  const entity = allEntities.value.find(e => e.id === selectedEntityForPlaceholder.value)
  if (!entity) return
  
  const entityContent = filterEntityContentWithExclusion(entity, promptRule.value.template.entityTemplate.excludedDimensions)
  
  // 在光标位置插入实体内容
  const textarea = document.querySelector('.generator-content textarea')
  if (textarea) {
    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd
    const textBefore = generatedPromptContent.value.substring(0, startPos)
    const textAfter = generatedPromptContent.value.substring(endPos)
    
    generatedPromptContent.value = `${textBefore}${entityContent}${textAfter}`
    
    // 重新设置光标位置
    nextTick(() => {
      textarea.focus()
      const newCursorPos = startPos + entityContent.length
      textarea.setSelectionRange(newCursorPos, newCursorPos)
    })
  } else {
    // 如果找不到textarea，直接追加
    generatedPromptContent.value += `\n${entityContent}`
  }
  
  entitySelectorVisible.value = false
}

// 插入单个模板占位符
const insertSingleTemplatePlaceholder = () => {
  if (!promptRule.value.template.selectedTemplateId) {
    ElMessage.warning('请先选择一个模板')
    return
  }
  
  const template = templateList.value.find(t => t.id === promptRule.value.template.selectedTemplateId)
  if (!template) {
    ElMessage.warning('未找到选中的模板')
    return
  }
  
  // 创建带有内嵌名称的占位符
  const placeholder = `{{模板:${template.id}【${template.name || '未命名模板'}】}}`
  
  // 在编辑区域插入占位符
  insertTextToEditor(placeholder)
  
  // 提供反馈
  ElMessage.success(`已插入"${template.name}"模板的占位符`)
}

// 插入单个实体占位符
const insertSingleEntityPlaceholder = () => {
  if (!promptRule.value.template.selectedEntityId) {
    ElMessage.warning('请先选择一个实体')
    return
  }
  
  const entity = allEntities.value.find(e => e.id === promptRule.value.template.selectedEntityId)
  if (!entity) {
    ElMessage.warning('未找到选中的实体')
    return
  }
  
  // 创建带有内嵌名称的占位符
  const placeholder = `{{实体:${promptRule.value.template.selectedEntityId}:${promptRule.value.template.entityOutputFormat}:${promptRule.value.template.entityExcludedDimensions.join(',')}【${entity.name || '未命名实体'}】}}`
  
  // 确保正确插入
  insertTextToEditor(placeholder)
  
  // 提供反馈
  ElMessage.success(`已插入实体"${entity.name}"的占位符`)
}

// 插入所有实体占位符
const insertAllEntitiesPlaceholder = () => {
  if (!promptRule.value.template.selectedTemplateId) {
    ElMessage.warning('请先选择一个模板')
    return
  }
  
  const template = templateList.value.find(t => t.id === promptRule.value.template.selectedTemplateId)
  if (!template) {
    ElMessage.warning('未找到选中的模板')
    return
  }
  
  // 创建带有内嵌名称的占位符
  const placeholder = `{{所有实体:${template.id}:${promptRule.value.template.allEntitiesOutputFormat}:${promptRule.value.template.excludedDimensions.join(',')}【${template.name || '未命名模板'}的所有实体】}}`
  
  // 在编辑区域插入占位符
  insertTextToEditor(placeholder)
  
  // 提供反馈
  ElMessage.success(`已插入"${template.name}"模板的所有实体占位符`)
}

// 在编辑区域插入文本的通用方法
const insertTextToEditor = (text) => {
  console.log("尝试插入文本:", text)
  
  // 验证占位符格式
  if (text.includes('{{') && text.includes('}}')) {
    debugPlaceholder(text)
  }
  
  const textarea = document.querySelector('.result-content-wrapper .el-textarea__inner')
  if (textarea) {
    console.log("找到文本区域元素")
    // 获取当前光标位置
    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd
    
    // 在光标位置插入文本
    const currentText = generatedPromptContent.value || ''
    const textBefore = currentText.substring(0, startPos)
    const textAfter = currentText.substring(endPos)
    
    // 更新内容
    generatedPromptContent.value = `${textBefore}${text}${textAfter}`
    
    // 提供反馈
    ElMessage.success('已插入占位符')
    
    // 重新设置光标位置到插入文本之后
    nextTick(() => {
      textarea.focus()
      const newCursorPos = startPos + text.length
      textarea.setSelectionRange(newCursorPos, newCursorPos)
    })
  } else {
    console.warn("未找到文本区域元素，将直接追加内容")
    // 如果找不到文本区域，直接追加
    if (!generatedPromptContent.value) {
      generatedPromptContent.value = text
    } else {
      generatedPromptContent.value += '\n' + text
    }
    
    // 提供反馈
    ElMessage.success('已追加占位符到内容末尾')
  }
  
  // 添加全局调试对象
  window.debugPVV.lastInsertedText = text
}



// 获取场景池数据
const fetchScenePools = async () => {
  try {
    // 这里应该调用后端API获取场景池数据
    const pools = await window.pywebview.api.get_scene_pools()
    scenePools.value = pools || []
  } catch (error) {
    console.error('获取场景池失败:', error)
    ElMessage.error('获取场景池失败')
  }
}


// 在组件挂载时获取数据
onMounted(async () => {
  try {
    // 确保书籍列表已加载
    await bookStore.loadBooks()
    
    // 如果有书籍列表，默认选择第一本书
    if (bookStore.bookList?.length > 0) {
      selectedBookId.value = bookStore.bookList[0].id
      await handleBookChange(selectedBookId.value)
    }
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('初始化失败，请刷新页面重试')
  }

  await Promise.all([
    loadSceneData(),

  ])
})

// 在 setup 中添加场景相关的数据和方法
const sceneList = ref([])
const scenePools = ref([])

// 修改插入场景占位符的方法
const insertScenePlaceholder = () => {
  if (promptRule.value.scene.mode === 'manual') {
    if (!promptRule.value.scene.selectedSceneIds?.length) {
      ElMessage.warning('请先选择至少一个场景')
      return
    }
    
    // 获取所选场景的名称
    const sceneNames = []
    for (const sceneId of promptRule.value.scene.selectedSceneIds) {
      let sceneName = '未知场景'
      // 遍历所有场景池寻找场景
      for (const pool of sceneData.value.pools) {
        if (pool.scenes) {
          const scene = pool.scenes.find(s => s.id === sceneId)
          if (scene) {
            sceneName = scene.title || '未命名场景'
            break
          }
        }
      }
      sceneNames.push(sceneName)
    }
    
    // 插入手动选择的场景占位符
    const placeholder = `{{场景:manual:${promptRule.value.scene.selectedSceneIds.join(',')}【${sceneNames.join('、')}】}}`
    insertTextToEditor(placeholder)
    
    // 提供反馈
    ElMessage.success(`已插入${sceneNames.length}个场景占位符`)
  } else {
    if (!promptRule.value.scene.selectedPoolId) {
      ElMessage.warning('请先选择一个场景池')
      return
    }
    
    // 获取场景池名称
    const pool = sceneData.value.pools.find(p => p.id === promptRule.value.scene.selectedPoolId)
    const poolName = pool ? pool.name || '未命名场景池' : '未知场景池'
    
    // 插入随机场景占位符
    const placeholder = `{{场景:random:${promptRule.value.scene.selectedPoolId}:${promptRule.value.scene.randomCount || 1}【随机${poolName}场景(${promptRule.value.scene.randomCount || 1}个)】}}`
    insertTextToEditor(placeholder)
    
    // 提供反馈
    ElMessage.success(`已插入"${poolName}"的随机场景占位符`)
  }
}

// 当选中的书籍改变时重新加载场景数据
watch(selectedBookId, async () => {
  await loadSceneData()
})

// 在单个实体模式的模板中添加随机按钮
const insertRandomEntityPlaceholder = () => {
  if (!promptRule.value.template.selectedTemplateId || !currentTemplateEntities.value.length) {
    ElMessage.warning('请先选择一个包含实体的模板')
    return
  }
  
  // 获取模板对象以获取名称
  const template = templateList.value.find(t => t.id === promptRule.value.template.selectedTemplateId)
  if (!template) {
    ElMessage.warning('未找到选中的模板')
    return
  }
  
  // 创建带有内嵌名称的占位符
  const placeholder = `{{实体:random:${promptRule.value.template.selectedTemplateId}:${promptRule.value.template.entityOutputFormat}:${promptRule.value.template.entityExcludedDimensions.join(',')}【随机${template.name || '未命名模板'}实体】}}`
  
  // 在编辑区域插入占位符
  insertTextToEditor(placeholder)
  
  // 提供反馈
  ElMessage.success(`已插入"${template.name}"模板的随机实体占位符`)
}

// 监听选中的书籍变化，重新加载实体数据
watch(selectedBookId, async () => {
  if (selectedBookId.value) {
    try {
      const response = await window.pywebview.api.book_controller.get_entities(selectedBookId.value)
      const result = typeof response === 'string' ? JSON.parse(response) : response
      if (result.status === 'success') {
        allEntities.value = result.data || []
      } else {
        console.error('加载实体失败:', result.message)
        ElMessage.error('加载实体失败')
      }
    } catch (error) {
      console.error('加载实体失败:', error)
      ElMessage.error('加载实体失败')
    }
  }
}, { immediate: true })

// 添加一个调试函数，可以在控制台调用来检查实体数据
const debugEntityData = () => {
  console.log("书籍ID:", selectedBookId.value);
  console.log("所有实体:", allEntities.value);
  console.log("所有模板:", templateList.value);

  // 查看是否能找到特定ID的实体
  const testId = 'f0b7cc16-9575-46d7-b5d2-2f8678f436a0'; // 替换成您的实体ID
  const entity = allEntities.value.find(e => e.id === testId);
  console.log("测试实体查找:", testId, entity);

  return {
    selectedBookId: selectedBookId.value,
    entityCount: allEntities.value.length,
    templateCount: templateList.value.length,
    testEntity: entity
  };
};

// 添加场景数据调试函数
const debugSceneData = () => {
  console.log("书籍ID:", selectedBookId.value);
  console.log("场景数据:", sceneData.value);
  console.log("场景池数量:", sceneData.value.pools?.length || 0);
  console.log("当前场景池ID:", sceneData.value.currentPoolId);

  // 统计所有场景
  let totalScenes = 0;
  sceneData.value.pools?.forEach(pool => {
    if (pool.scenes) {
      totalScenes += pool.scenes.length;
      console.log(`场景池 "${pool.name}" (${pool.id}): ${pool.scenes.length} 个场景`);
    }
  });

  return {
    selectedBookId: selectedBookId.value,
    poolCount: sceneData.value.pools?.length || 0,
    totalScenes: totalScenes,
    currentPoolId: sceneData.value.currentPoolId,
    pools: sceneData.value.pools
  };
};

// 添加占位符调试函数
const debugPlaceholder = (text) => {
  console.log("=== 占位符调试信息 ===");
  console.log("占位符文本:", text);

  // 检测占位符类型
  if (text.includes('{{实体:')) {
    console.log("类型: 实体占位符");
    const entityMatch = text.match(/{{实体:([^:【}]+):([^:【}]+)(?::([^:【}]*))?(?:【([^】]+)】)?}}/);
    if (entityMatch) {
      console.log("模式:", entityMatch[1]);
      console.log("模板ID:", entityMatch[2]);
      console.log("数量:", entityMatch[3] || "1");
      console.log("显示名称:", entityMatch[4] || "无");
    }
  } else if (text.includes('{{场景:')) {
    console.log("类型: 场景占位符");
    const sceneMatch = text.match(/{{场景:([^:【}]+):([^:【}]+)(?::([^:【}]*))?(?:【([^】]+)】)?}}/);
    if (sceneMatch) {
      console.log("模式:", sceneMatch[1]);
      console.log("ID值:", sceneMatch[2]);
      console.log("数量:", sceneMatch[3] || "1");
      console.log("显示名称:", sceneMatch[4] || "无");
    }
  } else {
    console.log("类型: 未知占位符");
  }

  console.log("=== 调试信息结束 ===");
};



// 将调试函数暴露到全局，方便在控制台调用
window.debugPVV = {
  entityData: debugEntityData,
  sceneData: debugSceneData,
  previewPrompt: previewPrompt,
  insertTextToEditor: insertTextToEditor,
  debugPlaceholder: debugPlaceholder,
  testInsert: (text) => insertTextToEditor(text)
};

// 查看已保存的提示词
const viewSavedPrompt = (prompt) => {
  // 显示提示词预览对话框
  previewPromptContent.value = prompt.previewContent || '';
  generatedPromptContent.value = prompt.content || '';
  previewDialogVisible.value = true;
}

// 从规则中移除已保存的提示词
const removeSavedPrompt = (promptId) => {
  ElMessageBox.confirm('确定要从当前规则中移除此提示词吗？', '移除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 从规则中移除提示词
    const index = promptRule.value.savedPrompts.findIndex(p => p.id === promptId);
    if (index !== -1) {
      promptRule.value.savedPrompts.splice(index, 1);
      ElMessage.success('提示词已从规则中移除');
    } else {
      ElMessage.warning('未找到指定的提示词');
    }
  }).catch(() => {
    // 用户取消操作
  });
}

// 添加导入方法：从其他规则加载提示词
const importSavedPromptsFromRule = (rule) => {
  if (!rule.savedPrompts || rule.savedPrompts.length === 0) {
    ElMessage.warning('选择的规则没有保存的提示词');
    return;
  }
  
  // 将提示词添加到当前规则
  const newPrompts = rule.savedPrompts.filter(p => !promptRule.value.savedPrompts.some(existing => existing.id === p.id));
  promptRule.value.savedPrompts.push(...newPrompts);
  
  ElMessage.success(`已从规则中导入${newPrompts.length}个提示词`);
}

// 添加导入规则对话框
const importRuleDialogVisible = ref(false)
const selectedRuleToImport = ref(null)

// 处理导入规则
const handleImportRule = async () => {
  try {
    if (!selectedRuleToImport.value) {
      ElMessage.warning('请选择要导入的规则')
      return
    }
    
    // 获取选择的规则
    const rule = promptList.value.find(p => p.id === selectedRuleToImport.value)
    
    if (!rule || !rule.rule || !rule.rule.savedPrompts) {
      ElMessage.warning('选择的规则不包含保存的提示词')
      return
    }
    
    // 导入提示词
    importSavedPromptsFromRule(rule.rule)
    
    // 关闭对话框
    importRuleDialogVisible.value = false
  } catch (error) {
    console.error('导入规则失败:', error)
    ElMessage.error('导入规则失败: ' + error.message)
  }
}

// 在组件挂载或书籍ID变化时加载提示词列表
watch(() => selectedBookId.value, (newId) => {
  if (newId) {
    loadPrompts();
  }
}, { immediate: true });

// 添加查看规则中提示词的方法
const viewPrompts = (rule) => {
  if (!rule.rule?.prompts || rule.rule.prompts.length === 0) {
    ElMessage.warning('该规则下暂无提示词');
    return;
  }
  
  // 显示规则下的提示词列表对话框
  promptsDialogVisible.value = true;
  currentRulePrompts.value = rule.rule.prompts || [];
  currentRuleName.value = rule.name;
  
  // 默认选择第一个
  selectedPromptIndex.value = currentRulePrompts.value.length > 0 ? 0 : -1;
  
  // 清空搜索
  promptSearchQuery.value = '';
}

// 添加相关变量
const promptsDialogVisible = ref(false);
const currentRulePrompts = ref([]);
const currentRuleName = ref('');

// 复制提示词内容
const copyPromptContent = (content) => {
  if (!content) {
    ElMessage.warning('没有可复制的内容');
    return;
  }
  
  window.pywebview.api.copy_to_clipboard(content)
    .then(() => {
      ElMessage.success('复制成功');
    })
    .catch(() => {
      ElMessage.error('复制失败');
    });
}

// 使用提示词
const usePrompt = (content) => {
  if (!content) return;
  
  // 设置当前编辑器内容
  generatedPromptContent.value = content;
  
  // 关闭对话框
  promptsDialogVisible.value = false;
  
  // 切换到生成器选项卡
  activeTab.value = 'generator';
  
  ElMessage.success('提示词已加载到编辑器');
}

// 优化ID生成，使用更安全的唯一ID生成方法
const generateUniqueId = () => {
  return 'prompt_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 添加当前正在编辑的提示词规则ID变量
const currentEditingPromptId = ref(null);

// 提示词列表相关变量
const promptSearchQuery = ref('');
const selectedPromptIndex = ref(0);

// 获取选中的提示词
const selectedPrompt = computed(() => {
  if (filteredPromptsList.value.length === 0) return null;
  if (selectedPromptIndex.value < 0 || selectedPromptIndex.value >= filteredPromptsList.value.length) {
    return filteredPromptsList.value[0];
  }
  return filteredPromptsList.value[selectedPromptIndex.value];
});

// 筛选后的提示词列表
const filteredPromptsList = computed(() => {
  if (!currentRulePrompts.value) return [];
  
  if (!promptSearchQuery.value) return currentRulePrompts.value;
  
  const query = promptSearchQuery.value.toLowerCase();
  return currentRulePrompts.value.filter(prompt => {
    return (
      (prompt.name && prompt.name.toLowerCase().includes(query)) ||
      (prompt.content && prompt.content.toLowerCase().includes(query))
    );
  });
});

// 选择提示词
const selectPrompt = (index) => {
  selectedPromptIndex.value = index;
};

// 获取提示词预览
const getPromptPreview = (content) => {
  if (!content) return '无内容';
  
  // 处理内容，去除多余空白
  const trimmed = content.replace(/\s+/g, ' ').trim();
  return trimmed.length > 60 ? trimmed.substring(0, 60) + '...' : trimmed;
};

// 编辑器配置
const editorConfig = ref({
  // 编辑器的默认配置
  fontSize: 14,
  theme: 'default',
  tabSize: 2,
  lineNumbers: true,
  // 可以根据需要添加其他编辑器配置
});



// 获取标签类型
const getTagType = (type) => {
  const typeMap = {
    '生成规则': 'success',
    '模板规则': 'primary',
    '场景规则': 'warning',
    '自定义': 'info'
  };
  return typeMap[type] || 'info';
};

// 表格行的类名
const tableRowClassName = ({ row, rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};


const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// 确保这些变量存在，如果在人工编辑中被删除了，需要重新添加
const currentPage = ref(1)
const pageSize = ref(10)

// 重置生成器规则的函数
const resetGeneratorRule = () => {
  // 重置规则配置
  promptRule.value = {
    template: {
      enabled: false,
      mode: 'single',
      selectedTemplateId: null,
      selectedDimensions: [],
      excludedDimensions: [],
      entityExcludedDimensions: [],
      entityOutputFormat: 'text',
      allEntitiesOutputFormat: 'text'
    },
    scene: {
      enabled: false,
      mode: 'manual',
      selectedSceneIds: [],
      randomCount: 1,
      selectedPoolId: null,
      placeholderText: '{{场景描述}}'
    },
    prompts: []
  };

  // 重置编辑器内容
  generatedPromptContent.value = '';
  generatedPromptName.value = '';
  generatedPromptDescription.value = '';
  
  // 重置当前编辑ID
  currentEditingPromptId.value = null;
}

// 新建规则并跳转到生成器
const createNewRule = () => {
  // 重置生成器规则
  resetGeneratorRule();
  
  // 切换到生成器选项卡
  activeTab.value = 'generator';
  
  // 滚动到生成器区域
  nextTick(() => {
    const generatorEl = document.querySelector('.prompt-generator');
    if (generatorEl) {
      generatorEl.scrollIntoView({ behavior: 'smooth' });
    }
  });
  
  // 显示提示消息
  ElMessage.success('已切换到生成器模式，请开始编辑新规则');
}

// 处理预览中的重新生成
const handleRegeneratePreview = async () => {
  try {
    // 重新调用预览生成函数
    await previewPrompt();
    ElMessage.success('已重新生成预览内容');
  } catch (error) {
    console.error('重新生成预览失败：', error);
    ElMessage.error('重新生成预览失败: ' + error.message);
  }
}

// 修改单个实体模式下的排除维度部分
const allTemplateDimensionsForEntity = computed(() => {
  if (!promptRule.value.template.selectedTemplateId) return [];
  
  // 获取当前模板下所有实体
  const templateEntities = allEntities.value.filter(entity => 
    entity.template_id === promptRule.value.template.selectedTemplateId
  );
  
  // 收集所有实体中出现的维度名称
  const dimensionNames = new Set();
  templateEntities.forEach(entity => {
    if (entity.dimensions) {
      Object.keys(entity.dimensions).forEach(dimName => {
        dimensionNames.add(dimName);
      });
    }
  });
  
  // 转换为所需的格式
  return Array.from(dimensionNames).map(name => ({ name }));
});

</script>

<style lang="scss" scoped>
.ai-prompt-manager {
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 100%;
  position: relative;
  background: var(--el-bg-color-page);

  &.glass-bg {
    background: rgba(var(--el-bg-color-rgb), 0.85);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 28px;
  border-radius: 16px;
  margin-bottom: 24px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  box-shadow:
    0 4px 20px rgba(var(--el-text-color-primary-rgb), 0.08),
    0 1px 3px rgba(var(--el-text-color-primary-rgb), 0.05),
    inset 0 1px 0 rgba(var(--el-bg-color-rgb), 0.9);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.02) 0%,
      rgba(var(--el-color-primary-light-3-rgb), 0.02) 100%);
    border-radius: 16px;
    pointer-events: none;
  }

  .left-section {
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    z-index: 1;

    .page-title {
      margin: 0;
      font-size: 32px;
      font-weight: 700;
      background: linear-gradient(135deg,
        var(--el-color-primary) 0%,
        var(--el-color-primary-light-3) 50%,
        var(--el-color-primary-light-5) 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      white-space: nowrap;
      letter-spacing: -0.5px;
    }

    .book-selector {
      width: 220px;

      :deep(.el-input__wrapper) {
        background: var(--el-bg-color-overlay);
        border: 1px solid var(--el-border-color-light);
        border-radius: 12px;
        box-shadow:
          0 2px 8px rgba(var(--el-text-color-primary-rgb), 0.06),
          inset 0 1px 0 rgba(var(--el-bg-color-rgb), 0.8);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          border-color: var(--el-color-primary-light-5);
          box-shadow:
            0 4px 12px rgba(var(--el-text-color-primary-rgb), 0.08),
            0 0 0 3px rgba(var(--el-color-primary-rgb), 0.1);
        }

        &.is-focus {
          border-color: var(--el-color-primary);
          box-shadow:
            0 4px 12px rgba(var(--el-text-color-primary-rgb), 0.08),
            0 0 0 3px rgba(var(--el-color-primary-rgb), 0.15);
        }
      }
    }
  }
  
  .tab-buttons {
    display: flex;
    gap: 8px;
    margin: 0 auto;
    background: var(--el-fill-color-lighter);
    padding: 6px;
    border-radius: 12px;
    border: 1px solid var(--el-border-color-light);
    position: relative;
    z-index: 1;

    .tab-button {
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 15px;
      font-weight: 600;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      background: transparent;
      border: none;
      color: var(--el-text-color-secondary);
      position: relative;

      &:hover {
        background: var(--el-bg-color);
        color: var(--el-text-color-primary);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(var(--el-text-color-primary-rgb), 0.1);
      }

      &.active {
        background: var(--el-color-primary);
        color: var(--el-color-white);
        box-shadow:
          0 4px 16px rgba(var(--el-color-primary-rgb), 0.3),
          0 2px 4px rgba(var(--el-text-color-primary-rgb), 0.1);
        transform: translateY(-1px);
      }
    }
  }

  .back-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(var(--el-color-primary-rgb), 0.2);
    background: rgba(var(--el-color-primary-rgb), 0.1);
    color: var(--el-color-primary);
    position: relative;
    z-index: 1;

    &:hover {
      background: var(--el-color-primary);
      color: var(--el-color-white);
      transform: translateY(-2px);
      box-shadow:
        0 8px 24px rgba(var(--el-color-primary-rgb), 0.25),
        0 4px 8px rgba(var(--el-text-color-primary-rgb), 0.1);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.main-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow:
    0 8px 32px rgba(var(--el-text-color-primary-rgb), 0.08),
    0 2px 8px rgba(var(--el-text-color-primary-rgb), 0.04),
    inset 0 1px 0 rgba(var(--el-bg-color-rgb), 0.9);
  max-width: 100%;
  overflow: hidden;
  margin-bottom: 24px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.02) 0%,
      rgba(var(--el-color-primary-light-3-rgb), 0.02) 100%);
    border-radius: 20px;
    pointer-events: none;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28px;
  position: relative;
  z-index: 2;

  .header-content {
    user-select: none;
    display: flex;
    align-items: center;
    gap: 20px;

    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 700;
      color: var(--el-text-color-primary);
      background: linear-gradient(135deg,
        var(--el-color-primary) 0%,
        var(--el-color-primary-light-3) 50%,
        var(--el-color-primary-light-5) 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      letter-spacing: -0.5px;
    }

    .search-input {
      width: 280px;

      :deep(.el-input__wrapper) {
        background: var(--el-bg-color-overlay);
        border: 1px solid var(--el-border-color-light);
        border-radius: 12px;
        box-shadow:
          0 2px 8px rgba(var(--el-text-color-primary-rgb), 0.06),
          inset 0 1px 0 rgba(var(--el-bg-color-rgb), 0.8);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        padding: 4px 16px;

        &:hover {
          border-color: var(--el-color-primary-light-5);
          background: var(--el-bg-color);
          box-shadow:
            0 4px 12px rgba(var(--el-text-color-primary-rgb), 0.08),
            0 0 0 3px rgba(var(--el-color-primary-rgb), 0.1);
        }

        &.is-focus {
          background: var(--el-bg-color);
          border-color: var(--el-color-primary);
          box-shadow:
            0 4px 12px rgba(var(--el-text-color-primary-rgb), 0.08),
            0 0 0 3px rgba(var(--el-color-primary-rgb), 0.15);
        }
      }

      :deep(.el-input__inner) {
        font-size: 15px;
        color: var(--el-text-color-primary);

        &::placeholder {
          color: var(--el-text-color-placeholder);
        }
      }
    }
  }

  .button-group {
    display: flex;
    gap: 12px;
    align-items: center;

    .el-button {
      padding: 12px 24px;
      height: 44px;
      font-size: 15px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;
      border-radius: 12px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      .el-icon {
        font-size: 18px;
      }

      &.el-button--primary {
        background: var(--el-color-primary);
        border: none;
        color: var(--el-color-white);
        box-shadow:
          0 4px 16px rgba(var(--el-color-primary-rgb), 0.3),
          0 2px 4px rgba(var(--el-text-color-primary-rgb), 0.1);

        &:hover {
          transform: translateY(-2px);
          box-shadow:
            0 8px 24px rgba(var(--el-color-primary-rgb), 0.4),
            0 4px 8px rgba(var(--el-text-color-primary-rgb), 0.15);
        }

        &:active {
          transform: translateY(0);
        }
      }

      &.el-button--success {
        background: var(--el-color-success);
        border: none;
        color: var(--el-color-white);
        box-shadow:
          0 4px 16px rgba(var(--el-color-success-rgb), 0.3),
          0 2px 4px rgba(var(--el-text-color-primary-rgb), 0.1);

        &:hover {
          transform: translateY(-2px);
          box-shadow:
            0 8px 24px rgba(var(--el-color-success-rgb), 0.4),
            0 4px 8px rgba(var(--el-text-color-primary-rgb), 0.15);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

.prompts-container,
.generator-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 16px;
  padding: 28px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;

  .section-header {
    flex-shrink: 0;
    margin-bottom: 24px;
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0;
    border-radius: 8px;
    background: rgba(var(--el-bg-color-rgb), 0.6);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
    margin-bottom: 16px;

    .el-table {
      flex: 1;
      overflow: hidden;

      :deep(.el-table__header) {
        th {
          font-size: 16px;
          font-weight: 600;
          padding: 16px 0;
          background-color: var(--el-bg-color-overlay);
        }
      }

      :deep(.el-table__body) {
        td {
          font-size: 15px;
          padding: 16px 0;
          line-height: 1.6;
        }
      }

      .el-table__inner-wrapper {
        height: 100%;
      }

      .el-table__header-wrapper {
        flex-shrink: 0;
      }

      .el-table__body-wrapper {
        overflow-y: auto;
        height: calc(100% - 48px) !important;
      }
    }
  }
}

.placeholder-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 4px 0;

  .placeholder-tag {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 15px;
    border: 1px solid var(--el-color-primary-light-5);
    background: linear-gradient(145deg, var(--el-color-primary-light-9), var(--el-color-primary-light-8));
    transition: all 0.3s ease;
    cursor: default;
    user-select: none;
  }
}

.operation-buttons {
  .el-button {
    font-size: 15px;
    
    .el-icon {
      font-size: 16px;
    }
  }
}

.pagination-container {
  padding: 12px 16px;
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-light);
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  justify-content: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  margin-top: auto;
  
  :deep(.el-pagination) {
    justify-content: center;
    
    .el-pagination__jump {
      margin-left: 16px;
    }
  }
}

.generator-content {
  display: flex;
  gap: 20px;
  height: calc(100vh - 200px);
  max-height: 700px;
  overflow: hidden;
  
  // 左侧面板
  .generator-left-panel {
    width: 40%;
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 0;
    max-height: 100%;
    
    .panel-item {
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 16px;
      
      &.info-panel {
        flex: 0 0 auto; // 不伸缩，保持自身大小
      }
      
      &.result-panel {
        flex: 1; // 占用剩余空间
        display: flex;
        flex-direction: column;
        overflow: hidden;
        
        .result-actions {
          margin-bottom: 12px;
          flex: 0 0 auto; // 不伸缩
          display: flex;
          justify-content: space-between;
          
          .el-button-group {
            .el-button {
              padding: 8px 12px;
              
              .el-icon {
                margin-right: 4px;
              }
              
              &.is-disabled {
                opacity: 0.6;
              }
            }
          }
        }
        
        .result-content-wrapper {
          flex: 1; // 占用剩余空间
          overflow: hidden;
          display: flex;
          flex-direction: column;
          
          .el-input {
            height: 100%;
            display: flex;
            flex-direction: column;
            
            :deep(.el-textarea__inner) {
              height: 100% !important;
              max-height: 100%;
              resize: none;
            }
          }
        }
      }
    }
  }
  
  // 右侧面板
  .generator-right-panel {
    width: 60%;
    min-width: 0;
    max-height: 100%;
    overflow: hidden;
    
    .rules-container {
      display: flex;
      flex-direction: row; // 改为水平布局
      gap: 20px;
      height: 100%;
      overflow: hidden;
      
      .rule-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: var(--el-bg-color-page);
        border: 1px solid var(--el-border-color-light);
        border-radius: 8px;
        overflow: hidden;
        min-width: 0;
        
        .rule-header {
          padding: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid var(--el-border-color-light);
          
          h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
          }
        }
        
        .rule-content {
          flex: 1;
          padding: 16px;
          overflow: hidden;
          
          .el-scrollbar {
            height: 100%;
          }
          
          .rule-form {
            padding-right: 16px;
          }
        }
      }
    }
  }
}

.panel-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
  background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.prompt-edit-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    
    .el-dialog__header {
      margin: 0;
      padding: 20px 24px;
      border-bottom: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .el-dialog__body {
      padding: 24px;
      background: var(--el-bg-color-page);
    }
    
    .el-dialog__footer {
      margin: 0;
      padding: 16px 24px;
      border-top: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
    }
  }
  
  .placeholder-helper {
    margin-top: 8px;
    font-size: 14px;
    color: var(--el-text-color-secondary);
    display: flex;
    align-items: center;
    gap: 8px;
    

  }
}

.prompt-detail-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    
    .el-dialog__header {
      margin: 0;
      padding: 20px 24px;
      border-bottom: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .el-dialog__body {
      padding: 24px;
      background: var(--el-bg-color-page);
    }
    
    .el-dialog__footer {
      margin: 0;
      padding: 16px 24px;
      border-top: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
    }
  }
  
  .prompt-detail {
    .prompt-detail-item {
      margin-bottom: 24px;
      
      h3 {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 12px 0;
        color: var(--el-text-color-primary);
      }
      
      p {
        margin: 0;
        font-size: 15px;
        line-height: 1.6;
        color: var(--el-text-color-regular);
      }
      
      .prompt-content-box {
        background: var(--el-bg-color);
        border: 1px solid var(--el-border-color-light);
        border-radius: 8px;
        padding: 16px;
        
        pre {
          margin: 0;

          font-size: 15px;
          line-height: 1.6;
          white-space: pre-wrap;
          color: var(--el-text-color-primary);
        }
      }
    }
  }
}

.empty-state {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px;
  border-radius: 12px;
  background: rgba(var(--el-bg-color-overlay-rgb), 0.7);
  border: 1px solid rgba(var(--el-border-color-rgb), 0.1);
  
  .empty-icon {
    color: var(--el-text-color-secondary);
  }
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  background: linear-gradient(120deg, var(--el-color-primary), var(--el-color-primary-light-3));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.result-container {
  .history-list {
    .history-item {
      margin-bottom: 16px;
      padding: 16px;
      border-radius: 8px;
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);

      .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .history-time {
          font-size: 14px;
          color: var(--el-text-color-secondary);
        }
      }

      .history-content {

        white-space: pre-wrap;
        font-size: 14px;
        line-height: 1.6;
        color: var(--el-text-color-regular);
      }
    }
  }
}

.batch-generate-dialog {
  .batch-results {
    .batch-result-item {
      margin-bottom: 16px;
      padding: 16px;
      border-radius: 8px;
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-light);

      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .result-index {
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
      }

      .result-content {

        white-space: pre-wrap;
        font-size: 14px;
        line-height: 1.6;
        color: var(--el-text-color-regular);
      }
    }
  }
}

.scene-selection {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  background: var(--el-bg-color);
  
  .scene-filter {
    padding: 16px 16px 8px;
  }
  
  .scene-count {
    padding: 0 16px 8px;
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
  
  .scene-list {
    padding: 0 16px 16px;
    
    .scene-item {
      margin-bottom: 8px;
      padding: 8px;
      border-radius: 6px;
      transition: background-color 0.2s;
      
      &:hover {
        background: var(--el-bg-color-page);
      }
      
      :deep(.el-checkbox) {
        width: 100%;
        margin-right: 0;
        
        .el-checkbox__label {
          padding-left: 8px;
          width: calc(100% - 24px);
        }
      }
      
      .scene-info {
        .scene-title {
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
        }
        
        .scene-description {
          font-size: 13px;
          color: var(--el-text-color-secondary);
          line-height: 1.4;
        }
      }
    }
  }
}

.scene-selector {
  width: 100%;
}

:deep(.scene-selector-dropdown) {
  .scene-option {
    padding: 4px 0;
    
    .scene-title {
      font-weight: 500;
      color: var(--el-text-color-primary);
      margin-bottom: 2px;
    }
    
    .scene-description {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      line-height: 1.4;
    }
  }
}

.entity-type {
  margin-left: 4px;
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

// 在 generatedPromptContent 下方添加
.rule-saved-prompts {
  margin-top: 20px;
  border-top: 1px solid var(--el-border-color-light);
  padding-top: 16px;
  
  .panel-title {
    margin-top: 0;
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .prompt-preview-text {
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    font-size: 13px;
    color: var(--el-text-color-secondary);
  }
}

// 添加预览对话框组件
.prompt-preview-text {
  white-space: pre-wrap;
  font-size: 14px;
  line-height: 1.6;
  color: var(--el-text-color-regular);
}

// 在操作区域添加导入按钮
.generator-actions {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

// 添加导入规则对话框
.import-rule-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    
    .el-dialog__header {
      margin: 0;
      padding: 20px 24px;
      border-bottom: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .el-dialog__body {
      padding: 24px;
      background: var(--el-bg-color-page);
    }
    
    .el-dialog__footer {
      margin: 0;
      padding: 16px 24px;
      border-top: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
    }
  }
  
  .import-rule-form {
    margin-top: 20px;
    
    .el-form-item {
      margin-bottom: 20px;
      
      .el-select {
        width: 100%;
      }
    }
  }
}

// 规则下提示词列表对话框
.prompts-dialog {
  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    max-height: 90vh;
    
    .el-dialog__header {
      margin: 0;
      padding: 20px;
      border-bottom: 1px solid var(--el-border-color-light);
      background: linear-gradient(135deg, var(--el-color-primary-light-9), var(--el-color-primary-light-8));
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-color-primary-dark-2);
      }
    }
    
    .el-dialog__body {
      flex: 1;
      overflow: hidden;
      padding: 0;
    }
  }
  
  .prompt-list-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .prompt-list-header {
      padding: 16px;
      border-bottom: 1px solid var(--el-border-color-light);
      background-color: var(--el-bg-color);
      
      .prompt-search {
        max-width: 300px;
      }
    }
    
    .prompt-list-content {
      display: flex;
      flex: 1;
      overflow: hidden;
      
      .prompt-sidebar {
        width: 30%;
        min-width: 280px;
        border-right: 1px solid var(--el-border-color-light);
        background-color: var(--el-bg-color-page);
        
        .prompt-list {
          .prompt-list-item {
            padding: 12px 16px;
            border-bottom: 1px solid var(--el-border-color-light);
            cursor: pointer;
            transition: all 0.2s ease;
            
            &:hover {
              background-color: var(--el-fill-color-light);
            }
            
            &.active {
              background-color: var(--el-color-primary-light-9);
              border-left: 3px solid var(--el-color-primary);
            }
            
            .prompt-list-item-content {
              .prompt-list-item-title {
                font-weight: 600;
                margin-bottom: 6px;
                font-size: 15px;
                color: var(--el-text-color-primary);
              }
              
              .prompt-list-item-preview {
                font-size: 13px;
                color: var(--el-text-color-regular);
                margin-bottom: 8px;
                line-height: 1.5;
              }
              
              .prompt-list-item-time {
                font-size: 12px;
                color: var(--el-text-color-secondary);
              }
            }
          }
        }
      }
      
      .prompt-content-view {
        flex: 1;
        padding: 20px;
        display: flex;
        flex-direction: column;
        
        .prompt-view-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          
          .prompt-view-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }
          
          .prompt-view-actions {
            display: flex;
            gap: 8px;
          }
        }
        
        .prompt-view-content {
          flex: 1;
          
          .prompt-content-text {
            white-space: pre-wrap;

            line-height: 1.6;
            font-size: 14px;
            background-color: var(--el-fill-color-light);
            padding: 16px;
            border-radius: 8px;
            color: var(--el-text-color-primary);
          }
        }
        
        .prompt-view-empty {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}

/* 美化提示词卡片样式 */
.prompt-card {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  }
  
  .prompt-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .prompt-name {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-color-primary);
    }
  }
  
  .prompt-card-content {
    .prompt-description {
      color: var(--el-text-color-secondary);
      font-size: 14px;
      margin-bottom: 12px;
    }
    
    .prompt-preview-text {
      background: var(--el-fill-color-light);
      border-radius: 4px;
      padding: 12px;

      font-size: 13px;
      color: var(--el-text-color-regular);
      margin-bottom: 16px;
      max-height: 100px;
      overflow-y: auto;
      white-space: pre-wrap;
    }
    
    .prompt-card-actions {
      display: flex;
      justify-content: flex-end;
      
      .el-button {
        margin-left: 8px;
      }
    }
  }
}

/* 美化删除确认对话框 */
:deep(.el-message-box.delete-confirm) {
  border-radius: 8px;
  overflow: hidden;
  
  .el-message-box__header {
    background: var(--el-color-danger-light-9);
    padding: 20px;
    
    .el-message-box__title {
      color: var(--el-color-danger);
    }
  }
  
  .el-message-box__content {
    padding: 20px;
  }
  
  .el-message-box__btns {
    padding: 10px 20px 20px;
  }
}

/* 美化编辑对话框 */
.prompt-edit-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
    
    .el-dialog__header {
      background: linear-gradient(135deg, var(--el-color-primary-light-8), var(--el-color-primary-light-9));
      padding: 20px;
      
      .el-dialog__title {
        font-weight: 600;
        color: var(--el-color-primary-dark-1);
      }
    }
    
    .el-dialog__body {
      padding: 24px;
    }
    
    .el-form-item {
      margin-bottom: 20px;
      
      &.is-required .el-form-item__label::before {
        color: var(--el-color-danger);
      }
    }
    

  }
}

.prompt-count-cell {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
  
  .count-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--el-color-primary-light-5), var(--el-color-primary));
    margin-right: 8px;
    
    .count-number {
      color: white;
      font-weight: 600;
      font-size: 14px;
    }
  }
  
  .view-button {
    margin-left: 4px;
    &:hover {
      text-decoration: underline;
    }
  }
}

.prompts-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(var(--el-text-color-primary-rgb), 0.08),
    0 2px 8px rgba(var(--el-text-color-primary-rgb), 0.04);
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(var(--el-color-primary-rgb), 0.01) 0%,
      rgba(var(--el-color-primary-light-3-rgb), 0.01) 100%);
    border-radius: 16px;
    pointer-events: none;
  }

  .section-header {
    padding: 12px 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--el-border-color-light);
    background: var(--el-bg-color-overlay);
    position: relative;
    z-index: 1;

    .header-content {
      display: flex;
      align-items: center;
      gap: 24px;

      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 700;
        color: var(--el-text-color-primary);
        background: linear-gradient(135deg,
          var(--el-color-primary) 0%,
          var(--el-color-primary-light-3) 50%,
          var(--el-color-primary-light-5) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        letter-spacing: -0.5px;
      }

      .search-input {
        width: 300px;

        :deep(.el-input__wrapper) {
          background: var(--el-bg-color);
          border: 1px solid var(--el-border-color-light);
          border-radius: 12px;
          padding: 6px 16px;
          box-shadow:
            0 2px 8px rgba(var(--el-text-color-primary-rgb), 0.06),
            inset 0 1px 0 rgba(var(--el-bg-color-rgb), 0.8);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &:hover {
            border-color: var(--el-color-primary-light-5);
            box-shadow:
              0 4px 12px rgba(var(--el-text-color-primary-rgb), 0.08),
              0 0 0 3px rgba(var(--el-color-primary-rgb), 0.1);
          }

          &.is-focus {
            border-color: var(--el-color-primary);
            box-shadow:
              0 4px 12px rgba(var(--el-text-color-primary-rgb), 0.08),
              0 0 0 3px rgba(var(--el-color-primary-rgb), 0.15);
          }
        }

        :deep(.el-input__inner) {
          font-size: 15px;
          color: var(--el-text-color-primary);

          &::placeholder {
            color: var(--el-text-color-placeholder);
          }
        }
      }
    }

    .header-actions {
      .el-button {
        font-size: 15px;
        font-weight: 600;
        padding: 12px 24px;
        border-radius: 12px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        .el-icon {
          font-size: 18px;
          margin-right: 8px;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow:
            0 8px 24px rgba(var(--el-color-primary-rgb), 0.25),
            0 4px 8px rgba(var(--el-text-color-primary-rgb), 0.1);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
  
  .prompt-list-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--el-bg-color-page);
    position: relative;
    z-index: 1;

    .el-table {
      flex: 1;
      overflow: auto;
      border-radius: 12px;
      margin-bottom: 20px;
      box-shadow:
        0 4px 20px rgba(var(--el-text-color-primary-rgb), 0.08),
        0 1px 3px rgba(var(--el-text-color-primary-rgb), 0.05);
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color-lighter);

      :deep(.el-table__header) {
        th {
          font-size: 16px;
          font-weight: 700;
          padding: 20px 16px;
          background: var(--el-bg-color-overlay);
          color: var(--el-text-color-primary);
          border-bottom: 2px solid var(--el-border-color-light);
          position: relative;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg,
              transparent 0%,
              var(--el-color-primary-light-7) 50%,
              transparent 100%);
          }
        }
      }

      :deep(.el-table__body) {
        td {
          font-size: 15px;
          padding: 18px 16px;
          line-height: 1.6;
          border-bottom: 1px solid var(--el-border-color-lighter);
        }
      }

      // 表格hover效果
      :deep(.el-table__row) {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          background: linear-gradient(135deg,
            rgba(var(--el-color-primary-rgb), 0.05) 0%,
            rgba(var(--el-color-primary-light-3-rgb), 0.05) 100%) !important;
          transform: translateY(-1px);
          box-shadow:
            0 8px 24px rgba(var(--el-text-color-primary-rgb), 0.12),
            0 2px 8px rgba(var(--el-color-primary-rgb), 0.1);
        }
      }

      :deep(.el-table__header-wrapper) {
        border-bottom: none;
      }
    }
    
    .prompt-name-cell {
      display: flex;
      align-items: center;
      gap: 12px;

      .prompt-name {
        font-weight: 700;
        font-size: 16px;
        color: var(--el-text-color-primary);
        background: linear-gradient(135deg,
          var(--el-text-color-primary) 0%,
          var(--el-color-primary) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .prompt-type-tag {
        font-size: 12px;
        font-weight: 600;
        padding: 4px 10px;
        border-radius: 8px;
        background: linear-gradient(135deg,
          rgba(var(--el-color-primary-rgb), 0.1) 0%,
          rgba(var(--el-color-primary-light-3-rgb), 0.1) 100%);
        border: 1px solid rgba(var(--el-color-primary-rgb), 0.2);
        color: var(--el-color-primary);
      }
    }

    .prompt-description {
      color: var(--el-text-color-secondary);
      font-size: 14px;
      line-height: 1.6;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .prompt-count {
      .prompt-badge {
        :deep(.el-badge__content) {
          background: var(--el-color-primary);
          border: none;
          font-size: 13px;
          font-weight: 600;
          height: 22px;
          min-width: 22px;
          line-height: 22px;
          border-radius: 11px;
          box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.3);
        }
      }
    }

    .update-time {
      color: var(--el-text-color-secondary);
      font-size: 14px;
      font-weight: 500;
    }

    .operation-buttons {
      display: flex;
      justify-content: center;
      gap: 6px;

      .el-button-group {
        .el-button {
          padding: 10px 14px;
          border-radius: 8px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          .el-icon {
            font-size: 16px;
          }

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
          }

          &.el-button--danger:hover {
            box-shadow: 0 4px 12px rgba(var(--el-color-danger-rgb), 0.2);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }
  
  .pagination-container {
    padding: 20px 32px;
    background: var(--el-bg-color-overlay);
    border-top: 1px solid var(--el-border-color-light);
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    display: flex;
    justify-content: center;
    box-shadow:
      0 -4px 16px rgba(var(--el-text-color-primary-rgb), 0.08),
      0 -1px 3px rgba(var(--el-text-color-primary-rgb), 0.05);
    margin-top: auto;

    :deep(.el-pagination) {
      justify-content: center;
      font-size: 15px;

      .el-pagination__jump {
        margin-left: 20px;

        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid var(--el-border-color-light);

          &:hover, &.is-focus {
            border-color: var(--el-color-primary);
            box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.1);
          }
        }
      }

      .el-pagination__sizes {
        margin-right: 20px;

        .el-select .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid var(--el-border-color-light);

          &:hover, &.is-focus {
            border-color: var(--el-color-primary);
            box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.1);
          }
        }
      }

      .el-pagination button {
        min-width: 40px;
        height: 40px;
        border-radius: 8px;
        border: 1px solid var(--el-border-color-light);
        background: var(--el-bg-color);
        color: var(--el-text-color-primary);
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          border-color: var(--el-color-primary);
          background: var(--el-color-primary);
          color: var(--el-color-white);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
        }

        &:disabled {
          opacity: 0.5;
          transform: none;
          box-shadow: none;
        }
      }

      .el-pager li {
        min-width: 40px;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 8px;
        border: 1px solid var(--el-border-color-light);
        background: var(--el-bg-color);
        color: var(--el-text-color-primary);
        margin: 0 2px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          border-color: var(--el-color-primary);
          background: var(--el-color-primary);
          color: var(--el-color-white);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
        }

        &.is-active {
          background: var(--el-color-primary);
          border-color: var(--el-color-primary);
          color: var(--el-color-white);
          box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
        }
      }
    }
  }
}

/* 添加头部样式 */
.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(145deg, var(--el-bg-color), var(--el-bg-color-page));
  border-radius: 12px;
  box-shadow: 
    0 2px 12px rgba(0, 0, 0, 0.05),
    0 1px 4px rgba(0, 0, 0, 0.03);

  h2 {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }

  .el-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .el-icon {
      font-size: 16px;
    }
  }
}

/* 暗色模式适配 */
html.dark {
  .prompt-header {
    background: linear-gradient(145deg, #1a1a1a, #242424);
    box-shadow: 
      0 2px 12px rgba(0, 0, 0, 0.2),
      0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

/* 更新 section-header 样式 */
.section-header {
  padding: 20px 28px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(var(--el-border-color-light-rgb), 0.5);
  background: linear-gradient(to bottom, 
    var(--el-bg-color) 0%,
    var(--el-bg-color-overlay) 100%);
  
  .header-content {
    display: flex;
    align-items: center;
    gap: 20px;
    
    h2 {
      margin: 0;
      font-size: 22px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      letter-spacing: -0.5px;
    }
    
    .search-input {
      width: 260px;
      
      :deep(.el-input__wrapper) {
        border-radius: 8px;
        padding: 4px 12px;
      }
      
      :deep(.el-input__inner) {
        font-size: 15px;
      }
    }
  }
  
  .header-actions {
    .el-button {
      font-size: 15px;
      padding: 10px 20px;
      border-radius: 8px;
      transition: all 0.3s ease;
      
      .el-icon {
        font-size: 16px;
        margin-right: 6px;
      }
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
  }
}

/* 暗色模式适配 */
html.dark {
  .ai-prompt-manager {
    background: var(--el-bg-color-page);

    &.glass-bg {
      background: rgba(var(--el-bg-color-rgb), 0.85);
    }
  }

  .header-section {
    background: var(--el-bg-color);
    border-color: var(--el-border-color);
    box-shadow:
      0 4px 20px rgba(var(--el-text-color-primary-rgb), 0.15),
      0 1px 3px rgba(var(--el-text-color-primary-rgb), 0.1),
      inset 0 1px 0 rgba(var(--el-border-color-rgb), 0.3);

    &::before {
      background: linear-gradient(135deg,
        rgba(var(--el-color-primary-rgb), 0.08) 0%,
        rgba(var(--el-color-primary-light-3-rgb), 0.08) 100%);
    }

    .tab-buttons {
      background: var(--el-fill-color-darker);
      border-color: var(--el-border-color);

      .tab-button {
        &:hover {
          background: var(--el-bg-color-overlay);
          box-shadow: 0 2px 8px rgba(var(--el-text-color-primary-rgb), 0.2);
        }

        &.active {
          box-shadow:
            0 4px 16px rgba(var(--el-color-primary-rgb), 0.4),
            0 2px 4px rgba(var(--el-text-color-primary-rgb), 0.2);
        }
      }
    }
  }

  .main-content {
    background: var(--el-bg-color);
    border-color: var(--el-border-color);
    box-shadow:
      0 8px 32px rgba(var(--el-text-color-primary-rgb), 0.15),
      0 2px 8px rgba(var(--el-text-color-primary-rgb), 0.08),
      inset 0 1px 0 rgba(var(--el-border-color-rgb), 0.3);

    &::before {
      background: linear-gradient(135deg,
        rgba(var(--el-color-primary-rgb), 0.08) 0%,
        rgba(var(--el-color-primary-light-3-rgb), 0.08) 100%);
    }
  }

  .prompts-container {
    background: var(--el-bg-color);
    border-color: var(--el-border-color);
    box-shadow:
      0 8px 32px rgba(var(--el-text-color-primary-rgb), 0.15),
      0 2px 8px rgba(var(--el-text-color-primary-rgb), 0.08);

    &::before {
      background: linear-gradient(135deg,
        rgba(var(--el-color-primary-rgb), 0.05) 0%,
        rgba(var(--el-color-primary-light-3-rgb), 0.05) 100%);
    }

    .section-header {
      background: var(--el-bg-color-overlay);
      border-bottom-color: var(--el-border-color);
    }

    .prompt-list-wrapper {
      background: var(--el-bg-color-page);

      .el-table {
        background: var(--el-bg-color);
        border-color: var(--el-border-color);

        :deep(.el-table__header) th {
          background: var(--el-bg-color-overlay);
          border-bottom-color: var(--el-border-color);
        }

        :deep(.el-table__body) td {
          border-bottom-color: var(--el-border-color-lighter);
        }

        :deep(.el-table__row) {
          &:hover {
            background: linear-gradient(135deg,
              rgba(var(--el-color-primary-rgb), 0.15) 0%,
              rgba(var(--el-color-primary-light-3-rgb), 0.15) 100%) !important;
            box-shadow:
              0 8px 24px rgba(var(--el-text-color-primary-rgb), 0.2),
              0 2px 8px rgba(var(--el-color-primary-rgb), 0.3);
          }
        }
      }
    }

    .pagination-container {
      background: var(--el-bg-color-overlay);
      border-top-color: var(--el-border-color);
      box-shadow:
        0 -4px 16px rgba(var(--el-text-color-primary-rgb), 0.15),
        0 -1px 3px rgba(var(--el-text-color-primary-rgb), 0.08);
    }
  }

  .button-group {
    .el-button {
      &:hover {
        box-shadow:
          0 8px 24px rgba(var(--el-color-primary-rgb), 0.5),
          0 4px 8px rgba(var(--el-text-color-primary-rgb), 0.2);
      }

      &.el-button--success:hover {
        box-shadow:
          0 8px 24px rgba(var(--el-color-success-rgb), 0.5),
          0 4px 8px rgba(var(--el-text-color-primary-rgb), 0.2);
      }
    }
  }
}
</style>

