<template>
  <div class="custom-pool-container">
    <!-- 页面头部 -->
    <div class="app-header">
      <h2>人物关系图谱</h2>
      <div class="header-actions">
        <div class="book-selector-wrapper">
          <span class="selector-label">当前书籍:</span>
          <el-select
            v-model="selectedBookId"
            placeholder="请选择书籍"
            size="default"
            @change="handleBookChange"
            clearable
            class="book-selector"
            style="min-width: 200px;"
          >
            <el-option
              v-for="book in bookStore.bookList"
              :key="book.id"
              :label="book.title"
              :value="book.id"
            >
              <div class="book-option">
                <span class="book-title">{{ book.title }}</span>
                <span class="book-info" v-if="book.author">{{ book.author }}</span>
              </div>
            </el-option>
          </el-select>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="app-content" v-loading="loading">
      <div class="relation-content-container">
        <!-- 左侧面板：角色管理 -->
        <div class="left-panel">
          <!-- 角色管理头部 -->
          <div class="panel-header">
            <div class="section-title">
              <el-icon><User /></el-icon>
              <span>角色管理</span>
            </div>
          </div>

          <!-- 搜索和筛选区域 -->
          <div class="filter-section">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索角色"
              :prefix-icon="Search"
              clearable
              size="small"
              class="search-input"
            />

            <el-select
              v-model="selectedTemplateIds"
              multiple
              collapse-tags
              collapse-tags-tooltip
              placeholder="按模板筛选角色"
              size="small"
              class="template-select"
              clearable
            >
              <el-option
                v-for="template in templates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              >
                <div class="template-option">
                  <span class="template-name">{{ template.name }}</span>
                  <el-tag size="small" type="info" effect="plain">
                    {{ getEntityCountByTemplate(template.id) }}个
                  </el-tag>
                </div>
              </el-option>
            </el-select>
          </div>

          <!-- 角色列表区域 -->
          <div class="entity-list-wrapper">
            <el-scrollbar ref="entityScrollbar" class="entity-scrollbar">
              <div class="entity-list">
                <template v-if="filteredEntities.length > 0">
                  <div
                    v-for="entity in filteredEntities"
                    :key="entity.id"
                    :ref="el => setEntityItemRef(el, entity.id)"
                    class="entity-item"
                    :class="{ active: selectedEntityId === entity.id }"
                    @click="selectEntity(entity)"
                  >
                    <div class="entity-avatar" :style="{ backgroundColor: getEntityColor(entity) }">
                      {{ entity.name.charAt(0) }}
                    </div>
                    <div class="entity-info">
                      <div class="entity-name">{{ entity.name }}</div>
                      <div class="entity-type">{{ getEntityType(entity) }}</div>
                    </div>

                    <!-- 实体操作按钮组 -->
                    <div class="entity-actions">
                      <el-tooltip content="查看属性" placement="top">
                        <div
                          class="entity-action-btn view-attributes-btn"
                          @click.stop="showEntityAttributes(entity)"
                        >
                          <el-icon><Document /></el-icon>
                        </div>
                      </el-tooltip>

                      <el-tooltip content="管理关系" placement="top">
                        <div
                          class="entity-action-btn manage-relations-btn"
                          @click.stop="showRelationManager(entity)"
                        >
                          <el-icon><Connection /></el-icon>
                        </div>
                      </el-tooltip>
                    </div>
                  </div>
                </template>

                <el-empty
                  v-else
                  description="未找到角色，请选择其他模板或清除搜索条件"
                  :image-size="100"
                />
              </div>
            </el-scrollbar>

            <!-- 添加关系按钮 - 固定在底部 -->
            <div class="action-footer">
              <el-button
                type="primary"
                :icon="Plus"
                class="add-relation-btn"
                @click="addNewRelation"
                :disabled="!selectedEntityId"
              >
                添加角色关系
              </el-button>
            </div>
          </div>
        </div>

        <!-- 右侧内容面板：关系图谱 -->
        <div class="right-panel">
          <!-- 图谱标题区 -->
          <div class="panel-header">
            <div class="section-title">
              <el-icon><Connection /></el-icon>
              <span>关系图谱</span>
            </div>
            <div class="panel-actions">
              <el-button-group>
                <el-tooltip content="重置视图">
                  <el-button
                    @click="resetGraph"
                    :disabled="!hasRelations"
                    type="default"
                    size="small"
                  >
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="全屏查看">
                  <el-button
                    @click="toggleFullScreen"
                    :disabled="!hasRelations"
                    type="default"
                    size="small"
                  >
                    <el-icon><FullScreen /></el-icon>
                  </el-button>
                </el-tooltip>
              </el-button-group>
            </div>
          </div>

          <!-- 右侧内容区域 -->
          <div class="right-content-wrapper">
            <!-- 图谱展示区 -->
            <div class="graph-container">
              <!-- 图表容器始终存在，确保ref引用有效 -->
              <div ref="graphContainer" class="chart-container"></div>

              <!-- 空状态提示 -->
              <div v-if="!hasEntities" class="empty-graph">
                <el-icon class="empty-icon"><User /></el-icon>
                <div class="empty-text">
                  暂无实体数据，请先添加角色
                </div>
              </div>

              <!-- 图表统计信息 -->
              <div v-if="hasEntities" class="graph-stats">
                <span class="stat-item">
                  <el-icon><User /></el-icon>
                  实体: {{ filteredEntities.length }}
                  <span v-if="selectedTemplateIds.length > 0" class="filter-indicator">
                    (已筛选)
                  </span>
                </span>
                <span class="stat-item">
                  <el-icon><Connection /></el-icon>
                  关系: {{ getFilteredRelationsCount() }}
                </span>
                <span class="stat-item" v-if="getFilteredIsolatedEntitiesCount() > 0">
                  <el-icon><Warning /></el-icon>
                  孤立: {{ getFilteredIsolatedEntitiesCount() }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 关系表单对话框 -->
    <el-dialog
      :title="isEditingRelation ? '编辑角色关系' : '添加角色关系'"
      v-model="relationEditVisible"
      width="600px"
      append-to-body
      destroy-on-close
      class="relation-dialog"
    >
      <el-form
        :model="currentRelation"
        label-position="top"
        class="relation-form"
      >
        <!-- 源角色和目标角色选择 -->
        <div class="roles-row">
          <div class="role-select-container">
            <el-form-item label="源角色" required>
              <el-select
                v-model="selectedSourceTemplateId"
                placeholder="选择角色模板"
                class="mb-2"
                :disabled="isEditMode"
                @change="handleSourceTemplateChange"
              >
                <el-option
                  v-for="template in templates"
                  :key="template.id"
                  :label="template.name"
                  :value="template.id"
                />
              </el-select>

              <el-select
                v-model="currentRelation.source"
                filterable
                placeholder="选择源角色"
                :disabled="isEditingRelation || !selectedSourceTemplateId"
              >
                <el-option
                  v-for="entity in filteredSourceEntities"
                  :key="entity.id"
                  :label="entity.name"
                  :value="entity.id"
                >
                  <div class="entity-option">
                    <div class="entity-avatar" :style="{ backgroundColor: getEntityColor(entity) }">
                      {{ entity.name.charAt(0) }}
                    </div>
                    <span class="entity-name">{{ entity.name }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </div>

          <div class="role-select-container">
            <el-form-item label="目标角色" required>
              <el-select
                v-model="selectedTargetTemplateId"
                placeholder="选择角色模板"
                class="mb-2"
                clearable
                @change="handleTargetTemplateChange"
              >
                <el-option
                  v-for="template in templates"
                  :key="template.id"
                  :label="template.name"
                  :value="template.id"
                />
              </el-select>

              <el-select
                v-model="currentRelation.target"
                filterable
                placeholder="选择目标角色"
                :disabled="isEditingRelation || !selectedTargetTemplateId"
              >
                <el-option
                  v-for="entity in filteredTargetEntities"
                  :key="entity.id"
                  :label="entity.name"
                  :value="entity.id"
                  :disabled="entity.id === currentRelation.source"
                >
                  <div class="entity-option">
                    <div class="entity-avatar" :style="{ backgroundColor: getEntityColor(entity) }">
                      {{ entity.name.charAt(0) }}
                    </div>
                    <span class="entity-name">{{ entity.name }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 关系类型和双向关系 -->
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="关系类型">
              <el-select
                v-model="currentRelation.type"
                placeholder="选择关系类型"
                filterable
              >
                <el-option
                  v-for="type in relationTypeOptions"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                >
                  <el-tag size="small" :type="type.color">{{ type.label }}</el-tag>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="双向关系">
              <el-switch
                v-model="currentRelation.bidirectional"
                active-text="是"
                inactive-text="否"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 关系描述 -->
        <el-form-item label="关系描述">
          <el-input
            v-model="currentRelation.description"
            type="textarea"
            :rows="3"
            placeholder="输入关系描述（可选）"
          />
        </el-form-item>

        <!-- 关系强度 -->
        <el-form-item label="关系强度">
          <div class="strength-container">
            <div class="strength-header">
              <span class="strength-label">{{ formatStrengthTooltip(currentRelation.strength) }}</span>
              <span class="strength-value">{{ currentRelation.strength }}/5</span>
            </div>
            <el-slider
              v-model="currentRelation.strength"
              :min="1"
              :max="5"
              :show-tooltip="false"
              :step="1"
              class="strength-slider"
            />
            <div class="strength-marks">
              <span class="mark-item">很弱</span>
              <span class="mark-item">较弱</span>
              <span class="mark-item">一般</span>
              <span class="mark-item">较强</span>
              <span class="mark-item">很强</span>
            </div>
          </div>
        </el-form-item>

        <!-- 关系标签 -->
        <el-form-item label="关系标签">
          <el-select
            v-model="currentRelation.tags"
            multiple
            allow-create
            filterable
            default-first-option
            placeholder="添加标签（可选）"
          >
            <el-option
              v-for="tag in relationTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="relationEditVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRelation">
            {{ isEditingRelation ? '保存修改' : '添加关系' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 实体属性对话框 -->
    <EntityAttributesDialog
      v-model="attributesDialogVisible"
      :entity="currentAttributesEntity"
      :entityList="entityList"
      :templates="templates"
      :relations="relations"
    />

    <!-- 关系管理对话框 -->
    <el-dialog
      v-model="relationManagerVisible"
      :title="`${currentEntity?.name || ''}的关系管理`"
      width="800px"
      destroy-on-close
      append-to-body
      class="relation-management-dialog"
    >
      <!-- 搜索过滤区域 -->
      <div class="relation-filter-header">
        <el-input
          v-model="relationSearchKeyword"
          placeholder="搜索关系类型或角色名称"
          clearable
          prefix-icon="Search"
          class="relation-search-input"
          @input="filterRelations"
        />
        <el-select
          v-model="relationTypeFilter"
          placeholder="按关系类型筛选"
          clearable
          class="relation-type-filter"
          @change="filterRelations"
        >
          <el-option
            v-for="type in availableRelationTypes"
            :key="type"
            :label="type"
            :value="type"
          />
        </el-select>
      </div>

      <!-- 关系列表 -->
      <el-scrollbar height="400px">
        <div v-if="filteredRelationsList.length > 0" class="relations-list">
          <div
            v-for="relation in filteredRelationsList"
            :key="relation.id"
            class="relation-item"
          >
            <div class="relation-info">
              <div class="relation-entities">
                <span class="entity-from">
                  {{ getEntityNameById(relation.source) }}
                </span>
                <el-icon class="relation-arrow">
                  <component :is="relation.bidirectional ? 'ArrowRight' : 'Right'" />
                </el-icon>
                <span class="entity-to">
                  {{ getEntityNameById(relation.target) }}
                </span>
              </div>
              <el-tag
                size="small"
                :type="getRelationTypeColor(relation.type)"
              >
                {{ relation.type }}
              </el-tag>
            </div>

            <div class="relation-desc" v-if="relation.description">
              {{ relation.description }}
            </div>

            <div class="relation-footer">
              <div class="relation-tags" v-if="relation.tags && relation.tags.length > 0">
                <el-tag
                  v-for="tag in relation.tags"
                  :key="tag"
                  size="small"
                  effect="plain"
                >
                  {{ tag }}
                </el-tag>
              </div>

              <div class="relation-actions">
                <el-button
                  type="primary"
                  size="small"
                  text
                  @click="showEditRelationDialog(relation)"
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  text
                  @click="confirmDeleteRelation(relation.id)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          description="没有找到符合条件的关系"
          :image-size="100"
        >
          <template #extra>
            <el-button @click="clearRelationFilters">清除筛选</el-button>
            <el-button
              type="primary"
              @click="addNewRelation"
            >
              添加关系
            </el-button>
          </template>
        </el-empty>
      </el-scrollbar>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="relationManagerVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 全屏关系图谱弹窗 -->
    <FullscreenGraphDialog
      :visible="fullScreenGraphVisible"
      @update:visible="fullScreenGraphVisible = $event"
      :relations="relations"
      :entityList="entityList"
      :bookId="selectedBookId"
      :templates="templates"
      @node-click="handleFullscreenNodeClick"
      @edge-click="handleFullscreenEdgeClick"
      @add-relation="handleAddRelationFromGraph"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useBookStore } from '@/stores/book'
import { useRelationshipStore } from '@/stores/relationship'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import {
  Search,
  User,
  Connection,
  Plus,
  Edit,
  Delete,
  Refresh,
  FullScreen,
  Document,
  ArrowRight,
  Right,
  Warning
} from '@element-plus/icons-vue'

// 导入组件
import EntityAttributesDialog from '@/components/EntityAttributesDialog.vue'
import FullscreenGraphDialog from '@/components/FullscreenGraphDialog.vue'

// 初始化
const router = useRouter()
const bookStore = useBookStore()
const relationshipStore = useRelationshipStore()

// 响应式数据
const loading = ref(true)
const selectedBookId = ref('')
const selectedEntityId = ref(null)
const selectedTemplateIds = ref([])
const searchKeyword = ref('')
const templates = ref([])
const entityList = ref([])
const relations = ref([])
const graphContainer = ref(null)
const chart = ref(null)
const entityScrollbar = ref(null)
const entityItemRefs = ref(new Map())


// 对话框状态
const attributesDialogVisible = ref(false)
const currentAttributesEntity = ref(null)
const fullScreenGraphVisible = ref(false)
const relationDialog = ref(false)
const entityRelationsDialog = ref(false)
const relationManagerVisible = ref(false)
const relationEditVisible = ref(false)

// 关系管理相关
const currentEntity = ref(null)
const currentEntityForRelations = ref(null)
const currentEntityRelations = ref([])
const isEditingRelation = ref(false)
const currentRelation = ref({
  id: '',
  source: '',
  target: '',
  type: '友好',
  description: '',
  bidirectional: true,
  strength: 3,
  tags: []
})



// 模板选择
const selectedSourceTemplateId = ref('')
const selectedTargetTemplateId = ref('')

// 关系筛选
const relationSearchKeyword = ref('')
const relationTypeFilter = ref('')

// 标签输入
const tagInputVisible = ref(false)
const tagInputValue = ref('')
const tagInput = ref(null)
const showCustomRelationType = ref(false)
const customRelationType = ref('')

// 关系类型选项
const relationTypeOptions = ref([
  { value: "友好", label: "友好", color: "success" },
  { value: "敌对", label: "敌对", color: "danger" },
  { value: "中立", label: "中立", color: "info" },
  { value: "血缘", label: "血缘", color: "warning" },
  { value: "恋爱", label: "恋爱", color: "danger" },
  { value: "师徒", label: "师徒", color: "info" },
  { value: "上下级", label: "上下级", color: "info" },
  { value: "合作", label: "合作", color: "success" }
])

// 关系标签
const relationTags = ref(['重要', '核心', '次要', '暂时', '长期'])

// 强度标记
const strengthMarks = ref({
  1: '很弱',
  2: '较弱',
  3: '一般',
  4: '较强',
  5: '很强'
})

// 计算属性
const filteredEntities = computed(() => {
  if (!Array.isArray(entityList.value)) return []

  let result = entityList.value

  // 按模板筛选
  if (selectedTemplateIds.value.length > 0) {
    result = result.filter(entity =>
      selectedTemplateIds.value.includes(entity.template_id)
    )
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(entity =>
      entity.name.toLowerCase().includes(keyword) ||
      (entity.description && entity.description.toLowerCase().includes(keyword))
    )
  }

  return result
})

const hasRelations = computed(() =>
  Array.isArray(relations.value) && relations.value.length > 0
)

const hasEntities = computed(() => {
  return Array.isArray(filteredEntities.value) && filteredEntities.value.length > 0
})

const filteredSourceEntities = computed(() => {
  if (!selectedSourceTemplateId.value) return []
  return entityList.value.filter(entity =>
    entity.template_id === selectedSourceTemplateId.value
  )
})

const filteredTargetEntities = computed(() => {
  if (!selectedTargetTemplateId.value) return []
  return entityList.value.filter(entity =>
    entity.template_id === selectedTargetTemplateId.value
  )
})

const availableRelationTypes = computed(() => {
  const types = new Set()
  relations.value.forEach(relation => {
    if (relation.type) types.add(relation.type)
  })
  return Array.from(types)
})

const filteredRelationsList = computed(() => {
  if (!currentEntity.value) return []

  let result = relations.value.filter(r =>
    r.source === currentEntity.value.id || r.target === currentEntity.value.id
  )

  // 按关键词筛选
  if (relationSearchKeyword.value) {
    const keyword = relationSearchKeyword.value.toLowerCase()
    result = result.filter(relation => {
      const sourceEntity = getEntityNameById(relation.source)
      const targetEntity = getEntityNameById(relation.target)
      return relation.type.toLowerCase().includes(keyword) ||
             sourceEntity.toLowerCase().includes(keyword) ||
             targetEntity.toLowerCase().includes(keyword)
    })
  }

  // 按关系类型筛选
  if (relationTypeFilter.value) {
    result = result.filter(relation => relation.type === relationTypeFilter.value)
  }

  return result
})

// 工具方法
const getEntityCountByTemplate = (templateId) => {
  return entityList.value.filter(entity => entity.template_id === templateId).length
}

const getEntityType = (entity) => {
  if (!entity || !entity.template_id) return '未知类型'
  const template = templates.value.find(t => t.id === entity.template_id)
  return template ? template.name : '未知类型'
}

const getEntityColor = (entity) => {
  if (!entity || !entity.template_id) return '#409EFF'

  const template = templates.value.find(t => t.id === entity.template_id)
  if (!template) return '#409EFF'

  const templateColors = {
    '正派人设': '#409EFF',
    '反派人设': '#F56C6C',
    '中立人设': '#67C23A',
    '妖兽': '#E6A23C',
    '龙套角色': '#909399',
    '装备': '#9370DB',
    '功法': '#FF9500',
    '道具': '#00CED1',
    '场景': '#6D8B74'
  }

  return templateColors[template.name] || '#409EFF'
}

const getEntityNameById = (id) => {
  const entity = entityList.value.find(e => e.id === id)
  return entity ? entity.name : '未知角色'
}

const getEntityRelationsCount = (entityId) => {
  if (!Array.isArray(relations.value)) return 0
  return relations.value.filter(r =>
    r.source === entityId || r.target === entityId
  ).length
}

// 获取孤立实体数量（原始数据）
const getIsolatedEntitiesCount = () => {
  if (!Array.isArray(entityList.value)) return 0
  return entityList.value.filter(entity =>
    entity && getEntityRelationsCount(entity.id) === 0
  ).length
}

// 获取过滤后的孤立实体数量
const getFilteredIsolatedEntitiesCount = () => {
  if (!Array.isArray(filteredEntities.value)) return 0
  return filteredEntities.value.filter(entity =>
    entity && getEntityRelationsCount(entity.id) === 0
  ).length
}

// 获取过滤后的关系数量
const getFilteredRelationsCount = () => {
  if (!Array.isArray(relations.value) || !Array.isArray(filteredEntities.value)) return 0

  const filteredEntityIds = new Set(filteredEntities.value.map(e => e.id))
  return relations.value.filter(relation =>
    relation && relation.source && relation.target &&
    filteredEntityIds.has(relation.source) &&
    filteredEntityIds.has(relation.target)
  ).length
}

const getRelationLineColor = (relationType) => {
  const colors = {
    '友好': '#67C23A',
    '敌对': '#F56C6C',
    '中立': '#909399',
    '血缘': '#E6A23C',
    '恋爱': '#FFB6C1',
    '师徒': '#409EFF',
    '上下级': '#9370DB',
    '合作': '#2E8B57'
  }
  return colors[relationType] || '#A9A9A9'
}

const getRelationTypeColor = (typeName) => {
  const typeObj = relationTypeOptions.value.find(t => t.value === typeName)
  return typeObj?.color
}

const formatStrengthTooltip = (value) => {
  return strengthMarks.value[value] || '一般'
}

const getStrengthGradient = (strength) => {
  const colors = ['#f56c6c', '#e6a23c', '#409eff', '#67c23a', '#67c23a']
  return colors[strength - 1] || '#409eff'
}

// 数据加载方法
const handleBookChange = async (bookId) => {
  if (!bookId) return

  try {
    loading.value = true
    selectedEntityId.value = null
    selectedTemplateIds.value = []

    await Promise.all([
      loadTemplates(bookId),
      loadEntities(bookId),
      loadRelations(bookId)
    ])

    nextTick(() => {
      setTimeout(() => {
        initGraph()
      }, 300)
    })
  } catch (error) {
    console.error('加载书籍数据失败:', error)
    ElMessage.error('加载书籍数据失败：' + error.message)
  } finally {
    loading.value = false
  }
}

const loadTemplates = async (bookId) => {
  if (!bookId) return

  try {
    const response = await window.pywebview.api.book_controller.get_templates(bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      templates.value = result.data || []
    } else {
      ElMessage.error(result.message || '加载模板失败')
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败: ' + (error.message || String(error)))
  }
}

const loadEntities = async (bookId) => {
  try {
    if (!bookId) {
      entityList.value = []
      return
    }

    const response = await window.pywebview.api.book_controller.get_entities(bookId)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      entityList.value = Array.isArray(result.data) ? result.data : []
    } else {
      console.error('加载实体失败:', result.message)
      ElMessage.error(result.message || '加载实体失败')
      entityList.value = []
    }
  } catch (error) {
    console.error('加载实体失败:', error)
    ElMessage.error('加载实体失败: ' + (error.message || String(error)))
    entityList.value = []
  }
}

const loadRelations = async (bookId) => {
  try {
    if (!bookId) {
      relations.value = []
      return
    }

    const result = await relationshipStore.getRelations(bookId)
    relations.value = Array.isArray(result) ? result : []

    nextTick(() => {
      initGraph()
    })
  } catch (error) {
    console.error('加载关系失败:', error)
    ElMessage.error('加载关系数据失败')
    relations.value = []
  }
}

// 设置实体项的ref引用
const setEntityItemRef = (el, entityId) => {
  if (el) {
    entityItemRefs.value.set(entityId, el)
    console.log('设置实体ref:', entityId, el)
  } else {
    entityItemRefs.value.delete(entityId)
  }
}

// 滚动到指定实体
const scrollToEntity = (entityId) => {
  console.log('尝试滚动到实体:', entityId)

  if (!entityScrollbar.value) {
    console.log('滚动容器不存在')
    return
  }

  if (!entityItemRefs.value.has(entityId)) {
    console.log('实体元素引用不存在:', entityId)
    return
  }

  const entityElement = entityItemRefs.value.get(entityId)
  if (!entityElement) {
    console.log('实体元素不存在')
    return
  }

  // 获取滚动容器
  const scrollContainer = entityScrollbar.value.wrapRef
  if (!scrollContainer) {
    console.log('滚动容器wrapRef不存在')
    return
  }

  console.log('开始检查实体可见性...')

  // 计算实体元素相对于滚动容器的位置
  const containerRect = scrollContainer.getBoundingClientRect()
  const entityRect = entityElement.getBoundingClientRect()

  // 检查实体是否在可视区域内
  const isVisible = entityRect.top >= containerRect.top &&
                   entityRect.bottom <= containerRect.bottom

  if (!isVisible) {
    // 计算需要滚动的距离
    const entityOffsetTop = entityElement.offsetTop
    const containerHeight = scrollContainer.clientHeight
    const entityHeight = entityElement.offsetHeight

    // 将实体滚动到容器中央
    const targetScrollTop = entityOffsetTop - (containerHeight / 2) + (entityHeight / 2)

    // 平滑滚动
    scrollContainer.scrollTo({
      top: Math.max(0, targetScrollTop),
      behavior: 'smooth'
    })

    // 获取实体名称用于日志
    const entity = entityList.value.find(e => e.id === entityId)
    console.log('自动滚动到实体:', entity?.name || entityId, '位置:', targetScrollTop)
  }
}

// 实体操作方法
const selectEntity = (entity) => {
  console.log('选中实体:', entity.name, entity.id)
  selectedEntityId.value = entity.id

  if (chart.value) {
    highlightEntityInGraph(entity.id)
  }

  // 自动滚动到选中的实体
  scrollToEntity(entity.id)
}

const showEntityAttributes = (entity) => {
  currentAttributesEntity.value = entity
  attributesDialogVisible.value = true
}

const showRelationManager = (entity) => {
  currentEntity.value = entity
  relationSearchKeyword.value = ''
  relationTypeFilter.value = ''
  relationManagerVisible.value = true
}

// 关系操作方法
const showAddRelationDialog = (sourceEntityId = null) => {
  if (!selectedBookId.value) {
    ElMessage.warning('请先选择一本书籍')
    return
  }

  isEditingRelation.value = false

  if (sourceEntityId) {
    const sourceEntity = entityList.value.find(e => e.id === sourceEntityId)
    if (sourceEntity) {
      currentEntity.value = sourceEntity
      currentRelation.value = {
        id: '',
        source: sourceEntityId,
        target: '',
        type: '友好',
        description: '',
        bidirectional: true,
        strength: 3,
        tags: []
      }
      selectedSourceTemplateId.value = sourceEntity.template_id || ''
    }
  } else {
    currentRelation.value = {
      id: '',
      source: '',
      target: '',
      type: '友好',
      description: '',
      bidirectional: true,
      strength: 3,
      tags: []
    }
    selectedSourceTemplateId.value = ''
    selectedTargetTemplateId.value = ''
  }

  relationEditVisible.value = true
}

const addNewRelation = () => {
  console.log('添加角色关系，当前选中实体ID:', selectedEntityId.value)
  console.log('当前书籍ID:', selectedBookId.value)

  // 检查是否已选择书籍
  if (!selectedBookId.value) {
    ElMessage.warning('请先选择一本书籍')
    return
  }

  // 重置编辑状态
  isEditingRelation.value = false

  // 重置当前选中的模板
  selectedSourceTemplateId.value = ''
  selectedTargetTemplateId.value = ''

  // 获取当前选中的实体
  const sourceEntityId = selectedEntityId.value || ''
  const sourceEntity = sourceEntityId
    ? entityList.value.find(e => e.id === sourceEntityId)
    : null

  if (!sourceEntity) {
    console.warn('未选中源实体，或找不到对应实体')
    ElMessage.warning('请先选择一个角色')
    return
  }

  // 确保currentEntity被正确设置 - 关键修复
  currentEntity.value = sourceEntity

  console.log('找到源实体:', sourceEntity.name, '模板ID:', sourceEntity.template_id)

  // 重置并创建关系对象并预填充数据
  currentRelation.value = {
    id: '', // 使用空字符串而不是null
    source: sourceEntityId,  // 预填充源实体ID
    type: '友好',  // 默认关系类型
    target: '',
    description: '',
    bidirectional: true,
    strength: 3,
    tags: []
  }

  // 预设源实体对应的模板
  selectedSourceTemplateId.value = sourceEntity.template_id || ''

  // 确保模板选择器显示正确
  nextTick(() => {
    console.log('预选模板设置完成，值为:', selectedSourceTemplateId.value)
    console.log('当前关系对象:', JSON.stringify(currentRelation.value))
    console.log('当前实体已设置:', currentEntity.value.name, currentEntity.value.id)
  })

  // 打开关系编辑弹窗
  relationEditVisible.value = true
}



// 图表相关方法
const initGraph = () => {
  console.log('initGraph 被调用')
  console.log('graphContainer.value:', graphContainer.value)
  console.log('entityList.value:', entityList.value)
  console.log('relations.value:', relations.value)

  if (!graphContainer.value) {
    console.log('graphContainer 不存在，退出')
    return
  }

  if (chart.value) {
    chart.value.dispose()
  }

  // 确保容器有正确的尺寸
  const containerWidth = graphContainer.value.clientWidth
  const containerHeight = graphContainer.value.clientHeight

  console.log('容器尺寸:', { width: containerWidth, height: containerHeight })

  if (containerWidth === 0 || containerHeight === 0) {
    console.log('容器尺寸为0，设置最小尺寸')
    graphContainer.value.style.width = '100%'
    graphContainer.value.style.height = '500px'
  }

  chart.value = echarts.init(graphContainer.value)

  // 检查是否有过滤后的实体数据
  if (!Array.isArray(filteredEntities.value) || filteredEntities.value.length === 0) {
    console.log('没有过滤后的实体数据，退出')
    return
  }

  // 准备节点数据 - 显示所有实体
  const nodes = []
  const nodeMap = new Map()

  // 使用过滤后的实体列表，支持模板筛选
  if (Array.isArray(filteredEntities.value)) {
    console.log('使用过滤后的实体列表，数量:', filteredEntities.value.length)

    filteredEntities.value
      .filter(entity => entity && entity.id)
      .forEach(entity => {
        const relationCount = getEntityRelationsCount(entity.id)

        const node = {
          id: entity.id,
          name: entity.name,
          value: relationCount,
          // 根据关系数量调整节点大小，最小30，最大80
          symbolSize: Math.max(30, Math.min(80, 30 + relationCount * 8)),
          itemStyle: {
            color: getEntityColor(entity),
            // 没有关系的实体使用虚线边框
            borderColor: relationCount > 0 ? getEntityColor(entity) : '#ddd',
            borderWidth: relationCount > 0 ? 0 : 2,
            borderType: relationCount > 0 ? 'solid' : 'dashed'
          },
          label: {
            show: true,
            fontSize: relationCount > 0 ? 12 : 10,
            color: relationCount > 0 ? '#333' : '#999'
          },
          // 添加分类信息，用于布局
          category: relationCount > 0 ? 'connected' : 'isolated'
        }

        nodes.push(node)
        nodeMap.set(entity.id, node)
      })
  }

  console.log('节点数据准备完成，数量:', nodes.length)
  console.log('节点示例:', nodes[0])

  // 准备边数据 - 只显示涉及到过滤后实体的关系
  const filteredEntityIds = new Set(filteredEntities.value.map(e => e.id))
  console.log('过滤后的实体ID集合:', Array.from(filteredEntityIds))

  const edges = Array.isArray(relations.value) ? relations.value
    .filter(relation => {
      // 只保留两端都在过滤后实体中的关系
      const isValid = relation && relation.source && relation.target &&
                     filteredEntityIds.has(relation.source) &&
                     filteredEntityIds.has(relation.target)
      if (!isValid) {
        console.log('过滤掉关系:', relation?.source, '->', relation?.target)
      }
      return isValid
    })
    .map(relation => {
      const sourceNode = nodeMap.get(relation.source)
      const targetNode = nodeMap.get(relation.target)

      if (!sourceNode || !targetNode) {
        console.log('找不到对应节点:', { source: relation.source, target: relation.target, sourceNode, targetNode })
        return null
      }

    return {
      source: relation.source,
      target: relation.target,
      relationId: relation.id,
      value: relation.strength || 1,
      label: {
        show: true,
        formatter: relation.type || '未知关系',
        fontSize: 12,
        color: '#666'
      },
      lineStyle: {
        width: Math.max(2, (relation.strength || 1) * 1.5),
        color: getRelationLineColor(relation.type),
        type: relation.bidirectional ? 'solid' : 'dashed',
        curveness: 0.1
      },
      symbol: ['none', relation.bidirectional ? 'arrow' : 'arrow'],
      symbolSize: [6, 10],
      // 确保边可以被点击
      emphasis: {
        lineStyle: {
          width: Math.max(4, (relation.strength || 1) * 2),
          shadowBlur: 10,
          shadowColor: getRelationLineColor(relation.type)
        }
      }
    }
  }).filter(Boolean) : []

  console.log('边数据准备完成，数量:', edges.length)
  console.log('边示例:', edges[0])

  // 只要有节点就显示图表，即使没有边
  if (nodes.length === 0) {
    console.log('没有节点数据，退出')
    return
  }

  console.log('准备设置图表选项...')

  try {
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          if (params.dataType === 'node') {
            return `<strong>${params.data.name}</strong><br/>关系数量: ${params.data.value}`
          } else if (params.dataType === 'edge') {
            const sourceEntity = getEntityNameById(params.data.source)
            const targetEntity = getEntityNameById(params.data.target)
            return `<strong>${sourceEntity} → ${targetEntity}</strong><br/>关系类型: ${params.data.label.formatter}<br/>点击编辑关系`
          }
          return ''
        },
        backgroundColor: 'rgba(50, 50, 50, 0.8)',
        borderRadius: 8,
        padding: 10,
        textStyle: {
          color: '#fff'
        }
      },
      animation: true,
      animationDuration: 1000,
      animationEasingUpdate: 'quinticInOut',
      series: [{
        type: 'graph',
        layout: 'force',
        draggable: true,
        roam: true,
        zoom: 1,
        nodeScaleRatio: 0.6,
        focusNodeAdjacency: true,
        data: nodes,
        links: edges,
        edgeSymbolSize: [6, 10],
        categories: [],
        itemStyle: {
          borderWidth: 2,
          borderColor: 'rgba(255, 255, 255, 0.3)',
          shadowColor: 'rgba(0, 0, 0, 0.3)',
          shadowBlur: 10
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{b}',
          fontSize: 12,
          fontWeight: 'bold',
          color: '#333'
        },
        lineStyle: {
          color: 'source',
          curveness: 0.25,
          width: 3,
          shadowColor: 'rgba(0, 0, 0, 0.2)',
          shadowBlur: 5,
          cap: 'round',
          join: 'round'
        },
        force: {
          repulsion: edges.length > 0 ? 400 : 200,
          gravity: edges.length > 0 ? 0.15 : 0.3,
          edgeLength: 120,
          layoutAnimation: true,
          friction: 0.6
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 6,
            shadowBlur: 10
          },
          itemStyle: {
            borderWidth: 3,
            shadowBlur: 15
          }
        }
      }]
    }

    console.log('设置图表选项，节点数:', nodes.length, '边数:', edges.length)
    chart.value.setOption(option)
    console.log('图表选项设置完成')

    // 确保图表正确渲染
    setTimeout(() => {
      if (chart.value) {
        chart.value.resize()
        console.log('图表resize完成')

        // 强制重绘
        chart.value.dispatchAction({
          type: 'graphRoam',
          zoom: 1
        })
        console.log('图表强制重绘完成')
      }
    }, 100)

    // 添加点击事件
    chart.value.on('click', (params) => {
      console.log('图表点击事件:', params)

      if (params.dataType === 'node' && params.data && params.data.id) {
        const entity = entityList.value.find(e => e && e.id === params.data.id)
        if (entity) {
          selectEntity(entity)
        }
      } else if (params.dataType === 'edge' && params.data && params.data.relationId) {
        console.log('点击了关系线条，relationId:', params.data.relationId)
        const relation = relations.value.find(r => r && r.id === params.data.relationId)
        if (relation) {
          console.log('找到关系数据，准备编辑:', relation)
          showEditRelationDialog(relation)
        } else {
          console.warn('未找到对应的关系数据')
        }
      }
    })

    console.log('图表事件绑定完成')

    // 添加鼠标悬停事件，提升交互体验
    chart.value.on('mouseover', (params) => {
      if (params.dataType === 'edge') {
        chart.value.getZr().setCursorStyle('pointer')
      }
    })

    chart.value.on('mouseout', (params) => {
      if (params.dataType === 'edge') {
        chart.value.getZr().setCursorStyle('default')
      }
    })
  } catch (error) {
    console.error('初始化图表失败:', error)
    console.error('错误堆栈:', error.stack)
    ElMessage.error('初始化图表失败：' + error.message)
  }

  console.log('initGraph 执行完成')
}

const resetGraph = () => {
  initGraph()
}

const toggleFullScreen = () => {
  if (!hasRelations.value) {
    ElMessage.warning('暂无关系数据，无法打开全屏图谱')
    return
  }

  if (!entityList.value || entityList.value.length === 0) {
    ElMessage.warning('暂无角色数据，请先添加角色')
    return
  }

  fullScreenGraphVisible.value = true
}

const highlightEntityInGraph = (entityId) => {
  if (!chart.value) return

  const dataIndex = chart.value.getOption().series[0].data.findIndex(
    item => item.id === entityId
  )

  if (dataIndex === -1) return

  chart.value.dispatchAction({
    type: 'highlight',
    seriesIndex: 0,
    dataIndex: dataIndex
  })

  chart.value.dispatchAction({
    type: 'focusNodeAdjacency',
    seriesIndex: 0,
    dataIndex: dataIndex
  })
}

// 关系编辑相关方法
const showEditRelationDialog = (relation) => {
  nextTick(() => {
    isEditingRelation.value = true

    currentRelation.value = {
      id: relation.id || '',
      source: relation.source || '',
      target: relation.target || '',
      type: relation.type || '',
      description: relation.description || '',
      bidirectional: Boolean(relation.bidirectional),
      strength: relation.strength || 3,
      tags: Array.isArray(relation.tags) ? [...relation.tags] : []
    }

    const sourceEntity = entityList.value.find(e => e.id === relation.source)
    if (sourceEntity) {
      selectedSourceTemplateId.value = sourceEntity.template_id
    }

    const targetEntity = entityList.value.find(e => e.id === relation.target)
    if (targetEntity) {
      selectedTargetTemplateId.value = targetEntity.template_id
    }

    relationEditVisible.value = true
  })
}



const handleSourceTemplateChange = (templateId) => {
  if (templateId !== selectedSourceTemplateId.value) {
    currentRelation.value.source = ''
  }
}

const handleTargetTemplateChange = (templateId) => {
  currentRelation.value.target = ''
}

const saveRelation = async () => {
  try {
    console.log('保存关系，状态检查:')
    console.log('- 当前关系数据:', JSON.stringify(currentRelation.value))
    console.log('- 当前书籍ID:', selectedBookId.value)

    // 验证书籍ID
    if (!selectedBookId.value) {
      ElMessage.warning('请先选择一本书籍')
      return
    }

    // 验证表单
    if (!currentRelation.value.source || !currentRelation.value.type || !currentRelation.value.target) {
      ElMessage({
        type: 'warning',
        message: '请填写完整的关系信息'
      })
      return
    }

    // 检查源实体和目标实体是否相同
    if (currentRelation.value.source === currentRelation.value.target) {
      ElMessage({
        type: 'warning',
        message: '源实体和目标实体不能相同'
      })
      return
    }

    // 保存关系的后端调用
    if (isEditingRelation.value && currentRelation.value.id) {
      console.log('更新现有关系:', currentRelation.value.id)

      await relationshipStore.updateRelation(currentRelation.value.id, {
        ...currentRelation.value,
        bookId: selectedBookId.value
      })
      ElMessage.success('关系更新成功')
    } else {
      console.log('添加新关系')
      const newRelation = {
        bookId: selectedBookId.value,
        source: currentRelation.value.source,
        target: currentRelation.value.target,
        type: currentRelation.value.type,
        description: currentRelation.value.description || '',
        bidirectional: currentRelation.value.bidirectional || false,
        strength: currentRelation.value.strength || 3,
        tags: currentRelation.value.tags || []
      }

      console.log('准备提交的新关系数据:', JSON.stringify(newRelation))

      try {
        const result = await relationshipStore.addRelation(newRelation)
        console.log('添加关系成功，返回结果:', result)

        if (!result || !result.id) {
          console.warn('添加关系返回结果缺少关键字段:', result)
        }

        // 刷新图谱显示
        await loadRelations(selectedBookId.value)

        ElMessage.success('关系添加成功')
      } catch (addError) {
        console.error('添加关系失败:', addError)
        ElMessage.error('添加关系失败: ' + (addError.message || '未知错误'))
        throw addError
      }
    }

    // 更新当前显示的关系列表 - 关键修复
    if (currentEntity.value && currentEntity.value.id) {
      // 如果有loadRelationsList方法，调用它
      // loadRelationsList(currentEntity.value.id)
    } else {
      console.log('没有当前实体或从全屏模式添加，跳过更新关系列表')
    }

    // 重新加载关系数据
    await loadRelations(selectedBookId.value)

    // 更新图谱
    initGraph()

    // 关闭弹窗
    relationEditVisible.value = false
  } catch (error) {
    console.error('保存关系失败:', error)
    console.error('保存关系失败时的关系数据:', JSON.stringify(currentRelation.value))
    console.error('保存关系失败时的书籍ID:', selectedBookId.value)
    ElMessage({
      type: 'error',
      message: '保存关系失败：' + (error.message || '请检查网络连接')
    })
  }
}

const addRelation = async (relation) => {
  try {
    await relationshipStore.addRelation(relation)
    await loadRelations(selectedBookId.value)
    ElMessage.success('关系添加成功')
  } catch (error) {
    console.error('添加关系失败:', error)
    ElMessage.error('添加关系失败: ' + (error.message || '未知错误'))
    throw error
  }
}

const updateRelation = async (relationId, relation) => {
  if (!relationId) {
    throw new Error('关系ID不能为空')
  }

  return await relationshipStore.updateRelation(relationId, relation)
}

// 筛选相关方法
const filterRelations = () => {
  // 触发计算属性重新计算
}

const clearRelationFilters = () => {
  relationSearchKeyword.value = ''
  relationTypeFilter.value = ''
}

// 全屏图谱相关方法
const handleFullscreenNodeClick = (entity) => {
  showEntityAttributes(entity)
}

const handleFullscreenEdgeClick = (relation) => {
  showEditRelationDialog(relation)
}

const handleAddRelationFromGraph = (sourceEntityId) => {
  showAddRelationDialog(sourceEntityId)
}

// 删除关系相关方法
const confirmDeleteRelation = (relationId) => {
  ElMessageBox.confirm('确定要删除这个关系吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await relationshipStore.deleteRelation(relationId)
      await loadRelations(selectedBookId.value)
      ElMessage.success('关系删除成功')
      initGraph()
    } catch (error) {
      console.error('删除关系失败:', error)
      ElMessage.error('删除关系失败')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 生命周期
onMounted(async () => {
  try {
    await bookStore.loadBooks()

    if (bookStore.bookList.length > 0) {
      selectedBookId.value = bookStore.bookList[0].id
      await handleBookChange(selectedBookId.value)
    }
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('初始化失败')
  } finally {
    loading.value = false
  }
})

// 监听数据变化，重新初始化图表
watch([entityList, relations], () => {
  nextTick(() => {
    setTimeout(() => {
      initGraph()
    }, 100)
  })
}, { deep: true })

// 监听模板筛选变化，重新渲染图表
watch(selectedTemplateIds, () => {
  console.log('模板筛选变化，重新渲染图表:', selectedTemplateIds.value)
  nextTick(() => {
    setTimeout(() => {
      initGraph()
    }, 100)
  })
}, { deep: true })

// 监听窗口大小变化
watch(() => graphContainer.value, () => {
  if (chart.value) {
    nextTick(() => {
      chart.value.resize()
    })
  }
})
</script>

<style lang="scss" scoped>
@import '@/scss/custompool.scss';

// 书籍选择器样式优化
.header-actions {
  .book-selector-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;

    .selector-label {
      font-size: 14px;
      color: var(--el-text-color-regular);
      white-space: nowrap;
    }

    .book-selector {
      min-width: 200px;
    }
  }
}

.book-option {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .book-title {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .book-info {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 2px;
  }
}

// 关系图谱专用样式
.relation-content-container {
  display: flex;
  height: calc(100vh - 140px);
  gap: 20px;
  overflow: hidden;
}

.left-panel {
  width: 350px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  background: rgba(var(--el-bg-color-rgb), 0.7);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 4px 24px rgba(0, 0, 0, 0.05),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05);
  overflow: hidden;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(var(--el-bg-color-rgb), 0.7);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 4px 24px rgba(0, 0, 0, 0.05),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;

  .section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);

    .el-icon {
      font-size: 20px;
      color: var(--el-color-primary);
    }
  }



  .panel-actions {
    display: flex;
    gap: 8px;
  }
}

.filter-section {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;

  .search-input {
    margin-bottom: 12px;
  }

  .template-select {
    width: 100%;
  }
}

.entity-list-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .entity-scrollbar {
    flex: 1;
    padding: 0 20px;
  }

  .entity-list {
    padding: 16px 0;
  }
}

.entity-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  position: relative;

  &:hover {
    background: rgba(var(--el-color-primary-rgb), 0.05);
    transform: translateX(4px);
  }

  &.active {
    background: rgba(var(--el-color-primary-rgb), 0.1);
    border-color: rgba(var(--el-color-primary-rgb), 0.2);
    transform: translateX(4px);

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background: var(--el-color-primary);
      border-radius: 3px 0 0 3px;
    }
  }

  .entity-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
    flex-shrink: 0;
    margin-right: 12px;
  }

  .entity-info {
    flex: 1;
    min-width: 0;

    .entity-name {
      font-weight: 500;
      font-size: 14px;
      margin-bottom: 4px;
      color: var(--el-text-color-primary);
    }

    .entity-type {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }

  .entity-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s;

    .entity-action-btn {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: var(--el-color-primary);
        color: white;
        transform: scale(1.1);
      }

      .el-icon {
        font-size: 14px;
      }
    }
  }

  &:hover .entity-actions {
    opacity: 1;
  }
}

.action-footer {
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;

  .add-relation-btn {
    width: 100%;
  }
}

.right-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.graph-container {
  flex: 1;
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;

  .chart-container {
    flex: 1;
    width: 100%;
    min-height: 500px;
    height: 100%;
    border-radius: 8px;
    background: rgba(var(--el-bg-color-rgb), 0.3);
    position: relative;
    z-index: 1;
  }

  .empty-graph {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .empty-icon {
      font-size: 64px;
      color: var(--el-text-color-placeholder);
      margin-bottom: 16px;
    }

    .empty-text {
      font-size: 16px;
      color: var(--el-text-color-secondary);
      margin-bottom: 24px;
    }

    .empty-add-btn {
      padding: 12px 24px;
    }
  }

  .graph-stats {
    display: flex;
    justify-content: center;
    gap: 24px;
    padding: 12px 0;
    border-top: 1px solid var(--el-border-color-light);
    margin-top: 12px;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
      color: var(--el-text-color-regular);

      .el-icon {
        font-size: 14px;
        color: var(--el-color-primary);
      }

      .filter-indicator {
        font-size: 10px;
        color: var(--el-color-primary);
        background: rgba(var(--el-color-primary-rgb), 0.1);
        padding: 1px 4px;
        border-radius: 8px;
        margin-left: 4px;
      }
    }
  }
}

.template-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .template-name {
    flex: 1;
  }
}

.entity-option {
  display: flex;
  align-items: center;
  gap: 8px;

  .entity-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 12px;
  }
}

// 对话框样式
.relation-dialog {
  .roles-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
  }

  .strength-container {
    .strength-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .strength-label {
        font-weight: 500;
        color: var(--el-color-primary);
        font-size: 14px;
      }

      .strength-value {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        background: var(--el-fill-color-light);
        padding: 2px 8px;
        border-radius: 12px;
      }
    }

    .strength-slider {
      margin: 8px 0;

      :deep(.el-slider__runway) {
        height: 6px;
        background: var(--el-fill-color-light);
      }

      :deep(.el-slider__bar) {
        background: linear-gradient(90deg,
          #f56c6c 0%,
          #e6a23c 25%,
          #409eff 50%,
          #67c23a 75%,
          #67c23a 100%);
      }

      :deep(.el-slider__button) {
        width: 16px;
        height: 16px;
        border: 2px solid #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }

    .strength-marks {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;

      .mark-item {
        font-size: 11px;
        color: var(--el-text-color-placeholder);
        text-align: center;
        flex: 1;
      }
    }
  }
}

.relation-management-dialog {
  .relation-filter-header {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;

    .relation-search-input {
      flex: 1;
    }

    .relation-type-filter {
      width: 200px;
    }
  }

  .relations-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .relation-item {
    padding: 16px;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 8px;
    background: var(--el-fill-color-light);

    .relation-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .relation-entities {
        display: flex;
        align-items: center;
        gap: 8px;

        .relation-arrow {
          color: var(--el-color-primary);
        }
      }
    }

    .relation-desc {
      color: var(--el-text-color-regular);
      margin-bottom: 8px;
      font-size: 14px;
    }

    .relation-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .relation-tags {
        display: flex;
        gap: 4px;
      }

      .relation-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
}

// 暗色主题适配
html.dark {
  .left-panel,
  .right-panel {
    background: rgba(30, 35, 45, 0.7);
    border-color: rgba(255, 255, 255, 0.05);
    box-shadow:
      0 4px 24px rgba(0, 0, 0, 0.2),
      inset 0 0 0 1px rgba(255, 255, 255, 0.03);
  }

  .panel-header,
  .filter-section,
  .action-footer {
    border-color: rgba(255, 255, 255, 0.05);
  }

  .entity-item {
    &:hover {
      background: rgba(var(--el-color-primary-rgb), 0.1);
    }

    &.active {
      background: rgba(var(--el-color-primary-rgb), 0.15);
      border-color: rgba(var(--el-color-primary-rgb), 0.3);
    }
  }

  .graph-content {
    background: rgba(0, 0, 0, 0.2);
  }
}

// 响应式调整
@media screen and (max-width: 768px) {
  .relation-content-container {
    flex-direction: column;
    height: auto;

    .left-panel {
      width: 100%;
      height: 300px;
      order: 2;
    }

    .right-panel {
      height: 400px;
      order: 1;
    }
  }

  .relation-dialog .roles-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .relation-filter-header {
    flex-direction: column;

    .relation-type-filter {
      width: 100%;
    }
  }
}

/* 书籍选择器样式优化 */
:deep(.book-selector) {
  .el-input__wrapper {
    background-color: var(--el-bg-color) !important;
    border: 1px solid var(--el-border-color) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;

    &:hover {
      border-color: var(--el-color-primary) !important;
    }

    &.is-focus {
      border-color: var(--el-color-primary) !important;
      box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2) !important;
    }
  }

  .el-input__inner {
    color: var(--el-text-color-primary) !important;

    &::placeholder {
      color: var(--el-text-color-placeholder) !important;
    }
  }
}

/* 自定义选择器下拉框样式，适配深色主题 */
:deep(.el-select-dropdown) {
  background-color: var(--el-bg-color-page) !important;
  border: 1px solid var(--el-border-color) !important;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item) {
  color: var(--el-text-color-primary) !important;
  background-color: transparent !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background-color: var(--el-fill-color-light) !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item.selected) {
  background-color: var(--el-color-primary) !important;
  color: #fff !important;
}

/* 模板选择器的特殊样式 */
:deep(.template-selector .el-select-dropdown) {
  background-color: #2c2c2c !important;
  border: 1px solid #404040 !important;
}

:deep(.template-selector .el-select-dropdown__item) {
  color: #e5e5e5 !important;
  padding: 8px 12px !important;
}

:deep(.template-selector .el-select-dropdown__item:hover) {
  background-color: #404040 !important;
}

:deep(.template-selector .el-select-dropdown__item.selected) {
  background-color: var(--el-color-primary) !important;
  color: #fff !important;
}

/* 模板选项中的计数标签样式 */
:deep(.template-option) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

:deep(.template-count) {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  background-color: var(--el-fill-color);
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
}
</style>