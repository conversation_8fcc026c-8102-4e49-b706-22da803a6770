<template>
    <div class="scene-cards-container">
      <!-- 顶部操作栏 -->
      <div class="action-bar">
        <el-select 
          v-model="selectedBookId" 
          class="book-select" 
          size="large" 
          placeholder="请选择书籍"
          @change="handleBookChange"
        >
          <el-option
            v-for="book in bookStore.bookList"
            :key="book.id"
            :label="book.title"
            :value="book.id"
          />
        </el-select>
        <el-select 
          v-model="sceneData.currentPoolId" 
          class="pool-select" 
          size="large" 
          placeholder="请选择场景卡池"
          :disabled="!selectedBookId"
        >
          <el-option
            key="all"
            label="全部场景"
            value="all"
          >
            <div class="pool-option">
              <span class="pool-name">全部场景</span>
              <span class="pool-info">
                {{ allScenesPool.scenes.length }}个场景
              </span>
            </div>
          </el-option>
          <el-divider content-position="center">
            其他卡池
            <el-button
              class="manage-pools-btn"
              link
              @click.stop="showManagePoolsDialog"
              :disabled="!selectedBookId"
            >
              <el-icon><Setting /></el-icon>
            </el-button>
          </el-divider>
          <el-option
            v-for="pool in sortedPools"
            :key="pool.id"
            :label="pool.name"
            :value="pool.id"
          >
            <div class="pool-option">
              <span class="pool-name">{{ pool.name }}</span>
              <span class="pool-info">
                {{ pool.scenes.length }}个场景 | {{ formatDate(pool.updateTime) }}
              </span>
              <el-button
                v-if="sceneData.pools.length > 1"
                class="delete-pool-btn"
                type="danger"
                link
                @click.stop="confirmDeletePool(pool)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </el-option>
          <template #prefix>
            <el-button
              class="create-pool-btn"
              link
              @click.stop="showCreatePoolDialog"
              :disabled="!selectedBookId"
            >
              <el-icon><Plus /></el-icon>
            </el-button>
          </template>
        </el-select>
        <el-button type="primary" @click="showCreateDialog" size="large" :disabled="!currentPool">
          <el-icon><Plus /></el-icon>
          创建场景
        </el-button>
        <el-select v-model="drawCount" class="draw-count-select" size="large" :disabled="scenes.length < 2">
          <el-option
            v-for="n in Math.min(5, scenes.length)"
            :key="n"
            :label="`抽取${n}个场景`"
            :value="n"
          />
        </el-select>
        <el-button @click="randomDraw" size="large" :disabled="scenes.length < 2">
        
          随机抽取
        </el-button>
        <el-dropdown @command="handleExportCommand" trigger="click">
          <el-button size="large" :disabled="scenes.length === 0">
            <el-icon><Download /></el-icon>
            导出场景
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="exportAll">
                <div class="dropdown-item-content">
                  <el-icon><FolderOpened /></el-icon>
                  <span>导出全部场景</span>
                </div>
              </el-dropdown-item>
              <el-dropdown-item 
                command="exportPool" 
                :disabled="!currentPool || currentPool.isVirtual"
              >
                <div class="dropdown-item-content">
                  <el-icon><CopyDocument /></el-icon>
                  <span>复制当前卡池</span>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown @command="handleImportCommand" trigger="click">
          <el-button size="large">
            <el-icon><Upload /></el-icon>
            导入场景
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="importFile">
                <div class="dropdown-item-content">
                  <el-icon><FolderAdd /></el-icon>
                  <span>导入全部场景</span>
                </div>
              </el-dropdown-item>
              <el-dropdown-item 
                command="importJson" 
                :disabled="!currentPool || currentPool.isVirtual"
              >
                <div class="dropdown-item-content">
                  <el-icon><DocumentAdd /></el-icon>
                  <span>导入到当前卡池</span>
                </div>
              </el-dropdown-item>
              <el-dropdown-item 
                command="mergeJson" 
                :disabled="!currentPool || currentPool.isVirtual"
              >
                <div class="dropdown-item-content">
                  <el-icon><Connection /></el-icon>
                  <span>融合到当前卡池</span>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <input
          type="file"
          ref="fileInput"
          style="display: none"
          accept=".json"
          @change="importScenes"
        />
        <el-button @click="historyDialogVisible = true" size="large" :disabled="!selectedBookId">
          <el-icon><Timer /></el-icon>
          灵感历史
        </el-button>
      </div>
  
      <!-- 场景卡片网格 - 改为无限画布 -->
      <div class="cards-grid" 
           @mousedown="handleCanvasMouseDown" 
           @mouseup="handleCanvasMouseUp"
           @mousemove="handleCanvasMouseMove"
           @contextmenu.prevent
           @wheel="handleCanvasWheel"
           ref="cardsGridRef">
        
        <!-- 拖动提示 -->
        <div class="drag-hint" :class="{ visible: showDragHint }">
          按住 <span class="key-hint">右键</span> 拖动画布 | 使用 <span class="key-hint">滚轮</span> 缩放
        </div>
        
        <!-- 无限画布容器 -->
        <div class="infinite-canvas" 
             :class="{ dragging: isDraggingCanvas }" 
             :style="canvasStyle" 
             ref="infiniteCanvasRef">
          
          <!-- 场景卡片 -->
          <div v-for="scene in scenes"
               :key="scene.id"
               class="scene-card-wrapper"
               :id="`scene-card-wrapper-${scene.id}`"
               :style="getCardStyle(scene)">

            <!-- 修改交互区域避免覆盖卡片按钮 -->
            <div class="scene-card-interaction-area"
                 @mousedown.stop="handleCardMouseDown(scene.id, $event)"
                 @dblclick.stop="handleCardDoubleClick(scene, $event)"
                 @mouseover.stop="handleCardMouseEnter(scene.id, $event)"
                 @mouseleave.stop="handleCardMouseLeave(scene.id)">
              <!-- 卡片内容 -->
              <Card
                :title="scene.title"
                :description="scene.description"
                :tags="scene.tags"
                @delete="confirmDeleteScene(scene)"
                :class="{ 
                  active: activeCardId === scene.id,
                  dragging: draggingCardId === scene.id
                }"
                :id="`scene-card-${scene.id}`"
              />
            </div>
          </div>
        </div>
        
        <!-- 添加画布控制按钮 -->
        <div class="canvas-controls">
          <div class="control-btn" @click="zoomIn">
            <el-icon><ZoomIn /></el-icon>
          </div>
          <div class="zoom-display">{{ Math.round(canvasScale * 100) }}%</div>
          <div class="control-btn" @click="zoomOut">
            <el-icon><ZoomOut /></el-icon>
          </div>
          <div class="control-btn" @click="autoLayout" title="自动排版">
            <el-icon><Grid /></el-icon>
          </div>
          <div class="control-btn" @click="resetCanvas">
            <el-icon><Refresh /></el-icon>
          </div>
          <!-- 添加全屏按钮 -->
          <div class="control-btn" @click="openFullscreenCanvas">
            <el-icon><FullScreen /></el-icon>
          </div>
        </div>
      </div>
  
      <!-- 自定义创建/编辑对话框 -->
      <div v-if="dialogVisible" class="custom-dialog-overlay" @click="handleSceneDialogOverlayClick">
        <div class="custom-dialog custom-dialog-medium" @click.stop>
          <div class="custom-dialog-header">
            <h3>{{ isEditing ? '编辑场景' : '创建场景' }}</h3>
            <button class="custom-dialog-close" @click="closeSceneDialog">×</button>
          </div>
          <div class="custom-dialog-body">
            <el-form :model="sceneForm" label-width="100px" class="scene-form">
              <el-form-item label="场景标题">
                <el-input
                  v-model="sceneForm.title"
                  placeholder="请输入场景标题"
                  ref="sceneTitleInput"
                  @keyup.enter.prevent="saveScene"
                  @keyup.esc.prevent="closeSceneDialog"
                  autofocus
                  class="scene-title-input"
                />
              </el-form-item>
              <el-form-item label="场景描述">
                <el-input
                    v-model="sceneForm.description"
                    type="textarea"
                    :rows="6"
                    resize="none"
                    placeholder="请描述场景内容"
                    class="scene-description-input"
                    @keyup.esc.prevent="closeSceneDialog"
                />
              </el-form-item>
              <el-form-item label="场景标签">
                <el-select
                    v-model="sceneForm.tags"
                    multiple
                    filterable
                    allow-create
                    default-first-option
                    placeholder="请选择或创建标签"
                    class="scene-tags-select"
                >
                  <el-option
                      v-for="tag in availableTags"
                      :key="tag"
                      :label="tag"
                      :value="tag"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div class="custom-dialog-footer">
            <el-button @click="closeSceneDialog" class="cancel-btn">取消</el-button>
            <el-button type="primary" @click="saveScene" class="save-btn">保存场景</el-button>
          </div>
        </div>
      </div>
  
      <!-- 随机抽取结果对话框 -->
      <el-dialog
          v-model="drawDialogVisible"
          title="场景组合"
          width="1200px"
          class="draw-dialog"
      >
        <div class="draw-result">
          <div class="draw-content">
            <!-- 左侧场景展示区域 -->
            <div class="scenes-section">
              <div class="drawn-cards">
                <div v-for="(scene, index) in drawnScenes" :key="scene.id" class="drawn-card-item">
                  <div class="scene-number">场景 {{ index + 1 }}</div>
                  <Card
                      :title="scene.title"
                      :description="scene.description"
                      :tags="scene.tags"
                      class="compact-card"
                  />
                  <div v-if="index < drawnScenes.length - 1" class="scene-connector">
                    <el-icon><Right /></el-icon>
                  </div>
                </div>
              </div>
              <div class="combination-hint">
                <el-icon><Connection /></el-icon>
                <p>思考这些场景之间可能存在的联系...</p>
              </div>
            </div>
            
            <!-- 右侧灵感输入区域 -->
            <div class="inspiration-section">
              <div class="inspiration-input">
                <div class="inspiration-label">记录你的灵感：</div>
                <el-input
                  v-model="currentInspiration.content"
                  type="textarea"
                  :rows="10"
                  placeholder="这些场景之间会发生什么有趣的故事？它们如何推动剧情发展？记录下你的想法..."
                  resize="none"
                />
                <div class="inspiration-meta">
                  <div class="inspiration-rating">
                    <span class="rating-label">灵感评分：</span>
                    <el-slider
                      v-model="currentInspiration.rating"
                      :min="1"
                      :max="100"
                      :format-tooltip="(val) => `${val}分`"
                      show-input
                      :input-size="'small'"
                      class="rating-slider"
                    >
                      <template #marks>
                        <span 
                          v-for="mark in [20, 40, 60, 80, 100]" 
                          :key="mark"
                          class="mark-label"
                        >
                          {{ mark }}
                        </span>
                      </template>
                    </el-slider>
                  </div>
                  <el-checkbox v-model="currentInspiration.isUsed">已使用此灵感</el-checkbox>
                </div>
                <div class="inspiration-actions">
                  <el-button type="primary" @click="saveInspiration" :disabled="!currentInspiration.content.trim()">
                    <el-icon><Check /></el-icon>
                    保存灵感
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-dialog>

      <!-- 历史记录对话框 -->
      <el-dialog
        v-model="historyDialogVisible"
        class="history-dialog"
        :fullscreen="true"
        :show-close="false"
        :modal="true"
        :close-on-click-modal="false"
        :close-on-press-escape="true"
        :lock-scroll="true"
        :destroy-on-close="false"
      >
        <template #header="{ close, titleId, titleClass }">
          <div class="dialog-header-content">
            <h4 :id="titleId" :class="titleClass">
              灵感历史记录
              <span class="history-count">(共 {{ totalInspirations }} 条)</span>
            </h4>
            <el-button class="close-btn" @click="close">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </template>
        <div class="history-container">
          <div class="history-content">
            <!-- 左侧场景列表 -->
            <div class="history-list">
              <div class="history-list-content">
                <template v-if="paginatedHistory.length > 0">
                  <div 
                    v-for="record in paginatedHistory" 
                    :key="record.timestamp" 
                    class="history-item" 
                    :class="{ 'active': currentExpandedRecord === record }" 
                    @click="toggleExpand(record)"
                  >
                    <div class="history-header">
                      <div class="header-main">
                        <div class="history-meta-info">
                          <span class="history-time">{{ formatDate(record.timestamp) }}</span>
                          <el-tag size="small" type="info" class="pool-tag">{{ record.poolName }}</el-tag>
                          <template v-if="record.fromAllPool">
                            <el-tag size="small" type="success" class="pool-tag">全部场景</el-tag>
                            <el-tooltip
                              effect="dark"
                              placement="top"
                            >
                              <template #content>
                                <div class="related-pools-tooltip">
                                  <div class="tooltip-title">关联卡池：</div>
                                  <div v-for="pool in record.relatedPools" :key="pool.name" class="related-pool-item">
                                    {{ pool.name }} ({{ pool.sceneCount }}个场景)
                                  </div>
                                </div>
                              </template>
                              <el-tag size="small" type="warning" class="pool-tag">
                                {{ record.relatedPools.length }}个卡池
                              </el-tag>
                            </el-tooltip>
                          </template>
                        </div>
                        <div class="history-brief">{{ record.content }}</div>
                      </div>
                      <div class="history-meta">
                        <div class="history-tags">
                          <el-tag :type="getRatingType(record.rating)" size="small">
                            {{ record.rating }}分
                          </el-tag>
                          <el-tag 
                            :type="record.isUsed ? 'success' : 'info'"
                            size="small"
                          >
                            {{ record.isUsed ? '已使用' : '未使用' }}
                          </el-tag>
                        </div>
                        <el-button
                          type="danger"
                          size="small"
                          link
                          class="delete-history-btn"
                          @click.stop="confirmDeleteHistory(record)"
                        >
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <el-empty description="暂无灵感记录" />
                </template>
              </div>
            </div>

            <!-- 右侧详情面板 -->
            <div class="history-detail-panel">
              <template v-if="currentExpandedRecord">
                <div class="history-scenes">
                  <div class="scenes-title">
                    场景组合
                    <el-tag size="small" type="info" class="scene-count">
                      {{ currentExpandedRecord.scenes.length }}个场景
                    </el-tag>
                  </div>
                  <div class="scenes-list">
                    <div 
                      v-for="(scene, index) in currentExpandedRecord.scenes" 
                      :key="scene.id" 
                      class="history-scene"
                    >
                      <div class="scene-number">场景 {{ index + 1 }}</div>
                      <div class="scene-title">{{ scene.title }}</div>
                      <div class="scene-description" v-if="scene.description">
                        {{ scene.description }}
                      </div>
                      <div class="scene-tags">
                        <el-tag 
                          v-for="tag in scene.tags" 
                          :key="tag" 
                          size="small"
                          class="scene-tag"
                        >
                          {{ tag }}
                        </el-tag>
                      </div>
                      <div 
                        v-if="index < currentExpandedRecord.scenes.length - 1" 
                        class="scene-connector"
                      >
                        <el-icon><Right /></el-icon>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="history-inspiration">
                  <div class="inspiration-header">
                    <div class="inspiration-title">
                      灵感内容
                      <el-tag :type="getRatingType(currentExpandedRecord.rating)" size="small">
                        {{ currentExpandedRecord.rating }}分
                      </el-tag>
                      <el-tag 
                        :type="currentExpandedRecord.isUsed ? 'success' : 'info'"
                        size="small"
                      >
                        {{ currentExpandedRecord.isUsed ? '已使用' : '未使用' }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="inspiration-content">{{ currentExpandedRecord.content }}</div>
                </div>
              </template>
              <div v-else class="empty-detail">
                <el-empty description="点击左侧记录查看详情" />
              </div>
            </div>
          </div>
          
          <!-- 分页区域 -->
          <div class="history-footer">
            <div class="history-pagination">
              <el-pagination
                :current-page="currentPage"
                :page-size="pageSize"
                :total="totalInspirations"
                :page-sizes="[5,10, 20, 30, 50]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </el-dialog>

      <!-- 创建卡池对话框 -->
      <el-dialog
        v-model="createPoolDialogVisible"
        title="创建场景卡池"
        width="400px"
        @close="handlePoolDialogClose"
      >
        <el-form 
          :model="poolForm" 
          label-width="80px"
          @submit.prevent="handleCreatePool"
          @keyup.enter="handleCreatePool"
        >
          <el-form-item label="卡池名称">
            <el-input 
              v-model="poolForm.name" 
              placeholder="请输入卡池名称"
              ref="poolNameInput"
              @keyup.enter.prevent="handleCreatePool"
              autofocus
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createPoolDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleCreatePool">确定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 导入JSON对话框 -->
      <el-dialog
        v-model="importJsonDialogVisible"
        title="导入JSON"
        width="400px"
        @close="handleImportJsonClose"
      >
        <el-form 
          :model="importJsonForm" 
          label-width="80px"
          @submit.prevent="handleImportJson"
          @keyup.enter="handleImportJson"
        >
          <el-form-item label="JSON内容">
            <el-input 
              v-model="importJsonForm.jsonContent"
              type="textarea"
              :rows="10"
              placeholder="请输入要导入的JSON内容"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="importJsonDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleImportJson">导入</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 融合JSON对话框 -->
      <el-dialog
        v-model="mergeJsonDialogVisible"
        title="融合JSON到当前卡池"
        width="400px"
        @close="handleMergeJsonClose"
      >
        <el-form 
          :model="mergeJsonForm" 
          label-width="80px"
          @submit.prevent="handleMergeJson"
          @keyup.enter="handleMergeJson"
        >
          <el-form-item label="JSON内容">
            <el-input 
              v-model="mergeJsonForm.jsonContent"
              type="textarea"
              :rows="10"
              placeholder="请输入要融合的JSON内容，将添加新场景到当前卡池"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="mergeJsonDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleMergeJson">融合</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 卡池管理对话框 -->
      <el-dialog
        v-model="managePoolsDialogVisible"
        title="管理场景卡池"
        width="600px"
        :close-on-click-modal="false"
        append-to-body
        :modal-append-to-body="false"
        :lock-scroll="true"
        :destroy-on-close="false"
        class="manage-pools-dialog"
        @open="handleManagePoolsOpen"
        @close="handleManagePoolsClose"
      >
        <div class="manage-pools-container">
          <div class="pools-toolbar">
            <div class="search-wrapper">
              <el-input
                v-model="poolSearchQuery"
                placeholder="搜索卡池名称..."
                clearable
                prefix-icon="Search"
                @input="handlePoolSearch"
              />
            </div>
            <div class="pools-actions">
              <el-button 
                @click="scrollToTop" 
                class="scroll-top-btn"
                type="default"
              >
                <el-icon><Top /></el-icon>
                <span>返回顶部</span>
              </el-button>
            </div>
          </div>
          
          <p class="manage-pools-hint">拖拽卡池可调整显示顺序，或点击置顶按钮将卡池移到最前</p>
          
          <el-scrollbar height="400px" ref="poolsScrollbar" class="pools-scrollbar">
            <el-empty v-if="filteredPools.length === 0" description="暂无卡池" />
            <draggable 
              v-else
              v-model="sceneData.pools" 
              item-key="id"
              ghost-class="ghost-pool"
              handle=".drag-handle"
              @end="savePoolOrder"
            >
              <template #item="{element, index}">
                <div class="pool-item" :class="{'is-searched': isPoolMatch(element)}">
                  <el-icon class="drag-handle"><Rank /></el-icon>
                  <div class="pool-item-content">
                    <div class="pool-item-name">{{ element.name }}</div>
                    <div class="pool-item-info">
                      <el-tag size="small" class="scene-count-tag">{{ element.scenes.length }}个场景</el-tag>
                      <span class="update-time">{{ formatDate(element.updateTime) }}</span>
                    </div>
                  </div>
                  <div class="pool-item-actions">
                    <el-tooltip content="置顶此卡池" placement="top" :enterable="false">
                      <el-button
                        type="info"
                        link
                        @click="movePoolToTop(index)"
                        class="top-pool-btn"
                        :disabled="index === 0"
                      >
                        <el-icon><Top /></el-icon>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip content="选择此卡池" placement="top" :enterable="false">
                      <el-button
                        type="primary"
                        link
                        @click="selectPool(element)"
                        class="select-pool-btn"
                      >
                        <el-icon><Check /></el-icon>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip content="重命名" placement="top" :enterable="false">
                      <el-button
                        type="warning"
                        link
                        @click="showRenamePoolDialog(element)"
                        class="rename-pool-btn"
                      >
                        <el-icon><Edit /></el-icon>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip content="删除" placement="top" :enterable="false">
                      <el-button
                        v-if="sceneData.pools.length > 1"
                        type="danger"
                        link
                        @click="confirmDeletePool(element)"
                        class="delete-pool-btn"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </el-tooltip>
                  </div>
                </div>
              </template>
            </draggable>
          </el-scrollbar>
        </div>
        
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="managePoolsDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="managePoolsDialogVisible = false">完成</el-button>
          </span>
        </template>
      </el-dialog>
      
      <!-- 修复重命名卡池对话框 -->
      <el-dialog
        v-model="renamePoolDialogVisible"
        title="重命名卡池"
        width="400px"
        append-to-body
        :close-on-click-modal="false"
        :lock-scroll="true"
        class="rename-pool-dialog"
        @close="handleRenamePoolClose"
      >
        <el-form 
          :model="renamePoolForm" 
          label-width="80px"
          @submit.prevent="handleRenamePool"
        >
          <el-form-item label="卡池名称">
            <el-input 
              v-model="renamePoolForm.name" 
              placeholder="请输入卡池名称"
              ref="renamePoolInput"
              autofocus
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="renamePoolDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleRenamePool">确认</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 全屏场景画布 -->
      <FullscreenSceneCanvas
        :visible="fullscreenCanvasVisible"
        @update:visible="fullscreenCanvasVisible = $event"
        :scenes="scenes"
        :book-title="selectedBookTitle"
        :current-pool="currentPool"
        @create-scene="showCreateDialog"
        @delete-scene="confirmDeleteScene"
        @save-scenes="saveSceneData"
      />
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'

  import { ElMessage, ElMessageBox } from 'element-plus'
  import { useBookStore } from '@/stores/book'
  import Card from './Card.vue'
  import FullscreenSceneCanvas from '@/components/FullscreenSceneCanvas.vue'
  import {
    Delete,
    Plus,
    Check,
    Edit,
    Setting,
    Rank,
    Top,
    Search,
    ZoomIn,
    ZoomOut,
    Refresh,
    Download,
    Upload,
    ArrowDown,
    FolderOpened,
    CopyDocument,
    FolderAdd,
    DocumentAdd,
    Connection,
    Timer,
    FullScreen,  // 添加全屏图标
    Close,       // 添加关闭图标用于退出全屏
    Grid         // 添加网格图标用于自动排版
  } from '@element-plus/icons-vue'
  import draggable from 'vuedraggable'
  const bookStore = useBookStore()
  // 拖拽状态
  const gridRef = ref(null)
  const draggingScene = ref(null)
  const dragOffset = reactive({ x: 0, y: 0 })
  const dragStartPos = reactive({ x: 0, y: 0 })
  const isDragging = ref(false)
  
  // 添加最大z-index追踪
  const maxZIndex = ref(1)

  // 更新卡片层级的方法
  const updateCardZIndex = (scene) => {
    // 如果传入的是ID，则先查找对应场景
    let targetScene = scene;
    if (typeof scene === 'string') {
      targetScene = scenes.value.find(s => s.id === scene);
    }
    
    if (!targetScene) return;
    
    // 找出当前最高的zIndex
    maxZIndex.value = Math.max(maxZIndex.value, ...scenes.value.map(s => s.zIndex || 0));
    
    // 将当前卡片设置为最高zIndex + 1
    maxZIndex.value++;
    targetScene.zIndex = maxZIndex.value;
    
    // 保存到存储
    saveSceneData();
  }

  // 场景卡池数据
  const sceneData = ref({
    pools: [],
    currentPoolId: null
  })

  // 当前卡池
  const currentPool = computed(() => {
    if (sceneData.value.currentPoolId === 'all') {
      return allScenesPool.value
    }
    return sceneData.value.pools.find(pool => pool.id === sceneData.value.currentPoolId) || null
  })

  // 添加一个新的计算属性，用于合并所有卡池的场景
  const allScenesPool = computed(() => {
    const allScenes = sceneData.value.pools.reduce((acc, pool) => {
      // 深拷贝场景，以避免引用问题
      const poolScenes = JSON.parse(JSON.stringify(pool.scenes || []))
      // 为每个场景添加来源卡池信息
      poolScenes.forEach(scene => {
        scene.sourcePool = {
          id: pool.id,
          name: pool.name
        }
      })
      return [...acc, ...poolScenes]
    }, [])

    return {
      id: 'all',
      name: '全部场景',
      scenes: allScenes,
      isVirtual: true, // 标记这是一个虚拟卡池
      createTime: Date.now(),
      updateTime: Date.now()
    }
  })

  // 场景列表
  const scenes = computed({
    get: () => currentPool.value?.scenes || [],
    set: (newScenes) => {
      if (currentPool.value && !currentPool.value.isVirtual) {
        currentPool.value.scenes = newScenes
        saveSceneData()
      }
    }
  })

  // 抽取数量
  const drawCount = ref(2)
  
  // 选中的书籍ID
  const selectedBookId = ref('')

  // 选中书籍的标题
  const selectedBookTitle = computed(() => {
    const book = bookStore.bookList?.find(b => b.id === selectedBookId.value)
    return book?.title || ''
  })

  // 在组件挂载时加载数据
  onMounted(async () => {
    try {
      // 确保书籍列表已加载
      await bookStore.loadBooks()
      // 如果有书籍列表，默认选择第一本书
      if (bookStore.bookList?.length > 0) {
        selectedBookId.value = bookStore.bookList[0].id
        await loadScene()
      }
    } catch (error) {
      console.error('初始化失败:', error)
      ElMessage.error('初始化失败，请刷新页面重试')
    }
  })

  // 当前展开的记录
  const currentExpandedRecord = ref(null)

  // 灵感历史记录
  const inspirationHistory = computed(() => {
    // 收集所有卡池的灵感记录
    const allInspirations = sceneData.value.pools.reduce((acc, pool) => {
      if (Array.isArray(pool.inspirations)) {
        // 为每条灵感添加所属卡池信息
        const poolInspirations = pool.inspirations.map(inspiration => {
          // 如果是来自全部场景卡池的灵感，使用主卡池信息
          if (inspiration.isFromAllPool) {
            return {
              ...inspiration,
              poolName: inspiration.mainPoolName || pool.name,
              poolId: inspiration.mainPoolId || pool.id,
              fromAllPool: true,
              relatedPools: inspiration.relatedPools || []
            }
          }
          // 普通灵感
          return {
            ...inspiration,
            poolName: pool.name,
            poolId: pool.id,
            fromAllPool: false,
            relatedPools: []
          }
        })
        acc.push(...poolInspirations)
      }
      return acc
    }, [])

    // 按时间戳降序排序
    return allInspirations.sort((a, b) => b.timestamp - a.timestamp)
  })

  // 加载场景数据
  const loadScene = async () => {
    if (!selectedBookId.value) return

    try {
      const response = await window.pywebview.api.book_controller.get_scene_events(selectedBookId.value)
      const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response

      if (parsedResponse.status === "success") {
        // 确保数据结构完整
        const data = parsedResponse.data || {}
        sceneData.value = {
          pools: Array.isArray(data.pools) ? data.pools : [],
          currentPoolId: data.currentPoolId || null
        }

        // 初始化最大zIndex
        maxZIndex.value = 1
        // 遍历所有卡池中的场景，找出最大的zIndex
        sceneData.value.pools.forEach(pool => {
          if (Array.isArray(pool.scenes)) {
            pool.scenes.forEach(scene => {
              // 确保每个场景都有zIndex属性
              if (!scene.zIndex) {
                scene.zIndex = maxZIndex.value
                maxZIndex.value++
              } else {
                // 更新最大zIndex
                maxZIndex.value = Math.max(maxZIndex.value, scene.zIndex)
              }
            })
          }
        })

        // 如果没有当前卡池，创建一个默认卡池
        if (!sceneData.value.pools.length) {
          createNewPool('默认卡池')
        }
        // 如果没有选择当前卡池，选择第一个
        if (!sceneData.value.currentPoolId && sceneData.value.pools.length > 0) {
          sceneData.value.currentPoolId = sceneData.value.pools[0].id
        }
      } else {
        throw new Error(parsedResponse.message || '加载失败')
      }
    } catch (error) {
      console.error('加载场景失败:', error)
      ElMessage.error(`加载场景失败: ${error.message}`)
      // 重置数据为有效的初始状态
      sceneData.value = { pools: [], currentPoolId: null }
      maxZIndex.value = 1
    }
  }

  // 保存场景数据
  const saveSceneData = async () => {
    // 确保数据结构完整
    if (!Array.isArray(sceneData.value.pools)) {
      sceneData.value.pools = []
    }

    try {
      const response = await window.pywebview.api.book_controller.save_scene_events(
        selectedBookId.value, 
        JSON.parse(JSON.stringify(sceneData.value)) // 深拷贝防止循环引用
      )
      const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response

      if (parsedResponse.status === "success") {
        return true
      } else {
        throw new Error(parsedResponse.message || '保存失败')
      }
    } catch (error) {
      console.error('保存场景数据失败:', error)
      throw error
    }
  }

  // 创建新卡池
  const createNewPool = (name) => {
    // 确保 pools 数组存在
    if (!Array.isArray(sceneData.value.pools)) {
      sceneData.value.pools = []
    }

    const newPool = {
      id: Date.now().toString(),
      name,
      createTime: Date.now(),
      updateTime: Date.now(),
      scenes: [],
      inspirations: []
    }
    sceneData.value.pools.push(newPool)
    sceneData.value.currentPoolId = newPool.id
    saveSceneData()
  }

  // 切换卡池
  const switchPool = (poolId) => {
    sceneData.value.currentPoolId = poolId
    saveSceneData()
  }

  // 开始拖拽
  const startDrag = (event, scene) => {
    // 如果是从按钮触发的，不启动拖拽
    if (event.target.closest('.el-button')) {
      return
    }

    // 如果是在全部场景卡池中，不允许拖拽
    if (currentPool.value?.isVirtual) {
      ElMessage.warning('不能在"全部场景"卡池中移动场景')
      return
    }

    draggingScene.value = scene
    const rect = event.target.getBoundingClientRect()
    dragOffset.x = event.clientX - rect.left
    dragOffset.y = event.clientY - rect.top
    
    // 记录开始拖拽的位置
    dragStartPos.x = event.clientX
    dragStartPos.y = event.clientY
    isDragging.value = false
    
    // 更新被拖拽卡片的层级
    updateCardZIndex(scene)
    
    document.addEventListener('mousemove', handleDrag)
    document.addEventListener('mouseup', stopDrag)
  }
  
  // 处理拖拽
  const handleDrag = (event) => {
    if (!draggingScene.value || !gridRef.value) return
    
    // 计算移动距离
    const moveDistance = Math.sqrt(
      Math.pow(event.clientX - dragStartPos.x, 2) + 
      Math.pow(event.clientY - dragStartPos.y, 2)
    )
    
    // 如果移动距离大于5像素，认为是拖拽而不是点击
    if (moveDistance > 5) {
      isDragging.value = true
    }
    
    const gridRect = gridRef.value.getBoundingClientRect()
    const scrollTop = gridRef.value.scrollTop
    
    draggingScene.value.position = {
      left: event.clientX - gridRect.left - dragOffset.x,
      top: event.clientY - gridRect.top - dragOffset.y + scrollTop
    }
  }
  
  // 停止拖拽
  const stopDrag = () => {
    if (draggingScene.value && isDragging.value) {
      saveSceneData()
    }
    draggingScene.value = null
    isDragging.value = false
    document.removeEventListener('mousemove', handleDrag)
    document.removeEventListener('mouseup', stopDrag)
  }
  
  // 组件卸载时清理事件监听
  onUnmounted(() => {
    document.removeEventListener('mousemove', handleDrag)
    document.removeEventListener('mouseup', stopDrag)
  })
  
  // 可用标签
  const availableTags = ref([
    '战斗', '对话', '探索', '追逐', '相遇',
    '告别', '冲突', '和解', '发现', '选择'
  ])
  
  // 对话框控制
  const dialogVisible = ref(false)
  const drawDialogVisible = ref(false)
  const isEditing = ref(false)
  
  // 表单数据
  const sceneForm = reactive({
    id: '',
    title: '',
    description: '',
    tags: []
  })
  
  // 抽取的场景
  const drawnScenes = ref([])
  
  // 当前灵感
  const currentInspiration = ref({
    content: '',
    rating: 80, // 默认80分
    isUsed: false
  })
  // 历史记录对话框控制
  const historyDialogVisible = ref(false)

  // 全屏画布对话框控制
  const fullscreenCanvasVisible = ref(false)
  
  // 显示创建对话框
  const showCreateDialog = () => {
    // 如果是虚拟卡池（全部场景），不允许创建
    if (currentPool.value?.isVirtual) {
      ElMessage.warning('不能在"全部场景"卡池中创建场景')
      return
    }
    isEditing.value = false
    sceneForm.id = ''
    sceneForm.title = ''
    sceneForm.description = ''
    sceneForm.tags = []
    dialogVisible.value = true
    // 延迟一帧后聚焦输入框
    setTimeout(() => {
      const input = document.querySelector('.custom-dialog input')
      if (input) {
        input.focus()
      }
    }, 0)
  }
  
  // 编辑场景
  const editScene = (event, scene) => {
    // 如果正在拖拽，不触发编辑
    if (isDragging.value) return
    
    // 如果是在全部场景卡池中，需要切换到源卡池
    if (currentPool.value?.isVirtual && scene.sourcePool) {
      sceneData.value.currentPoolId = scene.sourcePool.id
      ElMessage.success(`已切换到场景所在卡池：${scene.sourcePool.name}`)
      // 延迟一帧后再打开编辑对话框
      setTimeout(() => {
        const targetScene = scenes.value.find(s => s.id === scene.id)
        if (targetScene) {
          isEditing.value = true
          sceneForm.id = targetScene.id
          sceneForm.title = targetScene.title
          sceneForm.description = targetScene.description
          sceneForm.tags = [...targetScene.tags]
          dialogVisible.value = true
          // 延迟一帧后聚焦输入框
          setTimeout(() => {
            const input = document.querySelector('.custom-dialog input')
            if (input) {
              input.focus()
            }
          }, 0)
        }
      }, 0)
      return
    }
    
    isEditing.value = true
    sceneForm.id = scene.id
    sceneForm.title = scene.title
    sceneForm.description = scene.description
    sceneForm.tags = [...scene.tags]
    dialogVisible.value = true
    // 延迟一帧后聚焦输入框
    setTimeout(() => {
      const input = document.querySelector('.custom-dialog input')
      if (input) {
        input.focus()
      }
    }, 0)
  }
  
  // 删除场景
  const deleteScene = (scene) => {
    // 如果是在全部场景卡池中，需要切换到源卡池
    if (currentPool.value?.isVirtual && scene.sourcePool) {
      sceneData.value.currentPoolId = scene.sourcePool.id
      ElMessage.success(`已切换到场景所在卡池：${scene.sourcePool.name}`)
      // 延迟一帧后再执行删除
      setTimeout(() => {
        const targetScene = scenes.value.find(s => s.id === scene.id)
        if (targetScene) {
          confirmDeleteScene(targetScene)
        }
      }, 0)
      return
    }
    confirmDeleteScene(scene)
  }
  
  // 添加确认删除场景的方法
  const confirmDeleteScene = (scene) => {
    ElMessageBox.confirm(
      '确定要删除这个场景吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        // 添加以下配置，指定弹窗挂载到全屏元素上
        appendTo: cardsGridRef.value || document.body
      }
    ).then(async () => {
      const index = scenes.value.findIndex(s => s.id === scene.id)
      if (index !== -1) {
        scenes.value.splice(index, 1)
        try {
          await saveSceneData()
          ElMessage.success('场景已删除')
        } catch (error) {
          ElMessage.error('删除失败，请重试')
        }
      }
    }).catch(() => {})
  }
  
  // 保存场景
  const saveScene = async () => {
    if (!sceneForm.title.trim()) {
      ElMessage.warning('请输入场景标题')
      return
    }

    if (isEditing.value) {
      // 编辑现有场景 - 只更新需要修改的字段，保留位置信息
      const index = scenes.value.findIndex(s => s.id === sceneForm.id)
      if (index !== -1) {
        const existingScene = scenes.value[index]
        // 只更新标题、描述和标签，保留所有位置和其他信息
        existingScene.title = sceneForm.title
        existingScene.description = sceneForm.description
        existingScene.tags = sceneForm.tags
      }
    } else {
      // 创建新场景
      const newScene = {
        id: Date.now().toString(),
        title: sceneForm.title,
        description: sceneForm.description,
        tags: sceneForm.tags,
        x: Math.random() * 500,
        y: Math.random() * 300,
        zIndex: maxZIndex.value + 1
      }
      scenes.value.push(newScene)
      maxZIndex.value++ // 更新最大层级
    }

    // 保存更改
    try {
      await saveSceneData()
      dialogVisible.value = false
      ElMessage.success(isEditing.value ? '场景已更新' : '场景已创建')
    } catch (error) {
      ElMessage.error('保存失败，请重试')
    }
  }
  
  // 格式化日期
  const formatDate = (timestamp) => {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }
  
  // 保存灵感
  const saveInspiration = async () => {
    if (!currentInspiration.value.content.trim()) return

    const inspiration = {
      timestamp: Date.now(),
      scenes: drawnScenes.value,
      content: currentInspiration.value.content,
      rating: currentInspiration.value.rating,
      isUsed: currentInspiration.value.isUsed
    }

    // 如果是在全部场景卡池中，需要将灵感保存到一个主卡池中
    if (currentPool.value?.isVirtual) {
      // 按源卡池分组场景
      const scenesByPool = drawnScenes.value.reduce((acc, scene) => {
        if (scene.sourcePool) {
          if (!acc[scene.sourcePool.id]) {
            acc[scene.sourcePool.id] = {
              poolName: scene.sourcePool.name,
              scenes: []
            }
          }
          acc[scene.sourcePool.id].scenes.push(scene)
        }
        return acc
      }, {})

      // 获取所有相关的卡池信息
      const relatedPools = Object.values(scenesByPool).map(p => ({
        name: p.poolName,
        sceneCount: p.scenes.length
      }))

      // 选择第一个场景的源卡池作为主卡池
      const firstScene = drawnScenes.value[0]
      if (firstScene?.sourcePool) {
        const mainPool = sceneData.value.pools.find(p => p.id === firstScene.sourcePool.id)
        if (mainPool) {
          const allPoolInspiration = {
            ...inspiration,
            isFromAllPool: true, // 标记这个灵感来自全部场景卡池
            relatedPools, // 记录所有相关卡池信息
            mainPoolId: mainPool.id, // 记录主卡池ID
            mainPoolName: mainPool.name // 记录主卡池名称
          }
          
          if (!Array.isArray(mainPool.inspirations)) {
            mainPool.inspirations = []
          }
          mainPool.inspirations.unshift(allPoolInspiration)
          mainPool.updateTime = Date.now()
        }
      }
    } else {
      // 原有的保存逻辑
      if (!currentPool.value) return
      if (!Array.isArray(currentPool.value.inspirations)) {
        currentPool.value.inspirations = []
      }
      currentPool.value.inspirations.unshift(inspiration)
      currentPool.value.updateTime = Date.now()
    }
    
    if (await saveSceneData()) {
      ElMessage.success('灵感已保存')
      drawDialogVisible.value = false
      // 重置当前灵感
      currentInspiration.value = {
        content: '',
        rating: 80, // 默认80分
        isUsed: false
      }
    }
  }
  
  // 修改随机抽取函数，清空当前灵感
  const randomDraw = () => {
    if (scenes.value.length < drawCount.value) {
      ElMessage.warning(`需要至少${drawCount.value}个场景才能进行随机抽取`)
      return
    }

    const shuffled = [...scenes.value].sort(() => 0.5 - Math.random())
    drawnScenes.value = shuffled.slice(0, drawCount.value)
    // 重置当前灵感
    currentInspiration.value = {
      content: '',
      rating: 80, // 默认80分
      isUsed: false
    }
    drawDialogVisible.value = true
  }

  // 全屏相关方法
  const openFullscreenCanvas = () => {
    if (!scenes.value.length) {
      ElMessage.warning('暂无场景卡片，请先创建场景')
      return
    }
    fullscreenCanvasVisible.value = true
  }

  const editSceneFromFullscreen = (scene) => {
    // 现在编辑逻辑在全屏组件内部处理，这个方法可能不再需要
    // 但保留以防其他地方调用
    console.log('editSceneFromFullscreen called, but editing is now handled in fullscreen component')
  }
  
  const fileInput = ref(null)
  
  // 导出场景数据
  const exportScenes = async () => {
    try {
      const dataStr = JSON.stringify(sceneData.value, null, 2)
      const response = await window.pywebview.api.book_controller.export_scenes(dataStr)
      const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response
      
      if (parsedResponse.status === "success") {
        ElMessage.success('导出成功')
      } else {
        ElMessage.error(`导出失败: ${parsedResponse.message}`)
      }
    } catch (error) {
      ElMessage.error(`导出出错: ${error.message}`)
    }
  }
  
  // 触发文件选择
  const triggerImport = () => {
    fileInput.value.click()
  }
  
  // 导入场景数据
  const importScenes = async (event) => {
    const file = event.target.files[0]
    if (!file) return
  
    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        const importData = JSON.parse(e.target.result)
        // 验证数据结构
        if (!Array.isArray(importData.pools)) {
          throw new Error('无效的场景数据格式')
        }
        
        ElMessageBox.confirm(
          '导入将覆盖当前场景数据，是否继续？',
          '导入确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            // 添加挂载点配置
            appendTo: cardsGridRef.value || document.body
          }
        ).then(async () => {
          // 处理导入数据中的zIndex
          let maxImportZIndex = 1
          importData.pools.forEach(pool => {
            if (Array.isArray(pool.scenes)) {
              pool.scenes.forEach(scene => {
                if (!scene.zIndex) {
                  scene.zIndex = maxImportZIndex++
                } else {
                  maxImportZIndex = Math.max(maxImportZIndex, scene.zIndex + 1)
                }
              })
            }
          })
          
          sceneData.value = importData
          maxZIndex.value = maxImportZIndex
          
          try {
            await saveSceneData()
            ElMessage.success('场景数据导入成功')
          } catch (error) {
            ElMessage.error('保存失败，请重试')
            // 如果保存失败，重新加载原有数据
            await loadScene()
          }
        }).catch(() => {})
      } catch (error) {
        ElMessage.error(`导入失败：${error.message}`)
      }
      event.target.value = ''
    }
    reader.readAsText(file)
  }

  // 卡池表单数据
  const poolForm = reactive({
    name: ''
  })

  // 创建卡池对话框控制
  const createPoolDialogVisible = ref(false)

  // 显示创建卡池对话框
  const showCreatePoolDialog = (e) => {
    e.stopPropagation()
    // 先移除下拉框的激活状态
    const poolSelect = document.querySelector('.pool-select input')
    if (poolSelect) {
      poolSelect.blur()
    }
    // 延迟一帧后再打开弹窗，确保blur生效
    setTimeout(() => {
      poolForm.name = ''
      createPoolDialogVisible.value = true
      // 延迟一帧后聚焦输入框
      setTimeout(() => {
        const input = document.querySelector('.el-dialog input')
        if (input) {
          input.focus()
        }
      }, 0)
    }, 0)
  }

  // 处理创建卡池
  const handleCreatePool = () => {
    if (!poolForm.name.trim()) {
      ElMessage.warning('请输入卡池名称')
      return
    }
    createNewPool(poolForm.name.trim())
    createPoolDialogVisible.value = false
    ElMessage.success('卡池创建成功')
  }

  // 处理书籍变化
  const handleBookChange = async () => {
    // 重置为有效的初始状态
    sceneData.value = { pools: [], currentPoolId: null }
    if (!selectedBookId.value) return
    
    try {
      await loadScene()
    } catch (error) {
      console.error('加载场景失败:', error)
      ElMessage.error(`加载场景失败: ${error.message}`)
    }
  }

  // 分页相关
  const currentPage = ref(1)
  const pageSize = ref(5)
  const totalInspirations = computed(() => inspirationHistory.value.length)

  // 分页后的历史记录
  const paginatedHistory = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return inspirationHistory.value.slice(start, end)
  })

  // 处理分页变化
  const handleSizeChange = (val) => {
    pageSize.value = val
    currentPage.value = 1
    currentExpandedRecord.value = null
  }

  const handleCurrentChange = (val) => {
    currentPage.value = val
    currentExpandedRecord.value = null
  }

  // 展开/收起详情
  const toggleExpand = (record) => {
    currentExpandedRecord.value = currentExpandedRecord.value === record ? null : record
  }

  // 监听对话框关闭
  watch(historyDialogVisible, (newVal) => {
    if (!newVal) {
      currentExpandedRecord.value = null
      currentPage.value = 1
    }
  })

  // 监听书籍或卡池变化
  watch([selectedBookId, () => sceneData.value.currentPoolId], () => {
    currentExpandedRecord.value = null
    currentPage.value = 1
  })

  // 获取评分对应的标签类型
  const getRatingType = (rating) => {
    if (rating >= 90) return 'danger'
    if (rating >= 80) return 'success'
    if (rating >= 60) return 'warning'
    return 'info'
  }

  // 确认删除卡池
  const confirmDeletePool = (pool) => {
    ElMessageBox.confirm(
      `确定要删除卡池"${pool.name}"吗？此操作将永久删除该卡池及其所有场景和灵感记录。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
        // 添加以下配置，指定弹窗挂载到全屏元素上
        appendTo: cardsGridRef.value || document.body
      }
    ).then(() => {
      deletePool(pool)
    }).catch(() => {})
  }

  // 删除卡池
  const deletePool = (pool) => {
    const index = sceneData.value.pools.findIndex(p => p.id === pool.id)
    if (index !== -1) {
      sceneData.value.pools.splice(index, 1)
      
      // 如果删除的是当前选中的卡池，切换到第一个卡池
      if (pool.id === sceneData.value.currentPoolId) {
        sceneData.value.currentPoolId = sceneData.value.pools[0]?.id || null
      }
      
      saveSceneData().then(() => {
        ElMessage.success('卡池删除成功')
      })
    }
  }

  // 确认删除历史记录
  const confirmDeleteHistory = (record) => {
    ElMessageBox.confirm(
      '确定要删除这条灵感记录吗？此操作不可恢复。',
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
        // 添加以下配置，指定弹窗挂载到全屏元素上
        appendTo: cardsGridRef.value || document.body
      }
    ).then(() => {
      deleteHistory(record)
    }).catch(() => {})
  }

  // 删除历史记录
  const deleteHistory = (record) => {
    // 找到对应的卡池
    const pool = sceneData.value.pools.find(p => p.id === record.poolId)
    if (pool && Array.isArray(pool.inspirations)) {
      // 找到并删除对应的灵感记录
      const index = pool.inspirations.findIndex(i => i.timestamp === record.timestamp)
      if (index !== -1) {
        pool.inspirations.splice(index, 1)
        // 更新卡池的更新时间
        pool.updateTime = Date.now()
        
        // 保存更改
        saveSceneData().then(() => {
          ElMessage.success('灵感记录已删除')
          // 如果删除的是当前展开的记录，清空展开状态
          if (currentExpandedRecord.value === record) {
            currentExpandedRecord.value = null
          }
        }).catch(() => {
          ElMessage.error('删除失败，请重试')
        })
      }
    }
  }

  // 处理卡池对话框关闭
  const handlePoolDialogClose = () => {
    // 重置表单数据
    poolForm.name = ''
  }

  // 处理场景对话框关闭
  const handleSceneDialogClose = () => {
    // 重置表单数据
    sceneForm.id = ''
    sceneForm.title = ''
    sceneForm.description = ''
    sceneForm.tags = []
  }

  // 关闭场景对话框
  const closeSceneDialog = () => {
    dialogVisible.value = false
    handleSceneDialogClose()
  }

  // 处理场景对话框遮罩层点击
  const handleSceneDialogOverlayClick = (event) => {
    // 只有点击遮罩层本身时才关闭对话框
    if (event.target === event.currentTarget) {
      closeSceneDialog()
    }
  }

  // 导出命令处理
  const handleExportCommand = async (command) => {
    try {
      if (command === 'exportAll') {
        await exportScenes()
      } else if (command === 'exportPool') {
        await exportCurrentPool()
      }
    } catch (error) {
      ElMessage.error(`导出失败: ${error.message}`)
    }
  }

  // 导出当前卡池
  const exportCurrentPool = async () => {
    if (!currentPool.value || currentPool.value.isVirtual) {
      ElMessage.warning('请选择一个有效的卡池')
      return
    }

    try {
      const poolData = {
        id: currentPool.value.id,
        name: currentPool.value.name,
        scenes: currentPool.value.scenes,
        createTime: currentPool.value.createTime,
        updateTime: currentPool.value.updateTime
      }
      
      // 复制到剪贴板
      await window.pywebview.api.copy_to_clipboard(JSON.stringify(poolData, null, 2))
      ElMessage.success('卡池数据已复制到剪贴板')
    } catch (error) {
      console.error('导出卡池失败:', error)
      ElMessage.error('导出失败，请重试')
    }
  }

  // 导入命令处理
  const handleImportCommand = (command) => {
    if (command === 'importFile') {
      triggerImport()
    } else if (command === 'importJson') {
      showImportJsonDialog()
    } else if (command === 'mergeJson') {
      showMergeJsonDialog()
    }
  }

  // 导入JSON对话框控制
  const importJsonDialogVisible = ref(false)
  const importJsonForm = ref({
    jsonContent: ''
  })

  // 显示导入JSON对话框
  const showImportJsonDialog = () => {
    importJsonForm.value.jsonContent = ''
    importJsonDialogVisible.value = true
  }

  // 处理JSON导入
  const handleImportJson = async () => {
    if (!importJsonForm.value.jsonContent.trim()) {
      ElMessage.warning('请输入要导入的JSON数据')
      return
    }

    try {
      const importData = JSON.parse(importJsonForm.value.jsonContent)
      
      // 验证数据结构
      if (!importData.scenes || !Array.isArray(importData.scenes)) {
        throw new Error('无效的场景数据格式')
      }

      // 先保存JSON内容并关闭输入弹窗
      const jsonContentBackup = importJsonForm.value.jsonContent
      importJsonDialogVisible.value = false

      try {
        // 确认导入
        await ElMessageBox.confirm(
          '导入将覆盖当前卡池的场景数据，是否继续？',
          '导入确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            // 添加挂载点配置
            appendTo: cardsGridRef.value || document.body
          }
        )

        // 更新当前卡池的场景
        if (currentPool.value && !currentPool.value.isVirtual) {
          currentPool.value.scenes = importData.scenes
          currentPool.value.updateTime = Date.now()
          
          await saveSceneData()
          ElMessage.success('场景数据导入成功')
        }
      } catch (cancelError) {
        // 用户取消操作，重新打开输入弹窗并恢复内容
        importJsonForm.value.jsonContent = jsonContentBackup
        importJsonDialogVisible.value = true
      }
    } catch (error) {
      console.error('导入JSON失败:', error)
      ElMessage.error(`导入失败：${error.message}`)
    }
  }

  // 处理导入JSON对话框关闭
  const handleImportJsonClose = () => {
    // 重置表单数据
    importJsonForm.value.jsonContent = ''
  }

  // 融合JSON对话框控制
  const mergeJsonDialogVisible = ref(false)
  const mergeJsonForm = ref({
    jsonContent: ''
  })

  // 显示融合JSON对话框
  const showMergeJsonDialog = () => {
    mergeJsonForm.value.jsonContent = ''
    mergeJsonDialogVisible.value = true
  }

  // 处理JSON融合
  const handleMergeJson = async () => {
    if (!mergeJsonForm.value.jsonContent.trim()) {
      ElMessage.warning('请输入要融合的JSON数据')
      return
    }

    try {
      const importData = JSON.parse(mergeJsonForm.value.jsonContent)
      
      // 验证数据结构
      if (!importData.scenes || !Array.isArray(importData.scenes)) {
        throw new Error('无效的场景数据格式')
      }

      // 先保存JSON内容并关闭输入弹窗
      const jsonContentBackup = mergeJsonForm.value.jsonContent
      mergeJsonDialogVisible.value = false

      try {
        // 确认融合
        await ElMessageBox.confirm(
          '导入将添加新场景到当前卡池，可能会根据ID去除重复场景。是否继续？',
          '融合确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            // 添加挂载点配置
            appendTo: cardsGridRef.value || document.body
          }
        )

        // 融合到当前卡池
        if (currentPool.value && !currentPool.value.isVirtual) {
          // 确保当前卡池的scenes是数组
          if (!Array.isArray(currentPool.value.scenes)) {
            currentPool.value.scenes = []
          }
          
          // 获取当前场景的ID列表，用于去重
          const existingSceneIds = currentPool.value.scenes.map(scene => scene.id)
          
          // 过滤出不重复的场景
          const newScenes = importData.scenes.filter(scene => !existingSceneIds.includes(scene.id))
          
          // 添加到当前卡池
          if (newScenes.length > 0) {
            // 设置新场景的位置和层级
            newScenes.forEach(scene => {
              // 如果没有位置，随机设置
              if (!scene.position) {
                scene.position = {
                  left: Math.random() * (800 - 240),
                  top: Math.random() * (600 - 280)
                }
              }
              
              // 设置新的z-index
              maxZIndex.value++
              scene.zIndex = maxZIndex.value
            })
            
            // 添加到当前场景列表
            currentPool.value.scenes = [...currentPool.value.scenes, ...newScenes]
            currentPool.value.updateTime = Date.now()
            
            await saveSceneData()
            ElMessage.success(`成功融合 ${newScenes.length} 个新场景到当前卡池`)
          } else {
            ElMessage.info('没有新的场景需要融合，所有场景ID已存在')
          }
        }
      } catch (cancelError) {
        // 用户取消操作，重新打开输入弹窗并恢复内容
        mergeJsonForm.value.jsonContent = jsonContentBackup
        mergeJsonDialogVisible.value = true
      }
    } catch (error) {
      console.error('融合JSON失败:', error)
      ElMessage.error(`融合失败：${error.message}`)
    }
  }

  // 处理融合JSON对话框关闭
  const handleMergeJsonClose = () => {
    // 重置表单数据
    mergeJsonForm.value.jsonContent = ''
  }

  // 添加排序后的卡池计算属性
  const sortedPools = computed(() => {
    return [...sceneData.value.pools] || []
  })

  // 卡池管理对话框控制
  const managePoolsDialogVisible = ref(false)

  // 修改显示管理对话框方法
  const showManagePoolsDialog = (e) => {
    e.stopPropagation()
    e.preventDefault()
    
    // 强制关闭下拉菜单
    const selectDropdown = document.querySelector('.el-select__popper')
    if (selectDropdown) {
      // 添加隐藏类并移除显示类
      selectDropdown.classList.add('el-popper--hidden')
      selectDropdown.classList.remove('el-popper--visible')
    }
    
    // 获取select元素并移除focus状态
    const poolSelect = document.querySelector('.pool-select .el-select')
    if (poolSelect) {
      poolSelect.classList.remove('is-focus')
      const input = poolSelect.querySelector('input')
      if (input) input.blur()
    }
    
    // 延迟打开对话框确保下拉已完全关闭
    setTimeout(() => {
      managePoolsDialogVisible.value = true
    }, 50)
  }

  // 添加对话框打开处理函数
  const handleManagePoolsOpen = () => {
    // 禁止body滚动
    document.body.style.overflow = 'hidden'
    
    // 确保对话框内的滚动区域重置到顶部
    nextTick(() => {
      if (poolsScrollbar.value) {
        poolsScrollbar.value.setScrollTop(0)
      }
    })
  }

  // 修改对话框关闭处理函数
  const handleManagePoolsClose = () => {
    // 恢复body滚动
    document.body.style.overflow = ''
    
    // 重置搜索
    poolSearchQuery.value = ''
  }

  // 保存卡池排序
  const savePoolOrder = async () => {
    try {
      await saveSceneData()
      ElMessage.success('卡池顺序已保存')
    } catch (error) {
      ElMessage.error('保存顺序失败，请重试')
      console.error('保存卡池顺序失败:', error)
    }
  }

  // 选择卡池
  const selectPool = (pool) => {
    sceneData.value.currentPoolId = pool.id
    managePoolsDialogVisible.value = false
    ElMessage.success(`已切换到卡池：${pool.name}`)
  }

  // 重命名卡池对话框控制
  const renamePoolDialogVisible = ref(false)
  const renamePoolForm = reactive({
    id: '',
    name: ''
  })
  const currentRenamingPool = ref(null)

  // 修复显示重命名对话框
  const showRenamePoolDialog = (pool) => {
    currentRenamingPool.value = pool;
    renamePoolForm.id = pool.id;
    renamePoolForm.name = pool.name;
    renamePoolDialogVisible.value = true;
    
    // 聚焦输入框并选中内容
    nextTick(() => {
      const input = document.querySelector('.rename-pool-dialog .el-input__inner');
      if (input) {
        input.focus();
        input.select();
      }
    });
  };

  // 改进处理重命名卡池方法
  const handleRenamePool = async () => {
    // 检验名称不为空
    if (!renamePoolForm.name.trim()) {
      ElMessage.warning('请输入卡池名称');
      return;
    }
    
    // 查找并更新卡池
    const pool = sceneData.value.pools.find(p => p.id === renamePoolForm.id);
    if (pool) {
      pool.name = renamePoolForm.name.trim();
      pool.updateTime = Date.now();
      
      try {
        await saveSceneData();
        ElMessage.success('卡池已重命名');
        renamePoolDialogVisible.value = false;
      } catch (error) {
        ElMessage.error('重命名失败，请重试');
        console.error('重命名卡池失败:', error);
      }
    }
  };

  // 处理重命名对话框关闭
  const handleRenamePoolClose = () => {
    currentRenamingPool.value = null
    renamePoolForm.id = ''
    renamePoolForm.name = ''
  }

  // 搜索卡池
  const poolSearchQuery = ref('')
  const poolsScrollbar = ref(null)

  // 将卡池移动到顶部
  const movePoolToTop = async (index) => {
    if (index > 0) {
      // 移动卡池到数组开头
      const pool = sceneData.value.pools.splice(index, 1)[0]
      sceneData.value.pools.unshift(pool)
      
      try {
        await saveSceneData()
        ElMessage.success('卡池已置顶')
      } catch (error) {
        ElMessage.error('操作失败，请重试')
        console.error('卡池置顶失败:', error)
      }
    }
  }

  // 改进滚动到顶部方法，添加平滑动画
  const scrollToTop = () => {
    if (poolsScrollbar.value) {
      // 获取滚动元素
      const scrollWrap = poolsScrollbar.value.$el.querySelector('.el-scrollbar__wrap');
      if (!scrollWrap) return;
      
      // 起始位置
      const startPosition = scrollWrap.scrollTop;
      if (startPosition === 0) return; // 已经在顶部则不执行
      
      // 动画时长基于滚动距离动态计算，但有最小和最大限制
      const duration = Math.min(Math.max(startPosition * 0.5, 300), 800);
      const startTime = performance.now();
      
      // 动画函数
      function animateScroll(currentTime) {
        const elapsedTime = currentTime - startTime;
        const progress = Math.min(elapsedTime / duration, 1);
        
        // 使用缓出动画曲线，开始快结束慢
        const easeOutCubic = 1 - Math.pow(1 - progress, 3);
        
        // 计算新的滚动位置
        scrollWrap.scrollTop = startPosition * (1 - easeOutCubic);
        
        // 如果动画未完成，继续请求下一帧
        if (progress < 1) {
          requestAnimationFrame(animateScroll);
        }
      }
      
      // 开始动画
      requestAnimationFrame(animateScroll);
      
      // 播放轻微的点击反馈
      const btn = document.querySelector('.scroll-top-btn');
      if (btn) {
        btn.classList.add('is-scrolling');
        setTimeout(() => {
          btn.classList.remove('is-scrolling');
        }, 300);
      }
    }
  };

  // 过滤后的卡池列表
  const filteredPools = computed(() => {
    let pools = [...sceneData.value.pools]
    
    // 如果有搜索查询
    if (poolSearchQuery.value.trim()) {
      const query = poolSearchQuery.value.toLowerCase().trim()
      pools = pools.filter(pool => 
        pool.name.toLowerCase().includes(query)
      )
    }
    
    return pools
  })

  // 处理卡池搜索
  const handlePoolSearch = () => {
    // 重置滚动位置
    nextTick(() => {
      if (poolsScrollbar.value) {
        poolsScrollbar.value.setScrollTop(0)
      }
    })
  }

  // 判断卡池是否匹配搜索
  const isPoolMatch = (pool) => {
    if (!poolSearchQuery.value.trim()) return false
    const query = poolSearchQuery.value.toLowerCase().trim()
    return pool.name.toLowerCase().includes(query)
  }

  // 添加无限画布相关的状态
  const cardsGridRef = ref(null)
  const infiniteCanvasRef = ref(null)
  const canvasOffsetX = ref(0)
  const canvasOffsetY = ref(0)
  const canvasScale = ref(1)
  const isDraggingCanvas = ref(false)
  const dragStartX = ref(0)
  const dragStartY = ref(0)
  const showDragHint = ref(true)
  const activeCardId = ref(null)
  const draggingCardId = ref(null); // 当前正在拖拽的卡片ID

  // 新的交互状态管理
  const clickTimer = ref(null)
  const isDoubleClick = ref(false)
  const dragStartTime = ref(0)
  const dragThreshold = 5 // 像素阈值，超过这个距离才算拖拽
  const mouseStartPos = ref({ x: 0, y: 0 })

  // 设置画布样式
  const canvasStyle = computed(() => ({
    transform: `translate(${canvasOffsetX.value}px, ${canvasOffsetY.value}px) scale(${canvasScale.value})`,
  }))

  // 初始显示拖动提示，并在5秒后隐藏
  onMounted(() => {
    // 确保初始化场景数据
    if (scenes.value && scenes.value.length > 0) {
      scenes.value = scenes.value.map(initializeScene);
      
      // 保存转换后的数据结构
      saveSceneData();
    }
    
    // 显示拖动提示
    showDragHint.value = true;
    setTimeout(() => {
      showDragHint.value = false;
    }, 1000);
  });

  // 处理画布鼠标按下事件
  const handleCanvasMouseDown = (event) => {
    // 只有右键才触发画布拖动
    if (event.button === 2) {
      isDraggingCanvas.value = true
      dragStartX.value = event.clientX - canvasOffsetX.value
      dragStartY.value = event.clientY - canvasOffsetY.value
      
      // 显示拖动提示
      showDragHint.value = true
      
      // 改变鼠标样式
      if (infiniteCanvasRef.value) {
        infiniteCanvasRef.value.style.cursor = 'grabbing'
      }
      
      // 阻止默认右键菜单
      event.preventDefault()
    }
  }

  // 处理画布鼠标移动事件
  const handleCanvasMouseMove = (event) => {
    if (isDraggingCanvas.value) {
      canvasOffsetX.value = event.clientX - dragStartX.value
      canvasOffsetY.value = event.clientY - dragStartY.value
      event.preventDefault()
    }
  }

  // 处理画布鼠标释放事件
  const handleCanvasMouseUp = (event) => {
    if (isDraggingCanvas.value) {
      isDraggingCanvas.value = false
      
      // 恢复鼠标样式
      if (infiniteCanvasRef.value) {
        infiniteCanvasRef.value.style.cursor = 'default'
      }
      
      // 3秒后隐藏拖动提示
      setTimeout(() => {
        showDragHint.value = false
      }, 1000)
    }
  }

  // 处理画布滚轮事件 - 用于缩放
  const handleCanvasWheel = (event) => {
    // 防止页面滚动
    event.preventDefault()
    
    // 鼠标位置相对于画布
    const rect = cardsGridRef.value.getBoundingClientRect()
    const mouseX = event.clientX - rect.left
    const mouseY = event.clientY - rect.top
    
    // 缩放前在画布上的实际位置
    const beforeZoomX = (mouseX - canvasOffsetX.value) / canvasScale.value
    const beforeZoomY = (mouseY - canvasOffsetY.value) / canvasScale.value
    
    // 调整缩放比例
    const scaleFactor = 0.1
    if (event.deltaY < 0) {
      // 放大，最大缩放到200%
      canvasScale.value = Math.min(2, canvasScale.value + scaleFactor)
    } else {
      // 缩小，最小缩放到30%
      canvasScale.value = Math.max(0.3, canvasScale.value - scaleFactor)
    }
    
    // 缩放后在画布上的实际位置应该不变
    // 调整偏移量以保持鼠标下方的点不变
    const afterZoomX = (mouseX - canvasOffsetX.value) / canvasScale.value
    const afterZoomY = (mouseY - canvasOffsetY.value) / canvasScale.value
    
    canvasOffsetX.value += (afterZoomX - beforeZoomX) * canvasScale.value
    canvasOffsetY.value += (afterZoomY - beforeZoomY) * canvasScale.value
    
    // 显示缩放提示
    showDragHint.value = true
    setTimeout(() => {
      showDragHint.value = false
    }, 1000)
  }

  // 放大按钮
  const zoomIn = () => {
    canvasScale.value = Math.min(2, canvasScale.value + 0.1)
  }

  // 缩小按钮
  const zoomOut = () => {
    canvasScale.value = Math.max(0.3, canvasScale.value - 0.1)
  }

  // 自动排版功能
  const autoLayout = () => {
    if (!scenes.value.length) {
      ElMessage.info('没有场景卡片需要排版')
      return
    }

    // 如果是在全部场景卡池中，不允许自动排版
    if (currentPool.value?.isVirtual) {
      ElMessage.warning('不能在"全部场景"卡池中进行自动排版')
      return
    }

    // 获取容器尺寸
    const container = cardsGridRef.value
    if (!container) return

    const containerRect = container.getBoundingClientRect()
    const containerWidth = containerRect.width
    const containerHeight = containerRect.height

    // 卡片尺寸
    const cardWidth = 240
    const cardHeight = 280
    const padding = 20
    const margin = 16

    // 计算可用空间（考虑当前缩放比例，但不重置它）
    const availableWidth = containerWidth / canvasScale.value - padding * 2
    const availableHeight = containerHeight / canvasScale.value - padding * 2

    // 计算每行可以放置的卡片数量
    const cardsPerRow = Math.floor((availableWidth + margin) / (cardWidth + margin))
    const actualCardsPerRow = Math.max(1, Math.min(cardsPerRow, scenes.value.length))

    // 计算总行数
    const totalRows = Math.ceil(scenes.value.length / actualCardsPerRow)

    // 计算网格的总尺寸
    const gridWidth = actualCardsPerRow * cardWidth + (actualCardsPerRow - 1) * margin
    const gridHeight = totalRows * cardHeight + (totalRows - 1) * margin

    // 计算起始位置（居中）
    const startX = (availableWidth - gridWidth) / 2 + padding
    const startY = (availableHeight - gridHeight) / 2 + padding

    // 重新排列场景卡片
    scenes.value.forEach((scene, index) => {
      const row = Math.floor(index / actualCardsPerRow)
      const col = index % actualCardsPerRow

      scene.x = startX + col * (cardWidth + margin)
      scene.y = startY + row * (cardHeight + margin)
    })

    // 保存更改
    saveSceneData().then(() => {
      ElMessage.success(`已自动排版 ${scenes.value.length} 个场景卡片`)
    }).catch(() => {
      ElMessage.error('自动排版失败，请重试')
    })

    // 保持当前缩放比例，只调整画布位置以便查看排版结果
    nextTick(() => {
      if (scenes.value.length > 0) {
        // 计算排版后内容的边界
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity

        scenes.value.forEach(scene => {
          const x = scene.x || 0
          const y = scene.y || 0
          const width = cardWidth
          const height = cardHeight

          minX = Math.min(minX, x)
          minY = Math.min(minY, y)
          maxX = Math.max(maxX, x + width)
          maxY = Math.max(maxY, y + height)
        })

        // 计算内容的中心点
        const contentCenterX = (minX + maxX) / 2
        const contentCenterY = (minY + maxY) / 2

        // 计算容器的中心点
        const containerCenterX = containerRect.width / 2
        const containerCenterY = containerRect.height / 2

        // 调整偏移量使内容居中，保持当前缩放比例
        canvasOffsetX.value = containerCenterX - contentCenterX * canvasScale.value
        canvasOffsetY.value = containerCenterY - contentCenterY * canvasScale.value
      }
    })
  }

  // 重置画布位置和缩放
  const resetCanvas = () => {
    canvasOffsetX.value = 0
    canvasOffsetY.value = 0
    canvasScale.value = 1

    // 无论是否全屏，都确保内容居中
    if (cardsGridRef.value) {
      nextTick(() => {
        const container = cardsGridRef.value
        const containerRect = container.getBoundingClientRect()

        // 如果有场景卡片，计算它们的边界
        if (scenes.value.length > 0) {
          let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity

          scenes.value.forEach(scene => {
            const x = scene.x || 0
            const y = scene.y || 0
            const width = 240 // 卡片实际宽度
            const height = 280 // 卡片实际高度

            minX = Math.min(minX, x)
            minY = Math.min(minY, y)
            maxX = Math.max(maxX, x + width)
            maxY = Math.max(maxY, y + height)
          })

          // 计算内容的中心点
          const contentCenterX = (minX + maxX) / 2
          const contentCenterY = (minY + maxY) / 2

          // 计算容器的中心点
          const containerCenterX = containerRect.width / 2
          const containerCenterY = containerRect.height / 2

          // 调整偏移量使内容居中
          canvasOffsetX.value = containerCenterX - contentCenterX
          canvasOffsetY.value = containerCenterY - contentCenterY
        }
      })
    }
  }

  // 处理卡片鼠标进入
  const handleCardMouseEnter = (cardId, event) => {
    // 标记当前卡片为激活状态，用于视觉反馈
    activeCardId.value = cardId
    
    // 阻止事件冒泡，防止触发其他卡片的事件
    event.stopPropagation()
  }

  // 处理卡片鼠标离开
  const handleCardMouseLeave = () => {
    // 鼠标离开时清除激活状态
    activeCardId.value = null
  }

  // 新的鼠标按下处理函数
  const handleCardMouseDown = (cardId, event) => {
    // 只处理左键点击
    if (event.button !== 0 || isDraggingCanvas.value) return

    // 记录鼠标按下的时间和位置
    dragStartTime.value = Date.now()
    mouseStartPos.value = { x: event.clientX, y: event.clientY }
    isDoubleClick.value = false
    activeCardId.value = cardId

    // 清除之前的点击计时器
    if (clickTimer.value) {
      clearTimeout(clickTimer.value)
      clickTimer.value = null
    }

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleCardMouseMove)
    document.addEventListener('mouseup', handleCardMouseUp)
  }

  // 鼠标移动处理函数
  const handleCardMouseMove = (event) => {
    if (isDoubleClick.value) return

    const deltaX = Math.abs(event.clientX - mouseStartPos.value.x)
    const deltaY = Math.abs(event.clientY - mouseStartPos.value.y)
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

    // 如果移动距离超过阈值且不是双击，开始拖拽
    if (distance > dragThreshold && !draggingCardId.value) {
      startCardDrag(event)
    }

    // 如果已经在拖拽，继续拖拽
    if (draggingCardId.value) {
      handleCardDragMove(event)
    }
  }

  // 鼠标释放处理函数
  const handleCardMouseUp = (event) => {
    // 移除全局事件监听
    document.removeEventListener('mousemove', handleCardMouseMove)
    document.removeEventListener('mouseup', handleCardMouseUp)

    // 如果正在拖拽，结束拖拽
    if (draggingCardId.value) {
      handleCardDragEnd(event)
    }
  }

  // 开始拖动卡片
  const startCardDrag = (event) => {
    const cardId = activeCardId.value
    if (!cardId) return

    // 设置正在拖动的卡片
    draggingCardId.value = cardId

    // 在拖动时添加拖动样式
    const card = document.getElementById(`scene-card-${cardId}`)
    if (card) {
      card.classList.add('dragging')
    }

    // 获取卡片DOM元素和位置信息
    const scene = scenes.value.find(s => s.id === cardId)
    const cardWrapper = document.getElementById(`scene-card-wrapper-${cardId}`)

    if (scene && cardWrapper) {
      // 获取卡片的当前位置
      const cardRect = cardWrapper.getBoundingClientRect()

      // 计算鼠标相对于卡片左上角的偏移量
      const mouseOffsetX = event.clientX - cardRect.left
      const mouseOffsetY = event.clientY - cardRect.top

      // 记录这些关键信息用于拖动计算
      dragStartX.value = event.clientX
      dragStartY.value = event.clientY
      scene.startX = scene.x !== undefined ? scene.x : (scene.position?.left || 0)
      scene.startY = scene.y !== undefined ? scene.y : (scene.position?.top || 0)

      // 保存鼠标相对于卡片的偏移量
      scene.mouseOffsetX = mouseOffsetX
      scene.mouseOffsetY = mouseOffsetY
    }
  }

  // 双击处理函数
  const handleCardDoubleClick = (scene, event) => {
    // 标记为双击，阻止拖拽
    isDoubleClick.value = true

    // 清除拖拽状态
    if (draggingCardId.value) {
      const card = document.getElementById(`scene-card-${draggingCardId.value}`)
      if (card) {
        card.classList.remove('dragging')
      }
      draggingCardId.value = null
    }

    // 移除事件监听
    document.removeEventListener('mousemove', handleCardMouseMove)
    document.removeEventListener('mouseup', handleCardMouseUp)

    // 调用编辑函数
    editScene(event, scene)
  }

  // 卡片拖动中
  const handleCardDragMove = (event) => {
    if (draggingCardId.value) {
      const scene = scenes.value.find(s => s.id === draggingCardId.value);
      if (scene) {
        // 获取画布容器位置
        const canvasRect = cardsGridRef.value.getBoundingClientRect();
        
        // 计算鼠标在画布中的位置（考虑画布偏移和缩放）
        const canvasMouseX = (event.clientX - canvasRect.left - canvasOffsetX.value) / canvasScale.value;
        const canvasMouseY = (event.clientY - canvasRect.top - canvasOffsetY.value) / canvasScale.value;
        
        // 考虑鼠标在卡片上的偏移量，计算卡片新位置
        scene.x = canvasMouseX - (scene.mouseOffsetX / canvasScale.value);
        scene.y = canvasMouseY - (scene.mouseOffsetY / canvasScale.value);
      }
    }
  };

  // 卡片拖动结束
  const handleCardDragEnd = () => {
    if (draggingCardId.value) {
      // 拖动结束后移除临时类
      const card = document.getElementById(`scene-card-${draggingCardId.value}`)
      if (card) {
        card.classList.remove('dragging')
      }
      
      // 更新卡片的实际zIndex，确保最后操作的卡片在最上层
      updateCardZIndex(draggingCardId.value)
      
      // 保存场景数据
      saveSceneData()
      
      draggingCardId.value = null
      
      // 移除全局事件监听
      document.removeEventListener('mousemove', handleCardDragMove)
      document.removeEventListener('mouseup', handleCardDragEnd)
    }
  }

  // 获取卡片样式，包括位置和z-index
  const getCardStyle = (scene) => {
    // 优先使用x/y，如果没有则使用position
    const left = scene.x !== undefined ? scene.x : (scene.position?.left || 0);
    const top = scene.y !== undefined ? scene.y : (scene.position?.top || 0);
    
    return {
      left: `${left}px`,
      top: `${top}px`,
      zIndex: scene.zIndex || 0,
    }
  }

  // 场景数据结构适配，确保兼容性
  const initializeScene = (scene) => {
    // 确保场景有x和y坐标
    if (scene.position) {
      // 兼容旧数据结构
      scene.x = scene.position.left || 0;
      scene.y = scene.position.top || 0;
    } else if (typeof scene.x === 'undefined' || typeof scene.y === 'undefined') {
      // 新场景初始化默认位置
      scene.x = Math.random() * 500; // 随机位置让新卡片不重叠
      scene.y = Math.random() * 300;
    }
    
    // 确保有z-index
    if (!scene.zIndex) {
      scene.zIndex = 1;
    }
    
    return scene;
  }

  // 在加载场景时适配数据
  const loadScenes = async () => {
    // ... 原有的加载逻辑 ...
    
    // 应用数据适配
    scenes.value = scenes.value.map(initializeScene);
    
    // ... 其余代码 ...
  }

  // 创建新场景时使用新的数据结构
  const createScene = () => {

    newScene.x = Math.random() * 500;
    newScene.y = Math.random() * 300;
    newScene.zIndex = maxZIndex.value + 1;
    

  }




  </script>
  
  <style lang="scss" scoped>
  /* 原生应用通用样式 */
.native-app-style {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;

  /* 禁用右键菜单 */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;

  /* 禁用图片和媒体元素拖拽 */
  img, svg, canvas, video, audio {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    pointer-events: none;
  }

  /* 按钮和交互元素恢复指针事件 */
  button, .el-button, input, textarea, select, .el-input, .el-select, .el-textarea {
    pointer-events: auto;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* 输入框内容可以选择 */
  input, textarea, .el-input__inner, .el-textarea__inner {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
  }
}

.scene-cards-container {
    padding: 24px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 24px;
    background: var(--el-bg-color-page);

    /* 应用原生应用样式 */
    @extend .native-app-style;
  }

  .action-bar {
    display: flex;
    gap: 16px;
    padding: 16px;
    background: var(--el-bg-color);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    align-items: center;
    position: relative;
    z-index: 10;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(var(--el-border-color-light-rgb), 0.6);

    /* 原生应用样式 - 禁用选择和拖拽 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-drag: none;

    .book-select, .pool-select, .draw-count-select {
      :deep(.el-input__wrapper) {
        height: 40px;
        box-shadow: 0 0 0 1px var(--el-border-color-lighter) inset;
        transition: all 0.3s ease;
        background: var(--el-fill-color-blank);
        padding: 0 16px;
        
        &:hover {
          box-shadow: 0 0 0 1px var(--el-color-primary-light-5) inset;
        }
        
        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary-light-5) inset !important;
          border-color: transparent !important;
        }
      }
      
      :deep(.el-input__suffix) {
        display: flex;
        align-items: center;
      }
      
      :deep(.el-select__wrapper) {
        height: 40px;
        border: none;
        box-shadow: none !important;
      }
      
      // 移除激活状态的蓝色边框
      :deep(.el-select:hover .el-input__wrapper),
      :deep(.el-select.is-focus .el-input__wrapper) {
        box-shadow: 0 0 0 1px var(--el-color-primary-light-5) inset !important;
        border-color: transparent !important;
      }
    }

    .book-select {
      width: 200px;
    }

    .pool-select {
      width: 240px;
    }

    .draw-count-select {
      width: 140px;
    }

    // 确保所有按钮高度一致
    .el-button, .el-dropdown .el-button {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  :deep(.el-dropdown-menu__item) {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    font-size: 14px;
    line-height: 1.5;
    transition: all 0.3s ease;

    .el-icon {
      font-size: 16px;
      color: var(--el-color-primary);
    }

    &:not(.is-disabled):hover {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }

    &.is-disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }

  .dropdown-item-content {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    
    .el-icon {
      font-size: 16px;
    }
  }
  
  .cards-grid {
    flex: 1;
    position: relative;
    overflow: hidden;
    background: var(--el-bg-color);
    border-radius: 16px;
    min-height: 600px;
    height: calc(100vh - 200px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(var(--el-border-color-light-rgb), 0.8);
    backdrop-filter: blur(4px);

    background-image:
      linear-gradient(rgba(var(--el-border-color-lighter-rgb), 0.2) 1px, transparent 1px),
      linear-gradient(90deg, rgba(var(--el-border-color-lighter-rgb), 0.2) 1px, transparent 1px);
    background-size: 20px 20px;

    /* 原生应用样式 - 禁用选择和拖拽 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
  }
  
  .scene-card-wrapper {
    position: absolute;
    cursor: move;
    user-select: none;
    transform-origin: top left;
    width: 240px; /* 固定宽度 */
    transition: none !important; /* 禁用可能导致问题的过渡效果 */

    /* 原生应用样式 - 禁用图片和元素拖拽 */
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;

    /* 禁用卡片内所有图片和元素的拖拽 */
    img, svg, canvas, video {
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
      pointer-events: none;
    }
  }
  
  .scene-card-wrapper .el-card {
    width: 100%;
    height: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: visible;
  }
  
  .scene-card-wrapper .dragging {
    z-index: 1000 !important;
    transform: scale(1.02) !important;
    opacity: 0.9;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15) !important;
  }
  
  .ghost-card {
    opacity: 0.5;
    background: var(--el-color-primary-light-9);
  }
  
  .add-card {
    width: 240px;
    height: 280px;
    border: 2px dashed var(--el-border-color);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--el-text-color-secondary);
  }
  
  .add-card:hover {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
    transform: translateY(-2px);
  }
  
  .add-card .el-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }
  
  .add-card span {
    font-size: 16px;
  }
  
  .draw-dialog :deep(.el-dialog) {
    margin: 15vh auto 0 !important;
    margin-bottom: 0 !important;
  }

  .draw-dialog :deep(.el-dialog__body) {
    padding: 0;
    overflow: hidden;
  }

  .draw-result {
    background: var(--el-bg-color-page);
    height: 100%;
    overflow: hidden;
  }

  .draw-content {
    display: flex;
    gap: 24px;
    padding: 24px;
    height: 100%;
    overflow: hidden;
  }

  .scenes-section {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .inspiration-section {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .drawn-cards {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    background: var(--el-bg-color);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    border: 1px solid rgba(var(--el-border-color-light-rgb), 0.6);
  }

  .drawn-card-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
    flex: 1;
    min-width: 0;
  }

  .scene-connector {
    position: absolute;
    right: -28px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--el-color-primary);
    font-size: 24px;
  }
  
  .combination-hint {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    color: var(--el-text-color-regular);
    font-size: 16px;
    margin-top: 16px;
    padding: 16px;
    background: var(--el-color-primary-light-9);
    border-radius: 8px;
  }
  
  .inspiration-input {
    background: var(--el-bg-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid rgba(var(--el-border-color-light-rgb), 0.6);
  }
  
  .inspiration-input .el-textarea {
    flex: 1;
    overflow: hidden;
  }
  
  .inspiration-input .el-textarea :deep(.el-textarea__inner) {
    height: 100% !important;
    resize: none;
  }
  
  .inspiration-meta {
    margin-top: 16px;
    padding: 16px;
    background: var(--el-fill-color-blank);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    flex-shrink: 0;
    box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(var(--el-border-color-light-rgb), 0.4);
  }
  
  .inspiration-actions {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
  }
  
  .inspiration-label {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
    color: var(--el-text-color-primary);
    flex-shrink: 0;
  }
  
  .inspiration-rating {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .rating-label {
    color: var(--el-text-color-regular);
    font-size: 14px;
    white-space: nowrap;
  }
  
  .rating-slider {
    flex: 1;
    margin-right: 16px;
  }
  
  .compact-card {
    width: 100%;
  }
  
  .compact-card :deep(.el-card__body) {
    padding: 12px;
    max-height: 300px;
    overflow-y: auto;
  }
  
  .scene-number {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    font-weight: 500;
    text-align: center;
  }

  
  .history-dialog :deep(.el-dialog) {
    position: fixed;
    top: 0;
    left: 0;
    margin: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    display: flex;
    flex-direction: column;
    border-radius: 0;
    overflow: hidden;
    backdrop-filter: blur(8px);
    border: none;
    background: var(--el-bg-color-page);
  }

  .history-dialog :deep(.el-dialog__header) {
    margin: 0;
    padding: 0;
    height: 64px;
    min-height: 64px;
    flex: 0 0 64px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    backdrop-filter: blur(12px);
    background: rgba(var(--el-bg-color-rgb), 0.8);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .history-dialog :deep(.el-dialog__body) {
    margin: 0;
    padding: 0;
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .history-container {
    position: absolute;
    top: 11%;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
  }

  .history-content {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .history-list {
    user-select:none;
    width: 480px;
    border-right: 1px solid var(--el-border-color-lighter);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .history-list-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
  }

  .history-detail-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 24px 32px;
    overflow: hidden;
  }

  .history-scenes {
    user-select:none;
    flex: 0 0 240px;
    background: var(--el-bg-color);
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px var(--el-mask-color-extra-light);
    border: 1px solid var(--el-border-color-lighter);
    margin-bottom: 24px;
    overflow: hidden;
  }

  .history-inspiration {
    user-select:none;
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--el-bg-color);
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px var(--el-mask-color-extra-light);
    border: 1px solid var(--el-border-color-lighter);
    overflow: hidden;
  }

  .inspiration-content {
    user-select:text;
    flex: 1;
    overflow-y: auto;
    white-space: pre-wrap;
    line-height: 1.8;
    color: var(--el-text-color-regular);
    padding: 20px;
    background: var(--el-fill-color-blank);
    border-radius: 4px;
    font-size: 14px;
    border: 1px solid var(--el-border-color-lighter);
  }

  .history-footer {
    user-select:none;
    flex: 0 0 60px;
    border-top: 1px solid var(--el-border-color-lighter);
    background: var(--el-bg-color);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dialog-header-content {
    user-select: none;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32px;
    background: var(--el-bg-color);
  }

  .dialog-header-content h4 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .close-btn {
    font-size: 20px;
    color: var(--el-text-color-secondary);
    border: none;
    background: transparent;
    padding: 8px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .close-btn:hover {
    color: var(--el-text-color-primary);
    transform: scale(1.1);
  }

  .history-item {
    background: var(--el-bg-color);
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px var(--el-mask-color-extra-light);
    cursor: pointer;
    transition: all 0.3s;
    padding: 16px;
    border: 1px solid var(--el-border-color-lighter);
  }

  .history-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--el-mask-color-extra-light);
  }

  .history-item.active {
    border: 1px solid var(--el-color-primary);
    background: var(--el-color-primary-light-9);
  }

  .history-header {
    padding: 0;
  }

  .header-main {
    margin-bottom: 12px;
  }

  .history-meta-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .pool-tag {
    font-size: 12px;
  }

  .history-time {
    font-size: 13px;
    color: var(--el-text-color-secondary);
  }

  .history-brief {
    font-size: 14px;
    line-height: 1.6;
    color: var(--el-text-color-regular);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 12px;
  }

  .history-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
  }

  .history-tags {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  .scenes-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: var(--el-text-color-primary);
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .scenes-title:after {
    content: "";
    flex: 1;
    height: 1px;
    background: var(--el-border-color-lighter);
  }

  .scenes-list {
    display: flex;
    flex-direction: row;
    gap: 16px;
    overflow-x: auto;
    padding-bottom: 8px;
    height: calc(100% - 40px);
  }

  .history-scene {
    background: var(--el-bg-color);
    border-radius: 8px;
    padding: 20px;
    position: relative;
    width: 320px;
    flex-shrink: 0;
    border: 1px solid var(--el-border-color-lighter);
    display: flex;
    flex-direction: column;
  }

  .inspiration-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .inspiration-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .inspiration-title:after {
    content: "";
    flex: 1;
    height: 1px;
    background: var(--el-border-color-lighter);
  }

  .pool-info {
    margin-left: 12px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
  
  .create-pool-btn {
    padding: 0;
    margin-right: 8px;
    transition: transform 0.2s ease, color 0.2s ease;
    
    &:hover {
      color: var(--el-color-primary);
      transform: scale(1.2);
    }
  }
  
  .draw-count-select {
    width: 140px;
  }

  .scenes-list::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scenes-list::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 3px;
  }

  .scenes-list::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
  }

  .history-inspiration::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .history-inspiration::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 3px;
  }

  .history-inspiration::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
  }

  .history-list::-webkit-scrollbar {
    width: 4px;
  }

  .history-list::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 2px;
  }

  .history-list::-webkit-scrollbar-track {
    background: transparent;
  }

  .inspiration-content::-webkit-scrollbar {
    width: 4px;
  }

  .inspiration-content::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 2px;
  }

  .inspiration-content::-webkit-scrollbar-track {
    background: transparent;
  }

  :deep(.el-overlay) {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    overflow: hidden !important;
    height: 100vh !important;
    width: 100vw !important;
  }

  .history-count {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-left: 8px;
    font-weight: normal;
  }

  .scene-count {
    margin-left: 8px;
  }

  .scene-description {
    font-size: 13px;
    color: var(--el-text-color-secondary);
    line-height: 1.6;
    margin: 8px 0 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .pool-option {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .pool-name {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .delete-pool-btn {
    opacity: 0;
    transition: opacity 0.3s, transform 0.2s;
    margin-left: 8px;
    
    &:hover {
      transform: scale(1.2);
      color: var(--el-color-danger);
    }
  }

  .pool-option:hover .delete-pool-btn {
    opacity: 1;
  }

  .el-select-dropdown__item:hover {
    background-color: var(--el-fill-color-light);
  }

  .delete-history-btn {
    opacity: 0;
    transition: opacity 0.3s;
  }

  .history-item:hover .delete-history-btn {
    opacity: 1;
  }

  .delete-history-btn:hover {
    transform: scale(1.1);
  }

  /* 场景对话框样式 */
  .scene-dialog :deep(.el-dialog) {
  
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .scene-dialog :deep(.el-dialog__header) {

    margin: 0;
    padding: 24px 32px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-bg-color);
  }

  .scene-dialog :deep(.el-dialog__title) {
    font-size: 22px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .scene-dialog :deep(.el-dialog__headerbtn) {
    top: 24px;
    right: 24px;
    font-size: 20px;
  }

  .scene-dialog :deep(.el-dialog__body) {
    padding: 32px;
    background: var(--el-bg-color-page);
  }

  .scene-form :deep(.el-form-item__label) {
    user-select: none;
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    padding-right: 24px;
  }

  .scene-title-input :deep(.el-input__wrapper) {
    padding: 8px 16px;
    font-size: 16px;
    box-shadow: none;
    border: 1px solid var(--el-border-color);
    transition: all 0.3s;
  }

  .scene-title-input :deep(.el-input__wrapper:hover),
  .scene-title-input :deep(.el-input__wrapper.is-focus) {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px var(--el-color-primary-light-8);
  }

  .scene-description-input :deep(.el-textarea__inner) {
    padding: 16px;
    font-size: 15px;
    line-height: 1.6;
    border: 1px solid var(--el-border-color);
    box-shadow: none;
    transition: all 0.3s;
  }

  .scene-description-input :deep(.el-textarea__inner:hover),
  .scene-description-input :deep(.el-textarea__inner:focus) {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px var(--el-color-primary-light-8);
  }

  .scene-tags-select {
    width: 100%;
  }

  .scene-tags-select :deep(.el-select__wrapper) {
    padding: 8px 16px;
    font-size: 15px;
  }

  .scene-dialog :deep(.el-dialog__footer) {
    padding: 24px 32px;
    background: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-lighter);
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
  }

  .dialog-footer .el-button {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 8px;
    transition: all 0.3s;
  }

  .dialog-footer .cancel-btn {
    border: 1px solid var(--el-border-color);
  }

  .dialog-footer .cancel-btn:hover {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
  }

  .dialog-footer .save-btn {
    font-weight: 500;
  }

  .dialog-footer .save-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--el-color-primary-light-5);
  }

  /* 输入框placeholder样式 */
  .scene-dialog :deep(.el-input__inner::placeholder),
  .scene-dialog :deep(.el-textarea__inner::placeholder) {
    color: var(--el-text-color-placeholder);
    font-size: 14px;
  }

  /* 标签选择器样式 */
  .scene-tags-select :deep(.el-tag) {
    border-radius: 4px;
    padding: 4px 8px;
    margin: 2px;
  }

  .scene-tags-select :deep(.el-select__tags-text) {
    font-size: 14px;
  }

  .related-pools-tooltip {
    padding: 8px;
    min-width: 200px;
  }

  .tooltip-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #fff;
  }

  .related-pool-item {
    font-size: 13px;
    line-height: 1.6;
    color: #eee;
    padding: 4px 0;
  }

  .related-pool-item:not(:last-child) {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .dropdown-item-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  :deep(.el-dropdown-menu__item) {
    padding: 5px 16px;
    line-height: 1.5;
  }

  :deep(.el-dropdown-menu__item:not(.is-disabled):hover) {
    background-color: var(--el-fill-color-light);
  }

  :deep(.el-dropdown-menu__item.is-disabled) {
    opacity: 0.7;
  }

  :deep(.el-dropdown-menu__item .el-icon) {
    font-size: 16px;
    color: var(--el-color-primary);
  }

  /* 添加卡池拖拽相关样式 */
  .manage-pools-btn {
    margin-left: 6px;
    margin-right: 0;
    padding: 2px;
    font-size: 14px;
    transition: transform 0.2s;
    
    &:hover {
      color: var(--el-color-primary);
      transform: scale(1.1);
    }
  }

  .manage-pools-container {
    padding: 0 0 16px;
  }

  .manage-pools-hint {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-bottom: 16px;
    line-height: 1.5;
    padding: 8px 12px;
    background: var(--el-color-info-light-9);
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &:before {
      content: "💡";
      font-size: 16px;
    }
  }

  .pool-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: var(--el-bg-color);
    border-radius: 8px;
    border: 1px solid var(--el-border-color-lighter);
    transition: all 0.2s;
    
    &:hover {
      border-color: var(--el-color-primary-light-5);
      transform: translateY(-2px);
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
    }
  }

  .ghost-pool {
    opacity: 0.5;
    background: var(--el-color-primary-light-9) !important;
    border: 1px dashed var(--el-color-primary) !important;
  }

  .drag-handle {
    cursor: move;
    font-size: 18px;
    color: var(--el-text-color-secondary);
    margin-right: 12px;
    flex-shrink: 0;
    
    &:hover {
      color: var(--el-color-primary);
    }
  }

  .pool-item-content {
    flex: 1;
    min-width: 0;
    margin-right: 16px;
  }

  .pool-item-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .pool-item-info {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--el-text-color-secondary);
    font-size: 13px;
  }

  .pool-item-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
  }

  .select-pool-btn, .rename-pool-btn, .delete-pool-btn {
    padding: 8px;
    border-radius: 4px;
    font-size: 16px;
    
    &:hover {
      transform: scale(1.15);
    }
  }

  .select-pool-btn {
    color: var(--el-color-success);
  }

  .rename-pool-btn {
    color: var(--el-color-warning);
  }

  .delete-pool-btn {
    color: var(--el-color-danger);
  }

  :deep(.el-divider__text) {
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: var(--el-bg-color-overlay);
  }

  .pools-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .search-wrapper {
    flex: 1;
  }

  .pools-actions {
    display: flex;
    gap: 16px;
  }

  .scene-count-tag {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    background: var(--el-color-info-light-9);
    padding: 4px 8px;
    border-radius: 4px;
  }

  .manage-pools-dialog :deep(.el-dialog) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .manage-pools-dialog :deep(.el-dialog__header) {
    margin: 0;
    padding: 24px 32px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-bg-color);
  }

  .manage-pools-dialog :deep(.el-dialog__title) {
    font-size: 22px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .manage-pools-dialog :deep(.el-dialog__headerbtn) {
    top: 24px;
    right: 24px;
    font-size: 20px;
  }

  .manage-pools-dialog :deep(.el-dialog__body) {
    padding: 32px;
    background: var(--el-bg-color-page);
  }

  .manage-pools-dialog :deep(.el-scrollbar) {
    height: 400px;
  }

  .is-searched {
    background: var(--el-color-info-light-9);
  }

  /* 卡池管理对话框样式优化 */
  .manage-pools-dialog {
    :deep(.el-dialog) {
      display: flex;
      flex-direction: column;
      margin-top: 5vh !important;
      max-height: 90vh;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
    
    :deep(.el-dialog__header) {
      margin: 0;
      padding: 20px 24px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color);
    }
    
    :deep(.el-dialog__title) {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    
    :deep(.el-dialog__body) {
      flex: 1;
      padding: 20px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    
    :deep(.el-dialog__footer) {
      padding: 16px 24px;
      border-top: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color);
    }
  }

  .manage-pools-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .pools-toolbar {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    align-items: center;
    
    .search-wrapper {
      flex: 1;
    }
    
    .pools-actions {
      flex-shrink: 0;
    }
  }

  .pools-scrollbar {
    flex: 1;
    border-radius: 8px;
    border: 1px solid var(--el-border-color-lighter);
    padding: 4px;
    background: var(--el-bg-color);
    
    :deep(.el-scrollbar__wrap) {
      overflow-x: hidden;
    }
    
    :deep(.el-scrollbar__bar.is-horizontal) {
      display: none;
    }
    
    :deep(.el-empty) {
      padding: 40px 0;
    }
  }

  .pool-item {
    display: flex;
    align-items: center;
    padding: 14px 16px;
    margin-bottom: 10px;
    background: var(--el-bg-color-overlay);
    border-radius: 8px;
    border: 1px solid var(--el-border-color-lighter);
    transition: all 0.2s;
    
    &:hover {
      border-color: var(--el-color-primary-light-5);
      transform: translateY(-2px);
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
    }
    
    &.is-searched {
      background: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary-light-5);
    }
    
    &:last-child {
      margin-bottom: 4px;
    }
  }

  .scene-count-tag {
    font-size: 12px;
    border-radius: 4px;
    padding: 0 8px;
    height: 22px;
    line-height: 22px;
    background: var(--el-fill-color);
    color: var(--el-text-color-secondary);
    border: none;
  }

  .manage-pools-hint {
    background: var(--el-fill-color-light);
    border-left: 4px solid var(--el-color-info);
    padding: 10px 16px;
    margin-bottom: 16px;
    border-radius: 4px;
    color: var(--el-text-color-regular);
    font-size: 13px;
    line-height: 1.5;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &:before {
      content: "💡";
      font-size: 16px;
    }
  }

  .pool-item-actions {
    display: flex;
    align-items: center;
    gap: 6px;
    flex-shrink: 0;
  }

  .top-pool-btn, .select-pool-btn, .rename-pool-btn, .delete-pool-btn {
    padding: 8px;
    border-radius: 4px;
    font-size: 16px;
    
    &:hover {
      transform: scale(1.15);
    }
    
    &.is-disabled {
      opacity: 0.4;
      pointer-events: none;
    }
  }

  .top-pool-btn {
    color: var(--el-color-info);
    
    &:hover {
      color: var(--el-color-info-dark-2);
    }
  }

  /* 优化管理弹窗样式 */
  .manage-pools-dialog {
    :deep(.el-overlay) {
      overflow: hidden !important;
    }
    
    :deep(.el-dialog) {
      margin: 5vh auto !important;
      position: relative;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
    
    :deep(.el-dialog__header) {
      margin: 0;
      padding: 20px 24px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color);
      position: relative;
      z-index: 10;
    }
    
    :deep(.el-dialog__body) {
      flex: 1;
      padding: 20px;
      overflow: hidden;
      position: relative;
      max-height: calc(90vh - 140px); /* 减去header和footer的高度 */
    }
    
    :deep(.el-dialog__footer) {
      padding: 16px 24px;
      border-top: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color);
      position: relative;
      z-index: 10;
    }
  }

  .manage-pools-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    position: relative;
  }

  .pools-scrollbar {
    border-radius: 8px;
    border: 1px solid var(--el-border-color-lighter);
    padding: 4px;
    background: var(--el-bg-color);
    height: calc(100% - 80px); /* 减去工具栏和提示的高度 */
    overflow: hidden;
    
    :deep(.el-scrollbar__wrap) {
      overflow-x: hidden;
    }
    
    :deep(.el-scrollbar__bar.is-horizontal) {
      display: none;
    }
  }

  /* 禁止对话框外部内容滚动 */
  :deep(.el-overlay-dialog) {
    overflow: hidden !important;
    
    .el-dialog__wrapper {
      overflow: hidden !important;
    }
  }

  /* 美化返回顶部按钮 */
  .scroll-top-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    height: 36px;
    padding: 0 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-primary);
    background: var(--el-bg-color-overlay);
    border: 1px solid rgba(var(--el-border-color-light-rgb), 0.5);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    
    /* 拟态效果 */
    box-shadow: 
      2px 2px 5px rgba(0, 0, 0, 0.05),
      -2px -2px 5px rgba(255, 255, 255, 0.2),
      inset 0 0 0 rgba(0, 0, 0, 0.05),
      inset 0 0 0 rgba(255, 255, 255, 0.2);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        135deg,
        rgba(var(--el-color-primary-rgb), 0.05) 0%,
        transparent 100%
      );
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .el-icon {
      font-size: 16px;
      transition: transform 0.3s ease;
    }
    
    span {
      position: relative;
    }
    
    &:hover {
      color: var(--el-color-primary-dark-2);
      border-color: rgba(var(--el-color-primary-rgb), 0.3);
      transform: translateY(-2px);
      box-shadow: 
        3px 3px 8px rgba(0, 0, 0, 0.08),
        -3px -3px 8px rgba(255, 255, 255, 0.3),
        inset 0 0 0 rgba(0, 0, 0, 0),
        inset 0 0 0 rgba(255, 255, 255, 0);
      
      &::before {
        opacity: 1;
      }
      
      .el-icon {
        transform: translateY(-2px);
      }
    }
    
    &:active {
      transform: translateY(1px);
      box-shadow: 
        1px 1px 3px rgba(0, 0, 0, 0.05),
        -1px -1px 3px rgba(255, 255, 255, 0.1),
        inset 2px 2px 5px rgba(0, 0, 0, 0.05),
        inset -2px -2px 5px rgba(255, 255, 255, 0.1);
    }
  }

  /* 调整工具栏样式，使其更协调 */
  .pools-toolbar {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    align-items: center;
    
    .search-wrapper {
      flex: 1;
      
      :deep(.el-input__wrapper) {
        box-shadow: 
          0 0 0 1px rgba(var(--el-border-color-rgb), 0.2) inset,
          2px 2px 5px rgba(0, 0, 0, 0.03),
          -2px -2px 5px rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        border-radius: 8px;
        padding-left: 12px;
        
        &:hover {
          box-shadow: 
            0 0 0 1px rgba(var(--el-color-primary-rgb), 0.2) inset,
            2px 2px 5px rgba(0, 0, 0, 0.03),
            -2px -2px 5px rgba(255, 255, 255, 0.1);
        }
        
        &.is-focus {
          box-shadow: 
            0 0 0 1px var(--el-color-primary) inset,
            2px 2px 6px rgba(var(--el-color-primary-rgb), 0.1),
            -2px -2px 6px rgba(255, 255, 255, 0.2);
        }
      }
      
      :deep(.el-input__inner) {
        height: 36px;
        font-size: 14px;
      }
      
      :deep(.el-input__icon) {
        color: var(--el-text-color-secondary);
      }
    }
    
    .pools-actions {
      flex-shrink: 0;
    }
  }

  /* 调整提示条，使整体更协调 */
  .manage-pools-hint {
    background: rgba(var(--el-color-info-light-rgb), 0.1);
    backdrop-filter: blur(5px);
    border-left: 4px solid var(--el-color-primary-light-3);
    padding: 12px 16px;
    margin-bottom: 20px;
    border-radius: 8px;
    color: var(--el-text-color-primary);
    font-size: 14px;
    line-height: 1.5;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 
      2px 2px 10px rgba(0, 0, 0, 0.03),
      -2px -2px 10px rgba(255, 255, 255, 0.05);
    
    &:before {
      content: "💡";
      font-size: 18px;
    }
  }

  /* 添加滚动时的按钮状态 */
  .scroll-top-btn {
    &.is-scrolling {
      color: var(--el-color-primary);
      background: rgba(var(--el-color-primary-rgb), 0.05);
      border-color: rgba(var(--el-color-primary-rgb), 0.3);
      box-shadow: 
        1px 1px 3px rgba(0, 0, 0, 0.05),
        -1px -1px 3px rgba(255, 255, 255, 0.1),
        inset 1px 1px 3px rgba(0, 0, 0, 0.02),
        inset -1px -1px 3px rgba(255, 255, 255, 0.05);
      
      .el-icon {
        animation: float-icon 0.6s ease-out;
      }
    }
  }

  /* 滚动时图标浮动动画 */
  @keyframes float-icon {
    0% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-6px);
    }
    100% {
      transform: translateY(0);
    }
  }

  /* 为卡池项添加缓动过渡 */
  .pool-item {
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  /* 滚动条动画 */
  .pools-scrollbar {
    :deep(.el-scrollbar__bar) {
      opacity: 0;
      transition: opacity 0.3s ease, background-color 0.3s ease;
    }
    
    &:hover :deep(.el-scrollbar__bar),
    &:focus :deep(.el-scrollbar__bar) {
      opacity: 0.6;
    }
    
    &:hover :deep(.el-scrollbar__thumb),
    &:focus :deep(.el-scrollbar__thumb) {
      background-color: var(--el-color-primary-light-5);
    }
  }

  /* 重命名对话框样式 */
  .rename-pool-dialog {
    :deep(.el-dialog) {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
    
    :deep(.el-dialog__header) {
      margin: 0;
      padding: 20px 24px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color);
    }
    
    :deep(.el-dialog__body) {
      padding: 24px;
    }
    
    :deep(.el-dialog__footer) {
      padding: 16px 24px;
      border-top: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color);
    }
    
    :deep(.el-input__wrapper) {
      box-shadow: 
        0 0 0 1px rgba(var(--el-border-color-rgb), 0.2) inset,
        2px 2px 5px rgba(0, 0, 0, 0.03),
        -2px -2px 5px rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
      border-radius: 8px;
      
      &:hover {
        box-shadow: 
          0 0 0 1px rgba(var(--el-color-primary-rgb), 0.2) inset,
          2px 2px 5px rgba(0, 0, 0, 0.03),
          -2px -2px 5px rgba(255, 255, 255, 0.1);
      }
      
      &.is-focus {
        box-shadow: 
          0 0 0 1px var(--el-color-primary) inset,
          2px 2px 6px rgba(var(--el-color-primary-rgb), 0.1),
          -2px -2px 6px rgba(255, 255, 255, 0.2);
      }
    }
  }

  /* 修复卡片重叠时的频闪问题 */
  .scene-card {
    &:hover {
      /* 移除可能导致频闪的transform或z-index变化 */
      transform: none !important;
      /* 仅添加阴影效果，不改变卡片位置 */
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1) !important;
      
      /* 保留高亮效果但不影响层叠顺序 */
      border-color: rgba(var(--el-color-primary-rgb), 0.3) !important;
      
      /* 使用filter增强亮度而不是改变z-index */
      filter: brightness(1.02);
    }
    
    /* 确保激活状态不会改变z-index，仅改变视觉样式 */
    &.active {
      border-color: var(--el-color-primary) !important;
      box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2) !important;
      filter: brightness(1.05);
    }
    
    /* 为拖动状态单独设置样式，避免与hover冲突 */
    &.dragging {
      /* 拖动时才提高z-index */
      z-index: 1000 !important;
      transform: scale(1.02) !important;
      opacity: 0.9;
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15) !important;
      pointer-events: none;
    }
  }

  /* 增加过渡延迟，避免鼠标快速移动时的频闪 */
  .scene-card-wrapper {
    transition: z-index 0s 0.05s !important;
  }

  /* 确保卡片内容不影响z-index层叠逻辑 */
  .scene-card-content {
    pointer-events: none; /* 防止内容元素接收鼠标事件 */
  }

  /* 修复卡片交互区域，确保能正确接收拖拽事件 */
  .scene-card-interaction-area {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10; /* 提高z-index，确保能接收到鼠标事件 */
    cursor: move;
    background: transparent; /* 透明背景 */
    pointer-events: auto; /* 确保可以接收鼠标事件 */

    /* 原生应用样式 - 禁用选择但允许内容交互 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* 确保卡片内容仍然可见 */
  .scene-card-wrapper .el-card {
    pointer-events: none; /* 禁止卡片内容接收鼠标事件，让事件穿透到交互区域 */

    /* 卡片内容文本应该可以选择 */
    .el-card__body {
      user-select: text;
      -webkit-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      pointer-events: auto;
    }

    /* 卡片标题也应该可以选择 */
    .el-card__header {
      user-select: text;
      -webkit-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      pointer-events: auto;
    }
  }

  /* 但保留卡片内按钮的点击能力 */
  .scene-card-wrapper .el-card .el-button {
    pointer-events: auto; /* 恢复按钮的鼠标事件 */
    position: relative; /* 确保按钮在交互层之上 */
    z-index: 15; /* 高于交互区域 */
    user-select: none; /* 按钮文字不应该被选择 */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* 实际的无限画布容器 */
  .infinite-canvas {
    position: absolute;
    width: 10000px; /* 设置足够大的初始尺寸 */
    height: 10000px;
    transform-origin: 0 0;
    cursor: default;
    
    /* 添加平滑过渡，但仅对缩放应用，拖动保持即时 */
    transition: transform 0.1s ease-out;
    transition-property: transform;
    
    /* 禁用过渡当画布被拖动时 */
    &.dragging {
      transition: none;
    }
  }

  /* 画布控制区域 */
  .canvas-controls {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 100;
    padding: 8px;
    background: var(--el-bg-color);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    border: 1px solid var(--el-border-color-lighter);
    backdrop-filter: blur(8px);

    /* 原生应用样式 - 禁用选择和拖拽 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-drag: none;

    .control-btn {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      background: transparent;

      &:hover {
        background: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }

      .el-icon {
        font-size: 18px;
      }
    }

    .zoom-display {
      padding: 8px 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 500;
      color: var(--el-text-color-regular);
      background: var(--el-fill-color-light);
      border-radius: 6px;
      min-width: 40px;
      text-align: center;
    }
  }

  /* 右键拖动提示 */
  .drag-hint {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(var(--el-bg-color-rgb), 0.8);
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 100;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(4px);

    /* 原生应用样式 - 禁用选择 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    
    &.visible {
      opacity: 1;
    }
    
    .key-hint {
      background: var(--el-bg-color);
      padding: 2px 6px;
      border-radius: 4px;
      border: 1px solid var(--el-border-color-lighter);
      margin: 0 2px;
    }
  }









  /* 添加以下样式允许删除按钮点击穿透 */
  :deep(.el-button) {
    position: relative;
    z-index: 2; /* 确保按钮在交互区域之上 */
    pointer-events: auto; /* 确保按钮可以接收点击事件 */
  }

  .scene-card-interaction-area {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    
    /* 允许卡片内部按钮接收点击事件 */
    :deep(.el-card__header) {
      pointer-events: auto;
    }
    
    :deep(.card-actions) {
      pointer-events: auto;
    }
  }

  /* 自定义对话框样式 - 确保在全屏模式下也能正确显示 */
  .custom-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999999; /* 超高的z-index确保在全屏模式下也能显示 */
    padding: 20px;

    /* 原生应用样式 - 禁用选择和拖拽 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-drag: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  .custom-dialog {
    background: var(--el-bg-color);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 100%;
    max-width: 450px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    /* 对话框内容可以交互 */
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;

    /* 但标题栏不能选择 */
    .custom-dialog-header {
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
    }

    /* 按钮不能选择 */
    .custom-dialog-footer button {
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
    }
  }

  .custom-dialog-medium {
    max-width: 600px;
  }

  .custom-dialog-large {
    max-width: 1200px;
  }

  .custom-dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color);
    background: var(--el-bg-color);
  }

  .custom-dialog-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .custom-dialog-close {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--el-text-color-regular);
    cursor: pointer;
    padding: 4px;
    line-height: 1;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .custom-dialog-close:hover {
    background-color: var(--el-color-danger-light-9);
    color: var(--el-color-danger);
  }

  .custom-dialog-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
  }

  .custom-dialog-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--el-border-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background: var(--el-bg-color);
  }


  </style>
