<template>
  <div class="story-inspiration-container">
    <!-- 数据加载指示器 -->
    <div v-if="!dataReady && inspirationStore.isLoading" class="data-loading-overlay">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <p>正在加载灵感卡池数据...</p>
    </div>

    <!-- 数据错误提示 -->
    <div v-else-if="inspirationStore.hasError" class="data-error-overlay">
      <el-icon class="error-icon"><WarningFilled /></el-icon>
      <p>加载灵感卡池数据失败</p>
      <p class="error-message">{{ inspirationStore.error }}</p>
      <div class="error-actions">
        <el-button type="primary" @click="retryLoading">重试</el-button>
        <el-button type="info" @click="showDebugInfo">诊断信息</el-button>
        <el-button type="danger" @click="resetInspirationData">重置数据</el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div v-else class="app-content">
      <!-- 顶部标题栏 -->
      <div class="app-header">
        <h2>故事灵感</h2>
        <div class="header-actions">
          <el-button type="primary" @click="generateCombination" size="default" class="action-button primary-action">
            <el-icon><CopyDocument /></el-icon>
            <span>生成组合</span>
          </el-button>
          <el-button @click="randomSelect" size="default" class="action-button secondary-action">
            <el-icon><Refresh /></el-icon>
            <span>随机灵感</span>
          </el-button>
          <el-button @click="resetSelection" size="default" class="action-button danger-action">
            <el-icon><Delete /></el-icon>
            <span>重置选择</span>
          </el-button>
        </div>
      </div>

      <!-- 原型组合内容 -->
      <div class="archetype-content">
            <!-- 上半部分：选择区域 -->
            <div class="selection-container">
              <div class="selection-wrapper">
                <div
                  v-for="(config, categoryKey) in categories"
                  :key="categoryKey"
                  class="category-column">
                  <div class="category-header" :class="{'category-active': selectedElements[categoryKey].length > 0}">
                    <div class="category-title-wrapper">
                      <el-icon class="category-icon"><component :is="config.icon" /></el-icon>
                      <span class="category-title">{{ config.name }}</span>
                    </div>

                    <div class="category-functions">
                      <select
                        v-model="selectionCounts[categoryKey]"
                        class="count-select native-select">
                        <option
                          v-for="n in config.maxCount"
                          :key="n"
                          :value="n">
                          {{ n }}
                        </option>
                      </select>
                      <el-button
                        class="edit-button"
                        size="small"
                        type="primary"
                        @click="showElementEditor(categoryKey)"
                        circle>
                        <el-icon><Edit /></el-icon>
                      </el-button>
                    </div>
                  </div>

                  <div class="category-items-wrapper">
                    <div class="category-items-container native-scroll">
                      <template v-if="getElementsByCategory(categoryKey).length > 0">
                        <div
                          v-for="element in getElementsByCategory(categoryKey)"
                          :key="element.title"
                          class="category-item"
                          :class="{ 'item-selected': selectedElements[categoryKey].includes(element.title) }"
                          @click="toggleElementSelection(categoryKey, element.title)"
                        >
                          <el-checkbox
                            :model-value="selectedElements[categoryKey].includes(element.title)"
                            @update:model-value="(val) => updateElementSelection(categoryKey, element.title, val)"
                            @click.stop
                          ></el-checkbox>
                          <span class="item-title">{{ element.title }}</span>
                        </div>
                      </template>
                      <el-empty v-else description="暂无内容" :image-size="50"></el-empty>
                    </div>
                  </div>
                </div>

                <!-- 空状态提示 -->
                <el-empty v-if="Object.keys(categories).length === 0" description="暂无分类，请检查数据加载"></el-empty>
              </div>
            </div>

            <!-- 下半部分：结果展示区域 -->
            <div class="result-section tech-panel">
              <div class="result-header card-pool-header" @click="triggerInspirationBurst" title="点击获取灵感爆发！连续点击有惊喜哦~">
                <div class="section-title">
                  <div class="tech-lines"></div>
                  <el-icon class="result-icon"><Star /></el-icon>
                  <h3>✨ 灵感卡池抽取结果</h3>
                  <div class="title-sparkles">
                    <span class="sparkle">✦</span>
                    <span class="sparkle">✧</span>
                    <span class="sparkle">✦</span>
                  </div>
                </div>
                <div class="interaction-tips">
                  <div class="fixed-elements-indicator" v-if="hasFixedElements">
                    <el-icon class="lock-icon"><Lock /></el-icon>
                    <span class="fixed-count">已固定: {{ totalFixedElements }}个</span>
                    <el-tooltip content="点击气泡可固定/解除固定，固定的元素在随机时将被保留">
                      <el-icon class="info-icon"><InfoFilled /></el-icon>
                    </el-tooltip>
                  </div>
                  <el-tooltip placement="top" effect="light">
                    <template #content>
                      <div style="text-align: left;">
                        <div><el-icon><Mouse /></el-icon> <b>单击</b>: 固定/解除固定气泡</div>
                        <div><el-icon><DCaret /></el-icon> <b>双击</b>: 查看元素详情</div>
                      </div>
                    </template>
                    <el-icon class="tips-icon" :size="20"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </div>

              <!-- 结果展示区域 -->
              <div class="result-scroll-container">
                <div class="result-container-horizontal">
                  <template v-if="combinationResult">
                    <!-- 水平排列的卡池 -->
                    <div
                      v-for="(config, categoryKey) in categories"
                      :key="categoryKey"
                      class="card-pool-column"
                      v-show="combinationResult[categoryKey] && combinationResult[categoryKey].length > 0">

                      <!-- 卡池标题 -->
                      <div class="pool-header" :class="`pool-${config.color}`">
                        <div class="pool-icon">
                          <el-icon><component :is="config.icon" /></el-icon>
                        </div>
                        <div class="pool-info">
                          <span class="pool-title">{{ config.name }}</span>
                          <span class="pool-count">{{ combinationResult[categoryKey].length }}个元素</span>
                        </div>
                      </div>

                      <!-- 气泡元素区域 -->
                      <div class="bubble-container" :class="`pool-${config.color}`">
                        <div class="bubble-row">
                          <div
                            v-for="(element, index) in combinationResult[categoryKey]"
                            :key="element"
                            class="element-bubble"
                            :class="[
                              `bubble-${config.color}`,
                              { 'bubble-fixed': fixedElements[categoryKey][element] }
                            ]"
                            :style="{ animationDelay: `${index * 0.1}s` }"
                            @click="toggleElementFixed(categoryKey, element)"
                            @dblclick="showElementDetail(categoryKey, element)"
                          >
                            <el-icon v-if="fixedElements[categoryKey][element]" class="bubble-lock-icon"><Lock /></el-icon>
                            <span class="bubble-text">{{ element }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>

                  <!-- 空状态提示 -->
                  <div v-else class="empty-result-horizontal">
                    <el-empty description="请从上方选择元素并点击「生成组合」或「随机灵感」按钮" :image-size="80">
                      <template #image>
                        <el-icon class="empty-icon"><CopyDocument /></el-icon>
                      </template>
                    </el-empty>
                  </div>
                </div>
              </div>
            </div>
          </div>

    </div>

    <!-- 元素编辑对话框 -->
    <el-dialog
      v-model="elementEditorVisible"
      :title="getEditorTitle()"
      width="70%"
      class="element-editor-dialog"
      destroy-on-close="false"
      :append-to-body="true">

      <div class="editor-container">
        <div class="editor-toolbar">
          <el-button type="primary" size="default" @click="addNewElement">
            <el-icon><Plus /></el-icon> 添加新元素
          </el-button>
          <el-button type="success" size="default" @click="saveCustomElements">
            <el-icon><Check /></el-icon> 保存修改
          </el-button>
          <el-button size="default" @click="exportElements">
            <el-icon><Download /></el-icon> 导出配置
          </el-button>
          <el-button type="warning" size="default" @click="importElements">
            <el-icon><Upload /></el-icon> 导入配置
          </el-button>
        </div>

        <el-table :data="customElements" style="width: 100%" max-height="450px" border>
          <el-table-column label="标题" width="180">
            <template #default="{ row }">
              <el-input v-model="row.title" placeholder="输入标题" size="default" />
            </template>
          </el-table-column>

          <el-table-column label="描述">
            <template #default="{ row }">
              <el-input v-model="row.description" type="textarea" placeholder="输入描述" :rows="2" size="default" />
            </template>
          </el-table-column>

          <el-table-column label="情感走向" width="120">
            <template #default="{ row }">
              <el-select v-model="row.emotion" placeholder="选择走向" size="default">
                <el-option label="上升 ↑" value="↑" />
                <el-option label="下降 ↓" value="↓" />
                <el-option label="波动 ↓|↑" value="↓ | ↑" />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="示例" width="140">
            <template #default="{ row }">
              <el-popover
                placement="right"
                :width="350"
                trigger="click">
                <template #reference>
                  <el-button size="default">编辑 ({{ row.examples?.length || 0 }})</el-button>
                </template>
                <div class="examples-editor">
                  <div v-for="(example, index) in row.examples || []" :key="index" class="example-item">
                    <el-input v-model="row.examples[index]" placeholder="输入示例" size="default" />
                    <el-button type="danger" @click="removeExample(row, index)" size="small" circle>
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                  <el-button type="primary" @click="addExample(row)" size="default">
                    <el-icon><Plus /></el-icon> 添加示例
                  </el-button>
                </div>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button type="danger" @click="removeElement($index)" size="default" circle>
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 导入配置对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入配置"
      width="800px"
      class="import-config-dialog"
      :close-on-click-modal="false"
      :show-close="true"
      :append-to-body="true"
      :top="'5vh'">
      <div class="import-content">
        <el-collapse>
          <el-collapse-item>
            <template #title>
              <div class="format-hint-title">
                <el-icon><InfoFilled /></el-icon>
                <span>查看JSON格式示例</span>
              </div>
            </template>
            <div class="format-hint-content">
              <pre>[
  {
    "title": "元素标题",
    "description": "元素描述",
    "emotion": "↑",
    "examples": ["示例1", "示例2"]
  },
  {
    "title": "另一个元素",
    "description": "另一个描述",
    "emotion": "↓ | ↑",
    "examples": ["示例1"]
  }
]</pre>
            </div>
          </el-collapse-item>
        </el-collapse>

        <div class="import-options">
          <el-input
            v-model="importJsonContent"
            type="textarea"
            :rows="12"
            placeholder="请粘贴有效的JSON数据"
            class="import-input"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport">确认导入</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 元素详情对话框 -->
    <div
      v-if="elementDetailVisible"
      class="native-dialog-overlay"
      @click.self="closeElementDetail"
      @keydown.esc="closeElementDetail"
      tabindex="0">

      <div class="native-dialog" @click.stop>
        <!-- 头部 -->
        <div class="dialog-header">
          <div class="header-left">
            <div class="category-badge" :class="currentDetailCategory">
              <el-icon><component :is="categories[currentDetailCategory]?.icon || 'Document'" /></el-icon>
            </div>
            <div class="title-area">
              <span class="category-label">{{ categories[currentDetailCategory]?.name || '元素' }}</span>
              <h3 class="element-name">{{ currentDetailElement?.title }}</h3>
            </div>
          </div>

          <div class="header-right">
            <div
              v-if="currentDetailElement?.emotion"
              class="emotion-badge"
              :class="getEmotionType(currentDetailElement.emotion)">
              <el-icon><component :is="getEmotionIcon(currentDetailElement.emotion)" /></el-icon>
              <span>{{ getEmotionLabel(currentDetailElement.emotion) }}</span>
            </div>

            <button
              class="close-btn"
              @click="closeElementDetail"
              type="button">
              <el-icon><Close /></el-icon>
            </button>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="dialog-content" v-if="currentDetailElement">
          <div class="content-scroll">
            <!-- 描述 -->
            <div class="info-section">
              <div class="section-title">
                <el-icon><Document /></el-icon>
                <span>描述</span>
              </div>
              <p class="description">{{ currentDetailElement.description }}</p>
            </div>

            <!-- 示例 -->
            <div
              v-if="currentDetailElement.examples && currentDetailElement.examples.length > 0"
              class="info-section">
              <div class="section-title">
                <el-icon><List /></el-icon>
                <span>示例 ({{ currentDetailElement.examples.length }})</span>
              </div>
              <div class="examples-list">
                <div
                  v-for="(example, index) in currentDetailElement.examples"
                  :key="index"
                  class="example-item">
                  <span class="example-num">{{ index + 1 }}</span>
                  <span class="example-text">{{ example }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部 -->
        <div class="dialog-footer">
          <div class="footer-tip">
            <el-icon><InfoFilled /></el-icon>
            <span>双击标签查看详情，单击固定元素</span>
          </div>
          <button
            class="confirm-btn"
            @click="closeElementDetail">
            <el-icon><Check /></el-icon>
            <span>知道了</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive, nextTick, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CopyDocument, Refresh, Delete, Star, Sunrise, Connection, Key, TrendCharts, Plus, Check, Download, Upload, Edit, InfoFilled, Lock, Mouse, DCaret, Loading, WarningFilled, Moon, Sunny, Close, Document, List, ArrowUp, ArrowDown, Sort } from '@element-plus/icons-vue'

import { storyInspirationConfig } from '@/config/storyInspiration'
import { useConfigStore } from '@/stores/config'
import { useLoading } from '@/composables/useLoading'
import { useInspirationStore } from '@/stores/inspiration'

// 获取全局配置Store
const configStore = useConfigStore()

// 创建加载状态处理器
const { startLoading, stopLoading } = useLoading()



// 使用新的存储
const inspirationStore = useInspirationStore()

// 初始化卡池数据
const inspirationData = ref({
  categories: {},
  theme: [],
  volume: [],
  keyPoint: [],
  technique: []
})

// 替换原来的初始化配置方法
const loadInspirationData = async () => {
  try {
    startLoading('正在加载灵感卡池...')
    // 调用后端API获取故事灵感数据
    const response = await window.pywebview.api.get_story_inspiration()
    if (response.success && response.data) {
      inspirationData.value = response.data
    } else {
      console.error('加载灵感卡池失败:', response.message)
      ElMessage.error('加载灵感卡池失败: ' + response.message)
    }
  } catch (error) {
    console.error('加载灵感卡池出错:', error)
    ElMessage.error('加载灵感卡池出错')
  } finally {
    stopLoading()
  }
}

// 替换原来的从配置中获取分类的computed
const categories = computed(() => {
  return inspirationStore.categories
})

// 选择状态
const selectedElements = reactive({
  theme: [],
  volume: [],
  keyPoint: [],
  technique: []
})

// 固定元素状态
const fixedElements = reactive({
  theme: {},
  volume: {},
  keyPoint: {},
  technique: {}
})

const combinationResult = ref(null)

// 监听selectedElements变化，自动更新结果区域
watch(selectedElements, (newValue) => {
  // 只有当至少有一个分类有选中元素时才更新结果
  const hasSelectedElements = Object.values(newValue).some(category => category.length > 0)
  if (hasSelectedElements) {
    // 更新结果区域，避免直接引用同一对象
    combinationResult.value = JSON.parse(JSON.stringify(newValue))

    // 检查是否需要显示滚动提示
    nextTick(() => {
      checkScrollHint()
    })
  }
}, { deep: true })

// 检查是否需要显示滚动提示
const checkScrollHint = () => {
  const container = document.querySelector('.result-scroll-container')
  if (container) {
    const hasScroll = container.scrollWidth > container.clientWidth
    if (hasScroll) {
      container.classList.add('has-scroll')
    } else {
      container.classList.remove('has-scroll')
    }
  }
}

// 修改选择个数配置的初始化方式
// 将初始值设为固定数字，避免未加载数据时的错误
const selectionCounts = reactive({
  theme: 2,
  volume: 4,
  keyPoint: 5,
  technique: 3
})

// 移除第二个重复声明的 selectionCounts
// 保留 watch 监听器以在数据加载后更新这些值
watch(() => inspirationStore.categories, (newCategories) => {
  if (newCategories) {
    // 使用可选链操作符避免undefined错误
    if (newCategories.theme?.defaultCount) selectionCounts.theme = newCategories.theme.defaultCount
    if (newCategories.volume?.defaultCount) selectionCounts.volume = newCategories.volume.defaultCount
    if (newCategories.keyPoint?.defaultCount) selectionCounts.keyPoint = newCategories.keyPoint.defaultCount
    if (newCategories.technique?.defaultCount) selectionCounts.technique = newCategories.technique.defaultCount
  }
}, { immediate: true })

// 替换getElementsByCategory方法
const getElementsByCategory = (category) => {
  return inspirationStore.getElementsByCategory(category)
}

// 获取问题标题
const getQuestionTitle = (type) => {
  return categories.value[type]?.name + '相关问题' || type
}

// 切换元素选择状态
const toggleElementSelection = (category, elementTitle) => {
  const isSelected = selectedElements[category].includes(elementTitle)
  updateElementSelection(category, elementTitle, !isSelected)
}

// 更新元素选择状态
const updateElementSelection = (category, elementTitle, value) => {
  if (value) {
    // 检查是否超过选择数量限制
    if (selectedElements[category].length >= selectionCounts[category]) {
      ElMessage.warning(`${categories.value[category]?.name || category} 最多只能选择 ${selectionCounts[category]} 个元素`)
      return
    }
    // 添加到选择列表
    if (!selectedElements[category].includes(elementTitle)) {
      selectedElements[category].push(elementTitle)
    }
  } else {
    // 从选择列表中移除
    const index = selectedElements[category].indexOf(elementTitle)
    if (index > -1) {
      selectedElements[category].splice(index, 1)
    }
  }
}

// 获取分类图标样式
const getCategoryIconStyle = (colorType) => {
  const colorMap = {
    primary: 'var(--el-color-primary)',
    success: 'var(--el-color-success)',
    warning: 'var(--el-color-warning)',
    danger: 'var(--el-color-danger)',
    info: 'var(--el-color-info)'
  }
  return {
    backgroundColor: colorMap[colorType] || 'var(--el-color-primary)'
  }
}

// 随机选择
const randomSelect = () => {
  // 检查数据是否已加载
  if (!inspirationStore.isLoaded) {
    ElMessage.warning('数据正在加载中，请稍候...')
    return
  }

  // 遍历所有分类
  Object.keys(categories.value).forEach(category => {
    // 获取该分类的元素
    const elements = getElementsByCategory(category)

    // 保留已固定的元素
    const fixedTitles = Object.keys(fixedElements[category]).filter(title => fixedElements[category][title])

    // 计算需要随机选择的数量
    const remainingCount = selectionCounts[category] - fixedTitles.length

    if (remainingCount <= 0) {
      // 如果固定的元素已经达到或超过总数，只保留固定元素（最多不超过总数）
      selectedElements[category] = fixedTitles.slice(0, selectionCounts[category])
    } else {
      // 过滤掉已固定的元素，从剩余元素中随机选择
      const availableElements = elements.filter(element => !fixedElements[category][element.title])
      const randomElements = getRandomElements(availableElements, remainingCount)

      // 合并固定元素和随机元素
      selectedElements[category] = [
        ...fixedTitles,
        ...randomElements.map(element => element.title)
      ]
    }
  })

  generateCombination()

  // 检查滚动提示
  nextTick(() => {
    checkScrollHint()
  })
}

// 获取随机元素
const getRandomElements = (array, count) => {
  const shuffled = [...array].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

// 生成组合
const generateCombination = () => {
  // 检查数据是否已加载
  if (!inspirationStore.isLoaded) {
    ElMessage.warning('数据正在加载中，请稍候...')
    return
  }

  // 验证是否每个类型都至少选择了一个元素
  const isValid = Object.keys(categories.value).every(category =>
    selectedElements[category].length > 0
  )

  if (!isValid) {
    ElMessage.warning('请确保每个类型都至少选择了一个元素')
    return
  }

  // 生成组合结果
  const formattedResult = Object.keys(categories.value).flatMap(category => [
    `【${categories.value[category].name}】`,
    ...formatElements(selectedElements[category], categories.value[category].name),
    ''
  ]).join('\n')

  // 复制到剪贴板
  window.pywebview.api.copy_to_clipboard(formattedResult)
    .then(() => {
      ElMessage.success({
        message: '组合已生成并复制到剪贴板',
        duration: 2000
      })
    })
    .catch(() => {
      ElMessage.warning({
        message: '复制到剪贴板失败，请手动复制',
        duration: 2000
      })
    })

  // 强制刷新结果区域
  combinationResult.value = JSON.parse(JSON.stringify(selectedElements))
  console.log('组合结果已生成:', combinationResult.value)

  // 检查滚动提示
  nextTick(() => {
    checkScrollHint()
  })
}

// 格式化元素
const formatElements = (elements, type) => {
  return elements.map((element, index) => {
    return `${index + 1}. ${element}`
  })
}

// 重置选择
const resetSelection = () => {
  Object.keys(categories.value).forEach(category => {
    selectedElements[category] = []
    // 同时清除固定状态
    fixedElements[category] = {}
  })
  // 清空结果区域
  combinationResult.value = null
}

// 添加点击计时器跟踪变量
const clickTimer = ref(null)
const pendingClick = ref(null)

// 切换元素固定状态 - 使用点击延迟解决单双击冲突
const toggleElementFixed = (category, elementTitle) => {
  // 清除之前的点击计时器（如果存在）
  if (clickTimer.value !== null) {
    clearTimeout(clickTimer.value)
    clickTimer.value = null

    // 如果当前点击与之前相同，说明是双击，不执行切换操作
    if (pendingClick.value &&
        pendingClick.value.category === category &&
        pendingClick.value.elementTitle === elementTitle) {
      pendingClick.value = null
      return
    }
  }

  // 保存当前点击信息并设置延迟
  pendingClick.value = { category, elementTitle }

  // 设置300ms延迟，等待是否有双击发生
  clickTimer.value = setTimeout(() => {
    // 延迟后执行切换操作（如果没有触发双击）
    if (pendingClick.value) {
      if (fixedElements[category][elementTitle]) {
        // 取消固定
        fixedElements[category][elementTitle] = false
      } else {
        // 固定元素
        fixedElements[category][elementTitle] = true
      }

      // 清除点击状态
      pendingClick.value = null
      clickTimer.value = null
    }
  }, 300) // 300ms是一个合理的双击时间窗口
}

// 显示元素详情
const elementDetailVisible = ref(false)
const currentDetailCategory = ref('')
const currentDetailElement = ref(null)

const showElementDetail = (category, elementTitle) => {
  // 清除点击计时器，防止触发固定操作
  if (clickTimer.value !== null) {
    clearTimeout(clickTimer.value)
    clickTimer.value = null
    pendingClick.value = null
  }

  const elements = getElementsByCategory(category)
  const element = elements.find(el => el.title === elementTitle)

  if (element) {
    currentDetailCategory.value = category
    currentDetailElement.value = element
    elementDetailVisible.value = true
  }
}

const closeElementDetail = () => {
  elementDetailVisible.value = false
  currentDetailCategory.value = ''
  currentDetailElement.value = null
}

// 获取情感类型
const getEmotionType = (emotion) => {
  if (emotion === '↑') return 'up'
  if (emotion === '↓') return 'down'
  if (emotion === '↓ | ↑') return 'wave'
  return 'neutral'
}

// 获取情感图标
const getEmotionIcon = (emotion) => {
  if (emotion === '↑') return 'ArrowUp'
  if (emotion === '↓') return 'ArrowDown'
  if (emotion === '↓ | ↑') return 'Sort'
  return 'Connection'
}

// 获取情感标签
const getEmotionLabel = (emotion) => {
  if (emotion === '↑') return '上升'
  if (emotion === '↓') return '下降'
  if (emotion === '↓ | ↑') return '波动'
  return '平稳'
}

// 计算是否有固定元素
const hasFixedElements = computed(() => {
  return Object.values(fixedElements).some(categoryFixed =>
    Object.values(categoryFixed).some(isFixed => isFixed)
  )
})

// 计算总固定元素数量
const totalFixedElements = computed(() => {
  return Object.values(fixedElements).reduce((total, categoryFixed) => {
    return total + Object.values(categoryFixed).filter(isFixed => isFixed).length
  }, 0)
})

// 元素编辑器相关
const elementEditorVisible = ref(false)
const currentEditType = ref(null) // 当前正在编辑的类别类型
const customElements = ref([])

// 添加缓存系统
const editorCache = reactive({
  theme: null,
  character: null,
  plot: null,
  technique: null
})

// 优化后的打开编辑对话框函数
const showElementEditor = (type) => {
  // 先设置当前编辑类型
  currentEditType.value = type

  // 显示对话框（先让用户看到UI反馈）
  elementEditorVisible.value = true

  // 创建加载指示器
  const loadingInstance = ElMessage({
    message: '正在准备编辑器数据...',
    type: 'info',
    duration: 0
  })

  // 使用缓存或异步加载数据
  nextTick(() => {
    try {
      // 检查是否有缓存
      if (editorCache[type]) {
        console.log('使用缓存数据')
        customElements.value = editorCache[type]
        loadingInstance.close()
        return
      }

      // 无缓存时异步处理数据
      setTimeout(() => {
        let sourceData = getElementsByCategory(type)

        // 优化复制方式
        if (Array.isArray(sourceData)) {
          customElements.value = sourceData.map(item => ({...item}))

          // 保存到缓存
          editorCache[type] = [...customElements.value]
        } else {
          customElements.value = []
        }

        loadingInstance.close()
      }, 100)
    } catch (error) {
      console.error('加载编辑器数据失败:', error)
      loadingInstance.close()
      ElMessage.error('加载数据失败，请重试')
    }
  })
}

// 编辑对话框标题
const getEditorTitle = () => {
  return categories.value[currentEditType.value]?.name ?
    `编辑${categories.value[currentEditType.value].name}元素` :
    '编辑元素'
}

// 添加新元素
const addNewElement = () => {
  customElements.value.push({
    title: '新元素',
    description: '请输入描述',
    emotion: '↑',
    examples: ['请添加示例']
  })
}

const removeElement = (index) => {
  customElements.value.splice(index, 1)
}

const addExample = (element) => {
  if (!element.examples) {
    element.examples = []
  }
  element.examples.push('')
}

const removeExample = (element, index) => {
  if (element.examples) {
    element.examples.splice(index, 1)
  }
}

// 保存自定义元素方法
const saveCustomElements = async () => {
  try {
    startLoading('正在保存灵感元素...')
    const category = currentEditType.value // 使用现有的类别变量

    if (!category) {
      ElMessage.warning('没有选择灵感类别')
      return
    }

    console.log(`保存${categories.value[category]?.name || category}元素`, customElements.value)

    // 保存到存储
    await inspirationStore.saveInspirationCategory(category, customElements.value)

    // 成功提示
    ElMessage({
      type: 'success',
      message: `灵感类别 ${category} 保存成功`,
      duration: 2000
    })

    // 更新缓存
    editorCache[category] = [...customElements.value]

    // 关闭编辑器
    elementEditorVisible.value = false
  } catch (error) {
    // 错误处理
    console.error('保存自定义元素失败:', error)
    ElMessage.error(`保存失败: ${error.message}`)
  } finally {
    stopLoading()
  }
}

// 导出配置
const exportElements = () => {
  try {
    // 美化JSON格式，使用2个空格缩进
    const dataStr = JSON.stringify(customElements.value, null, 2)

    // 复制到剪贴板
    window.pywebview.api.copy_to_clipboard(dataStr)
      .then(() => {
        ElMessage.success(`已复制 ${customElements.value.length} 个${categories.value[currentEditType.value]?.name || ''}元素到剪贴板`)
      })
      .catch((error) => {
        console.error('复制到剪贴板失败:', error)
        ElMessage.error('复制到剪贴板失败，请检查浏览器权限')

        // 备用复制方法
        try {
          const textarea = document.createElement('textarea')
          textarea.value = dataStr
          textarea.style.position = 'fixed'
          textarea.style.opacity = '0'
          document.body.appendChild(textarea)
          textarea.select()
          const success = document.execCommand('copy')
          document.body.removeChild(textarea)

          if (success) {
            ElMessage.success('使用备用方法复制成功')
          } else {
            ElMessage.error('复制失败，请手动复制')
          }
        } catch (e) {
          console.error('备用复制方法失败:', e)
          ElMessage.error('复制失败，请手动复制')
        }
      })

    console.log('导出的元素数量:', customElements.value.length)
  } catch (error) {
    console.error('导出失败', error)
    ElMessage.error('导出失败：' + error.message)
  }
}

// 导入配置相关
const importDialogVisible = ref(false)
const importJsonContent = ref('')

// 显示导入对话框
const importElements = () => {
  importDialogVisible.value = true
  importJsonContent.value = ''
}

// 确认导入
const confirmImport = async () => {
  try {
    // 验证数据
    if (!importJsonContent.value.trim()) {
      ElMessage.warning('请输入JSON数据')
      return
    }

    // 解析JSON
    let jsonData
    try {
      jsonData = JSON.parse(importJsonContent.value)
    } catch (error) {
      ElMessage.error('JSON格式无效，请检查您的输入')
      return
    }

    // 验证数据格式
    if (!Array.isArray(jsonData)) {
      ElMessage.error('导入失败：数据必须是数组格式')
      return
    }

    // 验证每个元素是否有必要的字段
    for (const item of jsonData) {
      if (!item.title || !item.description) {
        ElMessage.error('导入失败：数据中有元素缺少标题或描述字段')
        return
      }
    }

    // 更新数据
    customElements.value = jsonData

    // 更新到缓存
    if (currentEditType.value) {
      editorCache[currentEditType.value] = [...jsonData]
    }

    // 关闭对话框并提示成功
    importDialogVisible.value = false
    ElMessage.success(`成功导入 ${jsonData.length} 个元素`)

    console.log('导入的元素数量:', jsonData.length)
  } catch (error) {
    console.error('导入失败', error)
    ElMessage.error('导入失败：' + error.message)
  }
}





// 添加数据就绪标志
const dataReady = ref(false)

// 页面加载时预加载常用分类数据
onMounted(async () => {
  try {
    startLoading('正在加载灵感卡池...')
    await inspirationStore.loadInspirationData()
    dataReady.value = true

    // 数据加载成功后初始化选择计数
    const cats = inspirationStore.categories
    if (cats) {
      if (cats.theme?.defaultCount) selectionCounts.theme = cats.theme.defaultCount
      if (cats.volume?.defaultCount) selectionCounts.volume = cats.volume.defaultCount
      if (cats.keyPoint?.defaultCount) selectionCounts.keyPoint = cats.keyPoint.defaultCount
      if (cats.technique?.defaultCount) selectionCounts.technique = cats.technique.defaultCount
    }
  } catch (error) {
    console.error('加载灵感卡池失败:', error)
    ElMessage.error('加载灵感卡池出错，请刷新页面重试')
  } finally {
    stopLoading()
  }

  // 后台预加载各分类数据到缓存
  setTimeout(() => {
    ['theme', 'volume', 'keyPoint', 'technique'].forEach(type => {
      if (!editorCache[type]) {
        let data = inspirationStore.getElementsByCategory(type)
        if (Array.isArray(data)) {
          editorCache[type] = data.map(item => ({...item}))
        }
      }
    })
    console.log('编辑器数据预加载完成')
  }, 1000)
})

// 添加重试加载功能
const retryLoading = async () => {
  try {
    startLoading('正在重新加载灵感卡池...')
    await inspirationStore.loadInspirationData()
    dataReady.value = true

    // 更新选择计数
    const cats = inspirationStore.categories
    if (cats) {
      if (cats.theme?.defaultCount) selectionCounts.theme = cats.theme.defaultCount
      if (cats.volume?.defaultCount) selectionCounts.volume = cats.volume.defaultCount
      if (cats.keyPoint?.defaultCount) selectionCounts.keyPoint = cats.keyPoint.defaultCount
      if (cats.technique?.defaultCount) selectionCounts.technique = cats.technique.defaultCount
    }

    ElMessage.success('数据加载成功')
  } catch (error) {
    console.error('重新加载灵感卡池失败:', error)
    ElMessage.error('重新加载失败，请检查网络连接')
  } finally {
    stopLoading()
  }
}

// 显示调试信息
const showDebugInfo = () => {
  ElMessageBox.alert(
    `错误信息: ${inspirationStore.error}\n
    API状态: ${window.pywebview ? '可用' : '不可用'}\n
    图书控制器: ${window.pywebview?.api?.book_controller ? '可用' : '不可用'}`,
    '诊断信息',
    { type: 'warning' }
  )
}

// 灵感爆发效果
const burstClickCount = ref(0)
const burstClickTimer = ref(null)

const triggerInspirationBurst = (event) => {
  const header = event.currentTarget

  // 增加点击计数
  burstClickCount.value++

  // 清除之前的计时器
  if (burstClickTimer.value) {
    clearTimeout(burstClickTimer.value)
  }

  // 设置新的计时器，2秒后重置计数
  burstClickTimer.value = setTimeout(() => {
    burstClickCount.value = 0
  }, 2000)

  // 检查是否触发超级爆发（连续点击5次）
  if (burstClickCount.value >= 5) {
    // 超级灵感爆发！
    header.classList.add('inspiration-burst', 'super-burst')
    ElMessage({
      message: '🎆 超级灵感爆发！创意宇宙大爆炸！！！',
      type: 'warning',
      duration: 3000,
      showClose: false
    })

    // 重置计数
    burstClickCount.value = 0

    // 移除超级爆发类
    setTimeout(() => {
      header.classList.remove('inspiration-burst', 'super-burst')
    }, 2000)

    return
  }

  // 普通灵感爆发
  header.classList.add('inspiration-burst')

  // 播放一些有趣的提示消息
  const inspirationMessages = [
    '✨ 灵感正在涌现...',
    '🌟 创意火花四溅！',
    '💡 想象力爆发中...',
    '🎨 艺术灵感降临！',
    '📚 故事元素正在重组...',
    '🔮 神秘的创作能量...',
    '🌈 彩虹般的创意光芒！',
    '⭐ 星辰般的灵感闪烁...'
  ]

  const randomMessage = inspirationMessages[Math.floor(Math.random() * inspirationMessages.length)]
  ElMessage({
    message: randomMessage,
    type: 'success',
    duration: 2000,
    showClose: false
  })

  // 1.5秒后移除类
  setTimeout(() => {
    header.classList.remove('inspiration-burst')
  }, 1500)
}

// 重置数据功能
const resetInspirationData = async () => {
  try {
    if (!confirm('确定要重置灵感卡池数据吗？这将删除所有自定义内容。')) {
      return
    }

    startLoading('正在重置灵感卡池数据...')

    // 从备份/配置中读取初始数据
    const defaultData = {
      categories: {
        theme: {
          name: "主题层",
          description: "故事的核心主题与情感基调",
          icon: "Sunrise",
          color: "primary",
          defaultCount: 2,
          maxCount: 5
        },
        volume: {
          name: "卷级结构",
          description: "故事的大纲架构与发展脉络",
          icon: "Connection",
          color: "success",
          defaultCount: 4,
          maxCount: 8
        },
        keyPoint: {
          name: "关键点",
          description: "故事中的重要转折与关键节点",
          icon: "Key",
          color: "warning",
          defaultCount: 5,
          maxCount: 8
        },
        technique: {
          name: "技法卡",
          description: "用于优化剧情的各种写作技巧",
          icon: "TrendCharts",
          color: "danger",
          defaultCount: 3,
          maxCount: 5
        }
      },
      theme: [],
      volume: [],
      keyPoint: [],
      technique: []
    }

    // 保存默认数据
    await inspirationStore.saveInspirationData(defaultData)

    // 重新加载
    await inspirationStore.loadInspirationData()
    dataReady.value = true

    ElMessage.success('灵感卡池数据已重置')
  } catch (error) {
    console.error('重置数据失败:', error)
    ElMessage.error('重置数据失败: ' + error.message)
  } finally {
    stopLoading()
  }
}



</script>

<style scoped>
.story-inspiration-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
  position: relative;
  overflow: hidden;
}

/* 科技感背景动效 */
.story-inspiration-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    /* 动态网格 */
    linear-gradient(90deg, rgba(64, 158, 255, 0.02) 1px, transparent 1px),
    linear-gradient(0deg, rgba(64, 158, 255, 0.02) 1px, transparent 1px),
    /* 径向渐变光晕 */
    radial-gradient(circle at 20% 20%, rgba(64, 158, 255, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(103, 194, 58, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(230, 162, 60, 0.02) 0%, transparent 50%);
  background-size:
    50px 50px,
    50px 50px,
    800px 800px,
    600px 600px,
    400px 400px;
  animation: techBackground 8s ease-in-out infinite, gridMove 20s linear infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes gridMove {
  0% {
    background-position: 0 0, 0 0, 0 0, 0 0, 0 0;
  }
  100% {
    background-position: 50px 50px, 50px 50px, 100px 100px, -100px 100px, 50px -50px;
  }
}

@keyframes techBackground {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* 扫描线效果 */
.story-inspiration-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--el-color-primary) 50%,
    transparent 100%);
  animation: scanLine 4s linear infinite;
  z-index: 1;
  pointer-events: none;
}

@keyframes scanLine {
  0% {
    left: -100%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* 数据加载和错误状态 */
.data-loading-overlay,
.data-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--el-bg-color);
  z-index: 100;
}

.loading-icon,
.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--el-color-primary);
}

.error-icon {
  color: var(--el-color-danger);
}

.error-message {
  color: var(--el-text-color-secondary);
  margin: 8px 0;
}

.error-actions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
}

/* 主要内容区域 */
.app-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 顶部标题栏 */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  flex-shrink: 0;
  position: relative;
  z-index: 10;
}

/* 标题栏科技感背景 */
.app-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(64, 158, 255, 0.05) 25%,
    rgba(103, 194, 58, 0.05) 50%,
    rgba(230, 162, 60, 0.05) 75%,
    transparent 100%);
  background-size: 200% 100%;
  animation: headerFlow 6s linear infinite;
  z-index: -1;
}

@keyframes headerFlow {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 0%;
  }
}

.app-header h2 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 20px;
  font-weight: 600;
  position: relative;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  animation: titleGlow 5s ease-in-out infinite;
}

/* 标题发光效果 */
@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  50% {
    text-shadow:
      0 1px 2px rgba(0, 0, 0, 0.1),
      0 0 10px rgba(64, 158, 255, 0.3);
  }
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 操作按钮样式 */
.action-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  border: 1px solid transparent;
  min-width: 100px;
  height: 36px;
  position: relative;
  overflow: hidden;
}

/* 按钮科技感光效 */
.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%);
  transition: left 0.5s ease;
  z-index: 1;
}

.action-button:hover::before {
  left: 100%;
}

/* 主要操作按钮脉冲效果 */
.primary-action {
  animation: primaryPulse 4s ease-in-out infinite;
}

@keyframes primaryPulse {
  0%, 100% {
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.06),
      0 0 0 0 rgba(64, 158, 255, 0);
  }
  50% {
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.12),
      0 0 0 4px rgba(64, 158, 255, 0.1);
  }
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.action-button:active {
  transform: translateY(0);
}

.action-button .el-icon {
  margin-right: 6px;
  font-size: 16px;
}

/* 主要操作按钮 */
.primary-action {
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2) 100%);
  border-color: var(--el-color-primary);
  color: white;
}

.primary-action:hover {
  background: linear-gradient(135deg, var(--el-color-primary-light-3) 0%, var(--el-color-primary) 100%);
  border-color: var(--el-color-primary-light-3);
}

/* 次要操作按钮 */
.secondary-action {
  background: linear-gradient(135deg, var(--el-color-success-light-8) 0%, var(--el-color-success-light-9) 100%);
  border-color: var(--el-color-success-light-5);
  color: var(--el-color-success);
}

.secondary-action:hover {
  background: linear-gradient(135deg, var(--el-color-success-light-7) 0%, var(--el-color-success-light-8) 100%);
  border-color: var(--el-color-success-light-3);
  color: var(--el-color-success-dark-2);
}

/* 危险操作按钮 */
.danger-action {
  background: linear-gradient(135deg, var(--el-color-warning-light-8) 0%, var(--el-color-warning-light-9) 100%);
  border-color: var(--el-color-warning-light-5);
  color: var(--el-color-warning);
}

.danger-action:hover {
  background: linear-gradient(135deg, var(--el-color-warning-light-7) 0%, var(--el-color-warning-light-8) 100%);
  border-color: var(--el-color-warning-light-3);
  color: var(--el-color-warning-dark-2);
}

/* 暗色主题适配 */
.dark .action-button {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark .action-button:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark .secondary-action {
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.15) 0%, rgba(103, 194, 58, 0.08) 100%);
  border-color: rgba(103, 194, 58, 0.3);
  color: var(--el-color-success-light-3);
}

.dark .secondary-action:hover {
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.25) 0%, rgba(103, 194, 58, 0.15) 100%);
  border-color: rgba(103, 194, 58, 0.5);
}

.dark .danger-action {
  background: linear-gradient(135deg, rgba(230, 162, 60, 0.15) 0%, rgba(230, 162, 60, 0.08) 100%);
  border-color: rgba(230, 162, 60, 0.3);
  color: var(--el-color-warning-light-3);
}

.dark .danger-action:hover {
  background: linear-gradient(135deg, rgba(230, 162, 60, 0.25) 0%, rgba(230, 162, 60, 0.15) 100%);
  border-color: rgba(230, 162, 60, 0.5);
}

/* 原生下拉框样式 */
.count-select.native-select {
  width: 70px;
  height: 28px;
  padding: 4px 8px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);
  font-size: 13px;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 6px center;
  background-size: 12px;
  padding-right: 24px;
  position: relative;
  overflow: hidden;
  animation: selectBreathing 4s ease-in-out infinite;
}

/* 下拉框呼吸效果 */
@keyframes selectBreathing {
  0%, 100% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }
}

/* 下拉框科技感边框 */
.count-select.native-select::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid transparent;
  border-radius: 6px;
  background: linear-gradient(45deg,
    transparent 0%,
    rgba(64, 158, 255, 0.2) 25%,
    transparent 50%,
    rgba(64, 158, 255, 0.2) 75%,
    transparent 100%);
  background-size: 200% 200%;
  animation: selectBorderScan 6s linear infinite;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.count-select.native-select:hover::before {
  opacity: 1;
}

@keyframes selectBorderScan {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 200%;
  }
}

.count-select.native-select:hover {
  border-color: var(--el-color-primary-light-5);
  background-color: var(--el-fill-color-light);
}

.count-select.native-select:focus {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
}

/* 暗色主题原生下拉框适配 */
.dark .count-select.native-select {
  background-color: var(--el-fill-color-dark);
  border-color: var(--el-border-color);
  color: var(--el-text-color-primary);
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23a8abb2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

.dark .count-select.native-select:hover {
  border-color: var(--el-color-primary-light-5);
  background-color: var(--el-fill-color);
}

.dark .count-select.native-select:focus {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 暗色主题科技感增强 */
.dark .story-inspiration-container::before {
  background:
    /* 更亮的网格线 */
    linear-gradient(90deg, rgba(64, 158, 255, 0.08) 1px, transparent 1px),
    linear-gradient(0deg, rgba(64, 158, 255, 0.08) 1px, transparent 1px),
    /* 更强的径向渐变 */
    radial-gradient(circle at 20% 20%, rgba(64, 158, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(103, 194, 58, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(230, 162, 60, 0.06) 0%, transparent 50%);
}



.dark .tech-panel::before {
  background: linear-gradient(90deg,
    var(--el-color-primary-light-3) 0%,
    var(--el-color-success-light-3) 33%,
    var(--el-color-warning-light-3) 66%,
    var(--el-color-danger-light-3) 100%);
}

.dark .bubble-container::before {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.03) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.02) 100%);
}

/* 暗色主题下的气泡边框和阴影 */
.dark .element-bubble {
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.3),
    0 1px 4px rgba(0, 0, 0, 0.2);
}

.dark .element-bubble:hover {
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.4),
    0 2px 6px rgba(0, 0, 0, 0.3);
}

/* 暗色主题下的彩色气泡阴影增强 */
.dark .bubble-primary {
  box-shadow:
    0 2px 8px rgba(64, 158, 255, 0.4),
    0 1px 4px rgba(0, 0, 0, 0.3);
}

.dark .bubble-success {
  box-shadow:
    0 2px 8px rgba(103, 194, 58, 0.4),
    0 1px 4px rgba(0, 0, 0, 0.3);
}

.dark .bubble-warning {
  box-shadow:
    0 2px 8px rgba(230, 162, 60, 0.4),
    0 1px 4px rgba(0, 0, 0, 0.3);
}

.dark .bubble-danger {
  box-shadow:
    0 2px 8px rgba(245, 108, 108, 0.4),
    0 1px 4px rgba(0, 0, 0, 0.3);
}

.dark .bubble-info {
  box-shadow:
    0 2px 8px rgba(144, 147, 153, 0.4),
    0 1px 4px rgba(0, 0, 0, 0.3);
}

/* 暗色主题下的结果头部特效 */
.dark .result-header {
  background: linear-gradient(135deg,
    var(--el-fill-color-light) 0%,
    rgba(255, 255, 255, 0.02) 50%,
    var(--el-fill-color-light) 100%);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

/* 暗色主题下也移除波浪效果 */

/* 暗色主题下的标题文字效果 */
.dark .section-title h3 {
  background: linear-gradient(45deg,
    #66b1ff 0%,
    #85ce61 25%,
    #ebb563 50%,
    #f78989 75%,
    #66b1ff 100%);
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}





/* 原型组合内容 */
.archetype-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 选择区域 */
.selection-container {
  flex: 0 0 45%;
  min-height: 0;
  padding: 20px;
  overflow-x: auto;
  overflow-y: hidden;
  background: var(--el-bg-color);
}

.selection-wrapper {
  display: flex;
  gap: 16px;
  height: 100%;
  width: 100%;
  align-items: stretch;
}

/* 分类列 */
.category-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(145deg,
    var(--el-bg-color) 0%,
    var(--el-fill-color-light) 30%,
    var(--el-bg-color) 100%);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  overflow: hidden;
  height: 100%;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  min-width: 200px;
  animation: categoryBreathing 6s ease-in-out infinite;
}

/* 分类列呼吸效果 */
@keyframes categoryBreathing {
  0%, 100% {
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.08),
      0 1px 4px rgba(0, 0, 0, 0.04),
      0 0 0 0 rgba(64, 158, 255, 0);
  }
  50% {
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.12),
      0 2px 8px rgba(0, 0, 0, 0.06),
      0 0 0 1px rgba(64, 158, 255, 0.1);
  }
}



.category-column:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.06);
}

.category-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: radial-gradient(
    ellipse at 50% 0%,
    var(--el-color-primary-light-9) 0%,
    transparent 70%
  );
  pointer-events: none;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

.category-column:hover::before {
  opacity: 0.6;
}

.category-header {
  padding: 16px 20px;
  background: linear-gradient(135deg,
    var(--el-fill-color-light) 0%,
    var(--el-fill-color) 100%);
  border-bottom: 1px solid var(--el-border-color-light);
  flex-shrink: 0;
  transition: all 0.3s ease;
  position: relative;
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.category-header.category-active {
  background: linear-gradient(135deg,
    var(--el-color-primary-light-9) 0%,
    var(--el-color-primary-light-8) 100%);
  border-bottom-color: var(--el-color-primary-light-7);
}

.category-title-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

.category-icon {
  color: var(--el-color-primary);
  font-size: 20px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  animation: iconPulse 3s ease-in-out infinite;
  position: relative;
}

/* 图标脉冲效果 */
@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 2px 8px rgba(64, 158, 255, 0.3));
  }
}

/* 图标光晕效果 */
.category-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30px;
  height: 30px;
  background: radial-gradient(circle, var(--el-color-primary-light-8) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: iconGlow 4s ease-in-out infinite;
  z-index: -1;
}

@keyframes iconGlow {
  0%, 100% {
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

.category-title {
  font-weight: 700;
  color: var(--el-text-color-primary);
  font-size: 17px;
  letter-spacing: 0.3px;
}



.category-functions {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
}



.edit-button {
  width: 30px;
  height: 30px;
}

/* 分类项目容器 */
.category-items-wrapper {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.category-items-container {
  height: 100%;
  padding: 12px;
  overflow-y: auto;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin-bottom: 6px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  position: relative;
  overflow: hidden;
}

/* 项目悬浮科技感效果 */
.category-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(64, 158, 255, 0.1) 50%,
    transparent 100%);
  transition: left 0.4s ease;
  z-index: 0;
}

.category-item:hover::before {
  left: 100%;
}

.category-item:hover {
  background: var(--el-fill-color-light);
  border-color: var(--el-color-primary-light-7);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.category-item.item-selected {
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-6);
  color: var(--el-color-primary);
  animation: selectedGlow 2s ease-in-out infinite;
  position: relative;
}

/* 选中项目发光效果 */
@keyframes selectedGlow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
  50% {
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}

/* 选中项目边框流动效果 */
.category-item.item-selected::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid var(--el-color-primary-light-5);
  border-radius: 6px;
  background: linear-gradient(45deg,
    transparent 0%,
    rgba(64, 158, 255, 0.1) 25%,
    transparent 50%,
    rgba(64, 158, 255, 0.1) 75%,
    transparent 100%);
  background-size: 200% 200%;
  animation: selectedBorderFlow 3s linear infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes selectedBorderFlow {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 200%;
  }
}

.item-title {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
}

/* 结果展示区域 */
.result-section {
  flex: 0 0 52%;
  margin: 20px 20px 20px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 250px;
}

.tech-panel {
  position: relative;
  background: linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-fill-color-light) 100%);
  animation: panelBreathing 8s ease-in-out infinite;
}

/* 结果面板呼吸效果 */
@keyframes panelBreathing {
  0%, 100% {
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      0 0 0 1px rgba(64, 158, 255, 0.1);
  }
}

/* 顶部彩色进度条动效 */
.tech-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
    var(--el-color-primary) 0%,
    var(--el-color-success) 33%,
    var(--el-color-warning) 66%,
    var(--el-color-danger) 100%);
  background-size: 200% 100%;
  animation: colorFlow 4s linear infinite;
  z-index: 2;
}

@keyframes colorFlow {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 0%;
  }
}

/* 数据流动效果 */
.tech-panel::after {
  content: '';
  position: absolute;
  top: 3px;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 48%, rgba(64, 158, 255, 0.02) 49%, rgba(64, 158, 255, 0.02) 51%, transparent 52%),
    linear-gradient(-45deg, transparent 48%, rgba(103, 194, 58, 0.02) 49%, rgba(103, 194, 58, 0.02) 51%, transparent 52%);
  background-size: 20px 20px, 30px 30px;
  animation: dataFlow 10s linear infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes dataFlow {
  0% {
    background-position: 0 0, 0 0;
  }
  100% {
    background-position: 20px 20px, -30px 30px;
  }
}

.result-header {
  padding: 16px 20px;
  background: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  animation: headerEntrance 1s ease-out, headerBreathe 4s ease-in-out infinite 1s;
  cursor: pointer;
  user-select: none;
}

/* 头部入场动画 */
@keyframes headerEntrance {
  0% {
    transform: translateY(-100%) scale(0.8);
    opacity: 0;
    filter: blur(10px);
  }
  50% {
    transform: translateY(10px) scale(1.05);
    opacity: 0.8;
    filter: blur(2px);
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
    filter: blur(0);
  }
}

/* 头部呼吸动画 */
@keyframes headerBreathe {
  0%, 100% {
    transform: translateY(0px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
  50% {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

/* 移除波浪效果，保持简洁 */

/* 头部悬停效果 */
.result-header:hover {
  animation: headerBreathe 4s ease-in-out infinite, headerHover 0.3s ease-out;
}

@keyframes headerHover {
  0% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-3px) scale(1.02);
  }
  100% {
    transform: translateY(-2px) scale(1.01);
  }
}

/* 移除波浪悬停效果 */

/* 悬停时增强标题动画 */
.result-header:hover .section-title {
  animation: titleBounce 1.5s ease-in-out infinite;
}

/* 悬停时加速图标旋转 */
.result-header:hover .result-icon {
  animation: iconSpin 2s ease-in-out infinite;
}

/* 悬停时增强星星效果 */
.result-header:hover .sparkle {
  animation: sparkleRotate 1s ease-in-out infinite, sparklePulse 0.8s ease-in-out infinite;
}

/* 灵感爆发效果 - 点击时触发 */
.result-header.inspiration-burst {
  animation: inspirationBurst 1.5s ease-out;
}

@keyframes inspirationBurst {
  0% {
    transform: scale(1);
    filter: brightness(1) saturate(1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
  20% {
    transform: scale(1.05);
    filter: brightness(1.3) saturate(1.5);
    box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
  }
  40% {
    transform: scale(0.98);
    filter: brightness(1.1) saturate(1.2);
    box-shadow: 0 12px 35px rgba(103, 194, 58, 0.3);
  }
  60% {
    transform: scale(1.02);
    filter: brightness(1.2) saturate(1.3);
    box-shadow: 0 10px 30px rgba(230, 162, 60, 0.3);
  }
  80% {
    transform: scale(0.99);
    filter: brightness(1.05) saturate(1.1);
    box-shadow: 0 6px 20px rgba(245, 108, 108, 0.3);
  }
  100% {
    transform: scale(1);
    filter: brightness(1) saturate(1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
}

/* 灵感爆发时的粒子效果 */
.result-header.inspiration-burst::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle,
    rgba(64, 158, 255, 0.6) 0%,
    rgba(103, 194, 58, 0.4) 25%,
    rgba(230, 162, 60, 0.3) 50%,
    rgba(245, 108, 108, 0.2) 75%,
    transparent 100%);
  border-radius: 50%;
  animation: burstParticles 1.5s ease-out;
  pointer-events: none;
  z-index: 10;
}

@keyframes burstParticles {
  0% {
    width: 0;
    height: 0;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
  30% {
    width: 200px;
    height: 200px;
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  100% {
    width: 400px;
    height: 400px;
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

/* 超级灵感爆发效果 */
.result-header.super-burst {
  animation: superInspirationBurst 2s ease-out;
}

@keyframes superInspirationBurst {
  0% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1) saturate(1) hue-rotate(0deg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
  10% {
    transform: scale(1.1) rotate(5deg);
    filter: brightness(1.5) saturate(2) hue-rotate(30deg);
    box-shadow: 0 15px 40px rgba(64, 158, 255, 0.5);
  }
  25% {
    transform: scale(0.9) rotate(-10deg);
    filter: brightness(1.8) saturate(2.5) hue-rotate(90deg);
    box-shadow: 0 20px 50px rgba(103, 194, 58, 0.6);
  }
  40% {
    transform: scale(1.15) rotate(8deg);
    filter: brightness(2) saturate(3) hue-rotate(180deg);
    box-shadow: 0 25px 60px rgba(230, 162, 60, 0.7);
  }
  55% {
    transform: scale(0.85) rotate(-15deg);
    filter: brightness(1.7) saturate(2.2) hue-rotate(270deg);
    box-shadow: 0 30px 70px rgba(245, 108, 108, 0.8);
  }
  70% {
    transform: scale(1.08) rotate(12deg);
    filter: brightness(1.4) saturate(1.8) hue-rotate(360deg);
    box-shadow: 0 20px 50px rgba(64, 158, 255, 0.6);
  }
  85% {
    transform: scale(0.95) rotate(-5deg);
    filter: brightness(1.2) saturate(1.4) hue-rotate(180deg);
    box-shadow: 0 10px 30px rgba(103, 194, 58, 0.4);
  }
  100% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1) saturate(1) hue-rotate(0deg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
}

/* 超级爆发时的多重粒子效果 */
.result-header.super-burst::after {
  animation: superBurstParticles 2s ease-out;
}

@keyframes superBurstParticles {
  0% {
    width: 0;
    height: 0;
    transform: translate(-50%, -50%) scale(0) rotate(0deg);
    opacity: 0;
    background: radial-gradient(circle,
      rgba(64, 158, 255, 0.8) 0%,
      rgba(103, 194, 58, 0.6) 20%,
      rgba(230, 162, 60, 0.5) 40%,
      rgba(245, 108, 108, 0.4) 60%,
      rgba(64, 158, 255, 0.3) 80%,
      transparent 100%);
  }
  20% {
    width: 300px;
    height: 300px;
    transform: translate(-50%, -50%) scale(1) rotate(90deg);
    opacity: 1;
  }
  50% {
    width: 600px;
    height: 600px;
    transform: translate(-50%, -50%) scale(1.5) rotate(270deg);
    opacity: 0.8;
  }
  100% {
    width: 800px;
    height: 800px;
    transform: translate(-50%, -50%) scale(2) rotate(720deg);
    opacity: 0;
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 1;
  animation: titleBounce 3s ease-in-out infinite;
}

/* 标题弹跳动画 */
@keyframes titleBounce {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  25% {
    transform: translateY(-2px) scale(1.02);
  }
  50% {
    transform: translateY(0px) scale(1);
  }
  75% {
    transform: translateY(-1px) scale(1.01);
  }
}

.tech-lines {
  width: 20px;
  height: 2px;
  background: var(--el-color-primary);
  position: relative;
  animation: techPulse 2s ease-in-out infinite;
  border-radius: 1px;
}

/* 科技线条脉冲动画 */
@keyframes techPulse {
  0%, 100% {
    background: var(--el-color-primary);
    box-shadow: 0 0 0 rgba(64, 158, 255, 0);
  }
  50% {
    background: var(--el-color-primary-light-3);
    box-shadow: 0 0 8px rgba(64, 158, 255, 0.4);
  }
}

.tech-lines::before,
.tech-lines::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 2px;
  background: var(--el-color-success);
  border-radius: 1px;
  animation: techSegmentMove 3s ease-in-out infinite;
}

.tech-lines::before {
  top: -4px;
  left: 4px;
  animation-delay: 0.5s;
}

.tech-lines::after {
  top: 4px;
  left: 8px;
  animation-delay: 1s;
}

/* 科技线条片段移动动画 */
@keyframes techSegmentMove {
  0%, 100% {
    transform: translateX(0px);
    background: var(--el-color-success);
  }
  25% {
    transform: translateX(2px);
    background: var(--el-color-success-light-3);
  }
  50% {
    transform: translateX(0px);
    background: var(--el-color-warning);
  }
  75% {
    transform: translateX(-2px);
    background: var(--el-color-success-light-3);
  }
}

.result-icon {
  color: var(--el-color-primary);
  font-size: 18px;
  animation: iconSpin 4s ease-in-out infinite;
  transform-origin: center;
}

/* 图标旋转闪烁动画 */
@keyframes iconSpin {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    color: var(--el-color-primary);
    filter: drop-shadow(0 0 0 transparent);
  }
  25% {
    transform: rotate(90deg) scale(1.1);
    color: var(--el-color-success);
    filter: drop-shadow(0 0 4px rgba(103, 194, 58, 0.3));
  }
  50% {
    transform: rotate(180deg) scale(1.2);
    color: var(--el-color-warning);
    filter: drop-shadow(0 0 6px rgba(230, 162, 60, 0.4));
  }
  75% {
    transform: rotate(270deg) scale(1.1);
    color: var(--el-color-danger);
    filter: drop-shadow(0 0 4px rgba(245, 108, 108, 0.3));
  }
}

.section-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(45deg,
    var(--el-color-primary) 0%,
    var(--el-color-success) 25%,
    var(--el-color-warning) 50%,
    var(--el-color-danger) 75%,
    var(--el-color-primary) 100%);
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: rainbowText 3s ease-in-out infinite;
  position: relative;
}

/* 彩虹文字动画 */
@keyframes rainbowText {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 移除文字闪光滑动效果 */

/* 卡池标题特效 */
.card-pool-header .section-title {
  position: relative;
}

.title-sparkles {
  display: flex;
  gap: 4px;
  margin-left: 8px;
  animation: sparkleFloat 2s ease-in-out infinite;
}

/* 星星容器浮动 */
@keyframes sparkleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

.sparkle {
  color: var(--el-color-warning);
  font-size: 12px;
  animation: sparkleRotate 2s ease-in-out infinite;
  position: relative;
  display: inline-block;
}

.sparkle:nth-child(1) {
  animation-delay: 0s;
  animation: sparkleRotate 2s ease-in-out infinite, sparklePulse 1.5s ease-in-out infinite;
}
.sparkle:nth-child(2) {
  animation-delay: 0.3s;
  animation: sparkleRotate 2s ease-in-out infinite, sparklePulse 1.5s ease-in-out infinite 0.5s;
}
.sparkle:nth-child(3) {
  animation-delay: 0.6s;
  animation: sparkleRotate 2s ease-in-out infinite, sparklePulse 1.5s ease-in-out infinite 1s;
}

@keyframes sparkleRotate {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    opacity: 0.6;
    color: var(--el-color-warning);
  }
  25% {
    transform: rotate(90deg) scale(1.3);
    opacity: 0.8;
    color: var(--el-color-success);
  }
  50% {
    transform: rotate(180deg) scale(1.5);
    opacity: 1;
    color: var(--el-color-primary);
  }
  75% {
    transform: rotate(270deg) scale(1.3);
    opacity: 0.8;
    color: var(--el-color-danger);
  }
}

@keyframes sparklePulse {
  0%, 100% {
    filter: drop-shadow(0 0 0 transparent);
  }
  50% {
    filter: drop-shadow(0 0 8px currentColor);
  }
}

.lock-icon {
  font-size: 14px;
  color: var(--el-color-warning);
  animation: lockPulse 1.5s ease-in-out infinite;
}

@keyframes lockPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.interaction-tips {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
  animation: tipsSlideIn 1s ease-out;
}

/* 提示滑入动画 */
@keyframes tipsSlideIn {
  0% {
    transform: translateX(20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.fixed-elements-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  background: var(--el-color-warning-light-8);
  color: var(--el-color-warning);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  animation: indicatorBounce 2s ease-in-out infinite;
  position: relative;
  overflow: hidden;
}

/* 指示器弹跳动画 */
@keyframes indicatorBounce {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(230, 162, 60, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(230, 162, 60, 0.3);
  }
}

/* 指示器背景流动效果 */
.fixed-elements-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%);
  animation: indicatorFlow 3s linear infinite;
}

@keyframes indicatorFlow {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.fixed-count {
  font-weight: 500;
  position: relative;
  z-index: 1;
  animation: countPulse 1.5s ease-in-out infinite;
}

@keyframes countPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.info-icon {
  font-size: 14px;
  position: relative;
  z-index: 1;
}

.tips-icon {
  color: var(--el-color-info);
  cursor: help;
  animation: tipsIconFloat 3s ease-in-out infinite;
}

@keyframes tipsIconFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-2px) rotate(5deg);
  }
  50% {
    transform: translateY(0px) rotate(0deg);
  }
  75% {
    transform: translateY(-1px) rotate(-5deg);
  }
}

/* 结果滚动容器 */
.result-scroll-container {
  flex: 1;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 16px 20px;
  position: relative;
}

/* 水平滚动提示 */
.result-scroll-container::before {
  content: '';
  position: absolute;
  top: 50%;
  right: 0;
  width: 20px;
  height: 60px;
  background: linear-gradient(to left, var(--el-bg-color) 0%, transparent 100%);
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 10;
}

.result-scroll-container::after {
  content: '→';
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  color: var(--el-text-color-placeholder);
  font-size: 16px;
  pointer-events: none;
  z-index: 11;
  animation: scrollHint 2s ease-in-out infinite;
}

@keyframes scrollHint {
  0%, 100% {
    opacity: 0.3;
    transform: translateY(-50%) translateX(0);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-50%) translateX(3px);
  }
}

/* 当没有横向滚动时隐藏提示 */
.result-scroll-container:not(.has-scroll)::before,
.result-scroll-container:not(.has-scroll)::after {
  display: none;
}

.result-container-horizontal {
  display: flex;
  gap: 20px;
  height: 100%;
  min-width: max-content;
}

/* 水平排列的卡池列 */
.card-pool-column {
  flex: 0 0 280px;
  display: flex;
  flex-direction: column;
  background: linear-gradient(145deg,
    var(--el-bg-color) 0%,
    var(--el-fill-color-light) 30%,
    var(--el-bg-color) 100%);
  border: 1px solid var(--el-border-color-light);
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  height: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-pool-column:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* 空状态 - 水平版本 */
.empty-result-horizontal {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.card-pool-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120px;
  background: radial-gradient(
    ellipse at 50% 0%,
    var(--el-color-primary-light-9) 0%,
    var(--el-color-primary-light-8) 25%,
    transparent 70%
  );
  pointer-events: none;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.card-pool-column:hover::before {
  opacity: 0.8;
}

.card-pool-column::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    currentColor 50%,
    transparent 100%);
  animation: shimmer 3s ease-in-out infinite;
  opacity: 0.6;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

@keyframes poolGlow {
  0%, 100% {
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.12),
      0 2px 8px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.15),
      0 4px 12px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }
}

.card-pool-column {
  animation: poolGlow 4s ease-in-out infinite;
}

.pool-header {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 48px;
}

.pool-header.pool-primary {
  background: linear-gradient(135deg,
    var(--el-color-primary) 0%,
    var(--el-color-primary-light-3) 30%,
    var(--el-color-primary-dark-2) 100%);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.pool-header.pool-success {
  background: linear-gradient(135deg,
    var(--el-color-success) 0%,
    var(--el-color-success-light-3) 30%,
    var(--el-color-success-dark-2) 100%);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.pool-header.pool-warning {
  background: linear-gradient(135deg,
    var(--el-color-warning) 0%,
    var(--el-color-warning-light-3) 30%,
    var(--el-color-warning-dark-2) 100%);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.pool-header.pool-danger {
  background: linear-gradient(135deg,
    var(--el-color-danger) 0%,
    var(--el-color-danger-light-3) 30%,
    var(--el-color-danger-dark-2) 100%);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.pool-header.pool-info {
  background: linear-gradient(135deg,
    var(--el-color-info) 0%,
    var(--el-color-info-light-3) 30%,
    var(--el-color-info-dark-2) 100%);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.pool-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  backdrop-filter: blur(10px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.card-pool-column:hover .pool-icon {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.3);
}

.pool-info {
  flex: 1;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.pool-title {
  font-size: 16px;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.3px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pool-count {
  font-size: 12px;
  opacity: 0.95;
  font-weight: 500;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.15);
  padding: 4px 8px;
  border-radius: 12px;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 气泡容器 */
.bubble-container {
  flex: 1;
  padding: 16px;
  position: relative;
  z-index: 1;
  overflow-y: auto;
  background: var(--el-bg-color);
  min-height: 0;
  border-radius: 8px;
}

/* 简化的气泡容器背景 - 仅保留微妙的渐变 */
.bubble-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.02) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.01) 100%);
  border-radius: 8px;
  pointer-events: none;
  z-index: 0;
}

/* 气泡行容器 */
.bubble-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
  align-content: flex-start;
  line-height: 1.5;
}

/* 为每个卡池添加独特的背景色调 - 更加微妙 */
.pool-primary .bubble-container {
  background: linear-gradient(135deg,
    var(--el-color-primary-light-9) 0%,
    var(--el-bg-color) 100%);
}

.pool-success .bubble-container {
  background: linear-gradient(135deg,
    var(--el-color-success-light-9) 0%,
    var(--el-bg-color) 100%);
}

.pool-warning .bubble-container {
  background: linear-gradient(135deg,
    var(--el-color-warning-light-9) 0%,
    var(--el-bg-color) 100%);
}

.pool-danger .bubble-container {
  background: linear-gradient(135deg,
    var(--el-color-danger-light-9) 0%,
    var(--el-bg-color) 100%);
}

.pool-info .bubble-container {
  background: linear-gradient(135deg,
    var(--el-color-info-light-9) 0%,
    var(--el-bg-color) 100%);
}

/* 气泡元素样式 - 清晰简洁版本 */
.element-bubble {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 10px 18px;
  border-radius: 20px;
  cursor: pointer;
  user-select: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  animation: bubbleAppear 0.3s ease-out forwards;
  transform: scale(0);
  background: linear-gradient(135deg, #909399 0%, #73767a 100%);
  color: white;
  border: 1px solid #909399;
  text-align: center;
  white-space: nowrap;
  box-sizing: border-box;
  flex-shrink: 0;
  min-height: 40px;
  line-height: 1.4;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.04);
  margin: 4px;
  z-index: 2;
}

@keyframes bubbleAppear {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.element-bubble:hover {
  transform: scale(1.03) translateY(-1px);
  background: linear-gradient(135deg, #a6a9ad 0%, #909399 100%);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.12),
    0 2px 6px rgba(0, 0, 0, 0.08);
}

.element-bubble:active {
  transform: scale(0.98);
}

/* 不同类型的气泡颜色 - 增强对比度版本 */
.bubble-primary {
  background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
  color: white;
  border: 1px solid #409eff;
  box-shadow:
    0 2px 8px rgba(64, 158, 255, 0.3),
    0 1px 4px rgba(64, 158, 255, 0.2);
}

.bubble-success {
  background: linear-gradient(135deg, #67c23a 0%, #529b2e 100%);
  color: white;
  border: 1px solid #67c23a;
  box-shadow:
    0 2px 8px rgba(103, 194, 58, 0.3),
    0 1px 4px rgba(103, 194, 58, 0.2);
}

.bubble-warning {
  background: linear-gradient(135deg, #e6a23c 0%, #b88230 100%);
  color: white;
  border: 1px solid #e6a23c;
  box-shadow:
    0 2px 8px rgba(230, 162, 60, 0.3),
    0 1px 4px rgba(230, 162, 60, 0.2);
}

.bubble-danger {
  background: linear-gradient(135deg, #f56c6c 0%, #c45656 100%);
  color: white;
  border: 1px solid #f56c6c;
  box-shadow:
    0 2px 8px rgba(245, 108, 108, 0.3),
    0 1px 4px rgba(245, 108, 108, 0.2);
}

.bubble-info {
  background: linear-gradient(135deg, #909399 0%, #73767a 100%);
  color: white;
  border: 1px solid #909399;
  box-shadow:
    0 2px 8px rgba(144, 147, 153, 0.3),
    0 1px 4px rgba(144, 147, 153, 0.2);
}

/* 气泡悬停时的颜色增强 */
.bubble-primary:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%);
  box-shadow:
    0 4px 12px rgba(64, 158, 255, 0.4),
    0 2px 6px rgba(64, 158, 255, 0.3);
}

.bubble-success:hover {
  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
  box-shadow:
    0 4px 12px rgba(103, 194, 58, 0.4),
    0 2px 6px rgba(103, 194, 58, 0.3);
}

.bubble-warning:hover {
  background: linear-gradient(135deg, #ebb563 0%, #e6a23c 100%);
  box-shadow:
    0 4px 12px rgba(230, 162, 60, 0.4),
    0 2px 6px rgba(230, 162, 60, 0.3);
}

.bubble-danger:hover {
  background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%);
  box-shadow:
    0 4px 12px rgba(245, 108, 108, 0.4),
    0 2px 6px rgba(245, 108, 108, 0.3);
}

.bubble-info:hover {
  background: linear-gradient(135deg, #a6a9ad 0%, #909399 100%);
  box-shadow:
    0 4px 12px rgba(144, 147, 153, 0.4),
    0 2px 6px rgba(144, 147, 153, 0.3);
}

/* 固定状态的气泡 - 简化版本 */
.bubble-fixed {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%) !important;
  border-color: rgba(255, 215, 0, 0.6) !important;
  box-shadow:
    0 3px 12px rgba(255, 215, 0, 0.3),
    0 1px 6px rgba(255, 215, 0, 0.2) !important;
  position: relative;
}

.bubble-fixed:hover {
  box-shadow:
    0 4px 16px rgba(255, 215, 0, 0.4),
    0 2px 8px rgba(255, 215, 0, 0.3) !important;
  transform: scale(1.03) translateY(-1px);
}

.bubble-lock-icon {
  font-size: 12px;
  opacity: 1;
  margin-right: 2px;
}

.bubble-text {
  position: relative;
  z-index: 2;
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* 移除气泡发光效果以保持清晰 */

/* 移除卡池星光效果以保持清晰 */

/* 简化气泡动画 - 仅保留出现动画 */
.element-bubble {
  animation: bubbleAppear 0.3s ease-out forwards;
  position: relative;
}

/* 移除能量脉冲效果以保持清晰 */

/* 简化点击效果 */
.element-bubble::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.element-bubble:active::before {
  opacity: 1;
}

.empty-result {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.empty-icon {
  font-size: 48px;
  color: var(--el-text-color-placeholder);
}



/* 元素编辑对话框 */
.element-editor-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.editor-container {
  display: flex;
  flex-direction: column;
  height: 600px;
}

.editor-toolbar {
  padding: 16px;
  background: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 导入配置对话框 */
.import-config-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.import-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.format-hint-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-color-info);
}

.format-hint-content {
  background: var(--el-fill-color-light);
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid var(--el-color-info);
}

.format-hint-content pre {
  margin: 0;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
  color: var(--el-text-color-regular);
  white-space: pre-wrap;
}

.import-input {
  font-family: 'Consolas', 'Monaco', monospace;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 示例编辑器 */
.examples-editor {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.example-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 原生对话框样式 */
.native-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.native-dialog {
  background: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid var(--el-border-color-light);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color-light);
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.category-badge {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.category-badge.theme { background: var(--el-color-primary); }
.category-badge.volume { background: var(--el-color-success); }
.category-badge.keyPoint { background: var(--el-color-warning); }
.category-badge.technique { background: var(--el-color-danger); }

.title-area {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.category-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.element-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.emotion-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.emotion-badge.up {
  background: var(--el-color-success-light-8);
  color: var(--el-color-success);
}

.emotion-badge.down {
  background: var(--el-color-danger-light-8);
  color: var(--el-color-danger);
}

.emotion-badge.wave {
  background: var(--el-color-info-light-8);
  color: var(--el-color-info);
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--el-fill-color);
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--el-text-color-secondary);
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: var(--el-color-danger-light-8);
  color: var(--el-color-danger);
}

.dialog-content {
  flex: 1;
  overflow: hidden;
}

.content-scroll {
  height: 100%;
  overflow-y: auto;
  padding: 24px;
}

.info-section {
  margin-bottom: 24px;
}

.info-section .section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: var(--el-text-color-primary);
  font-weight: 600;
}

.description {
  color: var(--el-text-color-regular);
  line-height: 1.6;
  margin: 0;
  padding: 16px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
  border-left: 3px solid var(--el-color-primary);
}

.examples-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.example-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
  border-left: 3px solid var(--el-color-success);
}

.example-num {
  background: var(--el-color-success);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.example-text {
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.dialog-footer {
  padding: 16px 24px;
  background: var(--el-fill-color-light);
  border-top: 1px solid var(--el-border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.footer-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

.confirm-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: var(--el-color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.confirm-btn:hover {
  background: var(--el-color-primary-dark-2);
}

/* 滚动条样式 */
.native-scroll {
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color-light) transparent;
}

.native-scroll::-webkit-scrollbar {
  width: 6px;
}

.native-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.native-scroll::-webkit-scrollbar-thumb {
  background: var(--el-border-color-light);
  border-radius: 3px;
}

.native-scroll::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color);
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .selection-wrapper {
    width: max-content;
    min-width: 100%;
  }

  .category-column {
    flex: 0 0 240px;
  }

  .card-pool-column {
    flex: 0 0 240px;
  }

  .bubble-container {
    gap: 8px;
    padding: 10px;
  }

  .bubble-row {
    gap: 6px;
  }

  .element-bubble {
    font-size: 12px;
    padding: 5px 10px;
    min-height: 26px;
  }
}

@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
    gap: 8px;
  }

  .action-button {
    min-width: 90px;
    height: 34px;
    font-size: 13px;
  }

  .selection-container {
    padding: 12px;
  }

  .selection-wrapper {
    width: max-content;
    min-width: 100%;
  }

  .category-column {
    flex: 0 0 200px;
  }

  .result-section {
    height: 320px;
    margin: 0 12px 12px;
  }

  .result-scroll-container {
    overflow-x: auto;
    overflow-y: hidden;
    padding: 16px;
  }

  .result-container-horizontal {
    min-width: max-content;
  }

  .card-pool-column {
    flex: 0 0 220px;
    min-height: 250px;
  }

  .bubble-container {
    padding: 12px;
    gap: 6px;
  }

  .element-bubble {
    font-size: 11px;
    padding: 6px 10px;
    min-height: 28px;
  }

  .pool-header {
    padding: 12px 16px;
  }

  .pool-icon {
    width: 28px;
    height: 28px;
    font-size: 16px;
  }

  .pool-title {
    font-size: 14px;
  }

  .pool-count {
    font-size: 11px;
  }

  .native-dialog {
    width: 95%;
    max-height: 90vh;
  }

  .dialog-header {
    padding: 16px;
  }

  .content-scroll {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .action-button {
    width: 100%;
    min-width: auto;
    height: 32px;
    font-size: 12px;
  }

  .category-functions {
    flex-direction: column;
    gap: 6px;
  }

  .count-select.native-select {
    width: 100%;
  }

  .selection-container {
    padding: 8px;
  }

  .selection-wrapper {
    width: max-content;
    min-width: 100%;
  }

  .category-column {
    flex: 0 0 160px;
  }

  .result-scroll-container {
    overflow-x: auto;
    padding: 12px;
  }

  .result-container-horizontal {
    gap: 12px;
  }

  .card-pool-column {
    flex: 0 0 180px;
    min-height: 220px;
  }

  .bubble-container {
    padding: 8px;
  }

  .bubble-row {
    gap: 4px;
  }

  .element-bubble {
    font-size: 11px;
    padding: 4px 8px;
    min-height: 22px;
    border-radius: 12px;
  }

  .result-section {
    height: 250px;
  }

  .pool-header {
    padding: 8px 12px;
  }

  .pool-icon {
    width: 24px;
    height: 24px;
    font-size: 14px;
  }

  .pool-title {
    font-size: 12px;
  }

  .pool-count {
    font-size: 10px;
  }
}
</style>