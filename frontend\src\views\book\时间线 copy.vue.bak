<template>
  <div class="timeline-page">
    <!-- Vue Flow Timeline section -->
    <div class="flow-timeline-section">
      <div class="flow-header">
        <h1 class="book-title">{{ bookTitle }} - 时间线</h1>
        <div class="flow-controls">
          <el-button-group>
            <el-button type="success" plain @click="exportFlowData" :icon="Download">
              导出数据
            </el-button>
            <el-button type="success" plain @click="exportToTxt" :icon="Document">
              导出TXT
            </el-button>
            <el-button type="info" plain @click="showImportDialog" :icon="Upload">
              导入数据
            </el-button>
            <el-button 
              type="primary" 
              plain 
              @click="saveTimelineToBackend(true)" 
              :loading="savingData"
            >
              <el-icon><Check /></el-icon>
              保存数据
            </el-button>
            <el-button
              class="back-button"
              @click="goBack"
              type="primary"
              plain
            >
              <el-icon><ArrowLeft /></el-icon>
              返回写作
            </el-button>
          </el-button-group>
        </div>
              </div>
      <!-- 引入流程时间线组件 -->
      <TimelineFlow ref="timelineFlowRef" />
      
      <!-- 导入数据对话框 -->
      <el-dialog
        v-model="importDialogVisible"
        title="导入时间线数据"
        width="500px"
        :close-on-click-modal="false"
        class="native-style-dialog"
      >
        <div class="import-container">
          <el-alert
            title="导入说明"
            type="info"
            :closable="false"
          >
            <template #default>
              <p>请粘贴之前导出的JSON格式数据，支持以下方式导入：</p>
              <ol class="import-instructions">
                <li>从导出功能复制的JSON数据</li>
                <li>从已导出的JSON文件中复制内容</li>
              </ol>
            </template>
          </el-alert>
          <el-input
            v-model="importJsonData"
            type="textarea"
            :rows="10"
            placeholder="在此粘贴JSON数据..."
            class="import-textarea"
          />
          <div class="import-tips">
            <el-icon><InfoFilled /></el-icon>
            <span>导入会替换当前所有时间线数据，请确保已保存重要内容</span>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="importDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="importTimelineData">导入</el-button>
          </span>
        </template>
      </el-dialog>
                    </div>
                  </div>
                </template>

<script setup>
// 引入Vue模块
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { Download, ArrowLeft, Upload, Check, InfoFilled, Document } from '@element-plus/icons-vue'

// 引入Vue Flow时间线组件
import TimelineFlow from './TimelineFlow.vue'

// 路由相关
const route = useRoute()
const router = useRouter()
const bookId = ref(route.query.id || route.params.id)
const bookTitle = ref(route.query.title || route.params.title || '我的时间线')

// 组件引用
const timelineFlowRef = ref(null)

// 导入数据对话框状态
const importDialogVisible = ref(false)

// 导入数据内容
const importJsonData = ref('')

// 保存状态
const savingData = ref(false)

// 自动保存计时器
let autoSaveTimer = null
// 标记数据是否被修改
const dataModified = ref(false)

// 导出流程图数据
function exportFlowData() {
  if (timelineFlowRef.value) {
    try {
      // 调用TimelineFlow组件的导出方法
      timelineFlowRef.value.exportTimelineData();
      // 无需额外提示，因为TimelineFlow组件中已经有提示
    } catch (error) {
      console.error('导出失败:', error);
      ElMessage.error('导出失败: ' + error.message);
    }
  } else {
    ElMessage.warning('时间线组件未初始化');
  }
}

// 导出为TXT文件
async function exportToTxt() {
  if (!timelineFlowRef.value) {
    ElMessage.warning('时间线组件未初始化');
    return;
  }

  try {
    const loading = ElLoading.service({
      lock: true,
      text: '准备导出TXT文件...',
      background: 'rgba(255, 255, 255, 0.7)',
    });

    // 获取时间线数据
    const timelineData = timelineFlowRef.value.getTimelineData();
    
    // 确保保存最新数据
    await saveTimelineToBackend(false);
    
    // 调用后端API导出TXT
    const responseStr = await window.pywebview.api.export_timeline_to_txt(
      bookId.value, 
      JSON.stringify(timelineData)
    );

    // 解析响应
    let response;
    try {
      response = JSON.parse(responseStr);
    } catch (err) {
      console.error('解析响应数据失败:', err);
      loading.close();
      ElMessage.error('导出失败: 数据格式错误');
      return;
    }

    loading.close();
    
    if (response && response.status === 'success') {
      ElMessage.success(response.message || '时间线已成功导出为TXT文件');
    } else {
      ElMessage.error('导出失败: ' + (response?.message || '未知错误'));
    }
  } catch (error) {
    console.error('导出TXT失败:', error);
    ElMessage.error('导出失败: ' + (error.message || '未知错误'));
  }
}

// 显示导入对话框
function showImportDialog() {
  importDialogVisible.value = true;
}

// 导入数据
function importTimelineData() {
  if (timelineFlowRef.value) {
    try {
      if (!importJsonData.value.trim()) {
        ElMessage.warning('请输入有效的JSON数据');
        return;
      }
      
      // 解析JSON数据
      let data;
      try {
        data = JSON.parse(importJsonData.value);
      } catch (error) {
        ElMessage.error('JSON格式无效，请检查数据格式');
        return;
      }
      
      // 检查数据基本结构
      if (!data || (!data.nodes && !data.edges && !data.mainTrunkEvents)) {
        ElMessage.error('数据格式不正确，缺少必要的节点或边数据');
        return;
      }
      
      // 导入数据
      const success = timelineFlowRef.value.importFromJson(data);
      
      if (success) {
        ElMessage.success('数据导入成功');
        importDialogVisible.value = false;
        importJsonData.value = '';
        
        // 导入后立即保存到后端
        saveTimelineToBackend(false);
      } else {
        ElMessage.error('导入失败，请检查数据格式');
      }
    } catch (error) {
      console.error('导入失败:', error);
      ElMessage.error('导入失败: ' + error.message);
    }
  } else {
    ElMessage.warning('时间线组件未初始化');
  }
}

// 返回上一页
function goBack() {
  router.push({
    name: 'bookWriting',
    query: {
      id: bookId.value
    }
  })
}

// 初始化自动保存
function initAutoSave() {
  // 监听数据变化事件
  document.addEventListener('timeline-data-changed', () => {
    
    dataModified.value = true;
  });
  
  // 设置自动保存定时器 (每5分钟自动保存一次)
  autoSaveTimer = setInterval(() => {
    if (dataModified.value) {
      console.log('自动保存时间线数据...');
      saveTimelineToBackend(false);
    }
  }, 1 * 60 * 1000);
}

// 保存时间线数据到后端
async function saveTimelineToBackend(showSuccessMessage = false) {
  if (!timelineFlowRef.value) {
    console.error('时间线组件未初始化，无法保存');
    return;
  }
  
  try {
    savingData.value = true;
    const timelineData = timelineFlowRef.value.getTimelineData();
    await window.pywebview.api.save_timeline(bookId.value, JSON.stringify(timelineData));
    dataModified.value = false;
    console.log('时间线数据保存成功');
    
    // 显示成功提示（仅当showSuccessMessage为true时）
    if (showSuccessMessage) {
      // 使用顶部固定提示，不会影响布局
      ElMessage({
        message: '时间线数据已保存',
        type: 'success',
        offset: 70, // 偏移以避免遮挡顶部标题
        duration: 2000
      });
    }
  } catch (error) {
    console.error('保存时间线数据失败:', error);
    ElMessage.error('保存失败: ' + (error.message || '未知错误'));
  } finally {
    savingData.value = false;
  }
}

// 生命周期钩子
onMounted(async () => {
  // 初始化自动保存功能
  initAutoSave();

  // 从后端加载数据
  await loadTimelineDataFromBackend();

  // 添加保存快捷键监听器
  document.addEventListener('save-timeline', handleSaveShortcut);

  // 添加页面关闭前的保存提示
  window.addEventListener('beforeunload', handleBeforeUnload);
});

// 从后端加载时间线数据
async function loadTimelineDataFromBackend() {
  try {
    const loading = ElLoading.service({
      lock: true,
      text: '加载时间线数据...',
      background: 'rgba(255, 255, 255, 0.7)',
    });
    
    try {
      // 调用后端API获取数据
      const responseStr = await window.pywebview.api.get_timeline(bookId.value);
      
      // 解析响应的JSON字符串
      let response;
      try {
        response = JSON.parse(responseStr);
      } catch (err) {
        console.error('解析响应数据失败:', err);
        loading.close();
        ElMessage.error('加载时间线数据失败: 数据格式错误');
        return;
      }
      
      // 检查是否有有效数据
      const hasValidData = response && 
                         response.status === 'success' && 
                         response.data && 
                         ((response.data.nodes && response.data.nodes.length > 0) || 
                          (response.data.mainTrunkEvents && response.data.mainTrunkEvents.length > 0));
      
      if (!hasValidData) {
        // 数据为空，创建默认主线事件
        loading.close();
        ElMessage.info('没有找到时间线数据，正在创建默认主线事件...');
        
        // 确认组件引用已初始化
        if (timelineFlowRef.value) {
          // 创建默认主线事件 - 只创建一个简单节点
          const defaultTimelineData = timelineFlowRef.value.createDefaultMainEvent();
          console.log('创建的默认数据:', defaultTimelineData);
          
          // 保存到后端
          await saveTimelineToBackend(false);
          
          ElMessage.success('已创建默认时间线起点');
        } else {
          ElMessage.warning('时间线组件未初始化，无法创建默认事件');
        }
      } else {
        // 数据不为空，使用后端返回的数据
        const flowData = convertBackendDataToFlowData(response.data);
        
        nextTick(() => {
          if (timelineFlowRef.value) {
            timelineFlowRef.value.importFromJson(flowData);
            loading.close();
            ElMessage.success('时间线数据加载成功');
          }
        });
      }
    } catch (error) {
      console.error('加载时间线数据失败:', error);
      loading.close();
      ElMessage.error('加载时间线数据失败: ' + error.message);
    }
  } catch (error) {
    console.error('初始化加载失败:', error);
    ElMessage.error('初始化加载失败: ' + error.message);
  }
}

// 将后端数据转换为流程图数据
function convertBackendDataToFlowData(backendData) {
  // 检查是否已经是新格式
  if (backendData.nodes && backendData.edges) {
    return backendData;
  }
  
  // 老数据格式转换为新格式
  let nodes = [];
  let edges = [];
  
  // 将老数据格式转换为新格式
  // 这里需要根据实际后端数据结构进行调整
  if (backendData.mainTrunkEvents) {
    // 处理主干事件节点
    backendData.mainTrunkEvents.forEach((event, index) => {
      nodes.push({
        id: event.id,
        type: 'main-event',
        position: { x: 400, y: 100 + index * 150 },
        data: {
          label: event.title,
          year: event.year,
          month: event.month,
          day: event.day,
          content: event.description || '',
          nodeType: 'main',
          color: '#409EFF'
        }
      });
      
      // 添加连接线
      if (index > 0) {
        const prevId = backendData.mainTrunkEvents[index - 1].id;
        edges.push({
          id: `e-${prevId}-${event.id}`,
          source: prevId,
          target: event.id,
          sourceHandle: 'bottom',
          targetHandle: 'top',
          type: 'smoothstep',
          style: {
            strokeWidth: 4,
            stroke: '#409EFF'
          }
        });
      }
    });
  }
  
  if (backendData.branches && backendData.branchEvents) {
    // 处理分支
    backendData.branches.forEach(branch => {
      // 找到关联到此分支的事件
      const branchEvents = backendData.branchEvents.filter(
        event => event.branchId === branch.id
      );
      
      branchEvents.forEach((event, index) => {
        const isLeftSide = branch.origin?.point === 'left';
        const xPos = isLeftSide ? 150 : 650;
        const yPos = branch.origin?.position?.y || (100 + index * 150);
        
        nodes.push({
          id: event.id,
          type: 'branch-event',
          position: { x: xPos, y: yPos },
          data: {
            label: event.title,
            year: event.year,
            month: event.month,
            day: event.day,
            content: event.description || '',
            nodeType: 'branch',
            color: branch.color,
            isLeftSide: isLeftSide,
            parentId: branch.origin?.id || null
          }
        });
        
        // 如果有主干连接点
        if (branch.origin && branch.origin.id) {
          edges.push({
            id: `e-${branch.origin.id}-${event.id}`,
            source: branch.origin.id,
            target: event.id,
            sourceHandle: isLeftSide ? 'left' : 'right',
            targetHandle: isLeftSide ? 'right' : 'left',
            type: 'smoothstep',
            animated: true,
            style: {
              strokeWidth: 3,
              stroke: branch.color
            }
          });
        }
      });
    });
  }
  
  // 处理额外的连接
  if (backendData.connections && backendData.connections.length > 0) {
    backendData.connections.forEach(conn => {
      edges.push({
        id: `e-${conn.source}-${conn.target}-custom`,
        source: conn.source,
        target: conn.target,
        type: 'smoothstep',
        animated: conn.animated || false,
        style: {
          strokeWidth: conn.style?.strokeWidth || 2,
          stroke: conn.style?.stroke || '#409EFF'
        }
      });
    });
  }
  
  return {
    nodes: nodes,
    edges: edges
  };
}

// 组件卸载时清除定时器和事件监听
onUnmounted(() => {
  if (autoSaveTimer) {
    clearInterval(autoSaveTimer);
    autoSaveTimer = null;
  }
  // 移除事件监听
  document.removeEventListener('timeline-data-changed', () => {});
  document.removeEventListener('save-timeline', handleSaveShortcut);
  window.removeEventListener('beforeunload', handleBeforeUnload);
});

// 处理保存快捷键事件
function handleSaveShortcut() {
  // 调用保存方法，并显示成功提示
  saveTimelineToBackend(true);
}

// 处理页面关闭前的事件
function handleBeforeUnload(event) {
  if (dataModified.value) {
    const message = '您有未保存的更改，确定要离开吗？';
    event.returnValue = message;
    return message;
  }
}
</script>

<style lang="scss" scoped>
.timeline-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.flow-timeline-section {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.flow-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 32px;
  background: linear-gradient(135deg,
              var(--el-bg-color) 0%,
              var(--el-bg-color-overlay) 100%);
  border-bottom: 1px solid var(--el-border-color-light);
  height: 80px;
  flex-shrink: 0;
  position: relative;
  backdrop-filter: blur(12px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  /* 添加顶部光泽效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.4) 50%,
                transparent 100%);
  }
}

.flow-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  min-height: 44px;

  .el-button-group {
    display: flex;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    backdrop-filter: blur(10px);
    border: 1px solid var(--el-border-color-lighter);
    background: var(--el-bg-color-overlay);

    .el-button {
      border: none !important;
      border-radius: 0 !important;
      margin: 0 !important;
      padding: 12px 20px !important;
      font-weight: 500 !important;
      font-size: 14px !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      position: relative !important;
      backdrop-filter: blur(8px) !important;

      &:not(:last-child) {
        border-right: 1px solid var(--el-border-color-lighter) !important;
      }

      &:first-child {
        border-top-left-radius: 11px !important;
        border-bottom-left-radius: 11px !important;
      }

      &:last-child {
        border-top-right-radius: 11px !important;
        border-bottom-right-radius: 11px !important;
      }

      &:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
        z-index: 2 !important;
      }

      &:active {
        transform: translateY(0px) !important;
        transition: all 0.1s ease !important;
      }
    }
  }
}

.book-title {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  letter-spacing: 0.5px;

  /* 添加装饰性下划线 */
  &::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg,
                var(--el-color-primary) 0%,
                var(--el-color-primary-light-5) 100%);
    border-radius: 2px;
  }
}

/* 时间线页面专用按钮样式 */
.flow-controls .el-button {
  position: relative;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
  font-weight: 500;
  letter-spacing: 0.3px;
  background: rgba(255, 255, 255, 0.9);

  /* 现代玻璃效果背景 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
                               rgba(255, 255, 255, 0.15) 0%,
                               rgba(255, 255, 255, 0.05) 100%);
    z-index: -1;
    border-radius: 7px;
  }

  /* 光泽扫过效果 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
                               transparent 0%,
                               rgba(255, 255, 255, 0.3) 50%,
                               transparent 100%);
    z-index: 1;
    transition: all 0.6s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--el-border-color);

    &::after {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0px);
    transition: all 0.15s ease;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--el-color-primary-rgb), 0.2);
  }

  /* 不同类型按钮的现代化颜色样式 - 使用CSS变量适配主题 */
  &.el-button--primary {
    background: linear-gradient(135deg,
                rgba(var(--el-color-primary-rgb), 0.12) 0%,
                rgba(var(--el-color-primary-rgb), 0.06) 100%);
    border-color: rgba(var(--el-color-primary-rgb), 0.4);
    color: var(--el-color-primary);
    box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);

    &:hover {
      background: linear-gradient(135deg,
                  rgba(var(--el-color-primary-rgb), 0.18) 0%,
                  rgba(var(--el-color-primary-rgb), 0.1) 100%);
      border-color: rgba(var(--el-color-primary-rgb), 0.6);
      box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.3);
    }
  }

  &.el-button--success {
    background: linear-gradient(135deg,
                rgba(var(--el-color-success-rgb), 0.12) 0%,
                rgba(var(--el-color-success-rgb), 0.06) 100%);
    border-color: rgba(var(--el-color-success-rgb), 0.4);
    color: var(--el-color-success);
    box-shadow: 0 4px 12px rgba(var(--el-color-success-rgb), 0.2);

    &:hover {
      background: linear-gradient(135deg,
                  rgba(var(--el-color-success-rgb), 0.18) 0%,
                  rgba(var(--el-color-success-rgb), 0.1) 100%);
      border-color: rgba(var(--el-color-success-rgb), 0.6);
      box-shadow: 0 8px 20px rgba(var(--el-color-success-rgb), 0.3);
    }
  }

  &.el-button--info {
    background: linear-gradient(135deg,
                rgba(var(--el-color-info-rgb), 0.12) 0%,
                rgba(var(--el-color-info-rgb), 0.06) 100%);
    border-color: rgba(var(--el-color-info-rgb), 0.4);
    color: var(--el-color-info);
    box-shadow: 0 4px 12px rgba(var(--el-color-info-rgb), 0.2);

    &:hover {
      background: linear-gradient(135deg,
                  rgba(var(--el-color-info-rgb), 0.18) 0%,
                  rgba(var(--el-color-info-rgb), 0.1) 100%);
      border-color: rgba(var(--el-color-info-rgb), 0.6);
      box-shadow: 0 8px 20px rgba(var(--el-color-info-rgb), 0.3);
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .flow-header {
    flex-direction: column;
    height: auto;
    padding: 12px 16px;
    gap: 12px;
  }

  .book-title {
    font-size: 18px;
    text-align: center;
  }

  .flow-controls {
    justify-content: center;
    flex-wrap: wrap;

    .el-button-group {
      .el-button {
        font-size: 12px;
        padding: 6px 8px;
      }
    }
  }
}

@media (max-width: 480px) {
  .flow-controls {
    .el-button-group {
      flex-direction: column;

      .el-button {
        border-radius: 6px !important;
        border-right: 1px solid var(--el-border-color-lighter) !important;
        border-bottom: 1px solid var(--el-border-color-lighter) !important;

        &:last-child {
          border-bottom: none !important;
        }
      }
    }
  }
}

/* 导入对话框样式 */
.import-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.import-textarea {
  margin-top: 10px;

}

.import-instructions {
  padding-left: 20px;
  margin: 8px 0;
  font-size: 14px;
}

.import-tips {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-color-warning);
  font-size: 13px;
  padding: 5px 0;
  
  .el-icon {
    font-size: 16px;
  }
}

/* 原生风格对话框 */
:deep(.native-style-dialog) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.native-style-dialog .el-dialog__header) {
  margin: 0;
  padding: 15px 20px;
  background-color: var(--el-bg-color-overlay);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.native-style-dialog .el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);
}

:deep(.native-style-dialog .el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-bg-color-overlay);
}

/* 确保TimelineFlow组件有适当的高度和滚动能力 */
:deep(.timeline-flow-wrapper) {
  height: calc(100% - 60px);
  flex: 1;
  overflow: auto;
}
</style> 