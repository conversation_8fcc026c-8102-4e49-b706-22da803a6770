<template>
  <div class="setting-manager native-app">
    <!-- 原生风格顶部导航栏 -->
    <div class="native-header">
      <div class="header-left">
        <div class="breadcrumb">
          <el-icon class="breadcrumb-icon"><Collection /></el-icon>
          <span class="book-name">{{ bookTitle }}</span>
          <el-icon class="separator"><ArrowRight /></el-icon>
          <span class="current-page">设定管理</span>
        </div>
      </div>
      <div class="header-actions">
        <el-button
          class="native-btn back-btn"
          @click="goBack"
          size="default"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回写作
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 - 原生侧边栏布局 -->
    <div class="native-main-layout">
      <!-- 左侧模板面板 -->
      <div class="native-sidebar">
        <div class="sidebar-header">
          <div class="header-title">
            <el-icon><Document /></el-icon>
            <span>设定模板</span>
            <el-badge :value="templateList.length" class="template-count" />
          </div>
          <div class="header-actions">
            <el-tooltip content="导入模板" placement="top">
              <el-button
                size="small"
                circle
                @click="showImportTemplateDialog"
                class="native-icon-btn"
              >
                <el-icon><Upload /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="新建模板" placement="top">
              <el-button
                size="small"
                type="primary"
                circle
                @click="showTemplateDialog()"
                class="native-icon-btn"
              >
                <el-icon><Plus /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>

        <!-- 模板搜索 -->
        <div class="search-section">
          <el-input
            v-model="templateSearchQuery"
            placeholder="搜索模板..."
            clearable
            size="small"
            @clear="handleTemplateSearchClear"
            class="native-search"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 模板列表 - 支持拖拽排序 -->
        <div class="template-list-container">
          <div class="template-list-wrapper">
            <div v-if="filteredTemplates.length === 0" class="empty-state">
              <div class="empty-icon">📝</div>
              <div class="empty-text">暂无模板</div>
              <el-button
                type="primary"
                size="small"
                @click="showTemplateDialog()"
                class="empty-action"
              >
                创建第一个模板
              </el-button>
            </div>
            <div v-else class="template-list">
              <draggable
                v-model="templateList"
                group="templates"
                item-key="id"
                handle=".drag-handle"
                ghost-class="ghost-template"
                :animation="200"
                class="template-draggable"
                @end="handleTemplateDragEnd"
              >
                <template #item="{element: template}">
                  <div
                    class="template-card"
                    :class="{ active: currentTemplateId === template.id }"
                    @click="selectTemplate(template.id)"
                  >
                    <div class="template-drag-handle drag-handle">
                      <el-icon><Rank /></el-icon>
                    </div>
                    <div class="template-main">
                      <div class="template-info">
                        <div class="template-name">{{ template.name }}</div>
                        <div class="template-desc" v-if="template.description">
                          {{ template.description }}
                        </div>
                      </div>
                      <div class="template-stats">
                        <el-tag size="small" type="info" effect="plain">
                          {{ getEntityCount(template) }}个实体
                        </el-tag>
                      </div>
                    </div>
                    <div class="template-dimensions">
                      <div class="dimension-tags">
                        <el-tag
                          v-for="dim in template.dimensions.slice(0, 3)"
                          :key="dim.name"
                          size="small"
                          effect="plain"
                          class="dimension-tag"
                        >
                          {{ dim.name }}
                        </el-tag>
                        <el-tag
                          v-if="template.dimensions.length > 3"
                          size="small"
                          type="info"
                          effect="plain"
                          class="more-tag"
                        >
                          +{{ template.dimensions.length - 3 }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="template-actions" @click.stop>
                      <el-dropdown trigger="click" @command="(cmd) => handleTemplateAction(cmd, template)">
                        <el-button size="small" text class="more-btn">
                          <el-icon><More /></el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item command="edit">
                              <el-icon><Edit /></el-icon>
                              编辑模板
                            </el-dropdown-item>
                            <el-dropdown-item command="export">
                              <el-icon><Download /></el-icon>
                              导出模板
                            </el-dropdown-item>
                            <el-dropdown-item command="delete" divided>
                              <el-icon><Delete /></el-icon>
                              删除模板
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧实体管理区域 -->
      <div class="native-content-area">
        <div v-if="!currentTemplateId" class="no-template-selected">
          <div class="placeholder-content">
            <div class="placeholder-icon">🎭</div>
            <div class="placeholder-title">选择一个模板开始管理实体</div>
            <div class="placeholder-desc">
              从左侧选择一个模板，或创建新的模板来管理设定实体
            </div>
          </div>
        </div>
        <div v-else class="entity-content">
          <!-- 实体管理头部 -->
          <div class="entity-header">
            <div class="entity-title-section">
              <div class="current-template-info">
                <h2 class="entity-title">{{ getCurrentTemplateName() }}</h2>
                <div class="template-meta">
                  <el-tag size="small" type="primary" effect="light">
                    {{ filteredEntities.length }} 个实体
                  </el-tag>
                  <el-tag size="small" type="info" effect="plain">
                    {{ getCurrentTemplate()?.dimensions?.length || 0 }} 个维度
                  </el-tag>
                </div>
              </div>
            </div>

            <div class="entity-actions">
              <!-- 搜索框 -->
              <el-input
                v-model="searchQuery"
                placeholder="搜索实体..."
                clearable
                size="default"
                class="native-search-input"
                @clear="handleSearchClear"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>

              <!-- 操作按钮组 -->
              <div class="action-buttons">
                <el-dropdown @command="handleExportCommand" class="export-dropdown">
                  <el-button size="default" class="native-btn">
                    <el-icon><Download /></el-icon>
                    导出
                    <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="exportCurrentNames" :disabled="!currentTemplateId">导出当前模板实体名称</el-dropdown-item>
                      <el-dropdown-item command="exportCurrentDetails" :disabled="!currentTemplateId">导出当前模板实体详情</el-dropdown-item>
                      <el-dropdown-item command="exportCurrentDetailsJson" :disabled="!currentTemplateId">导出当前模板JSON格式</el-dropdown-item>
                      <el-dropdown-item divided command="exportAllNames" :disabled="entityList.length === 0">导出所有模板实体名称</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>

                <el-button
                  size="default"
                  @click="handleImportEntity"
                  class="native-btn success-btn"
                >
                  <el-icon><Upload /></el-icon>
                  导入实体
                </el-button>

                <el-button
                  size="default"
                  @click="showEntityDialog()"
                  class="native-btn primary-btn"
                >
                  <el-icon><Plus /></el-icon>
                  新建实体
                </el-button>
              </div>
            </div>
          </div>

          <!-- 实体网格视图 - 修复滚动问题 -->
          <div class="entity-grid-container">
            <div class="entity-grid-wrapper">
              <div v-if="filteredEntities.length === 0" class="empty-entities">
                <div class="empty-icon">🎭</div>
                <div class="empty-text">暂无实体</div>
                <div class="empty-desc">点击"新建实体"创建第一个设定实体</div>
                <el-button
                  type="primary"
                  @click="showEntityDialog()"
                  class="empty-action"
                >
                  <el-icon><Plus /></el-icon>
                  创建实体
                </el-button>
              </div>

              <div v-else class="entity-grid">
                <div
                  v-for="entity in paginatedEntities"
                  :key="entity.id"
                  class="entity-card"
                  @click="showEntityDetail(entity)"
                >
                  <div class="entity-card-header">
                    <div class="entity-name">{{ entity.name }}</div>
                    <div class="entity-actions" @click.stop>
                      <el-dropdown trigger="click" @command="(cmd) => handleEntityAction(cmd, entity)">
                        <el-button size="small" text class="more-btn">
                          <el-icon><More /></el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item command="copy">
                              <el-icon><Document /></el-icon>
                              复制内容
                            </el-dropdown-item>
                            <el-dropdown-item command="copyJson">
                              <el-icon><DocumentCopy /></el-icon>
                              复制JSON
                            </el-dropdown-item>
                            <el-dropdown-item command="delete" divided>
                              <el-icon><Delete /></el-icon>
                              删除实体
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>

                  <div class="entity-card-body">
                    <div class="entity-description" v-if="entity.description">
                      {{ entity.description }}
                    </div>
                    <div class="entity-dimensions">
                      <div
                        v-for="(value, key) in limitedDimensions(entity.dimensions, 4)"
                        :key="key"
                        class="dimension-item"
                        :class="{ 'unset': value === '未设定' }"
                      >
                        <div class="dimension-label">{{ key }}</div>
                        <div class="dimension-value">{{ value === '未设定' ? '—' : value }}</div>
                      </div>
                      <div
                        v-if="Object.keys(entity.dimensions).length > 4"
                        class="dimension-item more-dimensions"
                      >
                        <div class="dimension-label">更多</div>
                        <div class="dimension-value">+{{ Object.keys(entity.dimensions).length - 4 }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-container" v-if="filteredEntities.length > 0">
            <el-pagination
              v-model="currentPage"
              :page-size="pageSize"
              :page-sizes="[12, 24, 48, 96]"
              :total="total"
              :pager-count="5"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
              layout="total, sizes, prev, pager, next"
              background
              small
            />
          </div>

        </div>
      </div>
    </div>

    <!-- 模板创建/编辑对话框 - 全屏原生风格 -->
    <el-drawer
      v-model="templateDialogVisible"
      :title="editingTemplate.id ? '编辑模板' : '创建模板'"
      direction="btt"
      size="100%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      class="native-template-drawer"
      @open="handleTemplateDialogOpen"
      @close="handleTemplateDialogClose"
    >
      <div class="fullscreen-content">
        <!-- 顶部工具栏 -->
        <div class="detail-toolbar">
          <div class="toolbar-left">
            <div class="template-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="template-info">
              <h2 class="entity-title">{{ editingTemplate.id ? '编辑模板' : '创建新模板' }}</h2>
              <div class="entity-meta">
                <span class="dimension-count">{{ editingTemplate.dimensions.length }} 个维度</span>
                <span class="template-status" v-if="editingTemplate.id">已保存</span>
                <span class="template-status new" v-else>新建</span>
              </div>
            </div>
          </div>
          <div class="toolbar-right">
            <el-button @click="templateDialogVisible = false" size="large" class="modern-button">
              <el-icon><Close /></el-icon>
              取消
            </el-button>
            <el-button
              type="primary"
              @click="saveTemplate"
              :disabled="!canSaveTemplate"
              size="large"
              class="modern-button"
            >
              <el-icon><Check /></el-icon>
              {{ editingTemplate.id ? '保存修改' : '创建模板' }}
            </el-button>
          </div>
        </div>

        <!-- 固定的基本信息区域 -->
        <div class="template-basic-info-fixed">
          <div class="section-title">
            <el-icon class="title-icon"><Edit /></el-icon>
            基本信息
          </div>
          <div class="form-grid">
            <div class="form-item">
              <label class="form-label">模板名称 <span class="required">*</span></label>
              <el-input
                v-model="editingTemplate.name"
                placeholder="请输入模板名称"
                clearable
                ref="templateNameInput"
                @keydown="handleTemplateInputKeydown"
                class="form-input"
                size="large"
              />
            </div>
            <div class="form-item full-width">
              <label class="form-label">模板描述</label>
              <el-input
                v-model="editingTemplate.description"
                type="textarea"
                :rows="3"
                placeholder="请输入模板描述（可选）"
                @keydown="handleTemplateInputKeydown"
                class="form-textarea"
                resize="none"
                maxlength="200"
                show-word-limit
              />
            </div>
          </div>
        </div>

        <!-- 固定的维度配置标题区域 -->
        <div class="template-dimensions-header-fixed">
          <div class="section-header">
            <div class="section-title">
              <el-icon class="title-icon"><DataLine /></el-icon>
              维度配置
              <div class="dimension-badge">{{ editingTemplate.dimensions.length }}</div>
            </div>
            <div class="section-actions">
              <el-button
                v-if="!showingDimensionInput"
                type="primary"
                @click="showingDimensionInput = true"
                class="add-dimension-btn"
                size="default"
              >
                <el-icon><Plus /></el-icon>
                添加维度
              </el-button>
            </div>
          </div>

          <!-- 添加维度输入区域 -->
          <div v-if="showingDimensionInput" class="add-dimension-input">
            <el-input
              v-model="newDimensionName"
              placeholder="请输入维度名称"
              ref="dimensionNameInput"
              @keydown.enter.prevent="addDimension"
              @keydown.esc="cancelDimensionInput"
              @blur="handleDimensionInputBlur"
              size="large"
              class="dimension-input"
            >
              <template #append>
                <el-button type="primary" @click="addDimension" size="large">
                  <el-icon><Check /></el-icon>
                </el-button>
              </template>
            </el-input>
            <div class="input-hint">按回车确认，ESC取消</div>
          </div>
        </div>

        <!-- 可滚动的维度列表区域 -->
        <div class="template-dimensions-scrollable">
          <div class="dimensions-container">
            <div v-if="editingTemplate.dimensions.length === 0" class="empty-state">
              <div class="empty-icon">📝</div>
              <div class="empty-title">暂无维度</div>
              <div class="empty-desc">点击"添加维度"按钮创建第一个维度</div>
            </div>
            <div v-else class="dimensions-form-native">
              <draggable
                v-model="editingTemplate.dimensions"
                group="dimensions"
                item-key="name"
                handle=".drag-handle"
                ghost-class="dimension-ghost"
                :animation="200"
                class="draggable-list"
              >
                <template #item="{element, index}">
                  <div class="dimension-form-item-native">
                    <div class="dimension-header">
                      <div class="dimension-label">
                        <el-icon class="drag-handle"><Rank /></el-icon>
                        {{ element.name }}
                      </div>
                      <el-button
                        size="small"
                        text
                        type="danger"
                        @click="removeDimension(index)"
                        class="remove-btn"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 实体创建/编辑对话框 - 原生全屏模式 -->
    <el-drawer
      v-model="entityDialogVisible"
      :title="`${editingEntity.id ? '编辑实体' : '创建实体'}: ${editingEntity.name || '未命名'}`"
      direction="btt"
      size="100%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="native-entity-create-drawer"
      @close="handleEntityDialogClose"
    >
      <div class="fullscreen-content">
        <!-- 顶部工具栏 -->
        <div class="detail-toolbar">
          <div class="toolbar-left">
            <h2 class="entity-title">{{ editingEntity.id ? '编辑实体' : '创建新实体' }}</h2>
            <div class="entity-meta">
              <el-tag size="small" type="info" effect="plain" v-if="getEditingEntityTemplate()">
                {{ getEditingEntityTemplate().name }}
              </el-tag>
              <el-tag size="small" type="primary" effect="light" v-if="getEditingEntityTemplate()">
                {{ getEditingEntityTemplate().dimensions.length }} 个维度
              </el-tag>
            </div>
          </div>
          <div class="toolbar-right">
            <el-button @click="entityDialogVisible = false" size="large" class="native-btn">
              <el-icon><Close /></el-icon>
              取消
            </el-button>
            <el-button type="primary" @click="saveEntity" size="large" class="native-btn primary-btn">
              <el-icon><Check /></el-icon>
              保存
            </el-button>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="detail-main-content">
          <!-- 基本信息区域 -->
          <div class="detail-basic-info">
            <h4 class="section-title">基本信息</h4>
            <div class="form-group-horizontal">
              <div class="form-field">
                <label class="field-label">实体名称 <span class="required">*</span></label>
                <el-input
                  v-model="editingEntity.name"
                  placeholder="输入实体名称"
                  size="large"
                  clearable
                  ref="entityNameInput"
                  @keydown="handleEntityInputKeydown"
                  class="native-input"
                />
              </div>
              <div class="form-field">
                <label class="field-label">描述</label>
                <el-input
                  v-model="editingEntity.description"
                  type="textarea"
                  :rows="4"
                  placeholder="输入实体描述"
                  @keydown="handleEntityInputKeydown"
                  class="native-textarea"
                />
              </div>
            </div>
          </div>

          <!-- 维度信息区域 -->
          <div class="detail-dimensions" v-if="currentTemplate">
            <div class="section-header">
              <h4 class="section-title">维度信息</h4>
              <div class="dimension-count">
                {{ currentTemplate.dimensions.length }} 个维度
              </div>
            </div>

            <div class="dimensions-grid">
              <div
                v-for="dimension in currentTemplate.dimensions"
                :key="dimension.name"
                class="dimension-card"
              >
                <div class="dimension-card-header">
                  <label
                    class="dimension-label clickable-label"
                    @dblclick="expandDimension(dimension.name)"
                    :title="`双击展开 ${dimension.name} 的大型编辑器`"
                  >
                    {{ dimension.name }}
                  </label>
                  <el-button
                    size="small"
                    text
                    @click="expandDimension(dimension.name)"
                    class="expand-btn"
                  >
                    <el-icon><FullScreen /></el-icon>
                  </el-button>
                </div>
                <el-input
                  v-model="editingEntity.dimensions[dimension.name]"
                  :placeholder="'输入' + dimension.name"
                  type="textarea"
                  :rows="6"
                  resize="vertical"
                  @keydown="handleEntityInputKeydown"
                  class="dimension-textarea native-textarea"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 实体详情对话框 - 原生全屏模式 -->
    <el-drawer
      v-model="entityDetailVisible"
      :title="`编辑实体: ${editingEntity.name || '未命名'}`"
      direction="btt"
      size="100%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="native-entity-detail-drawer"
    >
      <div class="fullscreen-content">
        <!-- 顶部工具栏 -->
        <div class="detail-toolbar">
          <div class="toolbar-left">
            <h2 class="entity-title">{{ editingEntity.name || '未命名实体' }}</h2>
            <div class="entity-meta">
              <el-tag size="small" type="info" effect="plain">
                {{ getEditingEntityTemplateName() }}
              </el-tag>
            </div>
          </div>
          <div class="toolbar-right">
            <el-button @click="entityDetailVisible = false" size="large" class="native-btn">
              <el-icon><Close /></el-icon>
              关闭
            </el-button>
            <el-button type="primary" @click="saveEntity" size="large" class="native-btn primary-btn">
              <el-icon><Check /></el-icon>
              保存
            </el-button>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="detail-main-content">
          <!-- 基本信息区域 -->
          <div class="detail-basic-info">
            <h4 class="section-title">基本信息</h4>
            <div class="form-group-horizontal">
              <div class="form-field">
                <label class="field-label">实体名称 <span class="required">*</span></label>
                <el-input
                  v-model="editingEntity.name"
                  placeholder="实体名称"
                  size="large"
                  @keydown="handleEntityInputKeydown"
                  class="native-input"
                />
              </div>
              <div class="form-field">
                <label class="field-label">描述</label>
                <el-input
                  v-model="editingEntity.description"
                  type="textarea"
                  :rows="4"
                  placeholder="输入实体描述"
                  @keydown="handleEntityInputKeydown"
                  class="native-textarea"
                />
              </div>
            </div>
          </div>

          <!-- 维度信息区域 -->
          <div class="detail-dimensions" v-if="currentTemplate">
            <div class="section-header">
              <h4 class="section-title">维度信息</h4>
              <div class="dimension-count">
                {{ currentTemplateDimensions.length }} 个维度
              </div>
            </div>

            <div class="dimensions-grid">
              <div
                v-for="dimension in currentTemplateDimensions"
                :key="dimension.name"
                class="dimension-card"
              >
                <div class="dimension-card-header">
                  <label
                    class="dimension-label clickable-label"
                    @dblclick="expandDimension(dimension.name)"
                    :title="`双击展开 ${dimension.name} 的大型编辑器`"
                  >
                    {{ dimension.name }}
                  </label>
                  <el-button
                    size="small"
                    text
                    @click="expandDimension(dimension.name)"
                    class="expand-btn"
                  >
                    <el-icon><FullScreen /></el-icon>
                  </el-button>
                </div>
                <el-input
                  v-model="editingEntity.dimensions[dimension.name]"
                  :placeholder="'输入' + dimension.name"
                  type="textarea"
                  :rows="6"
                  resize="vertical"
                  @keydown="handleEntityInputKeydown"
                  class="dimension-textarea native-textarea"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 大型维度编辑器抽屉 -->
    <el-drawer
      v-model="dimensionEditorVisible"
      :title="`编辑维度: ${currentEditingDimension}`"
      direction="btt"
      size="100%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      class="native-dimension-editor-drawer"
    >
      <div class="fullscreen-content">
        <!-- 顶部工具栏 -->
        <div class="detail-toolbar">
          <div class="toolbar-left">
            <div class="dimension-icon">
              <el-icon><Edit /></el-icon>
            </div>
            <div class="dimension-info">
              <h2 class="entity-title">编辑维度: {{ currentEditingDimension }}</h2>
              <div class="entity-meta">
                <span class="stat-item">
                  <el-icon><Document /></el-icon>
                  {{ dimensionContent.length }} 字符
                </span>
                <span class="stat-item">
                  <el-icon><List /></el-icon>
                  {{ dimensionContent.split('\n').length }} 行
                </span>
              </div>
            </div>
          </div>
          <div class="toolbar-right">
            <el-button
              @click="clearDimensionContent"
              size="large"
              class="modern-button danger-button"
            >
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
            <el-button
              @click="closeDimensionEditor"
              size="large"
              class="modern-button"
            >
              <el-icon><Close /></el-icon>
              取消
            </el-button>
            <el-button
              type="primary"
              @click="saveDimensionAndClose"
              size="large"
              class="modern-button"
            >
              <el-icon><Check /></el-icon>
              保存
            </el-button>
          </div>
        </div>

        <!-- 编辑器内容区域 -->
        <div class="dimension-editor-content">
          <div class="editor-wrapper">
            <el-input
              v-model="dimensionContent"
              type="textarea"
              placeholder="在此输入维度内容...&#10;&#10;支持多行文本编辑&#10;可以输入大量内容&#10;支持快捷键 Ctrl+S 保存"
              class="dimension-textarea"
              resize="none"
              @keydown="handleDimensionEditorKeydown"
              ref="dimensionTextarea"
            />
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 导入实体对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入实体"
      width="600px"
      class="import-dialog"
    >
      <div class="import-content">
        <el-collapse>
          <el-collapse-item>
            <template #title>
              <div class="format-hint-title">
                <el-icon><InfoFilled /></el-icon>
                <span>查看JSON格式示例</span>
              </div>
            </template>
            <div class="format-hint-content">
              <pre>{
  "name": "实体名称",
  "description": "实体描述",
  "dimensions": {
    "维度1": "值1",
    "维度2": "值2"
  }
}</pre>
            </div>
          </el-collapse-item>
        </el-collapse>
        <el-input
          v-model="importJsonContent"
          type="textarea"
          :rows="10"
          placeholder="请输入JSON字符串"
          class="import-input"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport">确认导入</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入模板对话框 -->
    <el-dialog
      v-model="importTemplateDialogVisible"
      title="导入模板"
      width="800px"
      class="import-template-dialog"
      :close-on-click-modal="false"
      :show-close="true"
    >
      <div class="import-content">
        <el-collapse>
          <el-collapse-item>
            <template #title>
              <div class="format-hint-title">
                <el-icon><InfoFilled /></el-icon>
                <span>查看JSON格式示例</span>
              </div>
            </template>
            <div class="format-hint-content">
              <pre>{
  "name": "模板名称",
  "description": "模板描述",
  "dimensions": [
    {
      "name": "维度1",
      "type": "text"
    },
    {
      "name": "维度2",
      "type": "text"
    }
  ]
}</pre>
            </div>
          </el-collapse-item>
        </el-collapse>
        <el-input
          v-model="importTemplateContent"
          type="textarea"
          :rows="10"
          placeholder="请输入模板JSON数据"
          class="import-input"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="large" @click="importTemplateDialogVisible = false">取消</el-button>
          <el-button size="large" type="primary" @click="confirmImportTemplate">确认导入</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, ArrowRight, ArrowDown, Plus, Document, DocumentCopy, Edit, Delete, Search, More, Upload, Download, InfoFilled, FullScreen, Rank, Check, Close, Collection, List, User, DataLine } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'

// 从路由中获取书籍信息
const route = useRoute()
const router = useRouter()

// 从路由中获取书籍信息
const bookId = ref(route.params.id || route.query.id)
const bookTitle = ref(route.query.title || route.params.title)

// 监听路由变化，更新书籍信息
watch(() => route.params, (newParams) => {
  const newBookId = newParams.id || route.query.id
  const newBookTitle = route.query.title || newParams.title

  // 如果书籍ID发生变化，更新相关数据
  if (newBookId && newBookId !== bookId.value) {
    console.log('路由参数变化，从书籍', bookId.value, '切换到', newBookId)
    bookId.value = newBookId
    bookTitle.value = newBookTitle

    // 重置相关状态
    currentTemplateId.value = ''
    templateList.value = []
    entityList.value = []
    allEntities.value = []

    // 重新加载数据
    loadTemplates()
  }
}, { deep: true })

// 监听查询参数变化
watch(() => route.query, (newQuery) => {
  if (newQuery.title && newQuery.title !== bookTitle.value) {
    bookTitle.value = newQuery.title
  }
}, { deep: true })

// 如果没有书籍ID，重定向到书籍列表
if (!bookId.value) {
  ElMessage.error('未找到书籍信息')
  router.push('/book/writing')
}

// 视图状态
const currentTemplateId = ref('')
const templateDialogVisible = ref(false)
const entityDialogVisible = ref(false)
const entityDetailVisible = ref(false)
const dimensionEditorVisible = ref(false)
const currentEditingDimension = ref('')
const dimensionContent = ref('')
const contextMenuVisible = ref(false)
const contextMenuX = ref(0)
const contextMenuY = ref(0)
const contextMenuDimension = ref('')

// 加载状态
const loading = ref(false)

// 数据列表
const templateList = ref([])
const entityList = ref([])
const allEntities = ref([]) // 新增：存储所有实体
const selectedEntity = ref(null)

// 编辑状态
const editingTemplate = ref({
  name: '',
  description: '',
  dimensions: []
})
const editingEntity = ref({})
// 维度输入状态
const inputDimensionVisible = ref(false)
const inputDimensionValue = ref('')
const dimensionInputRef = ref(null)
const entityNameInput = ref(null)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(12) // 改为网格布局适合的数量
const total = computed(() => filteredEntities.value.length)

const paginatedEntities = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredEntities.value.slice(start, end)
})

const handlePageChange = (page) => {
  currentPage.value = page
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

// 搜索相关
const searchQuery = ref('')

const handleSearchClear = () => {
  searchQuery.value = ''
  currentPage.value = 1
}

// 计算属性
const filteredEntities = computed(() => {
  if (!currentTemplateId.value) return []
  let filtered = entityList.value.filter(entity =>
      entity.template_id === currentTemplateId.value
  )

  // 如果有搜索关键词，进行过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    filtered = filtered.filter(entity =>
        entity.name.toLowerCase().includes(query)
    )
  }

  // 按更新时间排序，最新的在前面
  filtered.sort((a, b) => {
    const timeA = new Date(a.updated_at || a.created_at).getTime()
    const timeB = new Date(b.updated_at || b.created_at).getTime()
    return timeB - timeA
  })

  return filtered
})

const canSaveTemplate = computed(() => {
  return editingTemplate.value.name.trim() &&
      editingTemplate.value.dimensions.length > 0
})

const currentTemplate = computed(() => {
  return templateList.value.find(t => t.id === currentTemplateId.value)
})

// 新增计算属性
const filteredTemplates = computed(() => {
  if (!templateSearchQuery.value.trim()) {
    return templateList.value
  }
  const query = templateSearchQuery.value.toLowerCase().trim()
  return templateList.value.filter(template =>
    template.name.toLowerCase().includes(query) ||
    (template.description && template.description.toLowerCase().includes(query))
  )
})

// 模板搜索相关
const templateSearchQuery = ref('')

const handleTemplateSearchClear = () => {
  templateSearchQuery.value = ''
}

// 新增维度输入相关状态
const showingDimensionInput = ref(false)
const newDimensionName = ref('')
const dimensionNameInput = ref(null)

// 添加维度方法
const addDimension = () => {
  const name = newDimensionName.value.trim()
  if (!name) {
    showingDimensionInput.value = false
    return
  }

  // 检查是否已存在相同名称的维度
  const isDuplicate = editingTemplate.value.dimensions.some(dim => dim.name === name)
  if (isDuplicate) {
    ElMessage.warning('已存在相同名称的维度')
    return
  }

  editingTemplate.value.dimensions.push({
    name: name,
    type: 'text',
    required: false
  })

  newDimensionName.value = ''
  showingDimensionInput.value = false
}

// 移除维度方法
const removeDimension = (index) => {
  editingTemplate.value.dimensions.splice(index, 1)
}

// 处理维度输入失焦
const handleDimensionInputBlur = () => {
  setTimeout(() => {
    if (!newDimensionName.value.trim()) {
      showingDimensionInput.value = false
    }
  }, 200)
}

// 处理维度按钮点击
const handleDimensionButtonClick = () => {
  addDimension()
}

// 模板拖拽排序结束处理
const handleTemplateDragEnd = async () => {
  try {
    // 更新排序字段
    templateList.value.forEach((template, index) => {
      template.sort_order = index
    })

    // 保存到后端
    await saveTemplateOrder()
    ElMessage.success('模板排序已更新')
  } catch (error) {
    console.error('更新模板排序失败:', error)
    ElMessage.error('更新模板排序失败: ' + error.message)
    // 重新加载模板以恢复原始顺序
    await loadTemplates()
  }
}

// 保存模板排序到后端
const saveTemplateOrder = async () => {
  try {
    const orderData = templateList.value.map((template, index) => ({
      id: template.id,
      sort_order: index
    }))

    const response = await window.pywebview.api.book_controller.update_template_order({
      book_id: bookId.value,
      templates: orderData
    })

    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status !== 'success') {
      throw new Error(result.message || '保存排序失败')
    }
  } catch (error) {
    console.error('保存模板排序失败:', error)
    throw error
  }
}

// 获取当前模板信息的方法
const getCurrentTemplate = () => {
  return templateList.value.find(t => t.id === currentTemplateId.value)
}

const getCurrentTemplateName = () => {
  const template = getCurrentTemplate()
  return template ? template.name : '未选择模板'
}

// 获取编辑实体的模板名称
const getEditingEntityTemplateName = () => {
  if (!editingEntity.value?.template_id) return '未选择模板'
  const template = templateList.value.find(t => t.id === editingEntity.value.template_id)
  return template ? template.name : '未知模板'
}

// 获取编辑实体的模板对象
const getEditingEntityTemplate = () => {
  if (!editingEntity.value?.template_id) return null
  return templateList.value.find(t => t.id === editingEntity.value.template_id) || null
}

// 获取模板名称
const getTemplateName = (templateId) => {
  const template = templateList.value.find(t => t.id === templateId)
  return template ? template.name : '未知模板'
}

// 获取实体数量
const getEntityCount = (template) => {
  return allEntities.value.filter(entity => entity.template_id === template.id).length
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  if (isNaN(d.getTime())) return date
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 返回上一页
const goBack = () => {
  router.push({
    name: 'bookWriting'
  })
}

// 选择模板
const selectTemplate = (templateId) => {
  currentTemplateId.value = templateId
  handleTemplateChange(templateId)
}

// 处理模板操作
const handleTemplateAction = async (command, template) => {
  switch (command) {
    case 'edit':
      showTemplateDialog(template)
      break
    case 'export':
      await handleExportTemplate(template)
      break
    case 'delete':
      await deleteTemplate(template)
      break
  }
}

// 处理实体操作
const handleEntityAction = async (command, entity) => {
  switch (command) {
    case 'copy':
      await copyEntityToClipboard(entity)
      break
    case 'copyJson':
      await copyEntityJsonToClipboard(entity)
      break
    case 'delete':
      await deleteEntity(entity)
      break
  }
}

// 模板相关方法
const showTemplateDialog = (template = {}) => {
  editingTemplate.value = {
    name: '',
    description: '',
    dimensions: [],
    ...template
  }
  templateDialogVisible.value = true
}

const resetTemplateForm = () => {
  editingTemplate.value = {
    name: '',
    description: '',
    dimensions: []
  }
  inputDimensionVisible.value = false
  inputDimensionValue.value = ''
}

const handleDimensionInputKeydown = (e) => {
  if (e.key === 'Enter') {
    if (e.shiftKey) {
      // Shift+Enter: 确认并关闭
      e.preventDefault()
      handleDimensionInputConfirm(false)
    } else {
      // Enter: 确认并继续
      e.preventDefault()
      handleDimensionInputConfirm(true)
    }
  } else if (e.key === 'Escape') {
    // Esc: 取消输入
    inputDimensionVisible.value = false
    inputDimensionValue.value = ''
  }
}

const handleDimensionInputConfirm = (continueAdding = true) => {
  const value = inputDimensionValue.value.trim()
  if (value) {
    // 检查是否已存在相同名称的维度
    const isDuplicate = editingTemplate.value.dimensions.some(dim => dim.name === value)
    if (isDuplicate) {
      ElMessage.warning('已存在相同名称的维度')
      return
    }

    editingTemplate.value.dimensions = editingTemplate.value.dimensions || []
    editingTemplate.value.dimensions.push({
      name: value,
      type: 'text',
      required: false
    })

    if (continueAdding) {
      // 清空输入并保持输入框显示
      inputDimensionValue.value = ''
      nextTick(() => {
        dimensionInputRef.value?.input?.focus()
      })
    } else {
      // 关闭输入框
      inputDimensionVisible.value = false
      inputDimensionValue.value = ''
    }
  } else {
    // 如果输入为空且按了确认，则关闭输入框
    inputDimensionVisible.value = false
    inputDimensionValue.value = ''
  }
}

const showDimensionInput = () => {
  inputDimensionVisible.value = true
  nextTick(() => {
    dimensionInputRef.value?.input?.focus()
  })
}

const handleDimensionClose = (index) => {
  editingTemplate.value.dimensions.splice(index, 1)
}

const saveTemplate = async () => {
  try {
    console.log(bookId.value)
    const response = await window.pywebview.api.book_controller.save_template({
      ...editingTemplate.value,
      book_id: bookId.value
    })
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success('保存成功')
      templateDialogVisible.value = false
      loadTemplates()
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  }
}

const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(
        '删除模板将同时删除该模板下的所有实体，是否继续？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const response = await window.pywebview.api.book_controller.delete_template(template.id, bookId.value)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success('删除成功')
      loadTemplates()
      await loadAllEntities()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

// 实体相关方法
const showEntityDialog = (entity = {}) => {
  const template = templateList.value.find(t => t.id === (entity.template_id || currentTemplateId.value))

  editingEntity.value = {
    id: '',
    name: '',
    description: '',
    template_id: template?.id || '',
    dimensions: {},
    ...entity
  }

  // 初始化维度字段
  if (template && template.dimensions) {
    const initialDimensions = template.dimensions.reduce((acc, dim) => {
      acc[dim.name] = editingEntity.value.dimensions?.[dim.name] || ''
      return acc
    }, {})
    editingEntity.value.dimensions = initialDimensions
  }

  entityDialogVisible.value = true
}

const createEntityFromTemplate = (template) => {
  currentTemplateId.value = template.id

  // 使用模板的维度创建新实体
  const initialDimensions = template.dimensions.reduce((acc, dim) => {
    acc[dim.name] = ''
    return acc
  }, {})

  showEntityDialog({
    template_id: template.id,
    dimensions: initialDimensions
  })
}

const handleEntityInputKeydown = (e) => {
  if (e.key === 'Enter') {
    if (e.shiftKey) {
      // Shift+Enter: 仅保存实体
      e.preventDefault()
      saveEntity()
    } else if (!e.ctrlKey && !e.altKey && !e.metaKey && e.target.tagName !== 'TEXTAREA') {
      // Enter: 保存并关闭弹窗（仅对非文本域元素）
      e.preventDefault()
      saveEntity().then(() => {
        entityDialogVisible.value = false
        entityDetailVisible.value = false
      })
    }
  }
}

const saveEntity = async () => {
  try {
    if (!editingEntity.value.name) {
      ElMessage.error('请输入实体名称')
      return Promise.reject('请输入实体名称')
    }

    if (!editingEntity.value.template_id) {
      ElMessage.error('请选择模板类型')
      return Promise.reject('请选择模板类型')
    }

    const template = templateList.value.find(t => t.id === editingEntity.value.template_id)
    if (!template) {
      ElMessage.error('无效的模板类型')
      return Promise.reject('无效的模板类型')
    }

    // 检查同一模板下是否存在同名实体
    const existingEntity = entityList.value.find(entity =>
        entity.template_id === editingEntity.value.template_id &&
        entity.name === editingEntity.value.name &&
        entity.id !== editingEntity.value.id  // 排除当前编辑的实体（编辑模式下）
    )

    if (existingEntity) {
      ElMessage.error(`当前模板下已存在名为"${editingEntity.value.name}"的实体`)
      return Promise.reject(`当前模板下已存在名为"${editingEntity.value.name}"的实体`)
    }

    // 将空维度设置为"未设定"，并确保所有维度值都是字符串类型
    template.dimensions.forEach(dim => {
      const dimensionValue = editingEntity.value.dimensions[dim.name]
      if (dimensionValue === null || dimensionValue === undefined ||
          (typeof dimensionValue === 'string' && dimensionValue.trim() === '')) {
        editingEntity.value.dimensions[dim.name] = '未设定'
      } else {
        // 确保所有值都转换为字符串
        editingEntity.value.dimensions[dim.name] = String(dimensionValue)
      }
    })

    const response = await window.pywebview.api.book_controller.save_entity({
      ...editingEntity.value,
      book_id: bookId.value
    })
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success(editingEntity.value.id ? '更新成功' : '创建成功')
      entityDialogVisible.value = false
      // 重新加载所有实体
      await loadAllEntities()
      return Promise.resolve()
    } else {
      throw new Error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存实体失败:', error)
    ElMessage.error('保存失败：' + error.message)
    return Promise.reject(error)
  }
}

const deleteEntity = async (entity) => {
  try {
    await ElMessageBox.confirm(
        '确定要删除这个实体吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const response = await window.pywebview.api.book_controller.delete_entity(entity.id, bookId.value, entity.template_id)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      ElMessage.success('删除成功')

      // 立即从本地状态中移除实体，提供即时反馈
      const entityIndex = allEntities.value.findIndex(e => e.id === entity.id)
      if (entityIndex !== -1) {
        allEntities.value.splice(entityIndex, 1)
      }

      // 同时更新当前显示的实体列表
      if (currentTemplateId.value) {
        entityList.value = allEntities.value.filter(e => e.template_id === currentTemplateId.value)
      }

      // 重新加载所有实体以确保数据一致性
      await loadAllEntities()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

// 处理模板选择变化
const handleTemplateChange = async (templateId) => {
  currentTemplateId.value = templateId
  if (templateId) {
    // 从所有实体中筛选当前模板的实体
    entityList.value = allEntities.value.filter(entity => entity.template_id === templateId)
  } else {
    entityList.value = []
  }
}

const limitedDimensions = (dimensions, limit = 3) => {
  const entries = Object.entries(dimensions)
  const limited = entries.slice(0, limit)
  return Object.fromEntries(limited)
}

const currentTemplateDimensions = computed(() => {
  if (!editingEntity.value?.template_id) return []
  const template = templateList.value.find(t => t.id === editingEntity.value.template_id)
  return template?.dimensions || []
})

const showEntityDetail = (entity) => {
  const template = templateList.value.find(t => t.id === entity.template_id)
  if (!template) {
    ElMessage.error('找不到对应的模板')
    return
  }

  // 创建编辑对象的副本
  editingEntity.value = JSON.parse(JSON.stringify(entity))

  // 确保dimensions对象存在
  if (!editingEntity.value.dimensions) {
    editingEntity.value.dimensions = {}
  }

  // 同步模板维度，保留已有的值，添加新的维度
  template.dimensions.forEach(dimension => {
    if (!(dimension.name in editingEntity.value.dimensions)) {
      editingEntity.value.dimensions[dimension.name] = ''
    }
  })

  entityDetailVisible.value = true
}

// 展开维度编辑器
const expandDimension = (dimensionName) => {
  currentEditingDimension.value = dimensionName
  dimensionContent.value = editingEntity.value.dimensions[dimensionName] || ''
  dimensionEditorVisible.value = true
}

// 保存维度内容并关闭编辑器
const saveDimensionAndClose = () => {
  editingEntity.value.dimensions[currentEditingDimension.value] = dimensionContent.value
  dimensionEditorVisible.value = false
  ElMessage.success('维度内容已保存')
}

// 关闭维度编辑器
const closeDimensionEditor = () => {
  dimensionEditorVisible.value = false
  dimensionContent.value = ''
  currentEditingDimension.value = ''
}

// 清空维度内容
const clearDimensionContent = () => {
  dimensionContent.value = ''
}

// 处理维度编辑器键盘事件
const handleDimensionEditorKeydown = (e) => {
  if (e.key === 'Escape') {
    closeDimensionEditor()
  } else if (e.key === 's' && (e.ctrlKey || e.metaKey)) {
    e.preventDefault()
    saveDimensionAndClose()
  }
}

// 数据加载方法
const loadTemplates = async () => {
  loading.value = true
  try {
    const response = await window.pywebview.api.book_controller.get_templates(bookId.value)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      let templates = result.data || []

      // 按照sort_order排序，如果没有sort_order则按创建时间排序
      templates.sort((a, b) => {
        if (a.sort_order !== undefined && b.sort_order !== undefined) {
          return a.sort_order - b.sort_order
        }
        if (a.sort_order !== undefined) return -1
        if (b.sort_order !== undefined) return 1
        return new Date(a.created_at || 0) - new Date(b.created_at || 0)
      })

      templateList.value = templates

      // 如果有模板，默认选择第一个
      if (templateList.value.length > 0 && !currentTemplateId.value) {
        currentTemplateId.value = templateList.value[0].id
      }
      // 加载所有实体
      await loadAllEntities()

      // 延迟初始化拖拽排序，确保DOM已更新
      setTimeout(() => {
        initSortable()
      }, 100)
    } else {
      ElMessage.error(result.message || '加载模板失败')
    }
  } catch (error) {
    ElMessage.error('加载模板失败：' + error.message)
  } finally {
    loading.value = false
  }
}

const loadAllEntities = async () => {
  try {
    const response = await window.pywebview.api.book_controller.get_entities(bookId.value)
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      allEntities.value = result.data || []
      // 如果当前已选择模板，更新entityList
      if (currentTemplateId.value) {
        entityList.value = allEntities.value.filter(entity =>
          entity.template_id === currentTemplateId.value
        )
      }
    } else {
      ElMessage.error(result.message || '加载实体失败')
    }
  } catch (error) {
    console.error('加载实体失败：', error)
    ElMessage.error('加载实体失败：' + error.message)
  }
}

// 生命周期钩子
onMounted(async () => {
  console.log('组件挂载，bookId:', bookId.value)
  if (bookId.value) {
    await loadTemplates()
  }
})

onUnmounted(() => {
  // 清理资源
})

// 导出实体名称
const exportEntityNames = async () => {
  try {
    // 获取当前模板信息
    const currentTemplate = templateList.value.find(t => t.id === currentTemplateId.value)
    if (!currentTemplate) {
      ElMessage.error('请先选择一个模板')
      return
    }

    // 检查当前模板下是否有实体
    const templateEntities = entityList.value.filter(e => e.template_id === currentTemplateId.value)
    if (templateEntities.length === 0) {
      ElMessage.error('当前模板下没有实体')
      return
    }

    // 显示加载提示
    const loadingMessage = ElMessage({
      message: '正在导出实体名称...',
      type: 'info',
      duration: 0
    })

    try {
      // 准备导出数据
      const exportData = {
        book_id: bookId.value,
        template_id: currentTemplateId.value,
        type: 'names'
      }

      const response = await window.pywebview.api.book_controller.save_entity_export(exportData)
      const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response

      loadingMessage.close()

      if (parsedResponse.status === "success") {
        ElMessage.success('导出成功！文件已保存到指定目录')
      } else {
        // 处理特定的编码错误
        if (parsedResponse.message && parsedResponse.message.includes('gbk')) {
          ElMessage.warning('文件包含特殊字符，已使用UTF-8编码保存。如需GBK编码，请检查实体名称中的特殊字符。')
        } else {
          ElMessage.error(`导出失败: ${parsedResponse.message}`)
        }
      }
    } catch (exportError) {
      loadingMessage.close()
      throw exportError
    }
  } catch (error) {
    console.error('导出错误:', error)
    ElMessage.error(`导出出错: ${error.message}`)
  }
}

// 处理导出下拉菜单命令
const handleExportCommand = (command) => {
  switch (command) {
    case 'exportCurrentNames':
      exportEntityNames();
      break;
    case 'exportCurrentDetails':
      exportEntityDetails();
      break;
    case 'exportCurrentDetailsJson':
      exportEntityDetailsJson();
      break;
    case 'exportAllNames':
      exportAllEntitiesNames();
      break;
  }
}

// 导出所有模板下的实体名称
const exportAllEntitiesNames = async () => {
  try {
    // 检查是否有实体
    if (entityList.value.length === 0) {
      ElMessage.error('没有可导出的实体')
      return
    }

    // 显示加载提示
    const loadingMessage = ElMessage({
      message: '正在导出所有实体名称...',
      type: 'info',
      duration: 0
    })

    try {
      // 准备导出数据
      const exportData = {
        book_id: bookId.value,
        template_id: 'all',
        type: 'all_names'
      }

      const response = await window.pywebview.api.book_controller.save_entity_export(exportData)
      const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response

      loadingMessage.close()

      if (parsedResponse.status === "success") {
        ElMessage.success('导出成功！文件已保存到指定目录')
      } else {
        // 处理特定的编码错误
        if (parsedResponse.message && parsedResponse.message.includes('gbk')) {
          ElMessage.warning('文件包含特殊字符，已使用UTF-8编码保存。如需GBK编码，请检查实体名称中的特殊字符。')
        } else {
          ElMessage.error(`导出失败: ${parsedResponse.message}`)
        }
      }
    } catch (exportError) {
      loadingMessage.close()
      throw exportError
    }
  } catch (error) {
    console.error('导出错误:', error)
    ElMessage.error(`导出出错: ${error.message}`)
  }
}

// 导出实体详情
const exportEntityDetails = async () => {
  try {
    // 获取当前模板信息
    const currentTemplate = templateList.value.find(t => t.id === currentTemplateId.value)
    if (!currentTemplate) {
      ElMessage.error('请先选择一个模板')
      return
    }

    // 检查当前模板下是否有实体
    const templateEntities = entityList.value.filter(e => e.template_id === currentTemplateId.value)
    if (templateEntities.length === 0) {
      ElMessage.error('当前模板下没有实体')
      return
    }

    // 显示加载提示
    const loadingMessage = ElMessage({
      message: '正在导出实体详情...',
      type: 'info',
      duration: 0
    })

    try {
      // 准备导出数据
      const exportData = {
        book_id: bookId.value,
        template_id: currentTemplateId.value,
        type: 'details'
      }

      const response = await window.pywebview.api.book_controller.save_entity_export(exportData)
      const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response

      loadingMessage.close()

      if (parsedResponse.status === "success") {
        ElMessage.success('导出成功！文件已保存到指定目录')
      } else {
        // 处理特定的编码错误
        if (parsedResponse.message && parsedResponse.message.includes('gbk')) {
          ElMessage.warning('文件包含特殊字符，已使用UTF-8编码保存。如需GBK编码，请检查实体内容中的特殊字符。')
        } else {
          ElMessage.error(`导出失败: ${parsedResponse.message}`)
        }
      }
    } catch (exportError) {
      loadingMessage.close()
      throw exportError
    }
  } catch (error) {
    console.error('导出错误:', error)
    ElMessage.error(`导出出错: ${error.message}`)
  }
}

// 导出实体详情JSON版本
const exportEntityDetailsJson = async () => {
  try {
    // 获取当前模板信息
    const currentTemplate = templateList.value.find(t => t.id === currentTemplateId.value)
    if (!currentTemplate) {
      ElMessage.error('请先选择一个模板')
      return
    }

    // 检查当前模板下是否有实体
    const templateEntities = entityList.value.filter(e => e.template_id === currentTemplateId.value)
    if (templateEntities.length === 0) {
      ElMessage.error('当前模板下没有实体')
      return
    }

    // 显示加载提示
    const loadingMessage = ElMessage({
      message: '正在导出JSON格式实体详情...',
      type: 'info',
      duration: 0
    })

    try {
      // 准备导出数据
      const exportData = {
        book_id: bookId.value,
        template_id: currentTemplateId.value,
        type: 'details_json'
      }

      const response = await window.pywebview.api.book_controller.save_entity_export(exportData)
      const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response

      loadingMessage.close()

      if (parsedResponse.status === "success") {
        ElMessage.success('导出成功！JSON文件已保存到指定目录')
      } else {
        ElMessage.error(`导出失败: ${parsedResponse.message}`)
      }
    } catch (exportError) {
      loadingMessage.close()
      throw exportError
    }
  } catch (error) {
    console.error('导出错误:', error)
    ElMessage.error(`导出出错: ${error.message}`)
  }
}

const handleDialogOpened = () => {
  nextTick(() => {
    entityNameInput.value?.input?.focus()
  })
}

const handleEntityDialogClose = () => {
  // 重置编辑状态
  editingEntity.value = {}
}

const handleTemplateDialogOpen = () => {
  // 延迟聚焦，避免抖动
  setTimeout(() => {
    const templateNameInput = document.querySelector('.native-template-drawer .form-input input')
    if (templateNameInput) {
      templateNameInput.focus({ preventScroll: true })
    }
  }, 300) // 等待抽屉动画完成
}

const handleTemplateDialogClose = () => {
  // 重置模板编辑状态
  editingTemplate.value = {
    name: '',
    description: '',
    dimensions: []
  }
  showingDimensionInput.value = false
  newDimensionName.value = ''
}

const handleTemplateInputKeydown = (e) => {
  if (e.key === 'Enter' && !e.shiftKey && e.target.tagName !== 'TEXTAREA') {
    // Enter: 保存模板（仅对非文本域元素）
    e.preventDefault()
    if (canSaveTemplate.value) {
      saveTemplate()
    }
  }
}

const cancelDimensionInput = () => {
  showingDimensionInput.value = false
  newDimensionName.value = ''
}

// 在 script setup 部分添加复制功能
const copyEntityToClipboard = async (entity) => {
  try {
    // 构建格式化的文本内容
    let content = `【${entity.name}】\n`;

    if (entity.description && entity.description.trim()) {
      // 处理描述中的换行符，将其替换为空格
      const cleanDescription = entity.description.replace(/\r?\n/g, ' ').replace(/\s+/g, ' ').trim();
      content += `  描述: ${cleanDescription}\n`;
    }

    if (entity.dimensions && Object.keys(entity.dimensions).length > 0) {
      // 过滤出有效的维度信息
      const validDimensions = Object.entries(entity.dimensions).filter(([, value]) =>
        value && value.trim() !== '' && value !== '未设定'
      );

      if (validDimensions.length > 0) {
        content += '\n  维度信息:\n';
        validDimensions.forEach(([key, value]) => {
          // 处理值中的换行符，将其替换为空格或其他分隔符
          const cleanValue = value.replace(/\r?\n/g, ' ').replace(/\s+/g, ' ').trim();
          content += `    • ${key}: ${cleanValue}\n`;
        });
      }
    }

    // 添加分隔线
    content += '\n' + '─'.repeat(30) + '\n';

    await window.pywebview.api.copy_to_clipboard(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败：' + error.message)
  }
}

// 复制完整JSON数据功能
const copyEntityJsonToClipboard = async (entity) => {
  try {
    // 复制完整的实体对象JSON数据，包括所有字段
    await window.pywebview.api.copy_to_clipboard(JSON.stringify(entity, null, 2))
    ElMessage.success('实体完整JSON数据已复制到剪贴板')
  } catch (error) {
    console.error('复制完整JSON失败:', error)
    ElMessage.error('复制完整JSON失败：' + error.message)
  }
}

// 导入相关状态
const importDialogVisible = ref(false)
const importJsonContent = ref('')

// 处理导入实体
const handleImportEntity = () => {
  importDialogVisible.value = true
  importJsonContent.value = ''
}

// 确认导入
const confirmImport = async () => {
  try {
    if (!importJsonContent.value.trim()) {
      ElMessage.error('请输入JSON字符串')
      return
    }

    // 解析JSON
    const jsonContent = JSON.parse(importJsonContent.value)

    // 验证必要字段
    if (!jsonContent.name) {
      ElMessage.error('导入失败：缺少实体名称')
      return
    }

    // 检查是否存在有效的 template_id
    const targetTemplateId = currentTemplateId.value
    if (!targetTemplateId) {
      ElMessage.error('请先选择一个模板')
      return
    }

    // 检查同名实体
    const existingEntity = entityList.value.find(entity =>
        entity.template_id === targetTemplateId &&
        entity.name === jsonContent.name
    )

    // 如果存在同名实体，询问是否覆盖
    if (existingEntity) {
      const confirmResult = await ElMessageBox.confirm(
        `当前模板下已存在名为"${jsonContent.name}"的实体，是否覆盖？`,
        '警告',
        {
          confirmButtonText: '覆盖',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).catch(() => false)

      if (!confirmResult) return
    }

    // 准备实体数据
    const entityData = {
      name: jsonContent.name,
      description: jsonContent.description || '',
      dimensions: {},
      template_id: targetTemplateId,
      book_id: bookId.value
    }

    // 如果是覆盖现有实体，添加id字段
    if (existingEntity) {
      entityData.id = existingEntity.id
    }

    // 获取当前模板
    const currentTemplate = templateList.value.find(t => t.id === targetTemplateId)

    // 处理维度数据，只接受模板中定义的维度
    currentTemplate.dimensions.forEach(dim => {
      entityData.dimensions[dim.name] =
        jsonContent.dimensions && jsonContent.dimensions[dim.name] !== undefined
          ? jsonContent.dimensions[dim.name]
          : '未设定'
    })

    // 保存实体
    const response = await window.pywebview.api.book_controller.save_entity(entityData)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success(existingEntity ? '更新成功' : '导入成功')
      importDialogVisible.value = false
      loadAllEntities()
    } else {
      throw new Error(result.message || '导入失败')
    }
  } catch (error) {
    if (error instanceof SyntaxError) {
      ElMessage.error('导入失败：JSON格式不正确')
    } else {
      console.error('导入失败:', error)
      ElMessage.error('导入失败：' + error.message)
    }
  }
}

// 导入模板相关
const importTemplateDialogVisible = ref(false)
const importTemplateContent = ref('')

const showImportTemplateDialog = () => {
  importTemplateDialogVisible.value = true
  importTemplateContent.value = ''
}

const confirmImportTemplate = async () => {
  try {
    if (!importTemplateContent.value.trim()) {
      ElMessage.error('请输入模板数据')
      return
    }

    // 解析JSON
    const templateData = JSON.parse(importTemplateContent.value)

    // 验证必要字段
    if (!templateData.name || !Array.isArray(templateData.dimensions)) {
      ElMessage.error('模板数据格式不正确')
      return
    }

    // 检查是否存在同名模板
    const existingTemplate = templateList.value.find(t => t.name === templateData.name)
    if (existingTemplate) {
      const confirmResult = await ElMessageBox.confirm(
        `已存在名为"${templateData.name}"的模板，是否覆盖？`,
        '警告',
        {
          confirmButtonText: '覆盖',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).catch(() => false)

      if (!confirmResult) return
    }

    // 准备模板数据
    const saveData = {
      ...templateData,
      book_id: bookId.value
    }

    // 如果是覆盖现有模板，添加id
    if (existingTemplate) {
      saveData.id = existingTemplate.id
    }

    // 保存模板
    const response = await window.pywebview.api.book_controller.save_template(saveData)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success(existingTemplate ? '更新成功' : '导入成功')
      importTemplateDialogVisible.value = false
      loadTemplates()
    } else {
      throw new Error(result.message || '导入失败')
    }
  } catch (error) {
    if (error instanceof SyntaxError) {
      ElMessage.error('导入失败：JSON格式不正确')
    } else {
      console.error('导入失败:', error)
      ElMessage.error('导入失败：' + error.message)
    }
  }
}

// 导出模板
const handleExportTemplate = async (template) => {
  try {
    // 准备导出数据
    const exportData = {
      name: template.name,
      description: template.description,
      dimensions: template.dimensions
    }

    // 复制到剪贴板
    await window.pywebview.api.copy_to_clipboard(JSON.stringify(exportData, null, 2))
    ElMessage.success('模板数据已复制到剪贴板')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败：' + error.message)
  }
}

// 添加templateNameInput的定义
const templateNameInput = ref(null)

// 确保在显示维度输入框时聚焦
watch(showingDimensionInput, (newVal) => {
  if (newVal) {
    nextTick(() => {
      if (dimensionNameInput.value) {
        dimensionNameInput.value.focus()
      }
    })
  }
})


</script>

<style lang="scss" scoped>
/* ===== 企业级设定管理界面 - 专业主题色彩系统 ===== */

/* 定义专业的企业级颜色变量 */
.setting-manager.native-app {
  /* 企业级主色调系统 */
  --enterprise-primary: #2563eb;
  --enterprise-primary-hover: #1d4ed8;
  --enterprise-primary-light: #3b82f6;
  --enterprise-primary-lighter: #60a5fa;
  --enterprise-primary-lightest: #dbeafe;

  /* 企业级中性色系统 */
  --enterprise-neutral-50: #f8fafc;
  --enterprise-neutral-100: #f1f5f9;
  --enterprise-neutral-200: #e2e8f0;
  --enterprise-neutral-300: #cbd5e1;
  --enterprise-neutral-400: #94a3b8;
  --enterprise-neutral-500: #64748b;
  --enterprise-neutral-600: #475569;
  --enterprise-neutral-700: #334155;
  --enterprise-neutral-800: #1e293b;
  --enterprise-neutral-900: #0f172a;

  /* 企业级功能色系统 */
  --enterprise-success: #059669;
  --enterprise-success-light: #10b981;
  --enterprise-success-bg: #ecfdf5;
  --enterprise-warning: #d97706;
  --enterprise-warning-light: #f59e0b;
  --enterprise-warning-bg: #fffbeb;
  --enterprise-danger: #dc2626;
  --enterprise-danger-light: #ef4444;
  --enterprise-danger-bg: #fef2f2;

  /* 企业级阴影系统 */
  --enterprise-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --enterprise-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --enterprise-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --enterprise-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --enterprise-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 企业级边框系统 */
  --enterprise-border-light: rgba(148, 163, 184, 0.2);
  --enterprise-border: rgba(148, 163, 184, 0.3);
  --enterprise-border-strong: rgba(148, 163, 184, 0.4);
}

/* Light主题颜色覆盖 */
:root[data-theme="light"] .setting-manager.native-app,
html.light .setting-manager.native-app {
  /* 背景色系统 */
  --app-bg-primary: var(--enterprise-neutral-50);
  --app-bg-secondary: #ffffff;
  --app-bg-tertiary: var(--enterprise-neutral-100);
  --app-bg-elevated: #ffffff;

  /* 文字色系统 */
  --app-text-primary: var(--enterprise-neutral-900);
  --app-text-secondary: var(--enterprise-neutral-700);
  --app-text-tertiary: var(--enterprise-neutral-500);
  --app-text-placeholder: var(--enterprise-neutral-400);

  /* 边框色系统 */
  --app-border-light: var(--enterprise-neutral-200);
  --app-border: var(--enterprise-neutral-300);
  --app-border-strong: var(--enterprise-neutral-400);

  /* 交互色系统 */
  --app-primary: var(--enterprise-primary);
  --app-primary-hover: var(--enterprise-primary-hover);
  --app-primary-bg: var(--enterprise-primary-lightest);
  --app-primary-border: var(--enterprise-primary-lighter);

  /* 状态色系统 */
  --app-success: var(--enterprise-success);
  --app-success-bg: var(--enterprise-success-bg);
  --app-warning: var(--enterprise-warning);
  --app-warning-bg: var(--enterprise-warning-bg);
  --app-danger: var(--enterprise-danger);
  --app-danger-bg: var(--enterprise-danger-bg);

  /* 阴影系统 */
  --app-shadow-sm: var(--enterprise-shadow-sm);
  --app-shadow: var(--enterprise-shadow);
  --app-shadow-md: var(--enterprise-shadow-md);
  --app-shadow-lg: var(--enterprise-shadow-lg);
}

/* Dark主题颜色覆盖 */
:root[data-theme="dark"] .setting-manager.native-app,
html.dark .setting-manager.native-app {
  /* 背景色系统 */
  --app-bg-primary: var(--enterprise-neutral-900);
  --app-bg-secondary: var(--enterprise-neutral-800);
  --app-bg-tertiary: var(--enterprise-neutral-700);
  --app-bg-elevated: var(--enterprise-neutral-800);

  /* 文字色系统 */
  --app-text-primary: var(--enterprise-neutral-50);
  --app-text-secondary: var(--enterprise-neutral-200);
  --app-text-tertiary: var(--enterprise-neutral-400);
  --app-text-placeholder: var(--enterprise-neutral-500);

  /* 边框色系统 */
  --app-border-light: rgba(148, 163, 184, 0.1);
  --app-border: rgba(148, 163, 184, 0.2);
  --app-border-strong: rgba(148, 163, 184, 0.3);

  /* 交互色系统 */
  --app-primary: var(--enterprise-primary-light);
  --app-primary-hover: var(--enterprise-primary-lighter);
  --app-primary-bg: rgba(59, 130, 246, 0.1);
  --app-primary-border: rgba(59, 130, 246, 0.3);

  /* 状态色系统 */
  --app-success: var(--enterprise-success-light);
  --app-success-bg: rgba(16, 185, 129, 0.1);
  --app-warning: var(--enterprise-warning-light);
  --app-warning-bg: rgba(245, 158, 11, 0.1);
  --app-danger: var(--enterprise-danger-light);
  --app-danger-bg: rgba(239, 68, 68, 0.1);

  /* 阴影系统 - 暗色主题使用更深的阴影 */
  --app-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --app-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --app-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --app-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* 原生应用风格样式 */
.setting-manager.native-app {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--app-bg-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  color: var(--app-text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 企业级顶部导航栏 */
.native-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: var(--app-bg-elevated);
  border-bottom: 1px solid var(--app-border);
  box-shadow: var(--app-shadow-sm);
  z-index: 10;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;

  .breadcrumb {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--app-text-primary);
    font-size: 16px;
    font-weight: 500;

    .breadcrumb-icon {
      color: var(--app-primary);
      font-size: 20px;
      padding: 4px;
      border-radius: 6px;
      background: var(--app-primary-bg);
      transition: all 0.2s ease;
    }

    .book-name {
      font-weight: 600;
      color: var(--app-text-primary);
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .separator {
      color: var(--app-text-tertiary);
      font-size: 14px;
      margin: 0 4px;
    }

    .current-page {
      color: var(--app-primary);
      font-weight: 600;
      padding: 4px 8px;
      background: var(--app-primary-bg);
      border-radius: 6px;
      border: 1px solid var(--app-primary-border);
    }
  }

  .native-btn {
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    padding: 10px 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--app-border);
    background: var(--app-bg-secondary);
    color: var(--app-text-primary);

    &.back-btn {
      background: var(--app-primary);
      border-color: var(--app-primary);
      color: white;
      box-shadow: var(--app-shadow-sm);

      &:hover {
        background: var(--app-primary-hover);
        border-color: var(--app-primary-hover);
        transform: translateY(-1px);
        box-shadow: var(--app-shadow-md);
      }

      &:active {
        transform: translateY(0);
        box-shadow: var(--app-shadow-sm);
      }
    }

    .el-icon {
      margin-right: 6px;
      font-size: 16px;
    }
  }
}

/* 主要布局 */
.native-main-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 企业级左侧边栏 */
.native-sidebar {
  width: 340px;
  background: var(--app-bg-secondary);
  border-right: 1px solid var(--app-border);
  display: flex;
  flex-direction: column;
  box-shadow: var(--app-shadow-sm);
  transition: all 0.3s ease;

  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid var(--app-border-light);
    background: var(--app-bg-elevated);

    .header-title {
      display: flex;
      align-items: center;
      gap: 12px;
      font-weight: 600;
      font-size: 18px;
      color: var(--app-text-primary);

      .el-icon {
        color: var(--app-primary);
        font-size: 20px;
        padding: 6px;
        background: var(--app-primary-bg);
        border-radius: 8px;
        border: 1px solid var(--app-primary-border);
      }

      .template-count {
        margin-left: 8px;

        :deep(.el-badge__content) {
          background-color: var(--app-primary);
          border-color: var(--app-primary);
          color: white;
          font-weight: 600;
          font-size: 12px;
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 8px;

      .native-icon-btn {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        border: 1px solid var(--app-border);
        background: var(--app-bg-secondary);
        color: var(--app-text-secondary);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: var(--app-primary-bg);
          border-color: var(--app-primary-border);
          color: var(--app-primary);
          transform: translateY(-1px);
          box-shadow: var(--app-shadow-sm);
        }

        &:active {
          transform: translateY(0);
        }

        &.el-button--primary {
          background: var(--app-primary);
          border-color: var(--app-primary);
          color: white;

          &:hover {
            background: var(--app-primary-hover);
            border-color: var(--app-primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--app-shadow-md);
          }
        }
      }
    }
  }

  .search-section {
    padding: 20px 24px;
    border-bottom: 1px solid var(--app-border-light);
    background: var(--app-bg-elevated);

    .native-search {
      border-radius: 10px;

      :deep(.el-input__wrapper) {
        background-color: var(--app-bg-tertiary);
        border: 1px solid var(--app-border-light);
        border-radius: 10px;
        transition: all 0.3s ease;
        box-shadow: var(--app-shadow-sm);

        &:hover {
          border-color: var(--app-primary-border);
          box-shadow: var(--app-shadow);
        }

        &.is-focus {
          border-color: var(--app-primary);
          box-shadow: 0 0 0 3px var(--app-primary-bg);
        }
      }

      :deep(.el-input__inner) {
        background-color: transparent;
        color: var(--app-text-primary);
        font-size: 14px;
        padding: 12px 16px;

        &::placeholder {
          color: var(--app-text-placeholder);
        }
      }

      :deep(.el-input__prefix) {
        color: var(--app-text-tertiary);
      }
    }
  }

  .template-list-container {
    flex: 1;
    overflow: hidden;
    background: var(--app-bg-secondary);

    .template-list-wrapper {
      height: 100%;
      overflow-y: auto;
      padding: 12px;

      /* 自定义滚动条 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: var(--app-bg-tertiary);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--app-border-strong);
        border-radius: 3px;
        transition: background 0.2s ease;

        &:hover {
          background: var(--app-text-tertiary);
        }
      }
    }
  }
}

/* 企业级模板卡片 */
.template-card {
  position: relative;
  margin-bottom: 12px;
  padding: 16px;
  background: var(--app-bg-elevated);
  border: 1px solid var(--app-border-light);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--app-shadow-sm);
  overflow: hidden;

  /* 添加微妙的渐变背景 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--app-primary), var(--app-primary-hover));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    border-color: var(--app-primary-border);
    box-shadow: var(--app-shadow-md);
    transform: translateY(-2px);

    &::before {
      opacity: 1;
    }
  }

  &.active {
    border-color: var(--app-primary);
    background: var(--app-primary-bg);
    box-shadow: var(--app-shadow-lg);
    transform: translateY(-1px);

    &::before {
      opacity: 1;
    }

    .template-name {
      color: var(--app-primary);
      font-weight: 700;
    }
  }

  .template-drag-handle {
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--app-text-placeholder);
    cursor: grab;
    padding: 6px;
    border-radius: 6px;
    transition: all 0.2s ease;
    opacity: 0;

    &:hover {
      background: var(--app-bg-tertiary);
      color: var(--app-text-secondary);
      opacity: 1;
    }

    &:active {
      cursor: grabbing;
      background: var(--app-primary-bg);
      color: var(--app-primary);
    }
  }

  &:hover .template-drag-handle {
    opacity: 0.7;
  }

  .template-main {
    margin-left: 32px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .template-info {
      flex: 1;

      .template-name {
        font-weight: 600;
        font-size: 16px;
        color: var(--app-text-primary);
        margin-bottom: 6px;
        line-height: 1.4;
        transition: color 0.2s ease;
      }

      .template-desc {
        font-size: 13px;
        color: var(--app-text-secondary);
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }

    .template-stats {
      margin-left: 12px;

      :deep(.el-tag) {
        background-color: var(--app-bg-tertiary);
        border-color: var(--app-border);
        color: var(--app-text-secondary);
        font-size: 12px;
        font-weight: 500;
        border-radius: 6px;
        padding: 2px 8px;
      }
    }
  }

  .template-dimensions {
    margin-left: 32px;
    margin-bottom: 8px;

    .dimension-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .dimension-tag {
        font-size: 11px;
        font-weight: 500;
        border-radius: 6px;
        padding: 3px 8px;
        background: var(--app-bg-tertiary);
        border: 1px solid var(--app-border-light);
        color: var(--app-text-secondary);
        transition: all 0.2s ease;

        &:hover {
          background: var(--app-primary-bg);
          border-color: var(--app-primary-border);
          color: var(--app-primary);
        }
      }

      .more-tag {
        background: var(--app-primary-bg);
        color: var(--app-primary);
        border-color: var(--app-primary-border);
        font-weight: 600;
      }
    }
  }

  .template-actions {
    position: absolute;
    top: 12px;
    right: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;

    .more-btn {
      width: 28px;
      height: 28px;
      border-radius: 8px;
      border: 1px solid var(--app-border);
      background: var(--app-bg-elevated);
      color: var(--app-text-secondary);
      transition: all 0.2s ease;
      box-shadow: var(--app-shadow-sm);

      &:hover {
        background: var(--app-primary-bg);
        border-color: var(--app-primary-border);
        color: var(--app-primary);
        transform: scale(1.05);
      }
    }
  }

  &:hover .template-actions {
    opacity: 1;
  }
}

/* 企业级右侧内容区域 */
.native-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--app-bg-primary);

  .entity-header {
    padding: 24px 28px;
    border-bottom: 1px solid var(--app-border);
    background: var(--app-bg-elevated);
    box-shadow: var(--app-shadow-sm);

    .entity-title-section {
      margin-bottom: 20px;

      .current-template-info {
        .entity-title {
          margin: 0 0 12px 0;
          font-size: 24px;
          font-weight: 700;
          color: var(--app-text-primary);
          letter-spacing: -0.02em;
        }

        .template-meta {
          display: flex;
          gap: 12px;

          :deep(.el-tag) {
            font-size: 13px;
            font-weight: 500;
            border-radius: 8px;
            padding: 6px 12px;
            border: 1px solid var(--app-border);

            &.el-tag--primary {
              background: var(--app-primary-bg);
              color: var(--app-primary);
              border-color: var(--app-primary-border);
            }

            &.el-tag--info {
              background: var(--app-bg-tertiary);
              color: var(--app-text-secondary);
              border-color: var(--app-border);
            }
          }
        }
      }
    }

    .entity-actions {
      display: flex;
      align-items: center;
      gap: 16px;

      .native-search-input {
        width: 280px;

        :deep(.el-input__wrapper) {
          background-color: var(--app-bg-tertiary);
          border: 1px solid var(--app-border-light);
          border-radius: 10px;
          transition: all 0.3s ease;
          box-shadow: var(--app-shadow-sm);

          &:hover {
            border-color: var(--app-primary-border);
            box-shadow: var(--app-shadow);
          }

          &.is-focus {
            border-color: var(--app-primary);
            box-shadow: 0 0 0 3px var(--app-primary-bg);
          }
        }

        :deep(.el-input__inner) {
          background-color: transparent;
          color: var(--app-text-primary);
          font-size: 14px;
          padding: 12px 16px;

          &::placeholder {
            color: var(--app-text-placeholder);
          }
        }

        :deep(.el-input__prefix) {
          color: var(--app-text-tertiary);
        }
      }

      .action-buttons {
        display: flex;
        gap: 10px;

        .native-btn {
          border-radius: 10px;
          font-weight: 500;
          font-size: 14px;
          padding: 12px 18px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          border: 1px solid var(--app-border);
          background: var(--app-bg-secondary);
          color: var(--app-text-primary);
          box-shadow: var(--app-shadow-sm);

          &:hover {
            transform: translateY(-1px);
            box-shadow: var(--app-shadow-md);
          }

          &:active {
            transform: translateY(0);
            box-shadow: var(--app-shadow-sm);
          }

          &.success-btn {
            background: var(--app-success);
            border-color: var(--app-success);
            color: white;

            &:hover {
              background: var(--app-success);
              filter: brightness(1.1);
              box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
            }
          }

          &.primary-btn {
            background: var(--app-primary);
            border-color: var(--app-primary);
            color: white;

            &:hover {
              background: var(--app-primary-hover);
              border-color: var(--app-primary-hover);
              box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
            }
          }

          .el-icon {
            margin-right: 6px;
            font-size: 16px;
          }
        }

        .export-dropdown {
          :deep(.el-button) {
            border-radius: 10px;
            font-weight: 500;
            font-size: 14px;
            padding: 12px 18px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--app-border);
            background: var(--app-bg-secondary);
            color: var(--app-text-primary);
            box-shadow: var(--app-shadow-sm);

            &:hover {
              transform: translateY(-1px);
              box-shadow: var(--app-shadow-md);
              border-color: var(--app-primary-border);
            }
          }
        }
      }
    }
  }

  .entity-grid-container {
    flex: 1;
    overflow: hidden;
    background: var(--app-bg-primary);

    .entity-grid-wrapper {
      height: 100%;
      overflow-y: auto;
      padding: 24px 28px;

      /* 自定义滚动条 */
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: var(--app-bg-tertiary);
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--app-border-strong);
        border-radius: 4px;
        transition: background 0.2s ease;

        &:hover {
          background: var(--app-text-tertiary);
        }
      }
    }
  }
}

/* 企业级实体网格 */
.entity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 20px;
  padding: 4px;

  .entity-card {
    background: var(--app-bg-elevated);
    border: 1px solid var(--app-border-light);
    border-radius: 16px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--app-shadow-sm);
    position: relative;
    overflow: hidden;

    /* 添加微妙的渐变边框效果 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, var(--app-primary), var(--app-primary-hover));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      border-color: var(--app-primary-border);
      box-shadow: var(--app-shadow-lg);
      transform: translateY(-4px);

      &::before {
        opacity: 1;
      }
    }

    .entity-card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;

      .entity-name {
        font-weight: 600;
        color: var(--app-text-primary);
        font-size: 18px;
        line-height: 1.4;
        flex: 1;
        margin-right: 12px;
        transition: color 0.2s ease;
      }

      .entity-actions {
        opacity: 0;
        transition: opacity 0.3s ease;

        .more-btn {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          border: 1px solid var(--app-border);
          background: var(--app-bg-secondary);
          color: var(--app-text-secondary);
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background: var(--app-primary-bg);
            border-color: var(--app-primary-border);
            color: var(--app-primary);
            transform: scale(1.05);
          }
        }
      }
    }

    &:hover .entity-actions {
      opacity: 1;
    }

    .entity-card-body {
      .entity-description {
        color: var(--app-text-secondary);
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 16px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        min-height: 40px;
      }

      .entity-dimensions {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;

        .dimension-item {
          padding: 12px;
          background: var(--app-bg-tertiary);
          border: 1px solid var(--app-border-light);
          border-radius: 10px;
          transition: all 0.2s ease;

          &:hover {
            background: var(--app-bg-secondary);
            border-color: var(--app-border);
            transform: translateY(-1px);
            box-shadow: var(--app-shadow-sm);
          }

          &.unset {
            opacity: 0.6;
            background: var(--app-bg-tertiary);
            border-color: var(--app-border-light);

            .dimension-value {
              color: var(--app-text-placeholder);
              font-style: italic;
            }
          }

          &.more-dimensions {
            background: var(--app-primary-bg);
            border-color: var(--app-primary-border);
            color: var(--app-primary);
            text-align: center;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            &:hover {
              background: var(--app-primary);
              color: white;
              transform: translateY(-2px);
              box-shadow: var(--app-shadow-md);
            }

            .dimension-label,
            .dimension-value {
              color: inherit;
              font-weight: 600;
            }
          }

          .dimension-label {
            font-size: 11px;
            color: var(--app-text-tertiary);
            margin-bottom: 6px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .dimension-value {
            font-size: 14px;
            color: var(--app-text-primary);
            line-height: 1.4;
            font-weight: 500;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            word-break: break-word;
          }
        }
      }
    }
  }
}

/* 企业级分页容器 */
.pagination-container {
  padding: 20px 28px;
  border-top: 1px solid var(--app-border);
  background: var(--app-bg-elevated);
  display: flex;
  justify-content: center;
  box-shadow: var(--app-shadow-sm);

  :deep(.el-pagination) {
    .el-pager li {
      background: var(--app-bg-secondary);
      border: 1px solid var(--app-border-light);
      color: var(--app-text-secondary);
      border-radius: 8px;
      margin: 0 4px;
      transition: all 0.2s ease;

      &:hover {
        background: var(--app-primary-bg);
        border-color: var(--app-primary-border);
        color: var(--app-primary);
        transform: translateY(-1px);
      }

      &.is-active {
        background: var(--app-primary);
        border-color: var(--app-primary);
        color: white;
        box-shadow: var(--app-shadow-sm);
      }
    }

    .btn-prev,
    .btn-next {
      background: var(--app-bg-secondary);
      border: 1px solid var(--app-border-light);
      color: var(--app-text-secondary);
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        background: var(--app-primary-bg);
        border-color: var(--app-primary-border);
        color: var(--app-primary);
      }

      &:disabled {
        background: var(--app-bg-tertiary);
        border-color: var(--app-border-light);
        color: var(--app-text-placeholder);
      }
    }

    .el-pagination__total,
    .el-pagination__jump {
      color: var(--app-text-secondary);
    }

    .el-select .el-input__wrapper {
      background: var(--app-bg-secondary);
      border-color: var(--app-border-light);
      border-radius: 8px;
    }
  }
}

/* 企业级空状态 */
.empty-state,
.empty-entities,
.no-template-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 40px;
  text-align: center;
  background: var(--app-bg-elevated);
  border-radius: 16px;
  border: 1px solid var(--app-border-light);
  margin: 20px;
  box-shadow: var(--app-shadow-sm);

  .empty-icon,
  .placeholder-icon {
    font-size: 64px;
    margin-bottom: 24px;
    opacity: 0.6;
    color: var(--app-text-placeholder);
    filter: grayscale(0.3);
  }

  .empty-text,
  .placeholder-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--app-text-primary);
    margin-bottom: 12px;
    letter-spacing: -0.01em;
  }

  .empty-desc,
  .placeholder-desc {
    font-size: 16px;
    color: var(--app-text-secondary);
    margin-bottom: 32px;
    line-height: 1.6;
    max-width: 400px;
  }

  .empty-action {
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: 500;
    background: var(--app-primary);
    border-color: var(--app-primary);
    color: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--app-shadow-sm);

    &:hover {
      background: var(--app-primary-hover);
      border-color: var(--app-primary-hover);
      transform: translateY(-2px);
      box-shadow: var(--app-shadow-md);
    }

    &:active {
      transform: translateY(0);
      box-shadow: var(--app-shadow-sm);
    }

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
}

/* 企业级拖拽相关样式 */
.ghost-template {
  opacity: 0.6;
  background: var(--app-primary-bg);
  border-color: var(--app-primary);
  box-shadow: var(--app-shadow-lg);
  transform: rotate(2deg) scale(1.02);
}

.template-draggable {
  .sortable-chosen {
    background: var(--app-primary-bg);
    border-color: var(--app-primary);
    box-shadow: var(--app-shadow-md);
    transform: scale(1.02);
  }

  .sortable-ghost {
    opacity: 0.5;
    background: var(--app-primary-bg);
    border: 2px dashed var(--app-primary);
    transform: scale(0.98);
  }
}

:deep(.native-template-drawer) {
  .el-drawer__header {
    display: none;
  }

  .el-drawer__body {
    padding: 0;
    background: var(--el-bg-color);
  }
}

/* 模板对话框特定样式 */
.native-template-drawer {
  /* 固定的基本信息区域 */
  .template-basic-info-fixed {
    flex-shrink: 0;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding: 24px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);

      .title-icon {
        color: var(--el-color-primary);
        font-size: 20px;
      }
    }
  }

  /* 固定的维度配置标题区域 */
  .template-dimensions-header-fixed {
    flex-shrink: 0;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding: 20px 24px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);

        .title-icon {
          color: var(--el-color-primary);
          font-size: 20px;
        }

        .dimension-badge {
          background: var(--el-color-primary-light-9);
          color: var(--el-color-primary);
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          min-width: 20px;
          text-align: center;
        }
      }

      .section-actions {
        .add-dimension-btn {
          border-radius: 6px;
          font-weight: 500;
        }
      }
    }
  }

  /* 可滚动的维度列表区域 */
  .template-dimensions-scrollable {
    flex: 1;
    overflow-y: auto;
    background: var(--el-bg-color-page);
    min-height: 0;

    .dimensions-container {
      padding: 24px;
    }
  }
  .template-icon {
    width: 40px;
    height: 40px;
    background: var(--el-color-primary-light-9);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-color-primary);
    font-size: 20px;
  }


}



.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;

  .form-item {
    display: flex;
    flex-direction: column;

    &.full-width {
      grid-column: 1 / -1;
    }

    .form-label {
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);

      .required {
        color: var(--el-color-danger);
        margin-left: 2px;
      }
    }

    .form-input,
    .form-textarea {
      border-radius: 8px;
      transition: all 0.2s ease;

      &:focus-within {
        box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
      }
    }
  }
}

:deep(.native-entity-detail-drawer),
:deep(.native-entity-create-drawer) {
  .el-drawer__header {
    display: none;
  }

  .el-drawer__body {
    padding: 0;
    background: var(--el-bg-color);
  }
}

/* 添加维度输入样式 */
.add-dimension-input {
  margin-bottom: 20px;

  .dimension-input {
    border-radius: 8px;
  }

  .input-hint {
    margin-top: 8px;
    font-size: 12px;
    color: var(--el-text-color-placeholder);
    text-align: center;
  }
}

/* 维度列表容器 */
.dimensions-container {
  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--el-text-color-placeholder);

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .empty-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 8px;
      color: var(--el-text-color-regular);
    }

    .empty-desc {
      font-size: 16px;
    }
  }

  .dimensions-form-native {
    .draggable-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;
    }

    .dimension-form-item-native {
      background: var(--el-fill-color-extra-light);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 8px;
      padding: 16px;
      transition: all 0.2s ease;
      cursor: move;

      &:hover {
        border-color: var(--el-color-primary-light-7);
        box-shadow: var(--el-box-shadow-light);
      }

      .dimension-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .dimension-label {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 500;
          color: var(--el-text-color-primary);

          .drag-handle {
            color: var(--el-text-color-placeholder);
            cursor: move;
            font-size: 14px;

            &:hover {
              color: var(--el-color-primary);
            }
          }
        }

        .remove-btn {
          opacity: 0;
          transition: opacity 0.2s ease;

          &:hover {
            color: var(--el-color-danger);
          }
        }
      }

      &:hover .remove-btn {
        opacity: 1;
      }
    }

    .dimension-ghost {
      opacity: 0.4;
      transform: rotate(5deg);
    }
  }
}

.dimensions-list {
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;

    .empty-icon {
      font-size: 64px;
      margin-bottom: 16px;
      opacity: 0.6;
    }

    .empty-title {
      font-size: 20px;
      font-weight: 500;
      margin-bottom: 8px;
      color: var(--el-text-color-regular);
    }

    .empty-desc {
      font-size: 16px;
      color: var(--el-text-color-placeholder);
      max-width: 300px;
    }
  }

  .dimensions-grid {
    .draggable-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .dimension-item {
      display: flex;
      align-items: center;
      padding: 16px;
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--el-color-primary-light-5);
        box-shadow: var(--el-box-shadow);
        transform: translateY(-1px);
      }

      .dimension-drag {
        margin-right: 12px;

        .drag-handle {
          color: var(--el-text-color-placeholder);
          cursor: grab;
          font-size: 16px;

          &:hover {
            color: var(--el-text-color-regular);
          }

          &:active {
            cursor: grabbing;
          }
        }
      }

      .dimension-content {
        flex: 1;

        .dimension-name {
          font-size: 16px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
        }

        .dimension-type {
          font-size: 14px;
          color: var(--el-text-color-placeholder);
          background: var(--el-fill-color-light);
          padding: 2px 6px;
          border-radius: 4px;
          display: inline-block;
        }
      }

      .dimension-actions {
        .remove-btn {
          color: var(--el-color-danger);
          opacity: 0.7;

          &:hover {
            opacity: 1;
            background: var(--el-color-danger-light-9);
          }
        }
      }
    }

    .dimension-ghost {
      opacity: 0.5;
      background: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary);
      transform: rotate(2deg);
    }
  }
}



/* 全屏详情页面样式 */
.fullscreen-content {
  height: calc(100vh - 60px); /* 减去固定header的高度 */
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  position: relative;

  .detail-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-lighter);
    box-shadow: var(--el-box-shadow-light);

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .entity-title {
        margin: 0;
        font-size: 22px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .entity-meta {
        display: flex;
        gap: 8px;
      }
    }

    .toolbar-right {
      display: flex;
      gap: 12px;

      .native-btn {
        border-radius: 6px;
        font-weight: 500;

        &.primary-btn {
          background: var(--el-color-primary);
          border-color: var(--el-color-primary);
          color: white;

          &:hover {
            background: var(--el-color-primary-light-3);
            border-color: var(--el-color-primary-light-3);
          }
        }
      }
    }
  }

  .detail-main-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;

    .detail-basic-info {
      margin-bottom: 32px;

      .section-title {
        margin: 0 0 16px 0;
        font-size: 20px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .form-group-horizontal {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 24px;

        .form-field {
          .field-label {
            display: block;
            margin-bottom: 8px;
            font-size: 16px;
            font-weight: 500;
            color: var(--el-text-color-primary);

            .required {
              color: var(--el-color-danger);
            }
          }

          .native-input,
          .native-textarea {
            border-radius: 8px;
            border: 1px solid var(--el-border-color);
            transition: all 0.2s ease;

            &:focus {
              border-color: var(--el-color-primary);
              box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
            }
          }
        }
      }
    }

    .detail-dimensions {
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .section-title {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .dimension-count {
          font-size: 16px;
          color: var(--el-text-color-regular);
          background: var(--el-fill-color-light);
          padding: 6px 12px;
          border-radius: 6px;
        }
      }

      .dimensions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 20px;

        .dimension-card {
          background: var(--el-bg-color);
          border: 1px solid var(--el-border-color-lighter);
          border-radius: 12px;
          padding: 20px;
          transition: all 0.2s ease;

          &:hover {
            border-color: var(--el-color-primary-light-5);
            box-shadow: var(--el-box-shadow);
          }

          .dimension-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .dimension-label {
              font-size: 18px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              cursor: pointer;

              &:hover {
                color: var(--el-color-primary);
              }
            }

            .expand-btn {
              color: var(--el-text-color-placeholder);

              &:hover {
                color: var(--el-color-primary);
                background: var(--el-color-primary-light-9);
              }
            }
          }

          .dimension-textarea {
            border-radius: 8px;
            border: 1px solid var(--el-border-color);
            transition: all 0.2s ease;

            &:focus {
              border-color: var(--el-color-primary);
              box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
            }
          }
        }
      }
    }
  }
}

/* 维度拖拽样式 */
.ghost-dimension {
  opacity: 0.5;
  background: var(--el-color-primary-light-8);
  border-color: var(--el-color-primary);
}

.dimensions-draggable {
  .sortable-chosen {
    background: var(--el-color-primary-light-9);
    border-color: var(--el-color-primary);
  }

  .sortable-ghost {
    opacity: 0.4;
  }
}

/* 企业级响应式设计 */
@media (max-width: 1400px) {
  .native-sidebar {
    width: 320px;
  }

  .entity-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 1200px) {
  .native-main-layout {
    .native-sidebar {
      width: 300px;
    }
  }

  .entity-header {
    .entity-actions {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .native-search-input {
        width: 100%;
      }

      .action-buttons {
        justify-content: center;
        flex-wrap: wrap;
      }
    }
  }

  .entity-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
  }

  .detail-main-content {
    .detail-basic-info {
      .form-group-horizontal {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }

    .detail-dimensions {
      .dimensions-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}

@media (max-width: 768px) {
  .native-main-layout {
    flex-direction: column;

    .native-sidebar {
      width: 100%;
      height: 280px;
      border-right: none;
      border-bottom: 1px solid var(--app-border);
      box-shadow: var(--app-shadow-sm);

      .template-list-container {
        .template-list-wrapper {
          max-height: 200px;
        }
      }
    }
  }

  .native-header {
    padding: 12px 16px;

    .breadcrumb {
      font-size: 14px;

      .book-name {
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .native-btn {
      padding: 8px 12px;
      font-size: 13px;
    }
  }

  .entity-header {
    padding: 16px 20px;

    .entity-title-section {
      .current-template-info {
        .entity-title {
          font-size: 20px;
        }
      }
    }

    .entity-actions {
      .action-buttons {
        .native-btn {
          padding: 10px 14px;
          font-size: 13px;
        }
      }
    }
  }

  .entity-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .entity-grid-container {
    .entity-grid-wrapper {
      padding: 16px 20px;
    }
  }

  .pagination-container {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .native-header {
    .breadcrumb {
      .book-name {
        max-width: 80px;
      }
    }
  }

  .entity-card {
    .entity-card-body {
      .entity-dimensions {
        grid-template-columns: 1fr;
        gap: 8px;
      }
    }
  }

  .template-card {
    .template-dimensions {
      .dimension-tags {
        .dimension-tag {
          font-size: 10px;
          padding: 2px 6px;
        }
      }
    }
  }
}

/* 企业级全局滚动条美化 - 主题适配 */
:deep(*) {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background: transparent;
  }

  &::-webkit-scrollbar-track {
    background: var(--app-bg-tertiary);
    border-radius: 4px;
    margin: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--app-border-strong);
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: var(--app-text-tertiary);
    }

    &:active {
      background: var(--app-text-secondary);
    }
  }

  &::-webkit-scrollbar-corner {
    background: var(--app-bg-tertiary);
  }
}

/* 企业级动画和过渡效果 */
.setting-manager.native-app {
  /* 页面加载动画 */
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片进入动画 */
.template-card,
.entity-card {
  animation: slideInCard 0.4s cubic-bezier(0.4, 0, 0.2, 1) backwards;
}

@keyframes slideInCard {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 按钮点击反馈动画 */
.native-btn,
.native-icon-btn,
.more-btn {
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
  }

  &:active::after {
    width: 100px;
    height: 100px;
  }
}

/* 搜索框聚焦动画 */
.native-search,
.native-search-input {
  :deep(.el-input__wrapper) {
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent);
      transition: left 0.6s ease;
    }

    &.is-focus::before {
      left: 100%;
    }
  }
}

/* 加载状态动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* 主题切换过渡 */
* {
  transition: background-color 0.3s ease,
              color 0.3s ease,
              border-color 0.3s ease,
              box-shadow 0.3s ease;
}
/* ===== 现代化设定管理界面样式 - 主题适配版 ===== */

/* 主容器样式 */
.setting-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
  overflow: hidden;
}

/* 现代化顶部导航栏 */
.modern-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);
  z-index: 10;
  position: relative;
  box-shadow: 0 2px 8px var(--el-box-shadow-light);

  .header-left {
    display: flex;
    align-items: center;
  }

  .breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    color: var(--el-text-color-regular);

    .breadcrumb-icon {
      color: var(--el-color-primary);
      font-size: 18px;
    }

    .book-name {
      font-weight: 600;
      color: var(--el-text-color-primary);
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .separator {
      color: var(--el-text-color-placeholder);
      font-size: 12px;
    }

    .current-page {
      color: var(--el-color-primary);
      font-weight: 500;
    }
  }

  .header-actions {
    .action-btn {
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px var(--el-box-shadow);
        border-color: var(--el-color-primary);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

/* 主布局 */
.main-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
  background: var(--el-bg-color-page);
}

/* 左侧模板面板 */
.template-sidebar {
  width: 320px;
  background: var(--el-bg-color);
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--el-border-color-lighter);
  box-shadow: 2px 0 8px var(--el-box-shadow-light);

  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 16px 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-bg-color);

    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);

      .el-icon {
        color: var(--el-color-primary);
        font-size: 20px;
      }

      .template-count {
        margin-left: 4px;

        :deep(.el-badge__content) {
          background-color: var(--el-color-primary);
          border-color: var(--el-color-primary);
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 8px;

      .icon-btn {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid var(--el-border-color-light);
        background: var(--el-bg-color);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px var(--el-box-shadow);
          border-color: var(--el-color-primary);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  .search-section {
    padding: 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-bg-color);

    .el-input {
      border-radius: 8px;

      :deep(.el-input__wrapper) {
        background-color: var(--el-fill-color-light);
        border: 1px solid var(--el-border-color-light);
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--el-color-primary-light-7);
        }

        &.is-focus {
          border-color: var(--el-color-primary);
          box-shadow: 0 0 0 2px var(--el-color-primary-light-9);
        }
      }

      :deep(.el-input__inner) {
        background-color: transparent;
        color: var(--el-text-color-primary);
        font-size: 16px;

        &::placeholder {
          color: var(--el-text-color-placeholder);
        }
      }
    }
  }

  .template-list-container {
    flex: 1;
    overflow: hidden;
    background: var(--el-bg-color);
  }
}
/* 模板列表样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  background: var(--el-bg-color);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
    filter: grayscale(0.3);
  }

  .empty-text {
    font-size: 18px;
    color: var(--el-text-color-regular);
    margin-bottom: 16px;
    font-weight: 500;
  }

  .empty-action {
    border-radius: 8px;
    background: var(--el-color-primary);
    border-color: var(--el-color-primary);
    transition: all 0.3s ease;

    &:hover {
      background: var(--el-color-primary-dark-2);
      border-color: var(--el-color-primary-dark-2);
      transform: translateY(-1px);
    }
  }
}

.template-list {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.template-card {
  padding: 16px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  background: var(--el-bg-color);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 2px 4px var(--el-box-shadow-light);

  &:hover {
    border-color: var(--el-color-primary-light-5);
    box-shadow: 0 8px 24px var(--el-box-shadow);
    transform: translateY(-2px);
  }

  &.active {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    box-shadow: 0 8px 24px var(--el-color-primary-light-8);

    .template-name {
      color: var(--el-color-primary);
      font-weight: 600;
    }
  }

  .template-main {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .template-info {
      flex: 1;

      .template-name {
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
        line-height: 1.4;
      }

      .template-desc {
        font-size: 14px;
        color: var(--el-text-color-regular);
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }

    .template-stats {
      margin-left: 12px;

      :deep(.el-tag) {
        background-color: var(--el-fill-color-light);
        border-color: var(--el-border-color-light);
        color: var(--el-text-color-regular);
      }
    }
  }

  .template-dimensions {
    .dimension-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .dimension-tag {
        font-size: 12px;
        height: 22px;
        line-height: 20px;
        border-radius: 4px;
        background-color: var(--el-fill-color-light);
        border-color: var(--el-border-color-light);
        color: var(--el-text-color-regular);
      }

      .more-tag {
        background: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
        border: 1px solid var(--el-color-primary-light-5);
        font-weight: 600;
      }
    }
  }

  .template-actions {
    position: absolute;
    top: 12px;
    right: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;

    .more-btn {
      width: 24px;
      height: 24px;
      border-radius: 6px;
      color: var(--el-text-color-regular);
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color-light);

      &:hover {
        background: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
        border-color: var(--el-color-primary);
      }
    }
  }

  &:hover .template-actions {
    opacity: 1;
  }
}
/* 右侧实体管理区域 */
.entity-main-area {
  flex: 1;
  background: var(--el-bg-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: -2px 0 8px var(--el-box-shadow-light);
}

/* 未选择模板时的占位内容 */
.no-template-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-bg-color-page);

  .placeholder-content {
    text-align: center;
    padding: 40px;

    .placeholder-icon {
      font-size: 64px;
      margin-bottom: 24px;
      opacity: 0.6;
      color: var(--el-text-color-placeholder);
      filter: grayscale(0.3);
    }

    .placeholder-title {
      font-size: 22px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 12px;
    }

    .placeholder-desc {
      font-size: 16px;
      color: var(--el-text-color-regular);
      line-height: 1.6;
      max-width: 400px;
      margin: 0 auto;
    }
  }
}

/* 设定页面的实体内容区域 */
.setting-manager .entity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.entity-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color);

  .entity-title-section {
    .current-template-info {
      .entity-title {
        font-size: 22px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 8px 0;
      }

      .template-meta {
        display: flex;
        gap: 8px;
      }
    }
  }

  .entity-actions {
    display: flex;
    align-items: center;
    gap: 16px;

    .search-input {
      width: 240px;

      :deep(.el-input__wrapper) {
        background-color: var(--el-fill-color-light);
        border: 1px solid var(--el-border-color-light);
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--el-color-primary-light-7);
        }

        &.is-focus {
          border-color: var(--el-color-primary);
          box-shadow: 0 0 0 2px var(--el-color-primary-light-9);
        }
      }

      :deep(.el-input__inner) {
        background-color: transparent;
        color: var(--el-text-color-primary);
        font-size: 16px;

        &::placeholder {
          color: var(--el-text-color-placeholder);
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;

      .el-button {
        border-radius: 8px;
        font-weight: 500;
        font-size: 16px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid var(--el-border-color-light);
        background: var(--el-bg-color);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px var(--el-box-shadow);
          border-color: var(--el-color-primary);
        }

        &:active {
          transform: translateY(0);
        }
      }

      .create-btn {
        background: var(--el-color-primary);
        border-color: var(--el-color-primary);
        color: var(--el-color-white);

        &:hover {
          background: var(--el-color-primary-dark-2);
          border-color: var(--el-color-primary-dark-2);
          box-shadow: 0 4px 12px var(--el-color-primary-light-8);
        }
      }
    }
  }
}
/* 实体网格容器 */
.entity-grid-container {
  flex: 1;
  padding: 24px;
  overflow: auto;
  background: var(--el-bg-color-page);

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-fill-color-lighter);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-fill-color-dark);
    border-radius: 3px;

    &:hover {
      background: var(--el-text-color-placeholder);
    }
  }
}

.empty-entities {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  background: var(--el-bg-color);
  border-radius: 12px;
  margin: 20px;
  padding: 40px;
  border: 1px solid var(--el-border-color-lighter);

  .empty-icon {
    font-size: 64px;
    margin-bottom: 24px;
    opacity: 0.6;
    color: var(--el-text-color-placeholder);
    filter: grayscale(0.3);
  }

  .empty-text {
    font-size: 20px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }

  .empty-desc {
    font-size: 16px;
    color: var(--el-text-color-regular);
    margin-bottom: 24px;
    line-height: 1.6;
    max-width: 400px;
  }

  .empty-action {
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 500;
    background: var(--el-color-primary);
    border-color: var(--el-color-primary);
    transition: all 0.3s ease;

    &:hover {
      background: var(--el-color-primary-dark-2);
      border-color: var(--el-color-primary-dark-2);
      transform: translateY(-1px);
    }
  }
}

/* 实体网格 */
.entity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  padding: 8px 0;
  min-height: 100%;
}

.entity-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px var(--el-box-shadow-light);

  &:hover {
    border-color: var(--el-color-primary-light-5);
    box-shadow: 0 8px 24px var(--el-box-shadow);
    transform: translateY(-2px);
  }

  .entity-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .entity-name {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      line-height: 1.4;
      flex: 1;
      margin-right: 12px;
    }

    .entity-actions {
      opacity: 0;
      transition: opacity 0.3s ease;

      .more-btn {
        width: 28px;
        height: 28px;
        border-radius: 6px;
        color: var(--el-text-color-regular);
        background: var(--el-bg-color);
        border: 1px solid var(--el-border-color-light);

        &:hover {
          background: var(--el-color-primary-light-9);
          color: var(--el-color-primary);
          border-color: var(--el-color-primary);
        }
      }
    }
  }

  &:hover .entity-actions {
    opacity: 1;
  }

  .entity-card-body {
    .entity-description {
      font-size: 15px;
      color: var(--el-text-color-regular);
      line-height: 1.5;
      margin-bottom: 16px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .entity-dimensions {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;

      .dimension-item {
        background: var(--el-fill-color-light);
        border-radius: 8px;
        padding: 8px 12px;
        border: 1px solid var(--el-border-color-lighter);
        transition: all 0.3s ease;

        &.unset {
          opacity: 0.6;
          background: var(--el-fill-color-lighter);
          border-color: var(--el-border-color-extra-light);
        }

        &.more-dimensions {
          background: var(--el-color-primary-light-9);
          border-color: var(--el-color-primary-light-5);
          color: var(--el-color-primary);
          text-align: center;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &:hover {
            background: var(--el-color-primary-light-8);
            border-color: var(--el-color-primary-light-3);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px var(--el-color-primary-light-8);
          }
        }

        .dimension-label {
          font-size: 12px;
          color: var(--el-text-color-placeholder);
          margin-bottom: 4px;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .dimension-value {
          font-size: 14px;
          color: var(--el-text-color-primary);
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          word-break: break-word;
        }
      }
    }
  }
}
/* 分页容器 */
.pagination-container {
  padding: 16px 24px;
  border-top: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color);
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .template-sidebar {
    width: 280px;
  }

  .entity-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .main-layout {
    flex-direction: column;
  }

  .template-sidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--el-border-color-lighter);

    .template-list-container {
      :deep(.el-scrollbar__wrap) {
        max-height: 120px;
      }
    }

    .template-list {
      flex-direction: row;
      overflow-x: auto;
      padding: 8px 16px;

      .template-card {
        min-width: 200px;
        margin-right: 8px;
      }
    }
  }

  .entity-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    padding: 16px;

    .entity-actions {
      flex-direction: column;
      gap: 12px;

      .search-input {
        width: 100%;
      }

      .action-buttons {
        justify-content: center;
        flex-wrap: wrap;
      }
    }
  }

  .entity-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .entity-grid-container {
    padding: 16px;
  }

  .modern-header {
    padding: 12px 16px;

    .breadcrumb {
      font-size: 13px;

      .book-name {
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

/* 维度编辑器对话框样式 - 避免与header冲突 */
.native-editor-dialog {
  :deep(.el-overlay) {
    background: var(--el-overlay-color-lighter);
    backdrop-filter: blur(8px);
  }

  :deep(.el-dialog) {
    margin: 0 !important;
    border-radius: 0;
    position: fixed !important;
    top: 60px !important;
    left: 0 !important;
    width: 100vw !important;
    height: calc(100vh - 60px) !important;
    max-width: 100vw !important;
    max-height: calc(100vh - 60px) !important;
  }

  :deep(.el-dialog__body) {
    padding: 0;
    height: 100% !important;
    overflow: hidden;
  }

  .dimension-editor-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--el-bg-color);

    .editor-header {
      flex-shrink: 0;
      padding: 20px 24px;
      background: var(--el-bg-color);
      border-bottom: 1px solid var(--el-border-color-lighter);
      display: flex;
      justify-content: space-between;
      align-items: center;

      .dimension-info {
        h3 {
          margin: 0 0 8px 0;
          font-size: 22px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .editor-stats {
          display: flex;
          gap: 16px;
          font-size: 16px;
          color: var(--el-text-color-regular);

          .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
      }

      .editor-actions {
        display: flex;
        gap: 12px;

        .action-btn {
          border-radius: 6px;
          font-weight: 500;

          &.primary-btn {
            background: var(--el-color-primary);
            border-color: var(--el-color-primary);
            color: white;

            &:hover {
              background: var(--el-color-primary-light-3);
              border-color: var(--el-color-primary-light-3);
            }
          }

          &.danger-btn {
            background: var(--el-color-danger);
            border-color: var(--el-color-danger);
            color: white;

            &:hover {
              background: var(--el-color-danger-light-3);
              border-color: var(--el-color-danger-light-3);
            }
          }

          &.cancel-btn {
            background: var(--el-fill-color-light);
            border-color: var(--el-border-color);
            color: var(--el-text-color-primary);

            &:hover {
              background: var(--el-fill-color);
              border-color: var(--el-border-color-hover);
            }
          }

          .btn-text {
            margin-left: 4px;
          }
        }
      }
    }

    .editor-content {
      flex: 1;
      padding: 24px;
      overflow: hidden;

      .large-editor {
        height: 100%;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 16px;
        line-height: 1.6;

        :deep(.el-textarea__inner) {
          height: 100% !important;
          resize: none !important;
          border: 1px solid var(--el-border-color);
          border-radius: 8px;
          padding: 16px;
          font-family: inherit;
          font-size: inherit;
          line-height: inherit;

          &:focus {
            border-color: var(--el-color-primary);
            box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
          }
        }
      }
    }
  }
}

/* 维度编辑器抽屉样式 */
:deep(.native-dimension-editor-drawer) {
  .el-drawer__header {
    display: none;
  }

  .el-drawer__body {
    padding: 0;
    background: var(--el-bg-color);
  }
}

.native-dimension-editor-drawer {
  .dimension-icon {
    width: 40px;
    height: 40px;
    background: var(--el-color-primary-light-9);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-color-primary);
    font-size: 20px;
  }

  .entity-meta {
    .stat-item {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }

  .danger-button {
    color: var(--el-color-danger);
    border-color: var(--el-color-danger);

    &:hover {
      background: var(--el-color-danger);
      color: white;
    }
  }

  /* 编辑器内容区域 */
  .dimension-editor-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--el-bg-color-page);
    min-height: 0;
    overflow: hidden;

    .editor-wrapper {
      flex: 1;
      padding: 24px;
      display: flex;
      flex-direction: column;

      .dimension-textarea {
        flex: 1;
        height: 100%;
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;

        :deep(.el-textarea__inner) {
          height: 100% !important;
          min-height: 100% !important;
          resize: none !important;
          border: 1px solid var(--el-border-color-lighter);
          border-radius: 12px;
          padding: 20px;
          font-family: inherit;
          font-size: 20px;
          line-height: 1.7;
          background: var(--el-bg-color);
          color: var(--el-text-color-primary);
          transition: all 0.2s ease;

          &:focus {
            border-color: var(--el-color-primary);
            box-shadow: 0 0 0 3px rgba(var(--el-color-primary-rgb), 0.1);
            outline: none;
          }

          &::placeholder {
            color: var(--el-text-color-placeholder);
            font-size: 14px;
            line-height: 1.6;
          }
        }
      }
    }
  }
}

/* 企业级全局字体和组件优化 */
:deep(.el-button) {
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

:deep(.el-input__inner) {
  font-size: 14px;
  transition: all 0.3s ease;
}

:deep(.el-textarea__inner) {
  font-size: 14px;
  line-height: 1.6;
  transition: all 0.3s ease;
}

:deep(.el-select .el-input__inner) {
  font-size: 14px;
}

:deep(.el-dropdown-menu) {
  border-radius: 12px;
  box-shadow: var(--app-shadow-lg);
  border: 1px solid var(--app-border);
  background: var(--app-bg-elevated);
  backdrop-filter: blur(8px);
}

:deep(.el-dropdown-menu__item) {
  font-size: 14px;
  color: var(--app-text-primary);
  transition: all 0.2s ease;
  border-radius: 6px;
  margin: 4px 8px;

  &:hover {
    background: var(--app-primary-bg);
    color: var(--app-primary);
    transform: translateX(2px);
  }
}

:deep(.el-menu-item) {
  font-size: 14px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-dialog) {
  border-radius: 16px;
  box-shadow: var(--app-shadow-xl);
  backdrop-filter: blur(8px);
}

:deep(.el-dialog__title) {
  font-size: 20px;
  font-weight: 600;
  color: var(--app-text-primary);
}

:deep(.el-drawer) {
  background: var(--app-bg-primary);
  box-shadow: var(--app-shadow-xl);
}

:deep(.el-drawer__title) {
  font-size: 20px;
  font-weight: 600;
  color: var(--app-text-primary);
}

:deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 500;
  color: var(--app-text-primary);
}

:deep(.el-table) {
  font-size: 14px;
  background: var(--app-bg-elevated);
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-pagination) {
  font-size: 14px;

  .el-pagination__total {
    color: var(--app-text-secondary);
    font-weight: 500;
  }
}

:deep(.el-tag) {
  font-size: 12px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }
}

:deep(.el-tooltip__popper) {
  font-size: 13px;
  background: var(--app-bg-elevated);
  color: var(--app-text-primary);
  border: 1px solid var(--app-border);
  border-radius: 8px;
  box-shadow: var(--app-shadow-lg);
  backdrop-filter: blur(8px);
}

/* 企业级微交互效果 */
.template-card,
.entity-card {
  &:hover {
    .template-name,
    .entity-name {
      color: var(--app-primary);
    }
  }
}

/* 焦点可见性增强 */
:focus-visible {
  outline: 2px solid var(--app-primary);
  outline-offset: 2px;
  border-radius: 4px;
}

/* 选择状态优化 */
::selection {
  background: var(--app-primary-bg);
  color: var(--app-primary);
}

/* 企业级加载状态 */
.el-loading-mask {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
}

.el-loading-spinner {
  .circular {
    stroke: var(--app-primary);
  }
}
</style>
