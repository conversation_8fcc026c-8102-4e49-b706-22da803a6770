<template>
  <div class="chat2-container">
    <div class="chat-layout">
      <!-- 侧边栏区域 -->
      <div
        :style="{ width: sidebarCollapsed ? '60px' : '280px' }"
        class="chat-sidebar"
        :class="{ 'sidebar-collapsed': sidebarCollapsed }"
      >
        <!-- 侧边栏头部 -->
        <div class="sidebar-header">
          <el-button
            v-if="!sidebarCollapsed"
            type="primary"
            class="new-chat-btn"
            @click="handleNewChat"
          >
            <el-icon><Plus /></el-icon>
            <span>新建聊天</span>
          </el-button>
          <el-button link class="collapse-btn" @click="toggleSidebar">
            <el-icon>
              <Expand v-if="sidebarCollapsed" />
              <Fold v-else />
            </el-icon>
          </el-button>
        </div>

        <!-- 聊天列表 -->
        <div class="chat-list-container">
          <div
            v-for="chat in chatList"
            :key="chat.id"
            class="chat-item"
            :class="{ active: chat.id === currentChatId }"
            @click="selectChat(chat.id)"
          >
            <el-icon v-if="sidebarCollapsed" class="chat-icon">
              <ChatDotRound />
            </el-icon>
            <span v-if="!sidebarCollapsed" class="chat-title">{{
              chat.title
            }}</span>
            <el-button
              v-if="!sidebarCollapsed"
              link
              size="small"
              class="delete-btn"
              @click.stop="deleteChat(chat.id)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>

        <!-- 侧边栏底部 -->
        <div class="sidebar-footer">
          <el-button
            v-if="!sidebarCollapsed"
            link
            class="clear-all-btn"
            @click="clearAllChats"
          >
            <el-icon><Delete /></el-icon>
            <span>清空所有聊天</span>
          </el-button>
        </div>
      </div>

      <!-- 主内容区域使用 MateChat 布局 -->
      <McLayout class="main-content">
        <!-- 头部区域 -->
        <McLayoutHeader class="chat-header">
          <McHeader :title="currentChat?.title || 'PVV Chat'">
            <template #operationArea>
              <div class="header-controls">
                <!-- 模型选择 -->
                <div class="control-group model-selector">
                  <label class="control-label">
                    <el-icon class="label-icon"><Cpu /></el-icon>
                    模型
                  </label>
                  <el-select
                    v-model="selectedModel"
                    placeholder="选择模型"
                    size="small"
                    class="custom-select"
                    popper-class="custom-select-dropdown"
                    @change="onModelChange"
                  >
                    <el-option
                      v-for="model in modelOptions"
                      :key="model.value"
                      :label="model.label"
                      :value="model.value"
                      class="custom-option"
                    >
                      <div class="option-content">
                        <span class="option-label">{{ model.label }}</span>
                        <span class="option-desc">{{
                          model.description || "AI 模型"
                        }}</span>
                      </div>
                    </el-option>
                  </el-select>
                </div>

                <!-- 角色选择 -->
                <div class="control-group role-selector">
                  <label class="control-label">
                    <el-icon class="label-icon"><User /></el-icon>
                    角色
                  </label>
                  <el-select
                    v-model="selectedRoles"
                    multiple
                    placeholder="选择角色"
                    size="small"
                    class="custom-select"
                    popper-class="custom-select-dropdown"
                    collapse-tags
                    collapse-tags-tooltip
                    :max-collapse-tags="1"
                    clearable
                    @change="onRoleChange"
                    @clear="onRoleClear"
                  >
                    <el-option
                      v-for="role in roleOptions"
                      :key="role.value"
                      :label="role.label"
                      :value="role.value"
                      class="custom-option"
                    >
                      <div class="option-content">
                        <span class="option-label">{{ role.label }}</span>
                        <span class="option-desc">{{
                          role.description || "角色设定"
                        }}</span>
                      </div>
                    </el-option>
                  </el-select>
                </div>

                <!-- 记忆模式切换 -->
                <div class="control-group memory-toggle">
                  <el-button
                    :type="memoryMode ? 'success' : ''"
                    size="small"
                    class="memory-btn"
                    @click="toggleMemoryMode"
                  >
                    <el-icon class="memory-icon">
                      <Connection v-if="memoryMode" />
                      <Lightning v-else />
                    </el-icon>
                    <span class="memory-text">{{
                      memoryMode ? "记忆模式" : "单次对话"
                    }}</span>
                  </el-button>
                </div>

                <!-- 导入导出功能 -->
                <div class="control-group import-export-group">
                  <el-dropdown
                    trigger="click"
                    @command="handleImportExportCommand"
                  >
                    <el-button size="small" class="import-export-btn">
                      <el-icon><Operation /></el-icon>
                      <span>更多</span>
                      <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item
                          command="export"
                          :disabled="
                            !currentChatId || currentMessages.length === 0
                          "
                        >
                          <el-icon><Download /></el-icon>
                          导出对话
                        </el-dropdown-item>
                        <el-dropdown-item command="import">
                          <el-icon><Upload /></el-icon>
                          导入对话
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </template>
          </McHeader>
        </McLayoutHeader>

        <!-- 消息显示区域 -->
        <McLayoutContent
          class="messages-container"
          :class="{ sending: isSending }"
          ref="messagesContainer"
          @scroll="handleScroll"
        >
          <template v-if="currentMessages.length === 0">
            <!-- 欢迎界面 -->
            <div class="welcome-container">
              <McIntroduction
                :title="'PVV Chat'"
                :subTitle="'Hi，欢迎使用智能聊天助手'"
                :description="welcomeDescription"
              />
            </div>
          </template>
          <template v-else>
            <!-- 消息气泡 - 使用重构后的 MarkdownBubble 组件 -->
            <MarkdownBubble
              v-for="(message, index) in currentMessages"
              :key="index"
              :content="message.content"
              :messageType="message.role"
              :isError="message.isError || false"
              :loading="message.loading || false"
              :timestamp="message.timestamp"
              :senderName="
                message.role === 'user' ? '' : currentModelProviderName
              "
              :reasoning="message.reasoning || ''"
              :reasoningTime="message.reasoningTime || 0"
              :hasReasoning="!!message.reasoning"
              :showReasoning="true"
              :useDefaultIcons="true"
              :disabled="isSending"
              :theme="isDarkMode ? 'dark' : 'light'"
              :variant="'default'"
              :showAvatar="true"
              :showHeader="true"
              :showActions="true"
              :loadingText="'正在思考...'"
              :typing="shouldEnableTyping(message, index)"
              :typingOptions="typingOptions"
              @copy="handleMessageCopy"
              @regenerate="regenerateMessage(message, index)"
              @resend="resendMessage(message, index)"
              @typingEnd="handleTypingEnd"
            />
          </template>

          <!-- AI 思考中指示器 -->
          <div v-if="isAiTyping" class="typing-indicator">
            <MarkdownBubble
              content=""
              messageType="assistant"
              :loading="true"
              :senderName="currentModelProviderName"
              reasoning=""
              :reasoningTime="0"
              :hasReasoning="false"
              :showReasoning="false"
              :useDefaultIcons="true"
              :disabled="true"
              :theme="isDarkMode ? 'dark' : 'light'"
              :showAvatar="true"
              :showHeader="false"
              :showActions="false"
              :loadingText="'正在思考...'"
            />
          </div>
        </McLayoutContent>

        <!-- 输入区域 -->
        <McLayoutSender class="input-area" :class="{ sending: isSending }">
          <McInput
            :value="userInput"
            :maxLength="2000"
            :loading="isSending"
            showCount
            @change="(value) => (userInput = value)"
            @submit="handleSendMessage"
            @cancel="handleStopGeneration"
          >
            <template #extra>
              <div class="input-extra">
                <div class="input-foot-left">
                  <span
                    v-for="(item, index) in inputFootIcons"
                    :key="index"
                    class="input-foot-item"
                  >
                    <i :class="item.icon"></i>
                    {{ item.text }}
                  </span>
                  <span class="input-foot-dividing-line"></span>
                  <span class="input-foot-maxlength"
                    >{{ userInput.length }}/2000</span
                  >
                </div>
                <div class="input-foot-right">
                  <span class="input-hint"
                    >AI 可能会出错，请核实重要信息。</span
                  >
                </div>
              </div>
            </template>
          </McInput>
        </McLayoutSender>
      </McLayout>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, watch, onUnmounted, provide } from "vue";
import { useConfigStore } from "@/stores/config";
import { useAIRolesStore } from "@/stores/aiRoles";
import { useAIProvidersStore } from "@/stores/aiProviders";
import {
  Plus,
  Expand,
  Fold,
  ChatDotRound,
  Delete,
  Cpu,
  Lightning,
  User,
  Connection,
  Operation,
  Download,
  Upload,
  ArrowDown,
} from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import MarkdownBubble from "@/components/MarkdownBubble.vue";
import {
  McLayout,
  McLayoutHeader,
  McLayoutContent,
  McLayoutSender,
  McHeader,
  McIntroduction,
  McInput,
} from "@matechat/core";
import { nanoid } from "nanoid";
import { copyToClipboard } from "@/utils/apiUtils";

// 导入 stores
const configStore = useConfigStore();
const aiRolesStore = useAIRolesStore();
const aiProvidersStore = useAIProvidersStore();

// 提供配置存储给子组件
provide('configStore', configStore);

// 响应式数据
const sidebarCollapsed = ref(false);
const currentChatId = ref("");
const selectedModel = ref("");
const selectedRoles = ref([]);
const memoryMode = ref(true);
const userInput = ref("");
const isSending = ref(false);
const isAiTyping = ref(false);
const messagesContainer = ref(null);
const chatList = ref([]);
const sendingChats = ref(new Set()); // Set of chat IDs currently sending
const newlyGeneratedMessageId = ref(null); // 跟踪正在生成的新消息ID

// 打字机效果相关状态
const typingEnabled = ref(false); // 关闭打字机效果
const typingOptions = ref({
  step: [1, 2],
  interval: 50,
});

// 主题相关
const isDarkMode = computed(() => configStore.theme === "dark");

// 可用模型从 AI 提供商 store 获取
const availableModels = computed(() => {
  const options = aiProvidersStore.modelOptions;
  return options.map((option) => ({
    id: option.uniqueId, // 使用唯一标识符作为ID
    name: option.label, // 使用 label 作为显示名称
    providerId: option.providerId,
    providerName: option.providerName,
    uniqueId: option.uniqueId, // 添加uniqueId字段
    config: option.config, // 添加config字段
  }));
});

// 模型选项用于下拉选择
const modelOptions = computed(() => {
  return availableModels.value.map((model) => ({
    value: model.id, // 这里的model.id现在是uniqueId
    label: model.name,
    description: model.providerName
      ? `提供商: ${model.providerName}`
      : undefined,
    provider: model.providerName,
  }));
});

// 获取当前选中模型的显示名称（供应商 + 模型名）
const currentModelProviderName = computed(() => {
  if (!selectedModel.value) return "AI助手";

  const model = availableModels.value.find((m) => m.id === selectedModel.value);
  if (model) {
    // 使用模型的完整显示名称（已经包含供应商和模型名）
    return model.name;
  }

  // 如果没有找到模型，尝试从模型ID中提取并格式化
  const modelId = selectedModel.value;
  if (modelId.includes("/")) {
    const [provider, modelName] = modelId.split("/");

    // 格式化供应商名称
    const formattedProvider =
      provider.charAt(0).toUpperCase() + provider.slice(1);

    // 格式化模型名称，移除常见的后缀和前缀
    let formattedModelName = modelName
      .replace(/-free$/, "") // 移除 -free 后缀
      .replace(/^[^-]+-/, "") // 移除供应商前缀（如 deepseek-）
      .replace(/-/g, " ") // 将连字符替换为空格
      .replace(/\b\w/g, (l) => l.toUpperCase()); // 每个单词首字母大写

    return `${formattedProvider} ${formattedModelName}`;
  }

  return "AI助手";
});

// 可用角色从 AI 角色 store 获取
const availableRoles = computed(() => {
  return aiRolesStore.roles.filter((role) => role.isEnabled !== false);
});

// 角色选项用于下拉选择
const roleOptions = computed(() => {
  return availableRoles.value.map((role) => ({
    value: role.id,
    label: role.name || role.id,
    description: role.description,
  }));
});
// 获取当前聊天
const currentChat = computed(() => {
  return chatList.value.find((c) => c.id === currentChatId.value);
});

// 获取当前聊天的消息
const currentMessages = computed(() => {
  return currentChat.value?.messages || [];
});

// 是否正在发送消息
const isCurrentChatSending = computed(() => {
  return sendingChats.value.has(currentChatId.value);
});

const welcomeDescription = [
  "PVV Chat 可以帮助您进行智能对话、编程辅助、文档编写等。",
  "✨ 支持流式内容渲染，实时显示AI回复！",
  "💡 您可以进行多轮对话，AI会记住上下文内容。",
  "作为AI助手，我会尽力提供准确的信息，但请您核实重要内容。",
];

const inputFootIcons = [
  { icon: "icon-at", text: "智能体" },
  { icon: "icon-standard", text: "词库" },
  { icon: "icon-add", text: "附件" },
];

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};

const handleNewChat = async () => {
  if (!selectedModel.value) {
    ElMessage.warning("请先选择一个模型");
    return;
  }

  const id = nanoid();
  const chat = {
    id,
    title: `新建聊天 ${chatList.value.length + 1}`,
    name: `新建聊天 ${chatList.value.length + 1}`, // Keep for compatibility
    messages: [], // We'll add system messages if roles are selected
    model_id: selectedModel.value,
    roles: selectedRoles.value || [], // 保存当前选中的角色
    last_updated: Date.now() / 1000,
  };

  try {
    await saveChat(chat);
    chatList.value.unshift(chat);
    currentChatId.value = id;
  } catch (error) {
    console.error("Failed to create chat:", error);
    ElMessage.error("创建聊天失败");
  }
};

const selectChat = (chatId) => {
  if (currentChatId.value === chatId) return;
  currentChatId.value = chatId;
  // Find the current chat
  const chat = chatList.value.find((c) => c.id === chatId);

  if (chat) {
    // Update the selected model if the chat has a model_id
    if (chat.model_id) {
      selectedModel.value = chat.model_id;
    }

    // Update the selected roles if the chat has roles
    if (chat.roles && Array.isArray(chat.roles)) {
      selectedRoles.value = chat.roles;
    } else {
      selectedRoles.value = [];
    }
  }
};

const deleteChat = async (chatId) => {
  try {
    // 获取聊天标题用于确认对话框
    const chat = chatList.value.find((c) => c.id === chatId);
    const chatTitle = chat?.title || chat?.name || "未命名聊天";

    // 二次确认
    await ElMessageBox.confirm(
      `确定要删除聊天"${chatTitle}"吗？此操作不可撤销。`,
      "删除聊天",
      {
        confirmButtonText: "确定删除",
        cancelButtonText: "取消",
        type: "warning",
        confirmButtonClass: "el-button--danger",
        center: true,
      }
    );

    // Call backend API to delete
    const response = await window.pywebview.api.model_controller.delete_chat(
      chatId
    );
    const result =
      typeof response === "string" ? JSON.parse(response) : response;

    if (result.status !== "success") {
      throw new Error(result.message || "Delete failed");
    }

    const index = chatList.value.findIndex((chat) => chat.id === chatId);
    if (index > -1) {
      chatList.value.splice(index, 1);
      if (currentChatId.value === chatId) {
        currentChatId.value = chatList.value[0]?.id || "";
      }
    }

    ElMessage.success("聊天删除成功");
  } catch (error) {
    // 如果是用户取消操作，不显示错误信息
    if (error === "cancel") {
      return;
    }
    console.error("Failed to delete chat:", error);
    ElMessage.error("删除聊天失败");
  }
};

const clearAllChats = async () => {
  try {
    // 获取聊天数量用于确认对话框
    const chatCount = chatList.value.length;

    // 二次确认
    await ElMessageBox.confirm(
      `确定要清空所有聊天吗？这将删除 ${chatCount} 个聊天记录，此操作不可撤销。`,
      "清空所有聊天",
      {
        confirmButtonText: "确定清空",
        cancelButtonText: "取消",
        type: "warning",
        confirmButtonClass: "el-button--danger",
        center: true,
        dangerouslyUseHTMLString: false,
      }
    );

    const response =
      await window.pywebview.api.model_controller.clear_all_chats();
    const result =
      typeof response === "string" ? JSON.parse(response) : response;

    if (!result || result.status !== "success") {
      throw new Error(result?.message || "Failed to clear chats");
    }

    chatList.value = [];
    currentChatId.value = "";
    ElMessage.success("所有聊天已清空");

    // Create a new chat
    await handleNewChat();
  } catch (error) {
    // 如果是用户取消操作，不显示错误信息
    if (error === "cancel") {
      return;
    }
    console.error("Failed to clear chats:", error);
    ElMessage.error("清空聊天失败");
  }
};

const toggleMemoryMode = () => {
  memoryMode.value = !memoryMode.value;
  // Save user preference to local storage
  localStorage.setItem("chat_memory_enabled", memoryMode.value);
  console.log(
    memoryMode.value ? "Memory mode enabled" : "Memory mode disabled"
  );
};

// 模型变更处理
const onModelChange = async (value, option) => {
  console.log("Model changed:", value, option);
  // 更新当前聊天的模型
  const chat = chatList.value.find((c) => c.id === currentChatId.value);
  if (chat) {
    chat.model_id = value;
    await saveChat(chat);
  }
};

// 角色变更处理
const onRoleChange = async (values, option) => {
  console.log("Roles changed:", values, option);
  // 更新选中的角色
  selectedRoles.value = values;

  // 更新当前聊天的角色设置
  const chat = chatList.value.find((c) => c.id === currentChatId.value);
  if (chat) {
    chat.roles = values;
    await saveChat(chat);

    // 显示角色变更提示
    if (values.length > 0) {
      const roleNames = values
        .map((roleId) => {
          const role = availableRoles.value.find((r) => r.id === roleId);
          return role ? role.name || roleId : roleId;
        })
        .join(", ");
      ElMessage.success(`已启用角色: ${roleNames}`);
      console.log(`已选择角色: ${roleNames}`);
    } else {
      ElMessage.info("已关闭所有角色设定，后续对话将不使用角色提示");
      console.log("已清除所有角色设定");
    }
  }
};

// 角色清除处理
const onRoleClear = async () => {
  console.log("Roles cleared");
  selectedRoles.value = [];

  // 更新当前聊天的角色设置
  const chat = chatList.value.find((c) => c.id === currentChatId.value);
  if (chat) {
    chat.roles = [];
    await saveChat(chat);
    ElMessage.info("已清除所有角色设定，后续对话将不使用角色提示");
  }
};

// Save chat to backend
const saveChat = async (chat) => {
  try {
    // Ensure chat object has title field
    if (!chat.title && chat.name) {
      chat.title = chat.name; // For backwards compatibility
    } else if (!chat.title) {
      chat.title = `New Chat ${Date.now()}`;
    }

    // Call backend API to save chat
    await window.pywebview.api.model_controller.save_chat(chat.id, chat);
    return true;
  } catch (error) {
    console.error("Failed to save chat:", error);
    throw error;
  }
};

// Initialize chat list
const initChatList = async () => {
  try {
    const response =
      await window.pywebview.api.model_controller.get_all_chats();
    const result =
      typeof response === "string" ? JSON.parse(response) : response;

    if (result && result.status === "success" && Array.isArray(result.data)) {
      chatList.value = result.data;

      if (chatList.value.length > 0) {
        const latestChat = chatList.value[0];
        currentChatId.value = latestChat.id;
        if (latestChat.model_id) {
          selectedModel.value = latestChat.model_id;
        }
      } else {
        await handleNewChat();
      }
    } else {
      throw new Error(result?.message || "Failed to load chats");
    }
  } catch (error) {
    console.error("Failed to load chats:", error);
    ElMessage.error("加载聊天历史失败");
    await handleNewChat();
  }
};

const handleSendMessage = async () => {
  if (!userInput.value.trim() || isSending.value) return;

  // Get current chat
  const chat = chatList.value.find((c) => c.id === currentChatId.value);
  if (!chat) return;

  // Save current model to chat record
  chat.model_id = selectedModel.value;

  // Get system prompts if roles are selected
  const systemPrompts = [];

  if (selectedRoles.value && selectedRoles.value.length > 0) {
    for (const roleId of selectedRoles.value) {
      const role = aiRolesStore.roles.find((r) => r.id === roleId);
      if (role && role.prompt) {
        systemPrompts.push(role.prompt);
      }
    }
  }

  // Create user message
  const userMessage = {
    role: "user",
    content: userInput.value.trim(),
    timestamp: Date.now(),
  };

  // Build messages array to send
  let messagesToSend = [];

  // Determine which messages to send based on memory mode
  if (memoryMode.value) {
    // Memory mode: send system messages and all history
    const systemMessages =
      systemPrompts.length > 0
        ? [
            {
              role: "system",
              content: systemPrompts.join("\n\n"),
            },
          ]
        : [];

    // Create a copy of history messages (excluding system messages)
    const historyMessages = chat.messages
      .filter((msg) => msg.role !== "system")
      .map((msg) => ({ role: msg.role, content: msg.content }));

    messagesToSend = [...systemMessages, ...historyMessages, userMessage];
  } else {
    // No memory mode: only send system messages and current user message
    if (systemPrompts.length > 0) {
      messagesToSend.push({
        role: "system",
        content: systemPrompts.join("\n\n"),
      });
    }
    messagesToSend.push(userMessage);
  }

  // Add user message to chat history
  chat.messages.push(userMessage);

  // Update chat time
  chat.last_updated = Date.now() / 1000;

  // Clear input
  userInput.value = "";

  // Mark current chat as sending
  sendingChats.value.add(currentChatId.value);
  isSending.value = true;
  isAiTyping.value = true;

  // 发送消息时强制滚动到底部
  shouldAutoScroll.value = true;
  // 使用setTimeout确保DOM完全更新后再滚动
  nextTick(() => {
    setTimeout(() => {
      scrollToBottom(true);
      // 备选方案：尝试滚动到最后一个消息元素
      const messageElements = document.querySelectorAll(".message-bubble");
      if (messageElements.length > 0) {
        const lastMessage = messageElements[messageElements.length - 1];
        lastMessage.scrollIntoView({ behavior: "smooth", block: "end" });
      }
    }, 10);
  });

  try {
    // Save chat
    await saveChat(chat);

    // Auto-update title if first message
    await updateTitleFromMessage(currentChatId.value, userMessage.content);

    // 获取选中模型的配置
    const modelConfig = getModelConfig(selectedModel.value);

    // 合并配置：模型配置优先，只有stream强制为true
    const finalConfig = {
      stream: true, // 强制启用流式输出
      ...modelConfig, // 模型配置（包括temperature, top_p, max_tokens等）
    };

    // Call backend API

    window.pywebview.api.model_controller.chat(
      currentChatId.value,
      selectedModel.value,
      messagesToSend,
      finalConfig
    );
  } catch (error) {
    sendingChats.value.delete(currentChatId.value);
    isSending.value = false;
    isAiTyping.value = false;
    console.error("Failed to send message:", error);
    ElMessage.error("发送消息失败: " + (error.message || "Unknown error"));

    // 确保即使发生错误，用户消息也被保存
    try {
      await saveChat(chat);
      console.log("用户消息已保存，尽管发送过程中出现错误");
    } catch (saveError) {
      console.error("保存用户消息失败:", saveError);
      ElMessage.error("保存消息失败，消息可能会丢失");
    }
  }
};

const handleStopGeneration = async () => {
  if (!currentChatId.value || !sendingChats.value.has(currentChatId.value))
    return;

  try {
    const response = await window.pywebview.api.model_controller.stop_chat(
      currentChatId.value
    );
    const result =
      typeof response === "string" ? JSON.parse(response) : response;

    if (result.status === "success") {
      sendingChats.value.delete(currentChatId.value);
      isSending.value = false;
      isAiTyping.value = false;
      newlyGeneratedMessageId.value = null; // 清除新生成消息的标识
      console.log("Generation stopped");
      ElMessage.info("已停止生成");

      // 确保在停止生成时保存聊天记录
      const chat = chatList.value.find((c) => c.id === currentChatId.value);
      if (chat) {
        saveChat(chat).catch((error) => {
          console.error(
            "Failed to save chat after stopping generation:",
            error
          );
        });
      }
    } else {
      throw new Error(result.message || "Stop failed");
    }
  } catch (error) {
    console.error("Failed to stop chat:", error);
    ElMessage.error("停止生成失败: " + (error.message || "Unknown error"));
  } finally {
    sendingChats.value.delete(currentChatId.value);
    isSending.value = false;
    isAiTyping.value = false;
    newlyGeneratedMessageId.value = null; // 确保清除新生成消息的标识
  }
};

// 获取模型配置的方法
const getModelConfig = (modelUniqueId) => {
  try {
    // 从aiProvidersStore获取模型配置
    const model = availableModels.value.find((m) => m.id === modelUniqueId);
    if (model && model.config) {
      return model.config;
    }

    // 如果没有找到配置，返回默认配置
    console.log("未找到模型配置，使用默认配置");
    return {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true,
    };
  } catch (error) {
    console.error("获取模型配置失败:", error);
    return {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true,
    };
  }
};

// Auto-update title from first message
const updateTitleFromMessage = async (chatId, content) => {
  try {
    // Get chat data
    const response = await window.pywebview.api.model_controller.get_chat(
      chatId
    );
    const result =
      typeof response === "string" ? JSON.parse(response) : response;

    if (result.status === "success") {
      const chatData = result.data;

      // If first user message and title is default format
      const userMessages = chatData.messages.filter((m) => m.role === "user");
      if (userMessages.length <= 1 && /^新建聊天/.test(chatData.title)) {
        // Extract first 20 chars as title
        const newTitle =
          content.slice(0, 20) + (content.length > 20 ? "..." : "");
        await updateChatTitle(chatId, newTitle);
      }
    }
  } catch (error) {
    console.error("Failed to auto-update title:", error);
  }
};

// Update chat title
const updateChatTitle = async (chatId, newTitle) => {
  try {
    // Get latest chat data
    const response = await window.pywebview.api.model_controller.get_chat(
      chatId
    );
    const result =
      typeof response === "string" ? JSON.parse(response) : response;

    if (result.status === "success") {
      const chatData = result.data;
      // Update title
      chatData.title = newTitle;
      // For compatibility also update name field
      chatData.name = newTitle;

      // Save updated chat data
      const saveResponse =
        await window.pywebview.api.model_controller.save_chat(chatId, chatData);
      const saveResult =
        typeof saveResponse === "string"
          ? JSON.parse(saveResponse)
          : saveResponse;

      if (saveResult.status === "success") {
        // Update title in local chat list
        const chat = chatList.value.find((c) => c.id === chatId);
        if (chat) {
          chat.title = newTitle;
          chat.name = newTitle;
        }
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error("Failed to update chat title:", error);
    return false;
  }
};

// 消息操作方法
const handleMessageCopy = async (content, error = null) => {
  // 如果有错误，直接显示错误信息
  if (error) {
    ElMessage.error("复制失败: " + error.message);
    return;
  }

  // 使用公共的复制函数
  await copyToClipboard(content);
};

const regenerateMessage = async (message, index) => {
  if (isSending.value) return;

  if (currentChat.value && index > 0) {
    // 获取上一条用户消息
    const previousMessage = currentChat.value.messages[index - 1];
    if (previousMessage && previousMessage.role === "user") {
      // 删除当前AI回复
      currentChat.value.messages.splice(index, 1);

      // 重新发送消息
      ElMessage.info("正在重新生成回复...");

      // 模拟用户输入并发送
      userInput.value = previousMessage.content;
      await handleSendMessage();
    }
  }
};

const resendMessage = async (message, index) => {
  if (isSending.value) return;

  if (message && message.role === "user") {
    // 重新发送用户消息
    ElMessage.info("正在重新发送消息...");
    userInput.value = message.content;
    await handleSendMessage();
  }
};

// 智能滚动系统
const shouldAutoScroll = ref(true); // 是否应该自动滚动

const isUserNearBottom = () => {
  if (!messagesContainer.value) return false;

  let container = messagesContainer.value;

  // 如果是MateChat组件，可能需要找到内部的滚动容器
  if (container.$el) {
    container = container.$el;
  }

  // 尝试找到真正的滚动容器
  const scrollableElement =
    container.querySelector(".messages-container") ||
    container.querySelector('[class*="scroll"]') ||
    container;

  const threshold = 100; // 距离底部100px内认为是在底部附近
  return (
    scrollableElement.scrollHeight -
      scrollableElement.scrollTop -
      scrollableElement.clientHeight <=
    threshold
  );
};

// 滚动到底部
const scrollToBottom = (force = false) => {
  nextTick(() => {
    if (messagesContainer.value && (force || shouldAutoScroll.value)) {
      let container = messagesContainer.value;

      // 如果是MateChat组件，可能需要找到内部的滚动容器
      if (container.$el) {
        container = container.$el;
      }

      // 尝试找到真正的滚动容器
      const scrollableElement =
        container.querySelector(".messages-container") ||
        container.querySelector('[class*="scroll"]') ||
        container;

      const scrollHeight = scrollableElement.scrollHeight;
      const clientHeight = scrollableElement.clientHeight;

      // 滚动到底部
      scrollableElement.scrollTop = scrollHeight;

      // 确保滚动生效
      setTimeout(() => {
        if (scrollableElement.scrollTop < scrollHeight - clientHeight - 10) {
          scrollableElement.scrollTop = scrollHeight;
          console.log("Re-scrolled to bottom");
        }
      }, 50);
    }
  });
};

// 智能滚动 - 只有在用户接近底部时才自动滚动
const smartScroll = () => {
  if (isUserNearBottom()) {
    shouldAutoScroll.value = true;
    scrollToBottom();
  } else {
    shouldAutoScroll.value = false;
  }
};

// 监听用户滚动行为
const handleScroll = () => {
  if (!messagesContainer.value) return;

  // 检查用户是否滚动到底部附近
  if (isUserNearBottom()) {
    shouldAutoScroll.value = true;
  } else {
    shouldAutoScroll.value = false;
  }
};

// 打字机效果相关方法
const shouldEnableTyping = (message, index) => {
  // 只有在以下条件下才启用打字机效果：
  // 1. 打字机功能已启用
  // 2. 是助手消息
  // 3. 是最新的消息
  // 4. 消息没有loading状态
  // 5. 当前正在发送消息（AI正在生成内容）
  // 6. 不是在显示"思考中"状态（即已经有实际内容了）
  return (
    typingEnabled.value &&
    message.role === "assistant" &&
    index === currentMessages.value.length - 1 &&
    !message.loading &&
    isSending.value && // 关键：只有在AI正在生成时才启用打字机效果
    sendingChats.value.has(currentChatId.value) && // 确保当前聊天正在发送
    !isAiTyping.value
  ); // 当不在"思考中"状态时才启用打字机（即已经有实际内容）
};

const handleTypingEnd = () => {
  console.log("打字机效果完成");
};

// 导入导出功能
const handleImportExportCommand = async (command) => {
  if (command === "export") {
    await handleExportChat();
  } else if (command === "import") {
    await handleImportChat();
  }
};

const handleExportChat = async () => {
  if (!currentChatId.value) {
    ElMessage.warning("请先选择一个聊天");
    return;
  }

  if (currentMessages.value.length === 0) {
    ElMessage.warning("当前聊天没有消息可导出");
    return;
  }

  try {
    // 第一步：让用户输入文件名
    const now = new Date();
    const timestamp =
      now.getFullYear() +
      "-" +
      String(now.getMonth() + 1).padStart(2, "0") +
      "-" +
      String(now.getDate()).padStart(2, "0") +
      "_" +
      String(now.getHours()).padStart(2, "0") +
      "-" +
      String(now.getMinutes()).padStart(2, "0") +
      "-" +
      String(now.getSeconds()).padStart(2, "0");
    const defaultFileName = `聊天导出_${timestamp}`;

    const { value: fileName } = await ElMessageBox.prompt(
      "请输入导出文件名",
      "导出对话",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputValue: defaultFileName,
        inputPlaceholder: "请输入文件名（不需要扩展名）",
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return "文件名不能为空";
          }
          // 检查文件名是否包含非法字符（Windows文件名限制）
          if (/[<>:"/\\|?*]/.test(value)) {
            return '文件名不能包含以下字符: < > : " / \\ | ? *';
          }
          // 检查文件名长度
          if (value.trim().length > 200) {
            return "文件名过长，请控制在200个字符以内";
          }
          return true;
        },
      }
    );

    if (!fileName || !fileName.trim()) {
      return;
    }

    // 第二步：选择保存目录
    const dirResponse = await window.pywebview.api.select_directory();
    const dirResult =
      typeof dirResponse === "string" ? JSON.parse(dirResponse) : dirResponse;

    if (dirResult.status !== "success") {
      // 用户取消选择，不显示错误
      return;
    }

    const selectedDir = dirResult.data;

    // 构建完整文件路径
    const cleanFileName = fileName.trim().replace(/[<>:"/\\|?*]/g, "_");
    const fullFileName = cleanFileName.endsWith(".txt")
      ? cleanFileName
      : `${cleanFileName}.txt`;

    // 使用后端API来构建正确的文件路径，避免路径分隔符问题
    let filePath;
    if (selectedDir.endsWith("/") || selectedDir.endsWith("\\")) {
      filePath = selectedDir + fullFileName;
    } else {
      // 让后端处理路径拼接，使用统一的分隔符
      filePath = selectedDir + "/" + fullFileName;
    }

    // 第三步：执行导出
    console.log("准备导出到路径:", filePath);
    const exportResponse = await window.pywebview.api.export_chat_to_file(
      currentChatId.value,
      filePath
    );
    const exportResult =
      typeof exportResponse === "string"
        ? JSON.parse(exportResponse)
        : exportResponse;

    console.log("导出结果:", exportResult);
    if (exportResult.status === "success") {
      ElMessage({
        message: "回话导出成功。",
        type: "success",
        duration: 1000,
      });
    } else {
      console.error("导出失败详情:", exportResult);
      ElMessage.error(`导出失败: ${exportResult.message}`);
    }
  } catch (error) {
    if (error === "cancel") {
      // 用户取消操作，不显示错误
      return;
    }
    console.error("导出对话失败:", error);
    ElMessage.error("导出对话失败");
  }
};

const handleImportChat = async () => {
  try {
    // 第一步：选择要导入的文件
    const fileResponse = await window.pywebview.api.select_file_path();
    const fileResult =
      typeof fileResponse === "string"
        ? JSON.parse(fileResponse)
        : fileResponse;

    if (fileResult.status !== "success") {
      // 用户取消选择，不显示错误
      return;
    }

    // select_file_path 返回的是数组
    const selectedFiles = fileResult.data;
    if (!selectedFiles || selectedFiles.length === 0) {
      ElMessage.warning("未选择文件");
      return;
    }

    const filePath = selectedFiles[0];

    // 第二步：询问导入方式
    const importMode = await ElMessageBox.confirm(
      '请选择导入方式：',
      '导入对话',
      {
        confirmButtonText: '导入到当前对话',
        cancelButtonText: '创建新对话',
        distinguishCancelAndClose: true,
        type: 'question'
      }
    ).then(() => 'current').catch((action) => {
      if (action === 'cancel') return 'new'
      throw action // 用户点击了关闭按钮
    })

    // 第三步：执行导入
    const targetChatId = (importMode === 'current' && currentChatId.value) ? currentChatId.value : null

    const importResponse = await window.pywebview.api.import_chat_from_file(
      filePath,
      targetChatId
    );
    const importResult =
      typeof importResponse === "string"
        ? JSON.parse(importResponse)
        : importResponse;

    if (importResult.status === "success") {
      const data = importResult.data;
      const modeText = data.import_mode === 'append' ? '追加到当前对话' : '创建新对话'
      ElMessage.success(
        `成功${modeText}: ${data.title} (${data.message_count} 条消息)`
      );

      // 重新加载聊天列表
      await initChatList();

      // 如果是新建对话或当前没有选中对话，切换到目标对话
      if (data.import_mode === 'create' || !currentChatId.value) {
        currentChatId.value = data.chat_id;
      } else if (data.import_mode === 'append') {
        // 如果是追加模式，重新加载聊天列表以更新消息
        // currentMessages 计算属性会自动响应 chatList 的变化
        await initChatList();
      }
    } else {
      ElMessage.error(`导入失败: ${importResult.message}`);
    }
  } catch (error) {
    console.error("导入对话失败:", error);
    ElMessage.error("导入对话失败");
  }
};

// 生命周期
onMounted(async () => {
  console.log("Chat2.vue: 开始初始化聊天界面...");

  try {
    // 0. 首先加载AI提供商配置(最重要的配置)
    try {
      // 检查是否已经初始化，避免重复加载
      if (!aiProvidersStore.initialized) {
        console.log("Chat2.vue: 开始加载AI提供商配置...");
        await aiProvidersStore.loadProviders(true); // 强制刷新
        console.log(
          "Chat2.vue: AI提供商配置加载完成:",
          aiProvidersStore.providers.length,
          "个提供商"
        );
      } else {
        console.log("Chat2.vue: AI提供商配置已初始化，跳过加载");
      }
    } catch (providerError) {
      console.error("Chat2.vue: 加载AI提供商配置失败:", providerError);
    }

    // 1. 模型列表现在从 AI 提供商配置中获取，无需单独加载
    console.log("Chat2.vue: 可用模型数量:", availableModels.value.length);
    if (availableModels.value.length === 0) {
      console.warn("Chat2.vue: 没有可用的模型，请检查AI提供商配置");
    } else {
      // 如果没有选中的模型，默认选择第一个可用模型
      if (!selectedModel.value && availableModels.value.length > 0) {
        const firstModel = availableModels.value[0];
        selectedModel.value = firstModel.id;
        console.log("Chat2.vue: 默认选择模型:", selectedModel.value);
      }
    }

    // 2. 加载AI角色
    try {
      if (!aiRolesStore.roles.length) {
        console.log("Chat2.vue: 开始加载AI角色...");
        await aiRolesStore.loadRoles();
        console.log(
          "Chat2.vue: AI角色加载完成:",
          aiRolesStore.roles.length,
          "个角色"
        );
      } else {
        console.log("Chat2.vue: AI角色已加载，跳过加载");
      }
    } catch (roleError) {
      console.error("Chat2.vue: 加载AI角色失败:", roleError);
    }

    // 3. 加载聊天历史
    try {
      console.log("Chat2.vue: 开始加载聊天历史...");
      await initChatList();
      console.log(
        "Chat2.vue: 聊天历史加载完成:",
        chatList.value.length,
        "个聊天"
      );
    } catch (chatError) {
      console.error("Chat2.vue: 加载聊天历史失败:", chatError);
      await handleNewChat();
    }
  } catch (error) {
    console.error("Chat2.vue: 初始化错误:", error);
  }

  // Load memory mode from local storage
  const savedMemoryMode = localStorage.getItem("chat_memory_enabled");
  if (savedMemoryMode !== null) {
    memoryMode.value = savedMemoryMode === "true";
  }

  console.log("Chat2 interface initialized");
});

// 处理接收到的消息块
window.receiveChunk = (chunk) => {
  try {
    const decodedBytes = atob(chunk);
    const decodedChunk = new TextDecoder("utf-8").decode(
      new Uint8Array([...decodedBytes].map((char) => char.charCodeAt(0)))
    );

    const messageData = JSON.parse(decodedChunk);
    const { chat_id, content, reasoning } = messageData;

    // Debug output for reasoning
    if (reasoning) {
      console.log("Received reasoning content:", reasoning);
    }

    // Only process messages for current chat
    if (chat_id === currentChatId.value) {
      const chat = chatList.value.find((c) => c.id === chat_id);
      if (!chat) return;

      if (!chat.messages) {
        chat.messages = [];
      }

      // Check if last message is from AI
      const lastMessage = chat.messages[chat.messages.length - 1];

      if (!lastMessage || lastMessage.role !== "assistant") {
        // Add new AI message
        const newMessage = {
          role: "assistant",
          content: content || "",
          reasoningStartTime: reasoning ? Date.now() : null,
          timestamp: Date.now(),
        };
        if (reasoning) {
          newMessage.reasoning = reasoning;
          newMessage.reasoningCollapsed = false;
          newMessage.reasoningTime = "Thinking...";
        }
        chat.messages.push(newMessage);
      } else {
        // Update last message
        if (content) lastMessage.content += content;
        if (reasoning) {
          // If first reasoning content, record start time
          if (!lastMessage.reasoningStartTime) {
            lastMessage.reasoningStartTime = Date.now();
          }
          if (!lastMessage.reasoning) lastMessage.reasoning = "";
          // Handle escape characters
          const formattedReasoning = reasoning.replace(/\\n/g, "\n");
          lastMessage.reasoning += formattedReasoning;

          if (!("reasoningCollapsed" in lastMessage)) {
            lastMessage.reasoningCollapsed = false;
            lastMessage.reasoningTime = "Thinking...";
          }
        }
      }

      // 智能滚动 - 只有在用户接近底部时才自动滚动
      nextTick(() => {
        smartScroll();
      });

      // 当AI开始发送实际内容或推理内容时，隐藏"正在思考"指示器
      // 因为此时真正的消息气泡已经在显示内容了
      if ((content && content.trim()) || (reasoning && reasoning.trim())) {
        isAiTyping.value = false;
      }
    }
  } catch (error) {
    console.error("Failed to process message chunk:", error);
  }
};

// 处理消息完成
window.onMessageComplete = (chat_id) => {
  if (chat_id === currentChatId.value) {
    sendingChats.value.delete(chat_id);
    isSending.value = false;
    isAiTyping.value = false;
    // 清除新生成消息的标识
    newlyGeneratedMessageId.value = null;

    // Save chat
    const chat = chatList.value.find((c) => c.id === chat_id);
    if (chat && chat.messages.length > 0) {
      const lastMessage = chat.messages[chat.messages.length - 1];
      if (
        lastMessage &&
        lastMessage.reasoning &&
        lastMessage.reasoningStartTime
      ) {
        // Calculate reasoning time (ms)
        const reasoningDuration = Date.now() - lastMessage.reasoningStartTime;
        // Convert to seconds with 1 decimal place
        const seconds = (reasoningDuration / 1000).toFixed(1);
        lastMessage.reasoningTime = `for ${seconds}s`;
        // Clean up temp timestamp
        delete lastMessage.reasoningStartTime;
      }
      saveChat(chat).catch((error) => {
        console.error("Failed to save chat:", error);
      });
    }
  }
};

// 处理聊天错误
window.receiveChatError = (chunk) => {
  try {
    const decodedBytes = atob(chunk);
    const decodedChunk = new TextDecoder("utf-8").decode(
      new Uint8Array([...decodedBytes].map((char) => char.charCodeAt(0)))
    );

    const errorData = JSON.parse(decodedChunk);
    const { chat_id, error_message } = errorData;

    console.log("接收到聊天错误消息:", chat_id, error_message);

    // Always show error message to user
    ElMessage.error({
      message: `AI回复失败: ${error_message}`,
      duration: 5000,
    });

    // Handle errors for current chat
    if (chat_id === currentChatId.value) {
      // Reset sending state
      sendingChats.value.delete(chat_id);
      isSending.value = false;
      isAiTyping.value = false;
      // 清除新生成消息的标识
      newlyGeneratedMessageId.value = null;

      // Find current chat
      const chat = chatList.value.find((c) => c.id === chat_id);
      if (chat) {
        // 检查是否有空的 AI 消息需要转换为错误消息
        if (chat.messages.length > 0) {
          const lastMessage = chat.messages[chat.messages.length - 1];
          if (
            lastMessage &&
            lastMessage.role === "assistant" &&
            !lastMessage.content.trim()
          ) {
            lastMessage.content = `[Error: ${error_message}]`;
            lastMessage.isError = true;
          }
        }

        // 无论是否有 AI 消息错误，都尝试保存聊天记录以确保用户消息不丢失
        saveChat(chat).catch((error) => {
          console.error("Failed to save chat after error:", error);
          ElMessage.error("保存聊天记录失败，消息可能会丢失");
        });
      }
    }
  } catch (error) {
    console.error("Failed to process error message:", error);
  }
};

// 组件卸载时清理全局函数
onUnmounted(() => {
  // 清理全局函数，避免内存泄漏
  if (window.receiveChunk) {
    delete window.receiveChunk;
  }
  if (window.onMessageComplete) {
    delete window.onMessageComplete;
  }
  if (window.receiveChatError) {
    delete window.receiveChatError;
  }
});
</script>

<style lang="scss" scoped>
// <!-- https://matechat.gitcode.com/components/markDownCard/demo.html -->
//  <!-- D:\project\go\pvv\frontend\node_modules\@matechat\core\MarkdownCard\index.js -->

/* 护眼色彩系统 - 只在当前聊天界面的 light 主题下生效 */
.chat2-container.light,
.light .chat2-container {
  /* 重定义 Element Plus 的白色主题变量为更柔和的护眼米色调 */
  --el-bg-color-page: #fbfaf7;        /* 页面背景 - 非常柔和的暖白色 */
  --el-bg-color: #f9f8f5;             /* 主要背景 - 温暖的象牙白 */
  --el-bg-color-overlay: #fefdfb;     /* 覆盖层背景 - 极浅的奶白色 */
  --el-border-color-light: #ebe9e3;   /* 浅边框 - 柔和的浅灰米色 */
  --el-border-color-lighter: #f2f1ed; /* 更浅边框 - 非常浅的米白色 */
  --el-fill-color-light: #f6f5f2;     /* 浅填充色 - 柔和的浅米色 */
  --el-fill-color-lighter: #f9f8f5;   /* 更浅填充色 - 温暖象牙白 */
  --el-fill-color-extra-light: #fcfbf9; /* 极浅填充色 - 极柔和的暖白 */
}
.chat2-container {
  height: calc(100vh - 32px);
  width: calc(100% - 32px);
  overflow: hidden;
  background: var(--el-bg-color-page);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.chat-layout {
  height: 100%;
  display: flex;
  background: var(--el-bg-color);
  overflow: hidden;
  gap: 0;
}

/* 侧边栏样式 */
.chat-sidebar {
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  position: relative;

  &.sidebar-collapsed {
    .sidebar-header {
      justify-content: center;
      padding: 12px 8px;
      min-height: 60px; /* 保持与展开状态相同的高度 */
    }

    .new-chat-btn {
      display: none;
    }

    .chat-title,
    .clear-all-btn span {
      display: none;
    }

    .chat-item {
      justify-content: center;
      padding: 12px 8px;
    }

    .delete-btn {
      display: none;
    }
  }
}

.sidebar-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--el-bg-color);
  min-height: 60px; /* 与右侧头部高度保持一致 */
  box-sizing: border-box;

  .new-chat-btn {
    flex: 1;
    height: 36px; /* 调整按钮高度以适应新的容器高度 */
    border-radius: 8px;
    font-weight: 500;
    background: var(--el-color-primary);
    border-color: var(--el-color-primary);

    &:hover {
      background: var(--el-color-primary-light-3);
      border-color: var(--el-color-primary-light-3);
    }
  }

  .collapse-btn {
    width: 36px; /* 调整按钮宽度以适应新的容器高度 */
    height: 36px; /* 调整按钮高度以适应新的容器高度 */
    border-radius: 8px;
    color: var(--el-text-color-regular);

    &:hover {
      background: var(--el-fill-color-light);
      color: var(--el-color-primary);
    }
  }
}

.chat-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;

  /* 美化滚动条 - 支持主题适配 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-bg-color);
    border-radius: 3px;
    margin: 4px 0;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-lighter);
    border-radius: 3px;
    transition: all 0.3s ease;

    &:hover {
      background: var(--el-border-color-light);
    }

    &:active {
      background: var(--el-color-primary-light-5);
    }
  }

  /* 深色模式下的滚动条样式 */
  .dark & {
    &::-webkit-scrollbar-track {
      background: var(--el-bg-color-page);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color);

      &:hover {
        background: var(--el-border-color-light);
      }

      &:active {
        background: var(--el-color-primary-light-3);
      }
    }
  }

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color-lighter) var(--el-bg-color);

  .dark & {
    scrollbar-color: var(--el-border-color) var(--el-bg-color-page);
  }

  .chat-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 4px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--el-text-color-regular);

    &:hover {
      background: var(--el-fill-color-light);
    }

    &.active {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
      font-weight: 500;
    }

    .chat-icon {
      font-size: 18px;
    }

    .chat-title {
      flex: 1;
      margin-left: 12px;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .delete-btn {
      opacity: 0;
      transition: opacity 0.2s ease;
      color: var(--el-text-color-placeholder);

      &:hover {
        color: var(--el-color-danger);
      }
    }

    &:hover .delete-btn {
      opacity: 1;
    }
  }
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid var(--el-border-color-light);
  background: var(--el-bg-color);

  .clear-all-btn {
    width: 100%;
    height: 36px;
    color: var(--el-text-color-regular);

    &:hover {
      color: var(--el-color-danger);
      background: var(--el-color-danger-light-9);
    }
  }
}

/* 主内容区样式 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--el-bg-color);
}

.chat-header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);

  :deep(.mc-header) {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px; /* 固定高度，与左侧边栏头部保持一致 */
    box-sizing: border-box;

    .mc-header-logo-container {
      flex: 1;
      min-width: 0; /* 允许收缩 */
      margin-right: 16px;

      .mc-header-title {
        color: var(--el-text-color-primary) !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 100% !important;
      }
    }

    .mc-header-operation {
      flex-shrink: 0; /* 防止压缩 */

      .header-controls {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-wrap: nowrap; /* 防止换行 */

        .control-group {
          display: flex;
          align-items: center;
          gap: 6px;
          flex-shrink: 0; /* 防止压缩 */

          &.model-selector,
          &.role-selector {
            min-width: 200px;
            max-width: 240px;
            flex-shrink: 0; /* 防止压缩 */
          }

          &.memory-toggle {
            margin-left: 8px;
            min-width: 90px;
            flex-shrink: 0; /* 防止压缩 */
          }

          &.import-export-group {
            margin-left: 8px;
            min-width: 80px;
            flex-shrink: 0; /* 防止压缩 */
          }

          .control-label {
            display: flex;
            align-items: center;
            gap: 3px;
            font-size: 11px;
            font-weight: 500;
            color: var(--el-text-color-secondary);
            white-space: nowrap;
            min-width: 32px;

            .label-icon {
              font-size: 14px;
              color: var(--el-color-primary);
            }
          }

          .custom-select {
            flex: 1;
            min-width: 80px;

            :deep(.el-input__wrapper) {
              background: var(--el-bg-color);
              border: 1px solid var(--el-border-color-light);
              border-radius: 6px;
              box-shadow: none;
              transition: all 0.2s ease;
              height: 32px;

              &:hover {
                border-color: var(--el-color-primary);
              }

              &.is-focus {
                border-color: var(--el-color-primary);
                box-shadow: 0 0 0 2px var(--el-color-primary-light-9);
              }
            }

            :deep(.el-input__inner) {
              color: var(--el-text-color-primary);
              font-weight: 400;
              font-size: 13px;
              padding: 0 10px;
              white-space: nowrap !important;
              overflow: hidden !important;
              text-overflow: ellipsis !important;
            }

            /* 处理 el-select 的选中项显示 */
            :deep(.el-select__wrapper) {
              .el-select__selection {
                .el-select__selected-item {
                  .el-select__placeholder {
                    white-space: nowrap !important;
                    overflow: hidden !important;
                    text-overflow: ellipsis !important;
                    max-width: 100% !important;
                  }
                }
              }
            }

            :deep(.el-input__suffix) {
              .el-input__suffix-inner {
                .el-select__caret {
                  font-size: 14px;
                }
              }
            }

            :deep(.el-select__tags) {
              .el-tag {
                background: var(--el-color-primary-light-9);
                border-color: var(--el-color-primary-light-7);
                color: var(--el-color-primary);
                border-radius: 3px;
                font-size: 11px;
                font-weight: 400;
                height: 20px;
                line-height: 18px;
                padding: 0 6px;
                margin: 1px 3px 1px 0;
              }
            }
          }

          .memory-btn {
            height: 32px;
            padding: 0 12px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 13px;
            transition: all 0.2s ease;
            border: 1px solid var(--el-border-color-light);
            background: var(--el-bg-color);
            color: var(--el-text-color-primary);
            white-space: nowrap;

            &:hover {
              border-color: var(--el-color-primary);
              color: var(--el-color-primary);
            }

            &.el-button--success {
              background: var(--el-color-success);
              border-color: var(--el-color-success);
              color: white;

              &:hover {
                background: var(--el-color-success-light);
                border-color: var(--el-color-success-light);
              }
            }

            .memory-icon {
              margin-right: 4px;
              font-size: 14px;
            }

            .memory-text {
              font-size: 12px;
            }
          }

          .import-export-btn {
            height: 32px;
            padding: 0 12px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 13px;
            transition: all 0.2s ease;
            border: 1px solid var(--el-border-color-light);
            background: var(--el-bg-color);
            color: var(--el-text-color-primary);
            white-space: nowrap;

            &:hover {
              border-color: var(--el-color-primary);
              color: var(--el-color-primary);
            }

            .el-icon--right {
              margin-left: 4px;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: var(--el-bg-color-page);
  position: relative;

  /* 修复发送消息时的蒙版问题 - 确保消息区域在发送时保持清晰 */
  &.sending {
    opacity: 1 !important;
    filter: none !important;
    pointer-events: auto !important;
  }

  /* 美化滚动条 - 支持主题适配 */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
    border-radius: 4px;
    margin: 4px 0;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-light);
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: var(--el-border-color);
    }

    &:active {
      background: var(--el-color-primary-light-5);
    }
  }

  /* 深色模式下的滚动条样式 */
  .dark & {
    &::-webkit-scrollbar-track {
      background: var(--el-bg-color);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color);

      &:hover {
        background: var(--el-border-color-light);
      }

      &:active {
        background: var(--el-color-primary-light-3);
      }
    }
  }

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color-light) var(--el-bg-color-page);

  .dark & {
    scrollbar-color: var(--el-border-color) var(--el-bg-color);
  }

  .welcome-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }

  .typing-indicator {
    margin-top: 16px;
  }
}

.input-area {
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-light);

  /* 修复 MateChat 输入框主题适配 - 根据实际 DOM 结构 */
  :deep(.mc-input) {
    background: var(--el-bg-color) !important;

    .mc-input-content {
      background: var(--el-bg-color) !important;

      .mc-textarea {
        background: var(--el-bg-color) !important;
        color: var(--el-text-color-primary) !important;
        border-color: var(--el-border-color-light) !important;

        &::placeholder {
          color: var(--el-text-color-placeholder) !important;
        }

        &:hover {
          border-color: var(--el-color-primary) !important;
        }

        &:focus {
          border-color: var(--el-color-primary) !important;
          box-shadow: 0 0 0 2px var(--el-color-primary-light-9) !important;
        }
      }
    }

    .mc-input-foot {
      background: var(--el-bg-color) !important;
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      padding: 8px 12px !important;
      position: relative !important;

      .mc-input-foot-left {
        background: var(--el-bg-color) !important;
        flex: 1 !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
      }

      .mc-input-foot-count {
        color: var(--el-text-color-secondary) !important;
        font-size: 12px !important;
        white-space: nowrap !important;
        margin: 0 12px !important;
      }
    }
  }

  /* 全局强制覆盖 MateChat 组件样式 */
  :deep(.mc-input-wrapper) {
    background: var(--el-bg-color) !important;
    border-color: var(--el-border-color-light) !important;
  }

  :deep(.mc-input-inner),
  :deep(textarea) {
    background: var(--el-bg-color) !important;
    color: var(--el-text-color-primary) !important;
  }

  :deep(.mc-input-inner::placeholder),
  :deep(textarea::placeholder) {
    color: var(--el-text-color-placeholder) !important;
  }

  :deep(.mc-input-count) {
    color: var(--el-text-color-secondary) !important;
  }

  /* 修复 MateChat 发送器底部工具栏主题适配 */
  :deep(.mc-sender) {
    background: var(--el-bg-color) !important;

    .mc-sender-footer {
      background: var(--el-bg-color) !important;
      border-top: 1px solid var(--el-border-color-light) !important;

      .mc-sender-footer-left {
        .mc-sender-footer-item {
          color: var(--el-text-color-primary) !important;

          &:hover {
            background: var(--el-fill-color-light) !important;
            color: var(--el-color-primary) !important;
          }
        }

        .mc-sender-footer-dividing-line {
          background-color: var(--el-border-color-light) !important;
        }

        .mc-sender-footer-maxlength {
          color: var(--el-text-color-secondary) !important;
        }
      }

      .mc-sender-footer-right {
        color: var(--el-text-color-secondary) !important;
      }
    }
  }

  .input-extra {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 8px 12px;
    background: var(--el-bg-color);

    .input-foot-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .input-foot-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: var(--el-text-color-primary);
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background: var(--el-fill-color-light);
          color: var(--el-color-primary);
        }

        i {
          font-size: 14px;
        }
      }

      .input-foot-dividing-line {
        width: 1px;
        height: 14px;
        background-color: var(--el-border-color-light);
      }

      .input-foot-maxlength {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        display: none !important; /* 隐藏重复的字数统计 */
      }
    }

    .input-foot-right {
      .input-hint {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-layout {
    border-radius: 0;
  }

  .chat-sidebar {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    z-index: 1000;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

    &.sidebar-collapsed {
      transform: translateX(-100%);
    }
  }

  .header-controls {
    flex-wrap: wrap;
    gap: 8px !important;

    .control-group {
      .el-select {
        min-width: 100px;
      }
    }
  }
}

/* 自定义下拉框样式 */
.custom-select-dropdown {
  border-radius: 8px !important;
  border: 1px solid var(--el-border-color-light) !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important;
  background: var(--el-bg-color) !important;
  padding: 4px !important;
  min-width: 280px !important; /* 增加下拉框最小宽度 */

  .el-select-dropdown__item {
    border-radius: 4px !important;
    margin: 1px 0 !important;
    padding: 8px 12px !important;
    transition: all 0.2s ease !important;
    font-size: 13px !important;
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    min-height: 44px !important; /* 确保足够的高度 */

    &:hover {
      background: var(--el-fill-color-light) !important;
      color: var(--el-color-primary) !important;

      .option-content .option-label {
        color: var(--el-color-primary) !important;
      }
    }

    /* Element Plus 的选中状态类名 */
    &.selected,
    &.is-selected {
      background: var(--el-color-primary) !important;
      color: var(--el-color-white, white) !important;
      font-weight: 500 !important;
      border-left: 3px solid var(--el-color-white, rgba(255, 255, 255, 0.8)) !important;

      .option-content {
        .option-label {
          color: var(--el-color-white, white) !important;
        }

        .option-desc {
          color: var(--el-color-white, rgba(255, 255, 255, 0.8)) !important;
          opacity: 0.8 !important;
        }
      }

      /* 添加选中标记 - 使用更美观的圆形勾选 */
      &::after {
        content: "●" !important;
        position: absolute !important;
        right: 12px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        font-size: 8px !important;
        color: var(--el-color-white, rgba(255, 255, 255, 0.9)) !important;
        line-height: 1 !important;
      }
    }
  }

  .option-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
    flex: 1; /* 占据剩余空间 */
    justify-content: center; /* 垂直居中 */
    min-height: 28px; /* 最小高度确保居中效果 */

    .option-label {
      font-size: 13px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      line-height: 1.2;
    }

    .option-desc {
      font-size: 11px;
      color: var(--el-text-color-secondary);
      opacity: 0.9;
      line-height: 1.2;
    }
  }
}

/* DevUI 主题适配会自动处理深色/浅色主题 */

/* 响应式设计 */
@media (max-width: 1024px) {
  .mc-header {
    padding: 10px 16px !important;
    min-height: 56px !important;
  }

  .mc-header-logo-container {
    margin-right: 12px !important;
  }

  .mc-header-title {
    font-size: 15px !important;
  }

  .header-controls {
    gap: 8px !important;
  }

  .chat-header :deep(.mc-header .mc-header-operation .header-controls) {
    gap: 8px;

    .control-group {
      &.model-selector,
      &.role-selector {
        min-width: 180px;
        max-width: 200px;
      }

      .control-label {
        font-size: 10px;
        min-width: 28px;
      }

      .custom-select {
        :deep(.el-input__wrapper) {
          height: 28px;
        }

        :deep(.el-input__inner) {
          font-size: 12px;
        }
      }

      .memory-btn {
        height: 28px;
        padding: 0 10px;
        font-size: 12px;

        .memory-text {
          font-size: 11px;
        }

        .memory-icon {
          font-size: 12px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .mc-header {
    padding: 8px 12px !important;
    min-height: 52px !important;
  }

  .mc-header-logo-container {
    margin-right: 8px !important;
  }

  .mc-header-title {
    font-size: 14px !important;
  }

  .header-controls {
    gap: 6px !important;
    flex-wrap: wrap !important; /* 在小屏幕下允许换行 */
  }

  .chat2-container {
    height: 100vh;
    width: 100%;
    margin: 0;
    border-radius: 0;
  }

  .chat-sidebar {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    z-index: 1000;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);

    &.sidebar-collapsed {
      transform: translateX(-100%);
    }
  }

  .chat-header :deep(.mc-header) {
    .mc-header-operation .header-controls {
      flex-wrap: wrap;
      gap: 6px;

      .control-group {
        &.model-selector,
        &.role-selector {
          min-width: 160px;
          max-width: 180px;
        }

        &.memory-toggle {
          margin-left: 0;
          min-width: 70px;
        }

        .control-label {
          font-size: 9px;
          min-width: 24px;

          .label-icon {
            font-size: 12px;
          }
        }

        .custom-select {
          :deep(.el-input__wrapper) {
            height: 26px;
          }

          :deep(.el-input__inner) {
            font-size: 11px;
            padding: 0 8px;
          }
        }

        .memory-btn {
          height: 26px;
          padding: 0 8px;
          font-size: 11px;

          .memory-text {
            font-size: 10px;
          }

          .memory-icon {
            font-size: 11px;
          }
        }
      }
    }
  }
}

/* 欢迎界面容器 */
.welcome-container {
  animation: fadeIn 0.5s ease-in-out;

  /* McIntroduction 组件主题适配 */
  :deep(.mc-introduction) {
    background: transparent !important;

    .mc-introduction-logo-container {
      margin-bottom: 24px;

      img {
        width: 64px;
        height: 64px;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        /* 深色模式下的 logo 阴影 */
        .dark & {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
        }

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);

          .dark & {
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
          }
        }
      }

      .mc-introduction-title {
        color: var(--el-text-color-primary) !important;
        font-size: 28px !important;
        font-weight: 600 !important;
        margin-top: 16px !important;
        letter-spacing: -0.5px;
        background: linear-gradient(
          135deg,
          var(--el-color-primary),
          var(--el-color-primary-light-3)
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .mc-introduction-sub-title {
      color: var(--el-text-color-regular) !important;
      font-size: 18px !important;
      font-weight: 500 !important;
      margin-bottom: 20px !important;
      opacity: 0.9;
    }

    .mc-introduction-description {
      color: var(--el-text-color-secondary) !important;
      font-size: 14px !important;
      line-height: 1.6 !important;
      max-width: 480px;
      margin: 0 auto;

      div {
        margin-bottom: 8px !important;

        &:last-child {
          margin-bottom: 0 !important;
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 全局头部布局修复 */
.mc-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  min-height: 60px !important;
}

.mc-header-logo-container {
  flex: 1 !important;
  min-width: 0 !important;
  margin-right: 16px !important;
}

.mc-header-title {
  color: var(--el-text-color-primary) !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100% !important;
}

.mc-header-operation {
  flex-shrink: 0 !important;
}

.header-controls {
  display: flex !important;
  align-items: center !important;
  flex-wrap: nowrap !important;
  gap: 12px !important;
}

.header-controls .control-group {
  flex-shrink: 0 !important;
}

/* 下拉框文本溢出处理 */
.el-select__wrapper
  .el-select__selection
  .el-select__selected-item
  .el-select__placeholder {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100% !important;
}

.el-input__inner {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 全局 McIntroduction 组件主题适配 */
.mc-introduction {
  background: transparent !important;
}

.mc-introduction-logo-container img {
  width: 64px !important;
  height: 64px !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}

.mc-introduction-logo-container img:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
}

.mc-introduction-title {
  color: var(--el-text-color-primary) !important;
  font-size: 28px !important;
  font-weight: 600 !important;
  margin-top: 16px !important;
  letter-spacing: -0.5px !important;
  background: linear-gradient(
    135deg,
    var(--el-color-primary),
    var(--el-color-primary-light-3)
  ) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.mc-introduction-sub-title {
  color: var(--el-text-color-regular) !important;
  font-size: 18px !important;
  font-weight: 500 !important;
  margin-bottom: 20px !important;
  opacity: 0.9 !important;
}

.mc-introduction-description {
  color: var(--el-text-color-secondary) !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  max-width: 480px !important;
  margin: 0 auto !important;
}

.mc-introduction-description div {
  margin-bottom: 8px !important;
}

.mc-introduction-description div:last-child {
  margin-bottom: 0 !important;
}

/* 深色模式特殊适配 */
.dark .mc-introduction-logo-container img {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3) !important;
}

.dark .mc-introduction-logo-container img:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4) !important;
}

/* 全局滚动条美化 - 适用于所有滚动容器 */
* {
  /* Webkit 浏览器滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-light);
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: var(--el-border-color);
    }

    &:active {
      background: var(--el-color-primary-light-5);
    }
  }

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color-light) var(--el-bg-color-page);
}

/* 深色模式下的全局滚动条 */
.dark * {
  &::-webkit-scrollbar-track {
    background: var(--el-bg-color);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color);

    &:hover {
      background: var(--el-border-color-light);
    }

    &:active {
      background: var(--el-color-primary-light-3);
    }
  }

  scrollbar-color: var(--el-border-color) var(--el-bg-color);
}

/* 修复发送消息时的视觉问题 */
.messages-container.sending {
  /* 确保发送时消息区域保持完全清晰 */
  opacity: 1 !important;
  filter: none !important;
  backdrop-filter: none !important;
  pointer-events: auto !important;

  /* 防止任何可能的蒙版效果 */
  &::before,
  &::after {
    display: none !important;
  }
}

/* 确保消息气泡在发送时也保持清晰 */
.messages-container.sending .mc-bubble,
.messages-container.sending .markdown-bubble {
  opacity: 1 !important;
  filter: none !important;
  backdrop-filter: none !important;
}

/* 修复 MarkdownBubble 组件在发送时的蒙版问题 */
.messages-container .markdown-bubble.bubble-disabled {
  /* 覆盖 disabled 状态的 opacity，保持消息清晰可见 */
  opacity: 1 !important;

  /* 只禁用操作按钮的点击，不影响视觉效果 */
  .bubble-actions {
    pointer-events: none !important;

    .bubble-action-btn {
      opacity: 0.5 !important;
      cursor: not-allowed !important;
    }
  }
}

/* 确保发送状态下的消息气泡内容完全清晰 */
.messages-container.sending .markdown-bubble {
  .bubble-content {
    opacity: 1 !important;
    filter: none !important;
  }

  .bubble-header {
    opacity: 1 !important;
  }

  .bubble-avatar {
    opacity: 1 !important;
  }
}

/* 输入区域在发送时的样式优化 */
.input-area {
  /* 确保输入区域在发送时不会有视觉干扰 */
  &.sending {
    opacity: 1 !important;
    filter: none !important;
  }
}

/* 特殊容器的滚动条优化 */
.el-scrollbar__wrap {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-lighter);
    border-radius: 3px;
    transition: all 0.3s ease;

    &:hover {
      background: var(--el-border-color-light);
    }
  }
}

/* 下拉框滚动条 */
.el-select-dropdown__wrap {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-bg-color);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-lighter);
    border-radius: 2px;

    &:hover {
      background: var(--el-border-color-light);
    }
  }
}

/* 确保滚动条在主题切换时平滑过渡 */
* {
  &::-webkit-scrollbar,
  &::-webkit-scrollbar-track,
  &::-webkit-scrollbar-thumb {
    transition: background-color 0.3s ease, border-color 0.3s ease;
  }
}

/* 隐藏不必要的滚动条 */
.el-popper {
  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;
}

/* 消息气泡内容的滚动条（如果有的话） */
.markdown-bubble,
.mc-bubble {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-lighter);
    border-radius: 2px;

    &:hover {
      background: var(--el-border-color-light);
    }
  }
}

/* 全局 MateChat 组件主题适配 - 根据实际 DOM 结构 */
.mc-input {
  background: var(--el-bg-color) !important;
}

.mc-input-content {
  background: var(--el-bg-color) !important;
}

.mc-textarea {
  background: var(--el-bg-color) !important;
  color: var(--el-text-color-primary) !important;
  border-color: var(--el-border-color-light) !important;
}

.mc-textarea::placeholder {
  color: var(--el-text-color-placeholder) !important;
}

.mc-textarea:hover {
  border-color: var(--el-color-primary) !important;
}

.mc-textarea:focus {
  border-color: var(--el-color-primary) !important;
  box-shadow: 0 0 0 2px var(--el-color-primary-light-9) !important;
}

.mc-input-foot {
  background: var(--el-bg-color) !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 8px 12px !important;
  position: relative !important;
}

.mc-input-foot-left {
  background: var(--el-bg-color) !important;
  flex: 1 !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

.mc-input-foot-count {
  color: var(--el-text-color-secondary) !important;
  font-size: 12px !important;
  white-space: nowrap !important;
  margin: 0 12px !important;
  order: 2 !important; /* 确保在发送按钮前显示 */
}

/* 隐藏重复的字数统计 */
.input-foot-maxlength {
  display: none !important;
}

.mc-button {
  background: var(--el-color-primary) !important;
  border-color: var(--el-color-primary) !important;
  color: white !important;
  flex-shrink: 0 !important; /* 防止按钮被压缩 */
  margin-left: 8px !important;
  order: 3 !important; /* 确保在最后显示 */
}

.mc-button:hover {
  background: var(--el-color-primary-light-3) !important;
  border-color: var(--el-color-primary-light-3) !important;
}

.mc-button:disabled {
  background: var(--el-fill-color-light) !important;
  border-color: var(--el-border-color-light) !important;
  color: var(--el-text-color-placeholder) !important;
}

/* 全局 MateChat 消息气泡主题适配 */
.mc-bubble-right .mc-bubble-content {
  background: var(--el-color-primary) !important;
  color: white !important;
  border-radius: 18px 18px 4px 18px !important;
  padding: 12px 16px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.mc-bubble-left .mc-bubble-content {
  background: var(--el-bg-color) !important;
  color: var(--el-text-color-primary) !important;
  border: 1px solid var(--el-border-color-light) !important;
  border-radius: 18px 18px 18px 4px !important;
  padding: 12px 16px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

/* 美化头像样式 */
.mc-bubble-avatar-style {
  background: var(--el-bg-color) !important;
  border: 2px solid var(--el-border-color-light) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
  overflow: hidden !important;
}

.mc-bubble-avatar-style:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 头像 SVG 图标颜色适配 */
.mc-bubble-avatar-style svg path[fill="#959EB2"] {
  fill: var(--el-color-primary) !important;
}

.mc-bubble-avatar-style svg path[fill="#CACFD8"] {
  fill: var(--el-text-color-placeholder) !important;
}

/* 深色模式特殊适配 */
.dark .mc-bubble-right .mc-bubble-content {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.dark .mc-bubble-left .mc-bubble-content {
  border-color: var(--el-border-color) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

.dark .mc-bubble-avatar-style {
  border-color: var(--el-border-color) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.dark .mc-bubble-avatar-style:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

/* 响应式布局优化 */
@media (max-width: 1024px) {
  .input-hint {
    font-size: 11px !important;
  }

  .mc-bubble-content {
    padding: 10px 14px !important;
    font-size: 14px !important;
  }

  .mc-bubble-avatar-style {
    width: 32px !important;
    height: 32px !important;
  }

  .mc-bubble-avatar-style svg {
    width: 32px !important;
    height: 32px !important;
  }
}

/* 消息气泡动画效果 */
.mc-bubble {
  animation: messageSlideIn 0.3s ease-out !important;
  transform-origin: bottom !important;
}

.mc-bubble-right {
  animation: messageSlideInRight 0.3s ease-out !important;
}

.mc-bubble-left {
  animation: messageSlideInLeft 0.3s ease-out !important;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes messageSlideInRight {
  from {
    opacity: 0;
    transform: translateX(20px) translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) translateY(0) scale(1);
  }
}

@keyframes messageSlideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px) translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) translateY(0) scale(1);
  }
}

/* 消息气泡悬停效果 */
.mc-bubble-content {
  transition: all 0.2s ease !important;
  cursor: default !important;
}

.mc-bubble-content:hover {
  transform: translateY(-1px) !important;
}

.mc-bubble-right .mc-bubble-content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.mc-bubble-left .mc-bubble-content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.dark .mc-bubble-right .mc-bubble-content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

.dark .mc-bubble-left .mc-bubble-content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* 消息气泡底部操作按钮样式 */
.bubble-bottom-area {
  margin-top: 8px;
  display: flex;
  gap: 4px;
  justify-content: flex-start;
  align-items: center;
  opacity: 0;
  transform: translateY(-4px);
  transition: all 0.2s ease;

  .bubble-action-btn {
    width: 28px !important;
    height: 28px !important;
    border-radius: 6px !important;
    background: var(--el-bg-color) !important;
    border: 1px solid var(--el-border-color-lighter) !important;
    color: var(--el-text-color-regular) !important;
    padding: 0 !important;
    min-width: unset !important;

    &:hover {
      background: var(--el-fill-color-light) !important;
      border-color: var(--el-color-primary-light-7) !important;
      color: var(--el-color-primary) !important;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: translateY(0);
    }

    .el-icon {
      font-size: 14px;
    }
  }
}

/* 鼠标悬停在消息气泡上时显示操作按钮 */
.mc-bubble:hover .bubble-bottom-area {
  opacity: 1;
  transform: translateY(0);
}

/* 深色模式下的操作按钮 */
.dark .bubble-bottom-area {
  .bubble-action-btn {
    background: var(--el-bg-color-page) !important;
    border-color: var(--el-border-color) !important;

    &:hover {
      background: var(--el-fill-color) !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .bubble-bottom-area {
    margin-top: 6px;
    gap: 2px;

    .bubble-action-btn {
      width: 24px !important;
      height: 24px !important;

      .el-icon {
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 768px) {
  .mc-input-foot {
    padding: 6px 8px !important;
  }

  .mc-input-foot-count {
    font-size: 11px !important;
    margin: 0 8px !important;
  }

  .input-hint {
    display: none !important; /* 在小屏幕下隐藏提示文字 */
  }

  .mc-button {
    margin-left: 4px !important;
  }
}

@media (max-width: 480px) {
  .mc-input-foot {
    padding: 4px 6px !important;
  }

  .mc-input-foot-left {
    gap: 8px !important;
  }

  .mc-input-foot-count {
    font-size: 10px !important;
    margin: 0 6px !important;
  }
}

/* McIntroduction 响应式设计 */
@media (max-width: 1024px) {
  .mc-introduction-title {
    font-size: 24px !important;
  }

  .mc-introduction-sub-title {
    font-size: 16px !important;
  }

  .mc-introduction-description {
    font-size: 13px !important;
    max-width: 400px !important;
  }
}

@media (max-width: 768px) {
  .mc-introduction-logo-container img {
    width: 56px !important;
    height: 56px !important;
  }

  .mc-introduction-title {
    font-size: 22px !important;
    margin-top: 12px !important;
  }

  .mc-introduction-sub-title {
    font-size: 15px !important;
    margin-bottom: 16px !important;
  }

  .mc-introduction-description {
    font-size: 12px !important;
    max-width: 320px !important;
    padding: 0 16px;
  }
}

@media (max-width: 480px) {
  .mc-introduction-logo-container img {
    width: 48px !important;
    height: 48px !important;
  }

  .mc-introduction-title {
    font-size: 20px !important;
    margin-top: 10px !important;
  }

  .mc-introduction-sub-title {
    font-size: 14px !important;
    margin-bottom: 14px !important;
  }

  .mc-introduction-description {
    font-size: 11px !important;
    max-width: 280px !important;
    padding: 0 20px;
  }
}
</style>