<template>
  <div class="chat-ui-reference-demo">
    <div class="demo-header">
      <h1>ChatUI @ 引用功能演示</h1>
      <p>这个演示展示了ChatUI组件中新增的@引用功能，类似VSCode AI插件的体验</p>
    </div>

    <div class="demo-content">
      <div class="demo-section">
        <h2>功能特性</h2>
        <ul>
          <li>✨ 点击@按钮打开实体选择器</li>
          <li>🎯 选中后显示为高亮气泡</li>
          <li>⌨️ 光标移动时整个气泡作为一个单位</li>
          <li>💬 对话中引用也显示为高亮气泡</li>
          <li>🗑️ 可以删除引用气泡</li>
          <li>📝 自动构建引用上下文信息</li>
        </ul>
      </div>

      <div class="demo-section">
        <h2>演示区域</h2>
        <div class="chat-container">
          <ChatUI
            :chat-id="demoChat.id"
            :book-id="demoBookId"
            @chat-updated="handleChatUpdated"
            @get-editor-content="handleGetEditorContent"
          />
        </div>
      </div>

      <div class="demo-section">
        <h2>使用说明</h2>
        <ol>
          <li>在输入框中点击 <code>@</code> 按钮</li>
          <li>选择要引用的实体、章节或场景卡池</li>
          <li>选中的引用会显示为彩色气泡</li>
          <li>发送消息时，引用信息会自动添加到上下文中</li>
          <li>在对话中，@引用也会显示为高亮气泡</li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ChatUI from '@/components/ChatUI.vue'

// 演示数据
const demoBookId = ref('demo-book-123')
const demoChat = ref({
  id: 'demo-chat-456',
  title: '引用功能演示对话',
  model_id: 'gpt-3.5-turbo',
  messages: [
    {
      id: 'demo-msg-1',
      role: 'assistant',
      content: '你好！我是AI助手。你可以使用@按钮来引用实体、章节等内容，让我更好地理解你的问题。',
      timestamp: Math.floor(Date.now() / 1000) - 300
    },
    {
      id: 'demo-msg-2',
      role: 'user',
      content: '我想了解一下@主角的背景故事',
      timestamp: Math.floor(Date.now() / 1000) - 200
    },
    {
      id: 'demo-msg-3',
      role: 'assistant',
      content: '好的！我看到你引用了@主角。根据引用的信息，我可以为你详细介绍主角的背景故事...',
      timestamp: Math.floor(Date.now() / 1000) - 100
    }
  ],
  roles: [],
  memory_enabled: true,
  created_at: Math.floor(Date.now() / 1000) - 3600,
  last_updated: Math.floor(Date.now() / 1000)
})

// 事件处理
const handleChatUpdated = (chatData) => {
  console.log('演示：聊天数据已更新', chatData)
}

const handleGetEditorContent = (callback) => {
  // 模拟编辑器内容
  const mockEditorContent = `// 这是模拟的编辑器内容
function demoFunction() {
  console.log('这是一个演示函数');
  return 'Hello World';
}`
  
  callback(mockEditorContent)
}

// 组件挂载时的初始化
onMounted(() => {
  console.log('ChatUI引用功能演示页面已加载')
  
  // 模拟设置一些演示数据到全局
  if (window.pywebview && window.pywebview.api) {
    // 如果有API，可以设置一些模拟数据
    console.log('API可用，可以设置模拟数据')
  } else {
    console.log('API不可用，使用静态演示数据')
  }
})
</script>

<style lang="scss" scoped>
.chat-ui-reference-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: var(--el-bg-color-page);
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    color: var(--el-text-color-primary);
    font-size: 28px;
    margin-bottom: 10px;
  }
  
  p {
    color: var(--el-text-color-regular);
    font-size: 16px;
    line-height: 1.6;
  }
}

.demo-content {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 20px;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
}

.demo-section {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 20px;
  box-shadow: var(--el-box-shadow-light);
  
  h2 {
    color: var(--el-text-color-primary);
    font-size: 20px;
    margin-bottom: 15px;
    border-bottom: 2px solid var(--el-color-primary);
    padding-bottom: 5px;
  }
  
  ul, ol {
    color: var(--el-text-color-regular);
    line-height: 1.8;
    
    li {
      margin-bottom: 8px;
    }
  }
  
  code {
    background: var(--el-fill-color-light);
    color: var(--el-color-primary);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', monospace;
  }
}

.chat-container {
  height: 600px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
  background: var(--el-bg-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-ui-reference-demo {
    padding: 10px;
  }
  
  .demo-header h1 {
    font-size: 24px;
  }
  
  .chat-container {
    height: 500px;
  }
}
</style>
