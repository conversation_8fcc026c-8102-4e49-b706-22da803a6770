<template>
  <div class="novel-download">
    <el-tabs v-model="activeTab" class="download-tabs">
      <!-- 下载设置标签页 -->
      <el-tab-pane label="下载设置" name="settings">
        <div class="settings-panel">
          <div class="settings-card">
            <el-form :model="downloadForm" label-width="80px" class="create-task-form">
              <div class="main-content">
                <!-- 上方区域：URL输入和下载设置 -->
                <div class="top-section">
                  <!-- URL输入区域 -->
                  <div class="url-input-section">
                    <div class="section-header">
                      <el-icon><Link /></el-icon>
                      <span class="title">批量输入网址</span>
                    </div>
                    <div class="url-input-content">
                      <el-input
                        v-model="downloadForm.url"
                        type="textarea"
                        :rows="8"
                        placeholder="请输入小说网址，每行一个网址"
                        resize="none"
                        spellcheck="false"
                        autocomplete="off"
                        autocorrect="off"
                        autocapitalize="off"
                      />
                      <div class="url-input-footer">
                        <span class="url-count">已输入 {{ getUrlCount() }} 个网址</span>
                        <div class="url-actions">
                          <el-button type="primary" link @click="formatUrls">
                            <el-icon><Sort /></el-icon>
                            整理格式
                          </el-button>
                          <el-button type="danger" link @click="clearUrls">
                            <el-icon><Delete /></el-icon>
                            清空
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 下载设置区域 -->
                  <div class="download-settings">
                    <div class="section-header">
                      <el-icon><Setting /></el-icon>
                      <span class="title">下载设置</span>
                      <el-button 
                        type="primary" 
                        link 
                        class="manage-rules-btn"
                        @click="showRuleList"
                      >
                        <el-icon><Setting /></el-icon>
                        规则管理【标签】
                      </el-button>
                    </div>

                    <div class="settings-content">
                      <!-- 规则选择 -->
                      <el-form-item label="规则" required>
                        <el-select v-model="downloadForm.rule" placeholder="请选择规则">
                          <el-option
                            v-for="(rule, id) in novelRules"
                            :key="id"
                            :label="rule.name"
                            :value="id"
                          />
                        </el-select>
                      </el-form-item>

                      <!-- 下载目录 -->
                      <el-form-item label="目录" required>
                        <div class="path-input-group">
                          <el-input
                            v-model="downloadForm.downloadPath"
                            placeholder="请选择下载目录"
                            spellcheck="false"
                            autocomplete="off"
                            autocorrect="off"
                            autocapitalize="off"
                          />
                          <el-button type="primary" @click="selectDirectory('downloadPath')">
                            <el-icon><FolderOpened /></el-icon>
                          </el-button>
                        </div>
                      </el-form-item>

                      <!-- 下载参数 -->
                      <div class="download-params">
                        <el-form-item label="章节数">
                          <el-input-number
                            v-model="downloadForm.chapterCount"
                            :min="0"
                            :step="10"
                            controls-position="right"
                            placeholder="全部"
                          />
                        </el-form-item>
                        <el-form-item label="间隔(秒)">
                          <el-input-number 
                            v-model="downloadForm.intervalTime" 
                            :min="1" 
                            :max="60" 
                            :step="1"
                            controls-position="right"
                          />
                        </el-form-item>
                      </div>
                    </div>

                    <!-- 创建任务按钮 -->
                    <div class="form-actions">
                      <el-button type="primary" size="large" @click="createTask">
                        <el-icon><Upload /></el-icon>
                        创建下载任务
                      </el-button>
                    </div>
                  </div>
                </div>

                <!-- Chrome配置区域 -->
                <div class="chrome-config-section">
                  <div class="section-header">
                    <el-icon><Monitor /></el-icon>
                    <span class="title">Chrome配置</span>
                  </div>
                  <div class="chrome-config-list">
                    <el-table
                      :data="chromeUserDataDirs"
                      style="width: 100%"
                      highlight-current-row
                      @current-change="handleChromeConfigClick"
                    >
                      <el-table-column width="120">
                        <template #default="scope">
                          <el-radio 
                            v-model="downloadForm.chromeConfigId"
                            :label="scope.row.id"
                            @change="() => handleChromeConfigClick(scope.row)"
                          >
                            <span class="sr-only">选择 </span>
                          </el-radio>
                        </template>
                      </el-table-column>
                      <el-table-column prop="name" label="配置名称" min-width="120">
                        <template #default="scope">
                          <span class="config-name">{{ scope.row.name }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="path" label="路径" min-width="200" show-overflow-tooltip>
                        <template #default="scope">
                          <span class="config-path">{{ scope.row.path }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="标签" width="120" align="right">
                        <template #default="scope">
                          <div class="config-tags">
                            <el-tag size="small" class="port-badge">{{ scope.row.port }}</el-tag>
                            <el-tag v-if="scope.row.enableExtensions" size="small" type="warning">扩展</el-tag>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </el-form>
          </div>
        </div>
      </el-tab-pane>

      <!-- 任务管理标签页 -->
      <el-tab-pane label="任务管理" name="tasks">
        <div class="task-management">
          <div class="section-header">
            <h2 class="section-title">任务管理</h2>
            <div class="header-actions">
              <el-button type="primary" @click="refreshTasks">
                <el-icon><Refresh /></el-icon>
                刷新列表
              </el-button>
            </div>
          </div>

          <div class="task-list">
            <el-card 
              v-for="task in tasks" 
              :key="task.id" 
              class="task-card" 
              :body-style="{ padding: '0' }"
              :data-task-id="task.id"
            >
              <div class="task-header" @click="toggleTaskDetail(task)">
                <div class="task-info">
                  <div class="task-title">
                    <span class="time">{{ formatDateTime(task.created_at) }}</span>
                    <span class="rule">{{ task.config.rule.name }}</span>
                  </div>
                  <div class="task-progress">
                    <el-progress 
                      :percentage="task.progress" 
                      :status="getProgressStatus(task.status)"
                      :stroke-width="8"
                    />
                  </div>
                </div>
                <div class="task-status">
                  <el-tag :type="getTaskStatusType(task.status)" size="small">
                    {{ getTaskStatusText(task.status) }}
                  </el-tag>
                  <el-icon class="expand-icon" :class="{ 'is-active': task.showDetail }">
                    <ArrowDown />
                  </el-icon>
                </div>
              </div>

              <div v-show="task.showDetail" class="task-detail">
                <div class="detail-section">
                  <div class="section-title">任务信息</div>
                  <div class="info-grid">
                    <div class="info-item">
                      <span class="label">创建时间：</span>
                      <span class="value">{{ formatDateTime(task.created_at) }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">下载规则：</span>
                      <span class="value">{{ task.config.rule.name }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">总进度：</span>
                      <span class="value">{{ task.progress }}%</span>
                    </div>
                    <div class="info-item">
                      <span class="label">下载目录：</span>
                      <span class="value">{{ task.config.download_path }}</span>
                    </div>
                  </div>
                </div>

                <div class="detail-section">
                  <div class="section-title">
                    <span>任务日志</span>
                    <div class="log-actions">
                      <el-button
                        type="info"
                        size="small"
                        @click="scrollToBottom(task.id)"
                        title="跳转到最新日志"
                      >
                        <el-icon><ArrowDown /></el-icon>
                        底部
                      </el-button>
                      <el-button
                        v-if="task.status === 'running'"
                        type="warning"
                        size="small"
                        @click="stopTask(task.id)"
                      >
                        停止任务
                      </el-button>
                      <el-button
                        v-if="task.status === 'failed'"
                        type="primary"
                        size="small"
                        @click="retryTask(task.id)"
                      >
                        重试任务
                      </el-button>
                      <el-button
                        v-if="task.status === 'completed'"
                        type="success"
                        size="small"
                        @click="openDownloadFolder(task.config.download_path)"
                      >
                        打开目录
                      </el-button>
                    </div>
                  </div>
                  <div class="log-content">
                    <template v-if="task.outputs && task.outputs.length">
                      <div 
                        v-for="(output, index) in task.outputs" 
                        :key="index"
                        :class="['log-line', output.type]"
                      >
                        <el-tag 
                          v-if="output.type === 'error'" 
                          type="danger" 
                          size="small" 
                          effect="dark"
                        >
                          错误
                        </el-tag>
                        <el-tag 
                          v-else-if="output.type === 'success'" 
                          type="success" 
                          size="small" 
                          effect="dark"
                        >
                          成功
                        </el-tag>
                        <el-tag 
                          v-else-if="output.type === 'warning'" 
                          type="warning" 
                          size="small" 
                          effect="dark"
                        >
                          警告
                        </el-tag>
                        <el-tag 
                          v-else 
                          type="info" 
                          size="small" 
                          effect="dark"
                        >
                          信息
                        </el-tag>
                        <span class="message">{{ output.message }}</span>
                      </div>
                    </template>
                    <div v-else class="no-logs">
                      暂无日志信息
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 规则管理对话框 -->
    <el-dialog
      v-model="ruleListDialog.visible"
      title="规则管理"
      width="80%"
      class="rule-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :modal-append-to-body="false"
    >
      <div class="table-container">
        <el-table
          :data="Object.entries(novelRules).map(([id, rule]) => ({ id, ...rule }))"
          style="width: 100%"
          height="500px"
        >
          <el-table-column prop="name" label="规则名称" min-width="120" />
          <el-table-column prop="book_title_rule" label="书名选择器" min-width="150" show-overflow-tooltip />
          <el-table-column prop="directory_rule" label="目录选择器" min-width="150" show-overflow-tooltip />
          <el-table-column prop="content_rule" label="内容选择器" min-width="150" show-overflow-tooltip />
          <el-table-column prop="description_rule" label="简介选择器" min-width="150" show-overflow-tooltip />
          <!-- <el-table-column prop="chapter_title_rule" label="章节标题选择器" min-width="150" show-overflow-tooltip /> -->
          <el-table-column prop="need_decrypt" label="要解密" width="100">
            <template #default="scope">
              {{ scope.row.need_decrypt ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button-group>
                <el-button type="primary" link @click="editRule(scope.row)">编辑</el-button>
                <el-button type="danger" link @click="deleteRule(scope.row)" :disabled="isDefaultRule(scope.row.id)">删除</el-button>
                <el-button type="success" link @click="testRule(scope.row)">测试</el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="ruleListDialog.visible = false">关闭</el-button>
          <el-button type="primary" @click="showAddRule">添加规则</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 规则编辑对话框 -->
    <el-dialog
      v-model="ruleDialog.visible"
      :title="ruleDialog.isEdit ? '编辑规则' : '添加规则'"
      width="50%"
      class="edit-rule-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :modal-append-to-body="false"
    >
      <el-form :model="ruleDialog.form" label-width="120px">
        <el-form-item label="规则名称" required>
          <el-input
            v-model="ruleDialog.form.name"
            spellcheck="false"
            autocomplete="off"
            autocorrect="off"
            autocapitalize="off"
          />
        </el-form-item>
        <el-form-item label="书名选择器" required>
          <el-input
            v-model="ruleDialog.form.book_title_rule"
            spellcheck="false"
            autocomplete="off"
            autocorrect="off"
            autocapitalize="off"
          />
        </el-form-item>
        <el-form-item label="目录选择器" required>
          <el-input
            v-model="ruleDialog.form.directory_rule"
            spellcheck="false"
            autocomplete="off"
            autocorrect="off"
            autocapitalize="off"
          />
        </el-form-item>
        <el-form-item label="内容选择器" required>
          <el-input
            v-model="ruleDialog.form.content_rule"
            spellcheck="false"
            autocomplete="off"
            autocorrect="off"
            autocapitalize="off"
          />
        </el-form-item>
        <el-form-item label="简介选择器" required>
          <el-input
            v-model="ruleDialog.form.description_rule"
            spellcheck="false"
            autocomplete="off"
            autocorrect="off"
            autocapitalize="off"
          />
        </el-form-item>
        <!-- <el-form-item label="章节标题选择器" required>
          <el-input v-model="ruleDialog.form.chapter_title_rule" />
        </el-form-item> -->
        <el-form-item label="需要解密">
          <el-switch v-model="ruleDialog.form.need_decrypt" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ruleDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveRule">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick, onUnmounted, watch } from 'vue'
import { useConfigStore } from '@/stores/config'
import { useNovelRulesStore } from '@/stores/novelRules'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, Plus, Folder, Search, Monitor, Link, Upload, FolderOpened, ArrowDown } from '@element-plus/icons-vue'
import { useLocalStorage } from '@vueuse/core'
import { Refresh } from '@element-plus/icons-vue'
window.bridge = window.pywebview.api.drssion_controller
const configStore = useConfigStore()
const novelRulesStore = useNovelRulesStore()

// 计算属性
const novelRules = computed(() => {
  // 使用专门的小说规则store
  return novelRulesStore.rules || {}
})
const chromeUserDataDirs = computed(() => configStore.state.config.chrome.userDataDirs || [])

// 下载表单
const downloadForm = ref({
  url: '',
  rule: '',
  chapterCount: 30, // 默认章节数改为30
  intervalTime: 1, // 默认间隔改为1秒
  downloadPath: configStore.state.config.chrome.downloadDir || '',
  chromeConfigId: configStore.state.config.chrome.userDataDirs.find(dir => dir.isDefault)?.id || ''
})

// 章节列表
const chapterList = ref([])
const selectedChapters = ref([])
const directoryLoaded = ref(false)

// 规则管理对话框
const ruleListDialog = ref({
  visible: false
})

// 规则编辑对话框
const ruleDialog = ref({
  visible: false,
  isEdit: false,
  form: {
    id: '',
    name: '',
    book_title_rule: '',
    directory_rule: '',
    content_rule: '',
    description_rule: '',
    chapter_title_rule: '',
    need_decrypt: false
  }
})

// 默认规则ID列表
const defaultRuleIds = ['qidian', 'fanqie', 'feilu', 'ciweimao', 'qimao']

// 判断是否为默认规则
const isDefaultRule = (ruleId) => {
  return defaultRuleIds.includes(ruleId)
}

// 加载小说目录
const loadDirectory = async () => {
  try {
    if (!downloadForm.value.url) {
      ElMessage.warning('请输入小说网址')
      return
    }
    if (!downloadForm.value.rule) {
      ElMessage.warning('请选择则')
      return
    }

    const rule = novelRules.value[downloadForm.value.rule]
    if (!rule) {
      ElMessage.warning('规则不存在')
      return
    }

    const response = await novelRulesStore.testRule({
      rule,
      url: downloadForm.value.url
    })

    if (response.chapters) {
      chapterList.value = response.chapters.map(chapter => ({
        ...chapter,
        status: 'pending'
      }))
      directoryLoaded.value = true
    } else {
      ElMessage.warning('未找到何章节')
    }
  } catch (error) {
    ElMessage.error(error.message || '加载目录失败')
  }
}

// 章节选择相关
const handleSelectionChange = (selection) => {
  selectedChapters.value = selection
}

const selectAll = () => {
  chapterList.value.forEach(chapter => {
    chapter.selected = true
  })
}

const invertSelection = () => {
  chapterList.value.forEach(chapter => {
    chapter.selected = !chapter.selected
  })
}

// 开始下载
const startDownload = async () => {
  if (!downloadForm.value.downloadPath) {
    ElMessage.error('请选择下载目录')
    return
  }

  if (!downloadForm.value.chromeConfigId) {
    ElMessage.error('请选择Chrome配置')
    return
  }

  const selectedChapters = selectedChapters.value
  if (!selectedChapters.length) {
    ElMessage.error('请选择下载的章节')
    return
  }

  // 获取选中的Chrome配置
  const chromeConfig = chromeUserDataDirs.value.find(
    dir => dir.id === downloadForm.value.chromeConfigId
  )
  if (!chromeConfig) {
    ElMessage.error('Chrome配置不存在')
    return
  }

  const taskId = crypto.randomUUID()
  // taskManager.createTask(taskId, {
  //   url: downloadForm.value.url,
  //   rule: downloadForm.value.rule,
  //   chapters: selectedChapters,
  //   config: {
  //     chapterCount: downloadForm.value.chapterCount,
  //     intervalTime: downloadForm.value.intervalTime,
  //     downloadPath: downloadForm.value.downloadPath,
  //     chromeConfig: {
  //       chromePath: configStore.state.config.chrome.default_path,
  //       userDataDir: chromeConfig.path,
  //       proxy: chromeConfig.proxy || ''
  //     }
  //   }
  // })

  try {
    // await ipcRenderer.invoke('start-download', taskId)
    await window.pywebview.api.start_download(taskId)
    ElMessage.success('下载任务已启动')
  } catch (error) {
    ElMessage.error(`启动下载任务失败: ${error.message}`)
  }
}




// 规则管理相关
const showRuleList = async () => {
  // 确保规则数据已加载
  await novelRulesStore.loadRules()
  ruleListDialog.value.visible = true
}

const showAddRule = () => {
  ruleDialog.value.isEdit = false
  ruleDialog.value.form = {
    id: crypto.randomUUID(),
    name: '',
    book_title_rule: '',
    directory_rule: '',
    content_rule: '',
    description_rule: '',
    chapter_title_rule: '',
    need_decrypt: false
  }
  ruleDialog.value.visible = true
}

const editRule = (rule) => {
  ruleDialog.value.isEdit = true
  ruleDialog.value.form = { ...rule }
  ruleDialog.value.visible = true
}

const deleteRule = async (rule) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该规则吗？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const result = await novelRulesStore.deleteRule(rule.id)
    if (result.status === 'success') {
      ElMessage.success('删除成功')
      await refreshTasks()  // 刷新任务列表
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const saveRule = async () => {
  try {
    if (!ruleDialog.value.form.name) {
      throw new Error('规则名不能为空')
    }
    if (!ruleDialog.value.form.book_title_rule) {
      throw new Error('书名选择器不能为空')
    }
    if (!ruleDialog.value.form.directory_rule) {
      throw new Error('目录选择器不能为空')
    }
    if (!ruleDialog.value.form.content_rule) {
      throw new Error('内容选择器不能为空')
    }
    if (!ruleDialog.value.form.description_rule) {
      throw new Error('简介选择器不能为空')
    }
    // if (!ruleDialog.value.form.chapter_title_rule) {
    //   throw new Error('章节标题选择器不能为空')
    // }

    await novelRulesStore.saveRule(ruleDialog.value.form)
    ElMessage.success('保存成功')
    ruleDialog.value.visible = false

    // 保存后刷新任务
    await refreshTasks()
  } catch (err) {
    ElMessage.error(err.message)
  }
}

const testRule = async (rule) => {
  try {
    const url = await ElMessageBox.prompt('请输入要测试的URL', '测试规则', {
      inputPattern: /^https?:\/\/.+/,
      inputErrorMessage: '请输入正确的URL'
    })

    // 显示加载状态
    ElMessage({
      message: '正在测试规则，请稍候...',
      type: 'info',
      duration: 2000
    })

    const response = await novelRulesStore.testRule({
      rule,
      url: url.value
    })

    // 显示测试结果
    await ElMessageBox.alert(JSON.stringify(response, null, 2), '测试结果', {
      closeOnClickModal: true
    })
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '测试失败')
    }
  }
}

// 态相关
const getStatusType = (status) => {
  switch (status) {
    case 'success':
      return 'success'
    case 'error':
      return 'danger'
    case 'downloading':
      return 'warning'
    default:
      return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'success':
      return '已完成'
    case 'error':
      return '失败'
    case 'downloading':
      return '下载中'
    default:
      return '等待中'
  }
}

// 务列表
const tasks = ref([])

// 任务状映射
const taskStatusMap = {
  initializing: { type: 'info', text: '初始化' },
  running: { type: 'primary', text: '下载中' },
  completed: { type: 'success', text: '已完成' },
  failed: { type: 'danger', text: '失败' },
  stopped: { type: 'warning', text: '已停止' }
}

// 获取任务状态类型
const getTaskStatusType = (status) => taskStatusMap[status]?.type || 'info'

// 获取任务状态文本
const getTaskStatusText = (status) => taskStatusMap[status]?.text || status

// 获取进度条状态
const getProgressStatus = (status) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  if (status === 'stopped') return 'warning'
  return ''
}

// 获取规则名称
const getRuleName = (ruleId) => {
  const rules = {
    'qidian': 'qidian',
    'fanqie': '番茄',
    'feilu': '飞卢',
    'ciweimao': '刺猬猫',
    'qimao': '七猫'
  }
  return rules[ruleId] || ruleId
}

// 格式化时间
const formatDateTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 修改创建任务的逻辑
const createTask = async () => {
  try {
    // 获取Chrome配置
    const chromeConfig = chromeUserDataDirs.value.find(
      dir => dir.id === downloadForm.value.chromeConfigId
    )
    if (!chromeConfig) {
      throw new Error('请选择Chrome配置')
    }

    // 获取所有URL
    const urls = downloadForm.value.url.split('\n')
      .map(url => url.trim())
      .filter(url => url && url.startsWith('http'))

    if (urls.length === 0) {
      throw new Error('请输入有效的小说网址')
    }

    // 获取选中的规则对象
    if (!downloadForm.value.rule) {
      throw new Error('请选择规则')
    }

    // 确保规则列表已加载
    if (Object.keys(novelRules.value).length === 0) {
      await novelRulesStore.loadRules()
    }

    const selectedRule = novelRules.value[downloadForm.value.rule]
    if (!selectedRule) {
      throw new Error('选择的规则无效')
    }
    
    // 构建任务配置
    const taskConfig = {
      urls: urls,
      rule: selectedRule,  // 直接使用选中的规则对象
      chapter_count: downloadForm.value.chapterCount || 0,
      interval_time: downloadForm.value.intervalTime || 3,
      download_path: downloadForm.value.downloadPath,
      chrome_config: {
        user_data_dir: `${chromeConfig.path}/${chromeConfig.name}`,
        port: chromeConfig.port,
        load_extensions: chromeConfig.enableExtensions
      }
    }
    
    // 显示加载状态
    ElMessage({
      message: '正在创建任务...',
      type: 'info',
      duration: 2000
    })
    
    // 发送创建任务请求
    const res = JSON.parse(await window.bridge.create_download_task(taskConfig))
    if (res.status === 'success') {
      ElMessage.success('任务创建成功')
      
      // 创建任务后切换到任务管理标签页
      activeTab.value = 'tasks'
      await refreshTasks()
    } else {
      throw new Error(res.message || '创建任务失败')
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

// 停止任务
const stopTask = async (taskId) => {
  try {
    // 显示加载状态
    ElMessage({
      message: '正在停止任务...',
      type: 'info',
      duration: 1000
    })
    
    const res = JSON.parse(await window.bridge.stop_task(taskId))
    if (res.status === 'success') {
      ElMessage.success(res.message || '任务已停止')
      await refreshTasks()
    } else {
      throw new Error(res.message || '停止任务失败')
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

// 重试任务
const retryTask = async (taskId) => {
  try {
    const task = tasks.value.find(t => t.id === taskId)
    if (!task) {
      throw new Error('任务不存在')
    }
    
    // 显示加载状态
    ElMessage({
      message: '正在重试任务...',
      type: 'info',
      duration: 2000
    })
    
    // 使用原始配置重新创任务
    const res = JSON.parse(await window.bridge.create_download_task(task.config))
    if (res.status === 'success') {
      ElMessage.success(res.message || '任务已重新启动')
      await refreshTasks()
    } else {
      throw new Error(res.message || '重试任务失败')
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

// 刷新任务列表
const refreshTasks = async () => {
  try {
    const res = JSON.parse(await window.bridge.get_tasks())
    if (res.status === 'success') {
      // 保存当前展开状态
      const expandedTasks = new Set(
        tasks.value
          .filter(task => task.showDetail)
          .map(task => task.id)
      )
      
      // 更新任务列表，保持展开状态
      tasks.value = res.data.map(task => ({
        ...task,
        showDetail: expandedTasks.has(task.id) || false
      }))
    } else {
      throw new Error(res.message || '获取任务列表失败')
    }
  } catch (error) {
    console.error('刷新任务列表失败:', error)
    ElMessage.error('刷新任务列表失败: ' + error.message)
  }
}

// 监听任务状态更新
const setupTaskUpdates = () => {
  window.receiveTaskOutput = (taskId, encodedOutput) => {
    try {
      const output = JSON.parse(atob(encodedOutput))
      const task = tasks.value.find(t => t.id === taskId)
      if (task) {
        // 更新日志
        if (!task.outputs) task.outputs = []
        task.outputs.push(output)
        
        // 如果是状态更新，更新任务状态
        if (output.type === 'status') {
          task.status = output.status
          task.progress = output.progress || task.progress
          task.book_info = output.book_info || task.book_info
          task.chapters = output.chapters || task.chapters
          task.error = output.error
        }
      }
    } catch (error) {
      console.error('处理任务更新失败:', error)
    }
  }
}

// 跟踪每个任务的滚动状态
const taskScrollStates = ref(new Map())

// 检查用户是否在日志底部附近（容差20px）
const isNearBottom = (element) => {
  return element.scrollHeight - element.scrollTop - element.clientHeight < 20
}

// 监听任务输出变化
watch(tasks, (newTasks) => {
  newTasks.forEach(task => {
    if (task.showDetail && task.outputs?.length) {
      nextTick(() => {
        const taskCard = document.querySelector(`[data-task-id="${task.id}"]`)
        if (taskCard) {
          const logContent = taskCard.querySelector('.log-content')
          if (logContent) {
            // 获取当前任务的滚动状态
            const scrollState = taskScrollStates.value.get(task.id)

            // 如果是首次展开或用户在底部附近，才自动滚动到底部
            if (!scrollState || scrollState.autoScroll) {
              logContent.scrollTop = logContent.scrollHeight
            }
          }
        }
      })
    }
  })
}, { deep: true })

// 添加自动刷新定时器
const refreshTimer = ref(null)

// 启动自动刷新
const startAutoRefresh = () => {
  // 每3秒刷新一次任务列表
  refreshTimer.value = setInterval(refreshTasks, 3000)
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 修改 onMounted 钩子，确保组件初始化时加载所有数据
onMounted(async () => {
  try {

    // 确保规则数据已加载
    await novelRulesStore.loadRules()
    setupTaskUpdates()
    await refreshTasks()
    startAutoRefresh()
    ElMessage({
      message: `规则加载成功`,
      type: 'info',
      duration: 1000
    })
    
  } catch (error) {
        ElMessage({
      message: `规则加载失败`,
      type: 'error',
      duration: 3000
    })
    
  }
})

// 清理滚动监听器
const cleanupScrollListeners = () => {
  taskScrollStates.value.forEach((_, taskId) => {
    const taskCard = document.querySelector(`[data-task-id="${taskId}"]`)
    if (taskCard) {
      const logContent = taskCard.querySelector('.log-content')
      if (logContent && logContent._scrollHandler) {
        logContent.removeEventListener('scroll', logContent._scrollHandler)
        delete logContent._scrollHandler
      }
    }
  })
  taskScrollStates.value.clear()
}

// 添加 onUnmounted 钩子
onUnmounted(() => {
  stopAutoRefresh() // 组件卸载时停止自动刷新
  cleanupScrollListeners() // 清理滚动监听器
})

// 设置日志滚动监听器
const setupLogScrollListener = (taskId) => {
  nextTick(() => {
    const taskCard = document.querySelector(`[data-task-id="${taskId}"]`)
    if (taskCard) {
      const logContent = taskCard.querySelector('.log-content')
      if (logContent) {
        // 移除之前的监听器
        logContent.removeEventListener('scroll', logContent._scrollHandler)

        // 创建新的滚动处理器
        const scrollHandler = () => {
          const isAtBottom = isNearBottom(logContent)
          taskScrollStates.value.set(taskId, { autoScroll: isAtBottom })
        }

        // 保存处理器引用以便后续移除
        logContent._scrollHandler = scrollHandler
        logContent.addEventListener('scroll', scrollHandler)

        // 初始化滚动状态为自动滚动
        taskScrollStates.value.set(taskId, { autoScroll: true })
        logContent.scrollTop = logContent.scrollHeight
      }
    }
  })
}

// 手动滚动到底部
const scrollToBottom = (taskId) => {
  const taskCard = document.querySelector(`[data-task-id="${taskId}"]`)
  if (taskCard) {
    const logContent = taskCard.querySelector('.log-content')
    if (logContent) {
      logContent.scrollTop = logContent.scrollHeight
      // 重新启用自动滚动
      taskScrollStates.value.set(taskId, { autoScroll: true })
    }
  }
}

// 修改切换任务详情的逻辑
const toggleTaskDetail = (task) => {
  // 先关闭其他展开的任务
  tasks.value.forEach(t => {
    if (t.id !== task.id) {
      t.showDetail = false
    }
  })
  // 切换当前任务的展开状态
  task.showDetail = !task.showDetail

  // 如果是展开状态，设置滚动监听器
  if (task.showDetail) {
    setupLogScrollListener(task.id)
  }
}

// 选择目录
const selectDirectory = async (type) => {
  try {
    const result = await window.pywebview.api.select_directory()
    const response = typeof result === 'string' ? JSON.parse(result) : result

    const path = response?.data
    if (path) {
      if (type === 'downloadPath') {
        downloadForm.value.downloadPath = path
        // 保存到配置
        await configStore.updateConfigItem('chrome.downloadDir', path)
      }
    }
  } catch (error) {
    ElMessage.error('选择目录失败: ' + error.message)
  }
}

const handleChromeConfigClick = (row) => {
  downloadForm.value.chromeConfigId = row.id
}

// 获取URL数量
const getUrlCount = () => {
  if (!downloadForm.value.url) return 0
  return downloadForm.value.url.split('\n')
    .filter(url => url.trim())
    .length
}

// 格式化URLs
const formatUrls = () => {
  if (!downloadForm.value.url) return
  
  const urls = downloadForm.value.url.split('\n')
    .map(url => url.trim())
    .filter(url => url)
    .sort()
    .join('\n')
  
  downloadForm.value.url = urls
  ElMessage.success('URL格式已整理')
}

// 清空URLs
const clearUrls = () => {
  ElMessageBox.confirm(
    '确定要清空所有URL吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      downloadForm.value.url = ''
      ElMessage.success('URL已清空')
    })
    .catch(() => {})
}

// 添加 activeTab 状态
const activeTab = ref('settings')

// 监听标签页切换，自动刷新数据
watch(activeTab, async (newTab) => {
  if (newTab === 'tasks') {
    await refreshTasks()
  } else if (newTab === 'settings') {
    await novelRulesStore.loadRules()
  }
}, { immediate: false })

// 添加打开下载目录函数
const openDownloadFolder = async (path) => {
  try {
    await window.pywebview.api.open_directory(path)
    ElMessage.success('已打开下载目录')
  } catch (error) {
    ElMessage.error('打开目录失败: ' + error.message)
  }
}
</script>

<style lang="scss" scoped>
.novel-download {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color-page);
  overflow: hidden;
  user-select: none; // 防止不必要的文本选择

  .download-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--el-bg-color);
    border-radius: 8px;
    box-shadow: var(--el-box-shadow-light);
    overflow: hidden;
    min-height: 0;

    :deep(.el-tabs__header) {
      margin: 0;
      padding: 0;
      background: var(--el-bg-color);
      border-bottom: 1px solid var(--el-border-color-light);
      flex-shrink: 0;

      .el-tabs__nav-wrap {
        padding: 0 20px;
        &::after { display: none; }
      }

      .el-tabs__item {
        height: 48px;
        line-height: 48px;
        font-size: 14px;
        padding: 0 20px;
        user-select: none; // 标签页不允许选择

        &.is-active {
          font-weight: 500;
        }
      }
    }

    :deep(.el-tabs__content) {
      flex: 1;
      overflow: hidden;
      min-height: 0;
      padding: 20px;

      .el-tab-pane {
        height: 100%;
        overflow: hidden;
      }
    }
  }

  .settings-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 0;

    .settings-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
      background: var(--el-bg-color);
      border-radius: 8px;
      overflow: hidden;
    }
  }

  .create-task-form {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
    overflow: hidden;
    min-height: 0;

    .main-content {
      display: flex;
      flex-direction: column;
      gap: 20px;
      height: 100%;
      min-height: 0;

      // 上方区域：URL输入和下载设置
      .top-section {
        display: flex;
        gap: 20px;
        height: 320px;
        flex-shrink: 0;

        // URL输入区域
        .url-input-section {
          flex: 3;
          display: flex;
          flex-direction: column;
          background: var(--el-bg-color);
          border: 1px solid var(--el-border-color-light);
          border-radius: 8px;
          overflow: hidden;

          .section-header {
            padding: 16px;
            border-bottom: 1px solid var(--el-border-color-light);
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
            
            .el-icon {
              font-size: 16px;
              color: var(--el-text-color-secondary);
            }
            
            .title {
              font-size: 14px;
              font-weight: 500;
              color: var(--el-text-color-primary);
              user-select: none; // 标题不允许选择
            }
          }

          .url-input-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 16px;
            gap: 12px;
            min-height: 0;

            .el-textarea {
              flex: 1;
              min-height: 0;

              .el-textarea__inner {
                height: 100% !important;
                font-size: 13px;
                line-height: 1.6;
                resize: none;
                user-select: text; // 允许在输入框中选择文本
              }
            }

            .url-input-footer {
              flex-shrink: 0;
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0 4px;

              .url-count {
                font-size: 13px;
                color: var(--el-text-color-secondary);
                user-select: none; // 计数文本不允许选择
              }

              .url-actions {
                display: flex;
                gap: 16px;

                .el-button {
                  user-select: none; // 操作按钮不允许选择
                }
              }
            }
          }
        }

        // 下载设置区域
        .download-settings {
          flex: 2;
          background: var(--el-bg-color);
          border: 1px solid var(--el-border-color-light);
          border-radius: 8px;
          padding: 16px;
          display: flex;
          flex-direction: column;
          gap: 16px;

          .section-header {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
            
            .el-icon {
              font-size: 16px;
              color: var(--el-text-color-secondary);
            }
            
            .title {
              font-size: 14px;
              font-weight: 500;
              color: var(--el-text-color-primary);
              flex: 1;
            }

            .manage-rules-btn {
              padding: 4px 0;
              font-size: 13px;
              user-select: none; // 管理按钮不允许选择

              .el-icon {
                font-size: 14px;
                margin-right: 4px;
              }
            }
          }

          .settings-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow-y: auto;

            .el-form-item {
              margin-bottom: 16px;

              &:last-child {
                margin-bottom: 0;
              }

              :deep(.el-form-item__label) {
                font-size: 13px;
                padding-right: 8px;
              }

              .path-input-group {
                display: flex;
                gap: 8px;

                .el-input {
                  user-select: text; // 允许在路径输入框中选择文本
                }

                .el-button {
                  padding: 0 12px;
                  user-select: none; // 按钮不允许选择
                }
              }
            }

            .download-params {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 12px;

              .el-form-item {
                margin-bottom: 0;
              }
            }
          }

          .form-actions {
            margin-top: auto;
            padding-top: 16px;
            border-top: 1px solid var(--el-border-color-light);
            flex-shrink: 0;

            .el-button {
              width: 100%;
              height: 36px;
              justify-content: center;
              user-select: none; // 按钮不允许选择
            }
          }
        }
      }

      // Chrome配置区域
      .chrome-config-section {
        flex: 1;
        min-height: 0;
        background: var(--el-bg-color);
        border: 1px solid var(--el-border-color-light);
        border-radius: 8px;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .section-header {
          padding: 16px;
          border-bottom: 1px solid var(--el-border-color-light);
          display: flex;
          align-items: center;
          gap: 8px;
          flex-shrink: 0;
          
          .el-icon {
            font-size: 16px;
            color: var(--el-text-color-secondary);
          }
          
          .title {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
          }
        }

        .chrome-config-list {
          flex: 1;
          min-height: 0;
          overflow: hidden;

          :deep(.el-table) {
            height: 100%;

            .el-table__inner-wrapper {
              height: 100%;
            }

            .el-table__header-wrapper {
              flex-shrink: 0;
            }

            .el-table__body-wrapper {
              height: calc(100% - 40px) !important;
              overflow-y: auto !important;
            }

            .el-table__header th {
              background: var(--el-fill-color-light);
              font-size: 13px;
              padding: 8px 0;
              height: 40px;
            }

            .el-table__row {
              cursor: pointer;
              user-select: none; // 表格行不允许选择

              td {
                padding: 6px 0;
                height: 40px;
              }

              &:hover {
                background-color: var(--el-fill-color-light);
              }
            }
          }
        }
      }
    }
  }

  // 任务管理区域样式
  .task-management {
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 0;

    .section-header {
      padding: 16px 20px;
      border-bottom: 1px solid var(--el-border-color-light);
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0;
      background: var(--el-bg-color);

      .section-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        user-select: none; // 章节标题不允许选择
      }
    }

    .task-list {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      min-height: 0;

      .task-card {
        margin-bottom: 16px;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;

        &:last-child {
          margin-bottom: 0;
        }

        .task-header {
          padding: 16px;
          cursor: pointer;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: var(--el-bg-color);
          border-bottom: 1px solid var(--el-border-color-light);
          user-select: none; // 任务头部不允许选择

          &:hover {
            background: var(--el-fill-color-light);
          }

          .task-info {
            flex: 1;
            margin-right: 16px;

            .task-title {
              margin-bottom: 8px;
              display: flex;
              align-items: center;
              gap: 12px;

              .time {
                font-size: 14px;
                color: var(--el-text-color-regular);
              }

              .rule {
                font-size: 14px;
                font-weight: 500;
                color: var(--el-text-color-primary);
              }
            }

            .task-progress {
              width: 100%;
            }
          }

          .task-status {
            display: flex;
            align-items: center;
            gap: 12px;

            .expand-icon {
              font-size: 16px;
              color: var(--el-text-color-secondary);
              transition: transform 0.3s ease;

              &.is-active {
                transform: rotate(180deg);
              }
            }
          }
        }

        .task-detail {
          background: var(--el-fill-color-blank);
          
          .detail-section {
            padding: 16px;
            border-bottom: 1px solid var(--el-border-color-light);

            &:last-child {
              border-bottom: none;
            }

            .section-title {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;
              font-size: 14px;
              font-weight: 500;
              color: var(--el-text-color-primary);

              .log-actions {
                display: flex;
                gap: 8px;

                .el-button {
                  user-select: none; // 日志操作按钮不允许选择

                  &:first-child {
                    // 跳转到底部按钮的特殊样式
                    .el-icon {
                      font-size: 12px;
                    }
                  }
                }
              }
            }

            .info-grid {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 12px;

              .info-item {
                display: flex;
                align-items: center;

                .label {
                  font-size: 13px;
                  color: var(--el-text-color-secondary);
                  margin-right: 8px;
                  white-space: nowrap;
                }

                .value {
                  font-size: 13px;
                  color: var(--el-text-color-primary);
                }
              }
            }

            .log-content {
              background: var(--el-fill-color-light);
              border-radius: 4px;
              padding: 12px;
              height: 300px;
              overflow-y: auto;
              overflow-x: hidden;
              user-select: text; // 日志内容允许选择复制


              .log-line {
                display: flex;
                align-items: flex-start;
                gap: 8px;
                padding: 4px 0;
                font-size: 13px;
                line-height: 1.5;

                .message {
                  flex: 1;
                  word-break: break-all;
                  white-space: pre-wrap;
                }

                &.error .message {
                  color: var(--el-color-danger);
                }

                &.success .message {
                  color: var(--el-color-success);
                }

                &.warning .message {
                  color: var(--el-color-warning);
                }
              }

              .no-logs {
                text-align: center;
                color: var(--el-text-color-secondary);
                font-size: 13px;
                padding: 20px 0;
                user-select: none; // 提示文本不允许选择
              }

              &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
              }

              &::-webkit-scrollbar-thumb {
                background: var(--el-border-color);
                border-radius: 3px;
              }

              &::-webkit-scrollbar-track {
                background: transparent;
              }
            }
          }
        }
      }
    }
  }

  // 规则管理弹窗样式
  :deep(.rule-dialog) {
    display: flex;
    flex-direction: column;
    max-height: 80vh;

    .el-dialog__body {
      flex: 1;
      overflow: hidden;
      padding: 24px;

      .table-container {
        height: 100%;
        
        .el-table {
          // 表头样式
          .el-table__header {
            th {
              background-color: var(--el-fill-color-light);
              font-size: 15px;
              font-weight: 600;
              padding: 12px 0;
              height: 48px;
              user-select: none; // 表头不允许选择
            }
          }

          // 表格内容
          .el-table__body {
            td {
              font-size: 14px;
              padding: 12px 0;
              height: 48px;
              user-select: none; // 表格内容不允许选择（除了特定区域）
            }
          }
        }
      }
    }
  }

  // 规则编辑弹窗样式
  :deep(.edit-rule-dialog) {
    .el-dialog__header {
      padding: 20px 24px;
      margin-right: 0;
      border-bottom: 1px solid var(--el-border-color-light);

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
      }
    }

    .el-dialog__body {
      padding: 24px;

      .el-form {
        .el-form-item {
          margin-bottom: 20px;

          .el-form-item__label {
            font-size: 15px;
            font-weight: 500;
            padding-right: 12px;
          }

          .el-input {
            .el-input__wrapper {
              font-size: 14px;
              padding: 8px 12px;
            }

            .el-input__inner {
              user-select: text; // 输入框内容允许选择
            }
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .el-dialog__footer {
      padding: 16px 24px;
      border-top: 1px solid var(--el-border-color-light);

      .dialog-footer {
        .el-button {
          font-size: 14px;
          padding: 8px 20px;
          height: 36px;
          user-select: none; // 对话框按钮不允许选择
        }
      }
    }
  }

  // 对话框公共样式
  :deep(.el-dialog) {
    margin: 0 auto !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    user-select: none; // 对话框整体不允许选择

    .el-dialog__header {
      user-select: none; // 对话框头部不允许选择

      .el-dialog__title {
        user-select: none; // 对话框标题不允许选择
      }
    }

    .el-dialog__body {
      overflow-y: auto;
    }
  }

  // 全局按钮样式优化
  :deep(.el-button) {
    user-select: none; // 所有按钮不允许选择
    transition: all 0.2s ease; // 添加平滑过渡效果

    &:active {
      transform: translateY(1px); // 按下时轻微下移，模拟原生按钮效果
    }
  }

  // 全局标签样式优化
  :deep(.el-tag) {
    user-select: none; // 标签不允许选择
  }

  // 全局图标样式优化
  :deep(.el-icon) {
    user-select: none; // 图标不允许选择
  }

  // 全局进度条样式优化
  :deep(.el-progress) {
    user-select: none; // 进度条不允许选择
  }

  // 全局单选框样式优化
  :deep(.el-radio) {
    user-select: none; // 单选框不允许选择
  }

  // 全局选择器样式优化
  :deep(.el-select) {
    .el-select__wrapper {
      user-select: none; // 选择器不允许选择
    }
  }

  // 全局数字输入框样式优化
  :deep(.el-input-number) {
    user-select: none; // 数字输入框控制按钮不允许选择

    .el-input__inner {
      user-select: text; // 但输入框内容允许选择
    }
  }

  // 全局禁用拼写检查样式，模拟原生应用
  :deep(.el-input__inner),
  :deep(.el-textarea__inner) {
    // 移除浏览器默认的拼写检查红色波浪线
    &::-webkit-input-placeholder {
      -webkit-text-security: none;
    }

    // 禁用浏览器的自动填充样式
    &:-webkit-autofill,
    &:-webkit-autofill:hover,
    &:-webkit-autofill:focus {
      -webkit-box-shadow: 0 0 0 1000px var(--el-fill-color-blank) inset !important;
      -webkit-text-fill-color: var(--el-text-color-primary) !important;
      transition: background-color 5000s ease-in-out 0s;
    }
  }
}
</style>
