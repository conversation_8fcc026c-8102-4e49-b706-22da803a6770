<template>
  <div class="application-container">
    <el-tabs
      v-model="activeTab"
      class="application-tabs"
      v-loading="shouldShowLoading()"
      element-loading-text="加载配置中..."
      @tab-change="handleTabChange"
    >
      <!-- Chrome设置 -->
      <el-tab-pane label="Chrome设置" name="chrome">
        <ChromeSettings v-if="shouldRenderTab('chrome')" />
      </el-tab-pane>

      <!-- AI角色管理 -->
      <el-tab-pane label="AI角色" name="ai-roles">
        <AIRoleManager v-if="shouldRenderTab('ai-roles')" />
      </el-tab-pane>

      <!-- AI服务商配置 -->
      <el-tab-pane label="AI服务商" name="ai-providers">
        <AIProviderConfig v-if="shouldRenderTab('ai-providers')" />
      </el-tab-pane>

      <!-- 飞书配置 -->
      <el-tab-pane label="飞书配置" name="feishu">
        <FeishuConfig v-if="shouldRenderTab('feishu')" />
      </el-tab-pane>

      <!-- 聊天设置 -->
      <el-tab-pane label="聊天设置" name="chat">
        <ChatSettings v-if="shouldRenderTab('chat')" />
      </el-tab-pane>

      <!-- 备份设置 -->
      <el-tab-pane label="备份设置" name="backup">
        <BackupSettings v-if="shouldRenderTab('backup')" />
      </el-tab-pane>

      <!-- Git备份 -->
      <el-tab-pane label="Git备份" name="git">
        <GitBackup v-if="shouldRenderTab('git')" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, provide, nextTick, defineAsyncComponent, h } from 'vue'
import { useConfigStore } from '@/stores/config'
import { useAIRolesStore } from '@/stores/aiRoles'
import { useAIProvidersStore } from '@/stores/aiProviders'
import { ElMessage } from 'element-plus'

// 开发环境下导入API测试工具
if (process.env.NODE_ENV === 'development') {
  import('@/utils/api-test.js')
}

// 创建加载组件 - 使用渲染函数避免运行时编译
const LoadingComponent = {
  render() {
    return h('div', { class: 'tab-loading' }, [
      h('el-skeleton', { rows: 5, animated: true })
    ])
  }
}

// 创建错误组件 - 使用渲染函数避免运行时编译
const ErrorComponent = {
  emits: ['retry'],
  render() {
    return h('div', { class: 'tab-error' }, [
      h('el-result', {
        icon: 'error',
        title: '加载失败',
        'sub-title': '组件加载出现错误，请刷新页面重试'
      }, {
        extra: () => h('el-button', {
          type: 'primary',
          onClick: () => this.$emit('retry')
        }, '重试')
      })
    ])
  }
}

// 动态导入子组件 - 实现真正的懒加载
const ChromeSettings = defineAsyncComponent({
  loader: () => import('./components/ChromeSettings.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 10000
})

const AIRoleManager = defineAsyncComponent({
  loader: () => import('./components/AIRoleManager.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 10000
})

const AIProviderConfig = defineAsyncComponent({
  loader: () => import('./components/AIProviderConfig.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 10000
})

const FeishuConfig = defineAsyncComponent({
  loader: () => import('./components/FeishuConfig.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 10000
})

const ChatSettings = defineAsyncComponent({
  loader: () => import('./components/ChatSettings.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 10000
})

const BackupSettings = defineAsyncComponent({
  loader: () => import('./components/BackupSettings.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 10000
})

const GitBackup = defineAsyncComponent({
  loader: () => import('./components/GitBackup.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 10000
})

// 响应式数据
const activeTab = ref('chrome')
const isLoading = ref(false)

// 已加载的组件缓存，避免重复初始化
const loadedTabs = ref(new Set(['chrome'])) // 默认加载第一个tab

// 检查是否需要显示加载状态
const shouldShowLoading = () => {
  // 只有在配置未加载且正在加载时才显示loading
  return !configStore.loaded && configStore.isLoading
}

// 检查组件是否应该被渲染
const shouldRenderTab = (tabName) => {
  return loadedTabs.value.has(tabName)
}

// 监听tab切换，实现懒加载
const handleTabChange = (tabName) => {
  console.log(`切换到 ${tabName} tab`)
  if (!loadedTabs.value.has(tabName)) {
    console.log(`首次加载 ${tabName} 组件`)
    loadedTabs.value.add(tabName)
  }
}

// 初始化stores
const configStore = useConfigStore()
const aiRolesStore = useAIRolesStore()
const aiProvidersStore = useAIProvidersStore()

// 生命周期钩子
onMounted(async () => {
  console.log('Application 组件挂载，检查配置状态...')

  try {
    // 确保配置已加载（如果main.js中已加载，这里会直接返回）
    if (!configStore.loaded) {
      console.log('配置未加载，触发加载...')
      isLoading.value = true
      await configStore.loadConfig()
    } else {
      console.log('配置已加载，直接使用')
    }

    // 后台异步加载其他非关键配置，不阻塞界面
    const backgroundTasks = []

    if (!aiRolesStore.initialized) {
      backgroundTasks.push(
        aiRolesStore.loadRoles().catch(error => {
          console.warn('AI角色加载失败:', error)
        })
      )
    }

    if (!aiProvidersStore.initialized) {
      backgroundTasks.push(
        aiProvidersStore.loadProviders().catch(error => {
          console.warn('AI提供商加载失败:', error)
        })
      )
    }

    // 后台加载，不等待完成
    if (backgroundTasks.length > 0) {
      Promise.all(backgroundTasks).then(() => {
        console.log('后台配置加载完成')
      }).catch(error => {
        console.warn('部分后台配置加载失败:', error)
      })
    }

  } catch (error) {
    console.error('加载配置失败:', error)
    ElMessage.error('配置加载失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
})

onUnmounted(() => {
  // 清理定时器等资源
})

// 提供给子组件的方法
const showLoading = (text = '处理中...') => {
  isLoading.value = true
}

const hideLoading = () => {
  isLoading.value = false
}

// 向子组件提供全局方法
provide('showLoading', showLoading)
provide('hideLoading', hideLoading)
provide('configStore', configStore)
provide('aiRolesStore', aiRolesStore)
provide('aiProvidersStore', aiProvidersStore)

// 原生应用行为增强
onMounted(() => {
  // 禁用右键菜单 (在某些区域)
  const disableContextMenu = (e) => {
    // 允许在输入框中使用右键菜单
    if (e.target.tagName === 'INPUT' ||
        e.target.tagName === 'TEXTAREA' ||
        e.target.contentEditable === 'true' ||
        e.target.closest('.el-input') ||
        e.target.closest('.el-textarea') ||
        e.target.closest('.code-block') ||
        e.target.closest('pre') ||
        e.target.closest('code')) {
      return true
    }

    // 其他区域禁用右键菜单
    e.preventDefault()
    return false
  }

  // 禁用拖拽到外部应用
  const preventDragOut = (e) => {
    // 允许在输入框内的文本拖拽
    if (e.target.tagName === 'INPUT' ||
        e.target.tagName === 'TEXTAREA' ||
        e.target.closest('.el-input') ||
        e.target.closest('.el-textarea')) {
      return true
    }

    e.preventDefault()
    return false
  }

  // 禁用F12开发者工具 (可选)
  const preventDevTools = (e) => {
    if (e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
        (e.ctrlKey && e.shiftKey && e.key === 'C') ||
        (e.ctrlKey && e.key === 'U')) {
      e.preventDefault()
      return false
    }
  }

  // 绑定事件监听器
  document.addEventListener('contextmenu', disableContextMenu)
  document.addEventListener('dragstart', preventDragOut)
  document.addEventListener('keydown', preventDevTools)

  // 组件卸载时清理事件监听器
  onUnmounted(() => {
    document.removeEventListener('contextmenu', disableContextMenu)
    document.removeEventListener('dragstart', preventDragOut)
    document.removeEventListener('keydown', preventDevTools)
  })
})
</script>

<style lang="scss" scoped>
/* 原生应用样式 - 禁用Web行为 */
* {
  /* 禁用文本选择 - 模拟原生应用 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 禁用拖拽 */
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;

  /* 禁用右键菜单 */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* 允许输入框和文本域的文本选择 */
:deep(.el-input__inner),
:deep(.el-textarea__inner),
:deep(.el-input-number__input),
:deep(input),
:deep(textarea) {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
}

/* 允许代码块和预格式化文本的选择 */
:deep(pre),
:deep(code),
:deep(.code-block),
:deep(.el-code-editor) {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
}

/* WebView2 和 WebKit 兼容性 */
.application-container {
  height: 100%;
  width: 100%;
  min-width: 0; /* 防止flex子元素收缩问题 */
  display: flex;
  flex-direction: column;
  overflow: hidden;

  /* WebView2 优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* 禁用默认的滚动条样式，使用自定义样式 */
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color) transparent;

  /* WebKit 滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color);
    border-radius: 4px;

    &:hover {
      background-color: var(--el-border-color-dark);
    }
  }

  /* 调试边框 - 可以临时看到容器边界 */
  // border: 2px solid red;
}

/* 标签页样式 - 针对AppLayout内容区域优化 */
.application-tabs {
  height: 100%;
  width: 100%;
  min-width: 0;
  display: flex;
  flex-direction: column;

  /* 调试边框 - 可以临时看到标签页容器边界 */
  // border: 2px solid blue;

  :deep(.el-tabs__header) {
    margin: 0 0 16px 0;
    padding: 0;
    border-bottom: 1px solid var(--el-border-color-light);
    flex-shrink: 0;
    width: 100%;
    min-width: 0;

    /* 调试边框 - 可以临时看到标签页头部边界 */
    // border: 2px solid green;

    .el-tabs__nav-wrap {
      width: 100%;
      min-width: 0;
      overflow: visible;

      &::after {
        display: none;
      }

      .el-tabs__nav-scroll {
        overflow: visible;
        width: 100%;
        min-width: 0;

        .el-tabs__nav {
          width: 100%;
          min-width: 0;
          display: flex;
          flex-wrap: nowrap;
          transform: none;
        }
      }
    }

    .el-tabs__item {
      font-size: 14px;
      padding: 0 16px;
      height: 40px;
      line-height: 40px;
      transition: all 0.3s ease;
      color: var(--el-text-color-regular);
      flex-shrink: 0;
      flex-grow: 0;
      white-space: nowrap;
      border-radius: 6px 6px 0 0;
      margin-right: 2px;
      min-width: auto;
      max-width: none;
      width: auto;



      &.is-active {
        font-weight: 600;
        color: var(--el-color-primary);
        background-color: var(--el-bg-color);
        border-bottom: 2px solid var(--el-color-primary);
      }

      &:hover {
        color: var(--el-color-primary);
        background-color: var(--el-fill-color-light);
      }
    }
  }

  :deep(.el-tabs__content) {
    flex: 1;
    overflow: hidden;
    padding: 0;

    .el-tab-pane {
      height: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      /* 确保每个组件都能正确占用空间 */
      > * {
        flex: 1;
        overflow: hidden;
      }
    }
  }
}

/* 原生应用行为增强 */
.application-container {
  /* 禁用图片拖拽 */
  img {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    pointer-events: none;
  }

  /* 禁用链接拖拽 */
  a {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
  }

  /* 标题和标签不可选择 */
  h1, h2, h3, h4, h5, h6,
  .el-tag,
  .el-badge,
  .el-button,
  .el-switch,
  .section-title,
  .page-title,
  .provider-name,
  .subsection-header h3 {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
  }

  /* 表格头部不可选择 */
  :deep(.el-table__header-wrapper),
  :deep(.el-table__header),
  :deep(.el-table-column--selection),
  :deep(.el-table__empty-text) {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
  }

  /* 按钮和控件不可选择 */
  :deep(.el-button),
  :deep(.el-switch),
  :deep(.el-checkbox),
  :deep(.el-radio),
  :deep(.el-select),
  :deep(.el-dropdown),
  :deep(.el-pagination),
  :deep(.el-tabs__item) {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
  }
}

/* WebView2 特定优化 - 使用新的 forced-colors 媒体查询 */
@media (forced-colors: active) {
  .application-container {
    /* 强制颜色模式下的字体渲染优化 */
    text-rendering: optimizeLegibility;
    -webkit-font-feature-settings: "liga", "kern";
    font-feature-settings: "liga", "kern";
  }
}

/* 向后兼容：支持旧版本浏览器的 WebView2 优化 */
@supports not (forced-colors: active) {
  @media screen and (prefers-contrast: high) {
    .application-container {
      /* 高对比度模式下的字体渲染优化 */
      text-rendering: optimizeLegibility;
      -webkit-font-feature-settings: "liga", "kern";
      font-feature-settings: "liga", "kern";
    }
  }
}

/* WebKit (macOS) 特定优化 */
@supports (-webkit-backdrop-filter: blur(10px)) {
  .application-container {
    /* macOS WebKit 优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* 优化滚动性能 */
    -webkit-overflow-scrolling: touch;
  }

  /* macOS 风格的滚动条 */
  :deep(*::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
  }

  :deep(*::-webkit-scrollbar-track) {
    background: transparent;
    border-radius: 4px;
  }

  :deep(*::-webkit-scrollbar-thumb) {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    border: 1px solid transparent;
    background-clip: content-box;

    &:hover {
      background-color: rgba(0, 0, 0, 0.3);
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.4);
    }
  }
}

/* 暗色模式下的滚动条 */
@media (prefers-color-scheme: dark) {
  :deep(*::-webkit-scrollbar-thumb) {
    background-color: rgba(255, 255, 255, 0.2);

    &:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }

    &:active {
      background-color: rgba(255, 255, 255, 0.4);
    }
  }
}

/* 应用容器样式 - 适配AppLayout内容区域 */
.application-container {
  height: 100%;
  width: 100%;
  min-width: 0; /* 防止flex子元素收缩问题 */
  display: flex;
  flex-direction: column;
  overflow: hidden;

  /* 调试边框 - 可以临时看到容器边界 */
  // border: 2px solid red;
}

/* Tab 加载状态样式 */
.tab-loading {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;

  :deep(.el-skeleton) {
    width: 100%;
  }

  :deep(.el-skeleton__item) {
    background: var(--el-fill-color-lighter);
    border-radius: 6px;
  }
}

/* Tab 错误状态样式 */
.tab-error {
  padding: 20px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  :deep(.el-result) {
    padding: 0;
  }
}
</style>

<style lang="scss">
/* 全局样式修复 - 确保标签页在AppLayout中正确显示 */
.application-tabs {
  .el-tabs__header {
    width: 100% !important;
    overflow: visible !important;

    .el-tabs__nav-wrap {
      width: 100% !important;
      overflow: visible !important;

      .el-tabs__nav-scroll {
        overflow: visible !important;
        width: 100% !important;

        .el-tabs__nav {
          width: 100% !important;
          transform: none !important;
          display: flex !important;
          flex-wrap: nowrap !important;
        }
      }
    }

    .el-tabs__item {
      flex-shrink: 0 !important;
      white-space: nowrap !important;
      overflow: visible !important;
    }
  }
}
</style>
