<template>
  <div class="ai-provider-management">
    <div class="content-wrapper">
      <!-- 顶部操作栏 -->
      <div class="action-bar">
        <div class="action-left">
          <h2 class="page-title">AI 服务商管理</h2>
          <p class="page-description">配置和管理 AI 模型服务提供商</p>
        </div>
        <div class="action-right">
          <el-button
            type="info"
            @click="refreshProviders"
            :loading="aiProvidersStore.isLoading"
            :icon="Refresh"
          >
            刷新
          </el-button>
          <el-button
            type="success"
            @click="saveAllProvidersConfig"
            :loading="aiProvidersStore.isLoading"
          >
            保存所有配置并刷新模型
          </el-button>
          <el-button
            type="primary"
            @click="addProvider"
            :icon="Plus"
          >
            添加服务商
          </el-button>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
      <div v-if="aiProvidersStore.providers.length === 0" class="empty-providers">
        <el-empty description="暂无配置的服务商" />
        <el-button type="primary" @click="addProvider" class="mt-3">添加服务商</el-button>
      </div>

      <div v-else class="provider-list">
        <el-collapse v-model="aiActiveProviders">
          <el-collapse-item
            v-for="(provider, index) in aiProvidersStore.providers"
            :key="provider.id"
            :name="provider.id"
          >
            <template #title>
              <div class="provider-header">
                <span class="provider-name">{{ provider.name }}</span>
              </div>
            </template>

            <div class="provider-content">
              <el-form label-width="120px" size="default">

                <el-form-item label="服务商名称">
                  <el-input v-model="provider.name" placeholder="例如: OpenAI, Claude, 智谱AI等"></el-input>
                </el-form-item>

                <el-form-item label="API基础URL">
                  <el-input v-model="provider.baseUrl" placeholder="例如: https://api.openai.com/v1"></el-input>
                </el-form-item>

                <el-form-item class="providers-info">

                </el-form-item>

                <!-- 模型管理 -->
                <div class="subsection">
                  <div class="subsection-header">
                    <h3>模型管理</h3>
                    <div>
                      <el-button
                        type="primary"
                        size="small"
                        @click="addModel(provider.id)"
                      >
                        <el-icon><Plus /></el-icon>
                        添加模型
                      </el-button>
                      <el-button
                        type="success"
                        size="small"
                        @click="fetchModels(provider.id)"
                      >
                        <el-icon><Refresh /></el-icon>
                        获取可用模型
                      </el-button>
                    </div>
                  </div>

                  <div class="models-list">
                    <el-table :data="provider.models" style="width: 100%" border>
                      <el-table-column label="模型ID" min-width="180">
                        <template #default="{ row }">
                          <el-input
                            v-model="row.id"
                            placeholder="模型ID"
                            size="small"
                            @blur="validateModelId(provider.id, row)"
                          />
                        </template>
                      </el-table-column>
                      <el-table-column label="显示别名" min-width="150">
                        <template #default="{ row }">
                          <el-input
                            v-model="row.name"
                            placeholder="显示别名"
                            size="small"
                          />
                        </template>
                      </el-table-column>
                      <el-table-column label="启用" width="80" align="center">
                        <template #default="{ row }">
                          <el-switch
                            v-model="row.available"
                            @change="handleModelAvailableChange(row)"
                          ></el-switch>
                        </template>
                      </el-table-column>
                      <el-table-column label="参数配置" min-width="120" align="center">
                        <template #default="{ row }">
                          <el-button
                            type="primary"
                            size="small"
                            @click="openModelConfigDialog(provider.id, row)"
                          >
                            配置参数
                          </el-button>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="100" align="center">
                        <template #default="{ $index }">
                          <el-button
                            type="danger"
                            size="small"
                            @click="removeModel(provider.id, $index)"
                          >
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>

                    <div v-if="provider.models.length === 0" class="empty-models">
                      <el-empty description="暂无配置的模型" :image-size="60" />
                      <div class="empty-actions">
                        <el-button
                          type="primary"
                          size="small"
                          @click="addModel(provider.id)"
                          class="mt-2 mr-2"
                        >
                          手动添加
                        </el-button>
                        <el-button
                          type="success"
                          size="small"
                          @click="fetchModels(provider.id)"
                          class="mt-2"
                        >
                          获取可用模型
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- API Keys管理 -->
                <div class="subsection">
                  <div class="subsection-header">
                    <h3>API密钥管理</h3>
                    <el-button
                      type="primary"
                      size="small"
                      @click="addApiKey(provider.id)"
                    >
                      <el-icon><Plus /></el-icon>
                      添加密钥
                    </el-button>
                  </div>

                  <div class="keys-list">
                    <el-table :data="provider.apiKeys" style="width: 100%" border>
                      <el-table-column label="密钥" min-width="200">
                        <template #default="{ row }">
                          <el-input
                            v-model="row.key"
                            placeholder="API密钥"
                            show-password
                          ></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column label="权重" width="100">
                        <template #default="{ row }">
                          <el-input-number
                            v-model="row.weight"
                            :min="1"
                            :max="100"
                            controls-position="right"
                            size="small"
                          ></el-input-number>
                        </template>
                      </el-table-column>
                      <el-table-column label="状态" width="100" align="center">
                        <template #default="{ row }">
                          <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                            {{ row.status === 'active' ? '正常' : '异常' }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="150" align="center">
                        <template #default="{ row, $index }">
                          <el-button-group>
                            <el-button
                              type="primary"
                              size="small"
                              @click="testApiKey(provider.id, row.id)"
                            >
                              测试
                            </el-button>
                            <el-button
                              type="danger"
                              size="small"
                              @click="removeApiKey(provider.id, $index)"
                            >
                              删除
                            </el-button>
                          </el-button-group>
                        </template>
                      </el-table-column>
                    </el-table>

                    <div v-if="provider.apiKeys.length === 0" class="empty-keys">
                      <el-empty description="暂无配置的API密钥" :image-size="60" />
                      <el-button
                        type="primary"
                        size="small"
                        @click="addApiKey(provider.id)"
                        class="mt-2"
                      >
                        添加密钥
                      </el-button>
                    </div>
                  </div>
                </div>

                <!-- 代理配置 -->
                <div class="subsection" v-if="ensureProxyConfig(provider)">
                  <div class="subsection-header">
                    <h3>代理配置</h3>
                    <el-switch
                      v-model="provider.proxy.enabled"
                      active-text="启用代理"
                      inactive-text="禁用代理"
                      @change="handleProxyEnabledChange(provider, $event)"
                    />
                  </div>

                  <div v-if="provider.proxy.enabled" class="proxy-config">
                    <el-form label-width="120px" size="default">
                      <el-form-item label="代理地址">
                        <div class="proxy-url-input">
                          <el-input
                            v-model="provider.proxy.url"
                            placeholder="例如: 127.0.0.1:7890"
                            @focus="handleProxyUrlFocus(provider)"
                            style="flex: 1"
                          >
                            <template #prepend>
                              <el-select v-model="proxyProtocol" style="width: 100px">
                                <el-option label="HTTP" value="http://" />
                                <el-option label="HTTPS" value="https://" />
                                <el-option label="SOCKS5" value="socks5://" />
                              </el-select>
                            </template>
                          </el-input>
                          <el-button
                            type="primary"
                            size="default"
                            @click="detectSystemProxy(provider)"
                            :loading="proxyDetectLoading[provider.id]"
                            style="margin-left: 8px"
                          >
                            检测系统代理
                          </el-button>
                        </div>
                      </el-form-item>

                      <el-form-item label="用户名">
                        <el-input
                          v-model="provider.proxy.username"
                          placeholder="代理用户名（可选）"
                          clearable
                        />
                      </el-form-item>

                      <el-form-item label="密码">
                        <el-input
                          v-model="provider.proxy.password"
                          type="password"
                          placeholder="代理密码（可选）"
                          show-password
                          clearable
                        />
                      </el-form-item>

                      <el-form-item label="超时时间">
                        <el-input-number
                          v-model="provider.proxy.timeout"
                          :min="5"
                          :max="300"
                          :step="5"
                          controls-position="right"
                          style="width: 200px"
                        />
                        <span style="margin-left: 8px; color: var(--el-text-color-regular);">秒</span>
                      </el-form-item>

                      <el-form-item label="SSL验证">
                        <el-switch
                          v-model="provider.proxy.verify_ssl"
                          active-text="验证SSL证书"
                          inactive-text="跳过SSL验证"
                        />
                        <div style="margin-top: 4px; font-size: 12px; color: var(--el-text-color-placeholder);">
                          注意：跳过SSL验证可能存在安全风险
                        </div>
                      </el-form-item>
                    </el-form>

                    <div class="proxy-test">
                      <el-button
                        type="primary"
                        size="small"
                        @click="testProxyConnection(provider.id)"
                        :loading="proxyTestLoading[provider.id]"
                      >
                        测试代理连接
                      </el-button>
                      <span v-if="proxyTestResults[provider.id]"
                            :class="['proxy-test-result', proxyTestResults[provider.id].success ? 'success' : 'error']">
                        {{ proxyTestResults[provider.id].message }}
                      </span>
                    </div>
                  </div>

                  <div v-else class="proxy-disabled-hint">
                    <div class="custom-info-alert">
                      <div class="alert-icon">
                        <el-icon><InfoFilled /></el-icon>
                      </div>
                      <div class="alert-content">
                        <div class="alert-title">代理已禁用</div>
                        <div class="alert-description">
                          启用代理后，所有API请求将通过指定的代理服务器发送。
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="provider-actions mt-4">
                  <el-button-group>
                    <el-button
                      type="danger"
                      @click="removeProvider(index)"
                    >
                      删除服务商
                    </el-button>
                    <el-button
                      type="primary"
                      @click="saveProviderConfig(provider.id)"
                      :loading="aiProvidersStore.isLoading"
                    >
                      保存此服务商
                    </el-button>
                  </el-button-group>
                </div>
              </el-form>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <!-- 模型配置对话框 -->
    <el-dialog
      v-model="modelConfigDialog.visible"
      title="模型参数配置"
      width="500px"
      destroy-on-close
    >
      <div class="model-config-header">
        <h4>{{ modelConfigDialog.modelName }} ({{ modelConfigDialog.modelId }})</h4>
      </div>

      <el-form :model="modelConfigDialog.form" label-width="120px">
        <el-form-item label="温度 (Temperature)">
          <el-slider
            v-model="modelConfigDialog.form.temperature"
            :min="0"
            :max="2"
            :step="0.1"
            show-input
            :show-input-controls="false"
          />
        </el-form-item>

        <el-form-item label="最大令牌数">
          <el-input-number
            v-model="modelConfigDialog.form.max_tokens"
            :min="1"
            :max="32768"
            :step="256"
            controls-position="right"
          />
        </el-form-item>

        <el-form-item label="Top P">
          <el-slider
            v-model="modelConfigDialog.form.top_p"
            :min="0"
            :max="1"
            :step="0.1"
            show-input
            :show-input-controls="false"
          />
        </el-form-item>

        <el-form-item label="频率惩罚">
          <el-slider
            v-model="modelConfigDialog.form.frequency_penalty"
            :min="-2"
            :max="2"
            :step="0.1"
            show-input
            :show-input-controls="false"
          />
        </el-form-item>

        <el-form-item label="存在惩罚">
          <el-slider
            v-model="modelConfigDialog.form.presence_penalty"
            :min="-2"
            :max="2"
            :step="0.1"
            show-input
            :show-input-controls="false"
          />
        </el-form-item>

        <el-form-item label="流式输出">
          <el-switch v-model="modelConfigDialog.form.stream" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="modelConfigDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveModelConfig">保存</el-button>
        </div>
      </template>
    </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAIProvidersStore } from '@/stores/aiProviders'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, InfoFilled } from '@element-plus/icons-vue'

// Store
const aiProvidersStore = useAIProvidersStore()

// 响应式数据
const aiActiveProviders = ref([])

// 代理相关状态
const proxyProtocol = ref('http://')
const proxyTestLoading = ref({})
const proxyTestResults = ref({})
const proxyDetectLoading = ref({})

// 模型配置对话框状态
const modelConfigDialog = ref({
  visible: false,
  providerId: '',
  modelId: '',
  modelName: '',
  form: {
    temperature: 0.8,
    max_tokens: 8192,
    top_p: 0.8,
    frequency_penalty: 0,
    presence_penalty: 0,
    stream: true
  }
})

// 添加新的服务商
const addProvider = () => {
  const newProvider = {
    name: '新服务商',
    baseUrl: '',
    apiKeys: [],
    models: [],
    proxy: {
      enabled: false,
      url: '',
      username: '',
      password: '',
      timeout: 30.0,
      verify_ssl: true
    }
  }

  const provider = aiProvidersStore.addProvider(newProvider)
  aiActiveProviders.value = [provider.id]
}

// 删除服务商
const removeProvider = (index) => {
  ElMessageBox.confirm(
    '确定要删除该服务商配置吗？此操作不可恢复。',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const provider = aiProvidersStore.providers[index]
      if (!provider) {
        ElMessage.error('未找到要删除的服务商')
        return
      }

      // 从内存中删除
      aiProvidersStore.removeProvider(provider.id)

      // 立即保存到后端以持久化删除操作
      await aiProvidersStore.saveProviders()

      ElMessage.success('服务商已删除')
    } catch (error) {
      console.error('删除服务商失败:', error)
      ElMessage.error('删除服务商失败: ' + error.message)
    }
  }).catch(() => {})
}

// API Key 管理
const addApiKey = (providerId) => {
  try {
    aiProvidersStore.addApiKey(providerId)
    ElMessage.success('API密钥已添加，请填写密钥内容并保存配置')
  } catch (error) {
    console.error('添加API密钥失败:', error)
    ElMessage.error('添加API密钥失败: ' + error.message)
  }
}

const removeApiKey = async (providerId, keyIndex) => {
  try {
    aiProvidersStore.removeApiKey(providerId, keyIndex)
    ElMessage.success('API密钥已删除，请记得保存配置')
  } catch (error) {
    console.error('删除API密钥失败:', error)
    ElMessage.error('删除API密钥失败: ' + error.message)
  }
}

const testApiKey = async (providerId, keyId) => {
  await aiProvidersStore.testApiKey(providerId, keyId)
}

// 确保服务商有代理配置
const ensureProxyConfig = (provider) => {
  if (!provider.proxy) {
    provider.proxy = {
      enabled: false,
      url: '',
      username: '',
      password: '',
      timeout: 30.0,
      verify_ssl: true
    }
  }
  return true
}

// 处理代理启用状态变化
const handleProxyEnabledChange = (provider, enabled) => {
  if (enabled) {
    // 启用代理时，如果地址为空，设置默认值
    if (!provider.proxy.url || provider.proxy.url.trim() === '') {
      provider.proxy.url = '127.0.0.1:7890'
    }
  }
}

// 处理代理地址输入框焦点事件
const handleProxyUrlFocus = (provider) => {
  // 如果代理地址为空，设置一个默认值方便用户修改
  if (!provider.proxy.url || provider.proxy.url.trim() === '') {
    provider.proxy.url = '127.0.0.1:7890'
  }
}

// 检测系统代理
const detectSystemProxy = async (provider) => {
  try {
    proxyDetectLoading.value[provider.id] = true

    // 调用后端API检测系统代理
    const response = await window.pywebview.api.detect_system_proxy()
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result && result.status === 'success') {
      const proxyInfo = result.data

      if (proxyInfo && proxyInfo.proxy_url) {
        // 解析代理URL
        let proxyUrl = proxyInfo.proxy_url

        // 移除协议前缀，因为我们有单独的协议选择器
        if (proxyUrl.startsWith('http://')) {
          proxyUrl = proxyUrl.substring(7)
          proxyProtocol.value = 'http://'
        } else if (proxyUrl.startsWith('https://')) {
          proxyUrl = proxyUrl.substring(8)
          proxyProtocol.value = 'https://'
        } else if (proxyUrl.startsWith('socks5://')) {
          proxyUrl = proxyUrl.substring(9)
          proxyProtocol.value = 'socks5://'
        }

        // 设置代理地址
        provider.proxy.url = proxyUrl

        // 如果检测到认证信息，也填入
        if (proxyInfo.username) {
          provider.proxy.username = proxyInfo.username
        }
        if (proxyInfo.password) {
          provider.proxy.password = proxyInfo.password
        }

        ElMessage.success(`检测到系统代理: ${proxyInfo.proxy_url}`)
      } else {
        ElMessage.info('未检测到系统代理配置')
      }
    } else {
      throw new Error(result?.message || '检测系统代理失败')
    }
  } catch (error) {
    console.error('检测系统代理失败:', error)
    ElMessage.error('检测系统代理失败: ' + error.message)
  } finally {
    proxyDetectLoading.value[provider.id] = false
  }
}

// 代理测试
const testProxyConnection = async (providerId) => {
  try {
    proxyTestLoading.value[providerId] = true
    proxyTestResults.value[providerId] = null

    const provider = aiProvidersStore.providers.find(p => p.id === providerId)
    if (!provider) {
      throw new Error('未找到服务商')
    }

    // 确保代理配置存在
    ensureProxyConfig(provider)

    if (!provider.proxy.enabled) {
      throw new Error('代理未启用')
    }

    if (!provider.proxy.url) {
      throw new Error('请输入代理地址')
    }

    // 调用后端API专门测试代理连接
    const testParams = {
      proxy_url: provider.proxy.url,
      proxy_username: provider.proxy.username || '',
      proxy_password: provider.proxy.password || '',
      timeout: provider.proxy.timeout || 30.0,
      verify_ssl: provider.proxy.verify_ssl !== false
    }

    const response = await window.pywebview.api.test_proxy_connection(testParams)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result && result.status === 'success') {
      proxyTestResults.value[providerId] = {
        success: true,
        message: result.message || '代理连接测试成功'
      }
      ElMessage.success('代理连接测试成功')
    } else {
      throw new Error(result?.message || '代理连接测试失败')
    }
  } catch (error) {
    console.error('代理测试失败:', error)
    proxyTestResults.value[providerId] = {
      success: false,
      message: error.message || '代理连接测试失败'
    }
    ElMessage.error('代理连接测试失败: ' + error.message)
  } finally {
    proxyTestLoading.value[providerId] = false
  }
}

// 模型管理
const addModel = (providerId) => {
  // 使用简单的HTML字符串创建对话框内容
  const htmlContent = `
    <div class="add-model-form" style="padding: 10px;">
      <div class="form-item" style="margin-bottom: 15px;">
        <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: bold;">模型ID (必填):</label>
        <input
          id="model-id-input"
          class="el-input__inner"
          placeholder="例如: gpt-4-turbo"
          style="width: 100%; padding: 8px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 14px;"
        />
      </div>
      <div class="form-item" style="margin-bottom: 15px;">
        <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: bold;">显示别名 (可选):</label>
        <input
          id="model-name-input"
          class="el-input__inner"
          placeholder="例如: GPT-4 Turbo (留空则使用模型ID)"
          style="width: 100%; padding: 8px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 14px;"
        />
      </div>
      <div class="form-item" style="margin-bottom: 15px;">
        <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: bold;">
          <input
            id="model-available-input"
            type="checkbox"
            checked
            style="margin-right: 8px;"
          />
          启用此模型
        </label>
      </div>
    </div>
  `

  ElMessageBox({
    title: '添加模型',
    message: htmlContent,
    dangerouslyUseHTMLString: true,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        const modelId = document.getElementById('model-id-input')?.value?.trim()
        const modelName = document.getElementById('model-name-input')?.value?.trim()
        const modelAvailable = document.getElementById('model-available-input')?.checked

        if (!modelId) {
          ElMessage.warning('请输入模型ID')
          return
        }

        // 检查模型ID是否已存在
        const provider = aiProvidersStore.providers.find(p => p.id === providerId)
        if (provider?.models?.some(m => m.id === modelId)) {
          ElMessage.warning('该模型ID已存在')
          return
        }

        try {
          aiProvidersStore.addModel(providerId, {
            id: modelId,
            name: modelName || modelId,
            available: modelAvailable !== false
          })
          ElMessage.success('模型添加成功，请记得保存配置')
          done()
        } catch (error) {
          ElMessage.error('添加模型失败: ' + error.message)
        }
      } else {
        done()
      }
    }
  })
}

const removeModel = async (providerId, modelIndex) => {
  try {
    aiProvidersStore.removeModel(providerId, modelIndex)
    ElMessage.success('模型已删除，请记得保存配置')
  } catch (error) {
    console.error('删除模型失败:', error)
    ElMessage.error('删除模型失败: ' + error.message)
  }
}

// 验证模型ID
const validateModelId = (providerId, model) => {
  if (!model.id || !model.id.trim()) {
    ElMessage.warning('模型ID不能为空')
    return false
  }

  // 检查是否有重复的模型ID
  const provider = aiProvidersStore.providers.find(p => p.id === providerId)
  if (provider?.models) {
    const duplicates = provider.models.filter(m => m.id === model.id)
    if (duplicates.length > 1) {
      ElMessage.warning('模型ID不能重复')
      return false
    }
  }

  // 如果别名为空，使用模型ID作为别名
  if (!model.name || !model.name.trim()) {
    model.name = model.id
  }

  return true
}

// 处理模型可用性变化
const handleModelAvailableChange = (model) => {
  console.log(`模型 ${model.name} (${model.id}) 可用性变更为: ${model.available}`)
  // 这里可以添加额外的逻辑，比如自动保存配置
}

// 获取可用模型
const fetchModels = async (providerId) => {
  try {
    await aiProvidersStore.fetchModels(providerId)
  } catch (error) {
    console.error('获取模型错误', error)
  }
}

// 保存单个服务商配置
const saveProviderConfig = async (providerId) => {
  try {
    await aiProvidersStore.saveProvider(providerId)
    // 成功提醒已在 store 中处理，这里不需要重复提醒
    // 给用户额外提示
    setTimeout(() => {
      ElMessage.info('提示：如需更新聊天界面的模型列表，请点击"保存所有配置并刷新模型"', {
        duration: 4000
      })
    }, 1000)
  } catch (error) {
    console.error('保存服务商配置错误', error)
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`)
  }
}

// 保存所有服务商配置
const saveAllProvidersConfig = async () => {
  try {
    // 验证所有服务商配置的完整性
    const invalidProviders = aiProvidersStore.providers.filter(provider => {
      return !provider.name || !provider.name.trim()
    })

    if (invalidProviders.length > 0) {
      ElMessage.warning('请确保所有服务商都有名称')
      return
    }

    await aiProvidersStore.saveProviders()
    // 成功提醒已在 store 中处理，这里不需要重复提醒
  } catch (error) {
    console.error('保存服务商配置错误', error)
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`)
  }
}

// 加载服务商配置
const loadProviders = async (forceRefresh = false) => {
  try {
    // Either force refresh or only load if not initialized
    if (forceRefresh || !aiProvidersStore.initialized) {
      // 添加超时处理
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('加载AI服务商配置超时')), 10000)
      })

      // 与API调用竞争，哪个先完成就用哪个结果
      await Promise.race([
        aiProvidersStore.loadProviders(forceRefresh),
        timeoutPromise
      ])

      // 确保所有服务商都有代理配置
      aiProvidersStore.providers.forEach(provider => {
        if (!provider.proxy) {
          provider.proxy = {
            enabled: false,
            url: '',
            username: '',
            password: '',
            timeout: 30.0,
            verify_ssl: true
          }
        }
      })

      // If there are providers, open the first one
      if (aiProvidersStore.providers.length > 0) {
        aiActiveProviders.value = [aiProvidersStore.providers[0].id]
      }
    }
  } catch (error) {
    console.error('加载服务商配置错误', error)
    // 确保出错时也重置loading状态
    aiProvidersStore.loading = false
  }
}

// 刷新服务商配置
const refreshProviders = async () => {
  try {
    await loadProviders(true) // 强制刷新
    ElMessage.success('AI服务商配置已刷新')
  } catch (error) {
    ElMessage.error('刷新AI服务商配置失败: ' + error.message)
  }
}

// 打开模型配置对话框
const openModelConfigDialog = (providerId, model) => {
  modelConfigDialog.value.visible = true
  modelConfigDialog.value.providerId = providerId
  modelConfigDialog.value.modelId = model.id
  modelConfigDialog.value.modelName = model.name || model.id

  // 初始化表单数据，如果模型已有配置则使用现有配置，否则使用默认值
  if (model.config) {
    modelConfigDialog.value.form = {
      temperature: model.config.temperature ?? 0.8,
      max_tokens: model.config.max_tokens ?? 4096,
      top_p: model.config.top_p ?? 0.8,
      frequency_penalty: model.config.frequency_penalty ?? 0,
      presence_penalty: model.config.presence_penalty ?? 0,
      stream: model.config.stream ?? true
    }
  } else {
    // 如果没有配置，使用默认值并为模型添加config字段
    model.config = {
      temperature: 0.8,
      max_tokens: 8192,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: true
    }
    modelConfigDialog.value.form = { ...model.config }
  }
}

// 保存模型配置
const saveModelConfig = async () => {
  try {
    const provider = aiProvidersStore.providers.find(p => p.id === modelConfigDialog.value.providerId)
    if (!provider) {
      ElMessage.error('未找到对应的服务商')
      return
    }

    const model = provider.models.find(m => m.id === modelConfigDialog.value.modelId)
    if (!model) {
      ElMessage.error('未找到对应的模型')
      return
    }

    // 更新模型配置
    model.config = { ...modelConfigDialog.value.form }

    // 保存到后端
    await aiProvidersStore.saveProviders()

    modelConfigDialog.value.visible = false
    // 成功提醒已在 store 中处理，这里不需要重复提醒
  } catch (error) {
    console.error('保存模型配置失败:', error)
    ElMessage.error('保存模型配置失败: ' + error.message)
  }
}

// 组件挂载时加载配置
onMounted(async () => {
  try {
    await loadProviders()
  } catch (error) {
    console.error('初始化AI服务商配置失败:', error)
  }
})
</script>

<style scoped>
/* 原生应用样式 - 继承父组件的禁用行为 */
.ai-provider-management {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
  overflow: hidden;
}

/* 滚动条样式 */
.ai-provider-management * {
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color) transparent;
}

.ai-provider-management *::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ai-provider-management *::-webkit-scrollbar-track {
  background: transparent;
}

.ai-provider-management *::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color);
  border-radius: 4px;
}

.ai-provider-management *::-webkit-scrollbar-thumb:hover {
  background-color: var(--el-border-color-dark);
}

.content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow: hidden;
}

/* 顶部操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 16px 20px;
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  flex-shrink: 0;
}

.action-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-description {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.action-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 4px;
  min-height: 0;
}

/* 空状态 */
.empty-providers {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 服务商列表 */
.provider-list {
  background: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.provider-header {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.provider-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.provider-content {
  padding: 24px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 子部分样式 */
.subsection {
  margin: 24px 0;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}

.subsection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.subsection-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 模型和密钥列表 */
.models-list,
.keys-list {
  margin-top: 16px;
}

.empty-models,
.empty-keys {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px dashed var(--el-border-color);
}

.empty-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

/* 操作按钮 */
.provider-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 模型配置对话框 */
.model-config-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.model-config-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 代理配置样式 */
.proxy-config {
  margin-top: 16px;
}

.proxy-test {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.proxy-test-result {
  font-size: 14px;
  font-weight: 500;
}

.proxy-test-result.success {
  color: var(--el-color-success);
}

.proxy-test-result.error {
  color: var(--el-color-error);
}

.proxy-disabled-hint {
  margin-top: 16px;
}

.proxy-url-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-info-alert {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  color: var(--el-text-color-primary);
}

.alert-icon {
  flex-shrink: 0;
  font-size: 16px;
  color: var(--el-text-color-regular);
  margin-top: 2px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  font-size: 14px;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.alert-description {
  font-size: 13px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

/* 工具类 */
.mt-2 {
  margin-top: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.mt-4 {
  margin-top: 16px;
}

.mr-2 {
  margin-right: 8px;
}

.ml-1 {
  margin-left: 4px;
}

.ml-2 {
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-provider-management {
    padding: 16px;
  }

  .action-bar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-right {
    justify-content: flex-start;
  }

  .page-title {
    font-size: 20px;
  }

  .provider-content {
    padding: 16px;
  }

  .subsection {
    padding: 16px;
  }

  .subsection-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

/* Element Plus 组件样式覆盖 */
:deep(.el-collapse-item__header) {
  padding: 16px 24px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-collapse-item__content) {
  padding: 0;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 0;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 0 20px 20px;
}
</style>