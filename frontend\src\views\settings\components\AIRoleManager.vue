<template>
  <div class="ai-role-manager">
    <div class="settings-panel">
      <div class="section-header">
        <h2 class="section-title">AI角色管理</h2>
        <div class="header-actions">
          <el-button-group>
            <el-button type="primary" @click="addAIRole">
              <el-icon><Plus /></el-icon>
              添加角色
            </el-button>
            <el-button @click="refreshAIRoles" :loading="aiRolesStore.loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button @click="aiRoleImportDialog.visible = true">
              导入
            </el-button>
            <el-button @click="exportAIRoles">
              导出
            </el-button>
          </el-button-group>
        </div>
      </div>

      <div class="panel-content">
        <div class="settings-card table-container">
          <el-table :data="aiRolesStore.roles" style="width: 100%" border :key="'aiRoles-' + aiRolesStore.roles.length">
            <el-table-column prop="name" label="名称" min-width="120" />
            <el-table-column prop="prompt" label="提示词" min-width="200" show-overflow-tooltip />
            <el-table-column label="启用" width="80" align="center">
              <template #default="{ row }">
                <el-switch
                    v-model="row.isEnabled"
                    @change="(val) => handleRoleEnableChange(row, val)"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="{ row }">
                <el-button-group>
                  <el-button
                      type="primary"
                      size="small"
                      @click="editAIRole(row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                      type="danger"
                      size="small"
                      @click="deleteAIRole(row)"
                  >
                    删除
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- AI角色对话框 -->
    <el-dialog
      v-model="aiRoleDialog.visible"
      :title="aiRoleDialog.title"
      width="500px"
      destroy-on-close
    >
      <el-form :model="aiRoleDialog.form" label-width="100px">
        <el-form-item label="角色名称" required>
          <el-input v-model="aiRoleDialog.form.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="提示词" required>
          <el-input
            v-model="aiRoleDialog.form.prompt"
            type="textarea"
            :rows="8"
            placeholder="请输入角色提示词"
          />
        </el-form-item>
        <el-form-item label="启用">
          <el-switch v-model="aiRoleDialog.form.isEnabled" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="aiRoleDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveAIRole">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- AI角色导入对话框 -->
    <el-dialog
      v-model="aiRoleImportDialog.visible"
      title="导入AI角色"
      width="600px"
      destroy-on-close
    >
      <div class="import-content">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>支持导入JSON格式的角色配置文件，格式如下：</p>
            <pre class="import-example">[
  {
    "name": "角色名称",
    "prompt": "角色提示词",
    "isEnabled": true
  }
]</pre>
          </template>
        </el-alert>
        
        <div class="import-actions">
          <el-button type="primary" @click="selectImportFile">
            <el-icon><Upload /></el-icon>
            选择文件
          </el-button>
          <span v-if="importFileName" class="import-file-name">{{ importFileName }}</span>
        </div>
        
        <el-input
          v-model="importContent"
          type="textarea"
          :rows="10"
          placeholder="或者直接粘贴JSON内容到这里"
          class="import-textarea"
        />
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="aiRoleImportDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="importAIRoles" :disabled="!importContent">导入</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, inject } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Upload } from '@element-plus/icons-vue'

// 注入依赖
const aiRolesStore = inject('aiRolesStore')
const showLoading = inject('showLoading')
const hideLoading = inject('hideLoading')

// 响应式数据
const aiRoleDialog = ref({
  visible: false,
  title: '',
  form: {
    id: '',
    name: '',
    prompt: '',
    isEnabled: true
  }
})

const aiRoleImportDialog = ref({
  visible: false
})

const importContent = ref('')
const importFileName = ref('')

// 添加AI角色
const addAIRole = () => {
  aiRoleDialog.value = {
    visible: true,
    title: '添加AI角色',
    form: {
      id: '',
      name: '',
      prompt: '',
      isEnabled: true
    }
  }
}

// 编辑AI角色
const editAIRole = (row) => {
  aiRoleDialog.value = {
    visible: true,
    title: '编辑AI角色',
    form: { ...row }
  }
}

// 删除AI角色
const deleteAIRole = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await aiRolesStore.deleteRole(row.id)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除AI角色失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 保存AI角色
const saveAIRole = async () => {
  try {
    const form = aiRoleDialog.value.form
    
    if (!form.name.trim()) {
      ElMessage.error('请输入角色名称')
      return
    }
    
    if (!form.prompt.trim()) {
      ElMessage.error('请输入角色提示词')
      return
    }
    
    if (form.id) {
      await aiRolesStore.updateRole(form.id, form)
      ElMessage.success('编辑成功')
    } else {
      await aiRolesStore.addRole(form)
      ElMessage.success('添加成功')
    }
    
    aiRoleDialog.value.visible = false
  } catch (error) {
    console.error('保存AI角色失败:', error)
    ElMessage.error('保存失败')
  }
}

// 处理角色启用状态变更
const handleRoleEnableChange = async (row, val) => {
  try {
    await aiRolesStore.updateRole(row.id, { ...row, isEnabled: val })
    ElMessage.success(val ? '角色已启用' : '角色已禁用')
  } catch (error) {
    console.error('更新角色状态失败:', error)
    ElMessage.error('更新失败')
    // 回滚状态
    row.isEnabled = !val
  }
}

// 刷新AI角色
const refreshAIRoles = async () => {
  try {
    await aiRolesStore.loadRoles()
    ElMessage.success('刷新成功')
  } catch (error) {
    console.error('刷新AI角色失败:', error)
    ElMessage.error('刷新失败')
  }
}

// 导出AI角色
const exportAIRoles = async () => {
  try {
    const data = JSON.stringify(aiRolesStore.roles, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `ai-roles-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出AI角色失败:', error)
    ElMessage.error('导出失败')
  }
}

// 选择导入文件
const selectImportFile = async () => {
  try {
    const response = await window.pywebview.api.select_file_path()
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result && result.status === 'success' && result.data.length > 0) {
      const filePath = result.data[0]
      // 这里需要后端提供读取文件的API
      try {
        const fileContent = await window.pywebview.api.read_file({ path: filePath })
        const contentResult = typeof fileContent === 'string' ? JSON.parse(fileContent) : fileContent

        if (contentResult.status === 'success') {
          importContent.value = contentResult.data
          importFileName.value = filePath.split('/').pop() || filePath.split('\\').pop()
          ElMessage.success('文件读取成功')
        } else {
          throw new Error(contentResult.message || '读取文件失败')
        }
      } catch (readError) {
        console.error('读取文件失败:', readError)
        ElMessage.error('读取文件失败: ' + readError.message)
      }
    }
  } catch (error) {
    console.error('选择文件失败:', error)
    ElMessage.error('选择文件失败: ' + error.message)
  }
}

// 导入AI角色
const importAIRoles = async () => {
  try {
    const roles = JSON.parse(importContent.value)
    
    if (!Array.isArray(roles)) {
      ElMessage.error('导入数据格式错误，应为数组格式')
      return
    }
    
    let successCount = 0
    for (const role of roles) {
      if (role.name && role.prompt) {
        await aiRolesStore.addRole({
          name: role.name,
          prompt: role.prompt,
          isEnabled: role.isEnabled !== false
        })
        successCount++
      }
    }
    
    aiRoleImportDialog.value.visible = false
    importContent.value = ''
    importFileName.value = ''
    
    ElMessage.success(`成功导入 ${successCount} 个角色`)
  } catch (error) {
    console.error('导入AI角色失败:', error)
    ElMessage.error('导入失败: ' + error.message)
  }
}
</script>

<style lang="scss" scoped>
.ai-role-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.settings-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 16px;
  gap: 16px;
}

.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }
}

.settings-card {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 0;
  border: 1px solid var(--el-border-color-light);

  &.table-container {
    flex: 1; /* 表格容器占用剩余空间 */
    display: flex;
    flex-direction: column;
    overflow: hidden;

    :deep(.el-table) {
      border-radius: 8px;
      flex: 1;

      .el-table__body-wrapper {
        flex: 1;
        overflow-y: auto;
      }
    }
  }
}

.import-content {
  .import-example {
    background: var(--el-fill-color-light);
    padding: 12px;
    border-radius: 4px;
    font-size: 12px;
    margin-top: 8px;
  }
  
  .import-actions {
    margin: 16px 0;
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .import-file-name {
    color: var(--el-color-success);
    font-size: 14px;
  }
  
  .import-textarea {
    margin-top: 16px;
  }
}
</style>
