<template>
  <div class="backup-settings">
    <div class="settings-section">
      <div class="section-header">
        <h2 class="section-title">本地备份设置</h2>
        <div class="header-actions">
          <el-button type="primary" @click="backupNow">
            <el-icon><Upload /></el-icon>
            立即备份
          </el-button>
        </div>
      </div>

      <div class="panel-content">
        <div class="backup-content">
          <div class="settings-card backup-settings-form">
            <el-form :model="backupForm" label-width="100px" size="small">
              <el-form-item label="自动备份" class="mb-2">
                <el-switch
                  v-model="backupForm.autoBackup"
                  @change="handleAutoBackupChange"
                />
              </el-form-item>
              
              <template v-if="backupForm.autoBackup">
                <el-form-item label="备份间隔" class="mb-2">
                  <div class="input-with-tip">
                    <el-input-number 
                      v-model="backupForm.backupInterval" 
                      :min="1" 
                      :max="1440"
                      :step="1"
                      @change="handleBackupIntervalChange"
                      controls-position="right"
                      size="small"
                    />
                    <span class="ml-2">分钟</span>
                    <el-tooltip content="设置自动备份的时间间隔（1-1440分钟）" placement="top">
                      <el-icon class="ml-1"><InfoFilled /></el-icon>
                    </el-tooltip>
                  </div>
                </el-form-item>
                
                <el-form-item label="保留数量" class="mb-2">
                  <div class="input-with-tip">
                    <el-input-number
                      v-model="backupForm.keepBackups"
                      :min="1"
                      :max="100"
                      :step="1"
                      @change="handleKeepBackupsChange"
                      controls-position="right"
                      size="small"
                    />
                    <el-tooltip content="设置要保留的最新备份数量" placement="top">
                      <el-icon class="ml-1"><InfoFilled /></el-icon>
                    </el-tooltip>
                  </div>
                </el-form-item>
              </template>

              <el-form-item label="压缩备份" class="mb-2">
                <el-tooltip content="启用后将使用ZIP压缩格式进行备份，节省空间但可能增加处理时间" placement="top">
                  <el-switch
                    v-model="backupForm.useZip"
                    @change="handleUseZipChange"
                  />
                </el-tooltip>
              </el-form-item>

              <el-form-item label="备份源目录" class="mb-2">
                <div class="path-input-group">
                  <el-input
                    v-model="backupForm.backupDir"
                    placeholder="建议用软件的backup目录"
                    size="small"
                  />
                  <el-button type="primary" size="small" @click="selectBackupDir">
                    <el-icon><Folder /></el-icon>
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item label="备份目录" class="mb-2">
                <div class="path-input-group">
                  <el-input
                    v-model="backupForm.targetDir"
                    placeholder="备份存储目录"
                    size="small"
                  />
                  <el-button type="primary" size="small" @click="selectTargetDir">
                    <el-icon><Folder /></el-icon>
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>

          <!-- 备份进度显示 -->
          <div v-if="backupProgress.visible" class="settings-card backup-progress">
            <div class="progress-header">
              <h4>备份进度</h4>
            </div>
            <el-progress
              :percentage="backupProgress.percent"
              :status="backupProgress.status === 'error' ? 'exception' : 'success'"
              :stroke-width="8"
            />
            <p class="progress-message">{{ backupProgress.message }}</p>
          </div>

          <div class="settings-card backup-history">
            <div class="card-header">
              <h3>备份历史</h3>
              <div class="header-controls">
                <el-tooltip content="显示大小会增加加载时间，但提供更详细信息" placement="top">
                  <el-switch
                    v-model="showSizeColumn"
                    size="small"
                    active-text="显示大小"
                    inactive-text="隐藏大小"
                    style="margin-right: 12px;"
                  />
                </el-tooltip>
                <el-button-group>
                <el-button
                  type="primary"
                  size="small"
                  @click="backupNow"
                >
                  <el-icon><Upload /></el-icon>
                  立即备份
                </el-button>
                <el-button
                  size="small"
                  @click="handleManualRefresh"
                >
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>

                </el-button-group>
              </div>
            </div>
            
            <div class="table-container">
              <div class="table-wrapper">
                <el-table
                  :data="backupHistory"
                  :key="backupHistory.length"
                  style="width: 100%;"
                  border
                  size="small"
                  :height="350"
                  stripe
                  empty-text="暂无备份记录"
                  v-loading="false"
                  row-key="path"
                >
                <el-table-column label="备份时间" min-width="160">
                  <template #default="{ row }">
                    {{ formatBackupTime(row.time) }}
                    <el-tag 
                      :type="row.type === 'auto' ? 'success' : 'primary'"
                      size="small"
                      class="ml-2"
                    >
                      {{ row.type === 'auto' ? '自动' : '手动' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column v-if="showSizeColumn" label="大小" width="100">
                  <template #default="{ row }">
                    {{ row.size > 0 ? formatFileSize(row.size) : '未计算' }}
                  </template>
                </el-table-column>
                <el-table-column label="路径" min-width="200" show-overflow-tooltip>
                  <template #default="{ row }">
                    {{ row.path }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" align="center" fixed="right">
                  <template #default="{ row }">
                    <el-button-group>
                      <el-button
                        type="primary"
                        size="small"
                        @click="restoreFromHistory(row)"
                      >
                        恢复
                      </el-button>
                      <el-button
                        type="danger"
                        size="small"
                        @click="deleteBackup(row)"
                      >
                        删除
                      </el-button>
                    </el-button-group>
                  </template>
                </el-table-column>
              </el-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, inject, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Refresh, Folder, InfoFilled } from '@element-plus/icons-vue'

// 注入依赖
const configStore = inject('configStore')
const showLoading = inject('showLoading')
const hideLoading = inject('hideLoading')



// 本地备份配置
const backupForm = ref({
  autoBackup: false,
  backupInterval: 60,
  keepBackups: 10,
  useZip: true,
  backupDir: '',
  targetDir: ''
})

const backupHistory = ref([])
let backupTimeoutId = null

// 备份进度状态
const backupProgress = ref({
  visible: false,
  percent: 0,
  message: '',
  status: 'normal'
})

// 是否显示大小列
const showSizeColumn = ref(false)

// 监听显示大小开关变化
watch(showSizeColumn, async (newValue) => {
  if (newValue && backupHistory.value.length > 0) {
    // 如果开启显示大小且有数据，重新获取包含大小的数据
    await refreshBackupHistory(false, true)
  }
})

// 本地备份方法
const backupNow = async () => {
  try {
    // 检查API是否可用
    if (!window.pywebview || !window.pywebview.api) {
      ElMessage.error('API未初始化，请刷新页面重试')
      return
    }

    if (!backupForm.value.backupDir) {
      ElMessage.warning('请先选择需要备份的目录')
      return
    }
    if (!backupForm.value.targetDir) {
      ElMessage.warning('请先选择备份保存的目录')
      return
    }

    showLoading('正在执行备份...')

    // 显示进度条
    backupProgress.value = {
      visible: true,
      percent: 0,
      message: '正在准备备份...',
      status: 'normal'
    }

    // 设置超时机制，防止备份卡住
    backupTimeoutId = setTimeout(() => {
      console.log('备份超时，强制关闭加载状态')
      hideLoading()
      backupProgress.value.visible = false
      ElMessage.error('备份超时，请检查目录是否可访问')
    }, 300000) // 5分钟超时

    // 调用后端备份API
    const response = await window.pywebview.api.backup_data({
      backup_dir: backupForm.value.backupDir,
      target_dir: backupForm.value.targetDir,
      use_zip: backupForm.value.useZip,
      is_auto: false
    })

    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      // 备份API调用成功，等待进度回调处理完成状态
    } else {
      hideLoading()
      throw new Error(result.message || '备份失败')
    }
  } catch (error) {
    console.error('备份失败:', error)
    ElMessage.error('备份失败：' + (error.message || error.toString()))

    // 清除超时定时器
    if (backupTimeoutId) {
      clearTimeout(backupTimeoutId)
      backupTimeoutId = null
    }

    hideLoading()
  }
}

const refreshBackupHistory = async (showMessage = false, calculateSize = false) => {
  try {
    // 检查API是否可用
    if (!window.pywebview || !window.pywebview.api) {
      ElMessage.error('API未初始化，请刷新页面重试')
      return
    }

    if (!backupForm.value.targetDir) {
      backupHistory.value = []
      if (showMessage) {
        ElMessage.warning('请先设置备份目标目录')
      }
      return
    }

    const response = await window.pywebview.api.get_backup_history({
      target_dir: backupForm.value.targetDir,
      calculate_size: calculateSize
    })

    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      const newData = result.data || []
      backupHistory.value = [...newData]

      if (showMessage) {
        ElMessage.success(`已刷新备份历史，共 ${backupHistory.value.length} 条记录`)
      }
    } else {
      backupHistory.value = []
      if (result.message !== '备份目录不存在') {
        ElMessage.error('获取备份历史失败：' + result.message)
      }
    }
  } catch (error) {
    console.error('获取备份历史出错:', error)
    backupHistory.value = []
    ElMessage.error('获取备份历史失败：' + (error.message || error.toString()))
  }
}

// 手动刷新方法
const handleManualRefresh = async () => {
  await refreshBackupHistory(true, showSizeColumn.value)
}

const formatBackupTime = (timestamp) => {
  if (!timestamp) return '未知时间'
  // 如果时间戳小于10位，说明是秒级时间戳，需要转换为毫秒
  const ms = timestamp.toString().length <= 10 ? timestamp * 1000 : timestamp
  return new Date(ms).toLocaleString('zh-CN')
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const restoreFromHistory = async (backup) => {
  try {
    // 显示详细的确认对话框
    await ElMessageBox.confirm(
      `确定要从以下备份恢复吗？

备份时间: ${formatBackupTime(backup.time)}
备份路径: ${backup.path}
备份类型: ${backup.type === 'auto' ? '自动备份' : '手动备份'}

⚠️ 警告：恢复备份将覆盖当前数据，此操作不可撤销！
应用将在恢复完成后自动重启。`,
      '恢复确认',
      {
        confirmButtonText: '确定恢复',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    showLoading('正在恢复备份...')
    const response = await window.pywebview.api.restore_backup({
      backup_path: backup.path
    })

    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result.status === 'success') {
      hideLoading()

      // 显示成功消息和倒计时
      let countdown = 3
      const showCountdown = () => {
        if (countdown > 0) {
          ElMessage({
            message: `恢复成功！应用将在 ${countdown} 秒后重启...`,
            type: 'success',
            duration: 1000
          })
          countdown--
          setTimeout(showCountdown, 1000)
        } else {
          // 执行重启
          window.pywebview.api.restart_application().catch(error => {
            ElMessage.error('重启应用失败，请手动重启')
            console.error('重启失败:', error)
          })
        }
      }
      showCountdown()
    } else {
      throw new Error(result.message || '恢复失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('恢复备份失败:', error)
      ElMessage.error('恢复失败：' + error.message)
    }
  } finally {
    hideLoading()
  }
}

const deleteBackup = async (backup) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除备份 "${formatBackupTime(backup.time)}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    showLoading('正在删除备份...')

    const response = await window.pywebview.api.delete_backup({
      backup_path: backup.path
    })

    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('删除成功')

      // 先从本地列表中移除，提供即时反馈
      const index = backupHistory.value.findIndex(item => item.path === backup.path)
      if (index !== -1) {
        backupHistory.value.splice(index, 1)
        backupHistory.value = [...backupHistory.value]
      }

      // 延迟刷新完整列表确保数据同步
      setTimeout(async () => {
        await refreshBackupHistory()
      }, 1000)
    } else {
      throw new Error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除备份失败:', error)
      ElMessage.error('删除失败：' + (error.message || error.toString()))
    }
  } finally {
    hideLoading()
  }
}

// 配置变更处理
const handleAutoBackupChange = async (val) => {
  try {
    await configStore.updateConfigItem('backup.autoBackup', val)

    // 根据开关状态启动或停止自动备份
    if (val) {
      const response = await window.pywebview.api.check_auto_backup()
      const result = typeof response === 'string' ? JSON.parse(response) : response

      if (result.status === 'success') {
        ElMessage.success('自动备份已启用')
      } else {
        throw new Error(result.message || '启动自动备份失败')
      }
    } else {
      await window.pywebview.api.stop_auto_backup()
      ElMessage.info('自动备份已停止')
    }
  } catch (error) {
    console.error('更新自动备份设置失败:', error)
    ElMessage.error('更新自动备份设置失败：' + error.message)
    backupForm.value.autoBackup = !val
  }
}

const handleBackupIntervalChange = async (val) => {
  try {
    await configStore.updateConfigItem('backup.backupInterval', val)
  } catch (error) {
    console.error('更新备份间隔失败:', error)
    ElMessage.error('更新备份周期失败：' + error.message)
    backupForm.value.backupInterval = configStore.backup.backupInterval
  }
}

const handleKeepBackupsChange = async (val) => {
  try {
    await configStore.updateConfigItem('backup.keepBackups', val)
  } catch (error) {
    console.error('更新保留数量失败:', error)
    ElMessage.error('更新保留备份数量失败：' + error.message)
    backupForm.value.keepBackups = configStore.backup.keepBackups
  }
}

const handleUseZipChange = async (val) => {
  try {
    await configStore.updateConfigItem('backup.useZip', val)
  } catch (error) {
    console.error('更新压缩设置失败:', error)
    ElMessage.error('更新压缩备份设置失败：' + error.message)
    backupForm.value.useZip = !val
  }
}

// 目录选择
const selectBackupDir = async () => {
  try {
    const response = await window.pywebview.api.select_directory()
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result && result.status === 'success' && result.data) {
      backupForm.value.backupDir = result.data
      await configStore.updateConfigItem('backup.backupDir', result.data)
      ElMessage.success('备份源目录设置成功')
    }
  } catch (error) {
    console.error('选择备份源目录失败:', error)
    ElMessage.error('选择目录失败：' + error.message)
  }
}

const selectTargetDir = async () => {
  try {
    const response = await window.pywebview.api.select_directory()
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result && result.status === 'success' && result.data) {
      backupForm.value.targetDir = result.data
      await configStore.updateConfigItem('backup.targetDir', result.data)
      ElMessage.success('备份目标目录设置成功')
      // 目标目录设置后，刷新备份历史
      await refreshBackupHistory()
    }
  } catch (error) {
    console.error('选择备份目标目录失败:', error)
    ElMessage.error('选择目录失败：' + error.message)
  }
}

// 加载配置数据
const loadBackupConfig = async () => {
  try {
    if (configStore.backup) {
      backupForm.value = { ...configStore.backup }
    }
  } catch (error) {
    console.error('加载备份配置失败:', error)
  }
}



// 备份进度回调函数
const receiveBackupProgress = (progressData) => {
  // 清除超时定时器
  if (backupTimeoutId) {
    clearTimeout(backupTimeoutId)
    backupTimeoutId = null
  }

  // 更新进度显示
  backupProgress.value = {
    visible: true,
    percent: progressData.percent || 0,
    message: progressData.message || '',
    status: progressData.status || 'normal'
  }

  if (progressData.status === 'success') {
    // 备份完成后关闭加载状态并刷新历史记录
    if (progressData.percent === 100) {
      hideLoading()
      ElMessage.success('备份完成！')

      // 进度条显示完成状态，然后隐藏
      setTimeout(() => {
        backupProgress.value.visible = false
        refreshBackupHistory()
      }, 2000)
    }
  } else if (progressData.status === 'error' || progressData.status === 'exception') {
    hideLoading()
    ElMessage.error('备份失败：' + progressData.message)

    // 错误时也隐藏进度条
    setTimeout(() => {
      backupProgress.value.visible = false
    }, 3000)
  }
}

// 保存原有的全局函数（如果存在）
let originalReceiveBackupProgress = null

// 将进度回调函数暴露到全局，供后端调用
if (typeof window !== 'undefined') {
  // 保存原有函数
  originalReceiveBackupProgress = window.receiveBackupProgress

  // 创建一个组合函数，同时调用原有函数和当前组件的函数
  window.receiveBackupProgress = (progressData) => {
    // 调用当前组件的回调
    receiveBackupProgress(progressData)

    // 如果有原有的回调，也调用它
    if (originalReceiveBackupProgress && typeof originalReceiveBackupProgress === 'function') {
      try {
        originalReceiveBackupProgress(progressData)
      } catch (error) {
        console.error('调用原有备份进度回调失败:', error)
      }
    }
  }
}

// 生命周期钩子
onMounted(async () => {
  try {
    await loadBackupConfig()
    await refreshBackupHistory()
  } catch (error) {
    console.error('组件初始化失败:', error)
  }
})

onUnmounted(() => {
  // 清理超时定时器
  if (backupTimeoutId) {
    clearTimeout(backupTimeoutId)
    backupTimeoutId = null
  }

  // 恢复原有的全局回调函数
  if (typeof window !== 'undefined') {
    if (originalReceiveBackupProgress) {
      window.receiveBackupProgress = originalReceiveBackupProgress
    } else {
      delete window.receiveBackupProgress
    }
  }
})
</script>

<style lang="scss" scoped>
.backup-settings {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.settings-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 16px;
  gap: 16px;
}

.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }
}

.backup-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.settings-card {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--el-border-color-light);

  &.backup-progress {
    .progress-header {
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    .progress-message {
      margin: 12px 0 0 0;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }

  &.backup-history {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      flex-shrink: 0;
      position: relative;
      z-index: 1;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }

      .header-controls {
        display: flex;
        align-items: center;
        gap: 12px;

        .el-button-group {
          position: relative;
          z-index: 2;

          .el-button {
            pointer-events: auto;
            cursor: pointer;
          }
        }
      }
    }

    .table-container {
      flex: 1;
      overflow: hidden;
      height: 350px;
      border-radius: 8px;

      .table-wrapper {
        height: 100%;
        overflow: hidden;
      }

      .el-table {
        height: 350px !important;
        border-radius: 8px;

        :deep(.el-table__body-wrapper) {
          overflow-y: auto !important;
          max-height: 300px !important; /* 确保有足够空间显示内容 */

          /* 自定义滚动条样式 */
          &::-webkit-scrollbar {
            width: 8px;
          }

          &::-webkit-scrollbar-track {
            background: var(--el-fill-color-lighter);
            border-radius: 4px;
          }

          &::-webkit-scrollbar-thumb {
            background: var(--el-border-color-darker);
            border-radius: 4px;

            &:hover {
              background: var(--el-color-primary);
            }
          }
        }

        :deep(.el-table__header-wrapper) {
          overflow: visible;
          background: var(--el-bg-color);
        }

        /* 确保表格行有足够的内边距 */
        :deep(.el-table__row) {
          &:last-child td {
            padding-bottom: 12px !important;
          }
        }

        /* 确保表格底部有足够空间 */
        :deep(.el-table__body) {
          padding-bottom: 8px;
        }
      }
    }
  }
}

.path-input-group {
  display: flex;
  gap: 8px;
  
  .el-input {
    flex: 1;
  }
}

.input-with-tip {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
