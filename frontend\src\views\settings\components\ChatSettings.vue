<template>
  <div class="chat-settings">
    <div class="settings-section">
      <div class="section-header">
        <h2 class="section-title">AI聊天界面设置</h2>
        <div class="header-actions">
          <el-button type="primary" @click="saveChatSettings">
            <el-icon><Check /></el-icon>
            保存设置
          </el-button>
          <el-button @click="resetChatSettings">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </div>
      </div>

      <div class="panel-content">
        <div class="settings-card">
          <el-form label-width="140px">
            <h3 class="subsection-title">界面样式</h3>

            <el-form-item label="字体大小">
              <div class="font-size-slider-container">
                <div class="slider-header">
                  <span class="slider-label">缩放比例</span>
                  <span class="slider-value">{{ Math.round(chatSettings.fontSize * 100) }}%</span>
                </div>
                <el-slider
                  v-model="chatSettings.fontSize"
                  :min="0.5"
                  :max="2.0"
                  :step="0.05"
                  :show-tooltip="false"
                  class="font-size-slider"
                />

                <div class="slider-description">
                  <span class="desc-text">调整聊天消息的整体字体大小，影响标题、正文、代码等所有文本元素</span>
                </div>
              </div>
            </el-form-item>
            
            <el-form-item label="字体">
              <el-select v-model="chatSettings.fontFamily" style="width: 300px;">
                <el-option label="微软雅黑" value="微软雅黑, sans-serif" />
                <el-option label="宋体" value="宋体, serif" />
                <el-option label="黑体" value="黑体, sans-serif" />
                <el-option label="楷体" value="楷体, serif" />
                <el-option label="汉仪旗黑" value="汉仪旗黑, sans-serif" />
                <el-option label="Arial" value="Arial, sans-serif" />
              </el-select>
            </el-form-item>

            <h3 class="subsection-title">代码块样式</h3>
            
            <el-form-item label="代码块主题">
              <el-radio-group v-model="chatSettings.codeBlockTheme">
                <el-radio value="auto">跟随系统</el-radio>
                <el-radio value="light">亮色主题</el-radio>
                <el-radio value="dark">暗色主题</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="代码块字体大小">
              <div class="font-size-slider-container">
                <div class="slider-header">
                  <span class="slider-label">代码字体缩放</span>
                  <span class="slider-value">{{ Math.round(chatSettings.codeBlockFontSize * 100) }}%</span>
                </div>
                <el-slider
                  v-model="chatSettings.codeBlockFontSize"
                  :min="0.5"
                  :max="2.0"
                  :step="0.05"
                  :show-tooltip="false"
                  class="font-size-slider"
                />

              </div>
            </el-form-item>
            

          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, RefreshLeft } from '@element-plus/icons-vue'

// 注入依赖
const configStore = inject('configStore')
const showLoading = inject('showLoading')
const hideLoading = inject('hideLoading')

// 聊天设置
const chatSettings = ref({
  fontSize: 1.0, // 字体缩放比例，0.5-2.0
  fontFamily: '微软雅黑, sans-serif',
  codeBlockTheme: 'auto',
  codeBlockFontSize: 1.0 // 代码块字体缩放比例，0.5-2.0
})

// 聊天设置方法
const saveChatSettings = async () => {
  try {
    showLoading('正在保存聊天设置...')
    await configStore.updateConfigItem('chat', chatSettings.value)
    ElMessage.success('聊天设置保存成功')
  } catch (error) {
    console.error('保存聊天设置失败:', error)
    ElMessage.error(`保存失败: ${error.message}`)
  } finally {
    hideLoading()
  }
}

const resetChatSettings = () => {
  chatSettings.value = {
    fontSize: 1.0,
    fontFamily: '微软雅黑, sans-serif',
    codeBlockTheme: 'auto',
    codeBlockFontSize: 1.0
  }
  ElMessage.success('聊天设置已重置')
}

// 字符串到数字的转换映射
const convertLegacyFontSize = (value) => {
  if (typeof value === 'number') {
    return value
  }

  // 兼容旧的字符串格式
  const legacyMap = {
    'small': 0.875,
    'normal': 1.0,
    'large': 1.125
  }

  return legacyMap[value] || 1.0
}

// 加载配置数据
const loadChatSettings = async () => {
  try {
    if (configStore.chat) {
      const config = { ...configStore.chat }

      // 转换字体大小值，确保兼容性
      config.fontSize = convertLegacyFontSize(config.fontSize)
      config.codeBlockFontSize = convertLegacyFontSize(config.codeBlockFontSize)

      chatSettings.value = config
    }
  } catch (error) {
    console.error('加载聊天设置失败:', error)
  }
}

// 生命周期钩子
onMounted(async () => {
  await loadChatSettings()
})
</script>

<style lang="scss" scoped>
.chat-settings {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.settings-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 16px;
  gap: 16px;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }
}

.settings-card {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--el-border-color-light);
}

.subsection-title {
  font-size: 16px;
  font-weight: 600;
  margin: 20px 0 16px 0;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 8px;
}

// 字体大小滑动条样式
.font-size-slider-container {
  width: 100%;
  max-width: 400px;

  .slider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .slider-label {
      font-weight: 500;
      color: var(--el-text-color-primary);
      font-size: 14px;
    }

    .slider-value {
      font-weight: 600;
      color: var(--el-color-primary);
      font-size: 14px;
      background: var(--el-color-primary-light-9);
      padding: 2px 8px;
      border-radius: 4px;
    }
  }

  .font-size-slider {
    margin: 16px 0;

    :deep(.el-slider__runway) {
      height: 6px;
      background-color: var(--el-border-color-light);
    }

    :deep(.el-slider__bar) {
      background-color: var(--el-color-primary);
    }

    :deep(.el-slider__button) {
      width: 16px;
      height: 16px;
      border: 2px solid var(--el-color-primary);
    }
  }


  .slider-description {
    margin-top: 12px;

    .desc-text {
      font-size: 12px;
      color: var(--el-text-color-regular);
      line-height: 1.4;
    }
  }
}
</style>
