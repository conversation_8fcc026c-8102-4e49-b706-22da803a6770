<template>
  <div class="chrome-settings">
    <div class="settings-panel">
      <!-- Chrome基本设置 -->
      <div class="section-header">
        <h2 class="section-title">Chrome 设置</h2>
      </div>

      <div class="panel-content">
        <div class="settings-card">
          <el-form label-width="120px">
            <el-form-item label="Chrome路径">
              <div class="path-input-group">
                <el-input
                    v-model="configStore.chrome.default_path"
                    placeholder="请选择 Chrome 浏览器路径"
                />
                <el-button-group>
                  <el-button type="primary" @click="selectChromePath">
                    <el-icon><Folder /></el-icon>
                    选择路径
                  </el-button>
                  <el-button type="success" @click="detectChromePath">
                    <el-icon><Search /></el-icon>
                    自动检测
                  </el-button>
                </el-button-group>
              </div>
            </el-form-item>

            <el-form-item label="下载目录">
              <div class="path-input-group">
                <el-input
                    v-model="configStore.chrome.downloadDir"
                    placeholder="请选择下载目录"
                />
                <el-button type="primary" @click="selectDownloadDir">
                  <el-icon><Folder /></el-icon>
                  选择路径
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 用户数据目录管理 -->
        <div class="section-header">
          <h2 class="section-title">用户数据目录</h2>
          <div class="header-actions">
            <el-button type="primary" @click="addUserDataDir">
              <el-icon><Plus /></el-icon>
              添加目录
            </el-button>
          </div>
        </div>

        <div class="settings-card table-container">
          <el-table :data="userDataDirs" style="width: 100%" border>
            <el-table-column prop="name" label="名称" min-width="120" />
            <el-table-column prop="path" label="路径" min-width="200" show-overflow-tooltip />
            <el-table-column prop="port" label="端口" width="100" align="center" />
            <el-table-column label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="runningStatus[row.id] ? 'success' : 'info'">
                  {{ runningStatus[row.id] ? '运行中' : '已停止' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="默认" width="80" align="center">
              <template #default="{ row }">
                <el-switch
                    v-model="row.isDefault"
                    @change="(val) => handleDefaultChange(row, val)"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="280" align="center" fixed="right">
              <template #default="{ row }">
                <el-button-group>
                  <el-button
                      :type="runningStatus[row.id] ? 'danger' : 'success'"
                      size="small"
                      @click="toggleChrome(row)"
                  >
                    {{ runningStatus[row.id] ? '停止' : '启动' }}
                  </el-button>
                  <el-button
                      type="primary"
                      size="small"
                      @click="editUserDataDir(row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                      type="danger"
                      size="small"
                      @click="deleteUserDataDir(row)"
                  >
                    删除
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 添加/编辑用户数据目录对话框 -->
    <el-dialog
        v-model="userDataDirDialog.visible"
        :title="userDataDirDialog.isEdit ? '编辑用户数据目录' : '添加用户数据目录'"
        width="600px"
    >
      <el-form
          ref="userDataDirFormRef"
          :model="userDataDirDialog.form"
          :rules="userDataDirRules"
          label-width="120px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="userDataDirDialog.form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="路径" prop="path">
          <div class="path-input-group">
            <el-input v-model="userDataDirDialog.form.path" placeholder="请选择路径" />
            <el-button type="primary" @click="selectUserDataPath">
              <el-icon><Folder /></el-icon>
              选择路径
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="userDataDirDialog.form.port" :min="1024" :max="65535" />
        </el-form-item>
        <el-form-item label="设为默认">
          <el-switch v-model="userDataDirDialog.form.isDefault" />
        </el-form-item>
        <el-form-item label="启用扩展">
          <el-switch v-model="userDataDirDialog.form.enableExtensions" />
        </el-form-item>
        <el-form-item 
            label="扩展路径" 
            prop="extensionsPath"
            v-if="userDataDirDialog.form.enableExtensions"
        >
          <div class="path-input-group">
            <el-input v-model="userDataDirDialog.form.extensionsPath" placeholder="请选择扩展路径" />
            <el-button type="primary" @click="selectExtensionsPath">
              <el-icon><Folder /></el-icon>
              选择路径
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="userDataDirDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveUserDataDir">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, inject, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Folder, Plus, Search } from '@element-plus/icons-vue'

// 注入依赖
const configStore = inject('configStore')
const showLoading = inject('showLoading')
const hideLoading = inject('hideLoading')

// 响应式数据
const userDataDirs = computed(() => configStore.chrome?.userDataDirs || [])
const runningStatus = ref({})
const statusCheckTimer = ref(null)

// 用户数据目录对话框
const userDataDirDialog = ref({
  visible: false,
  isEdit: false,
  form: {
    id: '',
    name: '',
    path: '',
    port: 9222,
    isDefault: false,
    enableExtensions: false,
    extensionsPath: ''
  }
})

const userDataDirFormRef = ref()

// 表单验证规则
const userDataDirRules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' }
  ],
  path: [
    { required: true, message: '请选择路径', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口', trigger: 'blur' },
    { type: 'number', min: 1024, max: 65535, message: '端口范围1024-65535', trigger: 'blur' }
  ]
}

// Chrome路径选择
const selectChromePath = async () => {
  try {
    const response = await window.pywebview.api.select_file_path()
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result && result.status === 'success' && result.data.length > 0) {
      configStore.chrome.default_path = result.data[0]
      await configStore.updateConfigItem('chrome.default_path', result.data[0])
      ElMessage.success('Chrome路径设置成功')
    }
  } catch (error) {
    console.error('选择Chrome路径失败:', error)
    ElMessage.error('选择Chrome路径失败: ' + error.message)
  }
}

// 自动检测Chrome路径
const detectChromePath = async () => {
  try {
    showLoading('正在检测Chrome路径...')
    const result = await window.pywebview.api.drssion_controller.detect_chrome_path()
    const response = typeof result === 'string' ? JSON.parse(result) : result
    if (response.status === 'success') {
      configStore.chrome.default_path = response.data.path
      await configStore.updateConfigItem('chrome.default_path', response.data.path)
      ElMessage.success('Chrome路径检测成功')
    } else {
      ElMessage.warning('未检测到Chrome安装路径')
    }
  } catch (error) {
    console.error('检测Chrome路径失败:', error)
    ElMessage.error('检测Chrome路径失败：' + error.message)
  } finally {
    hideLoading()
  }
}

// 选择下载目录
const selectDownloadDir = async () => {
  try {
    const response = await window.pywebview.api.select_directory()
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result && result.status === 'success' && result.data) {
      configStore.chrome.downloadDir = result.data
      await configStore.updateConfigItem('chrome.downloadDir', result.data)
      ElMessage.success('下载目录设置成功')
    }
  } catch (error) {
    console.error('选择下载目录失败:', error)
    ElMessage.error('选择下载目录失败: ' + error.message)
  }
}

// 检查Chrome实例状态
const checkChromeStatus = async (dir) => {
  try {
    const response = await window.pywebview.api.drssion_controller.check_chrome_status({
      path: `${dir.path}/${dir.name}`,
      port: dir.port
    })
    const data = typeof response === 'string' ? JSON.parse(response) : response
    if (data.status === 'success') {
      runningStatus.value[dir.id] = data.data.running
    }
  } catch (error) {
    console.error('检查Chrome状态失败:', error)
    runningStatus.value[dir.id] = false
  }
}

// 检查所有Chrome实例状态
const checkAllChromeStatus = async () => {
  for (const dir of userDataDirs.value) {
    await checkChromeStatus(dir)
  }
}

// 切换Chrome实例
const toggleChrome = async (dir) => {
  try {
    showLoading(runningStatus.value[dir.id] ? '正在停止Chrome...' : '正在启动Chrome...')

    if (runningStatus.value[dir.id]) {
      // 停止Chrome实例
      const result = await window.pywebview.api.drssion_controller.stop_chrome_profile({
        path: `${dir.path}/${dir.name}`,
        port: dir.port
      })
      const response = typeof result === 'string' ? JSON.parse(result) : result

      if (response.status === 'success') {
        runningStatus.value[dir.id] = false
        ElMessage.success('Chrome实例已停止')
      } else {
        throw new Error(response.message || '停止失败')
      }
    } else {
      // 启动Chrome实例
      const result = await window.pywebview.api.drssion_controller.start_chrome_with_profile({
        path: `${dir.path}/${dir.name}`,
        port: dir.port,
        browser_path: configStore.chrome.default_path,
        extensions_enabled: dir.enableExtensions,
        extensions_path: dir.extensionsPath,
      })
      const response = typeof result === 'string' ? JSON.parse(result) : result

      if (response.status === 'success') {
        runningStatus.value[dir.id] = true
        ElMessage.success('Chrome实例已启动')
      } else {
        throw new Error(response.message || '启动失败')
      }
    }
  } catch (error) {
    console.error('切换Chrome状态失败:', error)
    ElMessage.error(error.message || (runningStatus.value[dir.id] ? '停止失败' : '启动失败'))
  } finally {
    hideLoading()
  }
}

// 处理默认设置变更
const handleDefaultChange = async (row, val) => {
  try {
    if (val) {
      // 将其他目录设为非默认
      userDataDirs.value.forEach(dir => {
        if (dir.id !== row.id) {
          dir.isDefault = false
        }
      })
    }
    await configStore.updateConfigItem('chrome.userDataDirs', configStore.chrome.userDataDirs)
    ElMessage.success('默认设置已更新')
  } catch (error) {
    console.error('更新默认设置失败:', error)
    ElMessage.error('更新失败')
    // 回滚状态
    row.isDefault = !val
  }
}

// 添加用户数据目录
const addUserDataDir = () => {
  userDataDirDialog.value = {
    visible: true,
    isEdit: false,
    form: {
      id: '',
      name: '',
      path: '',
      port: 9222,
      isDefault: false,
      enableExtensions: false,
      extensionsPath: ''
    }
  }
}

// 编辑用户数据目录
const editUserDataDir = (row) => {
  userDataDirDialog.value = {
    visible: true,
    isEdit: true,
    form: { ...row }
  }
}

// 删除用户数据目录
const deleteUserDataDir = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户数据目录 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 如果Chrome正在运行，先停止
    if (runningStatus.value[row.id]) {
      await toggleChrome(row)
    }

    // 从配置中删除
    const index = userDataDirs.value.findIndex(dir => dir.id === row.id)
    if (index > -1) {
      userDataDirs.value.splice(index, 1)
      await configStore.updateConfigItem('chrome.userDataDirs', userDataDirs.value)
      ElMessage.success('删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户数据目录失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 保存用户数据目录
const saveUserDataDir = async () => {
  try {
    await userDataDirFormRef.value.validate()

    const form = userDataDirDialog.value.form

    if (userDataDirDialog.value.isEdit) {
      // 编辑模式
      const index = userDataDirs.value.findIndex(dir => dir.id === form.id)
      if (index > -1) {
        userDataDirs.value[index] = { ...form }
      }
    } else {
      // 新增模式
      form.id = Date.now().toString()
      userDataDirs.value.push({ ...form })
    }

    // 如果设为默认，取消其他默认设置
    if (form.isDefault) {
      userDataDirs.value.forEach(dir => {
        if (dir.id !== form.id) {
          dir.isDefault = false
        }
      })
    }

    await configStore.updateConfigItem('chrome.userDataDirs', configStore.chrome.userDataDirs)
    userDataDirDialog.value.visible = false
    ElMessage.success(userDataDirDialog.value.isEdit ? '编辑成功' : '添加成功')
  } catch (error) {
    console.error('保存用户数据目录失败:', error)
    ElMessage.error('保存失败')
  }
}

// 选择用户数据路径
const selectUserDataPath = async () => {
  try {
    const response = await window.pywebview.api.select_directory()
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result && result.status === 'success' && result.data) {
      userDataDirDialog.value.form.path = result.data
    }
  } catch (error) {
    console.error('选择路径失败:', error)
    ElMessage.error('选择路径失败: ' + error.message)
  }
}

// 选择扩展路径
const selectExtensionsPath = async () => {
  try {
    const response = await window.pywebview.api.select_directory()
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result && result.status === 'success' && result.data) {
      userDataDirDialog.value.form.extensionsPath = result.data
    }
  } catch (error) {
    console.error('选择扩展路径失败:', error)
    ElMessage.error('选择扩展路径失败: ' + error.message)
  }
}

// 生命周期钩子
onMounted(() => {
  checkAllChromeStatus()
  // 定期检查状态
  statusCheckTimer.value = setInterval(checkAllChromeStatus, 5000)
})

onUnmounted(() => {
  if (statusCheckTimer.value) {
    clearInterval(statusCheckTimer.value)
  }
})
</script>

<style lang="scss" scoped>
.chrome-settings {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.settings-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 16px;
  gap: 16px;
}

.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  gap: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }
}

.settings-card {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--el-border-color-light);
  flex-shrink: 0; /* 固定区域不收缩 */

  &.table-container {
    flex: 1; /* 表格容器占用剩余空间 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 0;

    .el-table {
      flex: 1;

      :deep(.el-table__body-wrapper) {
        flex: 1;
        overflow-y: auto;
      }
    }
  }
}

.path-input-group {
  display: flex;
  gap: 8px;
  
  .el-input {
    flex: 1;
  }
}

.table-container {
  padding: 0;
  
  :deep(.el-table) {
    border-radius: 8px;
  }
}
</style>
