<template>
  <div class="feishu-config">
    <div class="settings-section">
      <div class="section-header">
        <h2 class="section-title">飞书配置</h2>
      </div>
      
      <div class="panel-content">
        <div class="settings-card">
          <el-form ref="feishuFormRef" :model="feishuForm" label-width="150px">
            <el-form-item label="App ID" prop="app_id">
              <el-input 
                v-model="feishuForm.app_id" 
                placeholder="请输入飞书应用的 App ID"
                show-password
              />
            </el-form-item>
            <el-form-item label="App Secret" prop="app_secret">
              <el-input 
                v-model="feishuForm.app_secret" 
                placeholder="请输入飞书应用的 App Secret"
                show-password
              />
            </el-form-item>
            <el-form-item label="Encrypt Key" prop="encrypt_key">
              <el-input 
                v-model="feishuForm.encrypt_key" 
                placeholder="请输入加密密钥（可选）"
                show-password
              />
            </el-form-item>
            <el-form-item label="Verification Token" prop="verification_token">
              <el-input 
                v-model="feishuForm.verification_token" 
                placeholder="请输入验证令牌（可选）"
                show-password
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveFeishuConfig">保存配置</el-button>
              <el-button @click="resetFeishuConfig">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 注入依赖
const configStore = inject('configStore')
const showLoading = inject('showLoading')
const hideLoading = inject('hideLoading')

// 飞书配置
const feishuForm = ref({
  app_id: '',
  app_secret: '',
  encrypt_key: '',
  verification_token: ''
})

const feishuFormRef = ref()

// 飞书配置方法
const saveFeishuConfig = async () => {
  try {
    showLoading('正在保存飞书配置...')
    await configStore.updateConfigItem('feishu', feishuForm.value)
    ElMessage.success('飞书配置保存成功')
  } catch (error) {
    console.error('保存飞书配置失败:', error)
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    hideLoading()
  }
}

const resetFeishuConfig = () => {
  feishuForm.value = {
    app_id: '',
    app_secret: '',
    encrypt_key: '',
    verification_token: ''
  }
  ElMessage.success('飞书配置已重置')
}

// 加载配置数据
const loadFeishuConfig = async () => {
  try {
    if (configStore.feishu) {
      feishuForm.value = { ...configStore.feishu }
    }
  } catch (error) {
    console.error('加载飞书配置失败:', error)
  }
}

// 生命周期钩子
onMounted(async () => {
  await loadFeishuConfig()
})
</script>

<style lang="scss" scoped>
.feishu-config {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.settings-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 16px;
  gap: 16px;
}

.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }
}

.settings-card {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--el-border-color-light);
}
</style>
