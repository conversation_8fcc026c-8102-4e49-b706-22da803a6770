<template>
  <div class="git-backup-management">
    <!-- 顶部操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <h2 class="page-title">Git 备份管理</h2>
        <p class="page-description">配置和管理 Git 远程备份</p>
      </div>
      <div class="action-right">
        <el-button 
          type="primary" 
          @click="showConfigDialog = true"
          :icon="Setting"
        >
          Git 配置
        </el-button>
        <el-button 
          type="info" 
          @click="checkGitInstallation"
          :loading="isGitChecking"
          :icon="Search"
        >
          检测 Git
        </el-button>
        <el-button
          type="warning"
          @click="initGitRepo"
          :disabled="!isGitConfigValid || !isGitInstalled"
          :icon="Refresh"
        >
          配置仓库
        </el-button>
        <el-button 
          type="success" 
          @click="showBackupDialog = true"
          :disabled="!isGitConfigValid || !isGitInstalled"
          :loading="isBackingUp"
          :icon="Upload"
        >
          立即备份
        </el-button>
      </div>
    </div>

    <!-- Git 状态卡片 -->
    <div class="status-cards">
      <el-card class="status-card">
        <div class="status-item">
          <div class="status-icon" :class="gitInstallStatus?.installed ? 'success' : 'error'">
            <svg class="status-icon-svg" viewBox="0 0 1024 1024">
              <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"/>
            </svg>
          </div>
          <div class="status-content">
            <div class="status-title">Git 状态</div>
            <div class="status-value">
              {{ gitInstallStatus?.installed ? `已安装 ${gitInstallStatus.version}` : '未安装' }}
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="status-card">
        <div class="status-item">
          <div class="status-icon" :class="gitConfig.repoUrl ? 'primary' : 'warning'">
            <svg class="status-icon-svg" viewBox="0 0 1024 1024">
              <path d="M574 665.4a8.03 8.03 0 0 0-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 0 0-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0L613.8 716.3a8.03 8.03 0 0 0 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 0 0 0 11.3L450 358.7a8.03 8.03 0 0 0 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.7a8.03 8.03 0 0 0 0 11.3l39.8 39.8a8.03 8.03 0 0 0 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 0 0-11.3 0L372.3 598.7a8.03 8.03 0 0 0 0 11.3l39.6 39.6a8.03 8.03 0 0 0 11.3 0l226.4-226.4a8.03 8.03 0 0 0 0-11.3l-39.5-39.6z"/>
            </svg>
          </div>
          <div class="status-content">
            <div class="status-title">远程仓库</div>
            <div class="status-value">{{ gitConfig.repoUrl || '未配置' }}</div>
          </div>
        </div>
      </el-card>

      <el-card class="status-card">
        <div class="status-item">
          <div class="status-icon" :class="gitConfig.backupDir ? 'info' : 'warning'">
            <svg class="status-icon-svg" viewBox="0 0 1024 1024">
              <path d="M880 298.4H521L403.7 186.2a8.15 8.15 0 0 0-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"/>
            </svg>
          </div>
          <div class="status-content">
            <div class="status-title">备份目录</div>
            <div class="status-value">
              {{ gitConfig.backupDir || '未配置' }}
              <div v-if="gitRepoStatus?.is_git_repo" class="status-sub">
                Git仓库 ({{ gitRepoStatus.current_branch || 'main' }} 分支)
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="status-card">
        <div class="status-item">
          <div class="status-icon" :class="gitHistory.length > 0 ? 'info' : 'warning'">
            <svg class="status-icon-svg" viewBox="0 0 1024 1024">
              <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"/>
              <path d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"/>
            </svg>
          </div>
          <div class="status-content">
            <div class="status-title">提交总数</div>
            <div class="status-value">{{ gitHistory.length }} 个</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 备份进度 -->
    <div v-if="gitProgress.visible" class="progress-section">
      <el-card>
        <div class="progress-header">
          <h3>Git 备份进度</h3>
          <el-button size="small" @click="cancelGitBackup" type="danger" plain>取消</el-button>
        </div>
        <el-progress 
          :percentage="gitProgress.percent" 
          :status="gitProgress.status === 'error' ? 'exception' : 'success'"
          :stroke-width="12"
          show-text
        />
        <p class="progress-message">{{ gitProgress.message }}</p>
      </el-card>
    </div>

    <!-- Git 历史记录 -->
    <div class="history-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span class="card-title">Git 提交历史</span>
            <div class="card-actions">
              <el-button 
                size="small" 
                @click="refreshGitHistory"
                :loading="isLoadingHistory"
                :icon="Refresh"
              >
                刷新历史
              </el-button>
            </div>
          </div>
        </template>

        <div class="table-container">
          <el-table
            :data="gitHistory"
            style="width: 100%;"
            border
            stripe
            empty-text="暂无提交记录"
            v-loading="isLoadingHistory"
            row-key="hash"
            :height="320"
          >
            <el-table-column label="提交信息" min-width="200">
              <template #default="{ row }">
                <div class="commit-cell">
                  <div class="commit-message">{{ row.message }}</div>
                  <div class="commit-hash">{{ row.hash.substring(0, 8) }}</div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="作者" width="120">
              <template #default="{ row }">
                <span class="author-text">{{ row.author }}</span>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button
                    type="info"
                    size="small"
                    @click="viewCommitDetails(row)"
                    :icon="View"
                  >
                    详情
                  </el-button>
                  <el-button
                    type="primary"
                    size="small"
                    @click="restoreFromGit(row)"
                    :icon="RefreshRight"
                  >
                    恢复
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <!-- Git 配置对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      title="Git 备份配置"
      width="700px"
      :close-on-click-modal="false"
    >
      <el-form :model="gitConfig" label-width="120px" ref="configFormRef">
        <el-form-item label="Git 仓库 URL" required>
          <el-input
            v-model="gitConfig.repoUrl"
            placeholder="例如: https://github.com/user/repo.git"
          />
        </el-form-item>

        <el-form-item label="认证方式">
          <el-radio-group v-model="gitConfig.authType">
            <el-radio value="password">用户名和密码</el-radio>
            <el-radio value="token">访问令牌</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-if="gitConfig.authType === 'password'">
          <el-form-item label="用户名" required>
            <el-input
              v-model="gitConfig.username"
              placeholder="Git 用户名"
            />
          </el-form-item>
          
          <el-form-item label="密码" required>
            <div class="path-input-group">
              <el-input
                v-model="gitConfig.password"
                placeholder="Git 密码"
                show-password
              />
              <el-button 
                type="primary" 
                @click="testGitCredentials"
                :disabled="!canTestConnection"
                :icon="Connection"
              >
                测试连接
              </el-button>
            </div>
          </el-form-item>
        </template>

        <template v-else>
          <el-form-item label="用户名" required>
            <el-input
              v-model="gitConfig.tokenUsername"
              placeholder="Git 用户名"
            />
          </el-form-item>
          
          <el-form-item label="访问令牌" required>
            <div class="path-input-group">
              <el-input
                v-model="gitConfig.token"
                placeholder="Git 个人访问令牌"
                show-password
              />
              <el-button 
                type="primary" 
                @click="testGitCredentials"
                :disabled="!canTestConnection"
                :icon="Connection"
              >
                测试连接
              </el-button>
            </div>
          </el-form-item>
        </template>

        <el-form-item label="备份目录" required>
          <div class="path-input-group">
            <el-input
              v-model="gitConfig.backupDir"
              placeholder="选择需要备份的目录"
              readonly
            />
            <el-button @click="selectGitBackupDir" type="primary" :icon="Folder">
              选择
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="自动备份">
          <div class="backup-options">
            <el-checkbox v-model="gitConfig.autoBackup">
              <span>启用自动备份</span>
              <el-tooltip content="自动备份将按设定间隔定期执行" placement="top">
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </el-checkbox>
          </div>
        </el-form-item>

        <el-form-item v-if="gitConfig.autoBackup" label="备份间隔">
          <el-select v-model="gitConfig.backupInterval" placeholder="选择备份间隔">
            <el-option label="每30分钟" :value="30" />
            <el-option label="每小时" :value="60" />
            <el-option label="每2小时" :value="120" />
            <el-option label="每6小时" :value="360" />
            <el-option label="每12小时" :value="720" />
            <el-option label="每天" :value="1440" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showConfigDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="saveGitConfig"
            :disabled="!isGitConfigValid"
          >
            保存配置
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 备份对话框 -->
    <el-dialog
      v-model="showBackupDialog"
      title="Git 备份"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="backupForm" label-width="100px">
        <el-form-item label="提交信息" required>
          <el-input
            v-model="backupForm.message"
            placeholder="请输入提交信息"
            type="textarea"
            :rows="3"
          />
        </el-form-item>

        <el-form-item label="标签名称">
          <el-input
            v-model="backupForm.tagName"
            placeholder="可选：为此次备份添加标签（如：v1.0.0, backup-20250108）"
            @input="validateTagName"
          />
          <div class="form-tip">
            标签名只能包含字母、数字、下划线、连字符和点号
          </div>
        </el-form-item>

        <el-form-item label="强制推送">
          <el-checkbox v-model="backupForm.force">
            强制推送（谨慎使用）
          </el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBackupDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="executeGitBackup"
            :disabled="!backupForm.message.trim()"
          >
            开始备份
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 提交详情对话框 -->
    <el-dialog
      v-model="showDetailsDialog"
      title="提交详情"
      width="800px"
    >
      <div v-loading="detailsLoading" class="commit-details">
        <pre class="diff-content">{{ commitDetails }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, inject, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting,
  Search,
  Refresh,
  Upload,
  Monitor,
  Link,
  FolderOpened,
  Clock,
  View,
  RefreshRight,
  Connection,
  Folder,
  QuestionFilled
} from '@element-plus/icons-vue'

// 注入依赖
const configStore = inject('configStore')
const showLoading = inject('showLoading')
const hideLoading = inject('hideLoading')

// 响应式数据
const showConfigDialog = ref(false)
const showBackupDialog = ref(false)
const showDetailsDialog = ref(false)
const isGitChecking = ref(false)
const isLoadingHistory = ref(false)
const isBackingUp = ref(false)
const detailsLoading = ref(false)
const commitDetails = ref('')

// Git 状态
const gitInstallStatus = ref(null)
const isGitInstalled = ref(false)
const gitHistory = ref([])
const gitRepoStatus = ref(null)

// Git 配置
const gitConfig = ref({
  repoUrl: '',
  authType: 'token',
  username: '',
  password: '',
  token: '',
  tokenUsername: '',
  backupDir: '',
  autoBackup: false,
  backupInterval: 60
})

// 备份表单
const backupForm = ref({
  message: '',
  tagName: '',
  force: false
})

// Git 进度
const gitProgress = ref({
  visible: false,
  percent: 0,
  message: '',
  status: 'normal'
})

// 计算属性
const isGitConfigValid = computed(() => {
  const config = gitConfig.value
  if (!config.repoUrl || !config.backupDir) return false

  if (config.authType === 'password') {
    return config.username && config.password
  } else {
    return config.token && config.tokenUsername
  }
})

const canTestConnection = computed(() => {
  const config = gitConfig.value
  if (!config.repoUrl) return false

  if (config.authType === 'password') {
    return config.username && config.password
  } else {
    return config.token && config.tokenUsername
  }
})

// 方法
const loadGitConfig = async () => {
  try {
    if (configStore.git) {
      gitConfig.value = { ...configStore.git }
    }
  } catch (error) {
    console.error('加载Git配置失败:', error)
  }
}

const saveGitConfig = async () => {
  try {
    // 保存每个配置项
    await configStore.updateConfigItem('git.repoUrl', gitConfig.value.repoUrl)
    await configStore.updateConfigItem('git.authType', gitConfig.value.authType)
    await configStore.updateConfigItem('git.username', gitConfig.value.username)
    await configStore.updateConfigItem('git.password', gitConfig.value.password)
    await configStore.updateConfigItem('git.token', gitConfig.value.token)
    await configStore.updateConfigItem('git.tokenUsername', gitConfig.value.tokenUsername)
    await configStore.updateConfigItem('git.backupDir', gitConfig.value.backupDir)
    await configStore.updateConfigItem('git.autoBackup', gitConfig.value.autoBackup)
    await configStore.updateConfigItem('git.backupInterval', gitConfig.value.backupInterval)

    showConfigDialog.value = false
    ElMessage.success('Git配置保存成功')

    // 刷新历史记录
    await refreshGitHistory()
  } catch (error) {
    console.error('保存Git配置失败:', error)
    ElMessage.error('保存Git配置失败：' + error.message)
  }
}

const checkGitInstallation = async () => {
  try {
    isGitChecking.value = true

    const response = await window.pywebview.api.check_git_installation()
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      gitInstallStatus.value = result.data
      isGitInstalled.value = result.data.installed

      if (result.data.installed) {
        ElMessage.success(`Git已安装: ${result.data.version}`)
      } else {
        ElMessage.warning('未检测到Git安装')
      }
    } else {
      throw new Error(result.message || 'Git检测失败')
    }
  } catch (error) {
    console.error('检测Git失败:', error)
    ElMessage.error('检测Git失败：' + error.message)
    gitInstallStatus.value = { installed: false }
    isGitInstalled.value = false
  } finally {
    isGitChecking.value = false
  }
}

const selectGitBackupDir = async () => {
  try {
    const response = await window.pywebview.api.select_directory()
    const result = typeof response === 'string' ? JSON.parse(response) : response
    if (result && result.status === 'success' && result.data) {
      gitConfig.value.backupDir = result.data
    }
  } catch (error) {
    console.error('选择备份目录失败:', error)
    ElMessage.error('选择目录失败: ' + error.message)
  }
}

const testGitCredentials = async () => {
  try {
    showLoading('正在测试Git连接...')

    const params = {
      repo_url: gitConfig.value.repoUrl,
      auth_type: gitConfig.value.authType,
      username: gitConfig.value.username,
      password: gitConfig.value.password,
      token: gitConfig.value.token,
      token_username: gitConfig.value.tokenUsername
    }

    const response = await window.pywebview.api.test_git_credentials(params)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('Git连接测试成功')
    } else {
      throw new Error(result.message || 'Git连接测试失败')
    }
  } catch (error) {
    console.error('Git连接测试失败:', error)
    ElMessage.error('Git连接测试失败：' + error.message)
  } finally {
    hideLoading()
  }
}

const initGitRepo = async () => {
  try {
    // 首先检查目录是否已经是Git仓库
    showLoading('正在检查Git仓库状态...')

    const checkParams = {
      backup_dir: gitConfig.value.backupDir
    }

    const checkResponse = await window.pywebview.api.check_git_repository_status(checkParams)
    const checkResult = typeof checkResponse === 'string' ? JSON.parse(checkResponse) : checkResponse

    hideLoading()

    if (checkResult.status === 'success' && checkResult.data?.is_git_repo) {
      // 已经是Git仓库
      const repoInfo = checkResult.data
      let message = `此目录已经是Git仓库！

📁 仓库路径: ${gitConfig.value.backupDir}
🌿 当前分支: ${repoInfo.current_branch || '未知'}
📊 提交数量: ${repoInfo.commit_count || 0} 个
🔗 远程仓库: ${repoInfo.remote_url || '未配置'}

是否要重新配置远程仓库连接？`

      await ElMessageBox.confirm(message, '仓库已存在', {
        confirmButtonText: '重新配置远程仓库',
        cancelButtonText: '取消',
        type: 'info',
        dangerouslyUseHTMLString: false
      })

      // 用户选择重新配置远程仓库
      showLoading('正在重新配置远程仓库...')
    } else {
      // 不是Git仓库，需要初始化
      await ElMessageBox.confirm(
        `将在以下目录初始化Git仓库：

📁 目录: ${gitConfig.value.backupDir}
🔗 远程仓库: ${gitConfig.value.repoUrl}

⚠️ 注意事项：
• 现有配置文件将被保护，不会受到影响
• 将创建 .git 目录来管理版本控制
• 所有现有文件将被添加到首次提交中

确定要继续吗？`,
        '初始化Git仓库',
        {
          confirmButtonText: '确定初始化',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      )

      showLoading('正在初始化Git仓库...')
    }

    const params = {
      repo_url: gitConfig.value.repoUrl,
      auth_type: gitConfig.value.authType,
      username: gitConfig.value.username,
      password: gitConfig.value.password,
      token: gitConfig.value.token,
      token_username: gitConfig.value.tokenUsername,
      backup_dir: gitConfig.value.backupDir,
      preserve_existing_files: true // 保护现有文件
    }

    const response = await window.pywebview.api.init_git_repository(params)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      ElMessage.success('Git仓库配置成功')
      await checkGitRepoStatus() // 更新仓库状态
      await refreshGitHistory()
    } else {
      throw new Error(result.message || '初始化失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('初始化Git仓库失败:', error)
      ElMessage.error('Git仓库配置失败：' + error.message)
    }
  } finally {
    hideLoading()
  }
}

const executeGitBackup = async () => {
  try {
    isBackingUp.value = true
    showBackupDialog.value = false

    // 显示进度
    gitProgress.value = {
      visible: true,
      percent: 0,
      message: '正在准备Git备份...',
      status: 'normal'
    }

    // 验证和清理标签名称
    let cleanTagName = ''
    if (backupForm.value.tagName && backupForm.value.tagName.trim()) {
      cleanTagName = backupForm.value.tagName.trim()
        .replace(/[^a-zA-Z0-9._-]/g, '') // 移除无效字符
        .replace(/^[.-]/, '') // 不能以点或连字符开头
        .replace(/[.-]$/, '') // 不能以点或连字符结尾
        .substring(0, 50) // 限制长度
    }

    const params = {
      repo_url: gitConfig.value.repoUrl,
      auth_type: gitConfig.value.authType,
      username: gitConfig.value.username,
      password: gitConfig.value.password,
      token: gitConfig.value.token,
      token_username: gitConfig.value.tokenUsername,
      backup_dir: gitConfig.value.backupDir,
      commit_message: backupForm.value.message,
      tag_name: cleanTagName, // 使用清理后的标签名
      force: backupForm.value.force
    }

    const response = await window.pywebview.api.backup_to_git(params)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      gitProgress.value.percent = 100
      gitProgress.value.status = 'success'
      gitProgress.value.message = 'Git备份完成'

      ElMessage.success('Git备份成功')

      // 清空表单
      backupForm.value = {
        message: '',
        tagName: '',
        force: false
      }

      // 延迟隐藏进度条并刷新历史
      setTimeout(async () => {
        gitProgress.value.visible = false
        await refreshGitHistory()
      }, 2000)
    } else {
      throw new Error(result.message || 'Git备份失败')
    }
  } catch (error) {
    console.error('Git备份失败:', error)
    ElMessage.error('Git备份失败：' + error.message)

    gitProgress.value.status = 'exception'
    gitProgress.value.message = 'Git备份失败：' + error.message

    setTimeout(() => {
      gitProgress.value.visible = false
    }, 3000)
  } finally {
    isBackingUp.value = false
  }
}

const checkGitRepoStatus = async () => {
  if (!gitConfig.value.backupDir) {
    gitRepoStatus.value = null
    return
  }

  try {
    const params = {
      backup_dir: gitConfig.value.backupDir
    }

    const response = await window.pywebview.api.check_git_repository_status(params)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      gitRepoStatus.value = result.data
    } else {
      gitRepoStatus.value = null
    }
  } catch (error) {
    console.error('检查Git仓库状态失败:', error)
    gitRepoStatus.value = null
  }
}

const refreshGitHistory = async () => {
  if (!gitConfig.value.backupDir) {
    gitHistory.value = []
    return
  }

  try {
    isLoadingHistory.value = true

    const params = {
      repo_url: gitConfig.value.repoUrl,
      local_path: gitConfig.value.backupDir,
      count: 50
    }

    const response = await window.pywebview.api.get_git_history(params)
    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      // 处理返回的历史记录，清理哈希值中的引号
      gitHistory.value = (result.history || []).map(commit => ({
        ...commit,
        hash: commit.hash.replace(/"/g, '').trim(),
        message: commit.message.replace(/"/g, '').trim()
      }))
    } else {
      gitHistory.value = []
      if (result.message && !result.message.includes('仓库未初始化')) {
        ElMessage.error('获取Git历史失败：' + result.message)
      }
    }
  } catch (error) {
    console.error('获取Git历史出错:', error)
    gitHistory.value = []
    ElMessage.error('获取Git历史失败：' + (error.message || error.toString()))
  } finally {
    isLoadingHistory.value = false
  }
}

const viewCommitDetails = async (commit) => {
  try {
    showDetailsDialog.value = true
    detailsLoading.value = true
    commitDetails.value = '加载中...'

    const cleanHash = commit.hash.replace(/"/g, '').trim()

    const response = await window.pywebview.api.get_git_commit_details({
      commit_hash: cleanHash,
      repo_path: gitConfig.value.backupDir
    })

    if (response.status === 'success') {
      commitDetails.value = response.diff || '此提交没有变更'
    } else {
      throw new Error(response.message || '获取提交详情失败')
    }
  } catch (error) {
    commitDetails.value = `获取提交详情失败: ${error.message}`
    ElMessage.error(`获取提交详情失败: ${error.message}`)
  } finally {
    detailsLoading.value = false
  }
}

const restoreFromGit = async (commit) => {
  try {
    const cleanHash = commit.hash.replace(/"/g, '').trim()

    await ElMessageBox.confirm(
      `确定要恢复到此版本吗？

提交: ${cleanHash.substring(0, 7)} - ${commit.message}
日期: ${commit.date}
作者: ${commit.author}

⚠️ 警告：此操作会丢失当前未提交的更改！
应用将在恢复完成后自动重启。`,
      '恢复确认',
      {
        confirmButtonText: '确认恢复',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    showLoading('正在从Git恢复...')

    const response = await window.pywebview.api.reset_to_commit({
      backup_dir: gitConfig.value.backupDir,
      commit_hash: cleanHash
    })

    const result = typeof response === 'string' ? JSON.parse(response) : response

    if (result.status === 'success') {
      hideLoading()

      // 显示成功消息和倒计时
      let countdown = 3
      const showCountdown = () => {
        if (countdown > 0) {
          ElMessage({
            message: `恢复成功！应用将在 ${countdown} 秒后重启...`,
            type: 'success',
            duration: 1000
          })
          countdown--
          setTimeout(showCountdown, 1000)
        } else {
          // 执行重启
          window.pywebview.api.restart_application().catch(error => {
            ElMessage.error('重启应用失败，请手动重启')
            console.error('重启失败:', error)
          })
        }
      }
      showCountdown()
    } else {
      throw new Error(result.message || '恢复失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Git恢复失败:', error)
      ElMessage.error('Git恢复失败：' + error.message)
    }
  } finally {
    hideLoading()
  }
}

const cancelGitBackup = () => {
  isBackingUp.value = false
  gitProgress.value.visible = false
  ElMessage.info('Git备份已取消')
}

// 工具方法
const validateTagName = () => {
  if (!backupForm.value.tagName) return

  // Git 标签名规则：只能包含字母、数字、下划线、连字符、点号，不能包含空格和特殊字符
  const validTagName = backupForm.value.tagName
    .replace(/[^a-zA-Z0-9._-]/g, '') // 移除无效字符
    .replace(/^[.-]/, '') // 不能以点或连字符开头
    .replace(/[.-]$/, '') // 不能以点或连字符结尾
    .substring(0, 50) // 限制长度

  if (validTagName !== backupForm.value.tagName) {
    backupForm.value.tagName = validTagName
  }
}

// 生命周期
onMounted(async () => {
  try {
    await loadGitConfig()
    await checkGitInstallation()
    await checkGitRepoStatus()
    await refreshGitHistory()
  } catch (error) {
    console.error('组件初始化失败:', error)
  }
})
</script>

<style scoped lang="scss">
.git-backup-management {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

// 顶部操作栏
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;

  .action-left {
    .page-title {
      margin: 0 0 4px 0;
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .page-description {
      margin: 0;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }

  .action-right {
    display: flex;
    gap: 12px;
  }
}

// 状态卡片
.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  flex-shrink: 0;

  .status-card {
    .status-item {
      display: flex;
      align-items: center;
      gap: 16px;

      .status-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        position: relative;
        transition: all 0.3s ease;

        .status-icon-svg {
          width: 24px;
          height: 24px;
          fill: currentColor;
          transition: fill 0.3s ease;
        }

        &.success {
          background: linear-gradient(135deg, var(--el-color-success-light-9) 0%, var(--el-color-success-light-8) 100%);
          color: var(--el-color-success);
          border: 1px solid var(--el-color-success-light-7);
          box-shadow: 0 2px 8px rgba(103, 194, 58, 0.15);

          .status-icon-svg {
            fill: var(--el-color-success);
            filter: drop-shadow(0 1px 2px rgba(103, 194, 58, 0.3));
          }
        }

        &.primary {
          background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-primary-light-8) 100%);
          color: var(--el-color-primary);
          border: 1px solid var(--el-color-primary-light-7);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);

          .status-icon-svg {
            fill: var(--el-color-primary);
            filter: drop-shadow(0 1px 2px rgba(64, 158, 255, 0.3));
          }
        }

        &.info {
          background: linear-gradient(135deg, var(--el-color-info-light-9) 0%, var(--el-color-info-light-8) 100%);
          color: var(--el-color-info);
          border: 1px solid var(--el-color-info-light-7);
          box-shadow: 0 2px 8px rgba(144, 147, 153, 0.15);

          .status-icon-svg {
            fill: var(--el-color-info);
            filter: drop-shadow(0 1px 2px rgba(144, 147, 153, 0.3));
          }
        }

        &.warning {
          background: linear-gradient(135deg, var(--el-color-warning-light-9) 0%, var(--el-color-warning-light-8) 100%);
          color: var(--el-color-warning);
          border: 1px solid var(--el-color-warning-light-7);
          box-shadow: 0 2px 8px rgba(230, 162, 60, 0.15);

          .status-icon-svg {
            fill: var(--el-color-warning);
            filter: drop-shadow(0 1px 2px rgba(230, 162, 60, 0.3));
          }
        }

        &.error {
          background: linear-gradient(135deg, var(--el-color-error-light-9) 0%, var(--el-color-error-light-8) 100%);
          color: var(--el-color-error);
          border: 1px solid var(--el-color-error-light-7);
          box-shadow: 0 2px 8px rgba(245, 108, 108, 0.15);

          .status-icon-svg {
            fill: var(--el-color-error);
            filter: drop-shadow(0 1px 2px rgba(245, 108, 108, 0.3));
          }
        }

        // 深色主题优化
        @media (prefers-color-scheme: dark) {
          &.success {
            background: linear-gradient(135deg, rgba(103, 194, 58, 0.15) 0%, rgba(103, 194, 58, 0.25) 100%);
            border: 1px solid rgba(103, 194, 58, 0.3);
            box-shadow: 0 2px 12px rgba(103, 194, 58, 0.2);

            .status-icon-svg {
              fill: #67c23a;
              filter: drop-shadow(0 1px 3px rgba(103, 194, 58, 0.4));
            }
          }

          &.primary {
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(64, 158, 255, 0.25) 100%);
            border: 1px solid rgba(64, 158, 255, 0.3);
            box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);

            .status-icon-svg {
              fill: #409eff;
              filter: drop-shadow(0 1px 3px rgba(64, 158, 255, 0.4));
            }
          }

          &.info {
            background: linear-gradient(135deg, rgba(144, 147, 153, 0.15) 0%, rgba(144, 147, 153, 0.25) 100%);
            border: 1px solid rgba(144, 147, 153, 0.3);
            box-shadow: 0 2px 12px rgba(144, 147, 153, 0.2);

            .status-icon-svg {
              fill: #909399;
              filter: drop-shadow(0 1px 3px rgba(144, 147, 153, 0.4));
            }
          }

          &.warning {
            background: linear-gradient(135deg, rgba(230, 162, 60, 0.15) 0%, rgba(230, 162, 60, 0.25) 100%);
            border: 1px solid rgba(230, 162, 60, 0.3);
            box-shadow: 0 2px 12px rgba(230, 162, 60, 0.2);

            .status-icon-svg {
              fill: #e6a23c;
              filter: drop-shadow(0 1px 3px rgba(230, 162, 60, 0.4));
            }
          }

          &.error {
            background: linear-gradient(135deg, rgba(245, 108, 108, 0.15) 0%, rgba(245, 108, 108, 0.25) 100%);
            border: 1px solid rgba(245, 108, 108, 0.3);
            box-shadow: 0 2px 12px rgba(245, 108, 108, 0.2);

            .status-icon-svg {
              fill: #f56c6c;
              filter: drop-shadow(0 1px 3px rgba(245, 108, 108, 0.4));
            }
          }
        }
      }

      .status-content {
        flex: 1;

        .status-title {
          font-size: 14px;
          color: var(--el-text-color-regular);
          margin-bottom: 4px;
        }

        .status-value {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          word-break: break-all;

          .status-sub {
            font-size: 12px;
            font-weight: 400;
            color: var(--el-color-success);
            margin-top: 2px;
          }
        }
      }
    }
  }
}

// 进度区域
.progress-section {
  flex-shrink: 0;

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
  }

  .progress-message {
    margin: 12px 0 0 0;
    font-size: 14px;
    color: var(--el-text-color-regular);
  }
}

// 历史区域
.history-section {
  flex: 1;
  overflow: hidden;
  min-height: 0;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      overflow: hidden;
      padding: 0;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);

    .card-title {
      font-size: 18px;
      font-weight: 600;
    }

    .card-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .table-container {
    height: calc(100% - 80px);
    padding: 16px 20px 20px;
    overflow: hidden;

    .commit-cell {
      .commit-message {
        font-weight: 500;
        margin-bottom: 4px;
        color: var(--el-text-color-primary);
      }

      .commit-hash {
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 12px;
        color: var(--el-text-color-placeholder);
        background: var(--el-fill-color-light);
        padding: 2px 6px;
        border-radius: 4px;
        display: inline-block;
      }
    }

    .author-text {
      font-size: 13px;
      color: var(--el-text-color-regular);
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }
}

// 对话框
.path-input-group {
  display: flex;
  gap: 8px;

  .el-input {
    flex: 1;
  }
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-top: 4px;
  line-height: 1.4;
}

.backup-options {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .el-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;

    .help-icon {
      color: var(--el-color-info);
      cursor: help;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 提交详情
.commit-details {
  .diff-content {
    background: var(--el-fill-color-lighter);
    padding: 16px;
    border-radius: 6px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 13px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 400px;
    overflow-y: auto;
  }
}
</style>
