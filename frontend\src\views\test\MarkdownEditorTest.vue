<template>
  <div class="markdown-editor-test">
    <div class="test-header">
      <h2>高级Markdown编辑器测试</h2>
      <div class="controls">
        <el-button-group>
          <el-button 
            :type="currentMode === 'edit' ? 'primary' : ''"
            @click="currentMode = 'edit'"
          >
            编辑模式
          </el-button>
          <el-button 
            :type="currentMode === 'mindmap' ? 'primary' : ''"
            @click="currentMode = 'mindmap'"
          >
            思维导图
          </el-button>
        </el-button-group>
        
        <el-input
          v-model="documentTitle"
          placeholder="文档标题"
          style="width: 200px; margin-left: 16px"
        />
      </div>
    </div>

    <div class="test-content">
      <el-empty description="AdvancedMarkdownEditor 组件已被移除" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'


// 响应式数据
const currentMode = ref('edit')
const documentTitle = ref('测试文档')
const markdownContent = ref(`# 测试文档

这是一个测试文档，用于验证高级Markdown编辑器的功能。

## 第一章：基础功能

### 1.1 文本编辑
支持基本的markdown语法编辑。

### 1.2 思维导图
可以将markdown内容转换为思维导图。

## 第二章：AI功能

### 2.1 内容生成
AI可以帮助生成内容。

### 2.2 子节点扩展
AI可以为节点生成子内容。

## 第三章：高级特性

### 3.1 拖拽功能
节点支持自由拖拽。

### 3.2 一键排版
支持多种布局算法。

### 3.3 样式美化
精美的视觉效果。
`)

// 事件处理
const handleContentChange = () => {
  console.log('内容已变化')
}

const handleSave = (data) => {
  console.log('保存文档:', data)
  ElMessage.success('文档已保存')
}
</script>

<style lang="scss" scoped>
.markdown-editor-test {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);

  .test-header {
    padding: 16px;
    border-bottom: 1px solid var(--el-border-color-light);
    background: var(--el-bg-color-page);
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      color: var(--el-text-color-primary);
    }

    .controls {
      display: flex;
      align-items: center;
    }
  }

  .test-content {
    flex: 1;
    overflow: hidden;
  }
}
</style>
