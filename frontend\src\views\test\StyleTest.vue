<template>
  <div class="style-test-page">
    <div class="test-header">
      <h1>样式污染测试页面</h1>
      <p>用于测试页面间样式隔离是否正常工作</p>
    </div>
    
    <div class="test-content">
      <div class="test-section">
        <h2>导航测试</h2>
        <div class="nav-buttons">
          <el-button type="primary" @click="goToWriting">
            前往写作页面
          </el-button>
          <el-button type="success" @click="goToTimeline">
            前往时间线页面
          </el-button>
          <el-button type="info" @click="refreshStyles">
            刷新样式
          </el-button>
        </div>
      </div>
      
      <div class="test-section">
        <h2>样式测试元素</h2>
        <div class="test-elements">
          <el-card class="test-card">
            <template #header>
              <div class="card-header">
                <span>测试卡片</span>
                <el-button type="primary" size="small">操作</el-button>
              </div>
            </template>
            <p>这是一个测试卡片，用于检查样式是否正常。</p>
            <div class="test-buttons">
              <el-button type="primary">主要按钮</el-button>
              <el-button type="success">成功按钮</el-button>
              <el-button type="warning">警告按钮</el-button>
              <el-button type="danger">危险按钮</el-button>
            </div>
          </el-card>
          
          <el-card class="test-card">
            <template #header>
              <span>表单测试</span>
            </template>
            <el-form>
              <el-form-item label="输入框">
                <el-input v-model="testInput" placeholder="请输入内容" />
              </el-form-item>
              <el-form-item label="选择器">
                <el-select v-model="testSelect" placeholder="请选择">
                  <el-option label="选项1" value="1" />
                  <el-option label="选项2" value="2" />
                </el-select>
              </el-form-item>
              <el-form-item label="文本域">
                <el-input 
                  v-model="testTextarea" 
                  type="textarea" 
                  :rows="3"
                  placeholder="请输入多行文本"
                />
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </div>
      
      <div class="test-section">
        <h2>样式状态检查</h2>
        <div class="style-info">
          <p><strong>当前页面容器类:</strong> {{ currentPageClass }}</p>
          <p><strong>检测到的样式污染:</strong> {{ stylePollutionDetected ? '是' : '否' }}</p>
          <p><strong>Webkit内核:</strong> {{ isWebkitBrowser ? '是' : '否' }}</p>
          <p><strong>样式隔离状态:</strong> {{ styleIsolationActive ? '激活' : '未激活' }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { isWebkit, refreshWritingPageStyles, refreshTimelinePageStyles } from '@/utils/styleIsolation'

const router = useRouter()

// 测试数据
const testInput = ref('')
const testSelect = ref('')
const testTextarea = ref('')

// 样式状态检查
const currentPageClass = ref('')
const stylePollutionDetected = ref(false)
const styleIsolationActive = ref(false)

// 计算属性
const isWebkitBrowser = computed(() => isWebkit())

// 检查样式状态
const checkStyleStatus = () => {
  // 检查当前页面容器类
  const writingContainer = document.querySelector('.writing-page-container')
  const timelineContainer = document.querySelector('.timeline-page-container')
  
  if (writingContainer && writingContainer.offsetParent !== null) {
    currentPageClass.value = 'writing-page-container'
  } else if (timelineContainer && timelineContainer.offsetParent !== null) {
    currentPageClass.value = 'timeline-page-container'
  } else {
    currentPageClass.value = '无'
  }
  
  // 检查样式污染
  const pageContainers = document.querySelectorAll('[class*="-page-container"]')
  stylePollutionDetected.value = pageContainers.length > 1
  
  // 检查样式隔离是否激活
  styleIsolationActive.value = document.querySelector('.writing-page-container, .timeline-page-container') !== null
}

// 导航方法
const goToWriting = () => {
  router.push('/book/写作')
}

const goToTimeline = () => {
  // 假设有一个书籍ID
  router.push({
    name: 'bookTimeline',
    params: { id: 'test-book-id' },
    query: { title: '测试书籍' }
  })
}

const refreshStyles = () => {
  if (currentPageClass.value === 'writing-page-container') {
    refreshWritingPageStyles()
    ElMessage.success('写作页面样式已刷新')
  } else if (currentPageClass.value === 'timeline-page-container') {
    refreshTimelinePageStyles()
    ElMessage.success('时间线页面样式已刷新')
  } else {
    ElMessage.info('当前不在需要样式隔离的页面')
  }
  
  // 重新检查样式状态
  setTimeout(checkStyleStatus, 100)
}

// 生命周期
onMounted(() => {
  checkStyleStatus()
  
  // 定期检查样式状态
  setInterval(checkStyleStatus, 2000)
})
</script>

<style lang="scss" scoped>
.style-test-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  
  .test-header {
    text-align: center;
    margin-bottom: 32px;
    
    h1 {
      color: var(--el-color-primary);
      margin-bottom: 8px;
    }
    
    p {
      color: var(--el-text-color-secondary);
    }
  }
  
  .test-content {
    .test-section {
      margin-bottom: 32px;
      
      h2 {
        color: var(--el-text-color-primary);
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid var(--el-border-color-light);
      }
    }
    
    .nav-buttons {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
    }
    
    .test-elements {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
      
      .test-card {
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        
        .test-buttons {
          display: flex;
          gap: 12px;
          flex-wrap: wrap;
          margin-top: 16px;
        }
      }
    }
    
    .style-info {
      background: var(--el-bg-color-overlay);
      padding: 16px;
      border-radius: 8px;
      border: 1px solid var(--el-border-color-light);
      
      p {
        margin: 8px 0;
        
        strong {
          color: var(--el-color-primary);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .style-test-page {
    padding: 16px;
    
    .test-elements {
      grid-template-columns: 1fr;
    }
    
    .nav-buttons {
      justify-content: center;
    }
  }
}
</style>
