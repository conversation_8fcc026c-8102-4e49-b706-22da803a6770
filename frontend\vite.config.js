import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import path from 'path'
import vueScriptMerger from './plugins/vue-script-merger'
import { execSync } from 'child_process'
import obfuscator from 'vite-plugin-javascript-obfuscator'
// add the following dependencies

import { AntDesignXVueResolver } from 'ant-design-x-vue/resolver';

// MateChat 组件库解析器
const MateChatResolver = () => {
  return {
    type: 'component',
    resolve: (name) => {
      if (name.startsWith('Mc')) {
        return {
          name,
          from: '@matechat/core',
        }
      }
    },
  }
}
// https://vite.dev/config/
export default defineConfig({
  base: './',
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '~': path.resolve(__dirname, 'src'),
      'assets': path.resolve(__dirname, 'src/assets'),
      'components': path.resolve(__dirname, 'src/components'),
      'views': path.resolve(__dirname, 'src/views'),
    },
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
  },

  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver(), AntDesignXVueResolver(), MateChatResolver()],
    }),
    vueScriptMerger({
      // 从多个位置查找脚本文件
      scriptPaths: [
        'scripts',        // 相对于src目录
      ],
      
      // 支持多种扩展名
      extensions: ['.script.js', '.vue.js', '.js'],
      
      // 路径别名(除了vite配置的别名外，可以添加额外的)
      aliases: {
        '@scripts': path.resolve(__dirname, 'src/scripts')
      },
      
      // 调试模式
      debug: true,
      
      // 自定义注入注释
      injectComment: '// 从 {filename} 自动导入',
      
      // 是否优先使用组件同目录下的脚本文件
      useSameDir: true
    }),
    {
      name: 'integrity-manifest',
      closeBundle() {
        // 在构建完成后生成完整性清单并更新代码
        console.log('正在生成文件完整性代码...');
        try {
          // 下面是用来生成网页hash保证不能被修改，单纯根据文件字符
          execSync('python build_integrity_code.py --dir ./statics --output ./PVV.py', {
            stdio: 'inherit',
            cwd: path.resolve(__dirname, '../') // 回到项目根目录
          });
        } catch (error) {
          console.error('生成完整性代码失败:', error);
        }
      }
    }
  ],
  server: {
    port: 13000, // 指定启动端口为3000
    fs: {
      strict: false, // 允许访问项目根目录之外的文件
    },
    proxy: {
      // 代理 Microsoft Edge TTS API
      '/api/tts': {
        target: 'https://speech.platform.bing.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/tts/, ''),
        secure: true,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
          'Accept': '*/*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      }
    }
  },

  build: {
    outDir: '../statics',
    minify: 'esbuild',  // 使用 esbuild 做基础压缩

    target: 'es2020',
    sourcemap: false,
    reportCompressedSize: false,
    
    rollupOptions: {
      input: {
        main: './index.html', // 指向入口文件
      },
      output: {
        // 保持文件名结构但添加内容哈希
        entryFileNames: 'assets/entry-[hash].js',
        chunkFileNames: ({name}) => {
          // 将中文名称转换为拼音或使用模块ID
          const safeChunkName = name
            ? name.replace(/[^\x00-\x7F]/g, '') // 移除非ASCII字符
              .replace(/[^a-zA-Z0-9_-]/g, '-')  // 其他非字母数字替换为连字符
              .replace(/^-+|-+$/g, '')          // 移除开头和结尾的连字符
              .replace(/-{2,}/g, '-')           // 连续的连字符替换为单个
              || 'chunk'                         // 如果为空则使用默认名
            : 'chunk';
          
          return `assets/${safeChunkName}-[hash].js`;
        },
        assetFileNames: (assetInfo) => {
          // 安全的资源文件名处理
          let fileName = assetInfo.name || '';
          const safeName = fileName
            .replace(/[^\x00-\x7F]/g, '')
            .replace(/[^a-zA-Z0-9_.-]/g, '-')
            .replace(/^-+|-+$/g, '')
            .replace(/-{2,}/g, '-')
            || 'asset';
          
          const extType = fileName.split('.').pop();
          
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType)) {
            return `assets/images/${safeName}-[hash][extname]`;
          }
          if (/woff|woff2|eot|ttf|otf/i.test(extType)) {
            return `assets/fonts/${safeName}-[hash][extname]`;
          }
          if (/css/i.test(extType)) {
            return `assets/css/${safeName}-[hash][extname]`;
          }
          return `assets/${safeName}-[hash][extname]`;
        },
        // 暂时禁用手动代码分割，使用Vite默认策略
        // manualChunks: undefined
      }
    },
    chunkSizeWarningLimit: 8000,
  },
  publicPath: './',
  assetsDir: 'static',
})
// 打印具体路径以进行调试
console.log('src目录的绝对路径是：', path.resolve(__dirname, 'src'));