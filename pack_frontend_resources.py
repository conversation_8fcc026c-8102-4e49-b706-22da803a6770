# coding:utf-8
"""
前端资源打包脚本
将 Vue3 打包后的前端资源文件打包成单个 Python 模块
"""

import os
import json
import gzip
import base64
from pathlib import Path
import mimetypes

def get_mime_type(file_path):
    """获取文件的MIME类型"""
    mime_type, _ = mimetypes.guess_type(str(file_path))
    if mime_type:
        return mime_type
    
    # 手动定义一些常见的MIME类型
    ext = file_path.suffix.lower()
    mime_types = {
        '.html': 'text/html; charset=utf-8',
        '.css': 'text/css; charset=utf-8',
        '.js': 'application/javascript; charset=utf-8',
        '.json': 'application/json; charset=utf-8',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon',
        '.woff': 'font/woff',
        '.woff2': 'font/woff2',
        '.ttf': 'font/ttf',
        '.eot': 'application/vnd.ms-fontobject',
        '.otf': 'font/otf'
    }
    return mime_types.get(ext, 'application/octet-stream')

def pack_frontend_resources(frontend_dir, output_file):
    """将前端资源打包为单个Python文件"""
    print(f"开始打包前端资源: {frontend_dir}")
    
    if not os.path.exists(frontend_dir):
        print(f"错误: 前端目录不存在: {frontend_dir}")
        return False
    
    resources = {}
    total_files = 0
    total_size = 0
    compressed_size = 0
    
    # 遍历所有文件
    for file_path in Path(frontend_dir).rglob('*'):
        if file_path.is_file():
            relative_path = str(file_path.relative_to(frontend_dir)).replace('\\', '/')
            
            try:
                with open(file_path, 'rb') as f:
                    content = f.read()
                    original_size = len(content)
                    
                    # 压缩内容
                    compressed = gzip.compress(content)
                    compressed_hex = compressed.hex()
                    
                    resources[relative_path] = {
                        'content': compressed_hex,
                        'mime_type': get_mime_type(file_path),
                        'original_size': original_size,
                        'compressed_size': len(compressed)
                    }
                    
                    total_files += 1
                    total_size += original_size
                    compressed_size += len(compressed)
                    
                    print(f"打包文件: {relative_path} ({original_size} -> {len(compressed)} bytes)")
                    
            except Exception as e:
                print(f"警告: 无法读取文件 {file_path}: {e}")
                continue
    
    # 生成Python模块代码
    module_code = f'''# coding:utf-8
"""
自动生成的前端资源模块
包含 {total_files} 个文件，原始大小: {total_size:,} bytes，压缩后: {compressed_size:,} bytes
压缩率: {(1 - compressed_size/total_size)*100:.1f}%
"""

import gzip
import binascii
from typing import Optional, List

# 资源数据
RESOURCES = {json.dumps(resources, indent=2, ensure_ascii=False)}

def get_resource(path: str) -> Optional[bytes]:
    """
    获取资源文件内容
    
    Args:
        path: 资源文件路径 (相对路径，使用 / 分隔符)
    
    Returns:
        文件内容的字节数据，如果文件不存在返回 None
    """
    # 标准化路径分隔符
    path = path.replace('\\\\', '/').lstrip('/')
    
    if path in RESOURCES:
        try:
            hex_content = RESOURCES[path]['content']
            compressed = binascii.unhexlify(hex_content)
            return gzip.decompress(compressed)
        except Exception as e:
            print(f"解压资源文件失败 {{path}}: {{e}}")
            return None
    return None

def get_mime_type(path: str) -> str:
    """
    获取文件MIME类型
    
    Args:
        path: 资源文件路径
    
    Returns:
        MIME类型字符串
    """
    # 标准化路径分隔符
    path = path.replace('\\\\', '/').lstrip('/')
    
    if path in RESOURCES:
        return RESOURCES[path]['mime_type']
    return 'application/octet-stream'

def list_resources() -> List[str]:
    """
    列出所有可用的资源文件路径
    
    Returns:
        资源文件路径列表
    """
    return list(RESOURCES.keys())

def get_resource_info(path: str) -> Optional[dict]:
    """
    获取资源文件信息
    
    Args:
        path: 资源文件路径
    
    Returns:
        包含文件信息的字典，如果文件不存在返回 None
    """
    # 标准化路径分隔符
    path = path.replace('\\\\', '/').lstrip('/')
    
    if path in RESOURCES:
        info = RESOURCES[path].copy()
        # 移除内容数据，只返回元信息
        info.pop('content', None)
        return info
    return None

# 统计信息
TOTAL_FILES = {total_files}
TOTAL_ORIGINAL_SIZE = {total_size}
TOTAL_COMPRESSED_SIZE = {compressed_size}
COMPRESSION_RATIO = {(1 - compressed_size/total_size)*100:.1f}

def get_stats() -> dict:
    """获取资源包统计信息"""
    return {{
        'total_files': TOTAL_FILES,
        'total_original_size': TOTAL_ORIGINAL_SIZE,
        'total_compressed_size': TOTAL_COMPRESSED_SIZE,
        'compression_ratio': COMPRESSION_RATIO
    }}

if __name__ == '__main__':
    print(f"前端资源模块")
    print(f"文件数量: {{TOTAL_FILES}}")
    print(f"原始大小: {{TOTAL_ORIGINAL_SIZE:,}} bytes")
    print(f"压缩大小: {{TOTAL_COMPRESSED_SIZE:,}} bytes")
    print(f"压缩率: {{COMPRESSION_RATIO:.1f}}%")
    print(f"\\n可用资源:")
    for path in sorted(list_resources())[:10]:  # 只显示前10个
        info = get_resource_info(path)
        print(f"  {{path}} ({{info['mime_type']}})")
    if len(list_resources()) > 10:
        print(f"  ... 还有 {{len(list_resources()) - 10}} 个文件")
'''
    
    # 写入输出文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(module_code)
        
        print(f"\n✅ 成功生成前端资源模块: {output_file}")
        print(f"📊 统计信息:")
        print(f"   - 文件数量: {total_files}")
        print(f"   - 原始大小: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
        print(f"   - 压缩大小: {compressed_size:,} bytes ({compressed_size/1024/1024:.2f} MB)")
        print(f"   - 压缩率: {(1 - compressed_size/total_size)*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 写入输出文件失败: {e}")
        return False

if __name__ == '__main__':
    # 默认配置
    frontend_dir = 'statics'  # Vue3 打包输出目录
    output_file = 'frontend_resources.py'  # 生成的Python模块文件
    
    print("🚀 前端资源打包工具")
    print(f"输入目录: {frontend_dir}")
    print(f"输出文件: {output_file}")
    print("-" * 50)
    
    success = pack_frontend_resources(frontend_dir, output_file)
    
    if success:
        print("\n🎉 打包完成！")
        print(f"现在你可以在代码中使用: from {output_file[:-3]} import get_resource, get_mime_type")
    else:
        print("\n❌ 打包失败！")
