import{_ as sl,r as $,bk as ge,c as nt,w as il,T as ot,o as nl,E as g,az as ol,b,m as p,e as d,V as al,d as l,g as o,t as rl,v as w,B as dl,C as h,ac as J,s as cl,aD as ul,X as S,Y as N,n as ne,F as O,bN as fl,J as pl,h as oe,a7 as P,aB as ve,bA as ml,$ as U,p as B,bC as at,aP as gl,aO as vl,aK as bl,aL as _l,ah as rt,bD as hl,aJ as yl,a5 as Re,ab as wl,bF as Al,aR as Le,aN as Cl,aA as ke,bV as Vl,K as Bl,k as jl,j as $l,ai as Ol,aj as kl,q as Dl,ak as El,al as xl,y as zl,x as dt,av as W,R as ct}from"./entry-BIjVVog3.js";/* empty css                   *//* empty css                    *//* empty css                *//* empty css               *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                   *//* empty css                 *//* empty css                 */const Sl={class:"a2b-container"},Nl={class:"app-header"},Ul={class:"header-top"},Jl={class:"header-title"},Tl={class:"design-info"},Fl={class:"design-history-panel"},Rl={class:"panel-header"},Ll={class:"history-search"},Ml={class:"history-list-container"},Pl={key:0,class:"empty-history"},ql={key:1,class:"history-list"},Gl=["onClick"],Hl={class:"item-content"},Ql={class:"item-title"},Yl={class:"item-meta"},Wl={class:"item-timestamp"},Xl={class:"item-info"},Zl={class:"item-actions"},Il={class:"header-actions"},Kl={class:"config-manager-panel"},es={class:"panel-header"},ts={class:"panel-actions"},ls={class:"config-search"},ss={class:"config-list-container"},is={key:0,class:"empty-config"},ns={key:1,class:"config-list"},os=["onClick"],as={class:"item-content"},rs={class:"item-title"},ds={class:"item-meta"},cs={class:"item-description"},us={class:"item-info"},fs={class:"item-actions"},ps={class:"app-content"},ms={key:0,class:"workflow-guide"},gs={class:"bridge-layout"},vs={class:"scene-panel scene-a"},bs={class:"panel-content"},_s={class:"json-editor"},hs={class:"field-header"},ys={class:"field-content"},ws={key:1,class:"array-editor"},As={key:2,class:"object-array-editor"},Cs={key:0,class:"empty-array-tip"},Vs={class:"object-header"},Bs={class:"prop-header"},js={class:"prop-title"},$s={key:3,class:"object-editor"},Os={class:"prop-header"},ks={class:"prop-title"},Ds={key:0,class:"prop-actions"},Es={class:"bridge-connector"},xs={class:"connector-icon"},zs={class:"scene-panel scene-b"},Ss={class:"panel-content"},Ns={class:"json-editor"},Us={class:"field-header"},Js={class:"field-content"},Ts={key:1,class:"array-editor"},Fs={key:2,class:"object-array-editor"},Rs={key:0,class:"empty-array-tip"},Ls={class:"object-header"},Ms={class:"prop-header"},Ps={class:"prop-title"},qs={key:3,class:"object-editor"},Gs={class:"prop-header"},Hs={class:"prop-title"},Qs={key:0,class:"prop-actions"},Ys={class:"toggle-content"},Ws={class:"toggle-icon"},Xs={class:"toggle-text"},Zs={class:"toggle-title"},Is={key:0,class:"toggle-badge"},Ks={class:"drawer-content"},ei={class:"drawer-header"},ti={class:"section-title"},li={class:"title-icon"},si={class:"guidelines-content"},ii={class:"guideline-header"},ni={class:"guideline-title"},oi={class:"guideline-description"},ai={class:"guideline-actions"},ri={class:"import-dialog-content"},di={key:0,class:"import-error"},ci={class:"dialog-footer"},ui={class:"dialog-custom-header"},fi=["id"],pi={class:"guideline-form"},mi={class:"form-header-section"},gi={class:"form-scrollable-content"},vi={class:"form-section"},bi={class:"guideline-dimensions"},_i={class:"dimension-header"},hi={class:"dimension-name"},yi={class:"dimension-question"},wi={class:"dimension-content"},Ai={class:"form-section"},Ci={class:"dialog-custom-footer"},Vi={class:"template-design-info",style:{"margin-bottom":"15px"}},Bi={class:"dialog-footer"},ji={class:"dialog-footer"},$i={class:"import-dialog-content"},Oi={key:0,class:"import-error"},ki={class:"dialog-footer"},Di={class:"dialog-custom-header"},Ei=["id"],xi={class:"config-editor-form"},zi={class:"form-header-section"},Si={class:"form-scrollable-content"},Ni={class:"form-section"},Ui={class:"fields-list"},Ji={class:"field-order-badge"},Ti={class:"field-header"},Fi={class:"field-name-type"},Ri={class:"field-actions"},Li={key:0,class:"field-children"},Mi={key:0,class:"empty-children"},Pi={class:"field-order-badge child-badge"},qi={class:"field-header"},Gi={class:"field-name-type"},Hi={class:"field-actions"},Qi={key:0,class:"nested-children"},Yi={key:0,class:"empty-children"},Wi={class:"field-order-badge nested-badge"},Xi={class:"field-header"},Zi={class:"field-name-type"},Ii={class:"field-actions"},Ki={class:"dialog-custom-footer"},en={__name:"A2B",setup(tn){const z=$(!1),k=$([]),L=$(null),H=$(!1),Ve=$(""),n=ge({id:"scene_default",name:"剧情无痕设计",description:"",bridgeA:{"读者感受/效果":[{效果:"",手段:""}],人物:[],行动:"",行动影响:[],反转:"",反套路:[]},bridgeB:{"读者感受/效果":[{效果:"",手段:""}],人物:[],行动:"",行动影响:[],反转:"",反套路:[]},guidelines:[]}),Me=nt(()=>{if(!Ve.value.trim())return k.value;const t=Ve.value.toLowerCase().trim();return k.value.filter(e=>{const s=(e.name||"").toLowerCase().includes(t),i=(e.description||"").toLowerCase().includes(t),a=Object.keys(e.bridgeA||{}).some(c=>c.toLowerCase().includes(t));return s||i||a})}),Q=$([]),De=$(""),Be=$(""),Pe=nt(()=>{if(!Be.value.trim())return Q.value;const t=Be.value.toLowerCase().trim();return Q.value.filter(e=>{const s=(e.name||"").toLowerCase().includes(t),i=(e.description||"").toLowerCase().includes(t),a=(e.guidelines||[]).some(c=>(c.title||"").toLowerCase().includes(t)||(c.description||"").toLowerCase().includes(t));return s||i||a})}),ae=$(!1),Ee=$(null),F=ge({name:"",configId:""}),re=$(!1),xe=$(null),T=ge({name:"",description:""}),I=$(!1),E=ge({id:"",name:"",description:"",bridgeA:{},bridgeB:{}}),f=ge({}),de=$(!1);function ut(){de.value=!de.value}const K=$(!1),ce=$(!1),je=$(-1),C=ge({type:"线索铺垫",title:"",description:"",priority:"normal",answers:{physicalCondition:"",motivation:"",obstacle:"",reasonableness:"",implementation:""}}),ft=[{key:"physicalCondition",dimension:"物理条件",question:"道具/环境是否就位？",placeholder:"例：太监服必须存在且可获取"},{key:"motivation",dimension:"行为动机+时机",question:"何必须现在做？",placeholder:"例：为什么角色此时必须这样做？"},{key:"obstacle",dimension:"阻碍清除方案",question:"为何不能选其他方案？",placeholder:"例：不穿太监服无法混入皇宫"},{key:"reasonableness",dimension:"对比设计",question:"如何让读者认同？",placeholder:"例：对比其他选择的劣势"},{key:"implementation",dimension:"如何达成",question:"具体如何铺垫实现？",placeholder:"例：在前文提及太监服的获取途径"}];function pt(){C.type="线索铺垫",C.title="",C.description="",C.priority="normal",Object.keys(C.answers).forEach(t=>{C.answers[t]=""}),ce.value=!1,je.value=-1,K.value=!0}function mt(){if(!C.title.trim()){g.warning("请输入铺垫标题");return}if(!C.description.trim()){g.warning("请输入完整描述");return}const t=Object.values(C.answers).filter(s=>s.trim()).join(`

`);C.description.trim()===""&&t&&(C.description=t);const e={type:C.type,title:C.title,description:C.description,priority:C.priority,answers:{...C.answers}};ce.value&&je.value>=0?(n.guidelines[je.value]=e,g.success("铺垫更新成功")):(n.guidelines.push(e),g.success("铺垫添加成功")),fe(),K.value=!1}const be=$(!1),_e=$(""),X=$(""),ue=$(!1),he=$(!1),ye=$(""),ee=$(""),we=$(!1),te=$(null),le=$(!0);il(()=>[n.bridgeA,n.bridgeB,n.guidelines],()=>{le.value||(H.value=!0,te.value&&clearTimeout(te.value),te.value=setTimeout(async()=>{await fe()},5e3))},{deep:!0}),ot(()=>{te.value&&clearTimeout(te.value)});function gt(t){if(!t)return"未知时间";const e=new Date(t),s=e.getFullYear(),i=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0"),c=String(e.getHours()).padStart(2,"0"),_=String(e.getMinutes()).padStart(2,"0");return`${s}-${i}-${a} ${c}:${_}`}function qe(){F.name="",F.configId=n.id,ae.value=!0}function vt(){ct(()=>{Ee.value&&Ee.value.focus()})}async function ze(){if(!F.name.trim()){g.warning("请输入设计名称");return}try{z.value=!0;const t=k.value.find(s=>s.id===F.configId)||k.value[0];if(!t){g.error("未找到有效的配置模板"),z.value=!1;return}const e={id:"design_"+Date.now(),name:F.name.trim(),description:"",timestamp:Date.now(),bridgeA:JSON.parse(JSON.stringify(t.bridgeA||t.sceneA||{})),bridgeB:JSON.parse(JSON.stringify(t.bridgeB||t.sceneB||{})),guidelines:[],isDesign:!0};Object.assign(n,e),L.value=e.id,await pe(!0),ae.value=!1,g.success(`新设计"${e.name}"已创建并保存到您的设计列表中`)}catch(t){console.error("创建新设计失败",t),g.error("创建新设计失败: "+(t.message||"未知错误"))}finally{z.value=!1}}function bt(){T.name=n.name||"",T.description=n.description||"",ct(()=>{xe.value&&xe.value.focus()})}async function Se(){if(!T.name.trim()){g.warning("请输入设计名称");return}try{n.name=T.name.trim(),n.description=T.description.trim(),await pe(!0),re.value=!1,g.success("设计已保存")}catch(t){console.error("保存设计失败",t),g.error("保存设计失败: "+(t.message||"未知错误"))}}async function Ne(){try{z.value=!0;const t=await window.pywebview.api.book_controller.get_scene_designs();t.success&&t.data?(Q.value=t.data.sort((e,s)=>(s.timestamp||0)-(e.timestamp||0)),console.log("从后端API加载历史设计列表成功:",Q.value)):(console.warn("后端API返回的设计历史为空:",t),Q.value=[])}catch(t){console.error("加载历史设计列表失败",t),g.error("加载历史设计列表失败: "+(t.message||"未知错误")),Q.value=[]}finally{z.value=!1}}async function _t(t){try{await W.confirm("确定要删除这个设计吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}),await ht(t)}catch(e){if(e==="cancel")return;console.error("确认删除设计失败:",e)}}async function ht(t){try{const e=await window.pywebview.api.book_controller.delete_scene_design(t);if(!e.success)throw new Error(e.message||"删除失败：API调用出错");await Ne(),t===L.value&&(Q.value.length>0?await Je(Q.value[0].id):Ge()),g.success("设计已删除")}catch(e){console.error("删除设计失败:",e),g.error(e.message||"删除设计失败")}}function Ge(){n.id="scene_default",n.name="剧情无痕设计",n.description="",n.bridgeA={"读者感受/效果":[{效果:"",手段:""}],人物:[],行动:"",行动影响:[],反转:"",反套路:[]},n.bridgeB={"读者感受/效果":[{效果:"",手段:""}],人物:[],行动:"",行动影响:[],反转:"",反套路:[]},n.guidelines=[],L.value="scene_default",H.value=!1,le.value=!1}function yt(){let t="新建模板",e=t,s=1;for(;k.value.some(a=>a.name===e);)e=`${t} ${s}`,s++;const i={id:"config_"+Date.now(),name:e,description:"用于剧情设计的结构模板",bridgeA:JSON.parse(JSON.stringify(n.bridgeA)),bridgeB:JSON.parse(JSON.stringify(n.bridgeB))};Ke(i.id,i)}function wt(t){try{H.value?W.confirm("当前有未保存的更改，切换配置将丢失这些更改。是否继续？","未保存的更改",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"}).then(()=>{Ue(t)}).catch(()=>{}):Ue(t)}catch(e){console.error("选择配置失败:",e),g.error("选择配置失败: "+(e.message||"未知错误"))}}function Ue(t){const e=k.value.find(s=>s.id===t);e&&(Object.assign(n,{id:e.id,name:e.name,description:e.description,bridgeA:JSON.parse(JSON.stringify(e.bridgeA||{})),bridgeB:JSON.parse(JSON.stringify(e.bridgeB||{})),guidelines:[]}),L.value=e.id,H.value=!1,le.value=!1,g.success(`已加载模板: ${e.name}`))}async function At(t){try{const e=k.value.find(s=>s.id===t);if(!e){g.error("未找到指定的模板");return}F.configId=t,F.name="",g.info(`您正在基于模板"${e.name||"未命名模板"}"创建新的设计`),ae.value=!0}catch(e){console.error("选择模板失败:",e),g.error("选择模板失败: "+(e.message||"未知错误"))}}async function Ct(t){try{await W.confirm("确定要删除这个模板吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}),await Vt(t)}catch(e){if(e==="cancel")return;console.error("确认删除模板失败:",e)}}async function Vt(t){try{if(t==="scene_default"){g.warning("无法删除默认模板");return}t===L.value&&g.info("删除当前使用的模板后将切换到默认模板");const s=await window.pywebview.api.book_controller.delete_bridge_config(null,t),i=typeof s=="string"?JSON.parse(s):s;if(i.success||i.status==="success"){if(k.value=k.value.filter(a=>a.id!==t),t===L.value){const a=k.value.find(c=>c.id==="scene_default")||k.value[0];a?Ue(a.id):Ge()}g.success("模板已删除")}else throw new Error(i.message||"删除失败")}catch(e){console.error("删除配置失败:",e),g.error("删除配置失败: "+(e.message||"未知错误"))}}async function Je(t){if(t)try{if(z.value=!0,H.value&&!await W.confirm("当前设计有未保存的更改，切换将丢失这些更改。是否继续？","未保存的更改",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"}).catch(()=>!1)){z.value=!1;return}const e=await window.pywebview.api.book_controller.load_scene_design(t);if(!e.success){g.error(e.message||"加载设计失败"),z.value=!1;return}const s=e.data,i={...s,bridgeA:s.bridgeA||s.sceneA||{},bridgeB:s.bridgeB||s.sceneB||{}};Object.assign(n,i),L.value=i.id,De.value=i.id,H.value=!1,le.value=!1,g.success(`已加载设计: ${i.name||i.id}`),console.log("加载历史设计成功:",i)}catch(e){console.error("加载历史设计失败",e),g.error("加载历史设计失败: "+(e.message||"未知错误"))}finally{z.value=!1}}nl(async()=>{try{let t=await window.pywebview.api.book_controller.get_bridge_configs();if(typeof t=="string")try{t=JSON.parse(t)}catch(e){console.error("解析后端响应失败:",e)}if((t.success||t.status==="success")&&t.data&&t.data.length>0){k.value=t.data;const e=t.data[0],s={...e,bridgeA:e.bridgeA||e.sceneA||{},bridgeB:e.bridgeB||e.sceneB||{}};Object.assign(n,s),L.value=s.id,console.log("加载配置成功:",n)}else console.log("未检测到现有配置，使用默认配置"),g.info("未检测到现有配置，使用默认配置");if(await Ne(),Q.value.length>0){const e=Q.value[0];De.value=e.id,await Je(e.id)}else await Pt()}catch(t){console.error("加载配置失败",t),g.warning("加载配置失败，使用默认配置")}Ot(),setTimeout(()=>{le.value=!1},1e3),document.addEventListener("keydown",He),window.addEventListener("beforeunload",st)}),ot(()=>{te.value&&clearTimeout(te.value),document.removeEventListener("keydown",He),window.removeEventListener("beforeunload",st)});function He(t){(t.ctrlKey||t.metaKey)&&t.key==="s"&&(t.preventDefault(),pe(!0))}async function fe(){try{if(z.value=!0,n.isDesign||n.id&&n.id.startsWith("design_"))return console.log("当前是设计而非配置模板，跳过保存到配置中"),await pe(!0);const t=JSON.parse(JSON.stringify(n));t.sceneA=t.bridgeA||{},t.sceneB=t.bridgeB||{};const e=t.id||null;console.log("准备保存配置:",e,t);let s=await window.pywebview.api.book_controller.save_bridge_config(e,t);if(typeof s=="string")try{s=JSON.parse(s)}catch(i){throw console.error("解析后端响应失败:",i),new Error("后端响应格式不正确")}if(s.success||s.status==="success"){const i=s.data||{},a=k.value.findIndex(c=>c.id===(i.id||e));return a!==-1?k.value[a]={...i}:k.value.push({...i}),i.id&&(L.value=i.id,n.id=i.id),i.sceneA&&(n.sceneA=i.sceneA,n.bridgeA=i.sceneA),i.sceneB&&(n.sceneB=i.sceneB,n.bridgeB=i.sceneB),H.value=!1,le.value=!1,console.log("配置保存成功:",i),!0}else return console.error("保存配置失败:",s),g.error(`保存失败: ${s.message||"未知错误"}`),!1}catch(t){return console.error("保存配置失败:",t),g.error(`保存失败: ${t.message||"未知错误"}`),z.value=!1,!1}finally{z.value=!1}}async function Bt(t){try{let e;if(t&&t!==n.id){const i=k.value.find(a=>a.id===t);if(!i)throw new Error("未找到指定的配置");e={...i}}else await fe(),e={...n};const s=JSON.stringify(e,null,2);await window.pywebview.api.copy_to_clipboard(s),g({message:`配置 "${e.name}" 已复制到剪贴板`,type:"success",duration:2e3})}catch(e){console.error("导出失败:",e),g.error("导出失败: "+(e.message||"复制到剪贴板失败，请检查浏览器权限"))}}function jt(){_e.value="",X.value="",be.value=!0}async function $t(){X.value="",ue.value=!0;try{if(!_e.value.trim()){X.value="请输入JSON配置",ue.value=!1;return}const t=JSON.parse(_e.value);if(!t.bridgeA&&!t.sceneA&&!t.bridgeB&&!t.sceneB){X.value="导入的数据格式不正确，缺少必要的剧情信息",ue.value=!1;return}const e={...t,bridgeA:t.bridgeA||t.sceneA||{},bridgeB:t.bridgeB||t.sceneB||{},sceneA:t.sceneA||t.bridgeA||{},sceneB:t.sceneB||t.bridgeB||{}};Object.assign(n,e),n.id||(n.id="scene_"+Date.now()),await fe()?(be.value=!1,g.success("配置导入成功")):X.value="保存导入的配置失败，请检查数据格式",ue.value=!1}catch(t){console.error("导入失败:",t),X.value=t.message||"无法解析JSON数据",ue.value=!1}}function Ot(){n.sceneA&&!n.bridgeA&&(n.bridgeA=n.sceneA),n.sceneB&&!n.bridgeB&&(n.bridgeB=n.sceneB),n.bridgeA||(n.bridgeA={"读者感受/效果":[{效果:"",手段:""}],人物:[],行动:"",行动影响:[],反转:"",反套路:[]}),n.bridgeB||(n.bridgeB={"读者感受/效果":[{效果:"",手段:""}],人物:[],行动:"",行动影响:[],反转:"",反套路:[]}),n.guidelines||(n.guidelines=[])}function Qe(t,e){Array.isArray(n[t][e])&&n[t][e].push("")}function Ye(t,e,s){Array.isArray(n[t][e])&&n[t][e].splice(s,1)}function $e(t,e){if(Array.isArray(n[t][e])&&n[t][e].length>0){const s=n[t][e][0],i={};for(const a in s)i[a]="";n[t][e].push(i)}}function We(t,e,s){Array.isArray(n[t][e])&&(n[t][e].splice(s,1),n[t][e].length===0&&e==="读者感受/效果"&&n[t][e].push({效果:"",手段:""}))}function Xe(t,e){typeof n[t][e]=="object"&&n[t][e]!==null&&!Array.isArray(n[t][e])&&W.prompt("请输入属性名","添加属性",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/\S+/,inputErrorMessage:"属性名不能为空"}).then(({value:s})=>{if(s in n[t][e]){g.warning("属性名已存在");return}n[t][e][s]="",g({type:"success",message:"属性添加成功"})}).catch(()=>{})}function Ze(t,e,s){typeof n[t][e]=="object"&&n[t][e]!==null&&!Array.isArray(n[t][e])&&W.confirm(`确定要删除属性 "${s}" 吗？`,"删除确认",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(()=>{delete n[t][e][s],Object.keys(n[t][e]).length===0&&(n[t][e].默认=""),g({type:"success",message:"属性已删除"})}).catch(()=>{})}function M(t){const e=n.bridgeA&&n.bridgeA[t],s=n.bridgeB&&n.bridgeB[t];return Array.isArray(e)||Array.isArray(s)}function Ie(t,e,s){const i=k.value.find(c=>c.id===n.id);if(!i)return!0;const a=i[t]&&i[t][e];return!a||typeof a!="object"||Array.isArray(a)?!0:!(s in a)}function kt(){W.confirm("确定要交换A和B剧情的数据吗？此操作将互换两侧的所有内容。","交换剧情数据",{confirmButtonText:"确认交换",cancelButtonText:"取消",type:"warning"}).then(()=>{const t=JSON.parse(JSON.stringify(n.bridgeA)),e=JSON.parse(JSON.stringify(n.bridgeB));n.bridgeA=e,n.bridgeB=t,fe(),g({type:"success",message:"A/B剧情数据交换成功",duration:2e3})}).catch(()=>{g({type:"info",message:"已取消交换操作",duration:1500})})}function Ke(t,e=null){let s=e;!s&&t&&(s=k.value.find(i=>i.id===t)),s||(s=n),E.id=s.id,E.name=s.name,E.description=s.description||"",e?(E.bridgeA=e.bridgeA||{},E.bridgeB=e.bridgeB||{}):(E.bridgeA={},E.bridgeB={}),Dt(s),I.value=!0}function Dt(t){Object.keys(f).forEach(i=>{delete f[i]});const e=Object.keys(t.bridgeA||{}).length>0?t.bridgeA:t.bridgeB;let s=1;Object.keys(e||{}).forEach(i=>{const a=e[i],c=`field_${s++}`;if(typeof a=="string")f[c]={name:i,type:"string"};else if(typeof a=="number")f[c]={name:i,type:"number"};else if(typeof a=="boolean")f[c]={name:i,type:"boolean"};else if(Array.isArray(a)){if(f[c]={name:i,type:"array",children:{}},a.length>0&&typeof a[0]=="object"){let _=1;Object.keys(a[0]||{}).forEach(A=>{const m=`child_${_++}`,y=a[0][A];f[c].children[m]={name:A,type:typeof y}})}}else if(typeof a=="object"&&a!==null){f[c]={name:i,type:"object",children:{}};let _=1;Object.keys(a).forEach(A=>{const m=`child_${_++}`,y=a[A];if(f[c].children[m]={name:A,type:typeof y=="object"&&y!==null&&!Array.isArray(y)?"object":Array.isArray(y)?"array":typeof y},typeof y=="object"&&y!==null&&!Array.isArray(y)){f[c].children[m].children={};let q=1;Object.keys(y).forEach(se=>{const ie=`nested_${q++}`,Y=y[se];f[c].children[m].children[ie]={name:se,type:typeof Y}})}if(Array.isArray(y)&&y.length>0&&typeof y[0]=="object"){f[c].children[m].children={};let q=1;Object.keys(y[0]).forEach(se=>{const ie=`nested_${q++}`,Y=y[0][se];f[c].children[m].children[ie]={name:se,type:typeof Y}})}})}})}function et(){const t=`field_${Date.now()}`;f[t]={name:"新字段",type:"string"}}function Et(t,e){f[t]&&(f[t].name=e)}function xt(t,e){if(f[t]){const s=f[t],i=s.type;if(s.type=e,e==="object"||e==="array"){if(s.children||(s.children={}),Object.keys(s.children).length===0){const a=`child_${Date.now()}`;s.children[a]={name:e==="object"?"默认字段":"项目",type:"string"},g({message:`已自动添加一个默认${e==="object"?"字段":"项目"}，您可以根据需要修改`,type:"info",duration:3e3})}}else i==="object"||i==="array"?W.confirm("切换为简单类型将丢失所有子字段数据，确定要继续吗？","类型转换警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{delete s.children}).catch(()=>{s.type=i}):delete s.children}}function zt(t){W.confirm("确定删除此字段吗？","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{delete f[t],g({message:"字段已删除",type:"success",duration:1500})})}function tt(t){if(f[t]&&(f[t].type==="object"||f[t].type==="array")){f[t].children||(f[t].children={});const e=`child_${Date.now()}`;f[t].children[e]={name:"子字段",type:"string"}}}function St(t,e,s){f[t]&&f[t].children&&f[t].children[e]&&(f[t].children[e].name=s)}function Nt(t,e,s){if(f[t]&&f[t].children&&f[t].children[e]){const i=f[t].children[e];i.type=s,s==="object"||s==="array"?i.children||(i.children={}):delete i.children}}function Ut(t,e){f[t]&&f[t].children&&f[t].children[e]&&(delete f[t].children[e],g({message:"子字段已删除",type:"success",duration:1500}))}async function Jt(){const t=E.name.trim();if(!t){g.warning("请输入模板名称");return}if(k.value.some(a=>a.name===t&&a.id!==E.id)){g.warning("模板名称已存在，请使用其他名称");return}const s={},i={};try{Te(f,s,i);const a={id:E.id,name:t,description:E.description.trim(),bridgeA:s,bridgeB:i};if(a.id!==n.id){const c=await window.pywebview.api.book_controller.save_bridge_config(a.id,a),_=typeof c=="string"?JSON.parse(c):c;if(_.success||_.status==="success"){const A=k.value.findIndex(m=>m.id===a.id);A!==-1?k.value[A]={...a}:k.value.push({...a}),I.value=!1,g.success("配置保存成功")}else throw new Error(_.message||"保存失败")}else{n.name=t,n.description=E.description.trim();const c={},_={};Object.keys(s).forEach(m=>{typeof s[m]=="object"&&!Array.isArray(s[m])?(c[m]={},Object.keys(s[m]).forEach(y=>{c[m][y]=n.bridgeA&&n.bridgeA[m]&&n.bridgeA[m][y]!==void 0?n.bridgeA[m][y]:s[m][y]})):Array.isArray(s[m])?c[m]=n.bridgeA&&n.bridgeA[m]?[...n.bridgeA[m]]:[...s[m]]:c[m]=n.bridgeA&&n.bridgeA[m]!==void 0?n.bridgeA[m]:s[m]}),Object.keys(i).forEach(m=>{typeof i[m]=="object"&&!Array.isArray(i[m])?(_[m]={},Object.keys(i[m]).forEach(y=>{_[m][y]=n.bridgeB&&n.bridgeB[m]&&n.bridgeB[m][y]!==void 0?n.bridgeB[m][y]:i[m][y]})):Array.isArray(i[m])?_[m]=n.bridgeB&&n.bridgeB[m]?[...n.bridgeB[m]]:[...i[m]]:_[m]=n.bridgeB&&n.bridgeB[m]!==void 0?n.bridgeB[m]:i[m]}),n.bridgeA=c,n.bridgeB=_,await fe()?(I.value=!1,g.success("配置保存成功")):g.error("保存配置失败")}}catch(a){console.error("处理配置数据失败",a),g.error("配置保存失败: "+(a.message||"配置格式错误，请检查字段设置"))}}function Te(t,e,s){Object.entries(t).map(([a,c])=>({key:a,...c})).forEach(a=>{if(!a.name||a.name.trim()==="")return;const c=a.name.trim();if(a.type==="string")e[c]="",s[c]="";else if(a.type==="number")e[c]=0,s[c]=0;else if(a.type==="boolean")e[c]=!1,s[c]=!1;else if(a.type==="object")e[c]={},s[c]={},a.children&&Object.keys(a.children).length>0&&Te(a.children,e[c],s[c]),Object.keys(e[c]).length===0&&(e[c].默认="",s[c].默认="");else if(a.type==="array"&&(e[c]=[],s[c]=[],a.children&&Object.keys(a.children).length>0)){const _={},A={};Te(a.children,_,A),Object.keys(_).length>0?(e[c].push(_),s[c].push(A)):(e[c].push(""),s[c].push(""))}})}const Tt=t=>{const e=n.guidelines[t];C.title=e.title||"",C.description=e.description||"",C.type=e.type||"线索铺垫",C.priority=e.priority||"normal",e.answers?Object.keys(C.answers).forEach(s=>{C.answers[s]=e.answers[s]||""}):Object.keys(C.answers).forEach(s=>{C.answers[s]=""}),ce.value=!0,je.value=t,K.value=!0},Ft=t=>{W.confirm("确定要删除这条铺垫指南吗？","删除确认",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(()=>{n.guidelines.splice(t,1),g.success("铺垫指南已删除")}).catch(()=>{})};function lt(t,e){if(f[t]&&f[t].children&&f[t].children[e]){const s=f[t].children[e];if(s.type!=="object"&&s.type!=="array")return;s.children||(s.children={});const i=`nested_${Date.now()}`;s.children[i]={name:"嵌套字段",type:"string"}}}function Rt(t,e,s,i){f[t]&&f[t].children&&f[t].children[e]&&f[t].children[e].children&&f[t].children[e].children[s]&&(f[t].children[e].children[s].name=i)}function Lt(t,e,s,i){f[t]&&f[t].children&&f[t].children[e]&&f[t].children[e].children&&f[t].children[e].children[s]&&(f[t].children[e].children[s].type=i)}function Mt(t,e,s){f[t]&&f[t].children&&f[t].children[e]&&f[t].children[e].children&&f[t].children[e].children[s]&&(delete f[t].children[e].children[s],g({message:"嵌套子字段已删除",type:"success",duration:1500}))}async function Pt(){try{z.value=!0;let t=await window.pywebview.api.book_controller.load_scene_design(n.id);if(typeof t=="string")try{t=JSON.parse(t)}catch(e){console.error("解析后端响应失败:",e),z.value=!1;return}t.success&&t.data&&(t.data.bridgeA?n.bridgeA=t.data.bridgeA:t.data.sceneA&&(n.bridgeA=t.data.sceneA),t.data.bridgeB?n.bridgeB=t.data.bridgeB:t.data.sceneB&&(n.bridgeB=t.data.sceneB),t.data.guidelines&&(n.guidelines=t.data.guidelines),g.success("设计已加载"))}catch(t){console.error("加载设计失败",t),g.error("加载设计失败: "+(t.message||"未知错误"))}finally{z.value=!1}}async function qt(){try{let t=`# ${n.name||"剧情无痕设计"}

`;t+=`## 当前剧情 (A)

`,Object.entries(n.bridgeA).forEach(([e,s])=>{t+=`### ${e}
`,typeof s=="string"?t+=`${s||"(空)"}

`:Array.isArray(s)&&(s.length===0?t+=`(空列表)

`:typeof s[0]=="object"?s.forEach((i,a)=>{t+=`- 项目${a+1}:
`,Object.entries(i).forEach(([c,_])=>{t+=`  * ${c}: ${_||"(空)"}
`}),t+=`
`}):(s.forEach(i=>{t+=`- ${i||"(空项)"}
`}),t+=`
`))}),t+=`## 目标剧情 (B)

`,Object.entries(n.bridgeB).forEach(([e,s])=>{t+=`### ${e}
`,typeof s=="string"?t+=`${s||"(空)"}

`:Array.isArray(s)&&(s.length===0?t+=`(空列表)

`:typeof s[0]=="object"?s.forEach((i,a)=>{t+=`- 项目${a+1}:
`,Object.entries(i).forEach(([c,_])=>{t+=`  * ${c}: ${_||"(空)"}
`}),t+=`
`}):(s.forEach(i=>{t+=`- ${i||"(空项)"}
`}),t+=`
`))}),t+=`## 无痕铺垫

`,n.guidelines&&n.guidelines.length>0?n.guidelines.forEach((e,s)=>{t+=`### 铺垫 ${s+1}: ${e.title||"无标题"}
`,t+=`- 类型: ${e.type||"未分类"}
`,t+=`- 优先级: ${e.priority==="high"?"高":"普通"}
`,t+=`- 描述: ${e.description||"(无描述)"}

`}):t+=`(暂无铺垫)

`,await window.pywebview.api.copy_to_clipboard(t),g.success("内容已复制到剪贴板")}catch(t){console.error("复制内容失败",t),g.error("复制到剪贴板失败，请检查浏览器权限")}}async function pe(t=!1){if(!n.name&&t){T.name="",T.description=n.description||"",re.value=!0;return}if(!t){T.name=n.name||"",T.description=n.description||"",re.value=!0;return}try{z.value=!0,n.timestamp||(n.timestamp=Date.now()),(!n.id||n.id==="scene_default")&&(n.id="design_"+Date.now());const e={id:n.id,name:n.name||`设计_${new Date().toLocaleDateString()}`,description:n.description||"",bridgeA:n.bridgeA,bridgeB:n.bridgeB,guidelines:n.guidelines,timestamp:Date.now()},s=await window.pywebview.api.book_controller.save_scene_design(e);if(!s.success)throw new Error(s.message||"调用后端API保存设计失败");L.value=e.id,De.value=e.id,await Ne(),H.value=!1,le.value=!1,g.success("设计已保存")}catch(e){console.error("保存设计失败",e),g.error("保存失败: "+(e.message||"未知错误"))}finally{z.value=!1}}function st(t){if(H.value){const e="您有未保存的更改，确定离开吗？";return t.returnValue=e,e}}function Gt(t){const e=Object.keys(f),s=e.indexOf(t);if(s>0){const i=e[s-1],a={};e.forEach((c,_)=>{_===s-1?a[c]={...f[t]}:_===s?a[c]={...f[i]}:a[c]=f[c]}),Object.keys(f).forEach(c=>{delete f[c]}),Object.keys(a).forEach(c=>{f[c]=a[c]}),g.success("字段已上移")}}function Ht(t){const e=Object.keys(f),s=e.indexOf(t);if(s<e.length-1){const i=e[s+1],a={};e.forEach((c,_)=>{_===s?a[c]={...f[i]}:_===s+1?a[c]={...f[t]}:a[c]=f[c]}),Object.keys(f).forEach(c=>{delete f[c]}),Object.keys(a).forEach(c=>{f[c]=a[c]}),g.success("字段已下移")}}function Qt(t,e){const s=f[t];if(!s||!s.children)return;const i=Object.keys(s.children),a=i.indexOf(e);if(a>0){const c=i[a-1],_={};i.forEach((A,m)=>{m===a-1?_[A]={...s.children[e]}:m===a?_[A]={...s.children[c]}:_[A]=s.children[A]}),Object.keys(s.children).forEach(A=>{delete s.children[A]}),Object.keys(_).forEach(A=>{s.children[A]=_[A]}),g.success("子字段已上移")}}function Yt(t,e){const s=f[t];if(!s||!s.children)return;const i=Object.keys(s.children),a=i.indexOf(e);if(a<i.length-1){const c=i[a+1],_={};i.forEach((A,m)=>{m===a?_[A]={...s.children[c]}:m===a+1?_[A]={...s.children[e]}:_[A]=s.children[A]}),Object.keys(s.children).forEach(A=>{delete s.children[A]}),Object.keys(_).forEach(A=>{s.children[A]=_[A]}),g.success("子字段已下移")}}function Wt(t,e,s){const i=f[t];if(!i||!i.children)return;const a=i.children[e];if(!a||!a.children)return;const c=Object.keys(a.children),_=c.indexOf(s);if(_>0){const A=c[_-1],m={};c.forEach((y,q)=>{q===_-1?m[y]={...a.children[s]}:q===_?m[y]={...a.children[A]}:m[y]=a.children[y]}),Object.keys(a.children).forEach(y=>{delete a.children[y]}),Object.keys(m).forEach(y=>{a.children[y]=m[y]}),g.success("嵌套字段已上移")}}function Xt(t,e,s){const i=f[t];if(!i||!i.children)return;const a=i.children[e];if(!a||!a.children)return;const c=Object.keys(a.children),_=c.indexOf(s);if(_<c.length-1){const A=c[_+1],m={};c.forEach((y,q)=>{q===_?m[y]={...a.children[A]}:q===_+1?m[y]={...a.children[s]}:m[y]=a.children[y]}),Object.keys(a.children).forEach(y=>{delete a.children[y]}),Object.keys(m).forEach(y=>{a.children[y]=m[y]}),g.success("嵌套字段已下移")}}function Zt(t){switch(t){case"copyJson":It();break;case"importDesign":Kt();break}}async function It(){try{const t={id:n.id,name:n.name,description:n.description||"",bridgeA:n.bridgeA,bridgeB:n.bridgeB,guidelines:n.guidelines,timestamp:Date.now()},e=JSON.stringify(t,null,2);await window.pywebview.api.copy_to_clipboard(e),g({message:"设计JSON已复制到剪贴板",type:"success",duration:2e3})}catch(t){console.error("复制设计JSON失败",t),g.error("复制到剪贴板失败，请检查浏览器权限")}}function Kt(){ye.value="",ee.value="",he.value=!0}async function el(){ee.value="",we.value=!0;try{if(!ye.value.trim()){ee.value="请输入JSON设计数据",we.value=!1;return}const t=JSON.parse(ye.value);if(!t.bridgeA&&!t.sceneA&&!t.bridgeB&&!t.sceneB){ee.value="导入的数据格式不正确，缺少必要的剧情信息",we.value=!1;return}const e={...t,id:"design_"+Date.now(),timestamp:Date.now(),bridgeA:t.bridgeA||t.sceneA||{},bridgeB:t.bridgeB||t.sceneB||{},guidelines:t.guidelines||[]};Object.assign(n,e),L.value=e.id,await pe(!0),he.value=!1,g.success("设计导入成功并已保存")}catch(t){console.error("导入设计失败:",t),ee.value=t.message||"无法解析JSON数据"}finally{we.value=!1}}return(t,e)=>{const s=dl,i=rl,a=cl,c=ul,_=pl,A=ml,m=ol("Switch"),y=_l,q=bl,se=yl,ie=Bl,Y=jl,G=Dl,Oe=kl,x=xl,Ae=El,it=Ol,Ce=$l,tl=zl,ll=Al;return p(),b("div",Sl,[d("div",Nl,[d("div",Ul,[d("div",Jl,[e[32]||(e[32]=d("div",{class:"title-main"},[d("h2",null,"剧情无痕设计"),d("div",{class:"title-subtitle"},"A2B 升级优化设计工具")],-1)),l(A,{placement:"bottom-start",width:380,trigger:"click","popper-class":"history-design-popover"},{reference:o(()=>[d("span",Tl,[w(O(n.name||"未命名设计")+" ",1),l(s,{class:"selector-icon"},{default:o(()=>[l(h(ve))]),_:1})])]),default:o(()=>[d("div",Fl,[d("div",Rl,[e[31]||(e[31]=d("span",{class:"panel-title"},"历史设计",-1)),l(i,{type:"primary",size:"small",plain:"",onClick:qe},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1}),e[30]||(e[30]=w(" 新建设计 "))]),_:1})]),d("div",Ll,[l(a,{modelValue:Be.value,"onUpdate:modelValue":e[0]||(e[0]=r=>Be.value=r),placeholder:"搜索设计...","prefix-icon":"Search",clearable:""},null,8,["modelValue"])]),d("div",Ml,[Pe.value.length===0?(p(),b("div",Pl,[l(c,{description:"暂无历史设计","image-size":60})])):(p(),b("div",ql,[(p(!0),b(S,null,N(Pe.value,r=>(p(),b("div",{key:r.id,class:ne(["history-item",{active:r.id===L.value}]),onClick:u=>Je(r.id)},[d("div",Hl,[d("div",Ql,O(r.name||"未命名设计"),1),d("div",Yl,[d("div",Wl,[l(s,null,{default:o(()=>[l(h(fl))]),_:1}),d("span",null,O(gt(r.timestamp)),1)]),d("div",Xl,[l(_,{size:"small",effect:"plain"},{default:o(()=>[w(O(r.guidelines?.length||0)+" 个铺垫 ",1)]),_:2},1024)])])]),d("div",Zl,[l(i,{type:"danger",size:"small",circle:"",plain:"",onClick:oe(u=>_t(r.id),["stop"])},{default:o(()=>[l(s,null,{default:o(()=>[l(h(P))]),_:1})]),_:2},1032,["onClick"])])],10,Gl))),128))]))])])]),_:1})]),d("div",Il,[l(i,{type:"primary",onClick:qe,size:"default",class:"action-btn"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1}),e[33]||(e[33]=w(" 新建 "))]),_:1}),l(i,{type:"primary",onClick:e[1]||(e[1]=r=>pe(!0)),size:"default",class:"action-btn"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(at))]),_:1}),e[34]||(e[34]=w(" 保存 "))]),_:1}),l(i,{type:"warning",onClick:kt,size:"default",class:"action-btn"},{default:o(()=>[l(s,null,{default:o(()=>[l(m)]),_:1}),e[35]||(e[35]=w(" 交换剧情 "))]),_:1}),l(i,{type:"success",onClick:qt,size:"default",class:"action-btn"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(gl))]),_:1}),e[36]||(e[36]=w(" 复制 "))]),_:1}),l(se,{trigger:"click",onCommand:Zt},{dropdown:o(()=>[l(q,null,{default:o(()=>[l(y,{command:"copyJson"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(rt))]),_:1}),e[38]||(e[38]=w(" 复制设计JSON "))]),_:1}),l(y,{command:"importDesign"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(hl))]),_:1}),e[39]||(e[39]=w(" 导入设计JSON "))]),_:1})]),_:1})]),default:o(()=>[l(i,{type:"info",size:"default",class:"action-btn"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(vl))]),_:1}),e[37]||(e[37]=w(" 更多 ")),l(s,{class:"el-icon--right"},{default:o(()=>[l(h(ve))]),_:1})]),_:1})]),_:1}),l(A,{placement:"bottom-start",width:380,trigger:"click","popper-class":"config-manager-popover"},{reference:o(()=>[l(i,{type:"info",size:"default",class:"action-btn"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(wl))]),_:1}),e[40]||(e[40]=w(" 模板 ")),l(s,{class:"el-icon--right"},{default:o(()=>[l(h(ve))]),_:1})]),_:1})]),default:o(()=>[d("div",Kl,[d("div",es,[e[43]||(e[43]=d("span",{class:"panel-title"},"模板管理",-1)),d("div",ts,[l(i,{type:"primary",size:"small",plain:"",onClick:yt},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1}),e[41]||(e[41]=w(" 新建模板 "))]),_:1}),l(i,{type:"success",size:"small",plain:"",onClick:jt},{default:o(()=>[l(s,null,{default:o(()=>[l(h(rt))]),_:1}),e[42]||(e[42]=w(" 导入模板 "))]),_:1})])]),d("div",ls,[l(a,{modelValue:Ve.value,"onUpdate:modelValue":e[2]||(e[2]=r=>Ve.value=r),placeholder:"搜索模板...","prefix-icon":"Search",clearable:""},null,8,["modelValue"])]),d("div",ss,[Me.value.length===0?(p(),b("div",is,[l(c,{description:"暂无模板","image-size":60})])):(p(),b("div",ns,[(p(!0),b(S,null,N(Me.value,r=>(p(),b("div",{key:r.id,class:ne(["config-item",{active:n.id===r.id}]),onClick:u=>wt(r.id)},[d("div",as,[d("div",rs,O(r.name||"未命名模板"),1),d("div",ds,[d("div",cs,O(r.description||"无描述"),1),d("div",us,[l(_,{size:"small",effect:"plain"},{default:o(()=>[w(O(Object.keys(r.bridgeA||{}).length)+" 个字段 ",1)]),_:2},1024)])])]),d("div",fs,[l(i,{type:"success",size:"small",circle:"",plain:"",onClick:oe(u=>At(r.id),["stop"]),title:"基于此模板创建新设计"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1})]),_:2},1032,["onClick"]),l(i,{type:"primary",size:"small",circle:"",plain:"",onClick:oe(u=>Ke(r.id),["stop"]),title:"编辑模板"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(Re))]),_:1})]),_:2},1032,["onClick"]),l(i,{type:"info",size:"small",circle:"",plain:"",onClick:oe(u=>Bt(r.id),["stop"]),title:"导出模板"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(at))]),_:1})]),_:2},1032,["onClick"]),l(i,{type:"danger",size:"small",circle:"",plain:"",onClick:oe(u=>Ct(r.id),["stop"]),title:"删除模板"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(P))]),_:1})]),_:2},1032,["onClick"])])],10,os))),128))]))])])]),_:1}),H.value?(p(),U(i,{key:0,type:"danger",size:"default",plain:"",class:"unsaved-indicator"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(Re))]),_:1}),e[44]||(e[44]=w(" 未保存 "))]),_:1})):B("",!0)])])]),al((p(),b("div",ps,[de.value?B("",!0):(p(),b("div",ms,e[45]||(e[45]=[Le('<div class="guide-content" data-v-6b002ca7><div class="guide-step" data-v-6b002ca7><span class="step-number" data-v-6b002ca7>1</span><span class="step-text" data-v-6b002ca7>选择模板，定义剧情结构维度</span></div><div class="guide-arrow" data-v-6b002ca7>→</div><div class="guide-step" data-v-6b002ca7><span class="step-number" data-v-6b002ca7>2</span><span class="step-text" data-v-6b002ca7>填写当前剧情(A)和目标剧情(B)</span></div><div class="guide-arrow" data-v-6b002ca7>→</div><div class="guide-step" data-v-6b002ca7><span class="step-number" data-v-6b002ca7>3</span><span class="step-text" data-v-6b002ca7>设计无痕铺垫，实现A到B的升级</span></div></div>',1)]))),d("div",gs,[d("div",vs,[e[50]||(e[50]=Le('<div class="panel-header" data-v-6b002ca7><div class="panel-title-group" data-v-6b002ca7><h3 data-v-6b002ca7>当前剧情 (A)</h3><div class="panel-subtitle" data-v-6b002ca7>现有的剧情设计</div></div><div class="panel-indicator" data-v-6b002ca7><span class="indicator-dot current" data-v-6b002ca7></span></div></div>',1)),d("div",bs,[d("div",_s,[(p(!0),b(S,null,N(n.bridgeA,(r,u)=>(p(),b("div",{key:`bridgeA-${u}`,class:"json-field"},[d("div",hs,O(u),1),d("div",ys,[typeof r=="string"?(p(),U(a,{key:0,modelValue:n.bridgeA[u],"onUpdate:modelValue":j=>n.bridgeA[u]=j,type:"textarea",autosize:{minRows:2,maxRows:10},placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])):Array.isArray(r)&&(r.length===0||typeof r[0]!="object")?(p(),b("div",ws,[(p(!0),b(S,null,N(r,(j,v)=>(p(),b("div",{key:`bridgeA-${u}-${v}`,class:"array-item"},[l(a,{type:"textarea",autosize:{minRows:2,maxRows:8},modelValue:n.bridgeA[u][v],"onUpdate:modelValue":V=>n.bridgeA[u][v]=V,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"]),M(u)?(p(),U(i,{key:0,onClick:V=>Ye("bridgeA",u,v),circle:"",size:"small"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(P))]),_:1})]),_:2},1032,["onClick"])):B("",!0)]))),128)),M(u)?(p(),U(i,{key:0,onClick:j=>Qe("bridgeA",u),class:"add-item-btn"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1}),e[46]||(e[46]=w(" 添加项目 "))]),_:2},1032,["onClick"])):B("",!0)])):Array.isArray(r)&&(r.length===0||typeof r[0]=="object")?(p(),b("div",As,[r.length===0&&M(u)?(p(),b("div",Cs,[l(c,{description:"暂无项目","image-size":60},{default:o(()=>[l(i,{onClick:j=>$e("bridgeA",u),type:"primary",size:"small"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1}),e[47]||(e[47]=w(" 添加第一个项目 "))]),_:2},1032,["onClick"])]),_:2},1024)])):B("",!0),(p(!0),b(S,null,N(r,(j,v)=>(p(),b("div",{key:`bridgeA-${u}-obj-${v}`,class:"object-item"},[d("div",Vs,[d("span",null,"项目 "+O(v+1),1),M(u)?(p(),U(i,{key:0,onClick:V=>We("bridgeA",u,v),circle:"",size:"small"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(P))]),_:1})]),_:2},1032,["onClick"])):B("",!0)]),(p(!0),b(S,null,N(j,(V,R)=>(p(),b("div",{key:`bridgeA-${u}-obj-${v}-${R}`,class:"object-prop"},[d("div",Bs,[d("div",js,O(R),1)]),l(a,{type:"textarea",autosize:{minRows:2,maxRows:8},modelValue:n.bridgeA[u][v][R],"onUpdate:modelValue":D=>n.bridgeA[u][v][R]=D,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])]))),128))]))),128)),M(u)&&r.length>0?(p(),U(i,{key:1,onClick:j=>$e("bridgeA",u),class:"add-item-btn"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1}),e[48]||(e[48]=w(" 添加项目 "))]),_:2},1032,["onClick"])):B("",!0)])):typeof r=="object"&&r!==null&&!Array.isArray(r)?(p(),b("div",$s,[(p(!0),b(S,null,N(r,(j,v)=>(p(),b("div",{key:`bridgeA-${u}-prop-${v}`,class:"object-prop"},[d("div",Os,[d("div",ks,O(v),1),Ie("bridgeA",u,v)?(p(),b("div",Ds,[l(i,{onClick:V=>Ze("bridgeA",u,v),circle:"",size:"small",type:"danger"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(P))]),_:1})]),_:2},1032,["onClick"])])):B("",!0)]),l(a,{type:"textarea",autosize:{minRows:2,maxRows:8},modelValue:n.bridgeA[u][v],"onUpdate:modelValue":V=>n.bridgeA[u][v]=V,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])]))),128)),M(u)?(p(),U(i,{key:0,onClick:j=>Xe("bridgeA",u),class:"add-item-btn"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1}),e[49]||(e[49]=w(" 添加属性 "))]),_:2},1032,["onClick"])):B("",!0)])):B("",!0)])]))),128))])])]),d("div",Es,[e[51]||(e[51]=d("div",{class:"connector-line"},null,-1)),d("div",xs,[l(s,null,{default:o(()=>[l(h(Cl))]),_:1})]),e[52]||(e[52]=d("div",{class:"connector-text"},"升级优化",-1))]),d("div",zs,[e[57]||(e[57]=Le('<div class="panel-header" data-v-6b002ca7><div class="panel-title-group" data-v-6b002ca7><h3 data-v-6b002ca7>目标剧情 (B)</h3><div class="panel-subtitle" data-v-6b002ca7>升级优化后的剧情设计</div></div><div class="panel-indicator" data-v-6b002ca7><span class="indicator-dot target" data-v-6b002ca7></span></div></div>',1)),d("div",Ss,[d("div",Ns,[(p(!0),b(S,null,N(n.bridgeB,(r,u)=>(p(),b("div",{key:`bridgeB-${u}`,class:"json-field"},[d("div",Us,O(u),1),d("div",Js,[typeof r=="string"?(p(),U(a,{key:0,modelValue:n.bridgeB[u],"onUpdate:modelValue":j=>n.bridgeB[u]=j,type:"textarea",autosize:{minRows:2,maxRows:10},placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])):Array.isArray(r)&&(r.length===0||typeof r[0]!="object")?(p(),b("div",Ts,[(p(!0),b(S,null,N(r,(j,v)=>(p(),b("div",{key:`bridgeB-${u}-${v}`,class:"array-item"},[l(a,{type:"textarea",autosize:{minRows:2,maxRows:8},modelValue:n.bridgeB[u][v],"onUpdate:modelValue":V=>n.bridgeB[u][v]=V,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"]),M(u)?(p(),U(i,{key:0,onClick:V=>Ye("bridgeB",u,v),circle:"",size:"small"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(P))]),_:1})]),_:2},1032,["onClick"])):B("",!0)]))),128)),M(u)?(p(),U(i,{key:0,onClick:j=>Qe("bridgeB",u),class:"add-item-btn"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1}),e[53]||(e[53]=w(" 添加项目 "))]),_:2},1032,["onClick"])):B("",!0)])):Array.isArray(r)&&(r.length===0||typeof r[0]=="object")?(p(),b("div",Fs,[r.length===0&&M(u)?(p(),b("div",Rs,[l(c,{description:"暂无项目","image-size":60},{default:o(()=>[l(i,{onClick:j=>$e("bridgeB",u),type:"primary",size:"small"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1}),e[54]||(e[54]=w(" 添加第一个项目 "))]),_:2},1032,["onClick"])]),_:2},1024)])):B("",!0),(p(!0),b(S,null,N(r,(j,v)=>(p(),b("div",{key:`bridgeB-${u}-obj-${v}`,class:"object-item"},[d("div",Ls,[d("span",null,"项目 "+O(v+1),1),M(u)?(p(),U(i,{key:0,onClick:V=>We("bridgeB",u,v),circle:"",size:"small"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(P))]),_:1})]),_:2},1032,["onClick"])):B("",!0)]),(p(!0),b(S,null,N(j,(V,R)=>(p(),b("div",{key:`bridgeB-${u}-obj-${v}-${R}`,class:"object-prop"},[d("div",Ms,[d("div",Ps,O(R),1)]),l(a,{type:"textarea",autosize:{minRows:2,maxRows:8},modelValue:n.bridgeB[u][v][R],"onUpdate:modelValue":D=>n.bridgeB[u][v][R]=D,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])]))),128))]))),128)),M(u)&&r.length>0?(p(),U(i,{key:1,onClick:j=>$e("bridgeB",u),class:"add-item-btn"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1}),e[55]||(e[55]=w(" 添加项目 "))]),_:2},1032,["onClick"])):B("",!0)])):typeof r=="object"&&r!==null&&!Array.isArray(r)?(p(),b("div",qs,[(p(!0),b(S,null,N(r,(j,v)=>(p(),b("div",{key:`bridgeB-${u}-prop-${v}`,class:"object-prop"},[d("div",Gs,[d("div",Hs,O(v),1),Ie("bridgeB",u,v)?(p(),b("div",Qs,[l(i,{onClick:V=>Ze("bridgeB",u,v),circle:"",size:"small",type:"danger"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(P))]),_:1})]),_:2},1032,["onClick"])])):B("",!0)]),l(a,{type:"textarea",autosize:{minRows:2,maxRows:8},modelValue:n.bridgeB[u][v],"onUpdate:modelValue":V=>n.bridgeB[u][v]=V,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])]))),128)),M(u)?(p(),U(i,{key:0,onClick:j=>Xe("bridgeB",u),class:"add-item-btn"},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1}),e[56]||(e[56]=w(" 添加属性 "))]),_:2},1032,["onClick"])):B("",!0)])):B("",!0)])]))),128))])])])]),d("div",{class:"drawer-toggle",onClick:ut},[d("div",Ys,[d("div",Ws,[l(s,{class:ne({"is-rotate":de.value})},{default:o(()=>[l(h(ke))]),_:1},8,["class"])]),d("div",Xs,[d("span",Zs,O(de.value?"收起铺垫设计":"展开铺垫设计"),1),e[58]||(e[58]=d("span",{class:"toggle-subtitle"},"设计从A到B的无痕过渡",-1))]),n.guidelines&&n.guidelines.length>0?(p(),b("div",Is,O(n.guidelines.length),1)):B("",!0)])]),d("div",{class:ne(["drawer-panel",{"drawer-visible":de.value}])},[d("div",Ks,[d("div",ei,[d("div",ti,[d("div",li,[l(s,null,{default:o(()=>[l(h(Vl))]),_:1})]),e[59]||(e[59]=d("div",{class:"title-content"},[d("h3",null,"无痕铺垫设计"),d("div",{class:"title-description"},"设计从当前剧情(A)到目标剧情(B)的升级路径")],-1))]),l(i,{type:"primary",size:"default",class:"add-guideline-btn",onClick:pt},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1}),e[60]||(e[60]=w(" 添加铺垫指南 "))]),_:1})]),d("div",si,[(p(!0),b(S,null,N(n.guidelines,(r,u)=>(p(),b("div",{key:u,class:ne(["guideline-item",{"high-priority":r.priority==="high"}])},[d("div",ii,[d("span",ni,O(r.title),1),l(_,{type:r.priority==="high"?"danger":r.priority==="medium"?"warning":"info",size:"small"},{default:o(()=>[w(O(r.priority==="high"?"高优先级":r.priority==="medium"?"中优先级":"低优先级"),1)]),_:2},1032,["type"])]),d("div",oi,O(r.description),1),d("div",ai,[l(i,{type:"primary",size:"small",text:"",onClick:j=>Tt(u)},{default:o(()=>[l(s,null,{default:o(()=>[l(h(Re))]),_:1}),e[61]||(e[61]=w(" 编辑 "))]),_:2},1032,["onClick"]),l(i,{type:"danger",size:"small",text:"",onClick:j=>Ft(u)},{default:o(()=>[l(s,null,{default:o(()=>[l(h(P))]),_:1}),e[62]||(e[62]=w(" 删除 "))]),_:2},1032,["onClick"])])],2))),128)),!n.guidelines||n.guidelines.length===0?(p(),U(c,{key:0,description:"暂无铺垫指南，点击添加按钮创建","image-size":120})):B("",!0)])])],2)])),[[ll,z.value]]),l(Y,{modelValue:be.value,"onUpdate:modelValue":e[5]||(e[5]=r=>be.value=r),title:"导入剧情配置",width:"600px","lock-scroll":!0,"destroy-on-close":!1},{footer:o(()=>[d("div",ci,[l(i,{onClick:e[4]||(e[4]=r=>be.value=!1),plain:""},{default:o(()=>e[65]||(e[65]=[w("取消")])),_:1}),l(i,{type:"primary",onClick:$t,loading:ue.value},{default:o(()=>e[66]||(e[66]=[w(" 导入 ")])),_:1},8,["loading"])])]),default:o(()=>[d("div",ri,[e[63]||(e[63]=d("p",{class:"import-tip"},"请将剧情配置的JSON字符串粘贴到下方文本框中:",-1)),l(a,{modelValue:_e.value,"onUpdate:modelValue":e[3]||(e[3]=r=>_e.value=r),type:"textarea",autosize:{minRows:8,maxRows:20},placeholder:"粘贴JSON配置文本..."},null,8,["modelValue"]),e[64]||(e[64]=d("div",{class:"import-help"},[d("p",null,[d("i",{class:"el-icon-info"}),w(" 提示：导入会替换当前所有配置内容")])],-1)),X.value?(p(),b("div",di,[l(ie,{title:X.value,type:"error","show-icon":"",closable:!1},null,8,["title"])])):B("",!0)])]),_:1},8,["modelValue"]),l(Y,{modelValue:K.value,"onUpdate:modelValue":e[12]||(e[12]=r=>K.value=r),title:ce.value?"编辑无痕铺垫":"添加无痕铺垫",width:"800px",top:"5vh",class:"guideline-dialog","modal-append-to-body":!0,"destroy-on-close":"","close-on-click-modal":!1,"show-close":!1,fullscreen:""},{header:o(({titleId:r,titleClass:u})=>[d("div",ui,[d("h4",{id:r,class:ne(u)},O(ce.value?"编辑无痕铺垫":"添加无痕铺垫"),11,fi),l(i,{class:"dialog-close-btn",onClick:e[6]||(e[6]=j=>K.value=!1),icon:"Close"})])]),footer:o(()=>[d("div",Ci,[l(i,{onClick:e[11]||(e[11]=r=>K.value=!1)},{default:o(()=>e[70]||(e[70]=[w("取消")])),_:1}),l(i,{type:"primary",onClick:mt},{default:o(()=>[w(O(ce.value?"更新":"保存"),1)]),_:1})])]),default:o(()=>[d("div",pi,[d("div",mi,[l(Ce,{model:C,"label-position":"top"},{default:o(()=>[l(it,{gutter:20},{default:o(()=>[l(Oe,{span:16},{default:o(()=>[l(G,{label:"铺垫标题"},{default:o(()=>[l(a,{modelValue:C.title,"onUpdate:modelValue":e[7]||(e[7]=r=>C.title=r),placeholder:"为铺垫添加简短标题"},null,8,["modelValue"])]),_:1})]),_:1}),l(Oe,{span:8},{default:o(()=>[l(G,{label:"铺垫类型"},{default:o(()=>[l(Ae,{modelValue:C.type,"onUpdate:modelValue":e[8]||(e[8]=r=>C.type=r),placeholder:"选择铺垫类型",class:"full-width"},{default:o(()=>[l(x,{label:"线索铺垫",value:"线索铺垫"}),l(x,{label:"人物特征",value:"人物特征"}),l(x,{label:"情感基调",value:"情感基调"}),l(x,{label:"视角处理",value:"视角处理"}),l(x,{label:"细节设计",value:"细节设计"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),d("div",gi,[d("div",vi,[e[67]||(e[67]=d("h3",{class:"section-title"},"铺垫设计思考",-1)),d("div",bi,[(p(),b(S,null,N(ft,r=>d("div",{key:r.key,class:"dimension-row"},[d("div",_i,[d("div",hi,O(r.dimension),1),d("div",yi,O(r.question),1)]),d("div",wi,[l(a,{type:"textarea",autosize:{minRows:2,maxRows:6},modelValue:C.answers[r.key],"onUpdate:modelValue":u=>C.answers[r.key]=u,placeholder:r.placeholder},null,8,["modelValue","onUpdate:modelValue","placeholder"])])])),64))])]),d("div",Ai,[e[69]||(e[69]=d("h3",{class:"section-title"},"完整描述",-1)),l(Ce,{model:C,"label-position":"top"},{default:o(()=>[l(G,{label:""},{default:o(()=>[l(a,{modelValue:C.description,"onUpdate:modelValue":e[9]||(e[9]=r=>C.description=r),type:"textarea",autosize:{minRows:3,maxRows:8},placeholder:"根据上面的思考，总结完整的铺垫描述"},null,8,["modelValue"])]),_:1}),l(G,{class:"priority-checkbox"},{default:o(()=>[l(tl,{modelValue:C.priority,"onUpdate:modelValue":e[10]||(e[10]=r=>C.priority=r),"true-label":"high","false-label":"normal"},{default:o(()=>e[68]||(e[68]=[d("span",{class:"priority-label"},"标记为高优先级",-1)])),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])])])]),_:1},8,["modelValue","title"]),l(Y,{modelValue:ae.value,"onUpdate:modelValue":e[16]||(e[16]=r=>ae.value=r),title:"基于模板创建新设计",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,onOpened:vt},{footer:o(()=>[d("span",Bi,[l(i,{onClick:e[15]||(e[15]=r=>ae.value=!1)},{default:o(()=>e[72]||(e[72]=[w("取消")])),_:1}),l(i,{type:"primary",onClick:ze,disabled:!F.name},{default:o(()=>e[73]||(e[73]=[w(" 创建设计 ")])),_:1},8,["disabled"])])]),default:o(()=>[d("div",Vi,[l(ie,{type:"info",closable:!1,"show-icon":"",class:"theme-adaptive-alert"},{default:o(()=>e[71]||(e[71]=[d("p",null,"您正在基于模板创建新的设计。这将不会修改模板本身，而是创建一个独立的设计保存到您的设计列表中。",-1)])),_:1})]),l(Ce,{model:F,"label-position":"top",onSubmit:oe(ze,["prevent"])},{default:o(()=>[l(G,{label:"设计名称",required:""},{default:o(()=>[l(a,{ref_key:"designNameInputRef",ref:Ee,modelValue:F.name,"onUpdate:modelValue":e[13]||(e[13]=r=>F.name=r),placeholder:"请输入设计名称",maxlength:"50","show-word-limit":"",onKeyup:dt(ze,["enter"])},null,8,["modelValue"])]),_:1}),l(G,{label:"选择配置模板"},{default:o(()=>[l(Ae,{modelValue:F.configId,"onUpdate:modelValue":e[14]||(e[14]=r=>F.configId=r),placeholder:"请选择配置模板",class:"full-width"},{default:o(()=>[(p(!0),b(S,null,N(k.value,r=>(p(),U(x,{key:r.id,label:r.name||r.id,value:r.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(Y,{modelValue:re.value,"onUpdate:modelValue":e[20]||(e[20]=r=>re.value=r),title:"保存设计",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,onOpened:bt},{footer:o(()=>[d("span",ji,[l(i,{onClick:e[19]||(e[19]=r=>re.value=!1)},{default:o(()=>e[74]||(e[74]=[w("取消")])),_:1}),l(i,{type:"primary",onClick:Se,disabled:!T.name},{default:o(()=>e[75]||(e[75]=[w(" 确认保存 ")])),_:1},8,["disabled"])])]),default:o(()=>[l(Ce,{model:T,"label-position":"top",onSubmit:oe(Se,["prevent"])},{default:o(()=>[l(G,{label:"设计名称",required:""},{default:o(()=>[l(a,{ref_key:"saveNameInputRef",ref:xe,modelValue:T.name,"onUpdate:modelValue":e[17]||(e[17]=r=>T.name=r),placeholder:"请输入设计名称",maxlength:"50","show-word-limit":"",onKeyup:dt(Se,["enter"])},null,8,["modelValue"])]),_:1}),l(G,{label:"描述"},{default:o(()=>[l(a,{modelValue:T.description,"onUpdate:modelValue":e[18]||(e[18]=r=>T.description=r),type:"textarea",autosize:{minRows:2,maxRows:6},placeholder:"请输入设计描述（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(Y,{modelValue:he.value,"onUpdate:modelValue":e[23]||(e[23]=r=>he.value=r),title:"导入设计JSON",width:"600px","close-on-click-modal":!1,"destroy-on-close":!1},{footer:o(()=>[d("div",ki,[l(i,{onClick:e[22]||(e[22]=r=>he.value=!1),plain:""},{default:o(()=>e[78]||(e[78]=[w("取消")])),_:1}),l(i,{type:"primary",onClick:el,loading:we.value},{default:o(()=>e[79]||(e[79]=[w(" 导入 ")])),_:1},8,["loading"])])]),default:o(()=>[d("div",$i,[e[76]||(e[76]=d("p",{class:"import-tip"},"请将设计的JSON字符串粘贴到下方文本框中:",-1)),l(a,{modelValue:ye.value,"onUpdate:modelValue":e[21]||(e[21]=r=>ye.value=r),type:"textarea",autosize:{minRows:8,maxRows:20},placeholder:"粘贴设计JSON文本..."},null,8,["modelValue"]),e[77]||(e[77]=d("div",{class:"import-help"},[d("p",null,[d("i",{class:"el-icon-info"}),w(" 提示：导入会创建一个新的设计")])],-1)),ee.value?(p(),b("div",Oi,[l(ie,{title:ee.value,type:"error","show-icon":"",closable:!1},null,8,["title"])])):B("",!0)])]),_:1},8,["modelValue"]),l(Y,{modelValue:I.value,"onUpdate:modelValue":e[29]||(e[29]=r=>I.value=r),title:"编辑剧情配置",width:"800px",top:"5vh",class:"config-editor-dialog","modal-append-to-body":!0,"destroy-on-close":"","close-on-click-modal":!1,"show-close":!1,fullscreen:""},{header:o(({titleId:r,titleClass:u})=>[d("div",Di,[d("h4",{id:r,class:ne(u)},"编辑剧情配置",10,Ei),l(i,{class:"dialog-close-btn",onClick:e[24]||(e[24]=j=>I.value=!1),icon:"Close"})])]),footer:o(()=>[d("div",Ki,[l(i,{onClick:e[28]||(e[28]=r=>I.value=!1)},{default:o(()=>e[88]||(e[88]=[w("取消")])),_:1}),l(i,{type:"primary",onClick:Jt},{default:o(()=>e[89]||(e[89]=[w("保存配置")])),_:1})])]),default:o(()=>[d("div",xi,[d("div",zi,[l(Ce,{model:E,"label-position":"top"},{default:o(()=>[l(it,{gutter:20},{default:o(()=>[l(Oe,{span:12},{default:o(()=>[l(G,{label:"配置名称"},{default:o(()=>[l(a,{modelValue:E.name,"onUpdate:modelValue":e[25]||(e[25]=r=>E.name=r),placeholder:"为配置添加名称"},null,8,["modelValue"])]),_:1})]),_:1}),l(Oe,{span:12},{default:o(()=>[l(G,{label:"配置ID"},{default:o(()=>[l(a,{modelValue:E.id,"onUpdate:modelValue":e[26]||(e[26]=r=>E.id=r),placeholder:"配置唯一标识",disabled:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(G,{label:"配置描述"},{default:o(()=>[l(a,{modelValue:E.description,"onUpdate:modelValue":e[27]||(e[27]=r=>E.description=r),type:"textarea",autosize:{minRows:2,maxRows:6},placeholder:"添加配置描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),d("div",Si,[d("div",Ni,[e[86]||(e[86]=d("h3",{class:"section-title"},"剧情结构定义",-1)),e[87]||(e[87]=d("p",{class:"section-description"}," 此处定义的结构模板将同时应用于A/B两个剧情，支持一层复杂的嵌套结构,暂时不更新。 ",-1)),l(i,{type:"primary",size:"small",onClick:et,style:{"margin-bottom":"15px"}},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1}),e[80]||(e[80]=w(" 添加根字段 "))]),_:1}),d("div",Ui,[(p(!0),b(S,null,N(f,(r,u,j)=>(p(),b("div",{key:`root-${u}`,class:"field-card"},[d("div",Ji,O(j+1),1),d("div",Ti,[d("div",Fi,[l(a,{modelValue:r.name,"onUpdate:modelValue":v=>r.name=v,placeholder:"字段名称",size:"small",onChange:v=>Et(u,r.name)},null,8,["modelValue","onUpdate:modelValue","onChange"]),l(Ae,{modelValue:r.type,"onUpdate:modelValue":v=>r.type=v,placeholder:"选择类型",size:"small",onChange:v=>xt(u,r.type)},{default:o(()=>[l(x,{label:"字符串",value:"string"}),l(x,{label:"数字",value:"number"}),l(x,{label:"布尔值",value:"boolean"}),l(x,{label:"对象",value:"object"}),l(x,{label:"数组",value:"array"})]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),d("div",Ri,[l(i,{size:"small",type:"info",onClick:v=>Gt(u),circle:"",disabled:j===0},{default:o(()=>[l(s,null,{default:o(()=>[l(h(ke))]),_:1})]),_:2},1032,["onClick","disabled"]),l(i,{size:"small",type:"info",onClick:v=>Ht(u),circle:"",disabled:j===Object.keys(f).length-1},{default:o(()=>[l(s,null,{default:o(()=>[l(h(ve))]),_:1})]),_:2},1032,["onClick","disabled"]),r.type==="object"||r.type==="array"?(p(),U(i,{key:0,size:"small",type:"primary",onClick:v=>tt(u),circle:""},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1})]),_:2},1032,["onClick"])):B("",!0),l(i,{size:"small",type:"danger",onClick:v=>zt(u),circle:""},{default:o(()=>[l(s,null,{default:o(()=>[l(h(P))]),_:1})]),_:2},1032,["onClick"])])]),(r.type==="object"||r.type==="array")&&r.children?(p(),b("div",Li,[Object.keys(r.children).length===0?(p(),b("div",Mi,[e[82]||(e[82]=d("span",null,"暂无子字段",-1)),l(i,{size:"small",type:"primary",onClick:v=>tt(u)},{default:o(()=>e[81]||(e[81]=[w(" 添加子字段 ")])),_:2},1032,["onClick"])])):B("",!0),(p(!0),b(S,null,N(r.children,(v,V,R)=>(p(),b("div",{key:`child-${u}-${V}`,class:"child-field"},[d("div",Pi,O(R+1),1),d("div",qi,[d("div",Gi,[l(a,{modelValue:v.name,"onUpdate:modelValue":D=>v.name=D,placeholder:"字段名称",size:"small",onChange:D=>St(u,V,v.name)},null,8,["modelValue","onUpdate:modelValue","onChange"]),l(Ae,{modelValue:v.type,"onUpdate:modelValue":D=>v.type=D,placeholder:"选择类型",size:"small",onChange:D=>Nt(u,V,v.type)},{default:o(()=>[l(x,{label:"字符串",value:"string"}),l(x,{label:"数字",value:"number"}),l(x,{label:"布尔值",value:"boolean"}),l(x,{label:"对象",value:"object"}),l(x,{label:"数组",value:"array"})]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),d("div",Hi,[l(i,{size:"small",type:"info",onClick:D=>Qt(u,V),circle:"",disabled:R===0},{default:o(()=>[l(s,null,{default:o(()=>[l(h(ke))]),_:1})]),_:2},1032,["onClick","disabled"]),l(i,{size:"small",type:"info",onClick:D=>Yt(u,V),circle:"",disabled:R===Object.keys(r.children).length-1},{default:o(()=>[l(s,null,{default:o(()=>[l(h(ve))]),_:1})]),_:2},1032,["onClick","disabled"]),v.type==="object"||v.type==="array"?(p(),U(i,{key:0,size:"small",type:"primary",onClick:D=>lt(u,V),circle:""},{default:o(()=>[l(s,null,{default:o(()=>[l(h(J))]),_:1})]),_:2},1032,["onClick"])):B("",!0),l(i,{size:"small",type:"danger",onClick:D=>Ut(u,V),circle:""},{default:o(()=>[l(s,null,{default:o(()=>[l(h(P))]),_:1})]),_:2},1032,["onClick"])])]),(v.type==="object"||v.type==="array")&&v.children?(p(),b("div",Qi,[Object.keys(v.children).length===0?(p(),b("div",Yi,[e[84]||(e[84]=d("span",null,"暂无嵌套字段",-1)),l(i,{size:"small",type:"primary",onClick:D=>lt(u,V)},{default:o(()=>e[83]||(e[83]=[w(" 添加嵌套字段 ")])),_:2},1032,["onClick"])])):B("",!0),(p(!0),b(S,null,N(v.children,(D,me,Fe)=>(p(),b("div",{key:`nested-${u}-${V}-${me}`,class:"nested-field"},[d("div",Wi,O(Fe+1),1),d("div",Xi,[d("div",Zi,[l(a,{modelValue:D.name,"onUpdate:modelValue":Z=>D.name=Z,placeholder:"字段名称",size:"small",onChange:Z=>Rt(u,V,me,D.name)},null,8,["modelValue","onUpdate:modelValue","onChange"]),l(Ae,{modelValue:D.type,"onUpdate:modelValue":Z=>D.type=Z,placeholder:"选择类型",size:"small",onChange:Z=>Lt(u,V,me,D.type)},{default:o(()=>[l(x,{label:"字符串",value:"string"}),l(x,{label:"数字",value:"number"}),l(x,{label:"布尔值",value:"boolean"})]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),d("div",Ii,[l(i,{size:"small",type:"info",onClick:Z=>Wt(u,V,me),circle:"",disabled:Fe===0},{default:o(()=>[l(s,null,{default:o(()=>[l(h(ke))]),_:1})]),_:2},1032,["onClick","disabled"]),l(i,{size:"small",type:"info",onClick:Z=>Xt(u,V,me),circle:"",disabled:Fe===Object.keys(v.children).length-1},{default:o(()=>[l(s,null,{default:o(()=>[l(h(ve))]),_:1})]),_:2},1032,["onClick","disabled"]),l(i,{size:"small",type:"danger",onClick:Z=>Mt(u,V,me),circle:""},{default:o(()=>[l(s,null,{default:o(()=>[l(h(P))]),_:1})]),_:2},1032,["onClick"])])])]))),128))])):B("",!0)]))),128))])):B("",!0)]))),128)),Object.keys(f).length===0?(p(),U(c,{key:0,description:"暂无字段结构","image-size":80},{default:o(()=>[l(i,{type:"primary",onClick:et},{default:o(()=>e[85]||(e[85]=[w(" 开始添加字段 ")])),_:1})]),_:1})):B("",!0)])])])])]),_:1},8,["modelValue"])])}}},mn=sl(en,[["__scopeId","data-v-6b002ca7"]]);export{mn as default};
