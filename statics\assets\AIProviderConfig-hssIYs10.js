import{_ as ce,r as h,o as fe,b as x,m as v,e as r,d as l,g as s,v as f,C as g,aa as O,t as ye,ac as B,aD as _e,X as ve,Y as ge,$ as xe,ay as be,j as Ve,p as E,q as we,s as he,B as ke,af as Ce,ad as Pe,an as Ie,am as Ue,J as Ee,F as P,a_ as Ae,ak as $e,al as ze,n as Se,G as Te,ax as Be,b8 as De,k as Me,av as G,E as u}from"./entry-BIjVVog3.js";/* empty css                  *//* empty css                 *//* empty css                        *//* empty css                         *//* empty css                *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css                 */import{useAIProvidersStore as Le}from"./aiProviders-BAOX-EH8.js";const Ne={class:"ai-provider-management"},Ke={class:"content-wrapper"},qe={class:"action-bar"},Fe={class:"action-right"},Oe={class:"main-content"},Ge={key:0,class:"empty-providers"},He={key:1,class:"provider-list"},Je={class:"provider-header"},We={class:"provider-name"},je={class:"provider-content"},Re={class:"subsection"},Xe={class:"subsection-header"},Ye={class:"models-list"},Qe={key:0,class:"empty-models"},Ze={class:"empty-actions"},el={class:"subsection"},ll={class:"subsection-header"},tl={class:"keys-list"},ol={key:0,class:"empty-keys"},sl={key:0,class:"subsection"},al={class:"subsection-header"},nl={key:0,class:"proxy-config"},rl={class:"proxy-url-input"},il={class:"proxy-test"},dl={key:1,class:"proxy-disabled-hint"},ul={class:"custom-info-alert"},ml={class:"alert-icon"},pl={class:"provider-actions mt-4"},cl={class:"model-config-header"},fl={class:"dialog-footer"},yl={__name:"AIProviderConfig",setup(_l){const i=Le(),I=h([]),k=h("http://"),A=h({}),V=h({}),$=h({}),m=h({visible:!1,providerId:"",modelId:"",modelName:"",form:{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0}}),D=()=>{const t={name:"新服务商",baseUrl:"",apiKeys:[],models:[],proxy:{enabled:!1,url:"",username:"",password:"",timeout:30,verify_ssl:!0}},e=i.addProvider(t);I.value=[e.id]},H=t=>{G.confirm("确定要删除该服务商配置吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=i.providers[t];if(!e){u.error("未找到要删除的服务商");return}i.removeProvider(e.id),await i.saveProviders(),u.success("服务商已删除")}catch(e){console.error("删除服务商失败:",e),u.error("删除服务商失败: "+e.message)}}).catch(()=>{})},M=t=>{try{i.addApiKey(t),u.success("API密钥已添加，请填写密钥内容并保存配置")}catch(e){console.error("添加API密钥失败:",e),u.error("添加API密钥失败: "+e.message)}},J=async(t,e)=>{try{i.removeApiKey(t,e),u.success("API密钥已删除，请记得保存配置")}catch(n){console.error("删除API密钥失败:",n),u.error("删除API密钥失败: "+n.message)}},W=async(t,e)=>{await i.testApiKey(t,e)},L=t=>(t.proxy||(t.proxy={enabled:!1,url:"",username:"",password:"",timeout:30,verify_ssl:!0}),!0),j=(t,e)=>{e&&(!t.proxy.url||t.proxy.url.trim()==="")&&(t.proxy.url="127.0.0.1:7890")},R=t=>{(!t.proxy.url||t.proxy.url.trim()==="")&&(t.proxy.url="127.0.0.1:7890")},X=async t=>{try{$.value[t.id]=!0;const e=await window.pywebview.api.detect_system_proxy(),n=typeof e=="string"?JSON.parse(e):e;if(n&&n.status==="success"){const p=n.data;if(p&&p.proxy_url){let d=p.proxy_url;d.startsWith("http://")?(d=d.substring(7),k.value="http://"):d.startsWith("https://")?(d=d.substring(8),k.value="https://"):d.startsWith("socks5://")&&(d=d.substring(9),k.value="socks5://"),t.proxy.url=d,p.username&&(t.proxy.username=p.username),p.password&&(t.proxy.password=p.password),u.success(`检测到系统代理: ${p.proxy_url}`)}else u.info("未检测到系统代理配置")}else throw new Error(n?.message||"检测系统代理失败")}catch(e){console.error("检测系统代理失败:",e),u.error("检测系统代理失败: "+e.message)}finally{$.value[t.id]=!1}},Y=async t=>{try{A.value[t]=!0,V.value[t]=null;const e=i.providers.find(c=>c.id===t);if(!e)throw new Error("未找到服务商");if(L(e),!e.proxy.enabled)throw new Error("代理未启用");if(!e.proxy.url)throw new Error("请输入代理地址");const n={proxy_url:e.proxy.url,proxy_username:e.proxy.username||"",proxy_password:e.proxy.password||"",timeout:e.proxy.timeout||30,verify_ssl:e.proxy.verify_ssl!==!1},p=await window.pywebview.api.test_proxy_connection(n),d=typeof p=="string"?JSON.parse(p):p;if(d&&d.status==="success")V.value[t]={success:!0,message:d.message||"代理连接测试成功"},u.success("代理连接测试成功");else throw new Error(d?.message||"代理连接测试失败")}catch(e){console.error("代理测试失败:",e),V.value[t]={success:!1,message:e.message||"代理连接测试失败"},u.error("代理连接测试失败: "+e.message)}finally{A.value[t]=!1}},N=t=>{G({title:"添加模型",message:`
    <div class="add-model-form" style="padding: 10px;">
      <div class="form-item" style="margin-bottom: 15px;">
        <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: bold;">模型ID (必填):</label>
        <input
          id="model-id-input"
          class="el-input__inner"
          placeholder="例如: gpt-4-turbo"
          style="width: 100%; padding: 8px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 14px;"
        />
      </div>
      <div class="form-item" style="margin-bottom: 15px;">
        <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: bold;">显示别名 (可选):</label>
        <input
          id="model-name-input"
          class="el-input__inner"
          placeholder="例如: GPT-4 Turbo (留空则使用模型ID)"
          style="width: 100%; padding: 8px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 14px;"
        />
      </div>
      <div class="form-item" style="margin-bottom: 15px;">
        <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: bold;">
          <input
            id="model-available-input"
            type="checkbox"
            checked
            style="margin-right: 8px;"
          />
          启用此模型
        </label>
      </div>
    </div>
  `,dangerouslyUseHTMLString:!0,showCancelButton:!0,confirmButtonText:"确定",cancelButtonText:"取消",beforeClose:(n,p,d)=>{if(n==="confirm"){const c=document.getElementById("model-id-input")?.value?.trim(),w=document.getElementById("model-name-input")?.value?.trim(),_=document.getElementById("model-available-input")?.checked;if(!c){u.warning("请输入模型ID");return}if(i.providers.find(b=>b.id===t)?.models?.some(b=>b.id===c)){u.warning("该模型ID已存在");return}try{i.addModel(t,{id:c,name:w||c,available:_!==!1}),u.success("模型添加成功，请记得保存配置"),d()}catch(b){u.error("添加模型失败: "+b.message)}}else d()}})},Q=async(t,e)=>{try{i.removeModel(t,e),u.success("模型已删除，请记得保存配置")}catch(n){console.error("删除模型失败:",n),u.error("删除模型失败: "+n.message)}},Z=(t,e)=>{if(!e.id||!e.id.trim())return u.warning("模型ID不能为空"),!1;const n=i.providers.find(p=>p.id===t);return n?.models&&n.models.filter(d=>d.id===e.id).length>1?(u.warning("模型ID不能重复"),!1):((!e.name||!e.name.trim())&&(e.name=e.id),!0)},ee=t=>{console.log(`模型 ${t.name} (${t.id}) 可用性变更为: ${t.available}`)},K=async t=>{try{await i.fetchModels(t)}catch(e){console.error("获取模型错误",e)}},le=async t=>{try{await i.saveProvider(t),setTimeout(()=>{u.info('提示：如需更新聊天界面的模型列表，请点击"保存所有配置并刷新模型"',{duration:4e3})},1e3)}catch(e){console.error("保存服务商配置错误",e),u.error(`保存失败: ${e.message||"未知错误"}`)}},te=async()=>{try{if(i.providers.filter(e=>!e.name||!e.name.trim()).length>0){u.warning("请确保所有服务商都有名称");return}await i.saveProviders()}catch(t){console.error("保存服务商配置错误",t),u.error(`保存失败: ${t.message||"未知错误"}`)}},q=async(t=!1)=>{try{if(t||!i.initialized){const e=new Promise((n,p)=>{setTimeout(()=>p(new Error("加载AI服务商配置超时")),1e4)});await Promise.race([i.loadProviders(t),e]),i.providers.forEach(n=>{n.proxy||(n.proxy={enabled:!1,url:"",username:"",password:"",timeout:30,verify_ssl:!0})}),i.providers.length>0&&(I.value=[i.providers[0].id])}}catch(e){console.error("加载服务商配置错误",e),i.loading=!1}},oe=async()=>{try{await q(!0),u.success("AI服务商配置已刷新")}catch(t){u.error("刷新AI服务商配置失败: "+t.message)}},se=(t,e)=>{m.value.visible=!0,m.value.providerId=t,m.value.modelId=e.id,m.value.modelName=e.name||e.id,e.config?m.value.form={temperature:e.config.temperature??.8,max_tokens:e.config.max_tokens??4096,top_p:e.config.top_p??.8,frequency_penalty:e.config.frequency_penalty??0,presence_penalty:e.config.presence_penalty??0,stream:e.config.stream??!0}:(e.config={temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0},m.value.form={...e.config})},ae=async()=>{try{const t=i.providers.find(n=>n.id===m.value.providerId);if(!t){u.error("未找到对应的服务商");return}const e=t.models.find(n=>n.id===m.value.modelId);if(!e){u.error("未找到对应的模型");return}e.config={...m.value.form},await i.saveProviders(),m.value.visible=!1}catch(t){console.error("保存模型配置失败:",t),u.error("保存模型配置失败: "+t.message)}};return fe(async()=>{try{await q()}catch(t){console.error("初始化AI服务商配置失败:",t)}}),(t,e)=>{const n=ye,p=_e,d=he,c=we,w=ke,_=Pe,C=Ie,b=Ce,z=Ue,ne=Ee,F=Ae,S=ze,re=$e,T=Ve,ie=be,de=Be,U=De,ue=Me;return v(),x("div",Ne,[r("div",Ke,[r("div",qe,[e[13]||(e[13]=r("div",{class:"action-left"},[r("h2",{class:"page-title"},"AI 服务商管理"),r("p",{class:"page-description"},"配置和管理 AI 模型服务提供商")],-1)),r("div",Fe,[l(n,{type:"info",onClick:oe,loading:g(i).isLoading,icon:g(O)},{default:s(()=>e[10]||(e[10]=[f(" 刷新 ")])),_:1},8,["loading","icon"]),l(n,{type:"success",onClick:te,loading:g(i).isLoading},{default:s(()=>e[11]||(e[11]=[f(" 保存所有配置并刷新模型 ")])),_:1},8,["loading"]),l(n,{type:"primary",onClick:D,icon:g(B)},{default:s(()=>e[12]||(e[12]=[f(" 添加服务商 ")])),_:1},8,["icon"])])]),r("div",Oe,[g(i).providers.length===0?(v(),x("div",Ge,[l(p,{description:"暂无配置的服务商"}),l(n,{type:"primary",onClick:D,class:"mt-3"},{default:s(()=>e[14]||(e[14]=[f("添加服务商")])),_:1})])):(v(),x("div",He,[l(de,{modelValue:I.value,"onUpdate:modelValue":e[1]||(e[1]=o=>I.value=o)},{default:s(()=>[(v(!0),x(ve,null,ge(g(i).providers,(o,me)=>(v(),xe(ie,{key:o.id,name:o.id},{title:s(()=>[r("div",Je,[r("span",We,P(o.name),1)])]),default:s(()=>[r("div",je,[l(T,{"label-width":"120px",size:"default"},{default:s(()=>[l(c,{label:"服务商名称"},{default:s(()=>[l(d,{modelValue:o.name,"onUpdate:modelValue":a=>o.name=a,placeholder:"例如: OpenAI, Claude, 智谱AI等"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(c,{label:"API基础URL"},{default:s(()=>[l(d,{modelValue:o.baseUrl,"onUpdate:modelValue":a=>o.baseUrl=a,placeholder:"例如: https://api.openai.com/v1"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(c,{class:"providers-info"}),r("div",Re,[r("div",Xe,[e[17]||(e[17]=r("h3",null,"模型管理",-1)),r("div",null,[l(n,{type:"primary",size:"small",onClick:a=>N(o.id)},{default:s(()=>[l(w,null,{default:s(()=>[l(g(B))]),_:1}),e[15]||(e[15]=f(" 添加模型 "))]),_:2},1032,["onClick"]),l(n,{type:"success",size:"small",onClick:a=>K(o.id)},{default:s(()=>[l(w,null,{default:s(()=>[l(g(O))]),_:1}),e[16]||(e[16]=f(" 获取可用模型 "))]),_:2},1032,["onClick"])])]),r("div",Ye,[l(b,{data:o.models,style:{width:"100%"},border:""},{default:s(()=>[l(_,{label:"模型ID","min-width":"180"},{default:s(({row:a})=>[l(d,{modelValue:a.id,"onUpdate:modelValue":y=>a.id=y,placeholder:"模型ID",size:"small",onBlur:y=>Z(o.id,a)},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:2},1024),l(_,{label:"显示别名","min-width":"150"},{default:s(({row:a})=>[l(d,{modelValue:a.name,"onUpdate:modelValue":y=>a.name=y,placeholder:"显示别名",size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(_,{label:"启用",width:"80",align:"center"},{default:s(({row:a})=>[l(C,{modelValue:a.available,"onUpdate:modelValue":y=>a.available=y,onChange:y=>ee(a)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(_,{label:"参数配置","min-width":"120",align:"center"},{default:s(({row:a})=>[l(n,{type:"primary",size:"small",onClick:y=>se(o.id,a)},{default:s(()=>e[18]||(e[18]=[f(" 配置参数 ")])),_:2},1032,["onClick"])]),_:2},1024),l(_,{label:"操作",width:"100",align:"center"},{default:s(({$index:a})=>[l(n,{type:"danger",size:"small",onClick:y=>Q(o.id,a)},{default:s(()=>e[19]||(e[19]=[f(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data"]),o.models.length===0?(v(),x("div",Qe,[l(p,{description:"暂无配置的模型","image-size":60}),r("div",Ze,[l(n,{type:"primary",size:"small",onClick:a=>N(o.id),class:"mt-2 mr-2"},{default:s(()=>e[20]||(e[20]=[f(" 手动添加 ")])),_:2},1032,["onClick"]),l(n,{type:"success",size:"small",onClick:a=>K(o.id),class:"mt-2"},{default:s(()=>e[21]||(e[21]=[f(" 获取可用模型 ")])),_:2},1032,["onClick"])])])):E("",!0)])]),r("div",el,[r("div",ll,[e[23]||(e[23]=r("h3",null,"API密钥管理",-1)),l(n,{type:"primary",size:"small",onClick:a=>M(o.id)},{default:s(()=>[l(w,null,{default:s(()=>[l(g(B))]),_:1}),e[22]||(e[22]=f(" 添加密钥 "))]),_:2},1032,["onClick"])]),r("div",tl,[l(b,{data:o.apiKeys,style:{width:"100%"},border:""},{default:s(()=>[l(_,{label:"密钥","min-width":"200"},{default:s(({row:a})=>[l(d,{modelValue:a.key,"onUpdate:modelValue":y=>a.key=y,placeholder:"API密钥","show-password":""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(_,{label:"权重",width:"100"},{default:s(({row:a})=>[l(z,{modelValue:a.weight,"onUpdate:modelValue":y=>a.weight=y,min:1,max:100,"controls-position":"right",size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(_,{label:"状态",width:"100",align:"center"},{default:s(({row:a})=>[l(ne,{type:a.status==="active"?"success":"danger"},{default:s(()=>[f(P(a.status==="active"?"正常":"异常"),1)]),_:2},1032,["type"])]),_:1}),l(_,{label:"操作",width:"150",align:"center"},{default:s(({row:a,$index:y})=>[l(F,null,{default:s(()=>[l(n,{type:"primary",size:"small",onClick:pe=>W(o.id,a.id)},{default:s(()=>e[24]||(e[24]=[f(" 测试 ")])),_:2},1032,["onClick"]),l(n,{type:"danger",size:"small",onClick:pe=>J(o.id,y)},{default:s(()=>e[25]||(e[25]=[f(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["data"]),o.apiKeys.length===0?(v(),x("div",ol,[l(p,{description:"暂无配置的API密钥","image-size":60}),l(n,{type:"primary",size:"small",onClick:a=>M(o.id),class:"mt-2"},{default:s(()=>e[26]||(e[26]=[f(" 添加密钥 ")])),_:2},1032,["onClick"])])):E("",!0)])]),L(o)?(v(),x("div",sl,[r("div",al,[e[27]||(e[27]=r("h3",null,"代理配置",-1)),l(C,{modelValue:o.proxy.enabled,"onUpdate:modelValue":a=>o.proxy.enabled=a,"active-text":"启用代理","inactive-text":"禁用代理",onChange:a=>j(o,a)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),o.proxy.enabled?(v(),x("div",nl,[l(T,{"label-width":"120px",size:"default"},{default:s(()=>[l(c,{label:"代理地址"},{default:s(()=>[r("div",rl,[l(d,{modelValue:o.proxy.url,"onUpdate:modelValue":a=>o.proxy.url=a,placeholder:"例如: 127.0.0.1:7890",onFocus:a=>R(o),style:{flex:"1"}},{prepend:s(()=>[l(re,{modelValue:k.value,"onUpdate:modelValue":e[0]||(e[0]=a=>k.value=a),style:{width:"100px"}},{default:s(()=>[l(S,{label:"HTTP",value:"http://"}),l(S,{label:"HTTPS",value:"https://"}),l(S,{label:"SOCKS5",value:"socks5://"})]),_:1},8,["modelValue"])]),_:2},1032,["modelValue","onUpdate:modelValue","onFocus"]),l(n,{type:"primary",size:"default",onClick:a=>X(o),loading:$.value[o.id],style:{"margin-left":"8px"}},{default:s(()=>e[28]||(e[28]=[f(" 检测系统代理 ")])),_:2},1032,["onClick","loading"])])]),_:2},1024),l(c,{label:"用户名"},{default:s(()=>[l(d,{modelValue:o.proxy.username,"onUpdate:modelValue":a=>o.proxy.username=a,placeholder:"代理用户名（可选）",clearable:""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(c,{label:"密码"},{default:s(()=>[l(d,{modelValue:o.proxy.password,"onUpdate:modelValue":a=>o.proxy.password=a,type:"password",placeholder:"代理密码（可选）","show-password":"",clearable:""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(c,{label:"超时时间"},{default:s(()=>[l(z,{modelValue:o.proxy.timeout,"onUpdate:modelValue":a=>o.proxy.timeout=a,min:5,max:300,step:5,"controls-position":"right",style:{width:"200px"}},null,8,["modelValue","onUpdate:modelValue"]),e[29]||(e[29]=r("span",{style:{"margin-left":"8px",color:"var(--el-text-color-regular)"}},"秒",-1))]),_:2},1024),l(c,{label:"SSL验证"},{default:s(()=>[l(C,{modelValue:o.proxy.verify_ssl,"onUpdate:modelValue":a=>o.proxy.verify_ssl=a,"active-text":"验证SSL证书","inactive-text":"跳过SSL验证"},null,8,["modelValue","onUpdate:modelValue"]),e[30]||(e[30]=r("div",{style:{"margin-top":"4px","font-size":"12px",color:"var(--el-text-color-placeholder)"}}," 注意：跳过SSL验证可能存在安全风险 ",-1))]),_:2},1024)]),_:2},1024),r("div",il,[l(n,{type:"primary",size:"small",onClick:a=>Y(o.id),loading:A.value[o.id]},{default:s(()=>e[31]||(e[31]=[f(" 测试代理连接 ")])),_:2},1032,["onClick","loading"]),V.value[o.id]?(v(),x("span",{key:0,class:Se(["proxy-test-result",V.value[o.id].success?"success":"error"])},P(V.value[o.id].message),3)):E("",!0)])])):(v(),x("div",dl,[r("div",ul,[r("div",ml,[l(w,null,{default:s(()=>[l(g(Te))]),_:1})]),e[32]||(e[32]=r("div",{class:"alert-content"},[r("div",{class:"alert-title"},"代理已禁用"),r("div",{class:"alert-description"}," 启用代理后，所有API请求将通过指定的代理服务器发送。 ")],-1))])]))])):E("",!0),r("div",pl,[l(F,null,{default:s(()=>[l(n,{type:"danger",onClick:a=>H(me)},{default:s(()=>e[33]||(e[33]=[f(" 删除服务商 ")])),_:2},1032,["onClick"]),l(n,{type:"primary",onClick:a=>le(o.id),loading:g(i).isLoading},{default:s(()=>e[34]||(e[34]=[f(" 保存此服务商 ")])),_:2},1032,["onClick","loading"])]),_:2},1024)])]),_:2},1024)])]),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"])]))]),l(ue,{modelValue:m.value.visible,"onUpdate:modelValue":e[9]||(e[9]=o=>m.value.visible=o),title:"模型参数配置",width:"500px","destroy-on-close":""},{footer:s(()=>[r("div",fl,[l(n,{onClick:e[8]||(e[8]=o=>m.value.visible=!1)},{default:s(()=>e[35]||(e[35]=[f("取消")])),_:1}),l(n,{type:"primary",onClick:ae},{default:s(()=>e[36]||(e[36]=[f("保存")])),_:1})])]),default:s(()=>[r("div",cl,[r("h4",null,P(m.value.modelName)+" ("+P(m.value.modelId)+")",1)]),l(T,{model:m.value.form,"label-width":"120px"},{default:s(()=>[l(c,{label:"温度 (Temperature)"},{default:s(()=>[l(U,{modelValue:m.value.form.temperature,"onUpdate:modelValue":e[2]||(e[2]=o=>m.value.form.temperature=o),min:0,max:2,step:.1,"show-input":"","show-input-controls":!1},null,8,["modelValue"])]),_:1}),l(c,{label:"最大令牌数"},{default:s(()=>[l(z,{modelValue:m.value.form.max_tokens,"onUpdate:modelValue":e[3]||(e[3]=o=>m.value.form.max_tokens=o),min:1,max:32768,step:256,"controls-position":"right"},null,8,["modelValue"])]),_:1}),l(c,{label:"Top P"},{default:s(()=>[l(U,{modelValue:m.value.form.top_p,"onUpdate:modelValue":e[4]||(e[4]=o=>m.value.form.top_p=o),min:0,max:1,step:.1,"show-input":"","show-input-controls":!1},null,8,["modelValue"])]),_:1}),l(c,{label:"频率惩罚"},{default:s(()=>[l(U,{modelValue:m.value.form.frequency_penalty,"onUpdate:modelValue":e[5]||(e[5]=o=>m.value.form.frequency_penalty=o),min:-2,max:2,step:.1,"show-input":"","show-input-controls":!1},null,8,["modelValue"])]),_:1}),l(c,{label:"存在惩罚"},{default:s(()=>[l(U,{modelValue:m.value.form.presence_penalty,"onUpdate:modelValue":e[6]||(e[6]=o=>m.value.form.presence_penalty=o),min:-2,max:2,step:.1,"show-input":"","show-input-controls":!1},null,8,["modelValue"])]),_:1}),l(c,{label:"流式输出"},{default:s(()=>[l(C,{modelValue:m.value.form.stream,"onUpdate:modelValue":e[7]||(e[7]=o=>m.value.form.stream=o)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])])}}},Al=ce(yl,[["__scopeId","data-v-a0a38568"]]);export{Al as default};
