import{_ as L,bh as R,r as g,b as k,m as V,e as d,d as o,g as t,t as F,v as u,B as T,C as f,ac as M,aa as q,a_ as z,$ as G,ad as K,an as P,af as H,j as Q,q as W,s as X,k as Y,K as Z,p as ee,bD as le,F as oe,E as n,av as te}from"./entry-BIjVVog3.js";/* empty css                 *//* empty css                *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css               */const ae={class:"ai-role-manager"},se={class:"settings-panel"},ne={class:"section-header"},re={class:"header-actions"},ie={class:"panel-content"},de={class:"settings-card table-container"},ue={class:"dialog-footer"},pe={class:"import-content"},me={class:"import-actions"},ce={key:0,class:"import-file-name"},fe={class:"dialog-footer"},ve={__name:"AIRoleManager",setup(_e){const p=R("aiRolesStore");R("showLoading"),R("hideLoading");const i=g({visible:!1,title:"",form:{id:"",name:"",prompt:"",isEnabled:!0}}),v=g({visible:!1}),m=g(""),b=g(""),A=()=>{i.value={visible:!0,title:"添加AI角色",form:{id:"",name:"",prompt:"",isEnabled:!0}}},x=l=>{i.value={visible:!0,title:"编辑AI角色",form:{...l}}},h=async l=>{try{await te.confirm(`确定要删除角色 "${l.name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await p.deleteRole(l.id),n.success("删除成功")}catch(e){e!=="cancel"&&(console.error("删除AI角色失败:",e),n.error("删除失败"))}},S=async()=>{try{const l=i.value.form;if(!l.name.trim()){n.error("请输入角色名称");return}if(!l.prompt.trim()){n.error("请输入角色提示词");return}l.id?(await p.updateRole(l.id,l),n.success("编辑成功")):(await p.addRole(l),n.success("添加成功")),i.value.visible=!1}catch(l){console.error("保存AI角色失败:",l),n.error("保存失败")}},U=async(l,e)=>{try{await p.updateRole(l.id,{...l,isEnabled:e}),n.success(e?"角色已启用":"角色已禁用")}catch(r){console.error("更新角色状态失败:",r),n.error("更新失败"),l.isEnabled=!e}},B=async()=>{try{await p.loadRoles(),n.success("刷新成功")}catch(l){console.error("刷新AI角色失败:",l),n.error("刷新失败")}},N=async()=>{try{const l=JSON.stringify(p.roles,null,2),e=new Blob([l],{type:"application/json"}),r=URL.createObjectURL(e),a=document.createElement("a");a.href=r,a.download=`ai-roles-${new Date().toISOString().slice(0,10)}.json`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(r),n.success("导出成功")}catch(l){console.error("导出AI角色失败:",l),n.error("导出失败")}},O=async()=>{try{const l=await window.pywebview.api.select_file_path(),e=typeof l=="string"?JSON.parse(l):l;if(e&&e.status==="success"&&e.data.length>0){const r=e.data[0];try{const a=await window.pywebview.api.read_file({path:r}),c=typeof a=="string"?JSON.parse(a):a;if(c.status==="success")m.value=c.data,b.value=r.split("/").pop()||r.split("\\").pop(),n.success("文件读取成功");else throw new Error(c.message||"读取文件失败")}catch(a){console.error("读取文件失败:",a),n.error("读取文件失败: "+a.message)}}}catch(l){console.error("选择文件失败:",l),n.error("选择文件失败: "+l.message)}},$=async()=>{try{const l=JSON.parse(m.value);if(!Array.isArray(l)){n.error("导入数据格式错误，应为数组格式");return}let e=0;for(const r of l)r.name&&r.prompt&&(await p.addRole({name:r.name,prompt:r.prompt,isEnabled:r.isEnabled!==!1}),e++);v.value.visible=!1,m.value="",b.value="",n.success(`成功导入 ${e} 个角色`)}catch(l){console.error("导入AI角色失败:",l),n.error("导入失败: "+l.message)}};return(l,e)=>{const r=T,a=F,c=z,y=K,C=P,j=H,w=X,E=W,D=Q,I=Y,J=Z;return V(),k("div",ae,[d("div",se,[d("div",ne,[e[13]||(e[13]=d("h2",{class:"section-title"},"AI角色管理",-1)),d("div",re,[o(c,null,{default:t(()=>[o(a,{type:"primary",onClick:A},{default:t(()=>[o(r,null,{default:t(()=>[o(f(M))]),_:1}),e[9]||(e[9]=u(" 添加角色 "))]),_:1}),o(a,{onClick:B,loading:f(p).loading},{default:t(()=>[o(r,null,{default:t(()=>[o(f(q))]),_:1}),e[10]||(e[10]=u(" 刷新 "))]),_:1},8,["loading"]),o(a,{onClick:e[0]||(e[0]=s=>v.value.visible=!0)},{default:t(()=>e[11]||(e[11]=[u(" 导入 ")])),_:1}),o(a,{onClick:N},{default:t(()=>e[12]||(e[12]=[u(" 导出 ")])),_:1})]),_:1})])]),d("div",ie,[d("div",de,[(V(),G(j,{data:f(p).roles,style:{width:"100%"},border:"",key:"aiRoles-"+f(p).roles.length},{default:t(()=>[o(y,{prop:"name",label:"名称","min-width":"120"}),o(y,{prop:"prompt",label:"提示词","min-width":"200","show-overflow-tooltip":""}),o(y,{label:"启用",width:"80",align:"center"},{default:t(({row:s})=>[o(C,{modelValue:s.isEnabled,"onUpdate:modelValue":_=>s.isEnabled=_,onChange:_=>U(s,_)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),o(y,{label:"操作",width:"200",align:"center",fixed:"right"},{default:t(({row:s})=>[o(c,null,{default:t(()=>[o(a,{type:"primary",size:"small",onClick:_=>x(s)},{default:t(()=>e[14]||(e[14]=[u(" 编辑 ")])),_:2},1032,["onClick"]),o(a,{type:"danger",size:"small",onClick:_=>h(s)},{default:t(()=>e[15]||(e[15]=[u(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]))])])]),o(I,{modelValue:i.value.visible,"onUpdate:modelValue":e[5]||(e[5]=s=>i.value.visible=s),title:i.value.title,width:"500px","destroy-on-close":""},{footer:t(()=>[d("div",ue,[o(a,{onClick:e[4]||(e[4]=s=>i.value.visible=!1)},{default:t(()=>e[16]||(e[16]=[u("取消")])),_:1}),o(a,{type:"primary",onClick:S},{default:t(()=>e[17]||(e[17]=[u("保存")])),_:1})])]),default:t(()=>[o(D,{model:i.value.form,"label-width":"100px"},{default:t(()=>[o(E,{label:"角色名称",required:""},{default:t(()=>[o(w,{modelValue:i.value.form.name,"onUpdate:modelValue":e[1]||(e[1]=s=>i.value.form.name=s),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),o(E,{label:"提示词",required:""},{default:t(()=>[o(w,{modelValue:i.value.form.prompt,"onUpdate:modelValue":e[2]||(e[2]=s=>i.value.form.prompt=s),type:"textarea",rows:8,placeholder:"请输入角色提示词"},null,8,["modelValue"])]),_:1}),o(E,{label:"启用"},{default:t(()=>[o(C,{modelValue:i.value.form.isEnabled,"onUpdate:modelValue":e[3]||(e[3]=s=>i.value.form.isEnabled=s)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),o(I,{modelValue:v.value.visible,"onUpdate:modelValue":e[8]||(e[8]=s=>v.value.visible=s),title:"导入AI角色",width:"600px","destroy-on-close":""},{footer:t(()=>[d("div",fe,[o(a,{onClick:e[7]||(e[7]=s=>v.value.visible=!1)},{default:t(()=>e[20]||(e[20]=[u("取消")])),_:1}),o(a,{type:"primary",onClick:$,disabled:!m.value},{default:t(()=>e[21]||(e[21]=[u("导入")])),_:1},8,["disabled"])])]),default:t(()=>[d("div",pe,[o(J,{title:"导入说明",type:"info",closable:!1,"show-icon":""},{default:t(()=>e[18]||(e[18]=[d("p",null,"支持导入JSON格式的角色配置文件，格式如下：",-1),d("pre",{class:"import-example"},`[
  {
    "name": "角色名称",
    "prompt": "角色提示词",
    "isEnabled": true
  }
]`,-1)])),_:1}),d("div",me,[o(a,{type:"primary",onClick:O},{default:t(()=>[o(r,null,{default:t(()=>[o(f(le))]),_:1}),e[19]||(e[19]=u(" 选择文件 "))]),_:1}),b.value?(V(),k("span",ce,oe(b.value),1)):ee("",!0)]),o(w,{modelValue:m.value,"onUpdate:modelValue":e[6]||(e[6]=s=>m.value=s),type:"textarea",rows:10,placeholder:"或者直接粘贴JSON内容到这里",class:"import-textarea"},null,8,["modelValue"])])]),_:1},8,["modelValue"])])}}},Ie=L(ve,[["__scopeId","data-v-3e64eb5d"]]);export{Ie as default};
