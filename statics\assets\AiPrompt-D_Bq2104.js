import{_ as nt,r as i,w as se,$ as E,m as c,g as a,e as s,d as l,b as y,p as ce,F as v,t as ot,v as m,B as st,C as T,aa as tt,ah as je,ac as Ve,a5 as lt,s as ut,k as rt,j as Et,h as yl,q as Tt,x as bl,E as r,R as ze,c as O,b6 as wl,aC as at,X as B,Y as J,n as Ae,aD as Dt,a0 as hl,bE as Nt,l as $l,i as Vl,o as Ct,al as kl,ak as Cl,V as It,W as xt,ad as Il,J as xl,dK as Pl,a_ as Sl,ar as El,at as Tl,a7 as Pt,af as Dl,bL as Nl,an as Ul,bc as Ol,dL as Ll,dM as Rl,y as Bl,ag as zl,ap as jl,am as Al,av as St}from"./entry-BIjVVog3.js";/* empty css                 *//* empty css                        *//* empty css                  *//* empty css                          *//* empty css                    *//* empty css                       *//* empty css                        *//* empty css                  *//* empty css                *//* empty css                      *//* empty css               *//* empty css                        *//* empty css                 */import{u as Fl}from"./book-BHcNewcO.js";import"./apiUtils-CGTCyBFs.js";const Ml={class:"preview-content"},Jl={class:"preview-header"},ql={class:"preview-info"},Gl={class:"preview-title"},Ql={key:0,class:"preview-description"},Wl={class:"preview-actions"},Kl={class:"preview-body"},Hl={class:"edit-hint"},Xl={class:"content-preview-text"},Yl={class:"dialog-footer"},Zl={__name:"PromptPreviewDialog",props:{modelValue:{type:Boolean,default:!1},promptName:{type:String,default:""},promptDescription:{type:String,default:""},promptContent:{type:String,default:""},previewContent:{type:String,default:""}},emits:["update:modelValue","save-to-rule","regenerate"],setup(ue,{emit:ee}){const g=ue,b=ee,q=i(!1),A=i("");se(q,D=>{b("update:modelValue",D)}),se(()=>g.modelValue,D=>{q.value=D,D&&(A.value=g.previewContent||g.promptContent)}),se(()=>g.previewContent,D=>{D&&(A.value=D)}),se(()=>g.promptContent,D=>{D&&!g.previewContent&&(A.value=D)});const G=i(!1),re=async()=>{if(!G.value)try{G.value=!0,await b("regenerate")}catch(D){console.error("重新生成失败:",D),r.error("重新生成失败，请重试")}finally{G.value=!1}},te=()=>{if(!A.value){r.warning("没有可复制的内容");return}window.pywebview.api.copy_to_clipboard(A.value).then(()=>{r.success("复制成功")}).catch(()=>{r.error("复制失败")})},L=i(!1),z=i(null),Q=i({name:"",description:""}),pe={name:[{required:!0,message:"请输入提示词名称",trigger:"blur"},{min:1,max:50,message:"长度在 1 到 50 个字符",trigger:"blur"}]},ve=i(null),ye=()=>{Q.value={name:`提示词 ${new Date().toLocaleString("zh-CN",{month:"numeric",day:"numeric",hour:"numeric",minute:"numeric"})}`,description:""},L.value=!0},C=async()=>{if(z.value)try{await z.value.validate(),b("save-to-rule",{name:Q.value.name,description:Q.value.description,content:A.value,timestamp:new Date().toISOString()}),L.value=!1,q.value=!1,r.success("提示词已添加到规则")}catch(D){console.error("表单验证失败:",D)}},I=()=>{ze(()=>{ve.value&&ve.value.focus()})};return(D,_)=>{const W=st,le=ot,fe=ut,F=Tt,be=Et,X=rt;return c(),E(X,{modelValue:q.value,"onUpdate:modelValue":_[5]||(_[5]=M=>q.value=M),title:"提示词",style:{"user-select":"none"},width:"70%",class:"prompt-preview-dialog","close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,fullscreen:!1,"modal-class":"prompt-preview-modal"},{default:a(()=>[s("div",Ml,[s("div",Jl,[s("div",ql,[s("h3",Gl,v(ue.promptName),1),ue.promptDescription?(c(),y("p",Ql,v(ue.promptDescription),1)):ce("",!0)]),s("div",Wl,[l(le,{type:"primary",onClick:re,loading:G.value},{default:a(()=>[l(W,null,{default:a(()=>[l(T(tt))]),_:1}),_[6]||(_[6]=m(" 重新生成 "))]),_:1},8,["loading"]),l(le,{type:"primary",onClick:te},{default:a(()=>[l(W,null,{default:a(()=>[l(T(je))]),_:1}),_[7]||(_[7]=m(" 复制内容 "))]),_:1}),l(le,{type:"success",onClick:ye},{default:a(()=>[l(W,null,{default:a(()=>[l(T(Ve))]),_:1}),_[8]||(_[8]=m(" 提示词保存 "))]),_:1})])]),s("div",Kl,[s("div",Hl,[l(W,null,{default:a(()=>[l(T(lt))]),_:1}),_[9]||(_[9]=s("span",null,"您可以直接编辑下方的提示词内容",-1))]),l(fe,{modelValue:A.value,"onUpdate:modelValue":_[0]||(_[0]=M=>A.value=M),type:"textarea",rows:18,class:"prompt-content-editor",spellcheck:!1,resize:"none"},null,8,["modelValue"])])]),l(X,{modelValue:L.value,"onUpdate:modelValue":_[4]||(_[4]=M=>L.value=M),title:"提示词保存",width:"500px","append-to-body":"",class:"save-prompt-dialog",onOpened:I},{footer:a(()=>[s("div",Yl,[l(le,{onClick:_[3]||(_[3]=M=>L.value=!1)},{default:a(()=>_[10]||(_[10]=[m("取消")])),_:1}),l(le,{type:"primary",onClick:C},{default:a(()=>_[11]||(_[11]=[m("确认添加")])),_:1})])]),default:a(()=>[l(be,{model:Q.value,ref_key:"saveFormRef",ref:z,rules:pe,"label-position":"top",onSubmit:yl(C,["prevent"])},{default:a(()=>[l(F,{label:"提示词名称",prop:"name"},{default:a(()=>[l(fe,{modelValue:Q.value.name,"onUpdate:modelValue":_[1]||(_[1]=M=>Q.value.name=M),placeholder:"请输入提示词名称",ref_key:"nameInputRef",ref:ve,onKeyup:bl(C,["enter"])},null,8,["modelValue"])]),_:1}),l(F,{label:"提示词描述",prop:"description"},{default:a(()=>[l(fe,{modelValue:Q.value.description,"onUpdate:modelValue":_[2]||(_[2]=M=>Q.value.description=M),type:"textarea",rows:2,placeholder:"请输入提示词描述（可选）"},null,8,["modelValue"])]),_:1}),l(F,{label:"提示词内容",class:"content-preview"},{default:a(()=>[s("pre",Xl,v(A.value),1)]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])}}},ea=nt(Zl,[["__scopeId","data-v-f69e0d09"]]),ta={class:"prompt-list-container"},la={class:"prompt-list-header"},aa={class:"header-left"},na={class:"header-title-section"},oa={class:"dialog-title"},sa={class:"dialog-subtitle"},ua={class:"header-right"},ra={class:"prompt-list-content"},ia={class:"prompt-sidebar"},da={class:"sidebar-header"},ca={class:"sidebar-count"},pa={class:"sidebar-content"},ma={key:0,class:"prompt-list"},va=["onClick"],fa={class:"prompt-card-header"},ga={class:"prompt-card-title"},_a={class:"prompt-card-time"},ya={class:"prompt-card-preview"},ba={class:"prompt-card-footer"},wa={class:"prompt-card-length"},ha={key:1,class:"empty-state"},$a={class:"prompt-content-view"},Va={class:"content-header"},ka={class:"content-title-section"},Ca={class:"content-title"},Ia={class:"content-meta"},xa={class:"content-length"},Pa={class:"content-time"},Sa={class:"content-actions"},Ea={class:"content-body"},Ta={class:"content-wrapper"},Da={class:"prompt-content-text"},Na={key:1,class:"content-empty"},Ua={__name:"PromptListDialog",props:{modelValue:{type:Boolean,default:!1},ruleName:{type:String,required:!0},prompts:{type:Array,default:()=>[]}},emits:["update:modelValue","use-prompt"],setup(ue,{emit:ee}){const g=ue,b=ee,q=i(!1),A=i(""),G=i(0),re=i(!1);se(q,C=>{b("update:modelValue",C)}),se(()=>g.modelValue,C=>{q.value=C,C&&(A.value="",G.value=g.prompts.length>0?0:-1)});const te=O(()=>{if(!g.prompts)return[];if(!A.value)return g.prompts;const C=A.value.toLowerCase();return g.prompts.filter(I=>I.name&&I.name.toLowerCase().includes(C)||I.content&&I.content.toLowerCase().includes(C))}),L=O(()=>te.value.length===0?null:G.value<0||G.value>=te.value.length?te.value[0]:te.value[G.value]),z=C=>{G.value=C},Q=C=>{if(!C)return"无内容";const I=C.replace(/\s+/g," ").trim();return I.length>80?I.substring(0,80)+"...":I},pe=C=>C?C.length:0,ve=C=>{window.pywebview.api.copy_to_clipboard(C).then(()=>{re.value=!0,setTimeout(()=>{re.value=!1},2e3),r({message:"内容已复制到剪贴板",type:"success",duration:2e3,offset:80})}).catch(()=>{r.error("复制失败")})},ye=C=>C?new Date(C).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"";return(C,I)=>{const D=st,_=ot,W=ut,le=Dt,fe=rt;return c(),E(fe,{modelValue:q.value,"onUpdate:modelValue":I[3]||(I[3]=F=>q.value=F),title:`${ue.ruleName} - 提示词列表`,width:"90%",class:"prompts-dialog","close-on-press-escape":!0,"destroy-on-close":!1,"modal-append-to-body":!0,fullscreen:!0},{default:a(()=>[s("div",ta,[s("div",la,[s("div",aa,[l(_,{onClick:I[0]||(I[0]=F=>q.value=!1),class:"close-button",text:""},{default:a(()=>[l(D,null,{default:a(()=>[l(T(wl))]),_:1}),I[4]||(I[4]=s("span",null,"返回",-1))]),_:1}),s("div",na,[s("h2",oa,v(ue.ruleName),1),s("div",sa,v(te.value.length)+" 个提示词",1)])]),s("div",ua,[l(W,{modelValue:A.value,"onUpdate:modelValue":I[1]||(I[1]=F=>A.value=F),placeholder:"搜索提示词内容...",class:"prompt-search",clearable:""},{prefix:a(()=>[l(D,null,{default:a(()=>[l(T(at))]),_:1})]),_:1},8,["modelValue"])])]),s("div",ra,[s("div",ia,[s("div",da,[I[5]||(I[5]=s("div",{class:"sidebar-title"},"提示词列表",-1)),s("div",ca,v(te.value.length),1)]),s("div",pa,[te.value.length>0?(c(),y("div",ma,[(c(!0),y(B,null,J(te.value,(F,be)=>(c(),y("div",{key:F.id,class:Ae(["prompt-card",{active:G.value===be}]),onClick:X=>z(be)},[s("div",fa,[s("div",ga,v(F.name||"未命名提示词"),1),s("div",_a,v(ye(F.timestamp)),1)]),s("div",ya,v(Q(F.content)),1),s("div",ba,[s("div",wa,v(pe(F.content))+" 字符 ",1)])],10,va))),128))])):(c(),y("div",ha,[l(le,{description:"暂无匹配的提示词","image-size":120},{image:a(()=>[l(D,{size:48,class:"empty-icon"},{default:a(()=>[l(T(at))]),_:1})]),_:1})]))])]),s("div",$a,[L.value?(c(),y(B,{key:0},[s("div",Va,[s("div",ka,[s("h3",Ca,v(L.value.name||"未命名提示词"),1),s("div",Ia,[s("span",xa,v(pe(L.value.content))+" 字符",1),s("span",Pa,v(ye(L.value.timestamp)),1)])]),s("div",Sa,[l(_,{type:"primary",onClick:I[2]||(I[2]=F=>ve(L.value.content)),class:Ae(["copy-button",{copied:re.value}])},{default:a(()=>[l(D,null,{default:a(()=>[(c(),E(hl(re.value?T(Nt):T(je))))]),_:1}),m(" "+v(re.value?"已复制":"复制内容"),1)]),_:1},8,["class"])])]),s("div",Ea,[s("div",Ta,[s("pre",Da,v(L.value.content),1)])])],64)):(c(),y("div",Na,[l(le,{description:"请从左侧选择一个提示词查看详情","image-size":160},{image:a(()=>[l(D,{size:64,class:"empty-icon"},{default:a(()=>[l(T(je))]),_:1})]),_:1})]))])])])]),_:1},8,["modelValue","title"])}}},Oa=nt(Ua,[["__scopeId","data-v-62ce193b"]]),La={class:"ai-prompt-manager glass-bg"},Ra={class:"header-section"},Ba={class:"left-section"},za={class:"tab-buttons"},ja={key:0,class:"main-content"},Aa={class:"prompts-container glass-bg"},Fa={class:"section-header"},Ma={class:"header-content"},Ja={class:"header-actions"},qa={class:"prompt-list-wrapper"},Ga={class:"prompt-name-cell"},Qa={class:"prompt-name"},Wa={class:"prompt-description"},Ka={class:"prompt-count"},Ha={class:"update-time"},Xa={class:"operation-buttons"},Ya={class:"pagination-container"},Za={class:"generator-container glass-bg"},en={class:"section-header"},tn={class:"button-group"},ln={class:"generator-content"},an={class:"generator-left-panel"},nn={class:"panel-item info-panel"},on={class:"panel-item result-panel"},sn={class:"result-actions"},un={class:"result-content-wrapper"},rn={class:"generator-right-panel"},dn={class:"rules-container"},cn={class:"rule-section"},pn={class:"rule-header"},mn={class:"rule-content"},vn={class:"option-count"},fn={class:"option-count"},gn={key:0,class:"entity-type"},_n={class:"button-group",style:{display:"flex",gap:"12px"}},yn={class:"option-count"},bn={class:"rule-section"},wn={class:"rule-header"},hn={class:"rule-content"},$n={class:"scene-option"},Vn={class:"scene-title"},kn={class:"scene-description"},Cn={key:1,class:"empty-state glass-bg"},In={class:"placeholder-helper"},xn={class:"dialog-footer"},Pn={class:"prompt-detail"},Sn={class:"prompt-detail-item"},En={class:"prompt-detail-item"},Tn={class:"prompt-detail-item"},Dn={class:"prompt-content-box"},Nn={class:"prompt-detail-item"},Un={class:"placeholder-tags"},On={class:"dialog-footer"},Ln={class:"batch-results"},Rn={class:"result-header"},Bn={class:"result-index"},zn={class:"result-content"},jn={class:"dialog-footer"},An={class:"option-count"},Fn={class:"dialog-footer"},Mn={class:"option-count"},Jn={key:0,class:"entity-type"},qn={class:"dialog-footer"},Gn={key:2,class:"rule-saved-prompts"},Qn={class:"panel-title"},Wn={class:"prompt-preview-text"},Kn={class:"dialog-footer"},Hn={__name:"AiPrompt",setup(ue){$l(),Vl();const ee=Fl(),g=i("");O(()=>ee.bookList.find(e=>e.id===g.value)?.title||"AI提示词");const b=i({pools:[],currentPoolId:null}),q=i([]),A=i("");O(()=>{let t=[];if(b.value.pools.forEach(e=>{e.scenes&&t.push(...e.scenes)}),A.value){const e=A.value.toLowerCase();t=t.filter(u=>u.title&&u.title.toLowerCase().includes(e)||u.description&&u.description.toLowerCase().includes(e))}return t});const G=O(()=>b.value.currentPoolId?b.value.currentPoolId==="all"?re.value:b.value.pools.find(t=>t.id===b.value.currentPoolId):null),re=O(()=>{const t=[];return b.value.pools&&Array.isArray(b.value.pools)&&b.value.pools.forEach(e=>{if(e&&e.scenes&&Array.isArray(e.scenes)){const u=e.scenes.map(d=>({...d,sourcePool:{id:e.id,name:e.name}}));t.push(...u)}}),{id:"all",name:"全部场景",scenes:t,isVirtual:!0}}),te=i(null);O(()=>q.value.find(t=>t.id===te.value));const L=i("promptList"),z=i([]),Q=i(""),pe=O(()=>{if(!z.value)return[];if(!Q.value)return z.value;const t=Q.value.toLowerCase();return z.value.filter(e=>e.name&&e.name.toLowerCase().includes(t)||e.description&&e.description.toLowerCase().includes(t))});O(()=>{const t=(xe.value-1)*Be.value,e=t+Be.value;return pe.value.slice(t,e)});const ve=t=>{Be.value=t,xe.value=1},ye=()=>{xe.value=1},C=t=>{if(!t)return[];const e=/\{\{([^}]+)\}\}/g,u=[];let d;for(;(d=e.exec(t))!==null;)u.push(d[1].trim());return Array.from(new Set(u))},I=i(!1),D=i(!1);i(!1);const _=i({id:"",name:"",description:"",content:""});i(null);const W=i({id:"",name:"",description:"",content:""}),le=t=>{ge.value=t.id,X.value=t.name,M.value=t.description,t.rule?(o.value=JSON.parse(JSON.stringify(t.rule)),t.rule.content&&(h.value=t.rule.content),t.rule.editorConfig&&Object.assign(et.value,t.rule.editorConfig)):o.value={prompts:[],content:"",editorConfig:{...et.value}},L.value="generator"},fe=async t=>{if(!(!t||!t.content))try{await window.pywebview.api.copy_to_clipboard(t.content),r.success("提示词已复制到剪贴板")}catch(e){console.error("复制失败:",e),r.error("复制失败，请手动复制")}},F=t=>{St.confirm(`确定要删除提示词 "${t.name}" 吗？此操作不可撤销。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",customClass:"delete-confirm"}).then(async()=>{try{ie.value=!0;const e=await window.pywebview.api.book_controller.delete_prompt(g.value,t.id),u=typeof e=="string"?JSON.parse(e):e;u.status==="success"?(r.success("删除成功"),z.value=z.value.filter(d=>d.id!==t.id)):(console.error("删除失败:",u.message),r.error(`删除失败: ${u.message}`))}catch(e){console.error("删除操作异常:",e),r.error("删除失败: "+(e.message||"未知错误"))}finally{ie.value=!1}}).catch(()=>{})},be=i({}),X=i(""),M=i(""),h=i(""),it=i("templates"),x=i([]),N=i([]);i([]);const Fe=i("all"),dt=i(""),Me=i(""),ct=i(null),pt=i(null),Je=i(null),o=i({template:{enabled:!1,mode:"single",selectedTemplateId:null,selectedDimensions:[],excludedDimensions:[],entityExcludedDimensions:[],entityOutputFormat:"text",allEntitiesOutputFormat:"text"},scene:{enabled:!1,mode:"manual",selectedSceneIds:[],randomCount:1,selectedPoolId:null,placeholderText:"{{场景描述}}"},prompts:[]}),Ut=async()=>{ie.value=!0;try{const t=await window.pywebview.api.book_controller.get_templates(g.value),e=typeof t=="string"?JSON.parse(t):t;e.status==="success"?(x.value=e.data||[],await mt()):r.error(e.message||"加载模板失败")}catch(t){console.error("加载模板失败:",t),r.error("加载模板失败："+t.message)}finally{ie.value=!1}},mt=async()=>{try{const t=await window.pywebview.api.book_controller.get_entities(g.value),e=typeof t=="string"?JSON.parse(t):t;e.status==="success"?N.value=e.data||[]:r.error(e.message||"加载实体失败")}catch(t){console.error("加载实体失败：",t),r.error("加载实体失败："+t.message)}},ke=t=>!t||!t.id?0:N.value.filter(e=>e.template_id===t.id).length,qe=O(()=>o.value.template.selectedTemplateId?N.value.filter(t=>t.template_id===o.value.template.selectedTemplateId):[]);O(()=>o.value.template.selectedTemplateId?x.value.find(e=>e.id===o.value.template.selectedTemplateId)?.dimensions||[]:[]),O(()=>{if(!o.value.template.selectedEntityId||!o.value.template.selectedTemplateId)return[];const t=N.value.find(e=>e.id===o.value.template.selectedEntityId&&e.template_id===o.value.template.selectedTemplateId);return!t||!t.dimensions?[]:Object.entries(t.dimensions).map(([e,u])=>({name:e,value:u}))});const Ot=O(()=>{if(!o.value.template.selectedTemplateId)return[];const t=N.value.filter(u=>u.template_id===o.value.template.selectedTemplateId),e=new Set;return t.forEach(u=>{u.dimensions&&Object.keys(u.dimensions).forEach(d=>{e.add(d)})}),Array.from(e).map(u=>({name:u}))});O(()=>{const t=[];return b.value.pools.forEach(e=>{e.scenes&&t.push(...e.scenes.map(u=>({key:u.id,label:u.title,description:u.description})))}),t});const vt=O(()=>{if(h.value&&h.value.trim())return!0;if(!g.value)return!1;if(o.value.template.enabled)switch(o.value.template.mode){case"single":if(!o.value.template.selectedTemplateId)return!1;break;case"entity":if(!o.value.template.selectedEntityId)return!1;break;case"all":if(!o.value.template.selectedTemplateId)return!1;break}if(o.value.scene.enabled){if(o.value.scene.mode==="manual"){if(!o.value.scene.selectedSceneIds?.length)return!1}else if(!o.value.scene.selectedPoolId)return!1}return o.value.template.enabled||o.value.scene.enabled}),Pe=i(!1),Lt=t=>{Pe.value=t},ft=async()=>{if(!g.value){r.warning("请先选择一本书");return}try{if(Lt(!0),h.value&&h.value.trim()){console.log("使用现有内容预览"),await Ke();return}console.log("根据规则生成新内容");let t=[];if(o.value.template.enabled)switch(o.value.template.mode){case"single":if(o.value.template.selectedTemplateId){const e=x.value.find(u=>u.id===o.value.template.selectedTemplateId);e&&t.push(`{{模板:${e.id}【${e.name||"未命名模板"}】}}`)}break;case"entity":if(o.value.template.selectedEntityId){const e=N.value.find(u=>u.id===o.value.template.selectedEntityId);if(e){const u=o.value.template.entityExcludedDimensions||[];t.push(`{{实体:${e.id}:${o.value.template.entityOutputFormat}:${u.join(",")}【${e.name||"未命名实体"}】}}`)}}break;case"all":if(o.value.template.selectedTemplateId){const e=x.value.find(u=>u.id===o.value.template.selectedTemplateId);if(e){const u=o.value.template.excludedDimensions||[];t.push(`{{所有实体:${e.id}:${o.value.template.allEntitiesOutputFormat}:${u.join(",")}【${e.name||"未命名模板"}的所有实体】}}`)}}break}o.value.scene.enabled&&(o.value.scene.mode==="manual"?o.value.scene.selectedSceneIds?.length>0&&t.push(`{{场景:manual:${o.value.scene.selectedSceneIds.join(",")}}}`):o.value.scene.selectedPoolId&&t.push(`{{场景:random:${o.value.scene.selectedPoolId}:${o.value.scene.randomCount}}}`)),h.value=t.join(`

`),We.value.unshift({timestamp:new Date,content:h.value,rule:JSON.parse(JSON.stringify(o.value))}),We.value.length>50&&We.value.pop(),r.success("提示词生成成功")}catch(t){console.error("生成提示词失败:",t),r.error("生成提示词失败: "+t.message)}},Rt=()=>{if(!h.value){r.warning("没有可复制的内容");return}window.pywebview.api.copy_to_clipboard(h.value).then(()=>r.success("已复制到剪贴板")).catch(()=>r.error("复制失败"))},Bt=()=>{h.value="",r.success("已清空生成结果")},zt=t=>{if(!t)return"未知日期";const e=new Date(t);return isNaN(e.getTime())?"无效日期":e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},Ge=async t=>{try{z.value=[],x.value=[],N.value=[],b.value={pools:[],currentPoolId:null},q.value=[],t&&(ie.value=!0,await Promise.all([gt(),Ut(),mt(),Qe()]),console.log("数据加载完成:",{templates:x.value,entities:N.value}),r.success("书籍数据加载完成"))}catch(e){console.error("加载书籍数据失败:",e),r.error("加载书籍数据失败，请重试")}finally{ie.value=!1}},gt=async()=>{if(g.value)try{_t(!0);const t=await window.pywebview.api.book_controller.get_prompts(g.value),e=typeof t=="string"?JSON.parse(t):t;e.status==="success"?z.value=e.data||[]:console.error("加载提示词失败:",e.message)}catch(t){console.error("加载提示词失败:",t)}finally{_t(!1)}},ie=i(!1),_t=t=>{ie.value=t},Qe=async()=>{if(g.value)try{const t=await window.pywebview.api.book_controller.get_scene_events(g.value),e=typeof t=="string"?JSON.parse(t):t;if(e.status==="success"){const u=e.data||{};b.value={pools:Array.isArray(u.pools)?u.pools:[],currentPoolId:u.currentPoolId||null},b.value.pools.length||console.warn("没有找到场景卡池")}else throw new Error(e.message||"加载失败")}catch(t){console.error("加载场景失败:",t),r.error(`加载场景失败: ${t.message}`),b.value={pools:[],currentPoolId:null}}};se(()=>b.value.currentPoolId,()=>{G.value&&G.value.scenes&&Array.isArray(G.value.scenes)?q.value=G.value.scenes:q.value=[]}),O(()=>(console.log("当前模板列表:",x.value),!x.value||!Array.isArray(x.value)||x.value.length===0?[]:!Me.value&&!Je.value?x.value:x.value.filter(t=>{let e=!0,u=!0;if(Me.value){const d=Me.value.toLowerCase();e=t&&(t.name&&t.name.toLowerCase().includes(d)||t.description&&t.description.toLowerCase().includes(d))}return Je.value&&t.dimensions&&(u=t.dimensions.some(d=>d.name===Je.value)),e&&u}))),O(()=>{if(console.log("当前实体列表:",N.value),!N.value||!Array.isArray(N.value)||N.value.length===0)return[];let t=N.value;if(Fe.value&&Fe.value!=="all"&&(t=t.filter(e=>e.type===Fe.value)),dt.value){const e=dt.value.toLowerCase();t=t.filter(u=>u&&(u.name&&u.name.toLowerCase().includes(e)||u.description&&u.description.toLowerCase().includes(e)))}return t}),O(()=>it.value==="templates"&&ct.value?x.value.find(t=>t.id===ct.value):it.value==="entities"&&pt.value?N.value.find(t=>t.id===pt.value):null);const yt=O(()=>h.value);O(()=>yt.value?yt.value.replace(/\{\{([^}]+)\}\}/g,(t,e)=>`<span class="placeholder-highlight">${t}</span>`):"");const bt=async()=>{try{if(!X.value||!X.value.trim()){r.warning("请输入提示词规则名称");return}ie.value=!0;const t={id:ge.value||wt(),name:X.value,description:M.value||"",rule:{...o.value,content:h.value,editorConfig:et.value,prompts:o.value.prompts||[],updateTime:new Date().toISOString()},type:"generator"},e=await window.pywebview.api.book_controller.add_prompt(g.value,t),u=typeof e=="string"?JSON.parse(e):e;if(u.status==="success"){if(r.success(ge.value?"规则更新成功":"规则创建成功"),ge.value){const d=z.value.findIndex(p=>p.id===ge.value);d!==-1&&(z.value[d]=t)}else z.value.push(t);return!0}else return r.error(u.message||"保存失败"),!1}catch(t){return console.error("保存提示词规则失败:",t),r.error("保存失败: "+t.message),!1}finally{ie.value=!1}},jt=async t=>{try{if(!o.value){r.warning("请先创建或选择一个规则");return}if(o.value.prompts||(o.value.prompts=[]),o.value.prompts.push({id:wt(),name:t.name,description:t.description,content:t.content,timestamp:t.timestamp}),await bt()){const u=ge.value;if(u){const d=z.value.findIndex(p=>p.id===u);d!==-1&&(z.value[d].rule=JSON.parse(JSON.stringify(o.value)))}Ce.value=!1,r.success("内容已添加到当前规则")}}catch(e){console.error("添加到规则失败:",e),r.error("添加失败: "+e.message)}},wt=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){const e=Math.random()*16|0;return(t==="x"?e:e&3|8).toString(16)});Ct(async()=>{try{await ee.loadBooks(),ee.bookList?.length>0&&(g.value=ee.bookList[0].id,await Ge(g.value))}catch(t){console.error("初始化失败:",t),r.error("初始化失败，请刷新页面重试")}await Promise.all([])}),i("current");const We=i([]),Se=i(!1),Ee=i({count:5}),Te=i([]),At=async()=>{if(!vt.value){r.warning("请先配置生成规则");return}Pe.value=!0,Te.value=[];try{for(let t=0;t<Ee.value.count;t++){const e=await ft();e&&Te.value.push(e)}r.success(`成功生成 ${Te.value.length} 个提示词`)}catch(t){console.error("批量生成失败:",t),r.error("批量生成失败")}finally{Pe.value=!1}},Ft=t=>{h.value=t,Se.value=!1,r.success("已使用所选结果")},Mt=t=>{window.pywebview.api.copy_to_clipboard(t).then(()=>r.success("已复制到剪贴板")).catch(()=>r.error("复制失败"))},Jt=()=>{o.value.template.selectedEntityId=null,o.value.template.selectedDimensions=[]},Ce=i(!1),Ke=async()=>{try{if(!h.value){r.warning("没有内容可预览");return}let t=h.value;console.log("开始预览处理，原始内容:",t);const e=/{{模板:([^【}]+)(?:【([^】]+)】)?}}/g;let u;for(;(u=e.exec(t))!==null;){const k=u[1],j=u[2];console.log("匹配到模板占位符:",u[0],"模板ID:",k,"占位符中名称:",j);const U=x.value.find(f=>f.id===k),H=U?U.name:j||"未知模板";if(U){const f={name:U.name,dimensions:Array.isArray(U.dimensions)?U.dimensions:Object.entries(U.dimensions||{}).map(([S,V])=>({name:S,value:V}))},$=`【模板：${H}】
${JSON.stringify(f,null,2)}`;t=t.replace(u[0],$)}else t=t.replace(u[0],`【未找到ID为 ${k} 的模板】`)}const d=/{{实体:random:([^:【}]+)(?::([^:【}]+))?(?::([^:【}]*))?(?:【([^】]+)】)?}}/g;let p;for(;(p=d.exec(t))!==null;){const k=p[1],j=p[2]||"text",U=p[3]||"",H=p[4];console.log("匹配到随机实体占位符:",p[0],"模板ID:",k,"格式:",j,"排除维度:",U,"占位符中名称:",H);const f=U?U.split(",").filter(Boolean):[],$=N.value.filter(S=>S.template_id===k);if(console.log(`找到${$.length}个实体属于模板${k}`),$.length>0){const S=Math.floor(Math.random()*$.length),V=$[S];console.log("随机选择实体:",V.name,V.id);let w=ht(V,j,f);t=t.replace(p[0],w)}else{const S=x.value.find(V=>V.id===k)?.name||(H?H.replace(/^随机/,"").replace(/实体$/,""):"未知模板");t=t.replace(p[0],`【无法找到"${S}"模板的实体】`)}}const P=/{{实体:(?!random:)([^:【}]+)(?::([^:【}]*))?(?::([^:【}]*))?(?:【([^】]+)】)?:?}}/g;let R;for(;(R=P.exec(t))!==null;){const k=R[1],j=R[2]||"text",U=R[3]||"",H=R[4];console.log("匹配到实体占位符:",R[0],"实体ID:",k,"格式:",j,"排除维度:",U,"占位符中名称:",H);const f=U?U.split(",").filter(Boolean):[],$=N.value.find(S=>S.id===k);if(console.log("找到实体:",$?$.name:"未找到"),$){let S=ht($,j,f);t=t.replace(R[0],S)}else{const S=H||"未知实体";t=t.replace(R[0],`【无法找到"${S}"实体】`)}}const ae=/{{所有实体:([^:【}]+)(?::([^:【}]*))?(?::([^:【}]*))?(?:【([^】]+)】)?:?}}/g;let K;for(;(K=ae.exec(t))!==null;){const k=K[1],j=K[2]||"text",U=K[3]||"",H=K[4];console.log("匹配到所有实体占位符:",K[0],"模板ID:",k,"格式:",j,"排除维度:",U,"占位符中名称:",H);const f=U?U.split(",").filter(Boolean):[],$=N.value.filter(S=>S.template_id===k);if(console.log(`找到${$.length}个实体属于模板${k}`),$.length>0)if(j==="json"){const S=$.map(w=>{const Y={};for(const[ne,_e]of Object.entries(w.dimensions||{}))f.includes(ne)||(Y[ne]=_e);return{id:w.id,name:w.name,type:w.type,description:w.description,dimensions:Y}}),V=`【模板"${x.value.find(w=>w.id===k)?.name||"未知模板"}"的所有实体】
`+JSON.stringify({template_id:k,template_name:x.value.find(w=>w.id===k)?.name||"未知模板",entity_count:$.length,entities:S},null,2);t=t.replace(K[0],V)}else{const V=`【${x.value.find(w=>w.id===k)?.name||"未知模板"}的所有实体 (共${$.length}个)】

`+$.map(w=>{const Y=[`🔹 ${w.name||"未命名实体"}`];w.type&&Y.push(`类型：${w.type}`),w.description&&Y.push(`描述：${w.description}`);for(const[ne,_e]of Object.entries(w.dimensions||{}))f.includes(ne)||Y.push(`${ne}：${_e}`);return Y.join(`
`)}).join(`

`);t=t.replace(K[0],V)}else t=t.replace(K[0],`【无法找到模板ID为 ${k} 的实体】`)}const oe=/{{场景:([^:【}]+):([^:【}]+)(?::([^:【}]*))?(?:【([^】]+)】)?}}/g;let de;for(;(de=oe.exec(t))!==null;){const k=de[1],j=de[2],U=de[3]||"1",H=de[4];console.log("匹配到场景占位符:",de[0],"模式:",k,"ID值:",j,"数量:",U,"占位符中名称:",H);let f="";if(k==="manual"){const $=j.split(",").filter(Boolean),S=[];for(const V of b.value.pools)if(V.scenes)for(const w of V.scenes)$.includes(w.id)&&S.push(w);console.log(`找到${S.length}个指定场景`),S.length>0?f=S.map(V=>`【场景：${V.title||"未命名"}】
${V.description||"无描述"}`).join(`

`):f="【未找到指定的场景】"}else if(k==="random"){const $=j,S=parseInt(U,10)||1,V=b.value.pools.find(w=>w.id===$);if(V&&V.scenes&&V.scenes.length>0){const w=[...V.scenes].sort(()=>.5-Math.random()),Y=w.slice(0,Math.min(S,w.length));console.log(`从场景池随机抽取了${Y.length}个场景`),f=Y.map(ne=>`【场景：${ne.title||"未命名"}】
${ne.description||"无描述"}`).join(`

`)}else f=`【未找到ID为 ${$} 的场景池或其中没有场景】`}else f=`【不支持的场景模式：${k}】`;t=t.replace(de[0],f)}console.log("预览处理完成"),Ce.value=!0,He.value=t}catch(t){console.error("生成预览内容失败:",t),r.error("生成预览内容失败: "+t.message)}},ht=(t,e,u)=>{if(!t)return console.warn("formatEntityContent: 实体为空"),"【无效实体】";try{if(console.log("格式化实体:",t.name,"格式:",e,"排除维度:",u),e==="json"){const d={};for(const[p,P]of Object.entries(t.dimensions||{}))u.includes(p)||(d[p]=P);return JSON.stringify({id:t.id,name:t.name,type:t.type,description:t.description,dimensions:d},null,2)}else{const d=[`【实体：${t.name||"未命名实体"}】`];if(t.type&&d.push(`类型：${t.type}`),t.description&&d.push(`描述：${t.description}`),t.dimensions)for(const[p,P]of Object.entries(t.dimensions))u.includes(p)||d.push(`${p}：${P}`);return d.join(`
`)}}catch(d){return console.error("格式化实体内容失败:",d),`【格式化失败：${d.message}】`}},qt=(t,e)=>{if(!t||!t.dimensions)return console.warn("实体没有维度信息"),"";e=e||[],console.log("排除的维度:",e);const u=[];t.description&&u.push(`描述：${t.description}`);for(const d in t.dimensions)e.includes(d)||u.push(`${d}：${t.dimensions[d]}`);return u.join(`
`)},Gt=t=>N.value.filter(e=>e.template_id===t),He=i(""),Qt=t=>{t==="single"?o.value.template.singleTemplate.placeholderText="{{单个模板}}":t==="entity"?o.value.template.entityTemplate.placeholderText="{{单个实体}}":t==="all"&&(o.value.template.allEntitiesTemplate.placeholderText="{{所有实体}}"),h.value+=`
${o.value.template[t].placeholderText}`},Xe=i(!1),Ye=i(null),De=i(!1),Ne=i(null),we=i(null),Wt=()=>{we.value=null},Kt=()=>{if(!we.value)return;const t=N.value.find(d=>d.id===we.value);if(!t)return;const e=qt(t,o.value.template.entityTemplate.excludedDimensions),u=document.querySelector(".generator-content textarea");if(u){const d=u.selectionStart,p=u.selectionEnd,P=h.value.substring(0,d),R=h.value.substring(p);h.value=`${P}${e}${R}`,ze(()=>{u.focus();const ae=d+e.length;u.setSelectionRange(ae,ae)})}else h.value+=`
${e}`;De.value=!1},Ht=()=>{if(!o.value.template.selectedTemplateId){r.warning("请先选择一个模板");return}const t=x.value.find(u=>u.id===o.value.template.selectedTemplateId);if(!t){r.warning("未找到选中的模板");return}const e=`{{模板:${t.id}【${t.name||"未命名模板"}】}}`;me(e),r.success(`已插入"${t.name}"模板的占位符`)},Xt=()=>{if(!o.value.template.selectedEntityId){r.warning("请先选择一个实体");return}const t=N.value.find(u=>u.id===o.value.template.selectedEntityId);if(!t){r.warning("未找到选中的实体");return}const e=`{{实体:${o.value.template.selectedEntityId}:${o.value.template.entityOutputFormat}:${o.value.template.entityExcludedDimensions.join(",")}【${t.name||"未命名实体"}】}}`;me(e),r.success(`已插入实体"${t.name}"的占位符`)},Yt=()=>{if(!o.value.template.selectedTemplateId){r.warning("请先选择一个模板");return}const t=x.value.find(u=>u.id===o.value.template.selectedTemplateId);if(!t){r.warning("未找到选中的模板");return}const e=`{{所有实体:${t.id}:${o.value.template.allEntitiesOutputFormat}:${o.value.template.excludedDimensions.join(",")}【${t.name||"未命名模板"}的所有实体】}}`;me(e),r.success(`已插入"${t.name}"模板的所有实体占位符`)},me=t=>{console.log("尝试插入文本:",t),t.includes("{{")&&t.includes("}}")&&$t(t);const e=document.querySelector(".result-content-wrapper .el-textarea__inner");if(e){console.log("找到文本区域元素");const u=e.selectionStart,d=e.selectionEnd,p=h.value||"",P=p.substring(0,u),R=p.substring(d);h.value=`${P}${t}${R}`,r.success("已插入占位符"),ze(()=>{e.focus();const ae=u+t.length;e.setSelectionRange(ae,ae)})}else console.warn("未找到文本区域元素，将直接追加内容"),h.value?h.value+=`
`+t:h.value=t,r.success("已追加占位符到内容末尾");window.debugPVV.lastInsertedText=t};Ct(async()=>{try{await ee.loadBooks(),ee.bookList?.length>0&&(g.value=ee.bookList[0].id,await Ge(g.value))}catch(t){console.error("初始化失败:",t),r.error("初始化失败，请刷新页面重试")}await Promise.all([Qe()])}),i([]),i([]);const Zt=()=>{if(o.value.scene.mode==="manual"){if(!o.value.scene.selectedSceneIds?.length){r.warning("请先选择至少一个场景");return}const t=[];for(const u of o.value.scene.selectedSceneIds){let d="未知场景";for(const p of b.value.pools)if(p.scenes){const P=p.scenes.find(R=>R.id===u);if(P){d=P.title||"未命名场景";break}}t.push(d)}const e=`{{场景:manual:${o.value.scene.selectedSceneIds.join(",")}【${t.join("、")}】}}`;me(e),r.success(`已插入${t.length}个场景占位符`)}else{if(!o.value.scene.selectedPoolId){r.warning("请先选择一个场景池");return}const t=b.value.pools.find(d=>d.id===o.value.scene.selectedPoolId),e=t?t.name||"未命名场景池":"未知场景池",u=`{{场景:random:${o.value.scene.selectedPoolId}:${o.value.scene.randomCount||1}【随机${e}场景(${o.value.scene.randomCount||1}个)】}}`;me(u),r.success(`已插入"${e}"的随机场景占位符`)}};se(g,async()=>{await Qe()});const el=()=>{if(!o.value.template.selectedTemplateId||!qe.value.length){r.warning("请先选择一个包含实体的模板");return}const t=x.value.find(u=>u.id===o.value.template.selectedTemplateId);if(!t){r.warning("未找到选中的模板");return}const e=`{{实体:random:${o.value.template.selectedTemplateId}:${o.value.template.entityOutputFormat}:${o.value.template.entityExcludedDimensions.join(",")}【随机${t.name||"未命名模板"}实体】}}`;me(e),r.success(`已插入"${t.name}"模板的随机实体占位符`)};se(g,async()=>{if(g.value)try{const t=await window.pywebview.api.book_controller.get_entities(g.value),e=typeof t=="string"?JSON.parse(t):t;e.status==="success"?N.value=e.data||[]:(console.error("加载实体失败:",e.message),r.error("加载实体失败"))}catch(t){console.error("加载实体失败:",t),r.error("加载实体失败")}},{immediate:!0});const tl=()=>{console.log("书籍ID:",g.value),console.log("所有实体:",N.value),console.log("所有模板:",x.value);const t="f0b7cc16-9575-46d7-b5d2-2f8678f436a0",e=N.value.find(u=>u.id===t);return console.log("测试实体查找:",t,e),{selectedBookId:g.value,entityCount:N.value.length,templateCount:x.value.length,testEntity:e}},ll=()=>{console.log("书籍ID:",g.value),console.log("场景数据:",b.value),console.log("场景池数量:",b.value.pools?.length||0),console.log("当前场景池ID:",b.value.currentPoolId);let t=0;return b.value.pools?.forEach(e=>{e.scenes&&(t+=e.scenes.length,console.log(`场景池 "${e.name}" (${e.id}): ${e.scenes.length} 个场景`))}),{selectedBookId:g.value,poolCount:b.value.pools?.length||0,totalScenes:t,currentPoolId:b.value.currentPoolId,pools:b.value.pools}},$t=t=>{if(console.log("=== 占位符调试信息 ==="),console.log("占位符文本:",t),t.includes("{{实体:")){console.log("类型: 实体占位符");const e=t.match(/{{实体:([^:【}]+):([^:【}]+)(?::([^:【}]*))?(?:【([^】]+)】)?}}/);e&&(console.log("模式:",e[1]),console.log("模板ID:",e[2]),console.log("数量:",e[3]||"1"),console.log("显示名称:",e[4]||"无"))}else if(t.includes("{{场景:")){console.log("类型: 场景占位符");const e=t.match(/{{场景:([^:【}]+):([^:【}]+)(?::([^:【}]*))?(?:【([^】]+)】)?}}/);e&&(console.log("模式:",e[1]),console.log("ID值:",e[2]),console.log("数量:",e[3]||"1"),console.log("显示名称:",e[4]||"无"))}else console.log("类型: 未知占位符");console.log("=== 调试信息结束 ===")};window.debugPVV={entityData:tl,sceneData:ll,previewPrompt:Ke,insertTextToEditor:me,debugPlaceholder:$t,testInsert:t=>me(t)};const al=t=>{He.value=t.previewContent||"",h.value=t.content||"",Ce.value=!0},nl=t=>{St.confirm("确定要从当前规则中移除此提示词吗？","移除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const e=o.value.savedPrompts.findIndex(u=>u.id===t);e!==-1?(o.value.savedPrompts.splice(e,1),r.success("提示词已从规则中移除")):r.warning("未找到指定的提示词")}).catch(()=>{})},ol=t=>{if(!t.savedPrompts||t.savedPrompts.length===0){r.warning("选择的规则没有保存的提示词");return}const e=t.savedPrompts.filter(u=>!o.value.savedPrompts.some(d=>d.id===u.id));o.value.savedPrompts.push(...e),r.success(`已从规则中导入${e.length}个提示词`)},Ue=i(!1),Ie=i(null),sl=async()=>{try{if(!Ie.value){r.warning("请选择要导入的规则");return}const t=z.value.find(e=>e.id===Ie.value);if(!t||!t.rule||!t.rule.savedPrompts){r.warning("选择的规则不包含保存的提示词");return}ol(t.rule),Ue.value=!1}catch(t){console.error("导入规则失败:",t),r.error("导入规则失败: "+t.message)}};se(()=>g.value,t=>{t&&gt()},{immediate:!0});const ul=t=>{if(!t.rule?.prompts||t.rule.prompts.length===0){r.warning("该规则下暂无提示词");return}Oe.value=!0,he.value=t.rule.prompts||[],Vt.value=t.name,Le.value=he.value.length>0?0:-1,Ze.value=""},Oe=i(!1),he=i([]),Vt=i(""),rl=t=>{t&&(h.value=t,Oe.value=!1,L.value="generator",r.success("提示词已加载到编辑器"))},ge=i(null),Ze=i(""),Le=i(0);O(()=>Re.value.length===0?null:Le.value<0||Le.value>=Re.value.length?Re.value[0]:Re.value[Le.value]);const Re=O(()=>{if(!he.value)return[];if(!Ze.value)return he.value;const t=Ze.value.toLowerCase();return he.value.filter(e=>e.name&&e.name.toLowerCase().includes(t)||e.content&&e.content.toLowerCase().includes(t))}),et=i({fontSize:14,theme:"default",tabSize:2,lineNumbers:!0}),il=t=>({生成规则:"success",模板规则:"primary",场景规则:"warning",自定义:"info"})[t]||"info",dl=({row:t,rowIndex:e})=>e%2===0?"even-row":"odd-row",cl=t=>{xe.value=t},xe=i(1),Be=i(10),pl=()=>{o.value={template:{enabled:!1,mode:"single",selectedTemplateId:null,selectedDimensions:[],excludedDimensions:[],entityExcludedDimensions:[],entityOutputFormat:"text",allEntitiesOutputFormat:"text"},scene:{enabled:!1,mode:"manual",selectedSceneIds:[],randomCount:1,selectedPoolId:null,placeholderText:"{{场景描述}}"},prompts:[]},h.value="",X.value="",M.value="",ge.value=null},ml=()=>{pl(),L.value="generator",ze(()=>{const t=document.querySelector(".prompt-generator");t&&t.scrollIntoView({behavior:"smooth"})}),r.success("已切换到生成器模式，请开始编辑新规则")},vl=async()=>{try{await Ke(),r.success("已重新生成预览内容")}catch(t){console.error("重新生成预览失败：",t),r.error("重新生成预览失败: "+t.message)}},fl=O(()=>{if(!o.value.template.selectedTemplateId)return[];const t=N.value.filter(u=>u.template_id===o.value.template.selectedTemplateId),e=new Set;return t.forEach(u=>{u.dimensions&&Object.keys(u.dimensions).forEach(d=>{e.add(d)})}),Array.from(e).map(u=>({name:u}))});return(t,e)=>{const u=kl,d=Cl,p=ot,P=st,R=ut,ae=Dt,K=xl,oe=Il,de=Pl,k=El,j=Sl,U=Dl,H=Nl,f=Tt,$=Et,S=Ul,V=Ll,w=Ol,Y=Bl,ne=Rl,_e=zl,gl=jl,kt=Al,$e=rt;return c(),y("div",La,[s("div",Ra,[s("div",Ba,[e[46]||(e[46]=s("h1",{class:"page-title"},"AI提示词",-1)),l(d,{modelValue:g.value,"onUpdate:modelValue":e[0]||(e[0]=n=>g.value=n),placeholder:"选择书籍",class:"book-selector",onChange:Ge},{default:a(()=>[(c(!0),y(B,null,J(T(ee).bookList,n=>(c(),E(u,{key:n.id,label:n.title,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("div",za,[l(p,{class:Ae(["tab-button",{active:L.value==="promptList"}]),onClick:e[1]||(e[1]=n=>L.value="promptList")},{default:a(()=>e[47]||(e[47]=[m(" 规则列表 ")])),_:1},8,["class"]),l(p,{class:Ae(["tab-button",{active:L.value==="generator"}]),onClick:e[2]||(e[2]=n=>L.value="generator")},{default:a(()=>e[48]||(e[48]=[m(" 规则生成器 ")])),_:1},8,["class"])])]),g.value?(c(),y("div",ja,[It(s("div",Aa,[s("div",Fa,[s("div",Ma,[e[49]||(e[49]=s("h2",null,"AI提示词规则列表",-1)),l(R,{modelValue:Q.value,"onUpdate:modelValue":e[3]||(e[3]=n=>Q.value=n),placeholder:"搜索规则",class:"search-input",clearable:"",onClear:ye},{prefix:a(()=>[l(P,null,{default:a(()=>[l(T(at))]),_:1})]),_:1},8,["modelValue"])]),s("div",Ja,[l(p,{type:"primary",onClick:ml},{default:a(()=>[l(P,null,{default:a(()=>[l(T(Ve))]),_:1}),e[50]||(e[50]=m(" 新建规则 "))]),_:1})])]),s("div",qa,[z.value.length===0?(c(),E(ae,{key:0,description:"暂无提示词，点击新建按钮创建"})):(c(),E(U,{key:1,data:pe.value,style:{width:"100%"},"header-cell-style":{background:"var(--el-bg-color-overlay)",color:"var(--el-text-color-primary)",fontWeight:"600"},"row-class-name":dl},{default:a(()=>[l(oe,{prop:"name",label:"规则名称","min-width":"180"},{default:a(({row:n})=>[s("div",Ga,[s("span",Qa,v(n.name),1),n.type?(c(),E(K,{key:0,size:"small",type:il(n.type),class:"prompt-type-tag"},{default:a(()=>[m(v(n.type),1)]),_:2},1032,["type"])):ce("",!0)])]),_:1}),l(oe,{prop:"description",label:"描述","min-width":"220"},{default:a(({row:n})=>[s("div",Wa,v(n.description||"暂无描述"),1)]),_:1}),l(oe,{label:"提示词",width:"120",align:"center"},{default:a(({row:n})=>[s("div",Ka,[l(de,{value:n.rule?.prompts?.length||0,type:n.rule?.prompts?.length?"primary":"info",class:"prompt-badge"},null,8,["value","type"])])]),_:1}),l(oe,{prop:"updateTime",label:"更新时间",width:"160"},{default:a(({row:n})=>[s("div",Ha,v(zt(n.updateTime)),1)]),_:1}),l(oe,{label:"操作",width:"200",fixed:"right"},{default:a(({row:n})=>[s("div",Xa,[l(j,null,{default:a(()=>[l(k,{content:"查看提示词",placement:"top"},{default:a(()=>[l(p,{type:"primary",link:"",onClick:Z=>ul(n)},{default:a(()=>[l(P,null,{default:a(()=>[l(T(Tl))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),l(k,{content:"编辑规则",placement:"top"},{default:a(()=>[l(p,{type:"primary",link:"",onClick:Z=>le(n)},{default:a(()=>[l(P,null,{default:a(()=>[l(T(lt))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),l(k,{content:"删除规则",placement:"top"},{default:a(()=>[l(p,{type:"danger",link:"",onClick:Z=>F(n)},{default:a(()=>[l(P,null,{default:a(()=>[l(T(Pt))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)])]),_:1})]),_:1},8,["data"])),s("div",Ya,[l(H,{"current-page":xe.value,"onUpdate:currentPage":cl,"page-size":Be.value,"page-sizes":[10,20,50,100],total:pe.value.length,"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ve},null,8,["current-page","page-size","total"])])])],512),[[xt,L.value==="promptList"]]),It(s("div",Za,[s("div",en,[e[53]||(e[53]=s("div",{class:"header-content"},[s("h2",null,"规则生成器")],-1)),s("div",tn,[l(p,{type:"primary",onClick:ft,disabled:!vt.value},{default:a(()=>[l(P,null,{default:a(()=>[l(T(tt))]),_:1}),e[51]||(e[51]=m(" 重新生成 "))]),_:1},8,["disabled"]),l(p,{type:"success",onClick:bt,disabled:!X.value||!h.value},{default:a(()=>[l(P,null,{default:a(()=>[l(T(Nt))]),_:1}),e[52]||(e[52]=m(" 保存规则 "))]),_:1},8,["disabled"])])]),s("div",ln,[s("div",an,[s("div",nn,[e[54]||(e[54]=s("h3",{class:"panel-title"},"提示词规则",-1)),l($,{model:be.value,"label-width":"80px"},{default:a(()=>[l(f,{label:"规则名称",required:""},{default:a(()=>[l(R,{modelValue:X.value,"onUpdate:modelValue":e[4]||(e[4]=n=>X.value=n),placeholder:"输入提示词规则名称"},null,8,["modelValue"])]),_:1}),l(f,{label:"描述"},{default:a(()=>[l(R,{modelValue:M.value,"onUpdate:modelValue":e[5]||(e[5]=n=>M.value=n),type:"textarea",rows:2,placeholder:"描述这个生成规则的用途"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),s("div",on,[e[57]||(e[57]=s("h3",{class:"panel-title"},"提示词编辑",-1)),s("div",sn,[l(j,null,{default:a(()=>[l(k,{content:"复制到剪贴板",placement:"top"},{default:a(()=>[l(p,{size:"small",onClick:Rt,disabled:!h.value},{default:a(()=>[l(P,null,{default:a(()=>[l(T(je))]),_:1}),e[55]||(e[55]=m(" 复制 "))]),_:1},8,["disabled"])]),_:1}),l(k,{content:"清空当前内容",placement:"top"},{default:a(()=>[l(p,{size:"small",type:"danger",onClick:Bt},{default:a(()=>[l(P,null,{default:a(()=>[l(T(Pt))]),_:1}),e[56]||(e[56]=m(" 清空 "))]),_:1})]),_:1})]),_:1})]),s("div",un,[l(R,{modelValue:h.value,"onUpdate:modelValue":e[6]||(e[6]=n=>h.value=n),type:"textarea",rows:12,placeholder:"在此编辑提示词，可以插入模板和场景占位符"},null,8,["modelValue"])])])]),s("div",rn,[s("div",dn,[s("div",cn,[s("div",pn,[e[58]||(e[58]=s("h4",null,"模板规则",-1)),l(S,{modelValue:o.value.template.enabled,"onUpdate:modelValue":e[7]||(e[7]=n=>o.value.template.enabled=n),"active-text":"启用"},null,8,["modelValue"])]),s("div",mn,[l(_e,null,{default:a(()=>[o.value.template.enabled?(c(),E($,{key:0,class:"rule-form","label-position":"top"},{default:a(()=>[l(f,{label:"使用模式"},{default:a(()=>[l(w,{modelValue:o.value.template.mode,"onUpdate:modelValue":e[8]||(e[8]=n=>o.value.template.mode=n)},{default:a(()=>[l(V,{label:"single"},{default:a(()=>e[59]||(e[59]=[m("单个模板")])),_:1}),l(V,{label:"entity"},{default:a(()=>e[60]||(e[60]=[m("单个实体")])),_:1}),l(V,{label:"all"},{default:a(()=>e[61]||(e[61]=[m("所有实体")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o.value.template.mode==="single"?(c(),y(B,{key:0},[l(f,{label:"选择模板"},{default:a(()=>[l(d,{modelValue:o.value.template.selectedTemplateId,"onUpdate:modelValue":e[9]||(e[9]=n=>o.value.template.selectedTemplateId=n),filterable:"",placeholder:"选择模板"},{default:a(()=>[(c(!0),y(B,null,J(x.value,n=>(c(),E(u,{key:n.id,label:n.name,value:n.id},{default:a(()=>[s("span",null,v(n.name),1),s("span",vn,v(ke(n))+"个实体",1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(f,null,{default:a(()=>[l(p,{type:"primary",onClick:Ht,disabled:!o.value.template.selectedTemplateId},{default:a(()=>[l(P,null,{default:a(()=>[l(T(Ve))]),_:1}),e[62]||(e[62]=m(" 插入此模板 "))]),_:1},8,["disabled"])]),_:1})],64)):o.value.template.mode==="entity"?(c(),y(B,{key:1},[l(f,{label:"选择模板"},{default:a(()=>[l(d,{modelValue:o.value.template.selectedTemplateId,"onUpdate:modelValue":e[10]||(e[10]=n=>o.value.template.selectedTemplateId=n),filterable:"",placeholder:"选择模板",onChange:Jt},{default:a(()=>[(c(!0),y(B,null,J(x.value,n=>(c(),E(u,{key:n.id,label:n.name,value:n.id},{default:a(()=>[s("span",null,v(n.name),1),s("span",fn,v(ke(n))+"个实体",1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(f,{label:"选择实体"},{default:a(()=>[l(d,{modelValue:o.value.template.selectedEntityId,"onUpdate:modelValue":e[11]||(e[11]=n=>o.value.template.selectedEntityId=n),filterable:"",placeholder:"选择实体"},{default:a(()=>[(c(!0),y(B,null,J(qe.value,n=>(c(),E(u,{key:n.id,label:n.name,value:n.id},{default:a(()=>[s("span",null,v(n.name),1),n.type?(c(),y("span",gn,"("+v(n.type)+")",1)):ce("",!0)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(f,{label:"输出格式"},{default:a(()=>[l(w,{modelValue:o.value.template.entityOutputFormat,"onUpdate:modelValue":e[12]||(e[12]=n=>o.value.template.entityOutputFormat=n)},{default:a(()=>[l(V,{label:"text"},{default:a(()=>e[63]||(e[63]=[m("文本格式")])),_:1}),l(V,{label:"json"},{default:a(()=>e[64]||(e[64]=[m("JSON格式")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l(f,{label:"排除维度"},{default:a(()=>[l(ne,{modelValue:o.value.template.entityExcludedDimensions,"onUpdate:modelValue":e[13]||(e[13]=n=>o.value.template.entityExcludedDimensions=n)},{default:a(()=>[(c(!0),y(B,null,J(fl.value,n=>(c(),E(Y,{key:n.name,label:n.name},{default:a(()=>[m(v(n.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(f,null,{default:a(()=>[s("div",_n,[l(p,{type:"primary",onClick:Xt,disabled:!o.value.template.selectedEntityId},{default:a(()=>[l(P,null,{default:a(()=>[l(T(Ve))]),_:1}),e[65]||(e[65]=m(" 插入此实体 "))]),_:1},8,["disabled"]),l(p,{type:"primary",onClick:el,disabled:!o.value.template.selectedTemplateId||!qe.value.length},{default:a(()=>[l(P,null,{default:a(()=>[l(T(tt))]),_:1}),e[66]||(e[66]=m(" 插入随机实体 "))]),_:1},8,["disabled"])])]),_:1})],64)):(c(),y(B,{key:2},[l(f,{label:"选择模板"},{default:a(()=>[l(d,{modelValue:o.value.template.selectedTemplateId,"onUpdate:modelValue":e[14]||(e[14]=n=>o.value.template.selectedTemplateId=n),filterable:"",placeholder:"选择模板"},{default:a(()=>[(c(!0),y(B,null,J(x.value,n=>(c(),E(u,{key:n.id,label:n.name,value:n.id},{default:a(()=>[s("span",null,v(n.name),1),s("span",yn,v(ke(n))+"个实体",1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(f,{label:"输出格式"},{default:a(()=>[l(w,{modelValue:o.value.template.allEntitiesOutputFormat,"onUpdate:modelValue":e[15]||(e[15]=n=>o.value.template.allEntitiesOutputFormat=n)},{default:a(()=>[l(V,{label:"text"},{default:a(()=>e[67]||(e[67]=[m("文本格式")])),_:1}),l(V,{label:"json"},{default:a(()=>e[68]||(e[68]=[m("JSON格式")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l(f,{label:"排除维度"},{default:a(()=>[l(ne,{modelValue:o.value.template.excludedDimensions,"onUpdate:modelValue":e[16]||(e[16]=n=>o.value.template.excludedDimensions=n)},{default:a(()=>[(c(!0),y(B,null,J(Ot.value,n=>(c(),E(Y,{key:n.name,label:n.name},{default:a(()=>[m(v(n.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(f,null,{default:a(()=>[l(p,{type:"primary",onClick:Yt,disabled:!o.value.template.selectedTemplateId},{default:a(()=>[l(P,null,{default:a(()=>[l(T(Ve))]),_:1}),e[69]||(e[69]=m(" 插入所有实体 "))]),_:1},8,["disabled"])]),_:1})],64))]),_:1})):ce("",!0)]),_:1})])]),s("div",bn,[s("div",wn,[e[70]||(e[70]=s("h4",null,"场景规则",-1)),l(S,{modelValue:o.value.scene.enabled,"onUpdate:modelValue":e[17]||(e[17]=n=>o.value.scene.enabled=n),"active-text":"启用"},null,8,["modelValue"])]),s("div",hn,[l(_e,null,{default:a(()=>[o.value.scene.enabled?(c(),E($,{key:0,class:"rule-form","label-position":"top"},{default:a(()=>[l(f,{label:"场景模式"},{default:a(()=>[l(w,{modelValue:o.value.scene.mode,"onUpdate:modelValue":e[18]||(e[18]=n=>o.value.scene.mode=n)},{default:a(()=>[l(V,{label:"manual"},{default:a(()=>e[71]||(e[71]=[m("指定场景")])),_:1}),l(V,{label:"random"},{default:a(()=>e[72]||(e[72]=[m("随机抽取")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o.value.scene.mode==="manual"?(c(),E(f,{key:0,label:"选择场景"},{default:a(()=>[l(d,{modelValue:o.value.scene.selectedSceneIds,"onUpdate:modelValue":e[19]||(e[19]=n=>o.value.scene.selectedSceneIds=n),multiple:"",filterable:"",placeholder:"选择场景",class:"scene-selector"},{default:a(()=>[(c(!0),y(B,null,J(b.value.pools,n=>(c(),E(gl,{key:n.id,label:n.name},{default:a(()=>[(c(!0),y(B,null,J(n.scenes,Z=>(c(),E(u,{key:Z.id,label:Z.title,value:Z.id},{default:a(()=>[s("div",$n,[s("div",Vn,v(Z.title),1),s("div",kn,v(Z.description),1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})):(c(),y(B,{key:1},[l(f,{label:"场景池"},{default:a(()=>[l(d,{modelValue:o.value.scene.selectedPoolId,"onUpdate:modelValue":e[20]||(e[20]=n=>o.value.scene.selectedPoolId=n)},{default:a(()=>[(c(!0),y(B,null,J(b.value.pools,n=>(c(),E(u,{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(f,{label:"抽取数量"},{default:a(()=>[l(kt,{modelValue:o.value.scene.randomCount,"onUpdate:modelValue":e[21]||(e[21]=n=>o.value.scene.randomCount=n),min:1,max:t.getMaxSceneCount},null,8,["modelValue","max"])]),_:1})],64)),l(f,null,{default:a(()=>[l(p,{type:"primary",onClick:Zt,disabled:o.value.scene.mode==="manual"&&(!o.value.scene.selectedSceneIds||o.value.scene.selectedSceneIds.length===0)||o.value.scene.mode==="random"&&!o.value.scene.selectedPoolId},{default:a(()=>[l(P,null,{default:a(()=>[l(T(Ve))]),_:1}),e[73]||(e[73]=m(" 插入场景描述 "))]),_:1},8,["disabled"])]),_:1})]),_:1})):ce("",!0)]),_:1})])])])])])],512),[[xt,L.value==="generator"]])])):(c(),y("div",Cn,[l(ae,{description:"请先选择一本书籍","image-size":200},{image:a(()=>[l(P,{size:64,class:"empty-icon"},{default:a(()=>[l(T(lt))]),_:1})]),_:1})])),l($e,{modelValue:I.value,"onUpdate:modelValue":e[26]||(e[26]=n=>I.value=n),title:_.value.id?"编辑提示词":"新建提示词",width:"800px","close-on-click-modal":!1,class:"prompt-edit-dialog"},{footer:a(()=>[s("div",xn,[l(p,{onClick:e[25]||(e[25]=n=>I.value=!1)},{default:a(()=>e[77]||(e[77]=[m("取消")])),_:1}),l(p,{type:"primary",onClick:t.savePrompt,disabled:!t.canSavePrompt},{default:a(()=>e[78]||(e[78]=[m("确定")])),_:1},8,["onClick","disabled"])])]),default:a(()=>[l($,{model:_.value,"label-width":"80px"},{default:a(()=>[l(f,{label:"名称",required:""},{default:a(()=>[l(R,{modelValue:_.value.name,"onUpdate:modelValue":e[22]||(e[22]=n=>_.value.name=n),placeholder:"输入提示词名称"},null,8,["modelValue"])]),_:1}),l(f,{label:"描述"},{default:a(()=>[l(R,{modelValue:_.value.description,"onUpdate:modelValue":e[23]||(e[23]=n=>_.value.description=n),type:"textarea",rows:3,placeholder:"输入提示词描述"},null,8,["modelValue"])]),_:1}),l(f,{label:"内容",required:""},{default:a(()=>[l(R,{modelValue:_.value.content,"onUpdate:modelValue":e[24]||(e[24]=n=>_.value.content=n),type:"textarea",rows:10,placeholder:"输入提示词内容"},null,8,["modelValue"]),s("div",In,[e[76]||(e[76]=s("span",null,"可用占位符格式：",-1)),l(K,{size:"small",effect:"plain"},{default:a(()=>e[74]||(e[74]=[m(v(123))])),_:1}),l(K,{size:"small",effect:"plain"},{default:a(()=>e[75]||(e[75]=[m(v(123))])),_:1})])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),l($e,{modelValue:D.value,"onUpdate:modelValue":e[30]||(e[30]=n=>D.value=n),title:W.value?.name||"未命名提示词",width:"800px","close-on-click-modal":!0,class:"prompt-detail-dialog"},{footer:a(()=>[s("div",On,[l(p,{onClick:e[27]||(e[27]=n=>D.value=!1)},{default:a(()=>e[83]||(e[83]=[m("关闭")])),_:1}),l(p,{type:"primary",onClick:e[28]||(e[28]=n=>le(W.value))},{default:a(()=>e[84]||(e[84]=[m("编辑")])),_:1}),l(p,{type:"success",onClick:e[29]||(e[29]=n=>fe(W.value))},{default:a(()=>e[85]||(e[85]=[m("复制到剪贴板")])),_:1})])]),default:a(()=>[s("div",Pn,[s("div",Sn,[e[79]||(e[79]=s("h3",null,"提示词名称",-1)),s("p",null,v(W.value?.name||"未命名提示词"),1)]),s("div",En,[e[80]||(e[80]=s("h3",null,"描述",-1)),s("p",null,v(W.value?.description||"无描述"),1)]),s("div",Tn,[e[81]||(e[81]=s("h3",null,"内容",-1)),s("div",Dn,[s("pre",null,v(W.value?.content||"无内容"),1)])]),s("div",Nn,[e[82]||(e[82]=s("h3",null,"占位符",-1)),s("div",Un,[(c(!0),y(B,null,J(C(W.value?.content||""),n=>(c(),E(K,{key:n,size:"large",class:"placeholder-tag",effect:"light"},{default:a(()=>[m(v(n),1)]),_:2},1024))),128)),C(W.value?.content||"").length===0?(c(),E(ae,{key:0,description:"此提示词中没有占位符"})):ce("",!0)])])])]),_:1},8,["modelValue","title"]),l($e,{modelValue:Se.value,"onUpdate:modelValue":e[33]||(e[33]=n=>Se.value=n),title:"批量生成提示词",width:"600px",class:"batch-generate-dialog"},{footer:a(()=>[s("div",jn,[l(p,{onClick:e[32]||(e[32]=n=>Se.value=!1)},{default:a(()=>e[88]||(e[88]=[m("关闭")])),_:1}),l(p,{type:"primary",onClick:At,loading:Pe.value},{default:a(()=>e[89]||(e[89]=[m(" 开始生成 ")])),_:1},8,["loading"])])]),default:a(()=>[l($,{model:Ee.value,"label-position":"top"},{default:a(()=>[l(f,{label:"生成数量"},{default:a(()=>[l(kt,{modelValue:Ee.value.count,"onUpdate:modelValue":e[31]||(e[31]=n=>Ee.value.count=n),min:1,max:20,step:1},null,8,["modelValue"])]),_:1}),l(f,{label:"生成结果"},{default:a(()=>[s("div",Ln,[l(_e,{height:"300px"},{default:a(()=>[(c(!0),y(B,null,J(Te.value,(n,Z)=>(c(),y("div",{key:Z,class:"batch-result-item"},[s("div",Rn,[s("span",Bn,"结果 #"+v(Z+1),1),l(j,null,{default:a(()=>[l(p,{size:"small",onClick:_l=>Ft(n)},{default:a(()=>e[86]||(e[86]=[m(" 使用 ")])),_:2},1032,["onClick"]),l(p,{size:"small",onClick:_l=>Mt(n)},{default:a(()=>e[87]||(e[87]=[m(" 复制 ")])),_:2},1032,["onClick"])]),_:2},1024)]),s("div",zn,v(n),1)]))),128))]),_:1})])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(ea,{modelValue:Ce.value,"onUpdate:modelValue":e[34]||(e[34]=n=>Ce.value=n),"prompt-name":X.value||"生成的提示词","prompt-description":M.value,"prompt-content":He.value,onSaveToRule:jt,onRegenerate:vl},null,8,["modelValue","prompt-name","prompt-description","prompt-content"]),l($e,{modelValue:Xe.value,"onUpdate:modelValue":e[37]||(e[37]=n=>Xe.value=n),title:"选择模板",width:"50%"},{footer:a(()=>[s("span",Fn,[l(p,{onClick:e[36]||(e[36]=n=>Xe.value=!1)},{default:a(()=>e[90]||(e[90]=[m("取消")])),_:1}),l(p,{type:"primary",onClick:Qt,disabled:!Ye.value},{default:a(()=>e[91]||(e[91]=[m(" 插入 ")])),_:1},8,["disabled"])])]),default:a(()=>[l($,null,{default:a(()=>[l(f,{label:"选择模板"},{default:a(()=>[l(d,{modelValue:Ye.value,"onUpdate:modelValue":e[35]||(e[35]=n=>Ye.value=n),filterable:"",placeholder:"选择模板",style:{width:"100%"}},{default:a(()=>[(c(!0),y(B,null,J(x.value,n=>(c(),E(u,{key:n.id,label:n.name,value:n.id},{default:a(()=>[s("span",null,v(n.name),1),s("span",An,v(ke(n))+"个实体",1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),l($e,{modelValue:De.value,"onUpdate:modelValue":e[41]||(e[41]=n=>De.value=n),title:"选择实体",width:"50%"},{footer:a(()=>[s("span",qn,[l(p,{onClick:e[40]||(e[40]=n=>De.value=!1)},{default:a(()=>e[92]||(e[92]=[m("取消")])),_:1}),l(p,{type:"primary",onClick:Kt,disabled:!we.value},{default:a(()=>e[93]||(e[93]=[m(" 插入 ")])),_:1},8,["disabled"])])]),default:a(()=>[l($,null,{default:a(()=>[l(f,{label:"选择模板"},{default:a(()=>[l(d,{modelValue:Ne.value,"onUpdate:modelValue":e[38]||(e[38]=n=>Ne.value=n),filterable:"",placeholder:"选择模板",onChange:Wt,style:{width:"100%"}},{default:a(()=>[(c(!0),y(B,null,J(x.value,n=>(c(),E(u,{key:n.id,label:n.name,value:n.id},{default:a(()=>[s("span",null,v(n.name),1),s("span",Mn,v(ke(n))+"个实体",1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),Ne.value?(c(),E(f,{key:0,label:"选择实体"},{default:a(()=>[l(d,{modelValue:we.value,"onUpdate:modelValue":e[39]||(e[39]=n=>we.value=n),filterable:"",placeholder:"选择实体",style:{width:"100%"}},{default:a(()=>[(c(!0),y(B,null,J(Gt(Ne.value),n=>(c(),E(u,{key:n.id,label:n.name,value:n.id},{default:a(()=>[s("span",null,v(n.name),1),n.type?(c(),y("span",Jn,"("+v(n.type)+")",1)):ce("",!0)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):ce("",!0)]),_:1})]),_:1},8,["modelValue"]),o.value.savedPrompts&&o.value.savedPrompts.length>0?(c(),y("div",Gn,[s("h3",Qn,"规则已保存提示词 ("+v(o.value.savedPrompts.length)+")",1),l(U,{data:o.value.savedPrompts,style:{width:"100%"},size:"small"},{default:a(()=>[l(oe,{label:"保存时间",width:"180"},{default:a(n=>[m(v(new Date(n.row.timestamp).toLocaleString()),1)]),_:1}),l(oe,{label:"预览内容"},{default:a(n=>[s("div",Wn,v(n.row.previewContent||n.row.content),1)]),_:1}),l(oe,{label:"操作",width:"150"},{default:a(n=>[l(p,{size:"small",onClick:Z=>al(n.row)},{default:a(()=>e[94]||(e[94]=[m("查看")])),_:2},1032,["onClick"]),l(p,{size:"small",type:"danger",onClick:Z=>nl(n.row.id)},{default:a(()=>e[95]||(e[95]=[m("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])):ce("",!0),l($e,{modelValue:Ue.value,"onUpdate:modelValue":e[44]||(e[44]=n=>Ue.value=n),title:"导入规则提示词",width:"500px"},{footer:a(()=>[s("span",Kn,[l(p,{onClick:e[43]||(e[43]=n=>Ue.value=!1)},{default:a(()=>e[96]||(e[96]=[m("取消")])),_:1}),l(p,{type:"primary",onClick:sl,disabled:!Ie.value},{default:a(()=>e[97]||(e[97]=[m(" 导入 ")])),_:1},8,["disabled"])])]),default:a(()=>[l($,null,{default:a(()=>[l(f,{label:"选择规则"},{default:a(()=>[l(d,{modelValue:Ie.value,"onUpdate:modelValue":e[42]||(e[42]=n=>Ie.value=n),filterable:"",placeholder:"选择规则"},{default:a(()=>[(c(!0),y(B,null,J(z.value.filter(n=>n.type==="generator"),n=>(c(),E(u,{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),l(Oa,{modelValue:Oe.value,"onUpdate:modelValue":e[45]||(e[45]=n=>Oe.value=n),"rule-name":Vt.value,prompts:he.value,onUsePrompt:rl},null,8,["modelValue","rule-name","prompts"])])}}},fo=nt(Hn,[["__scopeId","data-v-3bd12816"]]);export{fo as default};
