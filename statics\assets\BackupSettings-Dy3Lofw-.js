import{_ as le,bh as E,r as x,w as re,o as ne,U as ie,b as S,m as h,e as u,d as a,g as s,v as w,B as ue,C as v,bD as F,t as ce,p as T,q as pe,an as de,X as me,am as fe,ar as we,G as M,s as _e,ee as $,j as ge,eh as ve,F as B,aa as ke,a_ as be,V as ye,$ as J,ad as he,J as Be,af as Ce,E as l,av as Z,bF as Ve}from"./entry-BIjVVog3.js";/* empty css                   *//* empty css                        *//* empty css                    *//* empty css               *//* empty css                    *//* empty css                *//* empty css                 *//* empty css                        *//* empty css                  */const xe={class:"backup-settings"},Ie={class:"settings-section"},ze={class:"section-header"},Ee={class:"header-actions"},Se={class:"panel-content"},Te={class:"backup-content"},De={class:"settings-card backup-settings-form"},Ne={class:"input-with-tip"},Pe={class:"input-with-tip"},Ue={class:"path-input-group"},Fe={class:"path-input-group"},Me={key:0,class:"settings-card backup-progress"},$e={class:"progress-message"},Je={class:"settings-card backup-history"},Ze={class:"card-header"},Le={class:"header-controls"},Oe={class:"table-container"},He={class:"table-wrapper"},Ae={__name:"BackupSettings",setup(Ge){const p=E("configStore"),I=E("showLoading"),_=E("hideLoading"),o=x({autoBackup:!1,backupInterval:60,keepBackups:10,useZip:!0,backupDir:"",targetDir:""}),c=x([]);let d=null;const f=x({visible:!1,percent:0,message:"",status:"normal"}),y=x(!1);re(y,async t=>{t&&c.value.length>0&&await k(!1,!0)});const D=async()=>{try{if(!window.pywebview||!window.pywebview.api){l.error("API未初始化，请刷新页面重试");return}if(!o.value.backupDir){l.warning("请先选择需要备份的目录");return}if(!o.value.targetDir){l.warning("请先选择备份保存的目录");return}I("正在执行备份..."),f.value={visible:!0,percent:0,message:"正在准备备份...",status:"normal"},d=setTimeout(()=>{console.log("备份超时，强制关闭加载状态"),_(),f.value.visible=!1,l.error("备份超时，请检查目录是否可访问")},3e5);const t=await window.pywebview.api.backup_data({backup_dir:o.value.backupDir,target_dir:o.value.targetDir,use_zip:o.value.useZip,is_auto:!1}),e=typeof t=="string"?JSON.parse(t):t;if(e.status!=="success")throw _(),new Error(e.message||"备份失败")}catch(t){console.error("备份失败:",t),l.error("备份失败："+(t.message||t.toString())),d&&(clearTimeout(d),d=null),_()}},k=async(t=!1,e=!1)=>{try{if(!window.pywebview||!window.pywebview.api){l.error("API未初始化，请刷新页面重试");return}if(!o.value.targetDir){c.value=[],t&&l.warning("请先设置备份目标目录");return}const r=await window.pywebview.api.get_backup_history({target_dir:o.value.targetDir,calculate_size:e}),i=typeof r=="string"?JSON.parse(r):r;if(i.status==="success"){const m=i.data||[];c.value=[...m],t&&l.success(`已刷新备份历史，共 ${c.value.length} 条记录`)}else c.value=[],i.message!=="备份目录不存在"&&l.error("获取备份历史失败："+i.message)}catch(r){console.error("获取备份历史出错:",r),c.value=[],l.error("获取备份历史失败："+(r.message||r.toString()))}},L=async()=>{await k(!0,y.value)},z=t=>{if(!t)return"未知时间";const e=t.toString().length<=10?t*1e3:t;return new Date(e).toLocaleString("zh-CN")},O=t=>{if(t===0)return"0 B";const e=1024,r=["B","KB","MB","GB"],i=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,i)).toFixed(2))+" "+r[i]},H=async t=>{try{await Z.confirm(`确定要从以下备份恢复吗？

备份时间: ${z(t.time)}
备份路径: ${t.path}
备份类型: ${t.type==="auto"?"自动备份":"手动备份"}

⚠️ 警告：恢复备份将覆盖当前数据，此操作不可撤销！
应用将在恢复完成后自动重启。`,"恢复确认",{confirmButtonText:"确定恢复",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}),I("正在恢复备份...");const e=await window.pywebview.api.restore_backup({backup_path:t.path}),r=typeof e=="string"?JSON.parse(e):e;if(r.status==="success"){_();let i=3;const m=()=>{i>0?(l({message:`恢复成功！应用将在 ${i} 秒后重启...`,type:"success",duration:1e3}),i--,setTimeout(m,1e3)):window.pywebview.api.restart_application().catch(g=>{l.error("重启应用失败，请手动重启"),console.error("重启失败:",g)})};m()}else throw new Error(r.message||"恢复失败")}catch(e){e!=="cancel"&&(console.error("恢复备份失败:",e),l.error("恢复失败："+e.message))}finally{_()}},A=async t=>{try{await Z.confirm(`确定要删除备份 "${z(t.time)}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),I("正在删除备份...");const e=await window.pywebview.api.delete_backup({backup_path:t.path}),r=typeof e=="string"?JSON.parse(e):e;if(r.status==="success"){l.success("删除成功");const i=c.value.findIndex(m=>m.path===t.path);i!==-1&&(c.value.splice(i,1),c.value=[...c.value]),setTimeout(async()=>{await k()},1e3)}else throw new Error(r.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除备份失败:",e),l.error("删除失败："+(e.message||e.toString())))}finally{_()}},G=async t=>{try{if(await p.updateConfigItem("backup.autoBackup",t),t){const e=await window.pywebview.api.check_auto_backup(),r=typeof e=="string"?JSON.parse(e):e;if(r.status==="success")l.success("自动备份已启用");else throw new Error(r.message||"启动自动备份失败")}else await window.pywebview.api.stop_auto_backup(),l.info("自动备份已停止")}catch(e){console.error("更新自动备份设置失败:",e),l.error("更新自动备份设置失败："+e.message),o.value.autoBackup=!t}},j=async t=>{try{await p.updateConfigItem("backup.backupInterval",t)}catch(e){console.error("更新备份间隔失败:",e),l.error("更新备份周期失败："+e.message),o.value.backupInterval=p.backup.backupInterval}},K=async t=>{try{await p.updateConfigItem("backup.keepBackups",t)}catch(e){console.error("更新保留数量失败:",e),l.error("更新保留备份数量失败："+e.message),o.value.keepBackups=p.backup.keepBackups}},R=async t=>{try{await p.updateConfigItem("backup.useZip",t)}catch(e){console.error("更新压缩设置失败:",e),l.error("更新压缩备份设置失败："+e.message),o.value.useZip=!t}},q=async()=>{try{const t=await window.pywebview.api.select_directory(),e=typeof t=="string"?JSON.parse(t):t;e&&e.status==="success"&&e.data&&(o.value.backupDir=e.data,await p.updateConfigItem("backup.backupDir",e.data),l.success("备份源目录设置成功"))}catch(t){console.error("选择备份源目录失败:",t),l.error("选择目录失败："+t.message)}},X=async()=>{try{const t=await window.pywebview.api.select_directory(),e=typeof t=="string"?JSON.parse(t):t;e&&e.status==="success"&&e.data&&(o.value.targetDir=e.data,await p.updateConfigItem("backup.targetDir",e.data),l.success("备份目标目录设置成功"),await k())}catch(t){console.error("选择备份目标目录失败:",t),l.error("选择目录失败："+t.message)}},Q=async()=>{try{p.backup&&(o.value={...p.backup})}catch(t){console.error("加载备份配置失败:",t)}},W=t=>{d&&(clearTimeout(d),d=null),f.value={visible:!0,percent:t.percent||0,message:t.message||"",status:t.status||"normal"},t.status==="success"?t.percent===100&&(_(),l.success("备份完成！"),setTimeout(()=>{f.value.visible=!1,k()},2e3)):(t.status==="error"||t.status==="exception")&&(_(),l.error("备份失败："+t.message),setTimeout(()=>{f.value.visible=!1},3e3))};let b=null;return typeof window<"u"&&(b=window.receiveBackupProgress,window.receiveBackupProgress=t=>{if(W(t),b&&typeof b=="function")try{b(t)}catch(e){console.error("调用原有备份进度回调失败:",e)}}),ne(async()=>{try{await Q(),await k()}catch(t){console.error("组件初始化失败:",t)}}),ie(()=>{d&&(clearTimeout(d),d=null),typeof window<"u"&&(b?window.receiveBackupProgress=b:delete window.receiveBackupProgress)}),(t,e)=>{const r=ue,i=ce,m=de,g=pe,N=fe,C=we,P=_e,Y=ge,ee=ve,U=be,te=Be,V=he,ae=Ce,se=Ve;return h(),S("div",xe,[u("div",Ie,[u("div",ze,[e[8]||(e[8]=u("h2",{class:"section-title"},"本地备份设置",-1)),u("div",Ee,[a(i,{type:"primary",onClick:D},{default:s(()=>[a(r,null,{default:s(()=>[a(v(F))]),_:1}),e[7]||(e[7]=w(" 立即备份 "))]),_:1})])]),u("div",Se,[u("div",Te,[u("div",De,[a(Y,{model:o.value,"label-width":"100px",size:"small"},{default:s(()=>[a(g,{label:"自动备份",class:"mb-2"},{default:s(()=>[a(m,{modelValue:o.value.autoBackup,"onUpdate:modelValue":e[0]||(e[0]=n=>o.value.autoBackup=n),onChange:G},null,8,["modelValue"])]),_:1}),o.value.autoBackup?(h(),S(me,{key:0},[a(g,{label:"备份间隔",class:"mb-2"},{default:s(()=>[u("div",Ne,[a(N,{modelValue:o.value.backupInterval,"onUpdate:modelValue":e[1]||(e[1]=n=>o.value.backupInterval=n),min:1,max:1440,step:1,onChange:j,"controls-position":"right",size:"small"},null,8,["modelValue"]),e[9]||(e[9]=u("span",{class:"ml-2"},"分钟",-1)),a(C,{content:"设置自动备份的时间间隔（1-1440分钟）",placement:"top"},{default:s(()=>[a(r,{class:"ml-1"},{default:s(()=>[a(v(M))]),_:1})]),_:1})])]),_:1}),a(g,{label:"保留数量",class:"mb-2"},{default:s(()=>[u("div",Pe,[a(N,{modelValue:o.value.keepBackups,"onUpdate:modelValue":e[2]||(e[2]=n=>o.value.keepBackups=n),min:1,max:100,step:1,onChange:K,"controls-position":"right",size:"small"},null,8,["modelValue"]),a(C,{content:"设置要保留的最新备份数量",placement:"top"},{default:s(()=>[a(r,{class:"ml-1"},{default:s(()=>[a(v(M))]),_:1})]),_:1})])]),_:1})],64)):T("",!0),a(g,{label:"压缩备份",class:"mb-2"},{default:s(()=>[a(C,{content:"启用后将使用ZIP压缩格式进行备份，节省空间但可能增加处理时间",placement:"top"},{default:s(()=>[a(m,{modelValue:o.value.useZip,"onUpdate:modelValue":e[3]||(e[3]=n=>o.value.useZip=n),onChange:R},null,8,["modelValue"])]),_:1})]),_:1}),a(g,{label:"备份源目录",class:"mb-2"},{default:s(()=>[u("div",Ue,[a(P,{modelValue:o.value.backupDir,"onUpdate:modelValue":e[4]||(e[4]=n=>o.value.backupDir=n),placeholder:"建议用软件的backup目录",size:"small"},null,8,["modelValue"]),a(i,{type:"primary",size:"small",onClick:q},{default:s(()=>[a(r,null,{default:s(()=>[a(v($))]),_:1})]),_:1})])]),_:1}),a(g,{label:"备份目录",class:"mb-2"},{default:s(()=>[u("div",Fe,[a(P,{modelValue:o.value.targetDir,"onUpdate:modelValue":e[5]||(e[5]=n=>o.value.targetDir=n),placeholder:"备份存储目录",size:"small"},null,8,["modelValue"]),a(i,{type:"primary",size:"small",onClick:X},{default:s(()=>[a(r,null,{default:s(()=>[a(v($))]),_:1})]),_:1})])]),_:1})]),_:1},8,["model"])]),f.value.visible?(h(),S("div",Me,[e[10]||(e[10]=u("div",{class:"progress-header"},[u("h4",null,"备份进度")],-1)),a(ee,{percentage:f.value.percent,status:f.value.status==="error"?"exception":"success","stroke-width":8},null,8,["percentage","status"]),u("p",$e,B(f.value.message),1)])):T("",!0),u("div",Je,[u("div",Ze,[e[13]||(e[13]=u("h3",null,"备份历史",-1)),u("div",Le,[a(C,{content:"显示大小会增加加载时间，但提供更详细信息",placement:"top"},{default:s(()=>[a(m,{modelValue:y.value,"onUpdate:modelValue":e[6]||(e[6]=n=>y.value=n),size:"small","active-text":"显示大小","inactive-text":"隐藏大小",style:{"margin-right":"12px"}},null,8,["modelValue"])]),_:1}),a(U,null,{default:s(()=>[a(i,{type:"primary",size:"small",onClick:D},{default:s(()=>[a(r,null,{default:s(()=>[a(v(F))]),_:1}),e[11]||(e[11]=w(" 立即备份 "))]),_:1}),a(i,{size:"small",onClick:L},{default:s(()=>[a(r,null,{default:s(()=>[a(v(ke))]),_:1}),e[12]||(e[12]=w(" 刷新 "))]),_:1})]),_:1})])]),u("div",Oe,[u("div",He,[ye((h(),J(ae,{data:c.value,key:c.value.length,style:{width:"100%"},border:"",size:"small",height:350,stripe:"","empty-text":"暂无备份记录","row-key":"path"},{default:s(()=>[a(V,{label:"备份时间","min-width":"160"},{default:s(({row:n})=>[w(B(z(n.time))+" ",1),a(te,{type:n.type==="auto"?"success":"primary",size:"small",class:"ml-2"},{default:s(()=>[w(B(n.type==="auto"?"自动":"手动"),1)]),_:2},1032,["type"])]),_:1}),y.value?(h(),J(V,{key:0,label:"大小",width:"100"},{default:s(({row:n})=>[w(B(n.size>0?O(n.size):"未计算"),1)]),_:1})):T("",!0),a(V,{label:"路径","min-width":"200","show-overflow-tooltip":""},{default:s(({row:n})=>[w(B(n.path),1)]),_:1}),a(V,{label:"操作",width:"200",align:"center",fixed:"right"},{default:s(({row:n})=>[a(U,null,{default:s(()=>[a(i,{type:"primary",size:"small",onClick:oe=>H(n)},{default:s(()=>e[14]||(e[14]=[w(" 恢复 ")])),_:2},1032,["onClick"]),a(i,{type:"danger",size:"small",onClick:oe=>A(n)},{default:s(()=>e[15]||(e[15]=[w(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[se,!1]])])])])])])])])}}},at=le(Ae,[["__scopeId","data-v-ca9b835c"]]);export{at as default};
