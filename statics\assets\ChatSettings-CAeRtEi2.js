import{_ as x,bh as f,r as C,o as w,b as M,m as I,e as o,d as t,g as l,v as d,B as L,C as S,bE as T,t as U,ei as A,q as N,F as b,b8 as j,ak as R,al as q,bc as D,bd as G,j as O,E as m}from"./entry-BIjVVog3.js";/* empty css                *//* empty css                       *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        */const $={class:"chat-settings"},H={class:"settings-section"},J={class:"section-header"},K={class:"header-actions"},P={class:"panel-content"},Q={class:"settings-card"},W={class:"font-size-slider-container"},X={class:"slider-header"},Y={class:"slider-value"},Z={class:"font-size-slider-container"},ee={class:"slider-header"},te={class:"slider-value"},oe={__name:"ChatSettings",setup(se){const c=f("configStore"),h=f("showLoading"),z=f("hideLoading"),a=C({fontSize:1,fontFamily:"微软雅黑, sans-serif",codeBlockTheme:"auto",codeBlockFontSize:1}),k=async()=>{try{h("正在保存聊天设置..."),await c.updateConfigItem("chat",a.value),m.success("聊天设置保存成功")}catch(s){console.error("保存聊天设置失败:",s),m.error(`保存失败: ${s.message}`)}finally{z()}},y=()=>{a.value={fontSize:1,fontFamily:"微软雅黑, sans-serif",codeBlockTheme:"auto",codeBlockFontSize:1},m.success("聊天设置已重置")},p=s=>typeof s=="number"?s:{small:.875,normal:1,large:1.125}[s]||1,B=async()=>{try{if(c.chat){const s={...c.chat};s.fontSize=p(s.fontSize),s.codeBlockFontSize=p(s.codeBlockFontSize),a.value=s}}catch(s){console.error("加载聊天设置失败:",s)}};return w(async()=>{await B()}),(s,e)=>{const _=L,v=U,g=j,r=N,i=q,F=R,u=G,V=D,E=O;return I(),M("div",$,[o("div",H,[o("div",J,[e[6]||(e[6]=o("h2",{class:"section-title"},"AI聊天界面设置",-1)),o("div",K,[t(v,{type:"primary",onClick:k},{default:l(()=>[t(_,null,{default:l(()=>[t(S(T))]),_:1}),e[4]||(e[4]=d(" 保存设置 "))]),_:1}),t(v,{onClick:y},{default:l(()=>[t(_,null,{default:l(()=>[t(S(A))]),_:1}),e[5]||(e[5]=d(" 重置 "))]),_:1})])]),o("div",P,[o("div",Q,[t(E,{"label-width":"140px"},{default:l(()=>[e[13]||(e[13]=o("h3",{class:"subsection-title"},"界面样式",-1)),t(r,{label:"字体大小"},{default:l(()=>[o("div",W,[o("div",X,[e[7]||(e[7]=o("span",{class:"slider-label"},"缩放比例",-1)),o("span",Y,b(Math.round(a.value.fontSize*100))+"%",1)]),t(g,{modelValue:a.value.fontSize,"onUpdate:modelValue":e[0]||(e[0]=n=>a.value.fontSize=n),min:.5,max:2,step:.05,"show-tooltip":!1,class:"font-size-slider"},null,8,["modelValue"]),e[8]||(e[8]=o("div",{class:"slider-description"},[o("span",{class:"desc-text"},"调整聊天消息的整体字体大小，影响标题、正文、代码等所有文本元素")],-1))])]),_:1}),t(r,{label:"字体"},{default:l(()=>[t(F,{modelValue:a.value.fontFamily,"onUpdate:modelValue":e[1]||(e[1]=n=>a.value.fontFamily=n),style:{width:"300px"}},{default:l(()=>[t(i,{label:"微软雅黑",value:"微软雅黑, sans-serif"}),t(i,{label:"宋体",value:"宋体, serif"}),t(i,{label:"黑体",value:"黑体, sans-serif"}),t(i,{label:"楷体",value:"楷体, serif"}),t(i,{label:"汉仪旗黑",value:"汉仪旗黑, sans-serif"}),t(i,{label:"Arial",value:"Arial, sans-serif"})]),_:1},8,["modelValue"])]),_:1}),e[14]||(e[14]=o("h3",{class:"subsection-title"},"代码块样式",-1)),t(r,{label:"代码块主题"},{default:l(()=>[t(V,{modelValue:a.value.codeBlockTheme,"onUpdate:modelValue":e[2]||(e[2]=n=>a.value.codeBlockTheme=n)},{default:l(()=>[t(u,{value:"auto"},{default:l(()=>e[9]||(e[9]=[d("跟随系统")])),_:1}),t(u,{value:"light"},{default:l(()=>e[10]||(e[10]=[d("亮色主题")])),_:1}),t(u,{value:"dark"},{default:l(()=>e[11]||(e[11]=[d("暗色主题")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(r,{label:"代码块字体大小"},{default:l(()=>[o("div",Z,[o("div",ee,[e[12]||(e[12]=o("span",{class:"slider-label"},"代码字体缩放",-1)),o("span",te,b(Math.round(a.value.codeBlockFontSize*100))+"%",1)]),t(g,{modelValue:a.value.codeBlockFontSize,"onUpdate:modelValue":e[3]||(e[3]=n=>a.value.codeBlockFontSize=n),min:.5,max:2,step:.05,"show-tooltip":!1,class:"font-size-slider"},null,8,["modelValue"])])]),_:1})]),_:1})])])])])}}},me=x(oe,[["__scopeId","data-v-522928b4"]]);export{me as default};
