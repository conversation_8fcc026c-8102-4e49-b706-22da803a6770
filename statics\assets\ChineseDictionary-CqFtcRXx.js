import{_ as ce,r as f,c as H,w as ve,o as _e,b as c,m as r,e as l,d as a,v as h,g as d,C as p,bK as ye,B as pe,dL as fe,$ as K,p as k,F as y,aI as M,bc as he,L as we,aC as q,t as ge,x as xe,s as be,X as C,dY as O,ah as G,ar as me,dZ as ke,Y,d_ as Ce,dM as Ve,d$ as Ee,b8 as Le,aP as $e,n as J,dP as Q,k as De,aV as Z,E as w,aS as ee}from"./entry-BIjVVog3.js";/* empty css                  *//* empty css                 *//* empty css                        *//* empty css                          *//* empty css                         *//* empty css                       *//* empty css                        */const Me={class:"chinese-dictionary"},Te={class:"app-container"},Ae={class:"page-header"},Re={class:"header-content"},Se={class:"title"},Be={class:"dictionary-type-selector-wrapper"},Ie={class:"dictionary-type-selector"},We={key:0,class:"loading-container"},je={class:"loading-text"},Ne={key:1,class:"main-content"},Pe={class:"settings-panel"},Ue={class:"glass-card"},ze={class:"card-header"},Fe={class:"settings search-section"},He={class:"search-box"},Ke={class:"search-options"},Oe={class:"card-header"},Ge={class:"dictionary-info"},Ye={class:"count-badge"},Ze={class:"settings"},Xe={class:"setting-item"},qe={class:"setting-label"},Je={class:"setting-control"},Qe={class:"setting-item"},el={class:"setting-label"},ll={class:"setting-control"},tl={class:"actions"},al={class:"button-group"},ol={class:"results-panel"},sl={key:0,class:"loading-container specific-loader"},nl={key:1,class:"loading-container specific-loader"},il={key:2,class:"glass-card result-card"},dl={class:"card-header"},ul={class:"result-count"},rl=["onClick"],cl={class:"word-content"},vl={class:"word-text riddle-text"},_l={class:"word-pinyin xiehouyu-answer"},yl={class:"word-text"},pl=["innerHTML"],fl={class:"word-footer"},hl={class:"word-category"},wl={key:3,class:"glass-card empty-tip"},gl={key:4,class:"glass-card empty-tip"},xl={key:5,class:"glass-card empty-tip"},bl={key:6,class:"glass-card empty-tip"},ml={key:7,class:"glass-card empty-tip"},kl={key:0,class:"word-detail glass-effect"},Cl={class:"detail-header"},Vl={class:"word-title"},El={class:"detail-category"},Ll={class:"detail-content"},$l={class:"section"},Dl={class:"section-content"},Ml={class:"detail-header"},Tl={class:"word-title"},Al=["innerHTML"],Rl={class:"detail-category"},Sl={class:"detail-content"},Bl={class:"section"},Il={class:"section-content"},Wl={key:1},jl={key:0,class:"section"},Nl={class:"section-content"},Pl={__name:"ChineseDictionary",setup(Ul){const V=f([]),L=f([]),g=f(!1),x=f(!1),v=f(!1),j=f(!0),T=f(!1),o=f("xiehouyu"),A=H(()=>o.value==="word_entry"?V.value:o.value==="xiehouyu"?L.value:[]),R=f([2,3,4]),N=f(10),$=f(""),m=f("word"),S=f(!1),B=f(!1),b=f([]),P=f(!1),_=f(null),E=H(()=>A.value.length);H(()=>{const i=new Set;return A.value.forEach(e=>{e.category&&i.add(e.category)}),Array.from(i).map(e=>({value:e,label:e})).sort((e,t)=>e.label.localeCompare(t.label,"zh-CN"))});const U=i=>i?i.category||(i.type==="xiehouyu"?"歇后语":i.type==="word_entry"?"词汇":"未分类"):"未分类",z=i=>i?/[^a-zA-Zāáǎàēéěèīíǐìōóǒòūúǔùǖǘǚǜü\s]/.test(i)?`【${i}】`:`【${i}】`:"",I=async i=>{if(i==="word_entry"&&!g.value){v.value=!0;try{const e=await ee(()=>import("./word-JUwTMPif.js"),[],import.meta.url);let t=e.default||e;Array.isArray(t)?V.value=t.map((n,s)=>({type:"word_entry",id:`word-${s}`,word:n.word,pinyin:n.pinyin||"",definition:n.explanation,category:"词汇",abbr:n.abbr})).filter(n=>n.word&&n.word.trim().length>0):typeof t=="object"&&t!==null?V.value=Object.values(t).map((n,s)=>({type:"word_entry",id:`word-${s}`,word:n.word,pinyin:n.pinyin||"",definition:n.explanation,category:"词汇",abbr:n.abbr})).filter(n=>n.word&&n.word.trim().length>0):console.warn("Word.json 数据格式不符合预期或为空"),g.value=!0,Z({title:"成功",message:`现代词汇数据加载完成 (${V.value.length} 条)。`,type:"success"})}catch(e){console.error("Failed to load word.json on demand",e),w.error("现代词汇数据加载失败。")}finally{v.value=!1}}else if(i==="xiehouyu"&&!x.value){v.value=!0;try{const e=await ee(()=>import("./xiehouyu-CzgqJG6Z.js"),[],import.meta.url);let t=e.default||e;Array.isArray(t)?L.value=t.map((n,s)=>({type:"xiehouyu",id:`xh-${s}`,word:n.riddle,definition:n.answer,pinyin:"",category:"歇后语",riddle:n.riddle,answer:n.answer})).filter(n=>n.riddle&&n.riddle.trim().length>0):console.warn("歇后语数据格式不符合预期或为空"),x.value=!0,Z({title:"成功",message:`歇后语数据加载完成 (${L.value.length} 条)。`,type:"success"})}catch(e){console.error("Failed to load xiehouyu.json on demand",e),w.error("歇后语数据加载失败。")}finally{v.value=!1}}},le=async()=>{j.value=!0,await I("xiehouyu"),x.value&&L.value.length>0?(o.value="xiehouyu",m.value="riddle"):(o.value="word_entry",await I("word_entry"),g.value&&V.value.length>0?m.value="word":(o.value="xiehouyu",m.value="riddle",Z({title:"提示",message:"所有词典数据均未能加载，请检查数据文件或网络连接。",type:"warning"}))),j.value=!1};ve(o,async(i,e)=>{b.value=[],$.value="",B.value=!1,i==="word_entry"?(m.value="word",g.value||await I("word_entry")):i==="xiehouyu"&&(m.value="riddle",x.value||await I("xiehouyu"))});const te=()=>{const i=A.value;if(i.length===0){w.warning(`当前${o.value==="word_entry"?"词汇":"歇后语"}词典数据为空，无法抽取`);return}T.value=!0,b.value=[];try{let e=i.filter(s=>{let W=!0;const D=s.type==="xiehouyu"?s.riddle:s.word;return R.value.length>0&&(W=R.value.includes(D?.length||0)),W});if(e.length===0){w.warning("没有符合条件的条目，请调整筛选条件"),T.value=!1;return}const t=Math.min(N.value,e.length),n=[];for(;n.length<t&&e.length>0;){const s=Math.floor(Math.random()*e.length);n.push(e[s]),e.splice(s,1)}b.value=n,B.value=!1,w.success(`成功抽取 ${n.length} 个条目`)}catch(e){console.error("随机抽取失败:",e),w.error("抽取失败，请重试")}finally{T.value=!1}},X=()=>{const i=A.value;if(v.value){w.warning("词典数据正在加载中，请稍候...");return}if(i.length===0){w.warning(`当前${o.value==="word_entry"?"词汇":"歇后语"}词典数据为空，无法搜索`);return}if(!$.value.trim()){w.warning("请输入搜索关键词");return}S.value=!0,b.value=[];try{const e=$.value.trim().toLowerCase();let t=[];if(o.value==="word_entry"?m.value==="word"?t=i.filter(s=>s.word&&s.word.toLowerCase().includes(e)):m.value==="pinyin"?t=i.filter(s=>s.pinyin&&s.pinyin.toLowerCase().includes(e)):m.value==="definition"?t=i.filter(s=>s.definition&&s.definition.toLowerCase().includes(e)):t=i.filter(s=>s.word&&s.word.toLowerCase().includes(e)||s.definition&&s.definition.toLowerCase().includes(e)):o.value==="xiehouyu"&&(m.value==="riddle"||m.value==="word"?t=i.filter(s=>s.riddle&&s.riddle.toLowerCase().includes(e)):m.value==="answer"||m.value==="definition"?t=i.filter(s=>s.answer&&s.answer.toLowerCase().includes(e)):t=i.filter(s=>s.riddle&&s.riddle.toLowerCase().includes(e)||s.answer&&s.answer.toLowerCase().includes(e))),t.length===0){let s=o.value==="word_entry"?"词汇":"歇后语";w.warning(`在${s}中未找到相关条目`),S.value=!1;return}const n=100;t.length>n?(b.value=t.slice(0,n),w.info(`共找到 ${t.length} 个结果，仅显示前 ${n} 个`)):(b.value=t,w.success(`找到 ${t.length} 个结果`)),B.value=!0}catch(e){console.error("搜索词典失败:",e),w.error("搜索失败，请重试")}finally{S.value=!1}},ae=()=>{if(b.value.length===0){w.warning("没有可复制的内容");return}const i=b.value.map(e=>{let t="";return e.type==="word_entry"?(t=e.word||"",e.pinyin&&(t+=` ${z(e.pinyin)}`),e.definition&&(t+=`：${e.definition}`),e.abbr&&(t+=` (缩写: ${e.abbr})`)):e.type==="xiehouyu"&&(t=`歇后语：${e.riddle} —— ${e.answer}`),t}).join(`
`);window.pywebview.api.copy_to_clipboard(i).then(()=>{w.success("已复制到剪贴板")}).catch(e=>{console.error("复制失败:",e);try{const t=document.createElement("textarea");t.value=i,document.body.appendChild(t),t.focus(),t.select(),document.execCommand("copy"),document.body.removeChild(t),w.success("已复制到剪贴板 (备用方法)")}catch(t){console.error("备用复制方法也失败:",t),w.error("复制失败，请手动复制")}})},oe=i=>{_.value=i,P.value=!0};return _e(()=>{le()}),(i,e)=>{const t=pe,n=fe,s=he,W=we,D=ge,se=be,ne=me,ie=Ce,de=Ve,ue=Le,re=De;return r(),c("div",Me,[e[32]||(e[32]=l("div",{class:"bg-decoration"},[l("div",{class:"bg-gradient"}),l("div",{class:"bg-circles"})],-1)),l("div",Te,[l("div",Ae,[l("div",Re,[l("h2",Se,[a(t,null,{default:d(()=>[a(p(ye))]),_:1}),e[6]||(e[6]=h(" 灵感词典 "))]),e[7]||(e[7]=l("p",{class:"subtitle"},"在词汇与歇后语的海洋中遨游，激发无限创作灵感",-1))]),l("div",Be,[l("div",Ie,[a(s,{modelValue:o.value,"onUpdate:modelValue":e[0]||(e[0]=u=>o.value=u),size:"large",disabled:v.value},{default:d(()=>[a(n,{value:"word_entry"},{default:d(()=>[h(" 现代词汇 ("+y(g.value?V.value.length:"未加载")+") ",1),o.value==="word_entry"&&v.value?(r(),K(t,{key:0,class:"el-icon--right is-loading"},{default:d(()=>[a(p(M))]),_:1})):k("",!0)]),_:1}),a(n,{value:"xiehouyu"},{default:d(()=>[h(" 歇后语 ("+y(x.value?L.value.length:"未加载")+") ",1),o.value==="xiehouyu"&&v.value?(r(),K(t,{key:0,class:"el-icon--right is-loading"},{default:d(()=>[a(p(M))]),_:1})):k("",!0)]),_:1})]),_:1},8,["modelValue","disabled"])])])]),j.value?(r(),c("div",We,[a(W,{rows:10,animated:""}),l("div",je,[a(t,{class:"loading-icon"},{default:d(()=>[a(p(M))]),_:1}),e[8]||(e[8]=h(" 正在加载词典数据，请稍候... "))])])):(r(),c("div",Ne,[l("div",Pe,[l("div",Ue,[l("div",ze,[l("h3",null,[a(t,null,{default:d(()=>[a(p(q))]),_:1}),h(" "+y(o.value==="xiehouyu"?"歇后语搜索":"词汇搜索"),1)])]),l("div",Fe,[l("div",He,[a(se,{modelValue:$.value,"onUpdate:modelValue":e[1]||(e[1]=u=>$.value=u),placeholder:o.value==="xiehouyu"?"搜索谜面或谜底":"搜索词语、拼音或释义",clearable:"","prefix-icon":p(q),onKeyup:xe(X,["enter"]),disabled:v.value||o.value==="word_entry"&&!g.value||o.value==="xiehouyu"&&!x.value},{append:d(()=>[a(D,{onClick:X,loading:S.value,disabled:v.value||o.value==="word_entry"&&!g.value||o.value==="xiehouyu"&&!x.value},{default:d(()=>e[9]||(e[9]=[h(" 搜索 ")])),_:1},8,["loading","disabled"])]),_:1},8,["modelValue","placeholder","prefix-icon","disabled"])]),l("div",Ke,[a(s,{modelValue:m.value,"onUpdate:modelValue":e[2]||(e[2]=u=>m.value=u),size:"small",disabled:v.value||o.value==="word_entry"&&!g.value||o.value==="xiehouyu"&&!x.value},{default:d(()=>[o.value==="word_entry"?(r(),c(C,{key:0},[a(n,{value:"word"},{default:d(()=>e[10]||(e[10]=[h("词语")])),_:1}),a(n,{value:"pinyin"},{default:d(()=>e[11]||(e[11]=[h("拼音")])),_:1}),a(n,{value:"definition"},{default:d(()=>e[12]||(e[12]=[h("释义")])),_:1})],64)):o.value==="xiehouyu"?(r(),c(C,{key:1},[a(n,{value:"riddle"},{default:d(()=>e[13]||(e[13]=[h("谜面")])),_:1}),a(n,{value:"answer"},{default:d(()=>e[14]||(e[14]=[h("谜底")])),_:1})],64)):k("",!0)]),_:1},8,["modelValue","disabled"])])]),e[19]||(e[19]=l("div",{class:"separator"},[l("div",{class:"separator-line"}),l("div",{class:"separator-text"},"或者"),l("div",{class:"separator-line"})],-1)),l("div",Oe,[l("h3",null,[a(t,null,{default:d(()=>[a(p(O))]),_:1}),e[15]||(e[15]=h(" 随机抽取 "))]),l("div",Ge,[a(ne,{content:`当前词典 (${o.value==="word_entry"?"词汇":"歇后语"}) 总条目数`},{default:d(()=>[l("div",Ye,[a(t,null,{default:d(()=>[a(p(G))]),_:1}),l("span",null,y(o.value==="word_entry"&&g.value||o.value==="xiehouyu"&&x.value?E.value:"-")+" 条",1)])]),_:1},8,["content"])])]),l("div",Ze,[l("div",Xe,[l("div",qe,[a(t,null,{default:d(()=>[a(p(ke))]),_:1}),l("span",null,y(o.value==="xiehouyu"?"谜面长度":"词语长度")+"：",1)]),l("div",Je,[a(de,{modelValue:R.value,"onUpdate:modelValue":e[3]||(e[3]=u=>R.value=u),disabled:v.value||o.value==="word_entry"&&!g.value||o.value==="xiehouyu"&&!x.value},{default:d(()=>[(r(!0),c(C,null,Y(o.value==="xiehouyu"?[3,4,5,6,7,8,9,10]:[1,2,3,4,5,6],u=>(r(),K(ie,{key:u,value:u},{default:d(()=>[h(y(u)+"字 ",1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue","disabled"])])]),l("div",Qe,[l("div",el,[a(t,null,{default:d(()=>[a(p(Ee))]),_:1}),e[16]||(e[16]=l("span",null,"抽取数量：",-1))]),l("div",ll,[a(ue,{modelValue:N.value,"onUpdate:modelValue":e[4]||(e[4]=u=>N.value=u),min:1,max:50,step:1,"show-stops":"","show-input":"",marks:{1:"1",10:"10",25:"25",50:"50"},disabled:v.value||o.value==="word_entry"&&!g.value||o.value==="xiehouyu"&&!x.value},null,8,["modelValue","disabled"])])]),l("div",tl,[l("div",al,[a(D,{type:"primary",class:"extract-button",icon:p(O),onClick:te,disabled:v.value||T.value||E.value===0||o.value==="word_entry"&&!g.value||o.value==="xiehouyu"&&!x.value},{default:d(()=>e[17]||(e[17]=[h(" 随机抽取 ")])),_:1},8,["icon","disabled"]),a(D,{type:"success",class:"copy-button",icon:p($e),onClick:ae,disabled:!b.value.length},{default:d(()=>e[18]||(e[18]=[h(" 复制结果 ")])),_:1},8,["icon","disabled"])])])])])]),l("div",ol,[v.value&&o.value==="word_entry"&&!g.value?(r(),c("div",sl,[a(t,{class:"loading-icon"},{default:d(()=>[a(p(M))]),_:1}),e[20]||(e[20]=h()),e[21]||(e[21]=l("span",null,"正在加载现代词汇数据...",-1))])):v.value&&o.value==="xiehouyu"&&!x.value?(r(),c("div",nl,[a(t,{class:"loading-icon"},{default:d(()=>[a(p(M))]),_:1}),e[22]||(e[22]=h()),e[23]||(e[23]=l("span",null,"正在加载歇后语数据...",-1))])):b.value.length>0?(r(),c("div",il,[l("div",dl,[l("h3",null,y(B.value?"搜索结果":"抽取结果"),1),l("div",ul,y(b.value.length)+" 条",1)]),l("div",{class:J(["result-grid",{"large-grid":b.value.length<=15,"medium-grid":b.value.length>15&&b.value.length<=24,"small-grid":b.value.length>24}])},[(r(!0),c(C,null,Y(b.value,(u,F)=>(r(),c("div",{key:u.id||F,class:"word-card",onClick:zl=>oe(u)},[l("div",cl,[u.type==="xiehouyu"?(r(),c(C,{key:0},[l("div",vl,y(u.riddle),1),l("div",_l,y(u.answer),1)],64)):u.type==="word_entry"?(r(),c(C,{key:1},[l("div",yl,y(u.word),1),u.pinyin?(r(),c("div",{key:0,class:"word-pinyin",innerHTML:z(u.pinyin)},null,8,pl)):k("",!0)],64)):k("",!0)]),l("div",fl,[l("span",hl,y(U(u)),1)])],8,rl))),128))],2)])):!v.value&&(o.value==="word_entry"&&g.value&&E.value>0||o.value==="xiehouyu"&&x.value&&E.value>0)?(r(),c("div",wl,[a(t,null,{default:d(()=>[a(p(O))]),_:1}),e[24]||(e[24]=l("p",null,"请在上方操作面板中搜索或随机抽取条目",-1))])):!v.value&&o.value==="word_entry"&&g.value&&E.value===0?(r(),c("div",gl,[a(t,null,{default:d(()=>[a(p(G))]),_:1}),e[25]||(e[25]=l("p",null,"现代词汇数据为空，但已加载。",-1))])):!v.value&&o.value==="xiehouyu"&&x.value&&E.value===0?(r(),c("div",xl,[a(t,null,{default:d(()=>[a(p(G))]),_:1}),e[26]||(e[26]=l("p",null,"歇后语数据为空，但已加载。",-1))])):!v.value&&o.value==="word_entry"&&!g.value?(r(),c("div",bl,[a(t,null,{default:d(()=>[a(p(Q))]),_:1}),e[27]||(e[27]=l("p",null,"现代词汇数据加载失败或尚未加载。请尝试切换词典类型或刷新页面。",-1))])):!v.value&&o.value==="xiehouyu"&&!x.value?(r(),c("div",ml,[a(t,null,{default:d(()=>[a(p(Q))]),_:1}),e[28]||(e[28]=l("p",null,"歇后语数据加载失败或尚未加载。请尝试切换词典类型或刷新页面。",-1))])):k("",!0)])]))]),a(re,{modelValue:P.value,"onUpdate:modelValue":e[5]||(e[5]=u=>P.value=u),title:_.value?.type==="xiehouyu"?"歇后语详情":_.value?.word||"词条详情",width:"600px","close-on-click-modal":!0,"modal-class":"detail-dialog-bg","show-close":!0,"destroy-on-close":"",top:"8vh"},{default:d(()=>[_.value?(r(),c("div",kl,[_.value.type==="xiehouyu"?(r(),c(C,{key:0},[l("div",Cl,[l("div",Vl,[l("h3",null,y(_.value.riddle),1)]),l("div",El,y(U(_.value)),1)]),l("div",Ll,[l("div",$l,[e[29]||(e[29]=l("div",{class:"section-title"},"谜底",-1)),l("div",Dl,[l("p",{class:J({"dialog-xiehouyu-answer":_.value.type==="xiehouyu"})},y(_.value.answer),3)])])])],64)):(r(),c(C,{key:1},[l("div",Ml,[l("div",Tl,[l("h3",null,y(_.value.word),1),_.value.pinyin?(r(),c("div",{key:0,class:"word-pinyin",innerHTML:z(_.value.pinyin)},null,8,Al)):k("",!0)]),l("div",Rl,y(U(_.value)),1)]),l("div",Sl,[l("div",Bl,[e[30]||(e[30]=l("div",{class:"section-title"},"释义",-1)),l("div",Il,[_.value.definition&&typeof _.value.definition=="string"&&_.value.definition.includes(`
`)?(r(!0),c(C,{key:0},Y(_.value.definition.split(`
`).filter(u=>u.trim()),(u,F)=>(r(),c("p",{key:F,class:"definition-item"},y(u),1))),128)):(r(),c("p",Wl,y(_.value.definition||"暂无释义"),1))])]),_.value.type==="word_entry"&&_.value.abbr?(r(),c("div",jl,[e[31]||(e[31]=l("div",{class:"section-title"},"缩写",-1)),l("div",Nl,[l("p",null,y(_.value.abbr),1)])])):k("",!0)])],64))])):k("",!0)]),_:1},8,["modelValue","title"])])}}},ql=ce(Pl,[["__scopeId","data-v-fd090c25"]]);export{ql as default};
