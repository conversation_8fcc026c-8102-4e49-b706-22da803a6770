import{_ as Q,bh as D,c as W,r as w,o as X,U as Y,b as Z,m as N,e as u,d as a,g as s,q as ee,s as te,C as f,a_ as ae,t as se,v as c,B as oe,ee as y,aC as le,j as re,ac as ne,ad as ie,J as ue,F as $,an as de,af as ce,$ as pe,p as me,am as fe,k as he,E as n,av as _e}from"./entry-BIjVVog3.js";/* empty css                 *//* empty css                        *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css               *//* empty css                */const ve={class:"chrome-settings"},ge={class:"settings-panel"},we={class:"panel-content"},ye={class:"settings-card"},be={class:"path-input-group"},Ce={class:"path-input-group"},De={class:"section-header"},Ve={class:"header-actions"},xe={class:"settings-card table-container"},Ee={class:"path-input-group"},ke={class:"path-input-group"},Se={class:"dialog-footer"},Ue={__name:"ChromeSettings",setup(Ie){const i=D("configStore"),V=D("showLoading"),x=D("hideLoading"),m=W(()=>i.chrome?.userDataDirs||[]),d=w({}),b=w(null),l=w({visible:!1,isEdit:!1,form:{id:"",name:"",path:"",port:9222,isDefault:!1,enableExtensions:!1,extensionsPath:""}}),E=w(),P={name:[{required:!0,message:"请输入名称",trigger:"blur"}],path:[{required:!0,message:"请选择路径",trigger:"blur"}],port:[{required:!0,message:"请输入端口",trigger:"blur"},{type:"number",min:1024,max:65535,message:"端口范围1024-65535",trigger:"blur"}]},B=async()=>{try{const t=await window.pywebview.api.select_file_path(),e=typeof t=="string"?JSON.parse(t):t;e&&e.status==="success"&&e.data.length>0&&(i.chrome.default_path=e.data[0],await i.updateConfigItem("chrome.default_path",e.data[0]),n.success("Chrome路径设置成功"))}catch(t){console.error("选择Chrome路径失败:",t),n.error("选择Chrome路径失败: "+t.message)}},J=async()=>{try{V("正在检测Chrome路径...");const t=await window.pywebview.api.drssion_controller.detect_chrome_path(),e=typeof t=="string"?JSON.parse(t):t;e.status==="success"?(i.chrome.default_path=e.data.path,await i.updateConfigItem("chrome.default_path",e.data.path),n.success("Chrome路径检测成功")):n.warning("未检测到Chrome安装路径")}catch(t){console.error("检测Chrome路径失败:",t),n.error("检测Chrome路径失败："+t.message)}finally{x()}},O=async()=>{try{const t=await window.pywebview.api.select_directory(),e=typeof t=="string"?JSON.parse(t):t;e&&e.status==="success"&&e.data&&(i.chrome.downloadDir=e.data,await i.updateConfigItem("chrome.downloadDir",e.data),n.success("下载目录设置成功"))}catch(t){console.error("选择下载目录失败:",t),n.error("选择下载目录失败: "+t.message)}},T=async t=>{try{const e=await window.pywebview.api.drssion_controller.check_chrome_status({path:`${t.path}/${t.name}`,port:t.port}),r=typeof e=="string"?JSON.parse(e):e;r.status==="success"&&(d.value[t.id]=r.data.running)}catch(e){console.error("检查Chrome状态失败:",e),d.value[t.id]=!1}},k=async()=>{for(const t of m.value)await T(t)},S=async t=>{try{if(V(d.value[t.id]?"正在停止Chrome...":"正在启动Chrome..."),d.value[t.id]){const e=await window.pywebview.api.drssion_controller.stop_chrome_profile({path:`${t.path}/${t.name}`,port:t.port}),r=typeof e=="string"?JSON.parse(e):e;if(r.status==="success")d.value[t.id]=!1,n.success("Chrome实例已停止");else throw new Error(r.message||"停止失败")}else{const e=await window.pywebview.api.drssion_controller.start_chrome_with_profile({path:`${t.path}/${t.name}`,port:t.port,browser_path:i.chrome.default_path,extensions_enabled:t.enableExtensions,extensions_path:t.extensionsPath}),r=typeof e=="string"?JSON.parse(e):e;if(r.status==="success")d.value[t.id]=!0,n.success("Chrome实例已启动");else throw new Error(r.message||"启动失败")}}catch(e){console.error("切换Chrome状态失败:",e),n.error(e.message||(d.value[t.id]?"停止失败":"启动失败"))}finally{x()}},F=async(t,e)=>{try{e&&m.value.forEach(r=>{r.id!==t.id&&(r.isDefault=!1)}),await i.updateConfigItem("chrome.userDataDirs",i.chrome.userDataDirs),n.success("默认设置已更新")}catch(r){console.error("更新默认设置失败:",r),n.error("更新失败"),t.isDefault=!e}},q=()=>{l.value={visible:!0,isEdit:!1,form:{id:"",name:"",path:"",port:9222,isDefault:!1,enableExtensions:!1,extensionsPath:""}}},L=t=>{l.value={visible:!0,isEdit:!0,form:{...t}}},z=async t=>{try{await _e.confirm(`确定要删除用户数据目录 "${t.name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),d.value[t.id]&&await S(t);const e=m.value.findIndex(r=>r.id===t.id);e>-1&&(m.value.splice(e,1),await i.updateConfigItem("chrome.userDataDirs",m.value),n.success("删除成功"))}catch(e){e!=="cancel"&&(console.error("删除用户数据目录失败:",e),n.error("删除失败"))}},M=async()=>{try{await E.value.validate();const t=l.value.form;if(l.value.isEdit){const e=m.value.findIndex(r=>r.id===t.id);e>-1&&(m.value[e]={...t})}else t.id=Date.now().toString(),m.value.push({...t});t.isDefault&&m.value.forEach(e=>{e.id!==t.id&&(e.isDefault=!1)}),await i.updateConfigItem("chrome.userDataDirs",i.chrome.userDataDirs),l.value.visible=!1,n.success(l.value.isEdit?"编辑成功":"添加成功")}catch(t){console.error("保存用户数据目录失败:",t),n.error("保存失败")}},R=async()=>{try{const t=await window.pywebview.api.select_directory(),e=typeof t=="string"?JSON.parse(t):t;e&&e.status==="success"&&e.data&&(l.value.form.path=e.data)}catch(t){console.error("选择路径失败:",t),n.error("选择路径失败: "+t.message)}},j=async()=>{try{const t=await window.pywebview.api.select_directory(),e=typeof t=="string"?JSON.parse(t):t;e&&e.status==="success"&&e.data&&(l.value.form.extensionsPath=e.data)}catch(t){console.error("选择扩展路径失败:",t),n.error("选择扩展路径失败: "+t.message)}};return X(()=>{k(),b.value=setInterval(k,5e3)}),Y(()=>{b.value&&clearInterval(b.value)}),(t,e)=>{const r=te,_=oe,p=se,U=ae,h=ee,I=re,v=ie,A=ue,C=de,G=ce,H=fe,K=he;return N(),Z("div",ve,[u("div",ge,[e[17]||(e[17]=u("div",{class:"section-header"},[u("h2",{class:"section-title"},"Chrome 设置")],-1)),u("div",we,[u("div",ye,[a(I,{"label-width":"120px"},{default:s(()=>[a(h,{label:"Chrome路径"},{default:s(()=>[u("div",be,[a(r,{modelValue:f(i).chrome.default_path,"onUpdate:modelValue":e[0]||(e[0]=o=>f(i).chrome.default_path=o),placeholder:"请选择 Chrome 浏览器路径"},null,8,["modelValue"]),a(U,null,{default:s(()=>[a(p,{type:"primary",onClick:B},{default:s(()=>[a(_,null,{default:s(()=>[a(f(y))]),_:1}),e[10]||(e[10]=c(" 选择路径 "))]),_:1}),a(p,{type:"success",onClick:J},{default:s(()=>[a(_,null,{default:s(()=>[a(f(le))]),_:1}),e[11]||(e[11]=c(" 自动检测 "))]),_:1})]),_:1})])]),_:1}),a(h,{label:"下载目录"},{default:s(()=>[u("div",Ce,[a(r,{modelValue:f(i).chrome.downloadDir,"onUpdate:modelValue":e[1]||(e[1]=o=>f(i).chrome.downloadDir=o),placeholder:"请选择下载目录"},null,8,["modelValue"]),a(p,{type:"primary",onClick:O},{default:s(()=>[a(_,null,{default:s(()=>[a(f(y))]),_:1}),e[12]||(e[12]=c(" 选择路径 "))]),_:1})])]),_:1})]),_:1})]),u("div",De,[e[14]||(e[14]=u("h2",{class:"section-title"},"用户数据目录",-1)),u("div",Ve,[a(p,{type:"primary",onClick:q},{default:s(()=>[a(_,null,{default:s(()=>[a(f(ne))]),_:1}),e[13]||(e[13]=c(" 添加目录 "))]),_:1})])]),u("div",xe,[a(G,{data:m.value,style:{width:"100%"},border:""},{default:s(()=>[a(v,{prop:"name",label:"名称","min-width":"120"}),a(v,{prop:"path",label:"路径","min-width":"200","show-overflow-tooltip":""}),a(v,{prop:"port",label:"端口",width:"100",align:"center"}),a(v,{label:"状态",width:"100",align:"center"},{default:s(({row:o})=>[a(A,{type:d.value[o.id]?"success":"info"},{default:s(()=>[c($(d.value[o.id]?"运行中":"已停止"),1)]),_:2},1032,["type"])]),_:1}),a(v,{label:"默认",width:"80",align:"center"},{default:s(({row:o})=>[a(C,{modelValue:o.isDefault,"onUpdate:modelValue":g=>o.isDefault=g,onChange:g=>F(o,g)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a(v,{label:"操作",width:"280",align:"center",fixed:"right"},{default:s(({row:o})=>[a(U,null,{default:s(()=>[a(p,{type:d.value[o.id]?"danger":"success",size:"small",onClick:g=>S(o)},{default:s(()=>[c($(d.value[o.id]?"停止":"启动"),1)]),_:2},1032,["type","onClick"]),a(p,{type:"primary",size:"small",onClick:g=>L(o)},{default:s(()=>e[15]||(e[15]=[c(" 编辑 ")])),_:2},1032,["onClick"]),a(p,{type:"danger",size:"small",onClick:g=>z(o)},{default:s(()=>e[16]||(e[16]=[c(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])])])]),a(K,{modelValue:l.value.visible,"onUpdate:modelValue":e[9]||(e[9]=o=>l.value.visible=o),title:l.value.isEdit?"编辑用户数据目录":"添加用户数据目录",width:"600px"},{footer:s(()=>[u("span",Se,[a(p,{onClick:e[8]||(e[8]=o=>l.value.visible=!1)},{default:s(()=>e[20]||(e[20]=[c("取消")])),_:1}),a(p,{type:"primary",onClick:M},{default:s(()=>e[21]||(e[21]=[c("确定")])),_:1})])]),default:s(()=>[a(I,{ref_key:"userDataDirFormRef",ref:E,model:l.value.form,rules:P,"label-width":"120px"},{default:s(()=>[a(h,{label:"名称",prop:"name"},{default:s(()=>[a(r,{modelValue:l.value.form.name,"onUpdate:modelValue":e[2]||(e[2]=o=>l.value.form.name=o),placeholder:"请输入名称"},null,8,["modelValue"])]),_:1}),a(h,{label:"路径",prop:"path"},{default:s(()=>[u("div",Ee,[a(r,{modelValue:l.value.form.path,"onUpdate:modelValue":e[3]||(e[3]=o=>l.value.form.path=o),placeholder:"请选择路径"},null,8,["modelValue"]),a(p,{type:"primary",onClick:R},{default:s(()=>[a(_,null,{default:s(()=>[a(f(y))]),_:1}),e[18]||(e[18]=c(" 选择路径 "))]),_:1})])]),_:1}),a(h,{label:"端口",prop:"port"},{default:s(()=>[a(H,{modelValue:l.value.form.port,"onUpdate:modelValue":e[4]||(e[4]=o=>l.value.form.port=o),min:1024,max:65535},null,8,["modelValue"])]),_:1}),a(h,{label:"设为默认"},{default:s(()=>[a(C,{modelValue:l.value.form.isDefault,"onUpdate:modelValue":e[5]||(e[5]=o=>l.value.form.isDefault=o)},null,8,["modelValue"])]),_:1}),a(h,{label:"启用扩展"},{default:s(()=>[a(C,{modelValue:l.value.form.enableExtensions,"onUpdate:modelValue":e[6]||(e[6]=o=>l.value.form.enableExtensions=o)},null,8,["modelValue"])]),_:1}),l.value.form.enableExtensions?(N(),pe(h,{key:0,label:"扩展路径",prop:"extensionsPath"},{default:s(()=>[u("div",ke,[a(r,{modelValue:l.value.form.extensionsPath,"onUpdate:modelValue":e[7]||(e[7]=o=>l.value.form.extensionsPath=o),placeholder:"请选择扩展路径"},null,8,["modelValue"]),a(p,{type:"primary",onClick:j},{default:s(()=>[a(_,null,{default:s(()=>[a(f(y))]),_:1}),e[19]||(e[19]=c(" 选择路径 "))]),_:1})])]),_:1})):me("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},qe=Q(Ue,[["__scopeId","data-v-b6b7e6ae"]]);export{qe as default};
