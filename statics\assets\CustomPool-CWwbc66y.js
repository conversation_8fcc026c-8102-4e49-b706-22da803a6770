import{aW as Cl,r as f,bk as kt,c as re,_ as xt,a as $l,w as Le,o as tt,E as C,R as He,U as Ct,b as y,m as u,e as s,F as $,X as Q,Y as ee,n as Ee,Z as Te,V as je,dv as yt,dy as Vl,d as a,g as r,C as b,t as Ge,B as Ye,b6 as Sl,v as x,aN as Pl,a_ as El,ah as lt,aG as $t,bB as Ll,ac as ze,e0 as ht,aP as bt,a7 as Pe,bF as Tl,$ as L,p as G,dY as Ml,ar as Dl,al as Ul,ak as Nl,bC as Ze,a5 as _t,h as Ol,y as Fl,aD as Bl,G as Re,dQ as Al,dR as Il,bJ as zl,aB as Hl,aA as Yl,ag as Jl,J as ql,an as Rl,k as jl,j as Gl,q as Wl,s as Kl,b9 as Ql,ba as Xl,e1 as Zl,bE as eo,af as to,ad as lo,am as oo,e2 as ao,b7 as so,K as no,av as et}from"./entry-BIjVVog3.js";/* empty css                   *//* empty css                 *//* empty css                        *//* empty css                 *//* empty css                        *//* empty css                        *//* empty css                    *//* empty css               *//* empty css                    *//* empty css                *//* empty css                  *//* empty css                 *//* empty css                  */import{h as ro}from"./html2canvas-esm-B4CzG4On.js";const wt=Cl("customPool",()=>{const N=f([]),J=f(null),w=f(!1),K=f(null),W=f([]),U=kt({}),O=re(()=>N.value.length);async function M(){w.value=!0,K.value=null;try{const c=await window.pywebview.api.get_custom_pools(),p=typeof c=="string"?JSON.parse(c):c;p&&p.status==="success"?(N.value=p.data||[],N.value.forEach(v=>{U[v.id]||(U[v.id]=1)})):(K.value=p?.message||"获取卡池失败",console.error("获取卡池失败:",p?.message))}catch(c){K.value=c.message||"获取卡池时发生错误",console.error("获取卡池错误:",c)}finally{w.value=!1}}async function A(c){w.value=!0,K.value=null;try{const p=await window.pywebview.api.save_custom_pool(c),v=typeof p=="string"?JSON.parse(p):p;if(v&&v.status==="success"){if(!c.id)N.value.push(v.data),U[v.data.id]=1;else{const V=N.value.findIndex(k=>k.id===v.data.id);V!==-1&&(N.value[V]=v.data)}return v.data}else return K.value=v?.message||"保存卡池失败",console.error("保存卡池失败:",v?.message),null}catch(p){return K.value=p.message||"保存卡池时发生错误",console.error("保存卡池错误:",p),null}finally{w.value=!1}}async function be(c,p){w.value=!0,K.value=null;try{const v=await window.pywebview.api.update_custom_pool(c,p),V=typeof v=="string"?JSON.parse(v):v;if(V&&V.status==="success"){const k=N.value.findIndex(X=>X.id===c);return k!==-1&&(N.value[k]=V.data),V.data}else return K.value=V?.message||"更新卡池失败",console.error("更新卡池失败:",V?.message),null}catch(v){return K.value=v.message||"更新卡池时发生错误",console.error("更新卡池错误:",v),null}finally{w.value=!1}}async function fe(c){w.value=!0,K.value=null;try{const p=await window.pywebview.api.delete_custom_pool(c),v=typeof p=="string"?JSON.parse(p):p;return v&&v.status==="success"?(N.value=N.value.filter(V=>V.id!==c),J.value&&J.value.id===c&&(J.value=null),W.value=W.value.filter(V=>V!==c),delete U[c],!0):(K.value=v?.message||"删除卡池失败",console.error("删除卡池失败:",v?.message),!1)}catch(p){return K.value=p.message||"删除卡池时发生错误",console.error("删除卡池错误:",p),!1}finally{w.value=!1}}function ie(c){J.value=c}function ue(c){const p=W.value.indexOf(c);p===-1?(W.value.push(c),U[c]||(U[c]=1)):W.value.splice(p,1)}function te(c,p){U[c]=p}function z(c){return U[c]||1}function de(c,p=1){const v=N.value.find(Z=>Z.id===c);if(!v||!v.cards||v.cards.length===0)return[];const V=v.cards,k=[],X=[...V];for(let Z=0;Z<p&&X.length>0;Z++){const ae=Math.floor(Math.random()*X.length);k.push(X.splice(ae,1)[0])}return k}function le(c,p=1){if(!c||c.length===0)return[];const v=[],V=[...c];for(let k=0;k<p&&V.length>0;k++){const X=Math.floor(Math.random()*V.length);v.push(V.splice(X,1)[0])}return v}function Ne(){return{id:null,name:"新建卡池",description:"这是一个新的自定义卡池",cards:[],dimensions:[]}}function _e(c=[]){const p={id:Date.now().toString(),title:"新卡片",description:"",properties:{}};return c.forEach(v=>{p.properties[v.key]=v.defaultValue||""}),p}function H(){M()}function oe(c,p){const v=N.value.find(V=>V.id===c);if(v){const V={...v,drawCount:p};customPoolStore.updatePool(c,V)}}function d(){}async function D(){try{return await this.loadPools()}catch(c){throw console.error("Failed to get pools:",c),c}}async function ve(c,p){try{const v=N.value.find(k=>k.id===c);if(!v)throw new Error("卡池不存在");const V={...v,active:p};return await be(c,V),v.active=p,!0}catch(v){throw console.error("更新卡池激活状态失败:",v),v}}return{pools:N,currentPool:J,selectedPoolIds:W,poolDrawCounts:U,isLoading:w,error:K,poolCount:O,fetchPools:M,savePool:A,updatePool:be,deletePool:fe,setCurrentPool:ie,togglePoolSelection:ue,setPoolDrawCount:te,getPoolDrawCount:z,drawFromPool:de,drawRandomCards:le,createNewPool:Ne,createNewCard:_e,init:H,updatePoolDrawCount:oe,savePools:d,getPools:D,updatePoolActive:ve}}),io={class:"custom-pool-card-exporter"},uo={class:"exporter-container"},co={class:"control-panel"},po={class:"panel-header"},fo={class:"export-info"},vo={class:"card-count"},mo={class:"theme-list-container"},go={class:"theme-list"},yo=["onClick"],ho={class:"theme-name"},bo={class:"export-options-section"},_o={class:"option-list"},wo={class:"option-item"},ko={class:"option-item"},xo={class:"option-item"},Co={class:"panel-footer"},$o=["disabled"],Vo={class:"preview-panel"},So={class:"preview-header"},Po={class:"preview-controls"},Eo={class:"preview-content",ref:"previewContent"},Lo={key:0,class:"preview-container"},To={class:"card-info"},Mo={class:"pool-name"},Do={class:"stat-item"},Uo={class:"stat-item"},No={class:"preview-image-container"},Oo=["src"],Fo={key:1,class:"generating-preview"},Bo={key:1,class:"empty-state"},Ao={__name:"CustomPoolCardExporter",props:{cards:{type:Array,required:!0,default:()=>[]},poolName:{type:String,default:"未知卡池"},poolInfo:{type:Object,default:()=>({})},dimensions:{type:Array,default:()=>[]}},emits:["export-success","close"],setup(N,{emit:J}){const w=N,K=J,W=$l(),U=f("classic"),O=f(0),M=f(""),A=f(!1),be=f(0),fe=f(""),ie=f(!1);let ue=null;const te=f(!0),z=f(!0),de=f("3"),le=re(()=>w.cards[O.value]||null),Ne=[{id:"classic",name:"经典",colors:{background:"linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%)",textPrimary:"#333333",textSecondary:"#666666",textTertiary:"#999999",cardBackground:"#ffffff",accent:"#409EFF",accentLight:"#ecf5ff",avatarBackground:"#4a93ff",border:"#eaeaea",borderLight:"#f0f0f0",sectionBackground:"#f9f9f9",tagBackground:"#f0f9ff",tagText:"#1890ff"},fonts:{primary:"'PingFang SC', 'Microsoft YaHei', sans-serif",title:"600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",subtitle:"500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",body:"400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",caption:"400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",dimension:"500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"},spacing:{cardPadding:"24px",sectionGap:"20px",itemGap:"12px"},borderRadius:"12px",shadow:"0 8px 24px rgba(0, 0, 0, 0.12)",cardShadow:"0 2px 12px rgba(0, 0, 0, 0.08)",backgroundPattern:{type:"geometric",opacity:.08,svg:`<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="classicPattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
            <circle cx="30" cy="30" r="20" fill="none" stroke="#409EFF" stroke-width="0.5" opacity="0.3"/>
            <circle cx="30" cy="30" r="10" fill="none" stroke="#409EFF" stroke-width="0.3" opacity="0.5"/>
            <path d="M15,15 L45,45 M45,15 L15,45" stroke="#409EFF" stroke-width="0.2" opacity="0.2"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#classicPattern)"/>
      </svg>`}},{id:"dark",name:"暗黑",colors:{background:"linear-gradient(135deg, #2d3748 0%, #1a202c 100%)",textPrimary:"#e2e8f0",textSecondary:"#a0aec0",textTertiary:"#718096",cardBackground:"#2d3748",accent:"#63b3ed",accentLight:"#2a4a6b",avatarBackground:"#4299e1",border:"#4a5568",borderLight:"#3a4a5c",sectionBackground:"#283141",tagBackground:"#2a4a6b",tagText:"#90cdf4"},fonts:{primary:"'PingFang SC', 'Microsoft YaHei', sans-serif",title:"600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",subtitle:"500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",body:"400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",caption:"400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",dimension:"500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"},spacing:{cardPadding:"24px",sectionGap:"20px",itemGap:"12px"},borderRadius:"12px",shadow:"0 8px 24px rgba(0, 0, 0, 0.3)",cardShadow:"0 2px 12px rgba(0, 0, 0, 0.2)",backgroundPattern:{type:"circuit",opacity:.05,svg:`<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="darkPattern" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
            <rect x="20" y="20" width="40" height="40" fill="none" stroke="#63b3ed" stroke-width="0.5" opacity="0.3"/>
            <circle cx="40" cy="40" r="15" fill="none" stroke="#63b3ed" stroke-width="0.3" opacity="0.4"/>
            <line x1="0" y1="40" x2="80" y2="40" stroke="#63b3ed" stroke-width="0.2" opacity="0.2"/>
            <line x1="40" y1="0" x2="40" y2="80" stroke="#63b3ed" stroke-width="0.2" opacity="0.2"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#darkPattern)"/>
      </svg>`}},{id:"creative",name:"灵感",colors:{background:"linear-gradient(135deg, #fdf6fd 0%, #f3e7ff 100%)",textPrimary:"#4a2a5d",textSecondary:"#7b5a8c",textTertiary:"#a78bba",cardBackground:"#fcf8ff",accent:"#9c6bdf",accentLight:"#f3e7ff",avatarBackground:"#7956b3",border:"#e6d8f8",borderLight:"#f0e8ff",sectionBackground:"#f8f0ff",tagBackground:"#f3e7ff",tagText:"#8b5cf6"},fonts:{primary:"'PingFang SC', 'Microsoft YaHei', sans-serif",title:"600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",subtitle:"500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",body:"400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",caption:"400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",dimension:"500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"},spacing:{cardPadding:"24px",sectionGap:"20px",itemGap:"12px"},borderRadius:"12px",shadow:"0 8px 24px rgba(156, 107, 223, 0.15)",cardShadow:"0 2px 12px rgba(156, 107, 223, 0.1)",backgroundPattern:{type:"artistic",opacity:.06,svg:`<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="creativePattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
            <path d="M50,10 Q70,30 50,50 Q30,70 50,90 Q70,70 90,50 Q70,30 50,10" fill="none" stroke="#9c6bdf" stroke-width="0.5" opacity="0.3"/>
            <circle cx="25" cy="25" r="8" fill="none" stroke="#9c6bdf" stroke-width="0.3" opacity="0.4"/>
            <circle cx="75" cy="75" r="8" fill="none" stroke="#9c6bdf" stroke-width="0.3" opacity="0.4"/>
            <path d="M10,50 Q30,30 50,50 Q70,70 90,50" fill="none" stroke="#9c6bdf" stroke-width="0.2" opacity="0.2"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#creativePattern)"/>
      </svg>`}}],_e=re(()=>W.state.config.customThemes||[]),H=re(()=>[..._e.value,...Ne]),oe={spacing:{cardPadding:"24px",sectionGap:"20px",itemGap:"12px"},fonts:{primary:"'PingFang SC', 'Microsoft YaHei', sans-serif",title:"600 28px 'PingFang SC', 'Microsoft YaHei', sans-serif",subtitle:"500 16px 'PingFang SC', 'Microsoft YaHei', sans-serif",body:"400 14px 'PingFang SC', 'Microsoft YaHei', sans-serif",caption:"400 12px 'PingFang SC', 'Microsoft YaHei', sans-serif",dimension:"500 15px 'PingFang SC', 'Microsoft YaHei', sans-serif"},borderRadius:"12px",shadow:"0 8px 24px rgba(0, 0, 0, 0.12)",cardShadow:"0 2px 12px rgba(0, 0, 0, 0.08)"},d=re(()=>{const _=H.value.find(g=>g.id===U.value)||H.value[0];return{...oe,..._,spacing:{...oe.spacing,..._.spacing||{}},fonts:{...oe.fonts,..._.fonts||{}},colors:{background:"linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%)",textPrimary:"#333333",textSecondary:"#666666",textTertiary:"#999999",cardBackground:"#ffffff",accent:"#409EFF",accentLight:"#ecf5ff",avatarBackground:"#4a93ff",border:"#eaeaea",borderLight:"#f0f0f0",sectionBackground:"#f9f9f9",tagBackground:"#f0f9ff",tagText:"#1890ff",..._.colors||{}}}}),D=_=>{const g=w.dimensions.find(S=>S.id===_);return g?g.name:_},ve=(_,g)=>{const S=w.dimensions.find(q=>q.id===g);if(!S)return _;switch(S.type){case"array":return Array.isArray(_)?_.length===0?"暂无":_.map((q,R)=>`<span style="
            display: inline-block;
            background: ${d.value.colors.tagBackground};
            color: ${d.value.colors.tagText};
            padding: 3px 10px;
            margin: 2px 4px 2px 0;
            border-radius: 12px;
            font: ${d.value.fonts.caption};
            font-weight: 500;
            border: 1px solid ${d.value.colors.accentLight};
          ">${c(q)}</span>`).join(""):c(_);case"boolean":return _?"是":"否";case"text":return typeof _=="string"&&_.includes(`
`)?c(_).replace(/\n/g,"<br>"):c(_||"暂无");case"select":case"number":case"date":case"color":default:return c(_||"暂无")}},c=_=>_?_.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;"):"";let p=null,v=null;const V=async(_,g=null,S=null)=>{if(!_)return"";try{v!==U.value&&(k(),v=U.value),p||(p=document.createElement("div"),p.className="custom-pool-card-export",document.body.appendChild(p));let q=`
      width: 400px;
      padding: ${d.value.spacing.cardPadding};
      background: ${d.value.colors.background};
      border-radius: ${d.value.borderRadius};
      box-shadow: ${d.value.shadow};
      position: fixed;
      top: -9999px;
      left: -9999px;
      color: ${d.value.colors.textPrimary};
      z-index: -1;
      font-family: ${d.value.fonts.primary};
    `;p.style.cssText=q;let R="";d.value.backgroundPattern&&d.value.backgroundPattern.svg&&(R=`
        <div style="
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          opacity: ${d.value.backgroundPattern.opacity||.05};
          pointer-events: none;
          border-radius: ${d.value.borderRadius};
          overflow: hidden;
        ">
          ${d.value.backgroundPattern.svg}
        </div>
      `),p.innerHTML=`
      ${R}
      <div style="position: relative; z-index: 1;">
        <div style="text-align: center; margin-bottom: ${d.value.spacing.sectionGap};">
          <h2 style="
            margin: 0;
            font: ${d.value.fonts.title};
            color: ${d.value.colors.textPrimary};
          ">${g!==null?X(g+1,S||w.cards.length)+". ":""}${c(_.title||"未命名卡片")}</h2>
          ${te.value?`
            <p style="
              margin: 8px 0 0 0;
              font: ${d.value.fonts.body};
              color: ${d.value.colors.textSecondary};
            ">来自: ${c(w.poolName)}</p>
          `:""}
        </div>

        ${z.value&&_.properties&&Object.keys(_.properties).length>0?`
          <div style="
            background: ${d.value.colors.cardBackground};
            border-radius: ${d.value.borderRadius};
            padding: ${d.value.spacing.itemGap};
            border: 1px solid ${d.value.colors.border};
            box-shadow: ${d.value.cardShadow};
          ">
            <h3 style="
              margin: 0 0 ${d.value.spacing.itemGap} 0;
              font: ${d.value.fonts.subtitle};
              color: ${d.value.colors.textPrimary};
              border-bottom: 2px solid ${d.value.colors.accent};
              padding-bottom: 6px;
            ">属性信息</h3>
            ${Object.entries(_.properties).map(([ce,Oe])=>{const Me=D(ce),Fe=ve(Oe,ce),me=w.dimensions.find($e=>$e.id===ce);return`
                <div style="margin-bottom: ${d.value.spacing.itemGap};">
                  <div style="
                    font: ${d.value.fonts.dimension};
                    color: ${d.value.colors.textPrimary};
                    margin-bottom: 4px;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                  ">
                    <span style="
                      background: ${d.value.colors.accent};
                      color: white;
                      padding: 3px 8px;
                      border-radius: 12px;
                      font: ${d.value.fonts.caption};
                      font-weight: 500;
                    ">${c(Me)}</span>
                    ${me&&me.description?`
                      <span style="
                        font: ${d.value.fonts.caption};
                        color: ${d.value.colors.textTertiary};
                        font-weight: normal;
                      ">${c(me.description)}</span>
                    `:""}
                  </div>
                  <div style="
                    font: ${d.value.fonts.body};
                    color: ${d.value.colors.textSecondary};
                    background: ${d.value.colors.sectionBackground};
                    padding: 10px 12px;
                    border-radius: 8px;
                    word-break: break-word;
                    line-height: 1.6;
                    border-left: 3px solid ${d.value.colors.accent};
                    min-height: 20px;
                  ">${Fe}</div>
                </div>
              `}).join("")}
          </div>
        `:""}
      </div>

        <div style="
          text-align: center;
          margin-top: ${d.value.spacing.sectionGap};
          font: ${d.value.fonts.caption};
          color: ${d.value.colors.textTertiary};
          opacity: 0.8;
        ">
          ✨ Powered By PVV • ${new Date().toLocaleDateString("zh-CN")}
        </div>
    `,await new Promise(ce=>setTimeout(ce,50));const Y={1:1.5,2:2,3:3}[de.value]||2;return(await ro(p,{scale:Y,useCORS:!0,backgroundColor:null,allowTaint:!0,logging:!1})).toDataURL("image/png")}catch(q){return console.error("生成卡片预览失败:",q),""}},k=()=>{p&&p.parentNode&&(document.body.removeChild(p),p=null),v=null},X=(_,g=999)=>{const S=Math.max(3,g.toString().length);return _.toString().padStart(S,"0")},Z=async()=>{le.value&&(M.value="",M.value=await V(le.value,O.value,w.cards.length))},ae=async()=>{if(w.cards.length===0){C.warning("没有卡片可供导出");return}try{A.value=!0,be.value=0,fe.value="",ie.value=!1;const _=await window.pywebview.api.select_directory(),g=typeof _=="string"?JSON.parse(_):_;if(!g||g.status!=="success"||!g.data){A.value=!1,fe.value="",C.error("取消导出");return}ue=Ll.service({lock:!0,text:"正在导出卡片...",background:"rgba(0, 0, 0, 0.7)",customClass:"export-loading"});const S=g.data,q=[],R=[];for(let I=0;I<w.cards.length;I++){if(ie.value){C.warning(`导出已取消，已成功导出 ${q.length} 张卡片`);break}const Y=w.cards[I];fe.value=Y.title||`卡片${I+1}`,ue&&ue.setText(`正在导出第 ${I+1}/${w.cards.length} 张卡片: ${fe.value}`);try{const se=await V(Y,I,w.cards.length);if(!se){console.warn(`跳过卡片 ${Y.title}: 生成预览失败`),R.push(Y.title||"未命名卡片");continue}const we=I+1,ce=X(we,w.cards.length),Oe=(Y.title||`卡片${we}`).replace(/[<>:"/\\|?*]/g,"_"),Me=`${w.poolName}_${ce}_${Oe}.png`,Fe=`${S}/${Me}`,me=await window.pywebview.api.save_entity_card({file_path:Fe,image_data:se}),$e=typeof me=="string"?JSON.parse(me):me;$e&&$e.status==="success"?q.push(Me):(console.warn(`保存卡片 ${Y.title} 失败:`,$e?.message),R.push(Y.title||"未命名卡片"))}catch(se){console.error(`导出卡片 ${Y.title} 时出错:`,se),R.push(Y.title||"未命名卡片")}be.value=I+1,I<w.cards.length-1&&await new Promise(se=>setTimeout(se,100))}if(q.length>0){let I=`成功导出 ${q.length} 张卡片`;R.length>0&&(I+=`，${R.length} 张失败`),I+=`
保存位置: ${S}`,C.success(I),K("export-success",{directory:S,count:q.length,failed:R.length,failedCards:R});try{await window.pywebview.api.open_directory(S)}catch(Y){console.warn("无法打开文件目录:",Y)}}else C.error(`导出失败，所有 ${w.cards.length} 张卡片都未能成功导出`)}catch(_){console.error("导出卡片时出错:",_),C.error("导出失败："+(_.message||"未知错误"))}finally{k(),ue&&(ue.close(),ue=null),A.value=!1,be.value=0,fe.value="",ie.value=!1}};return Le(O,Z),Le(U,()=>{k(),Z()}),Le([te,z,de],Z),tt(async()=>{if(!W.state.config.loaded)try{await W.loadConfig()}catch(_){console.error("加载配置失败:",_),C.error("加载自定义主题失败")}w.cards.length>0&&He(Z)}),Ct(()=>{k(),ie.value===!1&&(ie.value=!0)}),(_,g)=>(u(),y("div",io,[s("div",uo,[s("div",co,[s("div",po,[g[5]||(g[5]=s("h3",{class:"panel-title"},"卡片导出设置",-1)),s("div",fo,[s("span",vo,$(N.cards.length)+" 张卡片",1)])]),g[11]||(g[11]=s("div",{class:"theme-section-header"},[s("div",{class:"section-title"},"选择主题风格")],-1)),s("div",mo,[s("div",go,[(u(!0),y(Q,null,ee(H.value,S=>(u(),y("div",{key:S.id,class:Ee(["theme-item",{active:U.value===S.id}]),onClick:q=>U.value=S.id},[s("div",{class:"theme-preview",style:Te({background:S.colors.background,borderColor:U.value===S.id?S.colors.accent:"transparent"})},[s("div",{class:"theme-card",style:Te({background:S.colors.cardBackground})},null,4),s("div",{class:"theme-accent",style:Te({background:S.colors.accent})},null,4)],4),s("div",ho,$(S.name),1)],10,yo))),128))])]),s("div",bo,[g[10]||(g[10]=s("div",{class:"section-title"},"导出选项",-1)),s("div",_o,[s("label",wo,[je(s("input",{type:"checkbox","onUpdate:modelValue":g[0]||(g[0]=S=>te.value=S)},null,512),[[yt,te.value]]),g[6]||(g[6]=s("span",{class:"option-text"},"包含卡池信息",-1))]),s("label",ko,[je(s("input",{type:"checkbox","onUpdate:modelValue":g[1]||(g[1]=S=>z.value=S)},null,512),[[yt,z.value]]),g[7]||(g[7]=s("span",{class:"option-text"},"包含属性维度",-1))]),s("div",xo,[g[9]||(g[9]=s("span",{class:"option-label"},"图片质量:",-1)),je(s("select",{"onUpdate:modelValue":g[2]||(g[2]=S=>de.value=S),class:"quality-select"},g[8]||(g[8]=[s("option",{value:"1"},"标准",-1),s("option",{value:"2"},"高清",-1),s("option",{value:"3"},"超清",-1)]),512),[[Vl,de.value]])])])]),s("div",Co,[s("button",{class:Ee(["export-btn",{loading:A.value}]),disabled:N.cards.length===0||A.value,onClick:ae},$(A.value?"导出中...":`导出全部 ${N.cards.length} 张卡片`),11,$o)])]),s("div",Vo,[s("div",So,[g[12]||(g[12]=s("h3",{class:"panel-title"},"卡片预览",-1)),s("div",Po,[a(b(El),{size:"small"},{default:r(()=>[a(b(Ge),{type:O.value>0?"primary":"default",disabled:O.value<=0,onClick:g[3]||(g[3]=S=>O.value--)},{default:r(()=>[a(b(Ye),null,{default:r(()=>[a(b(Sl))]),_:1})]),_:1},8,["type","disabled"]),a(b(Ge),{disabled:""},{default:r(()=>[x($(O.value+1)+" / "+$(N.cards.length),1)]),_:1}),a(b(Ge),{type:O.value<N.cards.length-1?"primary":"default",disabled:O.value>=N.cards.length-1,onClick:g[4]||(g[4]=S=>O.value++)},{default:r(()=>[a(b(Ye),null,{default:r(()=>[a(b(Pl))]),_:1})]),_:1},8,["type","disabled"])]),_:1})])]),s("div",Eo,[le.value?(u(),y("div",Lo,[s("div",To,[s("h4",null,$(X(O.value+1,N.cards.length))+". "+$(le.value.title),1),s("span",Mo,"来自: "+$(N.poolName),1),s("span",Do,[a(b(Ye),null,{default:r(()=>[a(b(lt))]),_:1}),x(" "+$(Object.keys(le.value.properties||{}).length)+" 个属性 ",1)]),s("span",Uo,[a(b(Ye),null,{default:r(()=>[a(b($t))]),_:1}),x(" "+$(U.value)+" 主题 ",1)])]),s("div",No,[M.value?(u(),y("img",{key:0,src:M.value,alt:"卡片预览",class:"preview-image"},null,8,Oo)):(u(),y("div",Fo,g[13]||(g[13]=[s("div",{class:"loading-spinner"},null,-1),s("p",null,"生成预览中...",-1)])))])])):(u(),y("div",Bo,g[14]||(g[14]=[s("div",{class:"empty-icon"},"📄",-1),s("p",{class:"empty-text"},"没有可预览的卡片",-1)])))],512)])])]))}},Io=xt(Ao,[["__scopeId","data-v-66f53b4c"]]),zo={class:"custom-pool-container"},Ho={class:"app-header"},Yo={class:"header-actions"},Jo={class:"app-content"},qo={class:"pool-title-wrapper"},Ro=["title"],jo={class:"pool-count"},Go={class:"pool-functions"},Wo=["onClick"],Ko={class:"pool-items-wrapper"},Qo={class:"pool-items-container native-scroll"},Xo=["onClick"],Zo={class:"item-title"},ea={class:"drawing-section tech-panel"},ta={class:"drawing-header"},la={class:"section-title"},oa={class:"interaction-tips"},aa={key:0,class:"fixed-elements-indicator"},sa={class:"fixed-count"},na={class:"export-btn-content"},ra={class:"export-count"},ia={style:{"text-align":"left"}},ua={class:"result-scroll-container"},da={class:"result-container"},ca={class:"result-group-header"},pa={class:"group-title"},fa={class:"group-count"},va={class:"result-items"},ma=["onClick"],ga=["onClick"],ya=["onClick"],ha={key:0,class:"empty-result"},ba={class:"detail-header"},_a={class:"detail-title"},wa={class:"detail-category-badge"},ka={class:"detail-content"},xa={class:"detail-section-title"},Ca={class:"detail-property-value"},$a={key:1,class:"detail-array-list"},Va={key:2,class:"detail-color"},Sa={class:"color-code"},Pa={key:3,class:"detail-text"},Ea={class:"detail-footer"},La={class:"detail-actions"},Ta={class:"pool-edit-container"},Ma={class:"pool-basic-info"},Da={class:"dialog-footer"},Ua={class:"manager-container dialog-content-wrapper"},Na={class:"editor-toolbar"},Oa={class:"toolbar-actions"},Fa={key:0,class:"entity-editor"},Ba={class:"card-title"},Aa={key:1},Ia={key:3,class:"property-value"},za={key:1,class:"empty-property"},Ha={key:1,class:"dimension-editor"},Ya={class:"options-editor"},Ja={class:"dialog-footer"},qa={class:"properties-editor dialog-content-wrapper"},Ra={class:"card-title-section"},ja={class:"properties-list"},Ga={class:"property-item-header"},Wa={class:"property-name"},Ka={class:"property-title"},Qa={class:"property-type-badge"},Xa={class:"property-item-editor"},Za={key:3,class:"array-editor"},es={class:"dialog-footer"},ts={class:"add-card-container dialog-content-wrapper"},ls={class:"card-title-section"},os={class:"card-properties-section"},as={key:1,class:"property-form"},ss={class:"property-label"},ns={class:"property-type-badge"},rs={class:"property-input"},is={key:3,class:"array-editor"},us={class:"dialog-footer"},ds={class:"import-dialog-content"},cs={key:0,class:"import-error"},ps={class:"dialog-footer"},fs={__name:"CustomPool",setup(N){const J=wt(),w=re(()=>J.pools.map(t=>({...t,drawCount:t.drawCount||1,cards:t.cards?t.cards.map(o=>({...o,selected:!1})):[]}))),K=re(()=>J.isLoading),W=f([]),U=f(!1),O=f(null),M=f([]);re(()=>W.value.length>0);const A=kt({}),be=re(()=>Object.keys(A).length>0),fe=re(()=>Object.keys(A).length),ie=f(null),ue=f(!1),te=f(!1),z=f(null),de=f(!1),le=f(null),Ne=f(null),_e=f(!1),H=f(null),oe=f("cards"),d=f([]),D=f([]),ve=f(!1),c=f(null),p=f([]),v=f(!1),V=f(null),k=f({});f(""),re(()=>{if(!V.value)return[];const t=Object.keys(k.value);return D.value.filter(e=>!t.includes(e.id))});const X=f(!0),Z=f(1);f(2.5),f(null);let ae=null,_=!1;f(!1);let g=[];const S=new Map,q=f(50),R=f(new Set),I=f(!1),Y=f([]),se=f(""),we=f({}),ce=f([]),Oe=re(()=>Y.value.length>0?`导出卡片 - ${se.value} (${Y.value.length}张)`:"导出卡片");tt(()=>{J.init(),He(()=>{const e=Pt(),o=fl(),n=vl();e&&g.push(e),o&&g.push(o),n&&g.push(n)}),nl();const t=document.querySelector(".result-scroll-container");t&&(t.addEventListener("wheel",Ke,{passive:!1}),g.push(()=>{t.removeEventListener("wheel",Ke)})),Xe(),Le(()=>M.value.length,()=>{He(()=>{Xe()})})});function Me(){if(M.value.length===0){C.warning("没有抽取结果可供导出");return}Y.value=M.value.map(e=>({title:e.title,properties:e.properties||{}})),se.value="抽取结果",we.value={description:`包含来自 ${Object.keys(Qe.value).length} 个卡池的抽取结果`,totalCards:M.value.length};const t=new Map;M.value.forEach(e=>{const o=w.value.find(n=>n.id===e.poolId);o&&o.dimensions&&o.dimensions.forEach(n=>{t.set(n.id,n)})}),ce.value=Array.from(t.values()),I.value=!0}function Fe(){if(!O.value){C.warning("没有选中的卡片");return}Y.value=[{title:O.value.title,properties:O.value.properties||{}}],se.value=qe(O.value.poolId),we.value={description:`来自卡池: ${se.value}`,totalCards:1};const t=w.value.find(e=>e.id===O.value.poolId);ce.value=t&&t.dimensions?t.dimensions:[],I.value=!0,U.value=!1}function me(t){if(!t.cards||t.cards.length===0){C.warning("该卡池没有卡片可供导出");return}Y.value=t.cards.map(e=>({title:e.title,properties:e.properties||{}})),se.value=t.name,we.value={description:t.description||"",totalCards:t.cards.length,dimensions:t.dimensions||[]},ce.value=t.dimensions||[],I.value=!0}function $e(t){C.success(`成功导出 ${t.count} 张卡片`),I.value=!1}function Vt(t,e){return!t||t.length===0?[]:t.length<=q.value||R.value.has(e)?t:t.slice(0,q.value)}function ot(t,e){R.value.has(t)?R.value.delete(t):R.value.add(t)}function St(){S.clear()}function Pt(){if(!le.value)return;let t=!1,e=0,o=0;const n=le.value,h=E=>{const B=E.target;B.tagName==="BUTTON"||B.tagName==="INPUT"||B.closest(".el-button")||B.closest(".el-checkbox")||B.closest(".el-select")||B.closest(".action-btn")||(t=!0,n.classList.add("grabbing"),e=E.pageX-n.offsetLeft,o=n.scrollLeft,n.style.scrollBehavior="auto",document.body.style.userSelect="none")},m=()=>{t&&(t=!1,n.classList.remove("grabbing"),n.style.scrollBehavior="smooth",document.body.style.userSelect="")},P=E=>{if(!t)return;E.preventDefault();const j=(E.pageX-n.offsetLeft-e)*1.2,pe=o-j,ye=n.scrollWidth-n.clientWidth;n.scrollLeft=Math.max(0,Math.min(pe,ye))};return n.addEventListener("mousedown",h),document.addEventListener("mouseup",m),document.addEventListener("mouseleave",m),document.addEventListener("mousemove",P),()=>{n.removeEventListener("mousedown",h),document.removeEventListener("mouseup",m),document.removeEventListener("mouseleave",m),document.removeEventListener("mousemove",P)}}function Et(t,e){Je(t,e,!e.selected)}function Je(t,e,o){const n=w.value.find(m=>m.id===t);if(!n||!n.cards)return;const h=n.cards.find(m=>m.id===e.id);if(h)if(h.selected=o,o)W.value.some(m=>m.id===e.id&&m.poolId===t)||W.value.push({id:e.id,poolId:t,...e}),M.value.some(m=>m.id===e.id&&m.poolId===t)||M.value.push({...e,poolId:t});else{W.value=W.value.filter(P=>!(P.id===e.id&&P.poolId===t));const m=`${t}-${e.id}`;A[m]||(M.value=M.value.filter(P=>!(P.id===e.id&&P.poolId===t)))}}function qe(t){const e=w.value.find(o=>o.id===t);return e?e.name:"未知卡池"}function Lt(){M.value.length!==0&&(w.value.forEach(t=>{t.cards&&t.cards.forEach(e=>{const o=`${t.id}-${e.id}`;A[o]||(e.selected=!1)})}),M.value=M.value.filter(t=>{const e=`${t.poolId}-${t.id}`;return A[e]}),W.value=W.value.filter(t=>{const e=`${t.poolId}-${t.id}`;return A[e]}))}function Tt(t,e){const o=w.value.find(n=>n.id===t);if(o){const n={...o,drawCount:e};J.updatePool(t,n)}}async function Mt(t,e){try{await wt().updatePoolActive(t,e),C.success(`卡池${e?"激活":"停用"}成功`)}catch(o){console.error("切换卡池激活状态失败:",o),C.error(`操作失败: ${o.message||"未知错误"}`);const n=w.value.find(h=>h.id===t);n&&(n.active=!e)}}function Dt(t,e){e?.stopPropagation(),O.value=t,U.value=!0,setTimeout(()=>{const o=document.querySelector(".detail-scrollbar .el-scrollbar__wrap");o&&(o.scrollTop=0),Ut()},300)}function Ut(){ae&&(clearInterval(ae),ae=null);const t=document.querySelector(".detail-scrollbar .el-scrollbar__wrap");if(!t)return;t.scrollTop=0,Z.value=1;let e=1,o=1;const n=document.querySelector(".detail-scrollbar");n&&(n.classList.remove("user-hovering"),n.classList.remove("changing-direction"),n.classList.add("auto-scrolling"),n.classList.add("high-performance"));const h=120,m=1,P=.65;ae=setInterval(()=>{if(!U.value||!X.value||_)return;const{scrollTop:E,scrollHeight:B,clientHeight:j}=t;E>=B-j-2&&Z.value>0?(Z.value=-1,o=P,n?.classList.add("changing-direction"),t.style.scrollBehavior="smooth",setTimeout(()=>{n?.classList.remove("changing-direction"),t.style.scrollBehavior="auto"},800)):E<=2&&Z.value<0&&(Z.value=1,o=m,n?.classList.add("changing-direction"),t.style.scrollBehavior="smooth",setTimeout(()=>{n?.classList.remove("changing-direction"),t.style.scrollBehavior="auto"},800)),Math.abs(e-o)>.01?e+=(o-e)*.1:e=o;const pe=h*e;t.scrollTop+=Z.value*(pe/120)},8)}function Nt(t){if(typeof t!="string")return!1;if(/^#([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6}|[0-9A-Fa-f]{8})$/.test(t))return!0;const o=/^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/,n=/^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/;return!!(o.test(t)||n.test(t))}function Ot(){const t=w.value.filter(n=>n.active&&n.cards&&n.cards.length>0);if(t.length===0){C.warning("没有激活的卡池可用于抽取");return}St(),R.value.clear();const e=[];M.value.forEach(n=>{const h=`${n.poolId}-${n.id}`;if(A[h]){e.push({...n});const m=t.find(P=>P.id===n.poolId);if(m&&m.cards){const P=m.cards.find(E=>E.id===n.id);P&&(P.selected=!0)}}}),t.forEach(n=>{n.cards.forEach(h=>{const m=`${n.id}-${h.id}`;A[m]||Je(n.id,h,!1)})});const o=[];o.push(...e),t.forEach(n=>{const h=n.drawCount||1,m=e.filter(E=>E.poolId===n.id).length,P=Math.max(0,h-m);if(P>0){const E=n.cards.filter(j=>{const pe=`${n.id}-${j.id}`;return!A[pe]});J.drawRandomCards(E,P).forEach(j=>{const pe=n.cards.find(ye=>ye.id===j.id);pe&&Je(n.id,pe,!0),o.push({...j,poolId:n.id})})}}),M.value=o}function at(t,e){const o=w.value.find(h=>h.id===t);if(!o||!o.dimensions)return e;const n=o.dimensions.find(h=>h.id===e);return n?n.name:e}function Ft(){z.value=J.createNewPool(),de.value=!0,te.value=!0}function Bt(t){et.confirm("您希望编辑什么内容？","编辑卡池",{confirmButtonText:"卡池内容",cancelButtonText:"基本信息",distinguishCancelAndClose:!0,type:"info"}).then(()=>{It(t)}).catch(e=>{e==="cancel"&&(z.value=JSON.parse(JSON.stringify(t)),de.value=!1,te.value=!0)})}const At=t=>{t&&et.confirm(`确定要删除卡池"${t.name}"吗？此操作不可恢复！`,"删除确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning",closeOnClickModal:!1}).then(()=>{J.deletePool(t.id).then(()=>{C.success("删除成功"),te&&(te.value=!1)}).catch(e=>{C.error(`删除失败: ${e.message||"未知错误"}`)})}).catch(()=>{})};function Ve(){document.body.style.overflow="hidden"}function Se(){document.body.style.overflow="auto"}async function st(){if(!z.value.name){C.warning("卡池名称不能为空");return}try{de.value&&(z.value.cards=[],z.value.dimensions=[]),await J.savePool(z.value)&&(te.value=!1,C.success(de.value?"卡池创建成功":"卡池更新成功"))}catch(t){C.error(`保存卡池失败: ${t.message||"未知错误"}`)}}function It(t){H.value={...t},d.value=t.cards?JSON.parse(JSON.stringify(t.cards)):[],D.value=t.dimensions?JSON.parse(JSON.stringify(t.dimensions)):[],We(),oe.value="cards",_e.value=!0}function zt(){if(oe.value==="cards")Zt();else{const t={id:`dim_${Date.now()}`,name:"",description:"",type:"text",options:[]};D.value.push(t)}}function We(){const t=D.value.map(e=>e.id);d.value.forEach(e=>{e.properties||(e.properties={});for(const o in e.properties)t.includes(o)||delete e.properties[o];D.value.forEach(o=>{if(e.properties[o.id]===void 0){let n="";switch(o.type){case"number":n=0;break;case"select":n=o.options?.length>0?o.options[0]:"";break;case"array":n=[];break;case"boolean":n=!1;break;default:n=""}e.properties[o.id]=n}})})}function Ht(){const t=H.value?.dimensions||[],e=D.value;(t.length!==e.length||t.some(n=>{const h=e.find(m=>m.id===n.id);return!h||h.type!==n.type}))&&(We(),C.info("卡片属性已根据维度变更自动调整"))}function Yt(t){const e=D.value[t].id;d.value.forEach(o=>{o.properties&&o.properties[e]&&delete o.properties[e]}),D.value.splice(t,1),C.info("已从所有卡片中移除相关属性")}function Jt(t){c.value=t,p.value=t.options?[...t.options]:[],ve.value=!0}function qt(){p.value.push("")}function Rt(t){p.value.splice(t,1)}function jt(){c.value.options=p.value.filter(t=>t.trim()!==""),ve.value=!1}async function Gt(){if(H.value)try{const t={name:H.value.name,description:H.value.description,cards:d.value,dimensions:D.value,version:"1.0"},e=JSON.stringify(t,null,2);await window.pywebview.api.copy_to_clipboard(e),C({message:"卡池配置已复制到剪贴板",type:"success",duration:2e3})}catch(t){console.error("导出失败:",t),C.error("复制到剪贴板失败，请检查浏览器权限")}}async function Wt(){Ie.value="",ge.value="",Ae.value=!0}function Kt(t){V.value=JSON.parse(JSON.stringify(t)),k.value=JSON.parse(JSON.stringify(t.properties||{})),D.value.forEach(e=>{if(k.value[e.id]===void 0){let o="";switch(e.type){case"number":o=0;break;case"select":o=e.options?.length>0?e.options[0]:"";break;case"array":o=[];break;case"boolean":o=!1;break;case"date":o="";break;case"color":o="#409EFF";break;default:o=""}k.value[e.id]=o}}),v.value=!0}function Qt(){if(V.value){const t=d.value.findIndex(e=>e.id===V.value.id);t!==-1&&(d.value[t].title=V.value.title,d.value[t].properties=JSON.parse(JSON.stringify(k.value))),v.value=!1,C.success("卡片已更新")}}function nt(t,e="new"){const o=e==="new"?F.value.properties:k.value;Array.isArray(o[t])||(o[t]=[]),o[t].push("")}function rt(t,e,o="new"){const n=o==="new"?F.value.properties:k.value;Array.isArray(n[t])&&n[t].splice(e,1)}function it(t){return{text:"文本",number:"数字",select:"选项",array:"数组",boolean:"布尔",date:"日期",color:"颜色"}[t]||t}const Be=f(!1),F=f({id:"",title:"",description:"",properties:{}});function Xt(){const t=`card_${Date.now()}`;F.value={id:t,title:`卡片 ${d.value.length+1}`,description:"",properties:{}},D.value.forEach(e=>{let o;switch(e.type){case"text":o="";break;case"number":o=0;break;case"select":o=e.options?.length>0?e.options[0]:"";break;case"array":o=[];break;case"boolean":o=!1;break;case"date":o=new Date().toISOString().split("T")[0];break;case"color":o="#409EFF";break;default:o=""}e.defaultValue!==void 0&&(o=e.defaultValue),F.value.properties[e.id]=o})}function Zt(){if(D.value.length===0){C.warning("请先添加维度再添加卡片"),oe.value="dimensions";return}Xt(),Be.value=!0}async function el(){const t=tl(F.value);if(t.length>0){C({type:"warning",message:t.join(`
`),duration:5e3,showClose:!0});return}try{d.value.push(JSON.parse(JSON.stringify(F.value))),Be.value=!1,C({type:"success",message:"卡片添加成功"})}catch(e){C({type:"error",message:"添加卡片失败："+(e.message||"未知错误")})}}function tl(t){const e=[];return D.value.forEach(o=>{const n=t.properties[o.id];switch(o.type){case"text":case"select":o.required&&(!n||n.trim()==="")&&e.push(`${o.name} 不能为空`);break;case"number":o.required&&(n==null||isNaN(n))&&e.push(`${o.name} 必须是有效数字`),o.min!==void 0&&n<o.min&&e.push(`${o.name} 不能小于 ${o.min}`),o.max!==void 0&&n>o.max&&e.push(`${o.name} 不能大于 ${o.max}`);break;case"array":o.required&&(!Array.isArray(n)||n.length===0)&&e.push(`${o.name} 至少需要一个值`);break;case"date":o.required&&!n&&e.push(`${o.name} 必须选择日期`);break;case"color":o.required&&!n&&e.push(`${o.name} 必须选择颜色`);break}}),e}const Ae=f(!1),Ie=f(""),ge=f(""),ke=f(!1);function ll(){return"pool_"+Date.now()+"_"+Math.floor(Math.random()*1e4)}async function ol(){ge.value="",ke.value=!0;try{if(!Ie.value.trim()){ge.value="请输入JSON配置",ke.value=!1;return}const t=JSON.parse(Ie.value);if(!t.cards||!t.dimensions){ge.value="导入的数据格式不正确，缺少卡片或维度信息",ke.value=!1;return}if(!H.value){z.value={name:t.name||"导入的卡池",description:t.description||"",id:ll()};try{await st(),H.value=z.value}catch(e){ge.value="创建新卡池失败: "+e.message,ke.value=!1;return}}d.value=t.cards,D.value=t.dimensions,t.name&&H.value&&(H.value.name=t.name),t.description&&H.value&&(H.value.description=t.description),We();try{await dt(!0),ke.value=!1,Ae.value=!1,C.success("卡池配置导入成功并已保存"),await al(),_e.value=!1}catch(e){ge.value="保存导入数据失败: "+e.message,ke.value=!1}}catch(t){console.error("导入失败:",t),ge.value=t.message||"无法解析JSON或保存数据",ke.value=!1}}async function al(){try{await J.fetchPools(),w.value=J.pools}catch(t){console.error("刷新卡池数据失败:",t),C.error("刷新数据失败: "+t.message)}}function ut(){He(()=>{document.querySelectorAll(".el-scrollbar").forEach(e=>{const o=e.__vue__;o&&typeof o.update=="function"&&o.update()})})}Le(()=>w.value.length,()=>{ut()}),Le(()=>w.value.map(t=>t.cards?.length||0).join(","),()=>{ut()});let De=null;function sl(t){if(De)return;De=setTimeout(()=>{De=null},16);const e=t.target;if(e.closest(".native-scroll")){const n=e.closest(".native-scroll"),{scrollTop:h,scrollHeight:m,clientHeight:P}=n,E=h<m-P-1,B=h>1;(t.deltaY>0&&!E||t.deltaY<0&&!B)&&(t.preventDefault(),requestAnimationFrame(()=>{le.value&&(le.value.scrollLeft+=t.deltaY*.8)}))}else t.preventDefault(),requestAnimationFrame(()=>{le.value&&(le.value.scrollLeft+=t.deltaY*.8)})}function nl(){He(()=>{document.querySelectorAll(".native-scroll").forEach(e=>{e.addEventListener("wheel",rl,{passive:!1})})})}function rl(t){const e=t.currentTarget,{scrollTop:o,scrollHeight:n,clientHeight:h}=e,m=o<n-h,P=o>0;(t.deltaY>0&&m||t.deltaY<0&&P)&&t.stopPropagation()}let Ue=null;function Ke(t){if(Ue)return;Ue=setTimeout(()=>{Ue=null},16),t.preventDefault();const e=document.querySelector(".result-scroll-container");e&&requestAnimationFrame(()=>{e.style.scrollBehavior="auto",e.scrollLeft+=t.deltaY*.8,setTimeout(()=>{e.style.scrollBehavior="smooth"},50)})}tt(()=>{const t=document.querySelector(".result-scroll-container");t&&t.addEventListener("wheel",Ke,{passive:!1})});const il=re(()=>w.value.some(t=>t.active&&t.cards&&t.cards.length>0)),Qe=re(()=>{const t={};return M.value.forEach(e=>{t[e.poolId]||(t[e.poolId]=[]),t[e.poolId].push(e)}),t});function ul(t){const e=w.value.find(o=>o.id===t);return e?e.name.charAt(0):"?"}function dl(t){const e=["rgba(var(--el-color-primary-rgb), 0.7)","rgba(var(--el-color-success-rgb), 0.7)","rgba(var(--el-color-warning-rgb), 0.7)","rgba(var(--el-color-danger-rgb), 0.7)","rgba(var(--el-color-info-rgb), 0.7)"],o=t.split("").reduce((n,h)=>n+h.charCodeAt(0),0)%e.length;return{backgroundColor:e[o]}}function cl(t,e){const o=`${t}-${e}`;if(!S.has(o)){const n=["var(--el-color-primary)","var(--el-color-success)","var(--el-color-warning)","var(--el-color-danger)","#3498db","#9b59b6","#1abc9c","#e67e22","#e74c3c"],m=e.split("").reduce((E,B)=>E+B.charCodeAt(0),0)%n.length,P={backgroundColor:n[m]};S.set(o,P)}return S.get(o)}async function pl(t){try{await et.confirm("确定要删除这张卡片吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),d.value.splice(t,1),C({type:"success",message:"卡片已删除"})}catch(e){e!=="cancel"&&C({type:"error",message:"删除卡片失败："+(e.message||"未知错误")})}}async function dt(){if(H.value)try{if(D.value.some(e=>!e.name)){C.warning("有维度缺少名称，请补充完整");return}Ht();const t={...H.value,cards:d.value,dimensions:D.value};return await J.savePool(t),C.success("保存成功"),!0}catch(t){return console.error("保存失败:",t),C.error("保存失败: "+(t.message||"未知错误")),!1}}function fl(){const t=document.querySelector(".result-container");if(!t)return;let e=!1,o=0,n=0;const h=E=>{E.target.closest(".result-item")||(e=!0,t.classList.add("dragging"),o=E.pageX-t.offsetLeft,n=t.scrollLeft,t.style.scrollBehavior="auto",document.body.style.userSelect="none")},m=()=>{e&&(e=!1,t.classList.remove("dragging"),t.style.scrollBehavior="smooth",document.body.style.userSelect="")},P=E=>{if(!e)return;E.preventDefault();const j=(E.pageX-t.offsetLeft-o)*1.5,pe=n-j,ye=t.scrollWidth-t.clientWidth;t.scrollLeft=Math.max(0,Math.min(pe,ye))};return t.addEventListener("mousedown",h),document.addEventListener("mouseup",m),document.addEventListener("mouseleave",m),document.addEventListener("mousemove",P),()=>{t.removeEventListener("mousedown",h),document.removeEventListener("mouseup",m),document.removeEventListener("mouseleave",m),document.removeEventListener("mousemove",P)}}function Xe(){setTimeout(()=>{const t=document.querySelectorAll(".result-items");console.log("找到结果项容器:",t.length),t.forEach(e=>{e.getAttribute("data-scroll-enhanced")||(e.setAttribute("data-scroll-enhanced","true"),e.addEventListener("wheel",o=>{const{scrollTop:n,scrollHeight:h,clientHeight:m}=e,P=n+m>=h-5,E=n<=0;h>m&&(o.deltaY>0&&P||o.deltaY<0&&E||o.stopPropagation()),e.classList.add("scrolling"),e.scrollTimer&&clearTimeout(e.scrollTimer),e.scrollTimer=setTimeout(()=>{e.classList.remove("scrolling")},1e3)}),e.addEventListener("mouseenter",()=>{e.classList.add("hover")}),e.addEventListener("mouseleave",()=>{e.classList.remove("hover"),e.classList.remove("scrolling")}))})},500)}function vl(){const t=new MutationObserver(()=>{Xe()});return t.observe(document.body,{childList:!0,subtree:!0}),()=>{t.disconnect()}}function ml(t,e){if(e?.stopPropagation(),ie.value){clearTimeout(ie.value),ie.value=null,ue.value=!0,Dt(t,e),setTimeout(()=>{ue.value=!1},300);return}ue.value=!1,ie.value=setTimeout(()=>{if(!ue.value){const o=`${t.poolId}-${t.id}`;A[o]?delete A[o]:A[o]=!0}ie.value=null},250)}async function gl(){try{const t={};M.value.forEach(o=>{const n=qe(o.poolId);t[n]||(t[n]=[]),t[n].push(o)});let e=`抽卡结果：

`;for(const[o,n]of Object.entries(t))e+=`【${o}】
`,n.forEach((h,m)=>{if(e+=`${m+1}. ${h.title}
`,h.properties)for(const[P,E]of Object.entries(h.properties)){const B=at(h.poolId,P);let j="";Array.isArray(E)?j=E.join("、"):typeof E=="boolean"?j=E?"是":"否":j=E,e+=`   ${B}: ${j}
`}e+=`
`}),e+=`------------------------

`;await window.pywebview.api.copy_to_clipboard(e),C({message:"抽卡结果已复制到剪贴板",type:"success",duration:2e3})}catch(t){console.error("复制失败:",t),C.error("复制失败，请检查浏览器权限")}}function yl(){_=!0;const t=document.querySelector(".detail-scrollbar");t&&(t.classList.add("user-hovering"),t.classList.remove("auto-scrolling"))}function hl(){_=!1;const t=document.querySelector(".detail-scrollbar");t&&(t.classList.remove("user-hovering"),X.value&&t.classList.add("auto-scrolling"))}Ct(()=>{ae&&(clearInterval(ae),ae=null),De&&(clearTimeout(De),De=null),Ue&&(clearTimeout(Ue),Ue=null),g.forEach(t=>{typeof t=="function"&&t()}),g=[]});function bl(t){const e=document.querySelector(".card-detail-dialog .el-dialog");e?(e.style.animation="dialogSlideOut 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)",setTimeout(()=>{t()},400)):t()}return Le(U,t=>{!t&&ae&&(clearInterval(ae),ae=null)}),(t,e)=>{const o=Ye,n=Ge,h=Dl,m=Ul,P=Nl,E=Fl,B=Bl,j=ql,pe=Jl,ye=Rl,xe=jl,ne=Kl,ct=Wl,_l=Gl,pt=Xl,wl=Ql,Ce=lo,ft=to,vt=oo,mt=ao,gt=so,kl=no,xl=Tl;return u(),y("div",zo,[s("div",Ho,[e[29]||(e[29]=s("h2",null,"自定义卡池",-1)),s("div",Yo,[a(n,{type:"primary",onClick:Ft,size:"small"},{default:r(()=>[a(o,null,{default:r(()=>[a(b(ze))]),_:1}),e[24]||(e[24]=x(" 新建卡池 "))]),_:1}),a(n,{type:"warning",onClick:Wt,size:"small"},{default:r(()=>[a(o,null,{default:r(()=>[a(b(lt))]),_:1}),e[25]||(e[25]=x(" 导入卡池 "))]),_:1}),a(n,{type:"success",onClick:Ot,disabled:!il.value,size:"small",class:"draw-button-top"},{default:r(()=>[a(o,null,{default:r(()=>[a(b(ht))]),_:1}),e[26]||(e[26]=x(" 抽取 "))]),_:1},8,["disabled"]),a(n,{type:"primary",onClick:gl,disabled:M.value.length===0,size:"small",plain:""},{default:r(()=>[a(o,null,{default:r(()=>[a(b(bt))]),_:1}),e[27]||(e[27]=x(" 复制结果 "))]),_:1},8,["disabled"]),a(n,{onClick:Lt,size:"small",type:"info",disabled:M.value.length===0},{default:r(()=>[a(o,null,{default:r(()=>[a(b(Pe))]),_:1}),e[28]||(e[28]=x(" 清空结果 "))]),_:1},8,["disabled"])])]),je((u(),y("div",Jo,[s("div",{class:"pools-container",ref_key:"poolsContainer",ref:Ne},[s("div",{class:"pools-wrapper",ref_key:"poolsWrapper",ref:le,onWheel:sl},[(u(!0),y(Q,null,ee(w.value,l=>(u(),y("div",{key:l.id,class:"pool-column"},[s("div",{class:Ee(["pool-header",{"pool-active":l.active}])},[s("div",qo,[s("span",{class:"pool-title",title:l.name},$(l.name),9,Ro),s("span",jo,$(l.cards?.length||0),1)]),s("div",Go,[a(h,{content:"激活/禁用卡池",placement:"top"},{default:r(()=>[s("div",{class:Ee(["pool-active-icon",{"is-active":l.active}]),onClick:i=>Mt(l.id,!l.active)},[a(o,null,{default:r(()=>[a(b(Ml))]),_:1})],10,Wo)]),_:2},1024),a(P,{modelValue:l.drawCount,"onUpdate:modelValue":i=>l.drawCount=i,size:"small",class:"draw-count-select",onChange:i=>Tt(l.id,l.drawCount),placeholder:"抽取数量"},{default:r(()=>[(u(!0),y(Q,null,ee(Math.min(10,l.cards?.length||1),i=>(u(),L(m,{key:i,label:i,value:i},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),a(h,{content:"导出卡池所有卡片",placement:"top"},{default:r(()=>[a(n,{type:"success",text:"",circle:"",size:"small",onClick:i=>me(l),class:"action-btn",disabled:!l.cards||l.cards.length===0},{default:r(()=>[a(o,null,{default:r(()=>[a(b(Ze))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024),a(n,{type:"primary",text:"",circle:"",size:"small",onClick:i=>Bt(l),class:"action-btn"},{default:r(()=>[a(o,null,{default:r(()=>[a(b(_t))]),_:1})]),_:2},1032,["onClick"])])],2),s("div",Ko,[s("div",Qo,[l.cards&&l.cards.length>0?(u(!0),y(Q,{key:0},ee(l.cards,i=>(u(),y("div",{key:i.id,class:Ee(["pool-item",{"item-selected":i.selected}]),onClick:T=>Et(l.id,i)},[a(E,{"model-value":i.selected,"onUpdate:modelValue":T=>Je(l.id,i,T),onClick:e[0]||(e[0]=Ol(()=>{},["stop"]))},null,8,["model-value","onUpdate:modelValue"]),s("span",Zo,$(i.title),1)],10,Xo))),128)):(u(),L(B,{key:1,description:"暂无内容","image-size":50}))])])]))),128)),w.value.length===0?(u(),L(B,{key:0,description:"暂无卡池，点击右上角按钮创建"})):G("",!0)],544)],512),s("div",ea,[s("div",ta,[s("div",la,[e[30]||(e[30]=s("div",{class:"tech-lines"},null,-1)),a(o,{class:"result-icon"},{default:r(()=>[a(b($t))]),_:1}),e[31]||(e[31]=s("h3",null,"抽卡结果",-1))]),s("div",oa,[be.value?(u(),y("div",aa,[s("span",sa,"已固定: "+$(fe.value)+"个",1),a(h,{content:"点击元素可固定/解除固定，固定的元素在随机时将被保留"},{default:r(()=>[a(o,{class:"info-icon"},{default:r(()=>[a(b(Re))]),_:1})]),_:1})])):G("",!0),M.value.length>0?(u(),L(n,{key:1,onClick:Me,size:"small",class:"export-results-btn"},{default:r(()=>[s("div",na,[a(o,{class:"export-icon"},{default:r(()=>[a(b(Ze))]),_:1}),e[32]||(e[32]=s("span",{class:"export-text"},"导出结果",-1)),s("span",ra,$(M.value.length),1)])]),_:1})):G("",!0),a(h,{placement:"top",effect:"light"},{content:r(()=>[s("div",ia,[s("div",null,[a(o,null,{default:r(()=>[a(b(Al))]),_:1}),e[33]||(e[33]=x()),e[34]||(e[34]=s("b",null,"点击",-1)),e[35]||(e[35]=x(": 固定/解除固定元素"))]),s("div",null,[a(o,null,{default:r(()=>[a(b(Il))]),_:1}),e[36]||(e[36]=x()),e[37]||(e[37]=s("b",null,"双击",-1)),e[38]||(e[38]=x(": 查看元素详情"))])])]),default:r(()=>[a(o,{class:"tips-icon",size:20},{default:r(()=>[a(b(Re))]),_:1})]),_:1})])]),s("div",ua,[s("div",da,[(u(!0),y(Q,null,ee(Qe.value,(l,i)=>(u(),y("div",{key:i,class:"result-group"},[s("div",ca,[s("div",{class:"group-icon",style:Te(dl(i))},$(ul(i)),5),s("div",pa,$(qe(i)),1),s("div",fa,$(l.length)+"个元素",1)]),s("div",va,[(u(!0),y(Q,null,ee(Vt(l,i),T=>(u(),y("div",{key:`${T.poolId}-${T.id}`,class:Ee(["result-item",{"fixed-element":A[`${T.poolId}-${T.id}`]}]),style:Te(cl(T.poolId,T.id)),onClick:he=>ml(T,he)},[A[`${T.poolId}-${T.id}`]?(u(),L(o,{key:0,class:"fixed-icon"},{default:r(()=>[a(b(zl))]),_:1})):G("",!0),x(" "+$(T.title),1)],14,ma))),128)),l.length>q.value&&!R.value.has(i)?(u(),y("div",{key:0,class:"show-more-btn",onClick:T=>ot(i,l.length)},[a(o,null,{default:r(()=>[a(b(Hl))]),_:1}),x(" 显示全部 "+$(l.length)+" 张卡片 ",1)],8,ga)):G("",!0),l.length>q.value&&R.value.has(i)?(u(),y("div",{key:1,class:"show-less-btn",onClick:T=>ot(i,l.length)},[a(o,null,{default:r(()=>[a(b(Yl))]),_:1}),e[39]||(e[39]=x(" 收起卡片 "))],8,ya)):G("",!0)])]))),128)),Object.keys(Qe.value).length===0?(u(),y("div",ha,[a(B,{description:"点击顶部「抽取」按钮开始抽卡","image-size":100},{image:r(()=>[a(o,{class:"empty-icon"},{default:r(()=>[a(b(ht))]),_:1})]),_:1})])):G("",!0)])])])])),[[xl,K.value]]),a(xe,{modelValue:U.value,"onUpdate:modelValue":e[3]||(e[3]=l=>U.value=l),width:"600px","close-on-click-modal":!0,"close-on-press-escape":!0,center:!0,"lock-scroll":!0,"show-close":!1,class:"card-detail-dialog tech-card enhanced-dialog","destroy-on-close":"","before-close":bl},{header:r(()=>[s("div",ba,[e[40]||(e[40]=s("div",{class:"tech-lines"},null,-1)),s("div",_a,[s("span",wa,$(qe(O.value?.poolId)),1),s("h3",null,$(O.value?.title),1)])])]),footer:r(()=>[s("div",Ea,[e[46]||(e[46]=s("div",{class:"tech-pulse"},null,-1)),s("div",La,[a(ye,{modelValue:X.value,"onUpdate:modelValue":e[1]||(e[1]=l=>X.value=l),"active-text":"自动滚动","inactive-text":"手动浏览",class:"auto-scroll-switch"},null,8,["modelValue"]),a(n,{type:"success",onClick:Fe,class:"tech-button",round:"",size:"small"},{default:r(()=>[a(o,null,{default:r(()=>[a(b(Ze))]),_:1}),e[44]||(e[44]=x(" 导出卡片 "))]),_:1}),a(n,{onClick:e[2]||(e[2]=l=>U.value=!1),class:"tech-button",round:""},{default:r(()=>e[45]||(e[45]=[x(" 关闭 ")])),_:1})])])]),default:r(()=>[O.value?(u(),y("div",{key:0,class:"detail-content-wrapper",onMouseenter:yl,onMouseleave:hl},[a(pe,{height:"420px",class:"detail-scrollbar"},{default:r(()=>[s("div",ka,[(u(!0),y(Q,null,ee(O.value.properties,(l,i)=>(u(),y("div",{key:i,class:"detail-section"},[s("h4",xa,$(at(O.value.poolId,i)),1),s("div",Ca,[typeof l=="boolean"?(u(),L(j,{key:0,size:"small",type:l?"success":"info"},{default:r(()=>[x($(l?"是":"否"),1)]),_:2},1032,["type"])):Array.isArray(l)?(u(),y("ul",$a,[(u(!0),y(Q,null,ee(l,(T,he)=>(u(),y("li",{key:he},[e[41]||(e[41]=s("div",{class:"item-bullet"},null,-1)),s("span",null,$(T),1)]))),128))])):Nt(l)?(u(),y("div",Va,[s("div",{class:"color-preview",style:Te({backgroundColor:l})},null,4),s("span",Sa,$(l),1)])):(u(),y("p",Pa,$(l),1))])]))),128)),e[42]||(e[42]=s("div",{class:"tech-decoration"},[s("svg",{viewBox:"0 0 100 100",class:"corner-decoration top-left"},[s("path",{d:"M0,0 L40,0 C30,0 20,0 20,20 L20,40 L0,40 Z"}),s("path",{d:"M0,15 L15,15"}),s("path",{d:"M15,0 L15,15"})]),s("svg",{viewBox:"0 0 100 100",class:"corner-decoration top-right"},[s("path",{d:"M0,0 L40,0 C30,0 20,0 20,20 L20,40 L0,40 Z"}),s("path",{d:"M0,15 L15,15"}),s("path",{d:"M15,0 L15,15"})]),s("svg",{viewBox:"0 0 100 100",class:"corner-decoration bottom-left"},[s("path",{d:"M0,0 L40,0 C30,0 20,0 20,20 L20,40 L0,40 Z"}),s("path",{d:"M0,15 L15,15"}),s("path",{d:"M15,0 L15,15"})]),s("svg",{viewBox:"0 0 100 100",class:"corner-decoration bottom-right"},[s("path",{d:"M0,0 L40,0 C30,0 20,0 20,20 L20,40 L0,40 Z"}),s("path",{d:"M0,15 L15,15"}),s("path",{d:"M15,0 L15,15"})])],-1))]),e[43]||(e[43]=s("div",{class:"auto-scroll-indicator"},null,-1))]),_:1})],32)):G("",!0)]),_:1},8,["modelValue"]),a(xe,{modelValue:te.value,"onUpdate:modelValue":e[8]||(e[8]=l=>te.value=l),title:de.value?"新建卡池":"编辑卡池",width:"500px","destroy-on-close":!0,"lock-scroll":!0,class:"pool-edit-dialog",onOpen:Ve,onClosed:Se},{footer:r(()=>[s("div",Da,[a(n,{onClick:e[6]||(e[6]=l=>te.value=!1),plain:""},{default:r(()=>e[47]||(e[47]=[x("取消")])),_:1}),a(n,{type:"danger",onClick:e[7]||(e[7]=l=>At(z.value)),class:"delete-pool-btn"},{default:r(()=>[a(o,null,{default:r(()=>[a(b(Pe))]),_:1}),e[48]||(e[48]=x(" 删除卡池 "))]),_:1}),a(n,{type:"primary",onClick:st,disabled:!z.value?.name},{default:r(()=>e[49]||(e[49]=[x(" 保存 ")])),_:1},8,["disabled"])])]),default:r(()=>[s("div",{class:Ee(["dialog-header-line",{"new-pool":de.value}])},null,2),s("div",Ta,[a(_l,{model:z.value,"label-position":"top",class:"edit-form"},{default:r(()=>[s("div",Ma,[a(ct,{label:"卡池名称",class:"required-field"},{default:r(()=>[a(ne,{modelValue:z.value.name,"onUpdate:modelValue":e[4]||(e[4]=l=>z.value.name=l),placeholder:"请输入卡池名称",maxlength:"50","show-word-limit":"",autofocus:""},null,8,["modelValue"])]),_:1}),a(ct,{label:"卡池描述"},{default:r(()=>[a(ne,{modelValue:z.value.description,"onUpdate:modelValue":e[5]||(e[5]=l=>z.value.description=l),placeholder:"请简要描述这个卡池的用途和内容",type:"textarea",rows:4,maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})])]),_:1},8,["model"])])]),_:1},8,["modelValue","title"]),a(xe,{modelValue:_e.value,"onUpdate:modelValue":e[12]||(e[12]=l=>_e.value=l),title:`管理卡池: ${H.value?.name||""}`,width:"80%","destroy-on-close":!1,"lock-scroll":!0,class:"entity-manager-dialog",onOpen:Ve,onClosed:Se},{default:r(()=>[e[57]||(e[57]=s("div",{class:"manager-header-line"},null,-1)),s("div",Ua,[s("div",Na,[a(wl,{modelValue:oe.value,"onUpdate:modelValue":e[9]||(e[9]=l=>oe.value=l),class:"manager-tabs"},{default:r(()=>[a(pt,{label:"卡片管理",name:"cards"},{label:r(()=>[a(o,{class:"tab-icon"},{default:r(()=>[a(b(lt))]),_:1}),e[50]||(e[50]=x(" 卡片管理 "))]),_:1}),a(pt,{label:"维度管理",name:"dimensions"},{label:r(()=>[a(o,{class:"tab-icon"},{default:r(()=>[a(b(Zl))]),_:1}),e[51]||(e[51]=x(" 维度管理 "))]),_:1})]),_:1},8,["modelValue"]),s("div",Oa,[a(n,{type:"primary",onClick:zt},{default:r(()=>[a(o,null,{default:r(()=>[a(b(ze))]),_:1}),x(" "+$(oe.value==="cards"?"添加卡片":"添加维度"),1)]),_:1}),a(n,{type:"success",onClick:dt},{default:r(()=>[a(o,null,{default:r(()=>[a(b(eo))]),_:1}),e[52]||(e[52]=x(" 保存修改 "))]),_:1}),a(n,{onClick:Gt},{default:r(()=>[a(o,null,{default:r(()=>[a(b(bt))]),_:1}),e[53]||(e[53]=x(" 导出配置 "))]),_:1})])]),oe.value==="cards"?(u(),y("div",Fa,[a(ft,{data:d.value,style:{width:"100%"},"max-height":"450px",border:""},{default:r(()=>[a(Ce,{label:"标题","min-width":"150"},{default:r(({row:l})=>[s("span",Ba,$(l.title),1)]),_:1}),(u(!0),y(Q,null,ee(D.value,l=>(u(),L(Ce,{key:l.id,label:l.name,"min-width":120},{default:r(({row:i})=>[i.properties&&i.properties[l.id]!==void 0?(u(),y(Q,{key:0},[l.type==="boolean"?(u(),L(j,{key:0,size:"small",type:i.properties[l.id]?"success":"info"},{default:r(()=>[x($(i.properties[l.id]?"是":"否"),1)]),_:2},1032,["type"])):l.type==="array"&&Array.isArray(i.properties[l.id])?(u(),y("span",Aa,$(i.properties[l.id].join(", ")),1)):l.type==="color"?(u(),y("div",{key:2,class:"color-preview",style:Te({backgroundColor:i.properties[l.id]})},null,4)):(u(),y("span",Ia,$(i.properties[l.id]),1))],64)):(u(),y("span",za,"-"))]),_:2},1032,["label"]))),128)),a(Ce,{label:"操作",width:"100",fixed:"right"},{default:r(({$index:l})=>[a(n,{type:"primary",onClick:i=>Kt(d.value[l]),size:"small",circle:""},{default:r(()=>[a(o,null,{default:r(()=>[a(b(_t))]),_:1})]),_:2},1032,["onClick"]),a(n,{type:"danger",onClick:i=>pl(l),size:"small",circle:""},{default:r(()=>[a(o,null,{default:r(()=>[a(b(Pe))]),_:1})]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),d.value.length===0?(u(),L(B,{key:0,description:"请添加卡片"})):G("",!0)])):G("",!0),oe.value==="dimensions"?(u(),y("div",Ha,[a(ft,{data:D.value,style:{width:"100%"},"max-height":"450px",border:""},{default:r(()=>[a(Ce,{label:"名称",width:"180"},{default:r(({row:l})=>[a(ne,{modelValue:l.name,"onUpdate:modelValue":i=>l.name=i,placeholder:"输入维度名称",size:"default"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(Ce,{label:"描述"},{default:r(({row:l})=>[a(ne,{modelValue:l.description,"onUpdate:modelValue":i=>l.description=i,type:"textarea",placeholder:"输入维度描述",rows:2,size:"default"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(Ce,{label:"类型",width:"150"},{default:r(({row:l})=>[a(P,{modelValue:l.type,"onUpdate:modelValue":i=>l.type=i,placeholder:"选择类型",size:"default"},{default:r(()=>[a(m,{label:"文本",value:"text"}),a(m,{label:"数字",value:"number"}),a(m,{label:"选项",value:"select"}),a(m,{label:"数组",value:"array"}),a(m,{label:"布尔",value:"boolean"}),a(m,{label:"日期",value:"date"}),a(m,{label:"颜色",value:"color"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),a(Ce,{label:"选项值",width:"180"},{default:r(({row:l})=>[a(n,{size:"default",disabled:l.type!=="select",onClick:i=>Jt(l)},{default:r(()=>[x(" 编辑选项 ("+$(l.options?.length||0)+") ",1)]),_:2},1032,["disabled","onClick"])]),_:1}),a(Ce,{label:"操作",width:"80",fixed:"right"},{default:r(({$index:l})=>[a(n,{type:"danger",onClick:i=>Yt(l),size:"small",circle:""},{default:r(()=>[a(o,null,{default:r(()=>[a(b(Pe))]),_:1})]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),D.value.length===0?(u(),L(B,{key:0,description:"请添加维度"})):G("",!0)])):G("",!0)]),a(xe,{modelValue:ve.value,"onUpdate:modelValue":e[11]||(e[11]=l=>ve.value=l),title:"编辑选项",width:"500px","append-to-body":"","lock-scroll":!0,onOpen:Ve,onClosed:Se},{footer:r(()=>[s("div",Ja,[a(n,{onClick:e[10]||(e[10]=l=>ve.value=!1)},{default:r(()=>e[55]||(e[55]=[x("取消")])),_:1}),a(n,{type:"primary",onClick:jt},{default:r(()=>e[56]||(e[56]=[x("确定")])),_:1})])]),default:r(()=>[s("div",Ya,[(u(!0),y(Q,null,ee(p.value,(l,i)=>(u(),y("div",{key:i,class:"option-item"},[a(ne,{modelValue:p.value[i],"onUpdate:modelValue":T=>p.value[i]=T,placeholder:"输入选项值",size:"default"},null,8,["modelValue","onUpdate:modelValue"]),a(n,{type:"danger",onClick:T=>Rt(i),size:"small",circle:""},{default:r(()=>[a(o,null,{default:r(()=>[a(b(Pe))]),_:1})]),_:2},1032,["onClick"])]))),128)),a(n,{type:"primary",onClick:qt,size:"default"},{default:r(()=>[a(o,null,{default:r(()=>[a(b(ze))]),_:1}),e[54]||(e[54]=x(" 添加选项 "))]),_:1})])]),_:1},8,["modelValue"])]),_:1},8,["modelValue","title"]),a(xe,{modelValue:v.value,"onUpdate:modelValue":e[15]||(e[15]=l=>v.value=l),title:"编辑卡片属性",width:"600px","append-to-body":"","lock-scroll":!0,onOpen:Ve,onClosed:Se},{footer:r(()=>[s("div",es,[a(n,{onClick:e[14]||(e[14]=l=>v.value=!1),plain:""},{default:r(()=>e[60]||(e[60]=[x("取消")])),_:1}),a(n,{type:"primary",onClick:Qt},{default:r(()=>e[61]||(e[61]=[x("保存")])),_:1})])]),default:r(()=>[s("div",qa,[s("div",Ra,[e[58]||(e[58]=s("div",{class:"section-header"},[s("h4",null,"卡片标题")],-1)),a(ne,{modelValue:V.value.title,"onUpdate:modelValue":e[13]||(e[13]=l=>V.value.title=l),placeholder:"请输入卡片标题",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),s("div",ja,[(u(!0),y(Q,null,ee(D.value,l=>(u(),y("div",{key:l.id,class:"property-item"},[s("div",Ga,[s("div",Wa,[s("span",Ka,$(l.name),1),s("span",Qa,$(it(l.type)),1),l.description?(u(),L(h,{key:0,content:l.description},{default:r(()=>[a(o,{class:"info-icon"},{default:r(()=>[a(b(Re))]),_:1})]),_:2},1032,["content"])):G("",!0)])]),s("div",Xa,[l.type==="text"?(u(),L(ne,{key:0,modelValue:k.value[l.id],"onUpdate:modelValue":i=>k.value[l.id]=i,type:"textarea",rows:3,placeholder:"请输入文本"},null,8,["modelValue","onUpdate:modelValue"])):l.type==="number"?(u(),L(vt,{key:1,modelValue:k.value[l.id],"onUpdate:modelValue":i=>k.value[l.id]=i,controls:!0,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])):l.type==="select"?(u(),L(P,{key:2,modelValue:k.value[l.id],"onUpdate:modelValue":i=>k.value[l.id]=i,placeholder:"请选择",style:{width:"100%"}},{default:r(()=>[(u(!0),y(Q,null,ee(l.options||[],i=>(u(),L(m,{key:i,label:i,value:i},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):l.type==="array"?(u(),y("div",Za,[(u(!0),y(Q,null,ee(k.value[l.id]||[],(i,T)=>(u(),y("div",{key:T,class:"array-item"},[a(ne,{modelValue:k.value[l.id][T],"onUpdate:modelValue":he=>k.value[l.id][T]=he,placeholder:"数组项"},null,8,["modelValue","onUpdate:modelValue"]),a(n,{type:"danger",onClick:he=>rt(l.id,T,"edit"),size:"small",circle:""},{default:r(()=>[a(o,null,{default:r(()=>[a(b(Pe))]),_:1})]),_:2},1032,["onClick"])]))),128)),a(n,{type:"primary",onClick:i=>nt(l.id,"edit"),size:"small"},{default:r(()=>[a(o,null,{default:r(()=>[a(b(ze))]),_:1}),e[59]||(e[59]=x(" 添加项目 "))]),_:2},1032,["onClick"])])):l.type==="boolean"?(u(),L(ye,{key:4,modelValue:k.value[l.id],"onUpdate:modelValue":i=>k.value[l.id]=i,"active-text":"是","inactive-text":"否"},null,8,["modelValue","onUpdate:modelValue"])):l.type==="date"?(u(),L(mt,{key:5,modelValue:k.value[l.id],"onUpdate:modelValue":i=>k.value[l.id]=i,type:"date",placeholder:"选择日期",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])):l.type==="color"?(u(),L(gt,{key:6,modelValue:k.value[l.id],"onUpdate:modelValue":i=>k.value[l.id]=i,"show-alpha":""},null,8,["modelValue","onUpdate:modelValue"])):(u(),L(ne,{key:7,modelValue:k.value[l.id],"onUpdate:modelValue":i=>k.value[l.id]=i,placeholder:"请输入值"},null,8,["modelValue","onUpdate:modelValue"]))])]))),128))]),D.value.length===0?(u(),L(B,{key:0,description:"暂无可编辑的属性"})):G("",!0)])]),_:1},8,["modelValue"]),a(xe,{modelValue:Be.value,"onUpdate:modelValue":e[18]||(e[18]=l=>Be.value=l),title:"添加新卡片",width:"600px","lock-scroll":!0,"destroy-on-close":!1,class:"add-card-dialog",onOpen:Ve,onClosed:Se},{footer:r(()=>[s("div",us,[a(n,{onClick:e[17]||(e[17]=l=>Be.value=!1),plain:""},{default:r(()=>e[65]||(e[65]=[x("取消")])),_:1}),a(n,{type:"primary",onClick:el},{default:r(()=>e[66]||(e[66]=[x("保存")])),_:1})])]),default:r(()=>[e[67]||(e[67]=s("div",{class:"add-card-header-line"},null,-1)),s("div",ts,[s("div",ls,[e[62]||(e[62]=s("div",{class:"section-header"},[s("h4",null,"卡片标题")],-1)),a(ne,{modelValue:F.value.title,"onUpdate:modelValue":e[16]||(e[16]=l=>F.value.title=l),placeholder:"请输入卡片标题",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),s("div",os,[e[64]||(e[64]=s("div",{class:"section-header"},[s("h4",null,"卡片属性")],-1)),D.value.length===0?(u(),L(B,{key:0,description:"请先添加维度"})):G("",!0),D.value.length>0?(u(),y("div",as,[(u(!0),y(Q,null,ee(D.value,l=>(u(),y("div",{key:l.id,class:"property-input-group"},[s("div",ss,[s("span",null,$(l.name),1),l.description?(u(),L(h,{key:0,content:l.description},{default:r(()=>[a(o,{class:"info-icon"},{default:r(()=>[a(b(Re))]),_:1})]),_:2},1032,["content"])):G("",!0),s("span",ns,$(it(l.type)),1)]),s("div",rs,[l.type==="text"?(u(),L(ne,{key:0,modelValue:F.value.properties[l.id],"onUpdate:modelValue":i=>F.value.properties[l.id]=i,placeholder:"请输入文本",type:"textarea",rows:2},null,8,["modelValue","onUpdate:modelValue"])):l.type==="number"?(u(),L(vt,{key:1,modelValue:F.value.properties[l.id],"onUpdate:modelValue":i=>F.value.properties[l.id]=i,controls:!0,precision:2,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])):l.type==="select"?(u(),L(P,{key:2,modelValue:F.value.properties[l.id],"onUpdate:modelValue":i=>F.value.properties[l.id]=i,placeholder:"请选择",style:{width:"100%"}},{default:r(()=>[(u(!0),y(Q,null,ee(l.options||[],i=>(u(),L(m,{key:i,label:i,value:i},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):l.type==="array"?(u(),y("div",is,[(u(!0),y(Q,null,ee(F.value.properties[l.id]||[],(i,T)=>(u(),y("div",{key:T,class:"array-item"},[a(ne,{modelValue:F.value.properties[l.id][T],"onUpdate:modelValue":he=>F.value.properties[l.id][T]=he,placeholder:"数组项"},null,8,["modelValue","onUpdate:modelValue"]),a(n,{type:"danger",onClick:he=>rt(l.id,T,"new"),size:"small",circle:""},{default:r(()=>[a(o,null,{default:r(()=>[a(b(Pe))]),_:1})]),_:2},1032,["onClick"])]))),128)),a(n,{type:"primary",onClick:i=>nt(l.id,"new"),size:"small"},{default:r(()=>[a(o,null,{default:r(()=>[a(b(ze))]),_:1}),e[63]||(e[63]=x(" 添加项目 "))]),_:2},1032,["onClick"])])):l.type==="boolean"?(u(),L(ye,{key:4,modelValue:F.value.properties[l.id],"onUpdate:modelValue":i=>F.value.properties[l.id]=i,"active-text":"是","inactive-text":"否"},null,8,["modelValue","onUpdate:modelValue"])):l.type==="date"?(u(),L(mt,{key:5,modelValue:F.value.properties[l.id],"onUpdate:modelValue":i=>F.value.properties[l.id]=i,type:"date",placeholder:"选择日期",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])):l.type==="color"?(u(),L(gt,{key:6,modelValue:F.value.properties[l.id],"onUpdate:modelValue":i=>F.value.properties[l.id]=i,"show-alpha":""},null,8,["modelValue","onUpdate:modelValue"])):(u(),L(ne,{key:7,modelValue:F.value.properties[l.id],"onUpdate:modelValue":i=>F.value.properties[l.id]=i,placeholder:"请输入值"},null,8,["modelValue","onUpdate:modelValue"]))])]))),128))])):G("",!0)])])]),_:1},8,["modelValue"]),a(xe,{modelValue:Ae.value,"onUpdate:modelValue":e[21]||(e[21]=l=>Ae.value=l),title:"导入卡池配置",width:"600px","lock-scroll":!0,"destroy-on-close":!1,onOpen:Ve,onClosed:Se},{footer:r(()=>[s("div",ps,[a(n,{onClick:e[20]||(e[20]=l=>Ae.value=!1),plain:""},{default:r(()=>e[69]||(e[69]=[x("取消")])),_:1}),a(n,{type:"primary",onClick:ol,loading:ke.value},{default:r(()=>e[70]||(e[70]=[x(" 导入 ")])),_:1},8,["loading"])])]),default:r(()=>[s("div",ds,[e[68]||(e[68]=s("p",{class:"import-tip"},"请将卡池配置的JSON字符串粘贴到下方文本框中:",-1)),a(ne,{modelValue:Ie.value,"onUpdate:modelValue":e[19]||(e[19]=l=>Ie.value=l),type:"textarea",rows:10,placeholder:"粘贴JSON配置文本...",resize:"none"},null,8,["modelValue"]),ge.value?(u(),y("div",cs,[a(kl,{title:ge.value,type:"error","show-icon":"",closable:!1},null,8,["title"])])):G("",!0)])]),_:1},8,["modelValue"]),a(xe,{modelValue:I.value,"onUpdate:modelValue":e[23]||(e[23]=l=>I.value=l),title:Oe.value,width:"90%","destroy-on-close":!0,"lock-scroll":!0,class:"export-dialog",onOpen:Ve,onClosed:Se},{default:r(()=>[I.value?(u(),L(Io,{key:0,cards:Y.value,"pool-name":se.value,"pool-info":we.value,dimensions:ce.value,onExportSuccess:$e,onClose:e[22]||(e[22]=l=>I.value=!1)},null,8,["cards","pool-name","pool-info","dimensions"])):G("",!0)]),_:1},8,["modelValue","title"])])}}},Es=xt(fs,[["__scopeId","data-v-3ebb4314"]]);export{Es as default};
