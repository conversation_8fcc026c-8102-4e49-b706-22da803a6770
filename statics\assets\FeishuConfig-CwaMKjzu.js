import{_ as w,bh as d,r as _,o as F,b as C,m as b,e as i,d as t,g as s,q as E,s as x,t as I,v as f,j as A,E as u}from"./entry-BIjVVog3.js";/* empty css                *//* empty css                 */const B={class:"feishu-config"},L={class:"settings-section"},S={class:"panel-content"},U={class:"settings-card"},N={__name:"FeishuConfig",setup(j){const p=d("configStore"),m=d("showLoading"),v=d("hideLoading"),o=_({app_id:"",app_secret:"",encrypt_key:"",verification_token:""}),h=_(),y=async()=>{try{m("正在保存飞书配置..."),await p.updateConfigItem("feishu",o.value),u.success("飞书配置保存成功")}catch(l){console.error("保存飞书配置失败:",l),u.error("保存失败: "+l.message)}finally{v()}},g=()=>{o.value={app_id:"",app_secret:"",encrypt_key:"",verification_token:""},u.success("飞书配置已重置")},k=async()=>{try{p.feishu&&(o.value={...p.feishu})}catch(l){console.error("加载飞书配置失败:",l)}};return F(async()=>{await k()}),(l,e)=>{const r=x,n=E,c=I,V=A;return b(),C("div",B,[i("div",L,[e[6]||(e[6]=i("div",{class:"section-header"},[i("h2",{class:"section-title"},"飞书配置")],-1)),i("div",S,[i("div",U,[t(V,{ref_key:"feishuFormRef",ref:h,model:o.value,"label-width":"150px"},{default:s(()=>[t(n,{label:"App ID",prop:"app_id"},{default:s(()=>[t(r,{modelValue:o.value.app_id,"onUpdate:modelValue":e[0]||(e[0]=a=>o.value.app_id=a),placeholder:"请输入飞书应用的 App ID","show-password":""},null,8,["modelValue"])]),_:1}),t(n,{label:"App Secret",prop:"app_secret"},{default:s(()=>[t(r,{modelValue:o.value.app_secret,"onUpdate:modelValue":e[1]||(e[1]=a=>o.value.app_secret=a),placeholder:"请输入飞书应用的 App Secret","show-password":""},null,8,["modelValue"])]),_:1}),t(n,{label:"Encrypt Key",prop:"encrypt_key"},{default:s(()=>[t(r,{modelValue:o.value.encrypt_key,"onUpdate:modelValue":e[2]||(e[2]=a=>o.value.encrypt_key=a),placeholder:"请输入加密密钥（可选）","show-password":""},null,8,["modelValue"])]),_:1}),t(n,{label:"Verification Token",prop:"verification_token"},{default:s(()=>[t(r,{modelValue:o.value.verification_token,"onUpdate:modelValue":e[3]||(e[3]=a=>o.value.verification_token=a),placeholder:"请输入验证令牌（可选）","show-password":""},null,8,["modelValue"])]),_:1}),t(n,null,{default:s(()=>[t(c,{type:"primary",onClick:y},{default:s(()=>e[4]||(e[4]=[f("保存配置")])),_:1}),t(c,{onClick:g},{default:s(()=>e[5]||(e[5]=[f("重置")])),_:1})]),_:1})]),_:1},8,["model"])])])])])}}},T=w(N,[["__scopeId","data-v-c9a741f7"]]);export{T as default};
