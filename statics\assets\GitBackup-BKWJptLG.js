import{_ as ye,bh as R,r as p,c as ee,o as be,b as U,m as b,e as l,p as F,d as s,g as o,v as r,C as g,ab as he,t as Ge,aC as Ve,aa as ae,bD as Ce,n as z,F as w,bI as Ue,eh as xe,V as te,$ as se,af as De,ad as Te,at as Be,ej as Ne,bF as Ee,j as Ie,q as $e,s as Le,bc as Se,bd as ze,X as le,ao as oe,ee as He,y as Me,ar as Oe,B as qe,as as Je,ak as Re,al as Fe,k as je,E as u,av as j}from"./entry-BIjVVog3.js";/* empty css                   *//* empty css                *//* empty css               *//* empty css                  *//* empty css                    *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                */const Pe={class:"git-backup-management"},Ae={class:"action-bar"},Ze={class:"action-right"},Xe={class:"status-cards"},Ke={class:"status-item"},Qe={class:"status-content"},We={class:"status-value"},Ye={class:"status-item"},ea={class:"status-content"},aa={class:"status-value"},ta={class:"status-item"},sa={class:"status-content"},la={class:"status-value"},oa={key:0,class:"status-sub"},ia={class:"status-item"},na={class:"status-content"},ra={class:"status-value"},ua={key:0,class:"progress-section"},da={class:"progress-header"},ca={class:"progress-message"},pa={class:"history-section"},va={class:"card-header"},ma={class:"card-actions"},fa={class:"table-container"},ga={class:"commit-cell"},_a={class:"commit-message"},wa={class:"commit-hash"},ka={class:"author-text"},ya={class:"action-buttons"},ba={class:"path-input-group"},ha={class:"path-input-group"},Ga={class:"path-input-group"},Va={class:"backup-options"},Ca={class:"dialog-footer"},Ua={class:"dialog-footer"},xa={class:"commit-details"},Da={class:"diff-content"},Ta={__name:"GitBackup",setup(Ba){const f=R("configStore"),x=R("showLoading"),D=R("hideLoading"),T=p(!1),B=p(!1),H=p(!1),M=p(!1),I=p(!1),$=p(!1),O=p(!1),L=p(""),N=p(null),S=p(!1),h=p([]),V=p(null),t=p({repoUrl:"",authType:"token",username:"",password:"",token:"",tokenUsername:"",backupDir:"",autoBackup:!1,backupInterval:60}),d=p({message:"",tagName:"",force:!1}),v=p({visible:!1,percent:0,message:"",status:"normal"}),q=ee(()=>{const a=t.value;return!a.repoUrl||!a.backupDir?!1:a.authType==="password"?a.username&&a.password:a.token&&a.tokenUsername}),P=ee(()=>{const a=t.value;return a.repoUrl?a.authType==="password"?a.username&&a.password:a.token&&a.tokenUsername:!1}),ie=async()=>{try{f.git&&(t.value={...f.git})}catch(a){console.error("加载Git配置失败:",a)}},ne=async()=>{try{await f.updateConfigItem("git.repoUrl",t.value.repoUrl),await f.updateConfigItem("git.authType",t.value.authType),await f.updateConfigItem("git.username",t.value.username),await f.updateConfigItem("git.password",t.value.password),await f.updateConfigItem("git.token",t.value.token),await f.updateConfigItem("git.tokenUsername",t.value.tokenUsername),await f.updateConfigItem("git.backupDir",t.value.backupDir),await f.updateConfigItem("git.autoBackup",t.value.autoBackup),await f.updateConfigItem("git.backupInterval",t.value.backupInterval),T.value=!1,u.success("Git配置保存成功"),await E()}catch(a){console.error("保存Git配置失败:",a),u.error("保存Git配置失败："+a.message)}},A=async()=>{try{M.value=!0;const a=await window.pywebview.api.check_git_installation(),e=typeof a=="string"?JSON.parse(a):a;if(e.status==="success")N.value=e.data,S.value=e.data.installed,e.data.installed?u.success(`Git已安装: ${e.data.version}`):u.warning("未检测到Git安装");else throw new Error(e.message||"Git检测失败")}catch(a){console.error("检测Git失败:",a),u.error("检测Git失败："+a.message),N.value={installed:!1},S.value=!1}finally{M.value=!1}},re=async()=>{try{const a=await window.pywebview.api.select_directory(),e=typeof a=="string"?JSON.parse(a):a;e&&e.status==="success"&&e.data&&(t.value.backupDir=e.data)}catch(a){console.error("选择备份目录失败:",a),u.error("选择目录失败: "+a.message)}},Z=async()=>{try{x("正在测试Git连接...");const a={repo_url:t.value.repoUrl,auth_type:t.value.authType,username:t.value.username,password:t.value.password,token:t.value.token,token_username:t.value.tokenUsername},e=await window.pywebview.api.test_git_credentials(a),i=typeof e=="string"?JSON.parse(e):e;if(i.status==="success")u.success("Git连接测试成功");else throw new Error(i.message||"Git连接测试失败")}catch(a){console.error("Git连接测试失败:",a),u.error("Git连接测试失败："+a.message)}finally{D()}},ue=async()=>{try{x("正在检查Git仓库状态...");const a={backup_dir:t.value.backupDir},e=await window.pywebview.api.check_git_repository_status(a),i=typeof e=="string"?JSON.parse(e):e;if(D(),i.status==="success"&&i.data?.is_git_repo){const G=i.data;let _=`此目录已经是Git仓库！

📁 仓库路径: ${t.value.backupDir}
🌿 当前分支: ${G.current_branch||"未知"}
📊 提交数量: ${G.commit_count||0} 个
🔗 远程仓库: ${G.remote_url||"未配置"}

是否要重新配置远程仓库连接？`;await j.confirm(_,"仓库已存在",{confirmButtonText:"重新配置远程仓库",cancelButtonText:"取消",type:"info",dangerouslyUseHTMLString:!1}),x("正在重新配置远程仓库...")}else await j.confirm(`将在以下目录初始化Git仓库：

📁 目录: ${t.value.backupDir}
🔗 远程仓库: ${t.value.repoUrl}

⚠️ 注意事项：
• 现有配置文件将被保护，不会受到影响
• 将创建 .git 目录来管理版本控制
• 所有现有文件将被添加到首次提交中

确定要继续吗？`,"初始化Git仓库",{confirmButtonText:"确定初始化",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}),x("正在初始化Git仓库...");const c={repo_url:t.value.repoUrl,auth_type:t.value.authType,username:t.value.username,password:t.value.password,token:t.value.token,token_username:t.value.tokenUsername,backup_dir:t.value.backupDir,preserve_existing_files:!0},k=await window.pywebview.api.init_git_repository(c),y=typeof k=="string"?JSON.parse(k):k;if(y.status==="success")u.success("Git仓库配置成功"),await X(),await E();else throw new Error(y.message||"初始化失败")}catch(a){a!=="cancel"&&(console.error("初始化Git仓库失败:",a),u.error("Git仓库配置失败："+a.message))}finally{D()}},de=async()=>{try{$.value=!0,B.value=!1,v.value={visible:!0,percent:0,message:"正在准备Git备份...",status:"normal"};let a="";d.value.tagName&&d.value.tagName.trim()&&(a=d.value.tagName.trim().replace(/[^a-zA-Z0-9._-]/g,"").replace(/^[.-]/,"").replace(/[.-]$/,"").substring(0,50));const e={repo_url:t.value.repoUrl,auth_type:t.value.authType,username:t.value.username,password:t.value.password,token:t.value.token,token_username:t.value.tokenUsername,backup_dir:t.value.backupDir,commit_message:d.value.message,tag_name:a,force:d.value.force},i=await window.pywebview.api.backup_to_git(e),c=typeof i=="string"?JSON.parse(i):i;if(c.status==="success")v.value.percent=100,v.value.status="success",v.value.message="Git备份完成",u.success("Git备份成功"),d.value={message:"",tagName:"",force:!1},setTimeout(async()=>{v.value.visible=!1,await E()},2e3);else throw new Error(c.message||"Git备份失败")}catch(a){console.error("Git备份失败:",a),u.error("Git备份失败："+a.message),v.value.status="exception",v.value.message="Git备份失败："+a.message,setTimeout(()=>{v.value.visible=!1},3e3)}finally{$.value=!1}},X=async()=>{if(!t.value.backupDir){V.value=null;return}try{const a={backup_dir:t.value.backupDir},e=await window.pywebview.api.check_git_repository_status(a),i=typeof e=="string"?JSON.parse(e):e;i.status==="success"?V.value=i.data:V.value=null}catch(a){console.error("检查Git仓库状态失败:",a),V.value=null}},E=async()=>{if(!t.value.backupDir){h.value=[];return}try{I.value=!0;const a={repo_url:t.value.repoUrl,local_path:t.value.backupDir,count:50},e=await window.pywebview.api.get_git_history(a),i=typeof e=="string"?JSON.parse(e):e;i.status==="success"?h.value=(i.history||[]).map(c=>({...c,hash:c.hash.replace(/"/g,"").trim(),message:c.message.replace(/"/g,"").trim()})):(h.value=[],i.message&&!i.message.includes("仓库未初始化")&&u.error("获取Git历史失败："+i.message))}catch(a){console.error("获取Git历史出错:",a),h.value=[],u.error("获取Git历史失败："+(a.message||a.toString()))}finally{I.value=!1}},ce=async a=>{try{H.value=!0,O.value=!0,L.value="加载中...";const e=a.hash.replace(/"/g,"").trim(),i=await window.pywebview.api.get_git_commit_details({commit_hash:e,repo_path:t.value.backupDir});if(i.status==="success")L.value=i.diff||"此提交没有变更";else throw new Error(i.message||"获取提交详情失败")}catch(e){L.value=`获取提交详情失败: ${e.message}`,u.error(`获取提交详情失败: ${e.message}`)}finally{O.value=!1}},pe=async a=>{try{const e=a.hash.replace(/"/g,"").trim();await j.confirm(`确定要恢复到此版本吗？

提交: ${e.substring(0,7)} - ${a.message}
日期: ${a.date}
作者: ${a.author}

⚠️ 警告：此操作会丢失当前未提交的更改！
应用将在恢复完成后自动重启。`,"恢复确认",{confirmButtonText:"确认恢复",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}),x("正在从Git恢复...");const i=await window.pywebview.api.reset_to_commit({backup_dir:t.value.backupDir,commit_hash:e}),c=typeof i=="string"?JSON.parse(i):i;if(c.status==="success"){D();let k=3;const y=()=>{k>0?(u({message:`恢复成功！应用将在 ${k} 秒后重启...`,type:"success",duration:1e3}),k--,setTimeout(y,1e3)):window.pywebview.api.restart_application().catch(G=>{u.error("重启应用失败，请手动重启"),console.error("重启失败:",G)})};y()}else throw new Error(c.message||"恢复失败")}catch(e){e!=="cancel"&&(console.error("Git恢复失败:",e),u.error("Git恢复失败："+e.message))}finally{D()}},ve=()=>{$.value=!1,v.value.visible=!1,u.info("Git备份已取消")},me=()=>{if(!d.value.tagName)return;const a=d.value.tagName.replace(/[^a-zA-Z0-9._-]/g,"").replace(/^[.-]/,"").replace(/[.-]$/,"").substring(0,50);a!==d.value.tagName&&(d.value.tagName=a)};return be(async()=>{try{await ie(),await A(),await X(),await E()}catch(a){console.error("组件初始化失败:",a)}}),(a,e)=>{const i=Ge,c=Ue,k=xe,y=Te,G=De,_=Le,m=$e,K=ze,fe=Se,ge=qe,_e=Oe,Q=Me,C=Fe,we=Re,W=Ie,J=je,Y=Ee;return b(),U("div",Pe,[l("div",Ae,[e[23]||(e[23]=l("div",{class:"action-left"},[l("h2",{class:"page-title"},"Git 备份管理"),l("p",{class:"page-description"},"配置和管理 Git 远程备份")],-1)),l("div",Ze,[s(i,{type:"primary",onClick:e[0]||(e[0]=n=>T.value=!0),icon:g(he)},{default:o(()=>e[19]||(e[19]=[r(" Git 配置 ")])),_:1},8,["icon"]),s(i,{type:"info",onClick:A,loading:M.value,icon:g(Ve)},{default:o(()=>e[20]||(e[20]=[r(" 检测 Git ")])),_:1},8,["loading","icon"]),s(i,{type:"warning",onClick:ue,disabled:!q.value||!S.value,icon:g(ae)},{default:o(()=>e[21]||(e[21]=[r(" 配置仓库 ")])),_:1},8,["disabled","icon"]),s(i,{type:"success",onClick:e[1]||(e[1]=n=>B.value=!0),disabled:!q.value||!S.value,loading:$.value,icon:g(Ce)},{default:o(()=>e[22]||(e[22]=[r(" 立即备份 ")])),_:1},8,["disabled","loading","icon"])])]),l("div",Xe,[s(c,{class:"status-card"},{default:o(()=>[l("div",Ke,[l("div",{class:z(["status-icon",N.value?.installed?"success":"error"])},e[24]||(e[24]=[l("svg",{class:"status-icon-svg",viewBox:"0 0 1024 1024"},[l("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"})],-1)]),2),l("div",Qe,[e[25]||(e[25]=l("div",{class:"status-title"},"Git 状态",-1)),l("div",We,w(N.value?.installed?`已安装 ${N.value.version}`:"未安装"),1)])])]),_:1}),s(c,{class:"status-card"},{default:o(()=>[l("div",Ye,[l("div",{class:z(["status-icon",t.value.repoUrl?"primary":"warning"])},e[26]||(e[26]=[l("svg",{class:"status-icon-svg",viewBox:"0 0 1024 1024"},[l("path",{d:"M574 665.4a8.03 8.03 0 0 0-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 0 0-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0L613.8 716.3a8.03 8.03 0 0 0 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 0 0 0 11.3L450 358.7a8.03 8.03 0 0 0 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.7a8.03 8.03 0 0 0 0 11.3l39.8 39.8a8.03 8.03 0 0 0 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 0 0-11.3 0L372.3 598.7a8.03 8.03 0 0 0 0 11.3l39.6 39.6a8.03 8.03 0 0 0 11.3 0l226.4-226.4a8.03 8.03 0 0 0 0-11.3l-39.5-39.6z"})],-1)]),2),l("div",ea,[e[27]||(e[27]=l("div",{class:"status-title"},"远程仓库",-1)),l("div",aa,w(t.value.repoUrl||"未配置"),1)])])]),_:1}),s(c,{class:"status-card"},{default:o(()=>[l("div",ta,[l("div",{class:z(["status-icon",t.value.backupDir?"info":"warning"])},e[28]||(e[28]=[l("svg",{class:"status-icon-svg",viewBox:"0 0 1024 1024"},[l("path",{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 0 0-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"})],-1)]),2),l("div",sa,[e[29]||(e[29]=l("div",{class:"status-title"},"备份目录",-1)),l("div",la,[r(w(t.value.backupDir||"未配置")+" ",1),V.value?.is_git_repo?(b(),U("div",oa," Git仓库 ("+w(V.value.current_branch||"main")+" 分支) ",1)):F("",!0)])])])]),_:1}),s(c,{class:"status-card"},{default:o(()=>[l("div",ia,[l("div",{class:z(["status-icon",h.value.length>0?"info":"warning"])},e[30]||(e[30]=[l("svg",{class:"status-icon-svg",viewBox:"0 0 1024 1024"},[l("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}),l("path",{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"})],-1)]),2),l("div",na,[e[31]||(e[31]=l("div",{class:"status-title"},"提交总数",-1)),l("div",ra,w(h.value.length)+" 个",1)])])]),_:1})]),v.value.visible?(b(),U("div",ua,[s(c,null,{default:o(()=>[l("div",da,[e[33]||(e[33]=l("h3",null,"Git 备份进度",-1)),s(i,{size:"small",onClick:ve,type:"danger",plain:""},{default:o(()=>e[32]||(e[32]=[r("取消")])),_:1})]),s(k,{percentage:v.value.percent,status:v.value.status==="error"?"exception":"success","stroke-width":12,"show-text":""},null,8,["percentage","status"]),l("p",ca,w(v.value.message),1)]),_:1})])):F("",!0),l("div",pa,[s(c,null,{header:o(()=>[l("div",va,[e[35]||(e[35]=l("span",{class:"card-title"},"Git 提交历史",-1)),l("div",ma,[s(i,{size:"small",onClick:E,loading:I.value,icon:g(ae)},{default:o(()=>e[34]||(e[34]=[r(" 刷新历史 ")])),_:1},8,["loading","icon"])])])]),default:o(()=>[l("div",fa,[te((b(),se(G,{data:h.value,style:{width:"100%"},border:"",stripe:"","empty-text":"暂无提交记录","row-key":"hash",height:320},{default:o(()=>[s(y,{label:"提交信息","min-width":"200"},{default:o(({row:n})=>[l("div",ga,[l("div",_a,w(n.message),1),l("div",wa,w(n.hash.substring(0,8)),1)])]),_:1}),s(y,{label:"作者",width:"120"},{default:o(({row:n})=>[l("span",ka,w(n.author),1)]),_:1}),s(y,{label:"操作",width:"200",fixed:"right"},{default:o(({row:n})=>[l("div",ya,[s(i,{type:"info",size:"small",onClick:ke=>ce(n),icon:g(Be)},{default:o(()=>e[36]||(e[36]=[r(" 详情 ")])),_:2},1032,["onClick","icon"]),s(i,{type:"primary",size:"small",onClick:ke=>pe(n),icon:g(Ne)},{default:o(()=>e[37]||(e[37]=[r(" 恢复 ")])),_:2},1032,["onClick","icon"])])]),_:1})]),_:1},8,["data"])),[[Y,I.value]])])]),_:1})]),s(J,{modelValue:T.value,"onUpdate:modelValue":e[12]||(e[12]=n=>T.value=n),title:"Git 备份配置",width:"700px","close-on-click-modal":!1},{footer:o(()=>[l("div",Ca,[s(i,{onClick:e[11]||(e[11]=n=>T.value=!1)},{default:o(()=>e[44]||(e[44]=[r("取消")])),_:1}),s(i,{type:"primary",onClick:ne,disabled:!q.value},{default:o(()=>e[45]||(e[45]=[r(" 保存配置 ")])),_:1},8,["disabled"])])]),default:o(()=>[s(W,{model:t.value,"label-width":"120px",ref:"configFormRef"},{default:o(()=>[s(m,{label:"Git 仓库 URL",required:""},{default:o(()=>[s(_,{modelValue:t.value.repoUrl,"onUpdate:modelValue":e[2]||(e[2]=n=>t.value.repoUrl=n),placeholder:"例如: https://github.com/user/repo.git"},null,8,["modelValue"])]),_:1}),s(m,{label:"认证方式"},{default:o(()=>[s(fe,{modelValue:t.value.authType,"onUpdate:modelValue":e[3]||(e[3]=n=>t.value.authType=n)},{default:o(()=>[s(K,{value:"password"},{default:o(()=>e[38]||(e[38]=[r("用户名和密码")])),_:1}),s(K,{value:"token"},{default:o(()=>e[39]||(e[39]=[r("访问令牌")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t.value.authType==="password"?(b(),U(le,{key:0},[s(m,{label:"用户名",required:""},{default:o(()=>[s(_,{modelValue:t.value.username,"onUpdate:modelValue":e[4]||(e[4]=n=>t.value.username=n),placeholder:"Git 用户名"},null,8,["modelValue"])]),_:1}),s(m,{label:"密码",required:""},{default:o(()=>[l("div",ba,[s(_,{modelValue:t.value.password,"onUpdate:modelValue":e[5]||(e[5]=n=>t.value.password=n),placeholder:"Git 密码","show-password":""},null,8,["modelValue"]),s(i,{type:"primary",onClick:Z,disabled:!P.value,icon:g(oe)},{default:o(()=>e[40]||(e[40]=[r(" 测试连接 ")])),_:1},8,["disabled","icon"])])]),_:1})],64)):(b(),U(le,{key:1},[s(m,{label:"用户名",required:""},{default:o(()=>[s(_,{modelValue:t.value.tokenUsername,"onUpdate:modelValue":e[6]||(e[6]=n=>t.value.tokenUsername=n),placeholder:"Git 用户名"},null,8,["modelValue"])]),_:1}),s(m,{label:"访问令牌",required:""},{default:o(()=>[l("div",ha,[s(_,{modelValue:t.value.token,"onUpdate:modelValue":e[7]||(e[7]=n=>t.value.token=n),placeholder:"Git 个人访问令牌","show-password":""},null,8,["modelValue"]),s(i,{type:"primary",onClick:Z,disabled:!P.value,icon:g(oe)},{default:o(()=>e[41]||(e[41]=[r(" 测试连接 ")])),_:1},8,["disabled","icon"])])]),_:1})],64)),s(m,{label:"备份目录",required:""},{default:o(()=>[l("div",Ga,[s(_,{modelValue:t.value.backupDir,"onUpdate:modelValue":e[8]||(e[8]=n=>t.value.backupDir=n),placeholder:"选择需要备份的目录",readonly:""},null,8,["modelValue"]),s(i,{onClick:re,type:"primary",icon:g(He)},{default:o(()=>e[42]||(e[42]=[r(" 选择 ")])),_:1},8,["icon"])])]),_:1}),s(m,{label:"自动备份"},{default:o(()=>[l("div",Va,[s(Q,{modelValue:t.value.autoBackup,"onUpdate:modelValue":e[9]||(e[9]=n=>t.value.autoBackup=n)},{default:o(()=>[e[43]||(e[43]=l("span",null,"启用自动备份",-1)),s(_e,{content:"自动备份将按设定间隔定期执行",placement:"top"},{default:o(()=>[s(ge,{class:"help-icon"},{default:o(()=>[s(g(Je))]),_:1})]),_:1})]),_:1},8,["modelValue"])])]),_:1}),t.value.autoBackup?(b(),se(m,{key:2,label:"备份间隔"},{default:o(()=>[s(we,{modelValue:t.value.backupInterval,"onUpdate:modelValue":e[10]||(e[10]=n=>t.value.backupInterval=n),placeholder:"选择备份间隔"},{default:o(()=>[s(C,{label:"每30分钟",value:30}),s(C,{label:"每小时",value:60}),s(C,{label:"每2小时",value:120}),s(C,{label:"每6小时",value:360}),s(C,{label:"每12小时",value:720}),s(C,{label:"每天",value:1440})]),_:1},8,["modelValue"])]),_:1})):F("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"]),s(J,{modelValue:B.value,"onUpdate:modelValue":e[17]||(e[17]=n=>B.value=n),title:"Git 备份",width:"500px","close-on-click-modal":!1},{footer:o(()=>[l("div",Ua,[s(i,{onClick:e[16]||(e[16]=n=>B.value=!1)},{default:o(()=>e[48]||(e[48]=[r("取消")])),_:1}),s(i,{type:"primary",onClick:de,disabled:!d.value.message.trim()},{default:o(()=>e[49]||(e[49]=[r(" 开始备份 ")])),_:1},8,["disabled"])])]),default:o(()=>[s(W,{model:d.value,"label-width":"100px"},{default:o(()=>[s(m,{label:"提交信息",required:""},{default:o(()=>[s(_,{modelValue:d.value.message,"onUpdate:modelValue":e[13]||(e[13]=n=>d.value.message=n),placeholder:"请输入提交信息",type:"textarea",rows:3},null,8,["modelValue"])]),_:1}),s(m,{label:"标签名称"},{default:o(()=>[s(_,{modelValue:d.value.tagName,"onUpdate:modelValue":e[14]||(e[14]=n=>d.value.tagName=n),placeholder:"可选：为此次备份添加标签（如：v1.0.0, backup-20250108）",onInput:me},null,8,["modelValue"]),e[46]||(e[46]=l("div",{class:"form-tip"}," 标签名只能包含字母、数字、下划线、连字符和点号 ",-1))]),_:1}),s(m,{label:"强制推送"},{default:o(()=>[s(Q,{modelValue:d.value.force,"onUpdate:modelValue":e[15]||(e[15]=n=>d.value.force=n)},{default:o(()=>e[47]||(e[47]=[r(" 强制推送（谨慎使用） ")])),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),s(J,{modelValue:H.value,"onUpdate:modelValue":e[18]||(e[18]=n=>H.value=n),title:"提交详情",width:"800px"},{default:o(()=>[te((b(),U("div",xa,[l("pre",Da,w(L.value),1)])),[[Y,O.value]])]),_:1},8,["modelValue"])])}}},Ra=ye(Ta,[["__scopeId","data-v-84c3fe21"]]);export{Ra as default};
