import{_ as H,bh as Y,r as T,c as i,P as s,o as Z,b as r,m as n,p as f,e as a,F as g,n as v,d as B,C,ed as M,X as q,Y as P,$ as W,a0 as X,Z as G,U as J}from"./entry-BIjVVog3.js";const K={key:0,class:"bubble-avatar"},Q=["src","alt"],_={key:1,class:"avatar-icon"},ee={key:0,class:"user-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},te={key:1,class:"assistant-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},le={key:2,class:"avatar-placeholder"},ae={class:"bubble-content-wrapper"},oe={key:0,class:"bubble-header"},ne={class:"sender-name"},re={class:"message-time"},se={key:0,class:"loading-content"},be={class:"loading-text"},ie={key:1,class:"error-content"},ce={class:"error-message"},ue={key:2,class:"message-content"},de={class:"reasoning-text"},ge={key:1,class:"reasoning-content"},fe={key:1,class:"bubble-actions"},he=["onClick","disabled","title"],pe={__name:"MarkdownBubble",props:{content:{type:String,required:!0},messageType:{type:String,default:"assistant",validator:e=>["user","assistant","system"].includes(e)},isError:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},showAvatar:{type:Boolean,default:!0},showHeader:{type:Boolean,default:!0},showActions:{type:Boolean,default:!0},senderName:{type:String,default:()=>""},avatarSrc:{type:String,default:""},useDefaultIcons:{type:Boolean,default:!0},timestamp:{type:Number,default:()=>Date.now()},theme:{type:String,default:"auto",validator:e=>["light","dark","auto"].includes(e)},variant:{type:String,default:"default",validator:e=>["default","minimal","card"].includes(e)},reasoning:{type:String,default:""},reasoningTime:{type:Number,default:0},hasReasoning:{type:Boolean,default:!1},showReasoning:{type:Boolean,default:!0},customActions:{type:Array,default:()=>[]},loadingText:{type:String,default:"正在思考..."},typing:{type:Boolean,default:!1},typingOptions:{type:Object,default:()=>({})},streaming:{type:Boolean,default:!1},fontSize:{type:Number,default:1,validator:e=>e>=.5&&e<=2}},emits:["copy","regenerate","resend","action","typingEnd","streamingUpdate"],setup(e,{emit:L}){const o=e,d=L,w=Y("configStore",null),h=T(!1),p=T(!0),y=i(()=>o.theme==="auto"?h.value?"dark":"light":o.theme),m=i(()=>o.messageType==="user"),k=i(()=>o.messageType==="assistant"),E=i(()=>o.fontSize&&o.fontSize!==1?o.fontSize:w?.chat?.fontSize&&typeof w.chat.fontSize=="number"?w.chat.fontSize:1),z=i(()=>[`bubble-${o.messageType}`,`bubble-${o.variant}`,`theme-${y.value}`,{"bubble-loading":o.loading,"bubble-error":o.isError,"bubble-disabled":o.disabled}]),D=i(()=>["content-main",{"content-user":m.value,"content-assistant":k.value}]),N=i(()=>{const t=y.value==="dark",c=t?{"--bubble-primary":"#4080ff","--bubble-primary-light-3":"#5c93ff","--bubble-primary-light-5":"#79a6ff","--bubble-primary-light-7":"#96b9ff","--bubble-primary-light-8":"#2d4b6d","--bubble-primary-light-9":"#1c2b3d","--bubble-success":"#67c23a","--bubble-success-light-8":"#2d4a22","--bubble-success-light-9":"#1f2f1a","--bubble-warning":"#e6a23c","--bubble-warning-light":"#b88230","--bubble-danger":"#f56c6c","--bubble-danger-light-3":"#c45656","--bubble-danger-light-7":"#4a2626","--bubble-danger-light-9":"#2e1a1a","--bubble-bg-color":"#262624","--bubble-bg-color-page":"#242424","--bubble-bg-color-overlay":"#2a2a2a","--bubble-user-bg":"#262624","--bubble-code-bg":"#2b2b29","--bubble-text-color-primary":"#e0e0e0","--bubble-text-color-regular":"#b0b0b0","--bubble-text-color-secondary":"#808080","--bubble-text-color-placeholder":"#606060","--bubble-border-color":"#333333","--bubble-border-color-light":"#3d3d3d","--bubble-border-color-lighter":"#474747","--bubble-border-color-extra-light":"#515151","--bubble-fill-color":"#2a2a2a","--bubble-fill-color-light":"#303030","--bubble-fill-color-lighter":"#363636","--bubble-fill-color-extra-light":"#3d3d3d","--bubble-fill-color-dark":"#242424","--bubble-fill-color-darker":"#1f1f1f","--bubble-fill-color-blank":"#1a1a1a","--bubble-shadow":"0 2px 8px rgba(0, 0, 0, 0.3)","--bubble-shadow-light":"0 1px 3px rgba(0, 0, 0, 0.3)","--bubble-shadow-dark":"0 4px 12px rgba(0, 0, 0, 0.4)"}:{"--bubble-primary":"#409eff","--bubble-primary-light-3":"#79bbff","--bubble-primary-light-5":"#a0cfff","--bubble-primary-light-7":"#c6e2ff","--bubble-primary-light-8":"#d9ecff","--bubble-primary-light-9":"#ecf5ff","--bubble-success":"#67c23a","--bubble-success-light-8":"#e1f3d8","--bubble-success-light-9":"#f0f9ff","--bubble-warning":"#e6a23c","--bubble-warning-light":"#f0c78a","--bubble-danger":"#f56c6c","--bubble-danger-light-3":"#f89898","--bubble-danger-light-7":"#fcd3d3","--bubble-danger-light-9":"#fef0f0","--bubble-bg-color":"#f0eee6","--bubble-bg-color-page":"#f5f7fa","--bubble-bg-color-overlay":"#ffffff","--bubble-user-bg":"#f0eee6","--bubble-code-bg":"#fdfcfa","--bubble-text-color-primary":"#303133","--bubble-text-color-regular":"#606266","--bubble-text-color-secondary":"#909399","--bubble-text-color-placeholder":"#a8abb2","--bubble-border-color":"#dcdfe6","--bubble-border-color-light":"#e4e7ed","--bubble-border-color-lighter":"#ebeef5","--bubble-border-color-extra-light":"#f2f6fc","--bubble-fill-color":"#f0f2f5","--bubble-fill-color-light":"#f5f7fa","--bubble-fill-color-lighter":"#fafafa","--bubble-fill-color-extra-light":"#fafcff","--bubble-fill-color-dark":"#ebedf0","--bubble-fill-color-darker":"#e6e8eb","--bubble-fill-color-blank":"#ffffff","--bubble-shadow":"0 2px 8px rgba(0, 0, 0, 0.1)","--bubble-shadow-light":"0 1px 3px rgba(0, 0, 0, 0.1)","--bubble-shadow-dark":"0 4px 12px rgba(0, 0, 0, 0.15)"},u={...c};m.value?(u["--bubble-bg"]=c["--bubble-user-bg"],u["--bubble-text"]=t?"#ffffff":"#2c2c2c"):(u["--bubble-bg"]=c["--bubble-bg-color"],u["--bubble-text"]=c["--bubble-text-color-primary"]),u["--bubble-border"]=c["--bubble-border-color-light"],u["--avatar-size"]="36px",u["--bubble-radius"]="12px";const V=E.value||1;return u["--bubble-font-scale"]=V.toString(),u}),x=i(()=>k.value&&o.hasReasoning&&o.showReasoning),A=i(()=>o.reasoningTime>0?`推理过程 (用时${o.reasoningTime.toFixed(1)}秒)`:"推理过程"),O=i(()=>{const t=[{key:"copy",title:"复制消息",class:"copy-btn",icon:()=>s("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[s("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),s("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})])}];return k.value&&t.push({key:"regenerate",title:"重新生成",class:"regenerate-btn",icon:()=>s("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[s("path",{d:"M21 2v6h-6"}),s("path",{d:"M3 12a9 9 0 0 1 15-6.7L21 8"}),s("path",{d:"M3 22v-6h6"}),s("path",{d:"M21 12a9 9 0 0 1-15 6.7L3 16"})])}),m.value&&t.push({key:"resend",title:"重发消息",class:"resend-btn",icon:()=>s("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[s("path",{d:"M21 2v6h-6"}),s("path",{d:"M3 12a9 9 0 0 1 15-6.7L21 8"}),s("path",{d:"M3 22v-6h6"}),s("path",{d:"M21 12a9 9 0 0 1-15 6.7L3 16"})])}),t}),R=i(()=>[...O.value,...o.customActions]),$=()=>{if(typeof window>"u")return;h.value=document.documentElement.classList.contains("dark");const t=new MutationObserver(l=>{l.forEach(b=>{if(b.type==="attributes"&&b.attributeName==="class"){const c=document.documentElement.classList.contains("dark");c!==h.value&&(h.value=c)}})});t.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),J(()=>{t.disconnect()})},j=()=>{p.value=!p.value},F=t=>{if(!t)return"";const l=new Date(t),b=new Date;return b-l<24*60*60*1e3&&l.getDate()===b.getDate()?l.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):l.toLocaleString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},I=async()=>{try{const l=window.getSelection().toString()||o.content;d("copy",l)}catch(t){console.error("复制失败:",t),d("copy",null,t)}},S=()=>{d("typingEnd")},U=t=>{switch(t.key){case"copy":I();break;case"regenerate":d("regenerate");break;case"resend":d("resend");break;default:d("action",t);break}};return Z(()=>{$()}),(t,l)=>(n(),r("div",{class:v(["markdown-bubble",z.value]),style:G(N.value)},[e.showAvatar?(n(),r("div",K,[e.avatarSrc&&!e.useDefaultIcons?(n(),r("img",{key:0,src:e.avatarSrc,alt:e.senderName,class:"avatar-image"},null,8,Q)):e.useDefaultIcons?(n(),r("div",_,[m.value?(n(),r("svg",ee,l[0]||(l[0]=[a("circle",{cx:"12",cy:"8",r:"4",fill:"currentColor"},null,-1),a("path",{d:"M6 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round"},null,-1)]))):(n(),r("svg",te,l[1]||(l[1]=[a("path",{d:"M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z",fill:"currentColor"},null,-1),a("circle",{cx:"12",cy:"19",r:"2",fill:"currentColor"},null,-1),a("path",{d:"M8 19h8",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round"},null,-1)])))])):(n(),r("div",le,g(e.senderName.charAt(0).toUpperCase()),1))])):f("",!0),a("div",ae,[e.showHeader?(n(),r("div",oe,[a("span",ne,g(e.senderName),1),a("span",re,g(F(e.timestamp)),1)])):f("",!0),a("div",{class:v(["bubble-content",D.value])},[e.loading?(n(),r("div",se,[l[2]||(l[2]=a("div",{class:"loading-dots"},[a("span",{class:"dot"}),a("span",{class:"dot"}),a("span",{class:"dot"})],-1)),a("span",be,g(e.loadingText),1)])):e.isError?(n(),r("div",ie,[l[3]||(l[3]=a("div",{class:"error-icon"},"⚠️",-1)),a("div",ce,g(e.content),1)])):(n(),r("div",ue,[x.value?(n(),r("div",{key:0,class:"reasoning-toggle",onClick:j},[l[4]||(l[4]=a("span",{class:"reasoning-icon"},null,-1)),a("span",de,g(A.value),1),a("span",{class:v(["reasoning-arrow",{expanded:p.value}])},"▼",2)])):f("",!0),x.value&&p.value&&e.reasoning?(n(),r("div",ge,[B(C(M),{content:e.reasoning,theme:y.value,typing:e.typing,typingOptions:e.typingOptions,onTypingEnd:S},null,8,["content","theme","typing","typingOptions"])])):f("",!0),B(C(M),{content:e.content,theme:y.value,typing:e.typing,typingOptions:e.typingOptions,onTypingEnd:S},null,8,["content","theme","typing","typingOptions"])]))],2),e.showActions?(n(),r("div",fe,[(n(!0),r(q,null,P(R.value,b=>(n(),r("button",{key:b.key,class:v(["action-btn",b.class]),onClick:c=>U(b),disabled:e.disabled,title:b.title},[(n(),W(X(b.icon)))],10,he))),128))])):f("",!0)])],6))}},me=H(pe,[["__scopeId","data-v-97dde09b"]]);export{me as M};
