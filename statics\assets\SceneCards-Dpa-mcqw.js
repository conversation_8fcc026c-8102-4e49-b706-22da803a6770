import{_ as Mt,b as S,m as g,e as n,d as l,g as o,C as _,b5 as Kl,B as Vt,F as k,a7 as st,h as M,t as Dt,X as L,Y as N,$ as K,v as y,J as Jt,c as W,r as c,o as kt,U as Nt,w as xt,p as Me,n as Ce,Z as it,bW as jt,bX as Ft,bY as qt,aa as Kt,au as Wt,j as Ht,q as Zt,s as Gt,x as pe,ak as Qt,al as el,k as tl,R as Ve,E as m,bk as Ge,az as Wl,aw as Hl,ab as Zl,ac as Bt,bC as Gl,aB as Lt,aK as Ql,aL as eo,aM as to,aP as lo,aJ as oo,bD as ao,bZ as no,b_ as so,ao as Rt,bH as io,bx as uo,b8 as ro,y as co,bE as Ut,ar as vo,aD as po,bL as mo,bQ as At,ag as fo,aH as go,a5 as yo,av as Je}from"./entry-BIjVVog3.js";/* empty css                      *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    *//* empty css                  *//* empty css                        *//* empty css                *//* empty css                   */import{u as _o}from"./book-BHcNewcO.js";import{d as ho}from"./vuedraggable-umd-CP6rdxdP.js";import"./apiUtils-CGTCyBFs.js";const bo={class:"scene-card"},wo={class:"card-header"},Co={class:"card-icon"},ko={class:"card-title"},xo={class:"card-actions"},So={class:"card-content"},Mo={class:"card-description"},Vo={class:"card-tags"},Do={__name:"Card",props:{title:{type:String,default:"未命名场景"},description:{type:String,default:""},tags:{type:Array,default:()=>[]}},emits:["delete"],setup(De){return(me,z)=>{const H=Vt,ke=Dt,le=Jt;return g(),S("div",bo,[n("div",wo,[n("div",Co,[l(H,null,{default:o(()=>[l(_(Kl))]),_:1})]),n("div",ko,k(De.title),1),n("div",xo,[l(ke,{type:"danger",size:"small",circle:"",onClick:z[0]||(z[0]=M(A=>me.$emit("delete"),["stop"])),class:"delete-btn"},{default:o(()=>[l(H,null,{default:o(()=>[l(_(st))]),_:1})]),_:1})])]),n("div",So,[n("div",Mo,k(De.description),1),n("div",Vo,[(g(!0),S(L,null,N(De.tags,A=>(g(),K(le,{key:A,size:"small",effect:"light",class:"card-tag"},{default:o(()=>[y(k(A),1)]),_:2},1024))),128))])])])}}},St=Mt(Do,[["__scopeId","data-v-b425cdbb"]]),Io={class:"fullscreen-canvas-wrapper"},Po=["onMousedown","onDblclick","onMouseover","onMouseleave"],Eo={class:"canvas-controls"},To={class:"zoom-display"},$o={class:"edit-dialog-body"},zo={class:"edit-dialog-footer"},Yo={__name:"FullscreenSceneCanvas",props:{visible:{type:Boolean,default:!1},scenes:{type:Array,default:()=>[]},bookTitle:{type:String,default:""},currentPool:{type:Object,default:null}},emits:["update:visible","create-scene","edit-scene","delete-scene","random-draw","save-scenes"],setup(De,{emit:me}){const z=De,H=me,ke=W({get:()=>z.visible,set:v=>H("update:visible",v)}),le=c(null),A=c(null),D=c(0),oe=c(0),u=c(1),f=c(!1),Ne=c(0),h=c(0),Z=c(!0),Y=c(null),ne=c(null),E=c(!1),$e=c(null),se=c(null),b=c({title:"",description:"",tags:[]}),Qe=W(()=>{const v=new Set;return z.scenes.forEach(p=>{p.tags&&Array.isArray(p.tags)&&p.tags.forEach(C=>v.add(C))}),Array.from(v)}),et=W(()=>({transform:`translate(${D.value}px, ${oe.value}px) scale(${u.value})`})),tt=v=>({position:"absolute",left:`${v.x||0}px`,top:`${v.y||0}px`,zIndex:v.zIndex||1,transform:"translate3d(0, 0, 0)"}),lt=()=>{ke.value=!1},xe=()=>{u.value=Math.min(2,u.value+.1)},ze=()=>{u.value=Math.max(.3,u.value-.1)},fe=()=>{D.value=0,oe.value=0,u.value=1,ge()},w=()=>{if(!z.scenes.length){m.warning("没有场景卡片需要排版");return}const v=280,p=280,C=20,T=16,ye=(le.value?.clientWidth||1200)/u.value-C*2,O=Math.floor((ye+T)/(v+T)),j=Math.max(1,Math.min(O,z.scenes.length));z.scenes.forEach((G,ue)=>{const x=Math.floor(ue/j),F=ue%j;G.x=C+F*(v+T),G.y=C+x*(p+T)}),H("save-scenes"),m.success(`已自动排版 ${z.scenes.length} 个场景卡片`),Ve(()=>{ge()})},ge=()=>{if(z.scenes.length>0&&le.value){let v=1/0,p=1/0,C=-1/0,T=-1/0;z.scenes.forEach(ue=>{const x=ue.x||0,F=ue.y||0;v=Math.min(v,x),p=Math.min(p,F),C=Math.max(C,x+280),T=Math.max(T,F+280)});const U=(v+C)/2,ye=(p+T)/2,O=le.value.getBoundingClientRect(),j=O.width/2,G=O.height/2;D.value=j-U*u.value,oe.value=G-ye*u.value}},R=v=>{v.button===2&&(f.value=!0,Ne.value=v.clientX-D.value,h.value=v.clientY-oe.value,Z.value=!0,A.value&&(A.value.style.cursor="grabbing"),v.preventDefault())},Ye=v=>{f.value&&(D.value=v.clientX-Ne.value,oe.value=v.clientY-h.value,v.preventDefault())},je=v=>{f.value&&(f.value=!1,A.value&&(A.value.style.cursor="default"),setTimeout(()=>{Z.value=!1},1e3))},ot=v=>{v.preventDefault();const p=le.value.getBoundingClientRect(),C=v.clientX-p.left,T=v.clientY-p.top,U=(C-D.value)/u.value,ye=(T-oe.value)/u.value,O=.1;v.deltaY<0?u.value=Math.min(2,u.value+O):u.value=Math.max(.3,u.value-O);const j=(C-D.value)/u.value,G=(T-oe.value)/u.value;D.value+=(j-U)*u.value,oe.value+=(G-ye)*u.value,Z.value=!0,setTimeout(()=>{Z.value=!1},1e3)},ut=(v,p)=>{if(p.button!==0||f.value)return;Y.value=v,ne.value=v;const C=z.scenes.find(T=>T.id===v);if(C){const T=p.currentTarget.getBoundingClientRect();C.mouseOffsetX=(p.clientX-T.left)*u.value,C.mouseOffsetY=(p.clientY-T.top)*u.value}document.addEventListener("mousemove",Xe),document.addEventListener("mouseup",Oe),p.preventDefault()},Xe=v=>{if(ne.value){const p=z.scenes.find(C=>C.id===ne.value);if(p){const C=le.value.getBoundingClientRect(),T=(v.clientX-C.left-D.value)/u.value,U=(v.clientY-C.top-oe.value)/u.value;p.x=T-p.mouseOffsetX/u.value,p.y=U-p.mouseOffsetY/u.value}}},Oe=()=>{ne.value&&(H("save-scenes"),ne.value=null),document.removeEventListener("mousemove",Xe),document.removeEventListener("mouseup",Oe)},Fe=v=>{ne.value||(Y.value=v)},rt=()=>{ne.value||(Y.value=null)},at=v=>{v.key==="Escape"&&(v.preventDefault(),v.stopPropagation(),E.value?ie():lt())};kt(()=>{setTimeout(()=>{Z.value=!1},3e3),document.addEventListener("keydown",at)}),Nt(()=>{document.removeEventListener("mousemove",Xe),document.removeEventListener("mouseup",Oe),document.removeEventListener("keydown",at)});const dt=v=>{se.value=v,b.value={title:v.title||"",description:v.description||"",tags:v.tags?[...v.tags]:[]},E.value=!0,Ve(()=>{$e.value&&$e.value.focus()})},ie=()=>{E.value=!1,se.value=null,b.value={title:"",description:"",tags:[]}},ct=()=>{ie()},nt=()=>{if(se.value){if(!b.value.title.trim()){m.warning("请输入场景标题");return}se.value.title=b.value.title.trim(),se.value.description=b.value.description.trim(),se.value.tags=b.value.tags,H("save-scenes"),m.success("场景已更新"),ie()}};return xt(()=>z.visible,v=>{v&&Ve(()=>{fe()})}),(v,p)=>{const C=Vt,T=Gt,U=Zt,ye=el,O=Qt,j=Ht,G=Dt,ue=tl;return g(),K(ue,{modelValue:ke.value,"onUpdate:modelValue":p[5]||(p[5]=x=>ke.value=x),title:"",class:"fullscreen-scene-canvas-dialog",fullscreen:"","destroy-on-close":"","show-close":!1,"lock-scroll":!0,"append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{default:o(()=>[n("div",Io,[n("div",{class:"scene-canvas",onMousedown:R,onMouseup:je,onMousemove:Ye,onContextmenu:p[0]||(p[0]=M(()=>{},["prevent"])),onWheel:ot,ref_key:"canvasRef",ref:le},[n("div",{class:Ce(["drag-hint",{visible:Z.value}])},p[6]||(p[6]=[y(" 按住 "),n("span",{class:"key-hint"},"右键",-1),y(" 拖动画布 | 使用 "),n("span",{class:"key-hint"},"滚轮",-1),y(" 缩放 ")]),2),n("div",{class:Ce(["infinite-canvas",{dragging:f.value}]),style:it(et.value),ref_key:"infiniteCanvasRef",ref:A},[(g(!0),S(L,null,N(De.scenes,x=>(g(),S("div",{key:x.id,class:"scene-card-wrapper",style:it(tt(x))},[n("div",{class:"scene-card-interaction-area",onMousedown:M(F=>ut(x.id,F),["stop"]),onDblclick:M(F=>dt(x),["stop"]),onMouseover:M(F=>Fe(x.id,F),["stop"]),onMouseleave:M(F=>rt(x.id),["stop"])},[l(St,{title:x.title,description:x.description,tags:x.tags,onDelete:F=>v.$emit("delete-scene",x),class:Ce({active:Y.value===x.id,dragging:ne.value===x.id})},null,8,["title","description","tags","onDelete","class"])],40,Po)],4))),128))],6),n("div",Eo,[n("div",{class:"control-btn",onClick:xe,title:"放大"},[l(C,null,{default:o(()=>[l(_(jt))]),_:1})]),n("div",To,k(Math.round(u.value*100))+"%",1),n("div",{class:"control-btn",onClick:ze,title:"缩小"},[l(C,null,{default:o(()=>[l(_(Ft))]),_:1})]),n("div",{class:"control-btn",onClick:w,title:"自动排版"},[l(C,null,{default:o(()=>[l(_(qt))]),_:1})]),n("div",{class:"control-btn",onClick:fe,title:"重置画布"},[l(C,null,{default:o(()=>[l(_(Kt))]),_:1})]),n("div",{class:"control-btn",onClick:lt,title:"退出全屏"},[l(C,null,{default:o(()=>[l(_(Wt))]),_:1})])])],544),E.value?(g(),S("div",{key:0,class:"fullscreen-edit-dialog-overlay",onClick:ct},[n("div",{class:"fullscreen-edit-dialog",onClick:p[4]||(p[4]=M(()=>{},["stop"]))},[n("div",{class:"edit-dialog-header"},[p[7]||(p[7]=n("h3",null,"编辑场景",-1)),n("button",{class:"edit-dialog-close",onClick:ie},"×")]),n("div",$o,[l(j,{model:b.value,"label-width":"100px",class:"scene-form"},{default:o(()=>[l(U,{label:"场景标题"},{default:o(()=>[l(T,{modelValue:b.value.title,"onUpdate:modelValue":p[1]||(p[1]=x=>b.value.title=x),placeholder:"请输入场景标题",ref_key:"editTitleInput",ref:$e,onKeyup:[pe(M(nt,["prevent"]),["enter"]),pe(M(ie,["prevent"]),["esc"])],autofocus:""},null,8,["modelValue","onKeyup"])]),_:1}),l(U,{label:"场景描述"},{default:o(()=>[l(T,{modelValue:b.value.description,"onUpdate:modelValue":p[2]||(p[2]=x=>b.value.description=x),type:"textarea",rows:6,resize:"none",placeholder:"请描述场景内容",onKeyup:pe(M(ie,["prevent"]),["esc"])},null,8,["modelValue","onKeyup"])]),_:1}),l(U,{label:"场景标签"},{default:o(()=>[l(O,{modelValue:b.value.tags,"onUpdate:modelValue":p[3]||(p[3]=x=>b.value.tags=x),multiple:"",filterable:"","allow-create":"","default-first-option":"",placeholder:"请选择或创建标签"},{default:o(()=>[(g(!0),S(L,null,N(Qe.value,x=>(g(),K(ye,{key:x,label:x,value:x},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),n("div",zo,[l(G,{onClick:ie},{default:o(()=>p[8]||(p[8]=[y("取消")])),_:1}),l(G,{type:"primary",onClick:nt},{default:o(()=>p[9]||(p[9]=[y("保存场景")])),_:1})])])])):Me("",!0)])]),_:1},8,["modelValue"])}}},Xo=Mt(Yo,[["__scopeId","data-v-c801145c"]]),Oo={class:"scene-cards-container"},Bo={class:"action-bar"},Lo={class:"pool-option"},Ro={class:"pool-info"},Uo={class:"pool-option"},Ao={class:"pool-name"},Jo={class:"pool-info"},No={class:"dropdown-item-content"},jo={class:"dropdown-item-content"},Fo={class:"dropdown-item-content"},qo={class:"dropdown-item-content"},Ko={class:"dropdown-item-content"},Wo=["id"],Ho=["onMousedown","onDblclick","onMouseover","onMouseleave"],Zo={class:"canvas-controls"},Go={class:"zoom-display"},Qo={class:"custom-dialog-header"},ea={class:"custom-dialog-body"},ta={class:"custom-dialog-footer"},la={class:"draw-result"},oa={class:"draw-content"},aa={class:"scenes-section"},na={class:"drawn-cards"},sa={class:"scene-number"},ia={key:0,class:"scene-connector"},ua={class:"combination-hint"},ra={class:"inspiration-section"},da={class:"inspiration-input"},ca={class:"inspiration-meta"},va={class:"inspiration-rating"},pa={class:"inspiration-actions"},ma={class:"dialog-header-content"},fa=["id"],ga={class:"history-count"},ya={class:"history-container"},_a={class:"history-content"},ha={class:"history-list"},ba={class:"history-list-content"},wa=["onClick"],Ca={class:"history-header"},ka={class:"header-main"},xa={class:"history-meta-info"},Sa={class:"history-time"},Ma={class:"related-pools-tooltip"},Va={class:"history-brief"},Da={class:"history-meta"},Ia={class:"history-tags"},Pa={class:"history-detail-panel"},Ea={class:"history-scenes"},Ta={class:"scenes-title"},$a={class:"scenes-list"},za={class:"scene-number"},Ya={class:"scene-title"},Xa={key:0,class:"scene-description"},Oa={class:"scene-tags"},Ba={key:1,class:"scene-connector"},La={class:"history-inspiration"},Ra={class:"inspiration-header"},Ua={class:"inspiration-title"},Aa={class:"inspiration-content"},Ja={key:1,class:"empty-detail"},Na={class:"history-footer"},ja={class:"history-pagination"},Fa={class:"dialog-footer"},qa={class:"dialog-footer"},Ka={class:"dialog-footer"},Wa={class:"manage-pools-container"},Ha={class:"pools-toolbar"},Za={class:"search-wrapper"},Ga={class:"pools-actions"},Qa={class:"pool-item-content"},en={class:"pool-item-name"},tn={class:"pool-item-info"},ln={class:"update-time"},on={class:"pool-item-actions"},an={class:"dialog-footer"},nn={class:"dialog-footer"},sn=5,un={__name:"SceneCards",setup(De){const me=_o(),z=c(null),H=c(null),ke=Ge({x:0,y:0}),le=Ge({x:0,y:0}),A=c(!1),D=c(1),oe=t=>{let e=t;typeof t=="string"&&(e=h.value.find(s=>s.id===t)),e&&(D.value=Math.max(D.value,...h.value.map(s=>s.zIndex||0)),D.value++,e.zIndex=D.value,b())},u=c({pools:[],currentPoolId:null}),f=W(()=>u.value.currentPoolId==="all"?Ne.value:u.value.pools.find(t=>t.id===u.value.currentPoolId)||null),Ne=W(()=>({id:"all",name:"全部场景",scenes:u.value.pools.reduce((e,s)=>{const d=JSON.parse(JSON.stringify(s.scenes||[]));return d.forEach(i=>{i.sourcePool={id:s.id,name:s.name}}),[...e,...d]},[]),isVirtual:!0,createTime:Date.now(),updateTime:Date.now()})),h=W({get:()=>f.value?.scenes||[],set:t=>{f.value&&!f.value.isVirtual&&(f.value.scenes=t,b())}}),Z=c(2),Y=c(""),ne=W(()=>me.bookList?.find(e=>e.id===Y.value)?.title||"");kt(async()=>{try{await me.loadBooks(),me.bookList?.length>0&&(Y.value=me.bookList[0].id,await se())}catch(t){console.error("初始化失败:",t),m.error("初始化失败，请刷新页面重试")}});const E=c(null),$e=W(()=>u.value.pools.reduce((e,s)=>{if(Array.isArray(s.inspirations)){const d=s.inspirations.map(i=>i.isFromAllPool?{...i,poolName:i.mainPoolName||s.name,poolId:i.mainPoolId||s.id,fromAllPool:!0,relatedPools:i.relatedPools||[]}:{...i,poolName:s.name,poolId:s.id,fromAllPool:!1,relatedPools:[]});e.push(...d)}return e},[]).sort((e,s)=>s.timestamp-e.timestamp)),se=async()=>{if(Y.value)try{const t=await window.pywebview.api.book_controller.get_scene_events(Y.value),e=typeof t=="string"?JSON.parse(t):t;if(e.status==="success"){const s=e.data||{};u.value={pools:Array.isArray(s.pools)?s.pools:[],currentPoolId:s.currentPoolId||null},D.value=1,u.value.pools.forEach(d=>{Array.isArray(d.scenes)&&d.scenes.forEach(i=>{i.zIndex?D.value=Math.max(D.value,i.zIndex):(i.zIndex=D.value,D.value++)})}),u.value.pools.length||Qe("默认卡池"),!u.value.currentPoolId&&u.value.pools.length>0&&(u.value.currentPoolId=u.value.pools[0].id)}else throw new Error(e.message||"加载失败")}catch(t){console.error("加载场景失败:",t),m.error(`加载场景失败: ${t.message}`),u.value={pools:[],currentPoolId:null},D.value=1}},b=async()=>{Array.isArray(u.value.pools)||(u.value.pools=[]);try{const t=await window.pywebview.api.book_controller.save_scene_events(Y.value,JSON.parse(JSON.stringify(u.value))),e=typeof t=="string"?JSON.parse(t):t;if(e.status==="success")return!0;throw new Error(e.message||"保存失败")}catch(t){throw console.error("保存场景数据失败:",t),t}},Qe=t=>{Array.isArray(u.value.pools)||(u.value.pools=[]);const e={id:Date.now().toString(),name:t,createTime:Date.now(),updateTime:Date.now(),scenes:[],inspirations:[]};u.value.pools.push(e),u.value.currentPoolId=e.id,b()},et=t=>{if(!H.value||!z.value)return;Math.sqrt(Math.pow(t.clientX-le.x,2)+Math.pow(t.clientY-le.y,2))>5&&(A.value=!0);const s=z.value.getBoundingClientRect(),d=z.value.scrollTop;H.value.position={left:t.clientX-s.left-ke.x,top:t.clientY-s.top-ke.y+d}},tt=()=>{H.value&&A.value&&b(),H.value=null,A.value=!1,document.removeEventListener("mousemove",et),document.removeEventListener("mouseup",tt)};Nt(()=>{document.removeEventListener("mousemove",et),document.removeEventListener("mouseup",tt)});const lt=c(["战斗","对话","探索","追逐","相遇","告别","冲突","和解","发现","选择"]),xe=c(!1),ze=c(!1),fe=c(!1),w=Ge({id:"",title:"",description:"",tags:[]}),ge=c([]),R=c({content:"",rating:80,isUsed:!1}),Ye=c(!1),je=c(!1),ot=()=>{if(f.value?.isVirtual){m.warning('不能在"全部场景"卡池中创建场景');return}fe.value=!1,w.id="",w.title="",w.description="",w.tags=[],xe.value=!0,setTimeout(()=>{const t=document.querySelector(".custom-dialog input");t&&t.focus()},0)},ut=(t,e)=>{if(!A.value){if(f.value?.isVirtual&&e.sourcePool){u.value.currentPoolId=e.sourcePool.id,m.success(`已切换到场景所在卡池：${e.sourcePool.name}`),setTimeout(()=>{const s=h.value.find(d=>d.id===e.id);s&&(fe.value=!0,w.id=s.id,w.title=s.title,w.description=s.description,w.tags=[...s.tags],xe.value=!0,setTimeout(()=>{const d=document.querySelector(".custom-dialog input");d&&d.focus()},0))},0);return}fe.value=!0,w.id=e.id,w.title=e.title,w.description=e.description,w.tags=[...e.tags],xe.value=!0,setTimeout(()=>{const s=document.querySelector(".custom-dialog input");s&&s.focus()},0)}},Xe=t=>{Je.confirm("确定要删除这个场景吗？","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",appendTo:Q.value||document.body}).then(async()=>{const e=h.value.findIndex(s=>s.id===t.id);if(e!==-1){h.value.splice(e,1);try{await b(),m.success("场景已删除")}catch{m.error("删除失败，请重试")}}}).catch(()=>{})},Oe=async()=>{if(!w.title.trim()){m.warning("请输入场景标题");return}if(fe.value){const t=h.value.findIndex(e=>e.id===w.id);if(t!==-1){const e=h.value[t];e.title=w.title,e.description=w.description,e.tags=w.tags}}else{const t={id:Date.now().toString(),title:w.title,description:w.description,tags:w.tags,x:Math.random()*500,y:Math.random()*300,zIndex:D.value+1};h.value.push(t),D.value++}try{await b(),xe.value=!1,m.success(fe.value?"场景已更新":"场景已创建")}catch{m.error("保存失败，请重试")}},Fe=t=>new Date(t).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}),rt=async()=>{if(!R.value.content.trim())return;const t={timestamp:Date.now(),scenes:ge.value,content:R.value.content,rating:R.value.rating,isUsed:R.value.isUsed};if(f.value?.isVirtual){const e=ge.value.reduce((i,r)=>(r.sourcePool&&(i[r.sourcePool.id]||(i[r.sourcePool.id]={poolName:r.sourcePool.name,scenes:[]}),i[r.sourcePool.id].scenes.push(r)),i),{}),s=Object.values(e).map(i=>({name:i.poolName,sceneCount:i.scenes.length})),d=ge.value[0];if(d?.sourcePool){const i=u.value.pools.find(r=>r.id===d.sourcePool.id);if(i){const r={...t,isFromAllPool:!0,relatedPools:s,mainPoolId:i.id,mainPoolName:i.name};Array.isArray(i.inspirations)||(i.inspirations=[]),i.inspirations.unshift(r),i.updateTime=Date.now()}}}else{if(!f.value)return;Array.isArray(f.value.inspirations)||(f.value.inspirations=[]),f.value.inspirations.unshift(t),f.value.updateTime=Date.now()}await b()&&(m.success("灵感已保存"),ze.value=!1,R.value={content:"",rating:80,isUsed:!1})},at=()=>{if(h.value.length<Z.value){m.warning(`需要至少${Z.value}个场景才能进行随机抽取`);return}const t=[...h.value].sort(()=>.5-Math.random());ge.value=t.slice(0,Z.value),R.value={content:"",rating:80,isUsed:!1},ze.value=!0},dt=()=>{if(!h.value.length){m.warning("暂无场景卡片，请先创建场景");return}je.value=!0},ie=c(null),ct=async()=>{try{const t=JSON.stringify(u.value,null,2),e=await window.pywebview.api.book_controller.export_scenes(t),s=typeof e=="string"?JSON.parse(e):e;s.status==="success"?m.success("导出成功"):m.error(`导出失败: ${s.message}`)}catch(t){m.error(`导出出错: ${t.message}`)}},nt=()=>{ie.value.click()},v=async t=>{const e=t.target.files[0];if(!e)return;const s=new FileReader;s.onload=async d=>{try{const i=JSON.parse(d.target.result);if(!Array.isArray(i.pools))throw new Error("无效的场景数据格式");Je.confirm("导入将覆盖当前场景数据，是否继续？","导入确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",appendTo:Q.value||document.body}).then(async()=>{let r=1;i.pools.forEach($=>{Array.isArray($.scenes)&&$.scenes.forEach(P=>{P.zIndex?r=Math.max(r,P.zIndex+1):P.zIndex=r++})}),u.value=i,D.value=r;try{await b(),m.success("场景数据导入成功")}catch{m.error("保存失败，请重试"),await se()}}).catch(()=>{})}catch(i){m.error(`导入失败：${i.message}`)}t.target.value=""},s.readAsText(e)},p=Ge({name:""}),C=c(!1),T=t=>{t.stopPropagation();const e=document.querySelector(".pool-select input");e&&e.blur(),setTimeout(()=>{p.name="",C.value=!0,setTimeout(()=>{const s=document.querySelector(".el-dialog input");s&&s.focus()},0)},0)},U=()=>{if(!p.name.trim()){m.warning("请输入卡池名称");return}Qe(p.name.trim()),C.value=!1,m.success("卡池创建成功")},ye=async()=>{if(u.value={pools:[],currentPoolId:null},!!Y.value)try{await se()}catch(t){console.error("加载场景失败:",t),m.error(`加载场景失败: ${t.message}`)}},O=c(1),j=c(5),G=W(()=>$e.value.length),ue=W(()=>{const t=(O.value-1)*j.value,e=t+j.value;return $e.value.slice(t,e)}),x=t=>{j.value=t,O.value=1,E.value=null},F=t=>{O.value=t,E.value=null},ll=t=>{E.value=E.value===t?null:t};xt(Ye,t=>{t||(E.value=null,O.value=1)}),xt([Y,()=>u.value.currentPoolId],()=>{E.value=null,O.value=1});const It=t=>t>=90?"danger":t>=80?"success":t>=60?"warning":"info",Pt=t=>{Je.confirm(`确定要删除卡池"${t.name}"吗？此操作将永久删除该卡池及其所有场景和灵感记录。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger",appendTo:Q.value||document.body}).then(()=>{ol(t)}).catch(()=>{})},ol=t=>{const e=u.value.pools.findIndex(s=>s.id===t.id);e!==-1&&(u.value.pools.splice(e,1),t.id===u.value.currentPoolId&&(u.value.currentPoolId=u.value.pools[0]?.id||null),b().then(()=>{m.success("卡池删除成功")}))},al=t=>{Je.confirm("确定要删除这条灵感记录吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger",appendTo:Q.value||document.body}).then(()=>{nl(t)}).catch(()=>{})},nl=t=>{const e=u.value.pools.find(s=>s.id===t.poolId);if(e&&Array.isArray(e.inspirations)){const s=e.inspirations.findIndex(d=>d.timestamp===t.timestamp);s!==-1&&(e.inspirations.splice(s,1),e.updateTime=Date.now(),b().then(()=>{m.success("灵感记录已删除"),E.value===t&&(E.value=null)}).catch(()=>{m.error("删除失败，请重试")}))}},sl=()=>{p.name=""},il=()=>{w.id="",w.title="",w.description="",w.tags=[]},qe=()=>{xe.value=!1,il()},ul=t=>{t.target===t.currentTarget&&qe()},rl=async t=>{try{t==="exportAll"?await ct():t==="exportPool"&&await dl()}catch(e){m.error(`导出失败: ${e.message}`)}},dl=async()=>{if(!f.value||f.value.isVirtual){m.warning("请选择一个有效的卡池");return}try{const t={id:f.value.id,name:f.value.name,scenes:f.value.scenes,createTime:f.value.createTime,updateTime:f.value.updateTime};await window.pywebview.api.copy_to_clipboard(JSON.stringify(t,null,2)),m.success("卡池数据已复制到剪贴板")}catch(t){console.error("导出卡池失败:",t),m.error("导出失败，请重试")}},cl=t=>{t==="importFile"?nt():t==="importJson"?vl():t==="mergeJson"&&ml()},Be=c(!1),_e=c({jsonContent:""}),vl=()=>{_e.value.jsonContent="",Be.value=!0},vt=async()=>{if(!_e.value.jsonContent.trim()){m.warning("请输入要导入的JSON数据");return}try{const t=JSON.parse(_e.value.jsonContent);if(!t.scenes||!Array.isArray(t.scenes))throw new Error("无效的场景数据格式");const e=_e.value.jsonContent;Be.value=!1;try{await Je.confirm("导入将覆盖当前卡池的场景数据，是否继续？","导入确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",appendTo:Q.value||document.body}),f.value&&!f.value.isVirtual&&(f.value.scenes=t.scenes,f.value.updateTime=Date.now(),await b(),m.success("场景数据导入成功"))}catch{_e.value.jsonContent=e,Be.value=!0}}catch(t){console.error("导入JSON失败:",t),m.error(`导入失败：${t.message}`)}},pl=()=>{_e.value.jsonContent=""},Le=c(!1),he=c({jsonContent:""}),ml=()=>{he.value.jsonContent="",Le.value=!0},pt=async()=>{if(!he.value.jsonContent.trim()){m.warning("请输入要融合的JSON数据");return}try{const t=JSON.parse(he.value.jsonContent);if(!t.scenes||!Array.isArray(t.scenes))throw new Error("无效的场景数据格式");const e=he.value.jsonContent;Le.value=!1;try{if(await Je.confirm("导入将添加新场景到当前卡池，可能会根据ID去除重复场景。是否继续？","融合确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",appendTo:Q.value||document.body}),f.value&&!f.value.isVirtual){Array.isArray(f.value.scenes)||(f.value.scenes=[]);const s=f.value.scenes.map(i=>i.id),d=t.scenes.filter(i=>!s.includes(i.id));d.length>0?(d.forEach(i=>{i.position||(i.position={left:Math.random()*560,top:Math.random()*320}),D.value++,i.zIndex=D.value}),f.value.scenes=[...f.value.scenes,...d],f.value.updateTime=Date.now(),await b(),m.success(`成功融合 ${d.length} 个新场景到当前卡池`)):m.info("没有新的场景需要融合，所有场景ID已存在")}}catch{he.value.jsonContent=e,Le.value=!0}}catch(t){console.error("融合JSON失败:",t),m.error(`融合失败：${t.message}`)}},fl=()=>{he.value.jsonContent=""},gl=W(()=>[...u.value.pools]),Re=c(!1),yl=t=>{t.stopPropagation(),t.preventDefault();const e=document.querySelector(".el-select__popper");e&&(e.classList.add("el-popper--hidden"),e.classList.remove("el-popper--visible"));const s=document.querySelector(".pool-select .el-select");if(s){s.classList.remove("is-focus");const d=s.querySelector("input");d&&d.blur()}setTimeout(()=>{Re.value=!0},50)},_l=()=>{document.body.style.overflow="hidden",Ve(()=>{Pe.value&&Pe.value.setScrollTop(0)})},hl=()=>{document.body.style.overflow="",Ie.value=""},bl=async()=>{try{await b(),m.success("卡池顺序已保存")}catch(t){m.error("保存顺序失败，请重试"),console.error("保存卡池顺序失败:",t)}},wl=t=>{u.value.currentPoolId=t.id,Re.value=!1,m.success(`已切换到卡池：${t.name}`)},Ke=c(!1),re=Ge({id:"",name:""}),Et=c(null),Cl=t=>{Et.value=t,re.id=t.id,re.name=t.name,Ke.value=!0,Ve(()=>{const e=document.querySelector(".rename-pool-dialog .el-input__inner");e&&(e.focus(),e.select())})},Tt=async()=>{if(!re.name.trim()){m.warning("请输入卡池名称");return}const t=u.value.pools.find(e=>e.id===re.id);if(t){t.name=re.name.trim(),t.updateTime=Date.now();try{await b(),m.success("卡池已重命名"),Ke.value=!1}catch(e){m.error("重命名失败，请重试"),console.error("重命名卡池失败:",e)}}},kl=()=>{Et.value=null,re.id="",re.name=""},Ie=c(""),Pe=c(null),xl=async t=>{if(t>0){const e=u.value.pools.splice(t,1)[0];u.value.pools.unshift(e);try{await b(),m.success("卡池已置顶")}catch(s){m.error("操作失败，请重试"),console.error("卡池置顶失败:",s)}}},Sl=()=>{if(Pe.value){let i=function($){const P=$-d,ee=Math.min(P/s,1),Se=1-Math.pow(1-ee,3);t.scrollTop=e*(1-Se),ee<1&&requestAnimationFrame(i)};const t=Pe.value.$el.querySelector(".el-scrollbar__wrap");if(!t)return;const e=t.scrollTop;if(e===0)return;const s=Math.min(Math.max(e*.5,300),800),d=performance.now();requestAnimationFrame(i);const r=document.querySelector(".scroll-top-btn");r&&(r.classList.add("is-scrolling"),setTimeout(()=>{r.classList.remove("is-scrolling")},300))}},Ml=W(()=>{let t=[...u.value.pools];if(Ie.value.trim()){const e=Ie.value.toLowerCase().trim();t=t.filter(s=>s.name.toLowerCase().includes(e))}return t}),Vl=()=>{Ve(()=>{Pe.value&&Pe.value.setScrollTop(0)})},Dl=t=>{if(!Ie.value.trim())return!1;const e=Ie.value.toLowerCase().trim();return t.name.toLowerCase().includes(e)},Q=c(null),We=c(null),de=c(0),ce=c(0),I=c(1),Ue=c(!1),mt=c(0),ft=c(0),Ee=c(!0),He=c(null),J=c(null),gt=c(null),yt=c(!1),Il=c(0),_t=c({x:0,y:0}),Pl=W(()=>({transform:`translate(${de.value}px, ${ce.value}px) scale(${I.value})`}));kt(()=>{h.value&&h.value.length>0&&(h.value=h.value.map(jl),b()),Ee.value=!0,setTimeout(()=>{Ee.value=!1},1e3)});const El=t=>{t.button===2&&(Ue.value=!0,mt.value=t.clientX-de.value,ft.value=t.clientY-ce.value,Ee.value=!0,We.value&&(We.value.style.cursor="grabbing"),t.preventDefault())},Tl=t=>{Ue.value&&(de.value=t.clientX-mt.value,ce.value=t.clientY-ft.value,t.preventDefault())},$l=t=>{Ue.value&&(Ue.value=!1,We.value&&(We.value.style.cursor="default"),setTimeout(()=>{Ee.value=!1},1e3))},zl=t=>{t.preventDefault();const e=Q.value.getBoundingClientRect(),s=t.clientX-e.left,d=t.clientY-e.top,i=(s-de.value)/I.value,r=(d-ce.value)/I.value,$=.1;t.deltaY<0?I.value=Math.min(2,I.value+$):I.value=Math.max(.3,I.value-$);const P=(s-de.value)/I.value,ee=(d-ce.value)/I.value;de.value+=(P-i)*I.value,ce.value+=(ee-r)*I.value,Ee.value=!0,setTimeout(()=>{Ee.value=!1},1e3)},Yl=()=>{I.value=Math.min(2,I.value+.1)},Xl=()=>{I.value=Math.max(.3,I.value-.1)},Ol=()=>{if(!h.value.length){m.info("没有场景卡片需要排版");return}if(f.value?.isVirtual){m.warning('不能在"全部场景"卡池中进行自动排版');return}const t=Q.value;if(!t)return;const e=t.getBoundingClientRect(),s=e.width,d=e.height,i=240,r=280,$=20,P=16,ee=s/I.value-$*2,Se=d/I.value-$*2,q=Math.floor((ee+P)/(i+P)),B=Math.max(1,Math.min(q,h.value.length)),ae=Math.ceil(h.value.length/B),Ae=B*i+(B-1)*P,Ze=ae*r+(ae-1)*P,wt=(ee-Ae)/2+$,be=(Se-Ze)/2+$;h.value.forEach((X,te)=>{const we=Math.floor(te/B),Te=te%B;X.x=wt+Te*(i+P),X.y=be+we*(r+P)}),b().then(()=>{m.success(`已自动排版 ${h.value.length} 个场景卡片`)}).catch(()=>{m.error("自动排版失败，请重试")}),Ve(()=>{if(h.value.length>0){let X=1/0,te=1/0,we=-1/0,Te=-1/0;h.value.forEach(Yt=>{const Xt=Yt.x||0,Ot=Yt.y||0,Fl=i,ql=r;X=Math.min(X,Xt),te=Math.min(te,Ot),we=Math.max(we,Xt+Fl),Te=Math.max(Te,Ot+ql)});const Ct=(X+we)/2,a=(te+Te)/2,V=e.width/2,ve=e.height/2;de.value=V-Ct*I.value,ce.value=ve-a*I.value}})},Bl=()=>{de.value=0,ce.value=0,I.value=1,Q.value&&Ve(()=>{const e=Q.value.getBoundingClientRect();if(h.value.length>0){let s=1/0,d=1/0,i=-1/0,r=-1/0;h.value.forEach(q=>{const B=q.x||0,ae=q.y||0,Ae=240,Ze=280;s=Math.min(s,B),d=Math.min(d,ae),i=Math.max(i,B+Ae),r=Math.max(r,ae+Ze)});const $=(s+i)/2,P=(d+r)/2,ee=e.width/2,Se=e.height/2;de.value=ee-$,ce.value=Se-P}})},Ll=(t,e)=>{He.value=t,e.stopPropagation()},Rl=()=>{He.value=null},Ul=(t,e)=>{e.button!==0||Ue.value||(Il.value=Date.now(),_t.value={x:e.clientX,y:e.clientY},yt.value=!1,He.value=t,gt.value&&(clearTimeout(gt.value),gt.value=null),document.addEventListener("mousemove",ht),document.addEventListener("mouseup",bt))},ht=t=>{if(yt.value)return;const e=Math.abs(t.clientX-_t.value.x),s=Math.abs(t.clientY-_t.value.y);Math.sqrt(e*e+s*s)>sn&&!J.value&&Al(t),J.value&&$t(t)},bt=t=>{document.removeEventListener("mousemove",ht),document.removeEventListener("mouseup",bt),J.value&&zt()},Al=t=>{const e=He.value;if(!e)return;J.value=e;const s=document.getElementById(`scene-card-${e}`);s&&s.classList.add("dragging");const d=h.value.find(r=>r.id===e),i=document.getElementById(`scene-card-wrapper-${e}`);if(d&&i){const r=i.getBoundingClientRect(),$=t.clientX-r.left,P=t.clientY-r.top;mt.value=t.clientX,ft.value=t.clientY,d.startX=d.x!==void 0?d.x:d.position?.left||0,d.startY=d.y!==void 0?d.y:d.position?.top||0,d.mouseOffsetX=$,d.mouseOffsetY=P}},Jl=(t,e)=>{if(yt.value=!0,J.value){const s=document.getElementById(`scene-card-${J.value}`);s&&s.classList.remove("dragging"),J.value=null}document.removeEventListener("mousemove",ht),document.removeEventListener("mouseup",bt),ut(e,t)},$t=t=>{if(J.value){const e=h.value.find(s=>s.id===J.value);if(e){const s=Q.value.getBoundingClientRect(),d=(t.clientX-s.left-de.value)/I.value,i=(t.clientY-s.top-ce.value)/I.value;e.x=d-e.mouseOffsetX/I.value,e.y=i-e.mouseOffsetY/I.value}}},zt=()=>{if(J.value){const t=document.getElementById(`scene-card-${J.value}`);t&&t.classList.remove("dragging"),oe(J.value),b(),J.value=null,document.removeEventListener("mousemove",$t),document.removeEventListener("mouseup",zt)}},Nl=t=>{const e=t.x!==void 0?t.x:t.position?.left||0,s=t.y!==void 0?t.y:t.position?.top||0;return{left:`${e}px`,top:`${s}px`,zIndex:t.zIndex||0}},jl=t=>(t.position?(t.x=t.position.left||0,t.y=t.position.top||0):(typeof t.x>"u"||typeof t.y>"u")&&(t.x=Math.random()*500,t.y=Math.random()*300),t.zIndex||(t.zIndex=1),t);return(t,e)=>{const s=el,d=Qt,i=Vt,r=Dt,$=Hl,P=eo,ee=Ql,Se=oo,q=Gt,B=Zt,ae=Ht,Ae=Wl("Right"),Ze=ro,wt=co,be=tl,X=Jt,te=vo,we=po,Te=mo,Ct=fo;return g(),S("div",Oo,[n("div",Bo,[l(d,{modelValue:Y.value,"onUpdate:modelValue":e[0]||(e[0]=a=>Y.value=a),class:"book-select",size:"large",placeholder:"请选择书籍",onChange:ye},{default:o(()=>[(g(!0),S(L,null,N(_(me).bookList,a=>(g(),K(s,{key:a.id,label:a.title,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),l(d,{modelValue:u.value.currentPoolId,"onUpdate:modelValue":e[1]||(e[1]=a=>u.value.currentPoolId=a),class:"pool-select",size:"large",placeholder:"请选择场景卡池",disabled:!Y.value},{prefix:o(()=>[l(r,{class:"create-pool-btn",link:"",onClick:M(T,["stop"]),disabled:!Y.value},{default:o(()=>[l(i,null,{default:o(()=>[l(_(Bt))]),_:1})]),_:1},8,["disabled"])]),default:o(()=>[l(s,{key:"all",label:"全部场景",value:"all"},{default:o(()=>[n("div",Lo,[e[32]||(e[32]=n("span",{class:"pool-name"},"全部场景",-1)),n("span",Ro,k(Ne.value.scenes.length)+"个场景 ",1)])]),_:1}),l($,{"content-position":"center"},{default:o(()=>[e[33]||(e[33]=y(" 其他卡池 ")),l(r,{class:"manage-pools-btn",link:"",onClick:M(yl,["stop"]),disabled:!Y.value},{default:o(()=>[l(i,null,{default:o(()=>[l(_(Zl))]),_:1})]),_:1},8,["disabled"])]),_:1}),(g(!0),S(L,null,N(gl.value,a=>(g(),K(s,{key:a.id,label:a.name,value:a.id},{default:o(()=>[n("div",Uo,[n("span",Ao,k(a.name),1),n("span",Jo,k(a.scenes.length)+"个场景 | "+k(Fe(a.updateTime)),1),u.value.pools.length>1?(g(),K(r,{key:0,class:"delete-pool-btn",type:"danger",link:"",onClick:M(V=>Pt(a),["stop"])},{default:o(()=>[l(i,null,{default:o(()=>[l(_(st))]),_:1})]),_:2},1032,["onClick"])):Me("",!0)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),l(r,{type:"primary",onClick:ot,size:"large",disabled:!f.value},{default:o(()=>[l(i,null,{default:o(()=>[l(_(Bt))]),_:1}),e[34]||(e[34]=y(" 创建场景 "))]),_:1},8,["disabled"]),l(d,{modelValue:Z.value,"onUpdate:modelValue":e[2]||(e[2]=a=>Z.value=a),class:"draw-count-select",size:"large",disabled:h.value.length<2},{default:o(()=>[(g(!0),S(L,null,N(Math.min(5,h.value.length),a=>(g(),K(s,{key:a,label:`抽取${a}个场景`,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),l(r,{onClick:at,size:"large",disabled:h.value.length<2},{default:o(()=>e[35]||(e[35]=[y(" 随机抽取 ")])),_:1},8,["disabled"]),l(Se,{onCommand:rl,trigger:"click"},{dropdown:o(()=>[l(ee,null,{default:o(()=>[l(P,{command:"exportAll"},{default:o(()=>[n("div",No,[l(i,null,{default:o(()=>[l(_(to))]),_:1}),e[37]||(e[37]=n("span",null,"导出全部场景",-1))])]),_:1}),l(P,{command:"exportPool",disabled:!f.value||f.value.isVirtual},{default:o(()=>[n("div",jo,[l(i,null,{default:o(()=>[l(_(lo))]),_:1}),e[38]||(e[38]=n("span",null,"复制当前卡池",-1))])]),_:1},8,["disabled"])]),_:1})]),default:o(()=>[l(r,{size:"large",disabled:h.value.length===0},{default:o(()=>[l(i,null,{default:o(()=>[l(_(Gl))]),_:1}),e[36]||(e[36]=y(" 导出场景 ")),l(i,{class:"el-icon--right"},{default:o(()=>[l(_(Lt))]),_:1})]),_:1},8,["disabled"])]),_:1}),l(Se,{onCommand:cl,trigger:"click"},{dropdown:o(()=>[l(ee,null,{default:o(()=>[l(P,{command:"importFile"},{default:o(()=>[n("div",Fo,[l(i,null,{default:o(()=>[l(_(no))]),_:1}),e[40]||(e[40]=n("span",null,"导入全部场景",-1))])]),_:1}),l(P,{command:"importJson",disabled:!f.value||f.value.isVirtual},{default:o(()=>[n("div",qo,[l(i,null,{default:o(()=>[l(_(so))]),_:1}),e[41]||(e[41]=n("span",null,"导入到当前卡池",-1))])]),_:1},8,["disabled"]),l(P,{command:"mergeJson",disabled:!f.value||f.value.isVirtual},{default:o(()=>[n("div",Ko,[l(i,null,{default:o(()=>[l(_(Rt))]),_:1}),e[42]||(e[42]=n("span",null,"融合到当前卡池",-1))])]),_:1},8,["disabled"])]),_:1})]),default:o(()=>[l(r,{size:"large"},{default:o(()=>[l(i,null,{default:o(()=>[l(_(ao))]),_:1}),e[39]||(e[39]=y(" 导入场景 ")),l(i,{class:"el-icon--right"},{default:o(()=>[l(_(Lt))]),_:1})]),_:1})]),_:1}),n("input",{type:"file",ref_key:"fileInput",ref:ie,style:{display:"none"},accept:".json",onChange:v},null,544),l(r,{onClick:e[3]||(e[3]=a=>Ye.value=!0),size:"large",disabled:!Y.value},{default:o(()=>[l(i,null,{default:o(()=>[l(_(io))]),_:1}),e[43]||(e[43]=y(" 灵感历史 "))]),_:1},8,["disabled"])]),n("div",{class:"cards-grid",onMousedown:El,onMouseup:$l,onMousemove:Tl,onContextmenu:e[4]||(e[4]=M(()=>{},["prevent"])),onWheel:zl,ref_key:"cardsGridRef",ref:Q},[n("div",{class:Ce(["drag-hint",{visible:Ee.value}])},e[44]||(e[44]=[y(" 按住 "),n("span",{class:"key-hint"},"右键",-1),y(" 拖动画布 | 使用 "),n("span",{class:"key-hint"},"滚轮",-1),y(" 缩放 ")]),2),n("div",{class:Ce(["infinite-canvas",{dragging:Ue.value}]),style:it(Pl.value),ref_key:"infiniteCanvasRef",ref:We},[(g(!0),S(L,null,N(h.value,a=>(g(),S("div",{key:a.id,class:"scene-card-wrapper",id:`scene-card-wrapper-${a.id}`,style:it(Nl(a))},[n("div",{class:"scene-card-interaction-area",onMousedown:M(V=>Ul(a.id,V),["stop"]),onDblclick:M(V=>Jl(a,V),["stop"]),onMouseover:M(V=>Ll(a.id,V),["stop"]),onMouseleave:M(V=>Rl(a.id),["stop"])},[l(St,{title:a.title,description:a.description,tags:a.tags,onDelete:V=>Xe(a),class:Ce({active:He.value===a.id,dragging:J.value===a.id}),id:`scene-card-${a.id}`},null,8,["title","description","tags","onDelete","class","id"])],40,Ho)],12,Wo))),128))],6),n("div",Zo,[n("div",{class:"control-btn",onClick:Yl},[l(i,null,{default:o(()=>[l(_(jt))]),_:1})]),n("div",Go,k(Math.round(I.value*100))+"%",1),n("div",{class:"control-btn",onClick:Xl},[l(i,null,{default:o(()=>[l(_(Ft))]),_:1})]),n("div",{class:"control-btn",onClick:Ol,title:"自动排版"},[l(i,null,{default:o(()=>[l(_(qt))]),_:1})]),n("div",{class:"control-btn",onClick:Bl},[l(i,null,{default:o(()=>[l(_(Kt))]),_:1})]),n("div",{class:"control-btn",onClick:dt},[l(i,null,{default:o(()=>[l(_(uo))]),_:1})])])],544),xe.value?(g(),S("div",{key:0,class:"custom-dialog-overlay",onClick:ul},[n("div",{class:"custom-dialog custom-dialog-medium",onClick:e[8]||(e[8]=M(()=>{},["stop"]))},[n("div",Qo,[n("h3",null,k(fe.value?"编辑场景":"创建场景"),1),n("button",{class:"custom-dialog-close",onClick:qe},"×")]),n("div",ea,[l(ae,{model:w,"label-width":"100px",class:"scene-form"},{default:o(()=>[l(B,{label:"场景标题"},{default:o(()=>[l(q,{modelValue:w.title,"onUpdate:modelValue":e[5]||(e[5]=a=>w.title=a),placeholder:"请输入场景标题",ref:"sceneTitleInput",onKeyup:[pe(M(Oe,["prevent"]),["enter"]),pe(M(qe,["prevent"]),["esc"])],autofocus:"",class:"scene-title-input"},null,8,["modelValue","onKeyup"])]),_:1}),l(B,{label:"场景描述"},{default:o(()=>[l(q,{modelValue:w.description,"onUpdate:modelValue":e[6]||(e[6]=a=>w.description=a),type:"textarea",rows:6,resize:"none",placeholder:"请描述场景内容",class:"scene-description-input",onKeyup:pe(M(qe,["prevent"]),["esc"])},null,8,["modelValue","onKeyup"])]),_:1}),l(B,{label:"场景标签"},{default:o(()=>[l(d,{modelValue:w.tags,"onUpdate:modelValue":e[7]||(e[7]=a=>w.tags=a),multiple:"",filterable:"","allow-create":"","default-first-option":"",placeholder:"请选择或创建标签",class:"scene-tags-select"},{default:o(()=>[(g(!0),S(L,null,N(lt.value,a=>(g(),K(s,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),n("div",ta,[l(r,{onClick:qe,class:"cancel-btn"},{default:o(()=>e[45]||(e[45]=[y("取消")])),_:1}),l(r,{type:"primary",onClick:Oe,class:"save-btn"},{default:o(()=>e[46]||(e[46]=[y("保存场景")])),_:1})])])])):Me("",!0),l(be,{modelValue:ze.value,"onUpdate:modelValue":e[12]||(e[12]=a=>ze.value=a),title:"场景组合",width:"1200px",class:"draw-dialog"},{default:o(()=>[n("div",la,[n("div",oa,[n("div",aa,[n("div",na,[(g(!0),S(L,null,N(ge.value,(a,V)=>(g(),S("div",{key:a.id,class:"drawn-card-item"},[n("div",sa,"场景 "+k(V+1),1),l(St,{title:a.title,description:a.description,tags:a.tags,class:"compact-card"},null,8,["title","description","tags"]),V<ge.value.length-1?(g(),S("div",ia,[l(i,null,{default:o(()=>[l(Ae)]),_:1})])):Me("",!0)]))),128))]),n("div",ua,[l(i,null,{default:o(()=>[l(_(Rt))]),_:1}),e[47]||(e[47]=n("p",null,"思考这些场景之间可能存在的联系...",-1))])]),n("div",ra,[n("div",da,[e[51]||(e[51]=n("div",{class:"inspiration-label"},"记录你的灵感：",-1)),l(q,{modelValue:R.value.content,"onUpdate:modelValue":e[9]||(e[9]=a=>R.value.content=a),type:"textarea",rows:10,placeholder:"这些场景之间会发生什么有趣的故事？它们如何推动剧情发展？记录下你的想法...",resize:"none"},null,8,["modelValue"]),n("div",ca,[n("div",va,[e[48]||(e[48]=n("span",{class:"rating-label"},"灵感评分：",-1)),l(Ze,{modelValue:R.value.rating,"onUpdate:modelValue":e[10]||(e[10]=a=>R.value.rating=a),min:1,max:100,"format-tooltip":a=>`${a}分`,"show-input":"","input-size":"small",class:"rating-slider"},{marks:o(()=>[(g(),S(L,null,N([20,40,60,80,100],a=>n("span",{key:a,class:"mark-label"},k(a),1)),64))]),_:1},8,["modelValue","format-tooltip"])]),l(wt,{modelValue:R.value.isUsed,"onUpdate:modelValue":e[11]||(e[11]=a=>R.value.isUsed=a)},{default:o(()=>e[49]||(e[49]=[y("已使用此灵感")])),_:1},8,["modelValue"])]),n("div",pa,[l(r,{type:"primary",onClick:rt,disabled:!R.value.content.trim()},{default:o(()=>[l(i,null,{default:o(()=>[l(_(Ut))]),_:1}),e[50]||(e[50]=y(" 保存灵感 "))]),_:1},8,["disabled"])])])])])])]),_:1},8,["modelValue"]),l(be,{modelValue:Ye.value,"onUpdate:modelValue":e[13]||(e[13]=a=>Ye.value=a),class:"history-dialog",fullscreen:!0,"show-close":!1,modal:!0,"close-on-click-modal":!1,"close-on-press-escape":!0,"lock-scroll":!0,"destroy-on-close":!1},{header:o(({close:a,titleId:V,titleClass:ve})=>[n("div",ma,[n("h4",{id:V,class:Ce(ve)},[e[52]||(e[52]=y(" 灵感历史记录 ")),n("span",ga,"(共 "+k(G.value)+" 条)",1)],10,fa),l(r,{class:"close-btn",onClick:a},{default:o(()=>[l(i,null,{default:o(()=>[l(_(Wt))]),_:1})]),_:2},1032,["onClick"])])]),default:o(()=>[n("div",ya,[n("div",_a,[n("div",ha,[n("div",ba,[ue.value.length>0?(g(!0),S(L,{key:0},N(ue.value,a=>(g(),S("div",{key:a.timestamp,class:Ce(["history-item",{active:E.value===a}]),onClick:V=>ll(a)},[n("div",Ca,[n("div",ka,[n("div",xa,[n("span",Sa,k(Fe(a.timestamp)),1),l(X,{size:"small",type:"info",class:"pool-tag"},{default:o(()=>[y(k(a.poolName),1)]),_:2},1024),a.fromAllPool?(g(),S(L,{key:0},[l(X,{size:"small",type:"success",class:"pool-tag"},{default:o(()=>e[53]||(e[53]=[y("全部场景")])),_:1}),l(te,{effect:"dark",placement:"top"},{content:o(()=>[n("div",Ma,[e[54]||(e[54]=n("div",{class:"tooltip-title"},"关联卡池：",-1)),(g(!0),S(L,null,N(a.relatedPools,V=>(g(),S("div",{key:V.name,class:"related-pool-item"},k(V.name)+" ("+k(V.sceneCount)+"个场景) ",1))),128))])]),default:o(()=>[l(X,{size:"small",type:"warning",class:"pool-tag"},{default:o(()=>[y(k(a.relatedPools.length)+"个卡池 ",1)]),_:2},1024)]),_:2},1024)],64)):Me("",!0)]),n("div",Va,k(a.content),1)]),n("div",Da,[n("div",Ia,[l(X,{type:It(a.rating),size:"small"},{default:o(()=>[y(k(a.rating)+"分 ",1)]),_:2},1032,["type"]),l(X,{type:a.isUsed?"success":"info",size:"small"},{default:o(()=>[y(k(a.isUsed?"已使用":"未使用"),1)]),_:2},1032,["type"])]),l(r,{type:"danger",size:"small",link:"",class:"delete-history-btn",onClick:M(V=>al(a),["stop"])},{default:o(()=>[l(i,null,{default:o(()=>[l(_(st))]),_:1})]),_:2},1032,["onClick"])])])],10,wa))),128)):(g(),K(we,{key:1,description:"暂无灵感记录"}))])]),n("div",Pa,[E.value?(g(),S(L,{key:0},[n("div",Ea,[n("div",Ta,[e[55]||(e[55]=y(" 场景组合 ")),l(X,{size:"small",type:"info",class:"scene-count"},{default:o(()=>[y(k(E.value.scenes.length)+"个场景 ",1)]),_:1})]),n("div",$a,[(g(!0),S(L,null,N(E.value.scenes,(a,V)=>(g(),S("div",{key:a.id,class:"history-scene"},[n("div",za,"场景 "+k(V+1),1),n("div",Ya,k(a.title),1),a.description?(g(),S("div",Xa,k(a.description),1)):Me("",!0),n("div",Oa,[(g(!0),S(L,null,N(a.tags,ve=>(g(),K(X,{key:ve,size:"small",class:"scene-tag"},{default:o(()=>[y(k(ve),1)]),_:2},1024))),128))]),V<E.value.scenes.length-1?(g(),S("div",Ba,[l(i,null,{default:o(()=>[l(Ae)]),_:1})])):Me("",!0)]))),128))])]),n("div",La,[n("div",Ra,[n("div",Ua,[e[56]||(e[56]=y(" 灵感内容 ")),l(X,{type:It(E.value.rating),size:"small"},{default:o(()=>[y(k(E.value.rating)+"分 ",1)]),_:1},8,["type"]),l(X,{type:E.value.isUsed?"success":"info",size:"small"},{default:o(()=>[y(k(E.value.isUsed?"已使用":"未使用"),1)]),_:1},8,["type"])])]),n("div",Aa,k(E.value.content),1)])],64)):(g(),S("div",Ja,[l(we,{description:"点击左侧记录查看详情"})]))])]),n("div",Na,[n("div",ja,[l(Te,{"current-page":O.value,"page-size":j.value,total:G.value,"page-sizes":[5,10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:x,onCurrentChange:F},null,8,["current-page","page-size","total"])])])])]),_:1},8,["modelValue"]),l(be,{modelValue:C.value,"onUpdate:modelValue":e[16]||(e[16]=a=>C.value=a),title:"创建场景卡池",width:"400px",onClose:sl},{footer:o(()=>[n("span",Fa,[l(r,{onClick:e[15]||(e[15]=a=>C.value=!1)},{default:o(()=>e[57]||(e[57]=[y("取消")])),_:1}),l(r,{type:"primary",onClick:U},{default:o(()=>e[58]||(e[58]=[y("确定")])),_:1})])]),default:o(()=>[l(ae,{model:p,"label-width":"80px",onSubmit:M(U,["prevent"]),onKeyup:pe(U,["enter"])},{default:o(()=>[l(B,{label:"卡池名称"},{default:o(()=>[l(q,{modelValue:p.name,"onUpdate:modelValue":e[14]||(e[14]=a=>p.name=a),placeholder:"请输入卡池名称",ref:"poolNameInput",onKeyup:pe(M(U,["prevent"]),["enter"]),autofocus:""},null,8,["modelValue","onKeyup"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(be,{modelValue:Be.value,"onUpdate:modelValue":e[19]||(e[19]=a=>Be.value=a),title:"导入JSON",width:"400px",onClose:pl},{footer:o(()=>[n("span",qa,[l(r,{onClick:e[18]||(e[18]=a=>Be.value=!1)},{default:o(()=>e[59]||(e[59]=[y("取消")])),_:1}),l(r,{type:"primary",onClick:vt},{default:o(()=>e[60]||(e[60]=[y("导入")])),_:1})])]),default:o(()=>[l(ae,{model:_e.value,"label-width":"80px",onSubmit:M(vt,["prevent"]),onKeyup:pe(vt,["enter"])},{default:o(()=>[l(B,{label:"JSON内容"},{default:o(()=>[l(q,{modelValue:_e.value.jsonContent,"onUpdate:modelValue":e[17]||(e[17]=a=>_e.value.jsonContent=a),type:"textarea",rows:10,placeholder:"请输入要导入的JSON内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(be,{modelValue:Le.value,"onUpdate:modelValue":e[22]||(e[22]=a=>Le.value=a),title:"融合JSON到当前卡池",width:"400px",onClose:fl},{footer:o(()=>[n("span",Ka,[l(r,{onClick:e[21]||(e[21]=a=>Le.value=!1)},{default:o(()=>e[61]||(e[61]=[y("取消")])),_:1}),l(r,{type:"primary",onClick:pt},{default:o(()=>e[62]||(e[62]=[y("融合")])),_:1})])]),default:o(()=>[l(ae,{model:he.value,"label-width":"80px",onSubmit:M(pt,["prevent"]),onKeyup:pe(pt,["enter"])},{default:o(()=>[l(B,{label:"JSON内容"},{default:o(()=>[l(q,{modelValue:he.value.jsonContent,"onUpdate:modelValue":e[20]||(e[20]=a=>he.value.jsonContent=a),type:"textarea",rows:10,placeholder:"请输入要融合的JSON内容，将添加新场景到当前卡池"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(be,{modelValue:Re.value,"onUpdate:modelValue":e[27]||(e[27]=a=>Re.value=a),title:"管理场景卡池",width:"600px","close-on-click-modal":!1,"append-to-body":"","modal-append-to-body":!1,"lock-scroll":!0,"destroy-on-close":!1,class:"manage-pools-dialog",onOpen:_l,onClose:hl},{footer:o(()=>[n("span",an,[l(r,{onClick:e[25]||(e[25]=a=>Re.value=!1)},{default:o(()=>e[65]||(e[65]=[y("取消")])),_:1}),l(r,{type:"primary",onClick:e[26]||(e[26]=a=>Re.value=!1)},{default:o(()=>e[66]||(e[66]=[y("完成")])),_:1})])]),default:o(()=>[n("div",Wa,[n("div",Ha,[n("div",Za,[l(q,{modelValue:Ie.value,"onUpdate:modelValue":e[23]||(e[23]=a=>Ie.value=a),placeholder:"搜索卡池名称...",clearable:"","prefix-icon":"Search",onInput:Vl},null,8,["modelValue"])]),n("div",Ga,[l(r,{onClick:Sl,class:"scroll-top-btn",type:"default"},{default:o(()=>[l(i,null,{default:o(()=>[l(_(At))]),_:1}),e[63]||(e[63]=n("span",null,"返回顶部",-1))]),_:1})])]),e[64]||(e[64]=n("p",{class:"manage-pools-hint"},"拖拽卡池可调整显示顺序，或点击置顶按钮将卡池移到最前",-1)),l(Ct,{height:"400px",ref_key:"poolsScrollbar",ref:Pe,class:"pools-scrollbar"},{default:o(()=>[Ml.value.length===0?(g(),K(we,{key:0,description:"暂无卡池"})):(g(),K(_(ho),{key:1,modelValue:u.value.pools,"onUpdate:modelValue":e[24]||(e[24]=a=>u.value.pools=a),"item-key":"id","ghost-class":"ghost-pool",handle:".drag-handle",onEnd:bl},{item:o(({element:a,index:V})=>[n("div",{class:Ce(["pool-item",{"is-searched":Dl(a)}])},[l(i,{class:"drag-handle"},{default:o(()=>[l(_(go))]),_:1}),n("div",Qa,[n("div",en,k(a.name),1),n("div",tn,[l(X,{size:"small",class:"scene-count-tag"},{default:o(()=>[y(k(a.scenes.length)+"个场景",1)]),_:2},1024),n("span",ln,k(Fe(a.updateTime)),1)])]),n("div",on,[l(te,{content:"置顶此卡池",placement:"top",enterable:!1},{default:o(()=>[l(r,{type:"info",link:"",onClick:ve=>xl(V),class:"top-pool-btn",disabled:V===0},{default:o(()=>[l(i,null,{default:o(()=>[l(_(At))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024),l(te,{content:"选择此卡池",placement:"top",enterable:!1},{default:o(()=>[l(r,{type:"primary",link:"",onClick:ve=>wl(a),class:"select-pool-btn"},{default:o(()=>[l(i,null,{default:o(()=>[l(_(Ut))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),l(te,{content:"重命名",placement:"top",enterable:!1},{default:o(()=>[l(r,{type:"warning",link:"",onClick:ve=>Cl(a),class:"rename-pool-btn"},{default:o(()=>[l(i,null,{default:o(()=>[l(_(yo))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),l(te,{content:"删除",placement:"top",enterable:!1},{default:o(()=>[u.value.pools.length>1?(g(),K(r,{key:0,type:"danger",link:"",onClick:ve=>Pt(a),class:"delete-pool-btn"},{default:o(()=>[l(i,null,{default:o(()=>[l(_(st))]),_:1})]),_:2},1032,["onClick"])):Me("",!0)]),_:2},1024)])],2)]),_:1},8,["modelValue"]))]),_:1},512)])]),_:1},8,["modelValue"]),l(be,{modelValue:Ke.value,"onUpdate:modelValue":e[30]||(e[30]=a=>Ke.value=a),title:"重命名卡池",width:"400px","append-to-body":"","close-on-click-modal":!1,"lock-scroll":!0,class:"rename-pool-dialog",onClose:kl},{footer:o(()=>[n("span",nn,[l(r,{onClick:e[29]||(e[29]=a=>Ke.value=!1)},{default:o(()=>e[67]||(e[67]=[y("取消")])),_:1}),l(r,{type:"primary",onClick:Tt},{default:o(()=>e[68]||(e[68]=[y("确认")])),_:1})])]),default:o(()=>[l(ae,{model:re,"label-width":"80px",onSubmit:M(Tt,["prevent"])},{default:o(()=>[l(B,{label:"卡池名称"},{default:o(()=>[l(q,{modelValue:re.name,"onUpdate:modelValue":e[28]||(e[28]=a=>re.name=a),placeholder:"请输入卡池名称",ref:"renamePoolInput",autofocus:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(Xo,{visible:je.value,"onUpdate:visible":e[31]||(e[31]=a=>je.value=a),scenes:h.value,"book-title":ne.value,"current-pool":f.value,onCreateScene:ot,onDeleteScene:Xe,onSaveScenes:b},null,8,["visible","scenes","book-title","current-pool"])])}}},kn=Mt(un,[["__scopeId","data-v-130d17ff"]]);export{kn as default};
