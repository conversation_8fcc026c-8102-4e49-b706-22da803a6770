import{r as x,bB as Qe,aW as Xe,bk as Z,c as q,_ as Ye,a as Ze,w as _e,o as Ke,E as d,b as _,m as c,d as l,p as J,C as p,e as s,g as n,aI as et,B as tt,dP as lt,F as S,v as V,t as st,aP as he,aa as ot,a7 as me,$ as M,X as j,Y as B,n as R,a0 as ne,V as ge,dy as nt,a5 as at,h as fe,y as it,aD as ut,aG as rt,bJ as ye,G as ae,ar as ct,dQ as dt,dR as pt,W as mt,Z as ft,ac as be,bE as ke,bC as vt,bD as _t,af as ht,ad as gt,s as yt,ak as bt,al as kt,bA as Ct,k as wt,ax as Et,ay as xt,x as St,au as Vt,ah as Dt,bM as $t,R as ie,av as It}from"./entry-BIjVVog3.js";/* empty css                         *//* empty css                        *//* empty css                    *//* empty css                   *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                 */function Pt(){const f=x(!1);let b=null;const k=(g="加载中...")=>{b&&h(),f.value=!0,b=Qe.service({lock:!0,text:g,background:"rgba(0, 0, 0, 0.7)"})},h=()=>{b&&(b.close(),b=null),f.value=!1};return{isLoading:f,startLoading:k,stopLoading:h}}const Lt=Xe("inspiration",()=>{const f=Z({data:{categories:{},theme:[],volume:[],keyPoint:[],technique:[]},isLoaded:!1}),b=x(!1),k=x(null),h=q(()=>f.data),g=q(()=>f.isLoaded),w=q(()=>f.data.categories||{}),I=q(()=>b.value),L=q(()=>!!k.value),W=m=>f.data&&f.data[m]&&Array.isArray(f.data[m])?f.data[m]:[];async function y(){try{if(b.value=!0,k.value=null,console.log("开始请求故事灵感数据..."),!window.pywebview?.api?.book_controller?.get_story_inspiration)throw console.error("API不存在: book_controller.get_story_inspiration"),new Error("API方法不存在，无法加载灵感卡池数据");const m=await window.pywebview.api.book_controller.get_story_inspiration();console.log("API原始响应:",m);let i=null;if(m==null?(console.warn("API返回了null或undefined，将创建默认数据"),i={success:!0,message:"已创建默认灵感卡池数据",data:O()}):i=typeof m=="string"?JSON.parse(m):m,console.log("处理后的响应:",i),i&&i.success&&i.data)return f.data=i.data,f.isLoaded=!0,console.log("数据加载成功:",f.data),i.data;if(i&&i.status==="success"&&i.data)return f.data=i.data,f.isLoaded=!0,console.log("数据加载成功(status格式):",f.data),i.data;{console.warn("响应没有包含有效数据，将使用默认数据");const C=O();f.data=C,f.isLoaded=!0;const E=i?.message||"加载灵感卡池失败: 服务器未返回有效数据，已使用默认数据";return k.value=E,console.error("加载灵感卡池警告:",E,i),C}}catch(m){console.error("加载灵感卡池出错:",m);const i=O();return f.data=i,f.isLoaded=!0,k.value=m.message||"加载灵感卡池出错: 请检查网络连接",i}finally{b.value=!1}}function O(){return{categories:{theme:{name:"主题层",description:"故事的核心主题与情感基调",icon:"Sunrise",color:"primary",defaultCount:2,maxCount:5},volume:{name:"卷级结构",description:"故事的大纲架构与发展脉络",icon:"Connection",color:"success",defaultCount:4,maxCount:8},keyPoint:{name:"关键点",description:"故事中的重要转折与关键节点",icon:"Key",color:"warning",defaultCount:5,maxCount:8},technique:{name:"技法卡",description:"用于优化剧情的各种写作技巧",icon:"TrendCharts",color:"danger",defaultCount:3,maxCount:5}},theme:[],volume:[],keyPoint:[],technique:[]}}async function ue(m){try{b.value=!0,k.value=null;const i=await window.pywebview.api.book_controller.save_story_inspiration(m),C=typeof i=="string"?JSON.parse(i):i;if(C&&C.success)return f.data=m,C.data;{const E=C?.message||"保存灵感卡池失败";throw k.value=E,new Error(E)}}catch(i){throw k.value=i.message||"保存灵感卡池失败",console.error("保存灵感卡池失败:",i),i}finally{b.value=!1}}async function K(m,i){try{b.value=!0,k.value=null,console.log(`开始保存灵感类别 ${m}，共 ${i.length} 个元素`);const C=await window.pywebview.api.book_controller.save_inspiration_category(m,i);console.log("保存灵感类别API响应:",C);let E;try{E=typeof C=="string"?JSON.parse(C):C}catch(D){console.warn("解析响应失败，使用原始响应",D),E=C}if(E&&E.success===!0||E&&E.status==="success"||typeof E=="string"&&E.includes("成功"))return f.data[m]=i,console.log(`灵感类别 ${m} 保存成功`),E.data||{[m]:i};{const D=E?.message||"保存灵感类别失败";throw k.value=D,console.error("保存灵感类别失败:",D),new Error(D)}}catch(C){throw k.value=C.message||`保存灵感类别 ${m} 失败`,console.error(`保存灵感类别 ${m} 失败:`,C),C}finally{b.value=!1}}function re(){k.value=null}return{state:f,loading:b,error:k,inspirationData:h,isLoaded:g,categories:w,isLoading:I,hasError:L,getElementsByCategory:W,loadInspirationData:y,saveInspirationData:ue,saveInspirationCategory:K,clearError:re}}),Ot={class:"story-inspiration-container"},zt={key:0,class:"data-loading-overlay"},qt={key:1,class:"data-error-overlay"},Nt={class:"error-message"},At={class:"error-actions"},Tt={key:2,class:"app-content"},Ut={class:"app-header"},Jt={class:"header-actions"},Mt={class:"archetype-content"},jt={class:"selection-container"},Bt={class:"selection-wrapper"},Rt={class:"category-title-wrapper"},Ft={class:"category-title"},Wt={class:"category-functions"},Gt=["onUpdate:modelValue"],Ht=["value"],Qt={class:"category-items-wrapper"},Xt={class:"category-items-container native-scroll"},Yt=["onClick"],Zt={class:"item-title"},Kt={class:"result-section tech-panel"},el={class:"section-title"},tl={class:"interaction-tips"},ll={key:0,class:"fixed-elements-indicator"},sl={class:"fixed-count"},ol={style:{"text-align":"left"}},nl={class:"result-scroll-container"},al={class:"result-container-horizontal"},il={class:"pool-icon"},ul={class:"pool-info"},rl={class:"pool-title"},cl={class:"pool-count"},dl={class:"bubble-row"},pl=["onClick","onDblclick"],ml={class:"bubble-text"},fl={key:1,class:"empty-result-horizontal"},vl={class:"editor-container"},_l={class:"editor-toolbar"},hl={class:"examples-editor"},gl={class:"import-content"},yl={class:"format-hint-title"},bl={class:"import-options"},kl={class:"dialog-footer"},Cl={class:"dialog-header"},wl={class:"header-left"},El={class:"title-area"},xl={class:"category-label"},Sl={class:"element-name"},Vl={class:"header-right"},Dl={key:0,class:"dialog-content"},$l={class:"content-scroll"},Il={class:"info-section"},Pl={class:"section-title"},Ll={class:"description"},Ol={key:0,class:"info-section"},zl={class:"section-title"},ql={class:"examples-list"},Nl={class:"example-num"},Al={class:"example-text"},Tl={class:"dialog-footer"},Ul={class:"footer-tip"},Jl={__name:"StoryInspiration",setup(f){Ze();const{startLoading:b,stopLoading:k}=Pt(),h=Lt();x({categories:{},theme:[],volume:[],keyPoint:[],technique:[]});const g=q(()=>h.categories),w=Z({theme:[],volume:[],keyPoint:[],technique:[]}),I=Z({theme:{},volume:{},keyPoint:{},technique:{}}),L=x(null);_e(w,e=>{Object.values(e).some(o=>o.length>0)&&(L.value=JSON.parse(JSON.stringify(e)),ie(()=>{W()}))},{deep:!0});const W=()=>{const e=document.querySelector(".result-scroll-container");e&&(e.scrollWidth>e.clientWidth?e.classList.add("has-scroll"):e.classList.remove("has-scroll"))},y=Z({theme:2,volume:4,keyPoint:5,technique:3});_e(()=>h.categories,e=>{e&&(e.theme?.defaultCount&&(y.theme=e.theme.defaultCount),e.volume?.defaultCount&&(y.volume=e.volume.defaultCount),e.keyPoint?.defaultCount&&(y.keyPoint=e.keyPoint.defaultCount),e.technique?.defaultCount&&(y.technique=e.technique.defaultCount))},{immediate:!0});const O=e=>h.getElementsByCategory(e),ue=(e,t)=>{const o=w[e].includes(t);K(e,t,!o)},K=(e,t,o)=>{if(o){if(w[e].length>=y[e]){d.warning(`${g.value[e]?.name||e} 最多只能选择 ${y[e]} 个元素`);return}w[e].includes(t)||w[e].push(t)}else{const u=w[e].indexOf(t);u>-1&&w[e].splice(u,1)}},re=()=>{if(!h.isLoaded){d.warning("数据正在加载中，请稍候...");return}Object.keys(g.value).forEach(e=>{const t=O(e),o=Object.keys(I[e]).filter(T=>I[e][T]),u=y[e]-o.length;if(u<=0)w[e]=o.slice(0,y[e]);else{const T=t.filter(F=>!I[e][F.title]),X=m(T,u);w[e]=[...o,...X.map(F=>F.title)]}}),i(),ie(()=>{W()})},m=(e,t)=>[...e].sort(()=>.5-Math.random()).slice(0,t),i=()=>{if(!h.isLoaded){d.warning("数据正在加载中，请稍候...");return}if(!Object.keys(g.value).every(o=>w[o].length>0)){d.warning("请确保每个类型都至少选择了一个元素");return}const t=Object.keys(g.value).flatMap(o=>[`【${g.value[o].name}】`,...C(w[o],g.value[o].name),""]).join(`
`);window.pywebview.api.copy_to_clipboard(t).then(()=>{d.success({message:"组合已生成并复制到剪贴板",duration:2e3})}).catch(()=>{d.warning({message:"复制到剪贴板失败，请手动复制",duration:2e3})}),L.value=JSON.parse(JSON.stringify(w)),console.log("组合结果已生成:",L.value),ie(()=>{W()})},C=(e,t)=>e.map((o,u)=>`${u+1}. ${o}`),E=()=>{Object.keys(g.value).forEach(e=>{w[e]=[],I[e]={}}),L.value=null},z=x(null),D=x(null),Ce=(e,t)=>{if(z.value!==null&&(clearTimeout(z.value),z.value=null,D.value&&D.value.category===e&&D.value.elementTitle===t)){D.value=null;return}D.value={category:e,elementTitle:t},z.value=setTimeout(()=>{D.value&&(I[e][t]?I[e][t]=!1:I[e][t]=!0,D.value=null,z.value=null)},300)},ce=x(!1),G=x(""),P=x(null),we=(e,t)=>{z.value!==null&&(clearTimeout(z.value),z.value=null,D.value=null);const u=O(e).find(T=>T.title===t);u&&(G.value=e,P.value=u,ce.value=!0)},ee=()=>{ce.value=!1,G.value="",P.value=null},Ee=e=>e==="↑"?"up":e==="↓"?"down":e==="↓ | ↑"?"wave":"neutral",xe=e=>e==="↑"?"ArrowUp":e==="↓"?"ArrowDown":e==="↓ | ↑"?"Sort":"Connection",Se=e=>e==="↑"?"上升":e==="↓"?"下降":e==="↓ | ↑"?"波动":"平稳",Ve=q(()=>Object.values(I).some(e=>Object.values(e).some(t=>t))),De=q(()=>Object.values(I).reduce((e,t)=>e+Object.values(t).filter(o=>o).length,0)),te=x(!1),N=x(null),$=x([]),A=Z({theme:null,character:null,plot:null,technique:null}),$e=e=>{N.value=e,te.value=!0;const t=d({message:"正在准备编辑器数据...",type:"info",duration:0});ie(()=>{try{if(A[e]){console.log("使用缓存数据"),$.value=A[e],t.close();return}setTimeout(()=>{let o=O(e);Array.isArray(o)?($.value=o.map(u=>({...u})),A[e]=[...$.value]):$.value=[],t.close()},100)}catch(o){console.error("加载编辑器数据失败:",o),t.close(),d.error("加载数据失败，请重试")}})},Ie=()=>g.value[N.value]?.name?`编辑${g.value[N.value].name}元素`:"编辑元素",Pe=()=>{$.value.push({title:"新元素",description:"请输入描述",emotion:"↑",examples:["请添加示例"]})},Le=e=>{$.value.splice(e,1)},Oe=e=>{e.examples||(e.examples=[]),e.examples.push("")},ze=(e,t)=>{e.examples&&e.examples.splice(t,1)},qe=async()=>{try{b("正在保存灵感元素...");const e=N.value;if(!e){d.warning("没有选择灵感类别");return}console.log(`保存${g.value[e]?.name||e}元素`,$.value),await h.saveInspirationCategory(e,$.value),d({type:"success",message:`灵感类别 ${e} 保存成功`,duration:2e3}),A[e]=[...$.value],te.value=!1}catch(e){console.error("保存自定义元素失败:",e),d.error(`保存失败: ${e.message}`)}finally{k()}},Ne=()=>{try{const e=JSON.stringify($.value,null,2);window.pywebview.api.copy_to_clipboard(e).then(()=>{d.success(`已复制 ${$.value.length} 个${g.value[N.value]?.name||""}元素到剪贴板`)}).catch(t=>{console.error("复制到剪贴板失败:",t),d.error("复制到剪贴板失败，请检查浏览器权限");try{const o=document.createElement("textarea");o.value=e,o.style.position="fixed",o.style.opacity="0",document.body.appendChild(o),o.select();const u=document.execCommand("copy");document.body.removeChild(o),u?d.success("使用备用方法复制成功"):d.error("复制失败，请手动复制")}catch(o){console.error("备用复制方法失败:",o),d.error("复制失败，请手动复制")}}),console.log("导出的元素数量:",$.value.length)}catch(e){console.error("导出失败",e),d.error("导出失败："+e.message)}},H=x(!1),Q=x(""),Ae=()=>{H.value=!0,Q.value=""},Te=async()=>{try{if(!Q.value.trim()){d.warning("请输入JSON数据");return}let e;try{e=JSON.parse(Q.value)}catch{d.error("JSON格式无效，请检查您的输入");return}if(!Array.isArray(e)){d.error("导入失败：数据必须是数组格式");return}for(const t of e)if(!t.title||!t.description){d.error("导入失败：数据中有元素缺少标题或描述字段");return}$.value=e,N.value&&(A[N.value]=[...e]),H.value=!1,d.success(`成功导入 ${e.length} 个元素`),console.log("导入的元素数量:",e.length)}catch(e){console.error("导入失败",e),d.error("导入失败："+e.message)}},le=x(!1);Ke(async()=>{try{b("正在加载灵感卡池..."),await h.loadInspirationData(),le.value=!0;const e=h.categories;e&&(e.theme?.defaultCount&&(y.theme=e.theme.defaultCount),e.volume?.defaultCount&&(y.volume=e.volume.defaultCount),e.keyPoint?.defaultCount&&(y.keyPoint=e.keyPoint.defaultCount),e.technique?.defaultCount&&(y.technique=e.technique.defaultCount))}catch(e){console.error("加载灵感卡池失败:",e),d.error("加载灵感卡池出错，请刷新页面重试")}finally{k()}setTimeout(()=>{["theme","volume","keyPoint","technique"].forEach(e=>{if(!A[e]){let t=h.getElementsByCategory(e);Array.isArray(t)&&(A[e]=t.map(o=>({...o})))}}),console.log("编辑器数据预加载完成")},1e3)});const Ue=async()=>{try{b("正在重新加载灵感卡池..."),await h.loadInspirationData(),le.value=!0;const e=h.categories;e&&(e.theme?.defaultCount&&(y.theme=e.theme.defaultCount),e.volume?.defaultCount&&(y.volume=e.volume.defaultCount),e.keyPoint?.defaultCount&&(y.keyPoint=e.keyPoint.defaultCount),e.technique?.defaultCount&&(y.technique=e.technique.defaultCount)),d.success("数据加载成功")}catch(e){console.error("重新加载灵感卡池失败:",e),d.error("重新加载失败，请检查网络连接")}finally{k()}},Je=()=>{It.alert(`错误信息: ${h.error}

    API状态: ${window.pywebview?"可用":"不可用"}

    图书控制器: ${window.pywebview?.api?.book_controller?"可用":"不可用"}`,"诊断信息",{type:"warning"})},se=x(0),de=x(null),Me=e=>{const t=e.currentTarget;if(se.value++,de.value&&clearTimeout(de.value),de.value=setTimeout(()=>{se.value=0},2e3),se.value>=5){t.classList.add("inspiration-burst","super-burst"),d({message:"🎆 超级灵感爆发！创意宇宙大爆炸！！！",type:"warning",duration:3e3,showClose:!1}),se.value=0,setTimeout(()=>{t.classList.remove("inspiration-burst","super-burst")},2e3);return}t.classList.add("inspiration-burst");const o=["✨ 灵感正在涌现...","🌟 创意火花四溅！","💡 想象力爆发中...","🎨 艺术灵感降临！","📚 故事元素正在重组...","🔮 神秘的创作能量...","🌈 彩虹般的创意光芒！","⭐ 星辰般的灵感闪烁..."],u=o[Math.floor(Math.random()*o.length)];d({message:u,type:"success",duration:2e3,showClose:!1}),setTimeout(()=>{t.classList.remove("inspiration-burst")},1500)},je=async()=>{try{if(!confirm("确定要重置灵感卡池数据吗？这将删除所有自定义内容。"))return;b("正在重置灵感卡池数据...");const e={categories:{theme:{name:"主题层",description:"故事的核心主题与情感基调",icon:"Sunrise",color:"primary",defaultCount:2,maxCount:5},volume:{name:"卷级结构",description:"故事的大纲架构与发展脉络",icon:"Connection",color:"success",defaultCount:4,maxCount:8},keyPoint:{name:"关键点",description:"故事中的重要转折与关键节点",icon:"Key",color:"warning",defaultCount:5,maxCount:8},technique:{name:"技法卡",description:"用于优化剧情的各种写作技巧",icon:"TrendCharts",color:"danger",defaultCount:3,maxCount:5}},theme:[],volume:[],keyPoint:[],technique:[]};await h.saveInspirationData(e),await h.loadInspirationData(),le.value=!0,d.success("灵感卡池数据已重置")}catch(e){console.error("重置数据失败:",e),d.error("重置数据失败: "+e.message)}finally{k()}};return(e,t)=>{const o=tt,u=st,T=it,X=ut,F=ct,oe=yt,Y=gt,pe=kt,Be=bt,Re=Ct,Fe=ht,ve=wt,We=xt,Ge=Et;return c(),_("div",Ot,[!le.value&&p(h).isLoading?(c(),_("div",zt,[l(o,{class:"loading-icon"},{default:n(()=>[l(p(et))]),_:1}),t[6]||(t[6]=s("p",null,"正在加载灵感卡池数据...",-1))])):p(h).hasError?(c(),_("div",qt,[l(o,{class:"error-icon"},{default:n(()=>[l(p(lt))]),_:1}),t[10]||(t[10]=s("p",null,"加载灵感卡池数据失败",-1)),s("p",Nt,S(p(h).error),1),s("div",At,[l(u,{type:"primary",onClick:Ue},{default:n(()=>t[7]||(t[7]=[V("重试")])),_:1}),l(u,{type:"info",onClick:Je},{default:n(()=>t[8]||(t[8]=[V("诊断信息")])),_:1}),l(u,{type:"danger",onClick:je},{default:n(()=>t[9]||(t[9]=[V("重置数据")])),_:1})])])):(c(),_("div",Tt,[s("div",Ut,[t[14]||(t[14]=s("h2",null,"故事灵感",-1)),s("div",Jt,[l(u,{type:"primary",onClick:i,size:"default",class:"action-button primary-action"},{default:n(()=>[l(o,null,{default:n(()=>[l(p(he))]),_:1}),t[11]||(t[11]=s("span",null,"生成组合",-1))]),_:1}),l(u,{onClick:re,size:"default",class:"action-button secondary-action"},{default:n(()=>[l(o,null,{default:n(()=>[l(p(ot))]),_:1}),t[12]||(t[12]=s("span",null,"随机灵感",-1))]),_:1}),l(u,{onClick:E,size:"default",class:"action-button danger-action"},{default:n(()=>[l(o,null,{default:n(()=>[l(p(me))]),_:1}),t[13]||(t[13]=s("span",null,"重置选择",-1))]),_:1})])]),s("div",Mt,[s("div",jt,[s("div",Bt,[(c(!0),_(j,null,B(g.value,(a,r)=>(c(),_("div",{key:r,class:"category-column"},[s("div",{class:R(["category-header",{"category-active":w[r].length>0}])},[s("div",Rt,[l(o,{class:"category-icon"},{default:n(()=>[(c(),M(ne(a.icon)))]),_:2},1024),s("span",Ft,S(a.name),1)]),s("div",Wt,[ge(s("select",{"onUpdate:modelValue":v=>y[r]=v,class:"count-select native-select"},[(c(!0),_(j,null,B(a.maxCount,v=>(c(),_("option",{key:v,value:v},S(v),9,Ht))),128))],8,Gt),[[nt,y[r]]]),l(u,{class:"edit-button",size:"small",type:"primary",onClick:v=>$e(r),circle:""},{default:n(()=>[l(o,null,{default:n(()=>[l(p(at))]),_:1})]),_:2},1032,["onClick"])])],2),s("div",Qt,[s("div",Xt,[O(r).length>0?(c(!0),_(j,{key:0},B(O(r),v=>(c(),_("div",{key:v.title,class:R(["category-item",{"item-selected":w[r].includes(v.title)}]),onClick:U=>ue(r,v.title)},[l(T,{"model-value":w[r].includes(v.title),"onUpdate:modelValue":U=>K(r,v.title,U),onClick:t[0]||(t[0]=fe(()=>{},["stop"]))},null,8,["model-value","onUpdate:modelValue"]),s("span",Zt,S(v.title),1)],10,Yt))),128)):(c(),M(X,{key:1,description:"暂无内容","image-size":50}))])])]))),128)),Object.keys(g.value).length===0?(c(),M(X,{key:0,description:"暂无分类，请检查数据加载"})):J("",!0)])]),s("div",Kt,[s("div",{class:"result-header card-pool-header",onClick:Me,title:"点击获取灵感爆发！连续点击有惊喜哦~"},[s("div",el,[t[15]||(t[15]=s("div",{class:"tech-lines"},null,-1)),l(o,{class:"result-icon"},{default:n(()=>[l(p(rt))]),_:1}),t[16]||(t[16]=s("h3",null,"✨ 灵感卡池抽取结果",-1)),t[17]||(t[17]=s("div",{class:"title-sparkles"},[s("span",{class:"sparkle"},"✦"),s("span",{class:"sparkle"},"✧"),s("span",{class:"sparkle"},"✦")],-1))]),s("div",tl,[Ve.value?(c(),_("div",ll,[l(o,{class:"lock-icon"},{default:n(()=>[l(p(ye))]),_:1}),s("span",sl,"已固定: "+S(De.value)+"个",1),l(F,{content:"点击气泡可固定/解除固定，固定的元素在随机时将被保留"},{default:n(()=>[l(o,{class:"info-icon"},{default:n(()=>[l(p(ae))]),_:1})]),_:1})])):J("",!0),l(F,{placement:"top",effect:"light"},{content:n(()=>[s("div",ol,[s("div",null,[l(o,null,{default:n(()=>[l(p(dt))]),_:1}),t[18]||(t[18]=V()),t[19]||(t[19]=s("b",null,"单击",-1)),t[20]||(t[20]=V(": 固定/解除固定气泡"))]),s("div",null,[l(o,null,{default:n(()=>[l(p(pt))]),_:1}),t[21]||(t[21]=V()),t[22]||(t[22]=s("b",null,"双击",-1)),t[23]||(t[23]=V(": 查看元素详情"))])])]),default:n(()=>[l(o,{class:"tips-icon",size:20},{default:n(()=>[l(p(ae))]),_:1})]),_:1})])]),s("div",nl,[s("div",al,[L.value?(c(!0),_(j,{key:0},B(g.value,(a,r)=>ge((c(),_("div",{key:r,class:"card-pool-column"},[s("div",{class:R(["pool-header",`pool-${a.color}`])},[s("div",il,[l(o,null,{default:n(()=>[(c(),M(ne(a.icon)))]),_:2},1024)]),s("div",ul,[s("span",rl,S(a.name),1),s("span",cl,S(L.value[r].length)+"个元素",1)])],2),s("div",{class:R(["bubble-container",`pool-${a.color}`])},[s("div",dl,[(c(!0),_(j,null,B(L.value[r],(v,U)=>(c(),_("div",{key:v,class:R(["element-bubble",[`bubble-${a.color}`,{"bubble-fixed":I[r][v]}]]),style:ft({animationDelay:`${U*.1}s`}),onClick:He=>Ce(r,v),onDblclick:He=>we(r,v)},[I[r][v]?(c(),M(o,{key:0,class:"bubble-lock-icon"},{default:n(()=>[l(p(ye))]),_:1})):J("",!0),s("span",ml,S(v),1)],46,pl))),128))])],2)])),[[mt,L.value[r]&&L.value[r].length>0]])),128)):(c(),_("div",fl,[l(X,{description:"请从上方选择元素并点击「生成组合」或「随机灵感」按钮","image-size":80},{image:n(()=>[l(o,{class:"empty-icon"},{default:n(()=>[l(p(he))]),_:1})]),_:1})]))])])])])])),l(ve,{modelValue:te.value,"onUpdate:modelValue":t[1]||(t[1]=a=>te.value=a),title:Ie(),width:"70%",class:"element-editor-dialog","destroy-on-close":"false","append-to-body":!0},{default:n(()=>[s("div",vl,[s("div",_l,[l(u,{type:"primary",size:"default",onClick:Pe},{default:n(()=>[l(o,null,{default:n(()=>[l(p(be))]),_:1}),t[24]||(t[24]=V(" 添加新元素 "))]),_:1}),l(u,{type:"success",size:"default",onClick:qe},{default:n(()=>[l(o,null,{default:n(()=>[l(p(ke))]),_:1}),t[25]||(t[25]=V(" 保存修改 "))]),_:1}),l(u,{size:"default",onClick:Ne},{default:n(()=>[l(o,null,{default:n(()=>[l(p(vt))]),_:1}),t[26]||(t[26]=V(" 导出配置 "))]),_:1}),l(u,{type:"warning",size:"default",onClick:Ae},{default:n(()=>[l(o,null,{default:n(()=>[l(p(_t))]),_:1}),t[27]||(t[27]=V(" 导入配置 "))]),_:1})]),l(Fe,{data:$.value,style:{width:"100%"},"max-height":"450px",border:""},{default:n(()=>[l(Y,{label:"标题",width:"180"},{default:n(({row:a})=>[l(oe,{modelValue:a.title,"onUpdate:modelValue":r=>a.title=r,placeholder:"输入标题",size:"default"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(Y,{label:"描述"},{default:n(({row:a})=>[l(oe,{modelValue:a.description,"onUpdate:modelValue":r=>a.description=r,type:"textarea",placeholder:"输入描述",rows:2,size:"default"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(Y,{label:"情感走向",width:"120"},{default:n(({row:a})=>[l(Be,{modelValue:a.emotion,"onUpdate:modelValue":r=>a.emotion=r,placeholder:"选择走向",size:"default"},{default:n(()=>[l(pe,{label:"上升 ↑",value:"↑"}),l(pe,{label:"下降 ↓",value:"↓"}),l(pe,{label:"波动 ↓|↑",value:"↓ | ↑"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),l(Y,{label:"示例",width:"140"},{default:n(({row:a})=>[l(Re,{placement:"right",width:350,trigger:"click"},{reference:n(()=>[l(u,{size:"default"},{default:n(()=>[V("编辑 ("+S(a.examples?.length||0)+")",1)]),_:2},1024)]),default:n(()=>[s("div",hl,[(c(!0),_(j,null,B(a.examples||[],(r,v)=>(c(),_("div",{key:v,class:"example-item"},[l(oe,{modelValue:a.examples[v],"onUpdate:modelValue":U=>a.examples[v]=U,placeholder:"输入示例",size:"default"},null,8,["modelValue","onUpdate:modelValue"]),l(u,{type:"danger",onClick:U=>ze(a,v),size:"small",circle:""},{default:n(()=>[l(o,null,{default:n(()=>[l(p(me))]),_:1})]),_:2},1032,["onClick"])]))),128)),l(u,{type:"primary",onClick:r=>Oe(a),size:"default"},{default:n(()=>[l(o,null,{default:n(()=>[l(p(be))]),_:1}),t[28]||(t[28]=V(" 添加示例 "))]),_:2},1032,["onClick"])])]),_:2},1024)]),_:1}),l(Y,{label:"操作",width:"80"},{default:n(({$index:a})=>[l(u,{type:"danger",onClick:r=>Le(a),size:"default",circle:""},{default:n(()=>[l(o,null,{default:n(()=>[l(p(me))]),_:1})]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])]),_:1},8,["modelValue","title"]),l(ve,{modelValue:H.value,"onUpdate:modelValue":t[4]||(t[4]=a=>H.value=a),title:"导入配置",width:"800px",class:"import-config-dialog","close-on-click-modal":!1,"show-close":!0,"append-to-body":!0,top:"5vh"},{footer:n(()=>[s("div",kl,[l(u,{onClick:t[3]||(t[3]=a=>H.value=!1)},{default:n(()=>t[31]||(t[31]=[V("取消")])),_:1}),l(u,{type:"primary",onClick:Te},{default:n(()=>t[32]||(t[32]=[V("确认导入")])),_:1})])]),default:n(()=>[s("div",gl,[l(Ge,null,{default:n(()=>[l(We,null,{title:n(()=>[s("div",yl,[l(o,null,{default:n(()=>[l(p(ae))]),_:1}),t[29]||(t[29]=s("span",null,"查看JSON格式示例",-1))])]),default:n(()=>[t[30]||(t[30]=s("div",{class:"format-hint-content"},[s("pre",null,`[
  {
    "title": "元素标题",
    "description": "元素描述",
    "emotion": "↑",
    "examples": ["示例1", "示例2"]
  },
  {
    "title": "另一个元素",
    "description": "另一个描述",
    "emotion": "↓ | ↑",
    "examples": ["示例1"]
  }
]`)],-1))]),_:1})]),_:1}),s("div",bl,[l(oe,{modelValue:Q.value,"onUpdate:modelValue":t[2]||(t[2]=a=>Q.value=a),type:"textarea",rows:12,placeholder:"请粘贴有效的JSON数据",class:"import-input"},null,8,["modelValue"])])])]),_:1},8,["modelValue"]),ce.value?(c(),_("div",{key:3,class:"native-dialog-overlay",onClick:fe(ee,["self"]),onKeydown:St(ee,["esc"]),tabindex:"0"},[s("div",{class:"native-dialog",onClick:t[5]||(t[5]=fe(()=>{},["stop"]))},[s("div",Cl,[s("div",wl,[s("div",{class:R(["category-badge",G.value])},[l(o,null,{default:n(()=>[(c(),M(ne(g.value[G.value]?.icon||"Document")))]),_:1})],2),s("div",El,[s("span",xl,S(g.value[G.value]?.name||"元素"),1),s("h3",Sl,S(P.value?.title),1)])]),s("div",Vl,[P.value?.emotion?(c(),_("div",{key:0,class:R(["emotion-badge",Ee(P.value.emotion)])},[l(o,null,{default:n(()=>[(c(),M(ne(xe(P.value.emotion))))]),_:1}),s("span",null,S(Se(P.value.emotion)),1)],2)):J("",!0),s("button",{class:"close-btn",onClick:ee,type:"button"},[l(o,null,{default:n(()=>[l(p(Vt))]),_:1})])])]),P.value?(c(),_("div",Dl,[s("div",$l,[s("div",Il,[s("div",Pl,[l(o,null,{default:n(()=>[l(p(Dt))]),_:1}),t[33]||(t[33]=s("span",null,"描述",-1))]),s("p",Ll,S(P.value.description),1)]),P.value.examples&&P.value.examples.length>0?(c(),_("div",Ol,[s("div",zl,[l(o,null,{default:n(()=>[l(p($t))]),_:1}),s("span",null,"示例 ("+S(P.value.examples.length)+")",1)]),s("div",ql,[(c(!0),_(j,null,B(P.value.examples,(a,r)=>(c(),_("div",{key:r,class:"example-item"},[s("span",Nl,S(r+1),1),s("span",Al,S(a),1)]))),128))])])):J("",!0)])])):J("",!0),s("div",Tl,[s("div",Ul,[l(o,null,{default:n(()=>[l(p(ae))]),_:1}),t[34]||(t[34]=s("span",null,"双击标签查看详情，单击固定元素",-1))]),s("button",{class:"confirm-btn",onClick:ee},[l(o,null,{default:n(()=>[l(p(ke))]),_:1}),t[35]||(t[35]=s("span",null,"知道了",-1))])])])],32)):J("",!0)])}}},Xl=Ye(Jl,[["__scopeId","data-v-43d99408"]]);export{Xl as default};
