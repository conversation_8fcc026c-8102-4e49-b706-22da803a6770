const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./entry-BIjVVog3.js","./css/main.css-f5X3pQNk.css"])))=>i.map(i=>d[i]);
import{aW as T,r as w,c as d,E as f,aS as j}from"./entry-BIjVVog3.js";const z=T("aiProviders",()=>{const n=w([]),u=w(!1),c=w(null),y=w(!1),p=w(!1),g=d(()=>n.value),h=d(()=>u.value),_=d(()=>!!c.value),m=d(()=>{const s=[];n.value.forEach(o=>{o.models&&Array.isArray(o.models)&&o.models.forEach(a=>{a.available===!0&&s.push({id:a.id,name:a.name||a.id,providerId:o.id,providerName:o.name,available:!0,config:a.config||{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0},uniqueId:`${o.id}:${a.id}`})})});const e=[],r=new Set;return s.forEach(o=>{r.has(o.uniqueId)||(r.add(o.uniqueId),e.push(o))}),console.log(`aiProvidersStore: 找到 ${e.length} 个可用模型`),e}),E=d(()=>{const s=[];return n.value.forEach(e=>{e.models&&Array.isArray(e.models)&&e.models.forEach(r=>{s.push({id:r.id,name:r.name||r.id,providerId:e.id,providerName:e.name,available:r.available!==!1,config:r.config||{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0},uniqueId:`${e.id}:${r.id}`})})}),s}),A=d(()=>m.value.map(s=>({value:s.uniqueId,label:s.name===s.id?`${s.id} (${s.providerName})`:`${s.name}  (${s.providerName})`,id:s.id,name:s.name,providerId:s.providerId,providerName:s.providerName,uniqueId:s.uniqueId,config:s.config})));async function P(s=!1){if(p.value&&!s)return n.value;try{u.value=!0,c.value=null;let e=0;const r=3;let o=!1;for(;!o&&e<r;)try{const a=window.pywebview.api.get_ai_providers(),i=new Promise((v,L)=>{setTimeout(()=>L(new Error("获取AI服务商列表超时")),5e3)}),l=await Promise.race([a,i]),t=typeof l=="string"?JSON.parse(l):l;if(t&&t.status==="success"){if(Array.isArray(t.data))return n.value=t.data,y.value=!0,p.value=!0,o=!0,n.value;throw console.warn("服务器返回的数据不是数组格式:",t.data),new Error("服务器返回的数据格式不正确")}else throw new Error(t?.message||"获取AI服务商列表失败")}catch(a){if(e++,e>=r)throw a;console.warn(`加载AI服务商失败 (尝试 ${e}/${r}):`,a),await new Promise(i=>setTimeout(i,1e3))}}catch(e){throw c.value=e.message,console.error("加载AI服务商失败:",e),p.value=!0,n.value=[],e}finally{u.value=!1}}async function $(){try{if(u.value=!0,c.value=null,!Array.isArray(n.value))throw new Error("服务商数据必须是数组格式");for(const r of n.value){if(!r||typeof r!="object")throw new Error("服务商必须是对象格式");r.name||(r.name="未命名服务商"),r.baseUrl||(r.baseUrl="https://api.openai.com/v1"),Array.isArray(r.apiKeys)||(r.apiKeys=[]),Array.isArray(r.models)||(r.models=[])}const s=await window.pywebview.api.save_ai_providers(n.value),e=typeof s=="string"?JSON.parse(s):s;if(e&&e.status==="success"){n.value.forEach(r=>{r._isNew&&delete r._isNew}),f.success("AI服务商配置保存成功");try{const{useConfigStore:r}=await j(async()=>{const{useConfigStore:a}=await import("./entry-BIjVVog3.js").then(i=>i.ek);return{useConfigStore:a}},__vite__mapDeps([0,1]),import.meta.url),o=r();console.log("AI提供商配置已更新，重新加载模型列表..."),await o.reloadModels(),console.log("模型列表重新加载完成")}catch(r){console.warn("重新加载模型列表失败:",r)}return!0}else throw new Error(e?.message||"保存AI服务商配置失败")}catch(s){throw c.value=s.message,f.error("保存AI服务商配置失败: "+s.message),console.error("保存AI服务商配置失败:",s),s}finally{u.value=!1}}async function b(s){const e={id:Date.now().toString(),name:s.name||"新服务商",baseUrl:s.baseUrl||"",apiKeys:s.apiKeys||[],models:s.models||[],proxy:s.proxy||{enabled:!1,url:"",username:"",password:"",timeout:30,verify_ssl:!0},_isNew:!0};return n.value.push(e),e}async function I(s,e){const r=n.value.find(o=>o.id===s);if(!r)throw new Error(`未找到ID为 ${s} 的服务商`);return Object.assign(r,e),r}async function K(s){try{u.value=!0,c.value=null,console.log("saveProvider: 尝试保存服务商ID:",s),console.log("saveProvider: 当前服务商列表:",n.value.map(i=>({id:i.id,name:i.name})));const e=n.value.find(i=>i.id===s);if(!e)throw console.error("saveProvider: 未找到服务商，可用的服务商ID:",n.value.map(i=>i.id)),new Error(`未找到ID为 ${s} 的服务商`);if(!e.name||!e.name.trim())throw new Error("服务商名称不能为空");e.baseUrl||(e.baseUrl="https://api.openai.com/v1"),Array.isArray(e.apiKeys)||(e.apiKeys=[]),Array.isArray(e.models)||(e.models=[]),e.proxy||(e.proxy={enabled:!1,url:"",username:"",password:"",timeout:30,verify_ssl:!0});const r=e._isNew===!0;console.log("saveProvider: 是否为新服务商:",r);let o,a;if(r?(console.log("saveProvider: 保存新服务商，调用 saveProviders"),o=await window.pywebview.api.save_ai_providers(n.value),a=typeof o=="string"?JSON.parse(o):o):(console.log("saveProvider: 更新已存在的服务商，调用 update_ai_provider"),o=await window.pywebview.api.update_ai_provider(s,e),a=typeof o=="string"?JSON.parse(o):o),a&&a.status==="success")return e._isNew&&delete e._isNew,f.success("服务商配置保存成功"),console.log('单个服务商配置已保存，如需更新模型列表请点击"保存所有配置"'),!0;throw new Error(a?.message||"保存服务商配置失败")}catch(e){throw c.value=e.message,f.error("保存服务商配置失败: "+e.message),console.error("保存服务商配置失败:",e),e}finally{u.value=!1}}async function N(s){const e=n.value.findIndex(r=>r.id===s);if(e===-1)throw new Error(`未找到ID为 ${s} 的服务商`);return n.value.splice(e,1),!0}async function S(s,e){try{u.value=!0;const r=n.value.find(v=>v.id===s);if(!r)throw new Error(`未找到ID为 ${s} 的服务商`);const o=r.apiKeys.find(v=>v.id===e);if(!o)throw new Error(`未找到ID为 ${e} 的API密钥`);let a="gpt-3.5-turbo";r.models&&r.models.length>0&&(a=r.models[0].id);const i={provider_id:s,api_key:o.key,base_url:r.baseUrl,test_model:a,proxy:r.proxy||{}},l=await window.pywebview.api.test_api_key(i),t=typeof l=="string"?JSON.parse(l):l;if(t&&t.status==="success")return o.status="active",f.success("API密钥测试成功"),!0;throw o.status="error",new Error(t?.message||"API密钥测试失败")}catch(r){return c.value=r.message,f.error("API密钥测试失败: "+r.message),!1}finally{u.value=!1}}async function M(s){try{u.value=!0;const e=n.value.find(i=>i.id===s);if(!e)throw new Error(`未找到ID为 ${s} 的服务商`);if(!e.apiKeys||e.apiKeys.length===0)throw new Error("请先添加API密钥");const r={provider_id:s,api_key:e.apiKeys[0].key,base_url:e.baseUrl,proxy:e.proxy||{}},o=await window.pywebview.api.fetch_models(r),a=typeof o=="string"?JSON.parse(o):o;if(a&&a.status==="success"){const i={};e.models.forEach(t=>{i[t.id]=t});const l=a.data.map(t=>i[t]?i[t]:{id:t,name:t,available:!0});return e.models=l,f.success(`成功获取 ${l.length} 个模型`),l}else throw new Error(a?.message||"获取模型列表失败")}catch(e){return c.value=e.message,f.error("获取模型列表失败: "+e.message),[]}finally{u.value=!1}}function x(s,e={}){const r=n.value.find(a=>a.id===s);if(!r)throw new Error(`未找到ID为 ${s} 的服务商`);const o={id:Date.now().toString(),key:e.key||"",weight:e.weight||1,status:"active"};return r.apiKeys||(r.apiKeys=[]),r.apiKeys.push(o),o}function k(s,e){const r=n.value.find(o=>o.id===s);if(!r||!r.apiKeys)throw new Error("未找到服务商或API密钥列表");if(e<0||e>=r.apiKeys.length)throw new Error(`无效的API密钥索引: ${e}`);return r.apiKeys.splice(e,1),!0}function O(s,e={}){const r=n.value.find(a=>a.id===s);if(!r)throw new Error(`未找到ID为 ${s} 的服务商`);if(!e.id||!e.id.trim())throw new Error("模型ID不能为空");if(r.models&&r.models.some(a=>a.id===e.id))throw new Error(`模型ID "${e.id}" 已存在`);const o={id:e.id.trim(),name:e.name?.trim()||e.id.trim(),available:e.available!==!1,config:{temperature:e.config?.temperature??.8,max_tokens:e.config?.max_tokens??8192,top_p:e.config?.top_p??.8,frequency_penalty:e.config?.frequency_penalty??0,presence_penalty:e.config?.presence_penalty??0,stream:e.config?.stream??!0}};return r.models||(r.models=[]),r.models.push(o),console.log(`添加模型: ID="${o.id}", 别名="${o.name}", 配置:`,o.config),o}function U(s,e){const r=n.value.find(o=>o.id===s);if(!r||!r.models)throw new Error("未找到服务商或模型列表");if(e<0||e>=r.models.length)throw new Error(`无效的模型索引: ${e}`);return r.models.splice(e,1),!0}function q(s){if(!s||!s.includes(":"))return console.warn("无效的模型唯一标识符:",s),null;const[e,r]=s.split(":"),o=n.value.find(i=>i.id===e);if(!o||!o.models)return console.warn(`未找到服务商: ${e}`),null;const a=o.models.find(i=>i.id===r);return a?{id:a.id,name:a.name||a.id,providerId:e,providerName:o.name,uniqueId:s,config:a.config||{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0}}:(console.warn(`未找到模型: ${r} 在服务商 ${e} 中`),null)}function J(s,e){if(!s||!s.includes(":"))throw new Error("无效的模型唯一标识符");const[r,o]=s.split(":"),a=n.value.find(l=>l.id===r);if(!a||!a.models)throw new Error(`未找到服务商: ${r}`);const i=a.models.find(l=>l.id===o);if(!i)throw new Error(`未找到模型: ${o} 在服务商 ${r} 中`);return i.config={...i.config,...e},console.log(`更新模型配置: ${s}`,i.config),i.config}function C(){n.value=[],y.value=!1,p.value=!1,c.value=null}return{providers:n,loading:u,error:c,isLoaded:y,initialized:p,allProviders:g,isLoading:h,hasError:_,allAvailableModels:m,allModels:E,modelOptions:A,loadProviders:P,saveProviders:$,saveProvider:K,addProvider:b,updateProvider:I,removeProvider:N,testApiKey:S,fetchModels:M,addApiKey:x,removeApiKey:k,addModel:O,removeModel:U,getModelConfig:q,updateModelConfig:J,reset:C}});export{z as useAIProvidersStore};
