import{aW as _,r as d,c as v}from"./entry-BIjVVog3.js";const x=_("aiRoles",()=>{const l=d([]),t=d(!1),n=d(null),u=d(!1),f=v(()=>l.value),w=v(()=>t.value),p=v(()=>!!n.value);async function i(r=!1){if(u.value&&!r)return l.value;try{t.value=!0,n.value=null,console.log("加载AI角色列表...");const e=await window.pywebview.api.model_controller.get_ai_roles(),s=typeof e=="string"?JSON.parse(e):e;if(s&&s.status==="success")return Array.isArray(s.data)?(l.value=s.data,console.log(`成功加载${l.value.length}个AI角色`)):(console.warn("API返回的角色数据不是数组:",s.data),l.value=[]),u.value=!0,l.value;{const a=s?.message||"获取AI角色列表失败";throw console.error("加载角色失败:",a),new Error(a)}}catch(e){throw n.value=e.message,console.error("加载AI角色失败:",e),e}finally{t.value=!1}}async function g(r){try{t.value=!0,n.value=null;const e=await window.pywebview.api.model_controller.add_ai_role(r),s=typeof e=="string"?JSON.parse(e):e;if(s&&s.status==="success"){const a=s.data;if(a&&a.id){const o=l.value.findIndex(c=>c.id===a.id);o>=0?l.value[o]={...a}:l.value.push({...a}),u.value=!0}else await i();return s}else throw new Error(s?.message||"添加AI角色失败")}catch(e){throw n.value=e.message,console.error("添加AI角色失败:",e),e}finally{t.value=!1}}async function y(r,e){try{t.value=!0,n.value=null;const s=await window.pywebview.api.model_controller.update_ai_role(r,e),a=typeof s=="string"?JSON.parse(s):s;if(a&&a.status==="success"){const o=a.data;if(o&&o.id){const c=l.value.findIndex(A=>A.id===o.id);c!==-1?(l.value[c]={...o},console.log("角色已更新:",o.id,o.name)):(l.value.push({...o}),console.log("找不到要更新的角色，已添加到列表:",o.id))}else await i();return a}else throw new Error(a?.message||"更新AI角色失败")}catch(s){throw n.value=s.message,console.error("更新AI角色失败:",s),s}finally{t.value=!1}}async function h(r){try{t.value=!0,n.value=null;const e=await window.pywebview.api.model_controller.delete_ai_role(r),s=typeof e=="string"?JSON.parse(e):e;if(s&&s.status==="success"){const a=l.value.findIndex(o=>o.id===r);return a!==-1?(console.log("从本地状态中移除角色:",r),l.value.splice(a,1)):(console.log("找不到要删除的角色，重新加载列表"),await i()),{success:!0,message:"角色已删除"}}else throw new Error(s?.message||"删除AI角色失败")}catch(e){throw n.value=e.message,console.error("删除AI角色失败:",e),e}finally{t.value=!1}}async function m(r){u.value||await i();const e=l.value.find(s=>s.id===r);if(!e)throw new Error(`未找到ID为 ${r} 的角色`);return e}function I(){l.value=[],u.value=!1,t.value=!1,n.value=null}return{roles:l,loading:t,error:n,isLoaded:u,allRoles:f,isLoading:w,hasError:p,loadRoles:i,addRole:g,updateRole:y,deleteRole:h,getRoleById:m,reset:I}});export{x as useAIRolesStore};
