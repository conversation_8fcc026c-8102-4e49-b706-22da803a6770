import{E as n}from"./entry-BIjVVog3.js";const b=async(r,t={})=>{const{showSuccess:a=!0,showError:l=!0}=t;try{if(console.log("Copying text:",r?r.substring(0,50)+"...":"undefined or empty"),!r)return l&&n.warning("没有内容可复制"),!1;if(window.pywebview&&window.pywebview.api&&window.pywebview.api.copy_to_clipboard)return await window.pywebview.api.copy_to_clipboard(r),a&&n.success("复制成功"),!0;if(navigator.clipboard&&navigator.clipboard.writeText)return await navigator.clipboard.writeText(r),a&&n.success("复制成功"),!0;const e=document.createElement("textarea");e.value=r,e.style.position="fixed",e.style.left="-999999px",e.style.top="-999999px",document.body.appendChild(e),e.focus(),e.select();const i=document.execCommand("copy");if(document.body.removeChild(e),i)return a&&n.success("复制成功"),!0;throw new Error("execCommand failed")}catch(e){return console.error("复制失败:",e),l&&n.error("复制失败: "+e.message),!1}},d=r=>{if(typeof r=="string")try{return JSON.parse(r)}catch(t){throw console.error("解析API响应失败:",t),new Error("API响应格式错误")}return r},h=r=>r&&r.status==="success",y=r=>r?.data||null,g=(r,t="操作失败")=>r?.message||t,f=async(r,t={})=>{const{successMessage:a,errorMessage:l="操作失败",showSuccess:e=!1,showError:i=!0}=t;try{const o=await r(),s=d(o);if(h(s))return e&&a&&n.success(a),y(s);{const c=g(s,l);throw i&&n.error(c),new Error(c)}}catch(o){throw i&&!o.message.includes("操作失败")&&n.error(o.message||l),o}},E=async(r,t={})=>{const{concurrent:a=!1,stopOnError:l=!1,showError:e=!0}=t,i=[],o=[];if(a){const s=r.map(async(p,w)=>{try{const u=await f(p,{showError:!1});return{index:w,result:u,error:null}}catch(u){return{index:w,result:null,error:u}}});(await Promise.all(s)).forEach(({index:p,result:w,error:u})=>{i[p]=w,u&&(o[p]=u)})}else for(let s=0;s<r.length;s++)try{const c=await f(r[s],{showError:!1});i[s]=c}catch(c){if(o[s]=c,l)break}if(e&&o.length>0){const s=o.filter(c=>c).length;s>0&&n.error(`${s} 个操作失败`)}return{results:i,errors:o}};export{E as a,b as c,f as h};
