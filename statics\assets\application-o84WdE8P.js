const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./ChromeSettings-BuJ3Dll-.js","./entry-BIjVVog3.js","./css/main.css-f5X3pQNk.css","./css/ChromeSettings.css-CX9GqlSQ.css","./css/el-input.css-BCtgWP8V.css","./css/el-input-number.css-DUUPPWGj.css","./css/el-table-column.css-CBdd4Iyj.css","./css/el-checkbox.css-DIPHKmvR.css","./css/el-switch.css-B5lTGWdM.css","./css/el-tag.css-DljBBxJR.css","./css/el-form.css-CKZiX9BY.css","./AIRoleManager-vyNx1kT2.js","./css/AIRoleManager.css-DpgWmboa.css","./css/el-alert.css-B9oGCRyi.css","./AIProviderConfig-hssIYs10.js","./aiProviders-BAOX-EH8.js","./css/AIProviderConfig.css-RNXEKKs2.css","./css/el-slider.css-DtISwLyR.css","./css/el-collapse-item.css-D7WIfuA2.css","./css/el-select.css-BSBZNJn6.css","./css/el-empty.css-D4ZqTl4F.css","./FeishuConfig-CwaMKjzu.js","./css/FeishuConfig.css-BPIGBCjz.css","./ChatSettings-CAeRtEi2.js","./css/ChatSettings.css-CTtSg12P.css","./css/el-radio-group.css-BzMpJalG.css","./css/el-radio.css-Py4_z_sx.css","./BackupSettings-Dy3Lofw-.js","./css/BackupSettings.css-CW9esKzQ.css","./css/el-loading.css-DLSpKYce.css","./css/el-progress.css-Dw9yTa91.css","./GitBackup-BKWJptLG.js","./css/GitBackup.css-BuxPKOYz.css","./css/el-card.css-fwQOLwdi.css"])))=>i.map(i=>d[i]);
import{_ as N,r as E,a as M,o as L,E as $,U as T,b as F,V as G,bF as z,$ as n,g as l,b9 as X,m as r,d as c,ba as j,p as u,C as m,bG as p,aS as _,P as v,aU as f}from"./entry-BIjVVog3.js";/* empty css                   *//* empty css                    */import{useAIRolesStore as q}from"./aiRoles-BNrBgqza.js";import{useAIProvidersStore as H}from"./aiProviders-BAOX-EH8.js";const J={class:"application-container"},Q={__name:"application",setup(W){const s={render(){return v("div",{class:"tab-loading"},[v("el-skeleton",{rows:5,animated:!0})])}},i={emits:["retry"],render(){return v("div",{class:"tab-error"},[v("el-result",{icon:"error",title:"加载失败","sub-title":"组件加载出现错误，请刷新页面重试"},{extra:()=>v("el-button",{type:"primary",onClick:()=>this.$emit("retry")},"重试")})])}},A=p({loader:()=>_(()=>import("./ChromeSettings-BuJ3Dll-.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]),import.meta.url),loadingComponent:s,errorComponent:i,delay:200,timeout:1e4}),I=p({loader:()=>_(()=>import("./AIRoleManager-vyNx1kT2.js"),__vite__mapDeps([11,1,2,12,13,10,4,6,7,8,9]),import.meta.url),loadingComponent:s,errorComponent:i,delay:200,timeout:1e4}),P=p({loader:()=>_(()=>import("./AIProviderConfig-hssIYs10.js"),__vite__mapDeps([14,1,2,15,16,17,4,5,18,10,9,19,6,7,8,20]),import.meta.url),loadingComponent:s,errorComponent:i,delay:200,timeout:1e4}),R=p({loader:()=>_(()=>import("./FeishuConfig-CwaMKjzu.js"),__vite__mapDeps([21,1,2,22,10,4]),import.meta.url),loadingComponent:s,errorComponent:i,delay:200,timeout:1e4}),S=p({loader:()=>_(()=>import("./ChatSettings-CAeRtEi2.js"),__vite__mapDeps([23,1,2,24,10,25,26,9,19,17,4,5]),import.meta.url),loadingComponent:s,errorComponent:i,delay:200,timeout:1e4}),w=p({loader:()=>_(()=>import("./BackupSettings-Dy3Lofw-.js"),__vite__mapDeps([27,1,2,28,29,6,7,9,30,10,4,5,8]),import.meta.url),loadingComponent:s,errorComponent:i,delay:200,timeout:1e4}),D=p({loader:()=>_(()=>import("./GitBackup-BKWJptLG.js"),__vite__mapDeps([31,1,2,32,29,10,9,19,7,25,26,4,6,30,33]),import.meta.url),loadingComponent:s,errorComponent:i,delay:200,timeout:1e4}),k=E("chrome"),h=E(!1),y=E(new Set(["chrome"])),V=()=>!g.loaded&&g.isLoading,d=t=>y.value.has(t),x=t=>{console.log(`切换到 ${t} tab`),y.value.has(t)||(console.log(`首次加载 ${t} 组件`),y.value.add(t))},g=M(),C=q(),b=H();L(async()=>{console.log("Application 组件挂载，检查配置状态...");try{g.loaded?console.log("配置已加载，直接使用"):(console.log("配置未加载，触发加载..."),h.value=!0,await g.loadConfig());const t=[];C.initialized||t.push(C.loadRoles().catch(o=>{console.warn("AI角色加载失败:",o)})),b.initialized||t.push(b.loadProviders().catch(o=>{console.warn("AI提供商加载失败:",o)})),t.length>0&&Promise.all(t).then(()=>{console.log("后台配置加载完成")}).catch(o=>{console.warn("部分后台配置加载失败:",o)})}catch(t){console.error("加载配置失败:",t),$.error("配置加载失败: "+t.message)}finally{h.value=!1}}),T(()=>{});const O=(t="处理中...")=>{h.value=!0},U=()=>{h.value=!1};return f("showLoading",O),f("hideLoading",U),f("configStore",g),f("aiRolesStore",C),f("aiProvidersStore",b),L(()=>{const t=e=>e.target.tagName==="INPUT"||e.target.tagName==="TEXTAREA"||e.target.contentEditable==="true"||e.target.closest(".el-input")||e.target.closest(".el-textarea")||e.target.closest(".code-block")||e.target.closest("pre")||e.target.closest("code")?!0:(e.preventDefault(),!1),o=e=>e.target.tagName==="INPUT"||e.target.tagName==="TEXTAREA"||e.target.closest(".el-input")||e.target.closest(".el-textarea")?!0:(e.preventDefault(),!1),a=e=>{if(e.key==="F12"||e.ctrlKey&&e.shiftKey&&e.key==="I"||e.ctrlKey&&e.shiftKey&&e.key==="C"||e.ctrlKey&&e.key==="U")return e.preventDefault(),!1};document.addEventListener("contextmenu",t),document.addEventListener("dragstart",o),document.addEventListener("keydown",a),T(()=>{document.removeEventListener("contextmenu",t),document.removeEventListener("dragstart",o),document.removeEventListener("keydown",a)})}),(t,o)=>{const a=j,e=X,B=z;return r(),F("div",J,[G((r(),n(e,{modelValue:k.value,"onUpdate:modelValue":o[0]||(o[0]=K=>k.value=K),class:"application-tabs","element-loading-text":"加载配置中...",onTabChange:x},{default:l(()=>[c(a,{label:"Chrome设置",name:"chrome"},{default:l(()=>[d("chrome")?(r(),n(m(A),{key:0})):u("",!0)]),_:1}),c(a,{label:"AI角色",name:"ai-roles"},{default:l(()=>[d("ai-roles")?(r(),n(m(I),{key:0})):u("",!0)]),_:1}),c(a,{label:"AI服务商",name:"ai-providers"},{default:l(()=>[d("ai-providers")?(r(),n(m(P),{key:0})):u("",!0)]),_:1}),c(a,{label:"飞书配置",name:"feishu"},{default:l(()=>[d("feishu")?(r(),n(m(R),{key:0})):u("",!0)]),_:1}),c(a,{label:"聊天设置",name:"chat"},{default:l(()=>[d("chat")?(r(),n(m(S),{key:0})):u("",!0)]),_:1}),c(a,{label:"备份设置",name:"backup"},{default:l(()=>[d("backup")?(r(),n(m(w),{key:0})):u("",!0)]),_:1}),c(a,{label:"Git备份",name:"git"},{default:l(()=>[d("git")?(r(),n(m(D),{key:0})):u("",!0)]),_:1})]),_:1},8,["modelValue"])),[[B,V()]])])}}},ae=N(Q,[["__scopeId","data-v-9545b016"]]);export{ae as default};
