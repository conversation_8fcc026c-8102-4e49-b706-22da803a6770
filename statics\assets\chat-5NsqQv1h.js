import{_ as Fe,a as Be,r as _,c as N,o as Oe,R as Z,E as f,U as Ve,b as M,m,e as p,d as i,n as q,Z as Je,$ as b,p as O,g as u,B as Ue,C as h,ac as Pe,t as qe,e3 as He,e4 as ze,X as H,Y as z,b3 as Le,F as I,a7 as ne,h as We,e5 as je,e6 as Ge,v as L,e7 as Ye,ak as Ke,al as Xe,dO as Ze,ao as Qe,a3 as et,aJ as tt,dW as at,aB as st,aK as ot,aL as nt,bC as lt,bD as rt,e8 as it,e9 as ct,ea as ut,eb as dt,ec as pt,av as Y,aU as vt}from"./entry-BIjVVog3.js";/* empty css               *//* empty css                  */import{useAIRolesStore as ft}from"./aiRoles-BNrBgqza.js";import{useAIProvidersStore as gt}from"./aiProviders-BAOX-EH8.js";import{M as le}from"./MarkdownBubble-CJ_yNM_A.js";import{n as mt}from"./index-browser-OxPLOBIU.js";import{c as ht}from"./apiUtils-CGTCyBFs.js";const wt={class:"chat2-container"},_t={class:"chat-layout"},yt={class:"sidebar-header"},Ct={class:"chat-list-container"},bt=["onClick"],St={key:1,class:"chat-title"},kt={class:"sidebar-footer"},Mt={class:"header-controls"},Tt={class:"control-group model-selector"},xt={class:"control-label"},At={class:"option-content"},Et={class:"option-label"},Nt={class:"option-desc"},Rt={class:"control-group role-selector"},It={class:"control-label"},$t={class:"option-content"},Dt={class:"option-label"},Ft={class:"option-desc"},Bt={class:"control-group memory-toggle"},Ot={class:"memory-text"},Vt={class:"control-group import-export-group"},Jt={key:0,class:"welcome-container"},Ut={key:2,class:"typing-indicator"},Pt={class:"input-extra"},qt={class:"input-foot-left"},Ht={class:"input-foot-maxlength"},zt={__name:"chat",setup(Lt){const Q=Be(),V=ft(),W=gt();vt("configStore",Q);const S=_(!1),c=_(""),w=_(""),k=_([]),T=_(!0),R=_(""),y=_(!1),x=_(!1),D=_(null),g=_([]),A=_(new Set),j=_(null),re=_(!1),ie=_({step:[1,2],interval:50}),ee=N(()=>Q.theme==="dark"),$=N(()=>W.modelOptions.map(e=>({id:e.uniqueId,name:e.label,providerId:e.providerId,providerName:e.providerName,uniqueId:e.uniqueId,config:e.config}))),ce=N(()=>$.value.map(t=>({value:t.id,label:t.name,description:t.providerName?`提供商: ${t.providerName}`:void 0,provider:t.providerName}))),te=N(()=>{if(!w.value)return"AI助手";const t=$.value.find(a=>a.id===w.value);if(t)return t.name;const e=w.value;if(e.includes("/")){const[a,o]=e.split("/"),n=a.charAt(0).toUpperCase()+a.slice(1);let r=o.replace(/-free$/,"").replace(/^[^-]+-/,"").replace(/-/g," ").replace(/\b\w/g,l=>l.toUpperCase());return`${n} ${r}`}return"AI助手"}),ae=N(()=>V.roles.filter(t=>t.isEnabled!==!1)),ue=N(()=>ae.value.map(t=>({value:t.id,label:t.name||t.id,description:t.description}))),J=N(()=>g.value.find(t=>t.id===c.value)),U=N(()=>J.value?.messages||[]);N(()=>A.value.has(c.value));const de=["PVV Chat 可以帮助您进行智能对话、编程辅助、文档编写等。","✨ 支持流式内容渲染，实时显示AI回复！","💡 您可以进行多轮对话，AI会记住上下文内容。","作为AI助手，我会尽力提供准确的信息，但请您核实重要内容。"],pe=[{icon:"icon-at",text:"智能体"},{icon:"icon-standard",text:"词库"},{icon:"icon-add",text:"附件"}],ve=()=>{S.value=!S.value},P=async()=>{if(!w.value){f.warning("请先选择一个模型");return}const t=mt(),e={id:t,title:`新建聊天 ${g.value.length+1}`,name:`新建聊天 ${g.value.length+1}`,messages:[],model_id:w.value,roles:k.value||[],last_updated:Date.now()/1e3};try{await E(e),g.value.unshift(e),c.value=t}catch(a){console.error("Failed to create chat:",a),f.error("创建聊天失败")}},fe=t=>{if(c.value===t)return;c.value=t;const e=g.value.find(a=>a.id===t);e&&(e.model_id&&(w.value=e.model_id),e.roles&&Array.isArray(e.roles)?k.value=e.roles:k.value=[])},ge=async t=>{try{const e=g.value.find(l=>l.id===t),a=e?.title||e?.name||"未命名聊天";await Y.confirm(`确定要删除聊天"${a}"吗？此操作不可撤销。`,"删除聊天",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger",center:!0});const o=await window.pywebview.api.model_controller.delete_chat(t),n=typeof o=="string"?JSON.parse(o):o;if(n.status!=="success")throw new Error(n.message||"Delete failed");const r=g.value.findIndex(l=>l.id===t);r>-1&&(g.value.splice(r,1),c.value===t&&(c.value=g.value[0]?.id||"")),f.success("聊天删除成功")}catch(e){if(e==="cancel")return;console.error("Failed to delete chat:",e),f.error("删除聊天失败")}},me=async()=>{try{const t=g.value.length;await Y.confirm(`确定要清空所有聊天吗？这将删除 ${t} 个聊天记录，此操作不可撤销。`,"清空所有聊天",{confirmButtonText:"确定清空",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger",center:!0,dangerouslyUseHTMLString:!1});const e=await window.pywebview.api.model_controller.clear_all_chats(),a=typeof e=="string"?JSON.parse(e):e;if(!a||a.status!=="success")throw new Error(a?.message||"Failed to clear chats");g.value=[],c.value="",f.success("所有聊天已清空"),await P()}catch(t){if(t==="cancel")return;console.error("Failed to clear chats:",t),f.error("清空聊天失败")}},he=()=>{T.value=!T.value,localStorage.setItem("chat_memory_enabled",T.value),console.log(T.value?"Memory mode enabled":"Memory mode disabled")},we=async(t,e)=>{console.log("Model changed:",t,e);const a=g.value.find(o=>o.id===c.value);a&&(a.model_id=t,await E(a))},_e=async(t,e)=>{console.log("Roles changed:",t,e),k.value=t;const a=g.value.find(o=>o.id===c.value);if(a)if(a.roles=t,await E(a),t.length>0){const o=t.map(n=>{const r=ae.value.find(l=>l.id===n);return r&&r.name||n}).join(", ");f.success(`已启用角色: ${o}`),console.log(`已选择角色: ${o}`)}else f.info("已关闭所有角色设定，后续对话将不使用角色提示"),console.log("已清除所有角色设定")},ye=async()=>{console.log("Roles cleared"),k.value=[];const t=g.value.find(e=>e.id===c.value);t&&(t.roles=[],await E(t),f.info("已清除所有角色设定，后续对话将不使用角色提示"))},E=async t=>{try{return!t.title&&t.name?t.title=t.name:t.title||(t.title=`New Chat ${Date.now()}`),await window.pywebview.api.model_controller.save_chat(t.id,t),!0}catch(e){throw console.error("Failed to save chat:",e),e}},K=async()=>{try{const t=await window.pywebview.api.model_controller.get_all_chats(),e=typeof t=="string"?JSON.parse(t):t;if(e&&e.status==="success"&&Array.isArray(e.data))if(g.value=e.data,g.value.length>0){const a=g.value[0];c.value=a.id,a.model_id&&(w.value=a.model_id)}else await P();else throw new Error(e?.message||"Failed to load chats")}catch(t){console.error("Failed to load chats:",t),f.error("加载聊天历史失败"),await P()}},X=async()=>{if(!R.value.trim()||y.value)return;const t=g.value.find(n=>n.id===c.value);if(!t)return;t.model_id=w.value;const e=[];if(k.value&&k.value.length>0)for(const n of k.value){const r=V.roles.find(l=>l.id===n);r&&r.prompt&&e.push(r.prompt)}const a={role:"user",content:R.value.trim(),timestamp:Date.now()};let o=[];if(T.value){const n=e.length>0?[{role:"system",content:e.join(`

`)}]:[],r=t.messages.filter(l=>l.role!=="system").map(l=>({role:l.role,content:l.content}));o=[...n,...r,a]}else e.length>0&&o.push({role:"system",content:e.join(`

`)}),o.push(a);t.messages.push(a),t.last_updated=Date.now()/1e3,R.value="",A.value.add(c.value),y.value=!0,x.value=!0,F.value=!0,Z(()=>{setTimeout(()=>{oe(!0);const n=document.querySelectorAll(".message-bubble");n.length>0&&n[n.length-1].scrollIntoView({behavior:"smooth",block:"end"})},10)});try{await E(t),await Se(c.value,a.content);const r={stream:!0,...be(w.value)};window.pywebview.api.model_controller.chat(c.value,w.value,o,r)}catch(n){A.value.delete(c.value),y.value=!1,x.value=!1,console.error("Failed to send message:",n),f.error("发送消息失败: "+(n.message||"Unknown error"));try{await E(t),console.log("用户消息已保存，尽管发送过程中出现错误")}catch(r){console.error("保存用户消息失败:",r),f.error("保存消息失败，消息可能会丢失")}}},Ce=async()=>{if(!(!c.value||!A.value.has(c.value)))try{const t=await window.pywebview.api.model_controller.stop_chat(c.value),e=typeof t=="string"?JSON.parse(t):t;if(e.status==="success"){A.value.delete(c.value),y.value=!1,x.value=!1,j.value=null,console.log("Generation stopped"),f.info("已停止生成");const a=g.value.find(o=>o.id===c.value);a&&E(a).catch(o=>{console.error("Failed to save chat after stopping generation:",o)})}else throw new Error(e.message||"Stop failed")}catch(t){console.error("Failed to stop chat:",t),f.error("停止生成失败: "+(t.message||"Unknown error"))}finally{A.value.delete(c.value),y.value=!1,x.value=!1,j.value=null}},be=t=>{try{const e=$.value.find(a=>a.id===t);return e&&e.config?e.config:(console.log("未找到模型配置，使用默认配置"),{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0})}catch(e){return console.error("获取模型配置失败:",e),{temperature:.8,max_tokens:8192,top_p:.8,frequency_penalty:0,presence_penalty:0,stream:!0}}},Se=async(t,e)=>{try{const a=await window.pywebview.api.model_controller.get_chat(t),o=typeof a=="string"?JSON.parse(a):a;if(o.status==="success"){const n=o.data;if(n.messages.filter(l=>l.role==="user").length<=1&&/^新建聊天/.test(n.title)){const l=e.slice(0,20)+(e.length>20?"...":"");await ke(t,l)}}}catch(a){console.error("Failed to auto-update title:",a)}},ke=async(t,e)=>{try{const a=await window.pywebview.api.model_controller.get_chat(t),o=typeof a=="string"?JSON.parse(a):a;if(o.status==="success"){const n=o.data;n.title=e,n.name=e;const r=await window.pywebview.api.model_controller.save_chat(t,n);if((typeof r=="string"?JSON.parse(r):r).status==="success"){const d=g.value.find(v=>v.id===t);return d&&(d.title=e,d.name=e),!0}}return!1}catch(a){return console.error("Failed to update chat title:",a),!1}},Me=async(t,e=null)=>{if(e){f.error("复制失败: "+e.message);return}await ht(t)},Te=async(t,e)=>{if(!y.value&&J.value&&e>0){const a=J.value.messages[e-1];a&&a.role==="user"&&(J.value.messages.splice(e,1),f.info("正在重新生成回复..."),R.value=a.content,await X())}},xe=async(t,e)=>{y.value||t&&t.role==="user"&&(f.info("正在重新发送消息..."),R.value=t.content,await X())},F=_(!0),se=()=>{if(!D.value)return!1;let t=D.value;t.$el&&(t=t.$el);const e=t.querySelector(".messages-container")||t.querySelector('[class*="scroll"]')||t;return e.scrollHeight-e.scrollTop-e.clientHeight<=100},oe=(t=!1)=>{Z(()=>{if(D.value&&(t||F.value)){let e=D.value;e.$el&&(e=e.$el);const a=e.querySelector(".messages-container")||e.querySelector('[class*="scroll"]')||e,o=a.scrollHeight,n=a.clientHeight;a.scrollTop=o,setTimeout(()=>{a.scrollTop<o-n-10&&(a.scrollTop=o,console.log("Re-scrolled to bottom"))},50)}})},Ae=()=>{se()?(F.value=!0,oe()):F.value=!1},Ee=()=>{D.value&&(se()?F.value=!0:F.value=!1)},Ne=(t,e)=>re.value&&t.role==="assistant"&&e===U.value.length-1&&!t.loading&&y.value&&A.value.has(c.value)&&!x.value,Re=()=>{console.log("打字机效果完成")},Ie=async t=>{t==="export"?await $e():t==="import"&&await De()},$e=async()=>{if(!c.value){f.warning("请先选择一个聊天");return}if(U.value.length===0){f.warning("当前聊天没有消息可导出");return}try{const t=new Date,a=`聊天导出_${t.getFullYear()+"-"+String(t.getMonth()+1).padStart(2,"0")+"-"+String(t.getDate()).padStart(2,"0")+"_"+String(t.getHours()).padStart(2,"0")+"-"+String(t.getMinutes()).padStart(2,"0")+"-"+String(t.getSeconds()).padStart(2,"0")}`,{value:o}=await Y.prompt("请输入导出文件名","导出对话",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:a,inputPlaceholder:"请输入文件名（不需要扩展名）",inputValidator:G=>!G||!G.trim()?"文件名不能为空":/[<>:"/\\|?*]/.test(G)?'文件名不能包含以下字符: < > : " / \\ | ? *':G.trim().length>200?"文件名过长，请控制在200个字符以内":!0});if(!o||!o.trim())return;const n=await window.pywebview.api.select_directory(),r=typeof n=="string"?JSON.parse(n):n;if(r.status!=="success")return;const l=r.data,d=o.trim().replace(/[<>:"/\\|?*]/g,"_"),v=d.endsWith(".txt")?d:`${d}.txt`;let s;l.endsWith("/")||l.endsWith("\\")?s=l+v:s=l+"/"+v,console.log("准备导出到路径:",s);const C=await window.pywebview.api.export_chat_to_file(c.value,s),B=typeof C=="string"?JSON.parse(C):C;console.log("导出结果:",B),B.status==="success"?f({message:"回话导出成功。",type:"success",duration:1e3}):(console.error("导出失败详情:",B),f.error(`导出失败: ${B.message}`))}catch(t){if(t==="cancel")return;console.error("导出对话失败:",t),f.error("导出对话失败")}},De=async()=>{try{const t=await window.pywebview.api.select_file_path(),e=typeof t=="string"?JSON.parse(t):t;if(e.status!=="success")return;const a=e.data;if(!a||a.length===0){f.warning("未选择文件");return}const o=a[0],r=await Y.confirm("请选择导入方式：","导入对话",{confirmButtonText:"导入到当前对话",cancelButtonText:"创建新对话",distinguishCancelAndClose:!0,type:"question"}).then(()=>"current").catch(v=>{if(v==="cancel")return"new";throw v})==="current"&&c.value?c.value:null,l=await window.pywebview.api.import_chat_from_file(o,r),d=typeof l=="string"?JSON.parse(l):l;if(d.status==="success"){const v=d.data,s=v.import_mode==="append"?"追加到当前对话":"创建新对话";f.success(`成功${s}: ${v.title} (${v.message_count} 条消息)`),await K(),v.import_mode==="create"||!c.value?c.value=v.chat_id:v.import_mode==="append"&&await K()}else f.error(`导入失败: ${d.message}`)}catch(t){console.error("导入对话失败:",t),f.error("导入对话失败")}};return Oe(async()=>{console.log("Chat2.vue: 开始初始化聊天界面...");try{try{W.initialized?console.log("Chat2.vue: AI提供商配置已初始化，跳过加载"):(console.log("Chat2.vue: 开始加载AI提供商配置..."),await W.loadProviders(!0),console.log("Chat2.vue: AI提供商配置加载完成:",W.providers.length,"个提供商"))}catch(e){console.error("Chat2.vue: 加载AI提供商配置失败:",e)}if(console.log("Chat2.vue: 可用模型数量:",$.value.length),$.value.length===0)console.warn("Chat2.vue: 没有可用的模型，请检查AI提供商配置");else if(!w.value&&$.value.length>0){const e=$.value[0];w.value=e.id,console.log("Chat2.vue: 默认选择模型:",w.value)}try{V.roles.length?console.log("Chat2.vue: AI角色已加载，跳过加载"):(console.log("Chat2.vue: 开始加载AI角色..."),await V.loadRoles(),console.log("Chat2.vue: AI角色加载完成:",V.roles.length,"个角色"))}catch(e){console.error("Chat2.vue: 加载AI角色失败:",e)}try{console.log("Chat2.vue: 开始加载聊天历史..."),await K(),console.log("Chat2.vue: 聊天历史加载完成:",g.value.length,"个聊天")}catch(e){console.error("Chat2.vue: 加载聊天历史失败:",e),await P()}}catch(e){console.error("Chat2.vue: 初始化错误:",e)}const t=localStorage.getItem("chat_memory_enabled");t!==null&&(T.value=t==="true"),console.log("Chat2 interface initialized")}),window.receiveChunk=t=>{try{const e=atob(t),a=new TextDecoder("utf-8").decode(new Uint8Array([...e].map(d=>d.charCodeAt(0)))),o=JSON.parse(a),{chat_id:n,content:r,reasoning:l}=o;if(l&&console.log("Received reasoning content:",l),n===c.value){const d=g.value.find(s=>s.id===n);if(!d)return;d.messages||(d.messages=[]);const v=d.messages[d.messages.length-1];if(!v||v.role!=="assistant"){const s={role:"assistant",content:r||"",reasoningStartTime:l?Date.now():null,timestamp:Date.now()};l&&(s.reasoning=l,s.reasoningCollapsed=!1,s.reasoningTime="Thinking..."),d.messages.push(s)}else if(r&&(v.content+=r),l){v.reasoningStartTime||(v.reasoningStartTime=Date.now()),v.reasoning||(v.reasoning="");const s=l.replace(/\\n/g,`
`);v.reasoning+=s,"reasoningCollapsed"in v||(v.reasoningCollapsed=!1,v.reasoningTime="Thinking...")}Z(()=>{Ae()}),(r&&r.trim()||l&&l.trim())&&(x.value=!1)}}catch(e){console.error("Failed to process message chunk:",e)}},window.onMessageComplete=t=>{if(t===c.value){A.value.delete(t),y.value=!1,x.value=!1,j.value=null;const e=g.value.find(a=>a.id===t);if(e&&e.messages.length>0){const a=e.messages[e.messages.length-1];if(a&&a.reasoning&&a.reasoningStartTime){const n=((Date.now()-a.reasoningStartTime)/1e3).toFixed(1);a.reasoningTime=`for ${n}s`,delete a.reasoningStartTime}E(e).catch(o=>{console.error("Failed to save chat:",o)})}}},window.receiveChatError=t=>{try{const e=atob(t),a=new TextDecoder("utf-8").decode(new Uint8Array([...e].map(l=>l.charCodeAt(0)))),o=JSON.parse(a),{chat_id:n,error_message:r}=o;if(console.log("接收到聊天错误消息:",n,r),f.error({message:`AI回复失败: ${r}`,duration:5e3}),n===c.value){A.value.delete(n),y.value=!1,x.value=!1,j.value=null;const l=g.value.find(d=>d.id===n);if(l){if(l.messages.length>0){const d=l.messages[l.messages.length-1];d&&d.role==="assistant"&&!d.content.trim()&&(d.content=`[Error: ${r}]`,d.isError=!0)}E(l).catch(d=>{console.error("Failed to save chat after error:",d),f.error("保存聊天记录失败，消息可能会丢失")})}}}catch(e){console.error("Failed to process error message:",e)}},Ve(()=>{window.receiveChunk&&delete window.receiveChunk,window.onMessageComplete&&delete window.onMessageComplete,window.receiveChatError&&delete window.receiveChatError}),(t,e)=>{const a=Ue,o=qe,n=Xe,r=Ke,l=nt,d=ot,v=tt;return m(),M("div",wt,[p("div",_t,[p("div",{style:Je({width:S.value?"60px":"280px"}),class:q(["chat-sidebar",{"sidebar-collapsed":S.value}])},[p("div",yt,[S.value?O("",!0):(m(),b(o,{key:0,type:"primary",class:"new-chat-btn",onClick:P},{default:u(()=>[i(a,null,{default:u(()=>[i(h(Pe))]),_:1}),e[3]||(e[3]=p("span",null,"新建聊天",-1))]),_:1})),i(o,{link:"",class:"collapse-btn",onClick:ve},{default:u(()=>[i(a,null,{default:u(()=>[S.value?(m(),b(h(He),{key:0})):(m(),b(h(ze),{key:1}))]),_:1})]),_:1})]),p("div",Ct,[(m(!0),M(H,null,z(g.value,s=>(m(),M("div",{key:s.id,class:q(["chat-item",{active:s.id===c.value}]),onClick:C=>fe(s.id)},[S.value?(m(),b(a,{key:0,class:"chat-icon"},{default:u(()=>[i(h(Le))]),_:1})):O("",!0),S.value?O("",!0):(m(),M("span",St,I(s.title),1)),S.value?O("",!0):(m(),b(o,{key:2,link:"",size:"small",class:"delete-btn",onClick:We(C=>ge(s.id),["stop"])},{default:u(()=>[i(a,null,{default:u(()=>[i(h(ne))]),_:1})]),_:2},1032,["onClick"]))],10,bt))),128))]),p("div",kt,[S.value?O("",!0):(m(),b(o,{key:0,link:"",class:"clear-all-btn",onClick:me},{default:u(()=>[i(a,null,{default:u(()=>[i(h(ne))]),_:1}),e[4]||(e[4]=p("span",null,"清空所有聊天",-1))]),_:1}))])],6),i(h(pt),{class:"main-content"},{default:u(()=>[i(h(je),{class:"chat-header"},{default:u(()=>[i(h(Ge),{title:J.value?.title||"PVV Chat"},{operationArea:u(()=>[p("div",Mt,[p("div",Tt,[p("label",xt,[i(a,{class:"label-icon"},{default:u(()=>[i(h(Ye))]),_:1}),e[5]||(e[5]=L(" 模型 "))]),i(r,{modelValue:w.value,"onUpdate:modelValue":e[0]||(e[0]=s=>w.value=s),placeholder:"选择模型",size:"small",class:"custom-select","popper-class":"custom-select-dropdown",onChange:we},{default:u(()=>[(m(!0),M(H,null,z(ce.value,s=>(m(),b(n,{key:s.value,label:s.label,value:s.value,class:"custom-option"},{default:u(()=>[p("div",At,[p("span",Et,I(s.label),1),p("span",Nt,I(s.description||"AI 模型"),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),p("div",Rt,[p("label",It,[i(a,{class:"label-icon"},{default:u(()=>[i(h(Ze))]),_:1}),e[6]||(e[6]=L(" 角色 "))]),i(r,{modelValue:k.value,"onUpdate:modelValue":e[1]||(e[1]=s=>k.value=s),multiple:"",placeholder:"选择角色",size:"small",class:"custom-select","popper-class":"custom-select-dropdown","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":1,clearable:"",onChange:_e,onClear:ye},{default:u(()=>[(m(!0),M(H,null,z(ue.value,s=>(m(),b(n,{key:s.value,label:s.label,value:s.value,class:"custom-option"},{default:u(()=>[p("div",$t,[p("span",Dt,I(s.label),1),p("span",Ft,I(s.description||"角色设定"),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),p("div",Bt,[i(o,{type:T.value?"success":"",size:"small",class:"memory-btn",onClick:he},{default:u(()=>[i(a,{class:"memory-icon"},{default:u(()=>[T.value?(m(),b(h(Qe),{key:0})):(m(),b(h(et),{key:1}))]),_:1}),p("span",Ot,I(T.value?"记忆模式":"单次对话"),1)]),_:1},8,["type"])]),p("div",Vt,[i(v,{trigger:"click",onCommand:Ie},{dropdown:u(()=>[i(d,null,{default:u(()=>[i(l,{command:"export",disabled:!c.value||U.value.length===0},{default:u(()=>[i(a,null,{default:u(()=>[i(h(lt))]),_:1}),e[8]||(e[8]=L(" 导出对话 "))]),_:1},8,["disabled"]),i(l,{command:"import"},{default:u(()=>[i(a,null,{default:u(()=>[i(h(rt))]),_:1}),e[9]||(e[9]=L(" 导入对话 "))]),_:1})]),_:1})]),default:u(()=>[i(o,{size:"small",class:"import-export-btn"},{default:u(()=>[i(a,null,{default:u(()=>[i(h(at))]),_:1}),e[7]||(e[7]=p("span",null,"更多",-1)),i(a,{class:"el-icon--right"},{default:u(()=>[i(h(st))]),_:1})]),_:1})]),_:1})])])]),_:1},8,["title"])]),_:1}),i(h(it),{class:q(["messages-container",{sending:y.value}]),ref_key:"messagesContainer",ref:D,onScroll:Ee},{default:u(()=>[U.value.length===0?(m(),M("div",Jt,[i(h(ct),{title:"PVV Chat",subTitle:"Hi，欢迎使用智能聊天助手",description:de})])):(m(!0),M(H,{key:1},z(U.value,(s,C)=>(m(),b(le,{key:C,content:s.content,messageType:s.role,isError:s.isError||!1,loading:s.loading||!1,timestamp:s.timestamp,senderName:s.role==="user"?"":te.value,reasoning:s.reasoning||"",reasoningTime:s.reasoningTime||0,hasReasoning:!!s.reasoning,showReasoning:!0,useDefaultIcons:!0,disabled:y.value,theme:ee.value?"dark":"light",variant:"default",showAvatar:!0,showHeader:!0,showActions:!0,loadingText:"正在思考...",typing:Ne(s,C),typingOptions:ie.value,onCopy:Me,onRegenerate:B=>Te(s,C),onResend:B=>xe(s,C),onTypingEnd:Re},null,8,["content","messageType","isError","loading","timestamp","senderName","reasoning","reasoningTime","hasReasoning","disabled","theme","typing","typingOptions","onRegenerate","onResend"]))),128)),x.value?(m(),M("div",Ut,[i(le,{content:"",messageType:"assistant",loading:!0,senderName:te.value,reasoning:"",reasoningTime:0,hasReasoning:!1,showReasoning:!1,useDefaultIcons:!0,disabled:!0,theme:ee.value?"dark":"light",showAvatar:!0,showHeader:!1,showActions:!1,loadingText:"正在思考..."},null,8,["senderName","theme"])])):O("",!0)]),_:1},8,["class"]),i(h(ut),{class:q(["input-area",{sending:y.value}])},{default:u(()=>[i(h(dt),{value:R.value,maxLength:2e3,loading:y.value,showCount:"",onChange:e[2]||(e[2]=s=>R.value=s),onSubmit:X,onCancel:Ce},{extra:u(()=>[p("div",Pt,[p("div",qt,[(m(),M(H,null,z(pe,(s,C)=>p("span",{key:C,class:"input-foot-item"},[p("i",{class:q(s.icon)},null,2),L(" "+I(s.text),1)])),64)),e[10]||(e[10]=p("span",{class:"input-foot-dividing-line"},null,-1)),p("span",Ht,I(R.value.length)+"/2000",1)]),e[11]||(e[11]=p("div",{class:"input-foot-right"},[p("span",{class:"input-hint"},"AI 可能会出错，请核实重要信息。")],-1))])]),_:1},8,["value","loading"])]),_:1},8,["class"])]),_:1})])])}}},ea=Fe(zt,[["__scopeId","data-v-15f846b0"]]);export{ea as default};
