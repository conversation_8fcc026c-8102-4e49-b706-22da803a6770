import{_ as Ge,i as He,r as k,o as Xe,c as re,b as f,m as p,d as o,$ as N,p as V,g as r,e as t,X as D,Y as W,C as u,n as B,F as m,t as Ye,h as Q,B as Ze,ab as Ke,ah as ne,bH as ie,v as w,a5 as Qe,bC as ee,a7 as de,ac as et,bI as tt,s as st,ag as ot,j as lt,ak as at,al as rt,Z as H,bE as R,an as nt,D as te,k as it,bJ as ce,q as dt,au as ue,V as ct,aB as ut,W as pt,G as vt,aI as ft,E as c,av as z}from"./entry-BIjVVog3.js";/* empty css                *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                */import{u as mt}from"./book-BHcNewcO.js";import"./apiUtils-CGTCyBFs.js";const _t={class:"writing-manager"},wt={class:"card-header"},yt={class:"header-actions"},gt={class:"books-container"},ht={class:"books-grid"},kt=["data-book-type","data-encrypted"],bt={class:"book-header"},Ct={class:"title-container"},Vt={class:"book-meta"},xt={class:"word-count"},Bt={class:"update-time"},Et={class:"book-description"},At={class:"book-stats"},St={class:"stat-item"},Pt={class:"stat-item"},$t={class:"book-actions"},Dt={class:"action-group primary"},Tt={class:"action-group secondary"},Lt={class:"card-header"},Ut={class:"writing-actions"},Ft={class:"editor-container"},qt={class:"book-create-content"},Mt={class:"create-form-wrapper"},Nt={class:"settings-section"},Wt={class:"form-row"},Rt={class:"form-item"},zt={class:"form-row"},Jt={class:"form-item"},Ot={class:"form-row"},It={class:"form-item"},jt={class:"settings-section"},Gt={class:"style-selector"},Ht=["onClick"],Xt={class:"style-preview"},Yt={class:"preview-content"},Zt={class:"preview-description"},Kt={key:0,class:"style-check"},Qt={class:"settings-section"},es={class:"form-row"},ts={class:"form-item"},ss={class:"form-row"},os={class:"form-item"},ls={class:"form-row"},as={class:"form-item"},rs={class:"warning-text"},ns={class:"dialog-footer"},is={key:0,class:"book-settings-content"},ds={class:"settings-form-wrapper"},cs={class:"settings-section"},us={class:"form-row"},ps={class:"form-item"},vs={class:"form-row"},fs={class:"form-item"},ms={class:"settings-section"},_s={class:"style-selector"},ws=["onClick"],ys={class:"style-preview"},gs={class:"preview-content"},hs={class:"preview-desc"},ks={key:0,class:"style-check"},bs={class:"settings-section"},Cs={class:"password-section"},Vs={key:0,class:"password-setup"},xs={key:1,class:"password-status"},Bs={class:"encrypted-info"},Es={class:"encrypted-badge"},As={class:"dialog-footer"},Ss={class:"warning-text"},Ps={class:"dialog-footer"},$s={class:"warning-text"},Ds={class:"dialog-footer"},Ts={class:"export-header"},Ls={class:"header-left"},Us={class:"header-icon"},Fs={key:0,class:"export-content"},qs={class:"quick-actions-bar"},Ms={class:"book-title-info"},Ns={class:"book-name"},Ws={class:"book-word-count"},Rs={class:"quick-buttons"},zs={class:"chapters-area"},Js={class:"chapters-list"},Os={key:0,class:"empty-chapters"},Is={class:"volume-header"},js=["onClick"],Gs={key:1,class:"indeterminate-mark"},Hs=["onClick"],Xs={class:"volume-info"},Ys={class:"volume-name"},Zs={class:"volume-stats"},Ks={class:"volume-words"},Qs={key:0,class:"chapter-items"},eo=["onClick"],to={class:"chapter-checkbox-wrapper"},so={class:"chapter-info"},oo={class:"chapter-name"},lo={class:"chapter-words"},ao={class:"export-footer"},ro={class:"footer-info"},no={class:"footer-actions"},io=["disabled"],co={__name:"写作",setup(uo){const X=He(),g=mt(),T=k(!1),L=k(!1),E=k(!1),i=k({}),b=k(null),J=k(""),A=k(null),d=k([]),pe=k("txt"),S=k(!1),U=k(!1),F=k(!1),C=k({password:"",confirmPassword:""}),P=k({password:""}),q=k(!1),se=k([{value:"classic-blue",label:"经典蓝调",description:"专业稳重，适合商务类作品",color:"#4a90e2"},{value:"warm-orange",label:"温暖橙光",description:"活力温馨，适合生活类作品",color:"#ff8c42"},{value:"fresh-green",label:"清新绿意",description:"自然清新，适合治愈类作品",color:"#2ecc71"},{value:"elegant-purple",label:"优雅紫韵",description:"神秘优雅，适合奇幻类作品",color:"#9b59b6"},{value:"mysterious-dark",label:"神秘深邃",description:"沉稳内敛，适合悬疑类作品",color:"#34495e"},{value:"minimal-gray",label:"简约灰调",description:"简洁现代，适合科技类作品",color:"#95a5a6"},{value:"sakura-pink",label:"樱花粉韵",description:"浪漫温柔，适合言情类作品",color:"#ff6b9d"},{value:"deep-ocean",label:"深海蓝调",description:"深邃宁静，适合哲学类作品",color:"#1e3a8a"},{value:"emerald-oasis",label:"翡翠绿洲",description:"生机盎然，适合冒险类作品",color:"#059669"},{value:"sunset-glow",label:"夕阳红霞",description:"热情奔放，适合青春类作品",color:"#dc2626"},{value:"lavender-dream",label:"薰衣草紫",description:"梦幻唯美，适合童话类作品",color:"#7c3aed"},{value:"amber-gold",label:"琥珀金辉",description:"典雅华贵，适合历史类作品",color:"#d97706"}]),v=k({title:"",description:"",type:"draft",book_style:"classic-blue",password:"",confirmPassword:""}),ve={title:[{required:!0,message:"请输入书名",trigger:"blur"}],type:[{required:!0,message:"请选择类型",trigger:"blur"}]};Xe(()=>{g.loadBooks()});const fe=async()=>{if(q.value){if(v.value.password!==v.value.confirmPassword){c.error("两次输入的密码不一致");return}if(v.value.password.length<6){c.error("密码长度至少6个字符");return}}try{const s=g.bookList.filter(a=>a.title===v.value.title);if(s.length>0)if(s.filter(n=>n.encrypted).length>0){await z.confirm("已存在同名加密书籍，创建新书籍可能导致无法解密原有内容。请使用其他名称。","警告",{confirmButtonText:"更改名称",cancelButtonText:"取消",type:"warning"});return}else await z.confirm("已存在同名书籍，继续创建将可能覆盖现有书籍。是否继续？","提示",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"});await g.createBook(v.value)&&(c.success("创建成功"),T.value=!1,v.value={title:"",description:"",type:"draft",book_style:"classic-blue",password:"",confirmPassword:""},q.value=!1)}catch(s){s!=="cancel"&&(console.error("创建失败:",s),c.error("创建失败："+(s.message||"未知错误")))}},me=async s=>{try{await z.confirm("注意：为了安全只会删除到软件垃圾桶目录，请手动找到目录确认删除！！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await g.removeBook(s)}catch(e){e!=="cancel"&&console.error("删除失败:",e)}},_e=s=>{X.push({name:"bookSettings",params:{id:s.id},query:{title:s.title}})},we=async s=>{try{X.push(`/book/editor/${s.id}`)}catch(e){console.error("导航到编辑器页面失败:",e),c.error("导航到编辑器页面失败："+e.message)}},ye=async()=>{if(b.value)try{await g.updateBook(b.value.id,{...b.value,content:J.value,word_count:J.value.length})&&(c.success("保存成功"),g.loadBooks())}catch(s){console.error("保存失败:",s),c.error("保存失败："+s.message)}},O=()=>{T.value=!0,v.value={title:"",description:"",type:"draft",book_style:"classic-blue",password:"",confirmPassword:""}},ge=s=>{i.value={...s,book_style:s.book_style||(s.theme_color?Y(s.theme_color):"classic-blue")},L.value=!0},he=s=>{i.value.book_style=s,b.value&&b.value.id===i.value.id&&(b.value.book_style=s);const e=g.bookList.find(a=>a.id===i.value.id);e&&(e.book_style=s)},ke=async()=>{try{if(console.log("开始更新书籍设置:",i.value),!i.value||!i.value.id)throw new Error("书籍信息无效");const s={id:i.value.id,title:i.value.title,description:i.value.description,book_style:i.value.book_style,type:i.value.type,encrypted:i.value.encrypted,created_at:i.value.created_at,updated_at:i.value.updated_at,word_count:i.value.word_count};if(i.value.encrypted&&(s.salt=i.value.salt,s.iv=i.value.iv,s.checksum=i.value.checksum),console.log("准备发送的书籍数据:",s),await g.updateBook(i.value.id,s))c.success("设置更新成功"),L.value=!1,setTimeout(async()=>{if(await g.loadBooks(),b.value&&b.value.id===i.value.id){const a=g.bookList.find(n=>n.id===i.value.id);a&&(b.value={...a})}},100);else throw new Error("更新失败，未收到成功响应")}catch(s){console.error("更新失败:",s),c.error("更新失败："+(s.message||"未知错误"))}},oe=s=>s?new Date(s).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}):"",be=s=>s.stats_cache&&typeof s.stats_cache.total_chapter_count=="number"?s.stats_cache.total_chapter_count:s.chapter_count||0,Ce=s=>s.stats_cache&&typeof s.stats_cache.total_word_count=="number"?s.stats_cache.total_word_count:s.word_count||0,Ve=s=>{if(!s.updated_at)return"未编辑";const e=new Date,a=new Date(s.updated_at),n=e-a,_=Math.floor(n/(1e3*60*60*24));return _===0?"今天":_===1?"昨天":_<30?`${_}天前`:oe(s.updated_at)},xe=s=>{const e="book-style";let a="classic-blue";s.book_style?a=s.book_style:s.theme_color&&(a=Y(s.theme_color));const n=s.type==="draft"?"draft-type":"work-type",_=s.encrypted?"encrypted-book":"";return[e,`style-${a}`,n,_].filter(Boolean).join(" ")},Y=s=>s&&{"#409EFF":"classic-blue","#67C23A":"fresh-green","#E6A23C":"warm-orange","#F56C6C":"elegant-purple","#909399":"minimal-gray","#000000":"mysterious-dark"}[s]||"classic-blue",Be=s=>{if(!s)return"";let e="classic-blue";return s.book_style?e=s.book_style:s.theme_color&&(e=Y(s.theme_color)),`writing-style-${e}`},Ee=s=>{if(!s||!s.id){c.error("无效的书籍信息");return}X.push({name:"bookTimeline",params:{id:s.id},query:{title:s.title}})},Ae=async s=>{try{if(A.value={...s},d.value=[],E.value=!0,c.info("正在加载书籍结构..."),console.log("准备获取书籍结构，书籍ID:",s.id),!s.id)throw new Error("无效的书籍ID");const e=window.pywebview?.api;if(!e)throw console.error("API未正确初始化"),new Error("系统API未就绪");console.log("调用get_volumes方法...");const a=await e.book_controller.get_volumes(s.id);if(console.log("获取书籍结构响应:",a),!a)throw new Error("获取书籍结构时服务器无响应");let n=a;if(typeof a=="string")try{n=JSON.parse(a)}catch(x){throw console.error("解析响应失败:",x),new Error("解析书籍数据失败")}if(n.status!=="success")throw new Error(n.message||"加载书籍结构失败");const _=n.data?.volumes||[];if(_.length===0)throw new Error("此书籍没有任何卷或章节");console.log("成功获取卷数量:",_.length),d.value=_.map(x=>({...x,selected:!0,expanded:!0,chapters:(x.chapters||[]).map(M=>({...M,selected:!0}))}));const y=d.value.reduce((x,M)=>x+M.chapters.length,0);y===0?c.warning("此书籍没有任何章节内容"):c.success(`成功加载 ${d.value.length} 卷 ${y} 章内容`)}catch(e){console.error("准备导出失败:",e),c.error("准备导出失败："+(e.message||"未知错误")),d.value=[],A.value=null,E.value=!1}},Se=()=>{Array.isArray(d.value)&&d.value.forEach(s=>{s.selected=!0,s.chapters&&s.chapters.forEach(e=>{e.selected=!0})})},Pe=()=>{Array.isArray(d.value)&&d.value.forEach(s=>{s.selected=!1,s.chapters&&s.chapters.forEach(e=>{e.selected=!1})})},$e=s=>{if(!Array.isArray(d.value))return;const e=d.value[s];e&&e.chapters&&(e.selected=e.chapters.every(a=>a.selected))},De=s=>{if(!Array.isArray(d.value))return;const e=d.value[s];e&&(e.expanded=!e.expanded)},Te=s=>{if(!Array.isArray(d.value))return;const e=d.value[s];if(e&&e.chapters){const a=!e.selected;e.selected=a,e.chapters.forEach(n=>{n.selected=a})}},Le=(s,e)=>{if(!Array.isArray(d.value))return;const a=d.value[s];a&&a.chapters&&a.chapters[e]&&(a.chapters[e].selected=!a.chapters[e].selected,$e(s))},Z=s=>{if(!Array.isArray(d.value))return!1;const e=d.value[s];if(!e||!e.chapters)return!1;const a=e.chapters.filter(n=>n.selected).length;return a>0&&a<e.chapters.length},Ue=s=>{if(!Array.isArray(d.value))return 0;const e=d.value[s];return!e||!e.chapters?0:e.chapters.filter(a=>a.selected).length},Fe=s=>{if(!Array.isArray(d.value))return 0;const e=d.value[s];return!e||!e.chapters?0:e.chapters.filter(a=>a.selected).reduce((a,n)=>a+(n.word_count||0),0)},I=re(()=>Array.isArray(d.value)?d.value.reduce((s,e)=>s+(e.chapters||[]).filter(a=>a.selected).length,0):0),qe=re(()=>Array.isArray(d.value)?d.value.reduce((s,e)=>s+(e.chapters||[]).filter(a=>a.selected).reduce((a,n)=>a+(n.word_count||0),0),0):0),Me=()=>{E.value=!1},Ne=async()=>{try{if(!Array.isArray(d.value)){console.error("exportVolumes 不是数组:",d.value),c.error("导出数据异常，请重新打开导出面板");return}if(I.value===0){c.warning("请选择至少一个章节");return}S.value=!0,c.info("正在准备导出..."),console.log("准备导出数据，书籍ID:",A.value.id),console.log("exportVolumes 类型:",typeof d.value,"是否为数组:",Array.isArray(d.value)),console.log("exportVolumes 内容:",d.value);const s={book_id:A.value.id,format:pe.value,volumes:d.value.map(_=>({id:_.id,title:_.title,chapters:(_.chapters||[]).filter(y=>y.selected).map(y=>({id:y.id,title:y.title}))})).filter(_=>_.chapters.length>0)};console.log("导出数据结构:",JSON.stringify(s,null,2));const e=window.pywebview?.api;if(!e)throw console.error("API未正确初始化"),new Error("系统API未就绪");console.log("调用export_book方法...");const a=await e.book_controller.export_book(s);if(console.log("导出响应:",a),!a)throw new Error("导出时服务器无响应");let n=a;if(typeof a=="string")try{n=JSON.parse(a)}catch(_){throw console.error("解析响应失败:",_),new Error("解析导出结果失败")}if(n.status!=="success")throw new Error(n.message||"导出失败");c.success(n.message||"导出成功"),E.value=!1}catch(s){console.error("导出失败:",s),c.error("导出失败："+(s.message||"未知错误"))}finally{S.value=!1}},We=async()=>{if(b.value)try{c.info("正在导出内容..."),c.success("内容导出成功")}catch(s){console.error("导出失败:",s),c.error("导出失败："+(s.message||"未知错误"))}},Re=(s,e,a)=>{e!==C.value.password?a(new Error("两次输入的密码不一致")):a()},ze=()=>{U.value=!0,C.value={password:"",confirmPassword:""}},Je=()=>{F.value=!0,P.value={password:""}},Oe=async()=>{if(C.value.password!==C.value.confirmPassword){c.error("两次输入的密码不一致");return}if(C.value.password.length<6){c.error("密码长度至少6个字符");return}try{await z.confirm("设置密码后，书籍内容将被加密保存。请务必记住密码，密码丢失将无法恢复内容！","重要提示",{confirmButtonText:"确认设置",cancelButtonText:"取消",type:"warning"}),c.info("正在加密书籍内容，请稍候..."),await g.setBookPassword(i.value.id,C.value.password)&&(c.success("密码设置成功，书籍内容已加密"),U.value=!1,setTimeout(async()=>{await g.loadBooks();const e=g.bookList.find(a=>a.id===i.value.id);e&&(i.value={...e})},100))}catch(s){s!=="cancel"&&(console.error("设置密码失败:",s),c.error("设置密码失败："+s.message))}},Ie=async()=>{if(!P.value.password){c.error("请输入当前密码");return}try{await z.confirm("移除密码保护后，书籍内容将以明文形式保存，确定要继续吗？","确认移除",{confirmButtonText:"确认移除",cancelButtonText:"取消",type:"warning"}),c.info("正在解密书籍内容，请稍候..."),await g.removeBookPassword(i.value.id,P.value.password)&&(c.success("密码已移除，书籍内容已解密"),F.value=!1,setTimeout(async()=>{await g.loadBooks();const e=g.bookList.find(a=>a.id===i.value.id);e&&(i.value={...e})},100))}catch(s){s!=="cancel"&&(console.error("移除密码失败:",s),c.error("移除密码失败："+(s.message||"密码可能错误")))}};return(s,e)=>{const a=Ye,n=Ze,_=tt,y=st,x=rt,M=at,je=nt,j=lt,le=ot,G=it,K=dt;return p(),f("div",_t,[o(_,{class:"book-list"},{header:r(()=>[t("div",wt,[t("div",yt,[o(a,{type:"primary",onClick:O},{default:r(()=>e[23]||(e[23]=[w("新建草稿")])),_:1}),o(a,{type:"primary",onClick:O},{default:r(()=>e[24]||(e[24]=[w("新建作品")])),_:1}),o(a,{type:"primary",onClick:O},{default:r(()=>e[25]||(e[25]=[w("导入草稿")])),_:1})])])]),default:r(()=>[t("div",gt,[t("div",ht,[(p(!0),f(D,null,W(u(g).bookList,l=>(p(),f("div",{key:l.id,class:"book-card"},[t("div",{class:B(["book-content",xe(l)]),"data-book-type":l.type,"data-encrypted":l.encrypted},[t("div",bt,[t("div",Ct,[t("h3",null,m(l.title),1)]),o(a,{class:"settings-btn",onClick:Q(h=>ge(l),["stop"])},{default:r(()=>[o(n,null,{default:r(()=>[o(u(Ke))]),_:1})]),_:2},1032,["onClick"])]),t("div",Vt,[t("span",xt,m(Ce(l).toLocaleString())+"字",1),t("span",Bt,m(oe(l.updated_at)),1)]),t("p",Et,m(l.description),1),t("div",At,[t("div",St,[o(n,null,{default:r(()=>[o(u(ne))]),_:1}),t("span",null,m(be(l))+"章",1)]),t("div",Pt,[o(n,null,{default:r(()=>[o(u(ie))]),_:1}),t("span",null,m(Ve(l)),1)])])],10,kt),t("div",$t,[t("div",Dt,[o(a,{type:"primary",class:"action-btn",onClick:h=>we(l)},{default:r(()=>[o(n,null,{default:r(()=>[o(u(Qe))]),_:1}),e[26]||(e[26]=w(" 写作 "))]),_:2},1032,["onClick"])]),t("div",Tt,[o(a,{type:"success",class:"action-btn",onClick:h=>_e(l)},{default:r(()=>e[27]||(e[27]=[w(" 设定 ")])),_:2},1032,["onClick"]),o(a,{type:"warning",class:"action-btn icon-btn",onClick:h=>Ee(l)},{default:r(()=>[o(n,null,{default:r(()=>[o(u(ie))]),_:1})]),_:2},1032,["onClick"]),o(a,{type:"info",class:"action-btn icon-btn",onClick:h=>Ae(l)},{default:r(()=>[o(n,null,{default:r(()=>[o(u(ee))]),_:1})]),_:2},1032,["onClick"]),o(a,{type:"danger",class:"action-btn icon-btn",onClick:h=>me(l)},{default:r(()=>[o(n,null,{default:r(()=>[o(u(de))]),_:1})]),_:2},1032,["onClick"])])])]))),128)),t("div",{class:"book-card new-book",onClick:O},[o(n,null,{default:r(()=>[o(u(et))]),_:1}),e[28]||(e[28]=t("span",null,"新建书籍",-1))])])])]),_:1}),b.value?(p(),N(_,{key:0,class:B(["writing-area",Be(b.value)])},{header:r(()=>[t("div",Lt,[t("span",null,m(b.value.title),1),t("div",Ut,[o(a,{type:"success",onClick:ye},{default:r(()=>e[29]||(e[29]=[w("保存")])),_:1}),o(a,{type:"info",onClick:We},{default:r(()=>e[30]||(e[30]=[w("导出")])),_:1})])])]),default:r(()=>[t("div",Ft,[o(y,{modelValue:J.value,"onUpdate:modelValue":e[0]||(e[0]=l=>J.value=l),type:"textarea",rows:20,placeholder:"开始创作...",resize:"none"},null,8,["modelValue"])])]),_:1},8,["class"])):V("",!0),o(G,{modelValue:T.value,"onUpdate:modelValue":e[8]||(e[8]=l=>T.value=l),title:v.value.type==="draft"?"📝 新建草稿":"📚 新建作品",width:"700px",class:"book-create-dialog","close-on-click-modal":!1,"destroy-on-close":!1,"append-to-body":""},{footer:r(()=>[t("span",ns,[o(a,{onClick:e[7]||(e[7]=l=>T.value=!1)},{default:r(()=>e[41]||(e[41]=[w("取消")])),_:1}),o(a,{type:"primary",onClick:fe},{default:r(()=>e[42]||(e[42]=[w("创建")])),_:1})])]),default:r(()=>[t("div",qt,[o(le,{class:"create-scroll-container","max-height":"500px"},{default:r(()=>[t("div",Mt,[o(j,{ref:"bookFormRef",model:v.value,rules:ve,"label-width":"0px",class:"create-form"},{default:r(()=>[t("div",Nt,[e[34]||(e[34]=t("h3",{class:"section-title"},"📝 基本信息",-1)),t("div",Wt,[t("div",Rt,[e[31]||(e[31]=t("label",{class:"form-label"},"书名",-1)),o(y,{modelValue:v.value.title,"onUpdate:modelValue":e[1]||(e[1]=l=>v.value.title=l),placeholder:"请输入书名",class:"styled-input"},null,8,["modelValue"])])]),t("div",zt,[t("div",Jt,[e[32]||(e[32]=t("label",{class:"form-label"},"描述",-1)),o(y,{modelValue:v.value.description,"onUpdate:modelValue":e[2]||(e[2]=l=>v.value.description=l),type:"textarea",rows:3,placeholder:"请输入书籍描述",class:"styled-textarea"},null,8,["modelValue"])])]),t("div",Ot,[t("div",It,[e[33]||(e[33]=t("label",{class:"form-label"},"类型",-1)),o(M,{modelValue:v.value.type,"onUpdate:modelValue":e[3]||(e[3]=l=>v.value.type=l),placeholder:"请选择类型",class:"styled-select"},{default:r(()=>[o(x,{label:"草稿",value:"draft"}),o(x,{label:"作品",value:"work"})]),_:1},8,["modelValue"])])])]),t("div",jt,[e[35]||(e[35]=t("h3",{class:"section-title"},"🎨 书籍风格",-1)),t("div",Gt,[(p(!0),f(D,null,W(se.value,l=>(p(),f("div",{key:l.value,class:B(["style-card",{selected:v.value.book_style===l.value,[`preview-${l.value}`]:!0}]),onClick:h=>v.value.book_style=l.value},[t("div",Xt,[t("div",{class:"preview-header",style:H({backgroundColor:l.color})},null,4),t("div",Yt,[t("div",{class:"preview-title",style:H({color:l.color})},m(l.label),5),t("div",Zt,m(l.description),1)])]),v.value.book_style===l.value?(p(),f("div",Kt,[o(n,null,{default:r(()=>[o(u(R))]),_:1})])):V("",!0)],10,Ht))),128))])]),t("div",Qt,[e[40]||(e[40]=t("h3",{class:"section-title"},"🔒 密码保护",-1)),t("div",es,[t("div",ts,[e[36]||(e[36]=t("label",{class:"form-label"},"启用密码",-1)),o(je,{modelValue:q.value,"onUpdate:modelValue":e[4]||(e[4]=l=>q.value=l)},null,8,["modelValue"])])]),q.value?(p(),f(D,{key:0},[t("div",ss,[t("div",os,[e[37]||(e[37]=t("label",{class:"form-label"},"密码",-1)),o(y,{modelValue:v.value.password,"onUpdate:modelValue":e[5]||(e[5]=l=>v.value.password=l),type:"password",placeholder:"请输入密码","show-password":"",class:"styled-input"},null,8,["modelValue"])])]),t("div",ls,[t("div",as,[e[38]||(e[38]=t("label",{class:"form-label"},"确认密码",-1)),o(y,{modelValue:v.value.confirmPassword,"onUpdate:modelValue":e[6]||(e[6]=l=>v.value.confirmPassword=l),type:"password",placeholder:"请再次输入密码","show-password":"",class:"styled-input"},null,8,["modelValue"])])]),t("div",rs,[o(n,null,{default:r(()=>[o(u(te))]),_:1}),e[39]||(e[39]=t("span",null,"请牢记您的密码，密码丢失将无法恢复书籍内容！",-1))])],64)):V("",!0)])]),_:1},8,["model"])])]),_:1})])]),_:1},8,["modelValue","title"]),o(G,{modelValue:L.value,"onUpdate:modelValue":e[12]||(e[12]=l=>L.value=l),title:"📚 书籍设置",width:"700px",class:"book-settings-dialog","close-on-click-modal":!1,"destroy-on-close":!1,"append-to-body":""},{footer:r(()=>[t("span",As,[o(a,{onClick:e[11]||(e[11]=l=>L.value=!1)},{default:r(()=>e[53]||(e[53]=[w("取消")])),_:1}),o(a,{type:"primary",onClick:ke},{default:r(()=>e[54]||(e[54]=[w("保存")])),_:1})])]),default:r(()=>[i.value?(p(),f("div",is,[o(le,{class:"settings-scroll-container","max-height":"500px"},{default:r(()=>[t("div",ds,[o(j,{model:i.value,"label-width":"0px",class:"settings-form"},{default:r(()=>[t("div",cs,[e[45]||(e[45]=t("h3",{class:"section-title"},"📝 基本信息",-1)),t("div",us,[t("div",ps,[e[43]||(e[43]=t("label",{class:"form-label"},"书名",-1)),o(y,{modelValue:i.value.title,"onUpdate:modelValue":e[9]||(e[9]=l=>i.value.title=l),placeholder:"请输入书名",class:"styled-input"},null,8,["modelValue"])])]),t("div",vs,[t("div",fs,[e[44]||(e[44]=t("label",{class:"form-label"},"描述",-1)),o(y,{modelValue:i.value.description,"onUpdate:modelValue":e[10]||(e[10]=l=>i.value.description=l),type:"textarea",rows:3,placeholder:"请输入书籍描述",class:"styled-textarea"},null,8,["modelValue"])])])]),t("div",ms,[e[46]||(e[46]=t("h3",{class:"section-title"},"🎨 书籍风格",-1)),t("div",_s,[(p(!0),f(D,null,W(se.value,l=>(p(),f("div",{key:l.value,class:B(["style-card",{selected:i.value.book_style===l.value,[`preview-${l.value}`]:!0}]),onClick:h=>he(l.value)},[t("div",ys,[t("div",{class:"preview-header",style:H({backgroundColor:l.color})},null,4),t("div",gs,[t("div",{class:"preview-title",style:H({color:l.color})},m(l.label),5),t("div",hs,m(l.description),1)])]),i.value.book_style===l.value?(p(),f("div",ks,[o(n,null,{default:r(()=>[o(u(R))]),_:1})])):V("",!0)],10,ws))),128))])]),t("div",bs,[e[52]||(e[52]=t("h3",{class:"section-title"},"🔒 密码保护",-1)),t("div",Cs,[i.value.encrypted?(p(),f("div",xs,[t("div",Bs,[t("div",Es,[o(n,{class:"lock-icon"},{default:r(()=>[o(u(ce))]),_:1}),e[49]||(e[49]=t("span",null,"此书籍已加密",-1))]),e[50]||(e[50]=t("p",{class:"info-text"},"书籍内容已加密保护，访问时需要输入密码。",-1))]),o(a,{type:"danger",onClick:Je,class:"password-btn danger"},{default:r(()=>[o(n,null,{default:r(()=>[o(u(de))]),_:1}),e[51]||(e[51]=w(" 移除密码保护 "))]),_:1})])):(p(),f("div",Vs,[e[48]||(e[48]=t("div",{class:"password-info"},[t("p",{class:"info-text"},"设置密码后，所有章节内容将被加密保存，访问时需要输入密码。")],-1)),o(a,{type:"primary",onClick:ze,class:"password-btn"},{default:r(()=>[o(n,null,{default:r(()=>[o(u(ce))]),_:1}),e[47]||(e[47]=w(" 设置密码保护 "))]),_:1})]))])])]),_:1},8,["model"])])]),_:1})])):V("",!0)]),_:1},8,["modelValue"]),o(G,{modelValue:U.value,"onUpdate:modelValue":e[16]||(e[16]=l=>U.value=l),title:"设置密码保护",width:"400px","append-to-body":"","destroy-on-close":""},{footer:r(()=>[t("span",Ps,[o(a,{onClick:e[15]||(e[15]=l=>U.value=!1)},{default:r(()=>e[56]||(e[56]=[w("取消")])),_:1}),o(a,{type:"primary",onClick:Oe},{default:r(()=>e[57]||(e[57]=[w("确认设置")])),_:1})])]),default:r(()=>[o(j,{model:C.value,ref:"passwordFormRef","label-width":"100px"},{default:r(()=>[o(K,{label:"密码",prop:"password",rules:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少6个字符",trigger:"blur"}]},{default:r(()=>[o(y,{modelValue:C.value.password,"onUpdate:modelValue":e[13]||(e[13]=l=>C.value.password=l),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1}),o(K,{label:"确认密码",prop:"confirmPassword",rules:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:Re,trigger:"blur"}]},{default:r(()=>[o(y,{modelValue:C.value.confirmPassword,"onUpdate:modelValue":e[14]||(e[14]=l=>C.value.confirmPassword=l),type:"password",placeholder:"请再次输入密码","show-password":""},null,8,["modelValue"])]),_:1},8,["rules"])]),_:1},8,["model"]),t("div",Ss,[o(n,null,{default:r(()=>[o(u(te))]),_:1}),e[55]||(e[55]=t("span",null,"请牢记您的密码，密码丢失将无法恢复书籍内容！",-1))])]),_:1},8,["modelValue"]),o(G,{modelValue:F.value,"onUpdate:modelValue":e[19]||(e[19]=l=>F.value=l),title:"移除密码保护",width:"400px","append-to-body":"","destroy-on-close":""},{footer:r(()=>[t("span",Ds,[o(a,{onClick:e[18]||(e[18]=l=>F.value=!1)},{default:r(()=>e[59]||(e[59]=[w("取消")])),_:1}),o(a,{type:"danger",onClick:Ie},{default:r(()=>e[60]||(e[60]=[w("确认移除")])),_:1})])]),default:r(()=>[o(j,{model:P.value,ref:"removePasswordFormRef","label-width":"100px"},{default:r(()=>[o(K,{label:"当前密码",prop:"password",rules:[{required:!0,message:"请输入当前密码",trigger:"blur"}]},{default:r(()=>[o(y,{modelValue:P.value.password,"onUpdate:modelValue":e[17]||(e[17]=l=>P.value.password=l),type:"password",placeholder:"请输入当前密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),t("div",$s,[o(n,null,{default:r(()=>[o(u(te))]),_:1}),e[58]||(e[58]=t("span",null,"移除密码保护后，书籍内容将以明文形式保存。",-1))])]),_:1},8,["modelValue"]),E.value?(p(),f("div",{key:1,class:"export-modal-overlay",onClick:Me},[t("div",{class:"export-modal",onClick:e[22]||(e[22]=Q(()=>{},["stop"]))},[t("div",Ts,[t("div",Ls,[t("div",Us,[o(n,null,{default:r(()=>[o(u(ee))]),_:1})]),e[61]||(e[61]=t("div",{class:"header-text"},[t("h2",{class:"header-title"},"导出书籍内容"),t("p",{class:"header-subtitle"},"选择要导出的章节和格式")],-1))]),t("button",{class:"close-btn",onClick:e[20]||(e[20]=l=>E.value=!1)},[o(n,null,{default:r(()=>[o(u(ue))]),_:1})])]),A.value?(p(),f("div",Fs,[t("div",qs,[t("div",Ms,[t("h3",Ns,"《"+m(A.value.title)+"》",1),t("span",Ws,m((A.value.word_count||0).toLocaleString())+" 字",1)]),t("div",Rs,[t("button",{class:"quick-btn select-all",onClick:Se},[o(n,null,{default:r(()=>[o(u(R))]),_:1}),e[62]||(e[62]=w(" 全选 "))]),t("button",{class:"quick-btn clear-all",onClick:Pe},[o(n,null,{default:r(()=>[o(u(ue))]),_:1}),e[63]||(e[63]=w(" 清空 "))])])]),t("div",zs,[t("div",Js,[d.value.length===0?(p(),f("div",Os,[o(n,{class:"empty-icon"},{default:r(()=>[o(u(ne))]),_:1}),e[64]||(e[64]=t("p",{class:"empty-text"},"暂无章节内容",-1))])):V("",!0),(p(!0),f(D,null,W(d.value,(l,h)=>(p(),f("div",{key:h,class:"volume-group"},[t("div",Is,[t("div",{class:"volume-checkbox-wrapper",onClick:Q($=>Te(h),["stop"])},[t("div",{class:B(["custom-checkbox",{checked:l.selected,indeterminate:Z(h)}])},[l.selected&&!Z(h)?(p(),N(n,{key:0,class:"check-icon"},{default:r(()=>[o(u(R))]),_:1})):Z(h)?(p(),f("div",Gs)):V("",!0)],2)],8,js),t("div",{class:"volume-content",onClick:$=>De(h)},[t("div",Xs,[t("span",Ys,m(l.title),1),t("span",Zs,[w(m(Ue(h))+"/"+m(l.chapters?.length||0)+" 章 ",1),t("span",Ks,m(Fe(h).toLocaleString())+" 字",1)])]),t("div",{class:B(["volume-toggle",{expanded:l.expanded}])},[o(n,null,{default:r(()=>[o(u(ut))]),_:1})],2)],8,Hs)]),l.chapters&&l.chapters.length>0?ct((p(),f("div",Qs,[(p(!0),f(D,null,W(l.chapters,($,ae)=>(p(),f("div",{key:ae,class:"chapter-row",onClick:po=>Le(h,ae)},[t("div",to,[t("div",{class:B(["custom-checkbox",{checked:$.selected}])},[$.selected?(p(),N(n,{key:0,class:"check-icon"},{default:r(()=>[o(u(R))]),_:1})):V("",!0)],2),t("div",so,[t("span",oo,m($.title),1),t("span",lo,m(($.word_count||0).toLocaleString())+" 字",1)])])],8,eo))),128))],512)),[[pt,l.expanded]]):V("",!0)]))),128))])])])):V("",!0),t("div",ao,[t("div",ro,[o(n,null,{default:r(()=>[o(u(vt))]),_:1}),t("span",null,"将导出 "+m(I.value)+" 个章节，总计 "+m(qe.value.toLocaleString())+" 字",1)]),t("div",no,[t("button",{class:"cancel-btn",onClick:e[21]||(e[21]=l=>E.value=!1)}," 取消 "),t("button",{class:B(["export-btn",{loading:S.value,disabled:I.value===0}]),disabled:I.value===0||S.value,onClick:Ne},[S.value?(p(),N(n,{key:0,class:"loading-icon"},{default:r(()=>[o(u(ft))]),_:1})):(p(),N(n,{key:1},{default:r(()=>[o(u(ee))]),_:1})),t("span",null,m(S.value?"导出中...":"开始导出"),1)],10,io)])])])])):V("",!0)])}}},bo=Ge(co,[["__scopeId","data-v-6eab4665"]]);export{bo as default};
