import{_ as Ji,l as Bi,i as Ui,r as C,w as He,E as x,c as H,o as Fi,U as Pi,b as z,m as A,e as _,d as c,F as O,g as p,v as T,B as Hi,C as $,bK as Ki,n as Ke,t as Xi,ah as gt,b6 as Yi,V as Xe,W as jt,X as fe,Y as Ne,$ as pe,al as Gi,J as Wi,ak as Qi,aC as en,s as Zi,aK as ji,aL as er,aJ as tr,bD as tn,ac as yt,aD as nr,ad as ir,p as Te,a_ as rr,h as Q,ae as sr,a7 as wt,af as or,bF as ar,bL as lr,aH as nn,a5 as ur,bC as cr,x as rn,bE as xt,au as sn,k as dr,j as fr,q as pr,ag as mr,bx as hr,bM as vr,ax as _r,ay as gr,G as on,R as Ye,av as Ge}from"./entry-BIjVVog3.js";/* empty css                   *//* empty css                         *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                 */import{d as yr}from"./vuedraggable-umd-CP6rdxdP.js";var wr={value:()=>{}};function kn(){for(var e=0,t=arguments.length,n={},i;e<t;++e){if(!(i=arguments[e]+"")||i in n||/[\s.]/.test(i))throw new Error("illegal type: "+i);n[i]=[]}return new je(n)}function je(e){this._=e}function xr(e,t){return e.trim().split(/^|\s+/).map(function(n){var i="",s=n.indexOf(".");if(s>=0&&(i=n.slice(s+1),n=n.slice(0,s)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:i}})}je.prototype=kn.prototype={constructor:je,on:function(e,t){var n=this._,i=xr(e+"",n),s,l=-1,a=i.length;if(arguments.length<2){for(;++l<a;)if((s=(e=i[l]).type)&&(s=br(n[s],e.name)))return s;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++l<a;)if(s=(e=i[l]).type)n[s]=an(n[s],e.name,t);else if(t==null)for(s in n)n[s]=an(n[s],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new je(e)},call:function(e,t){if((s=arguments.length-2)>0)for(var n=new Array(s),i=0,s,l;i<s;++i)n[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(l=this._[e],i=0,s=l.length;i<s;++i)l[i].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var i=this._[e],s=0,l=i.length;s<l;++s)i[s].value.apply(t,n)}};function br(e,t){for(var n=0,i=e.length,s;n<i;++n)if((s=e[n]).name===t)return s.value}function an(e,t,n){for(var i=0,s=e.length;i<s;++i)if(e[i].name===t){e[i]=wr,e=e.slice(0,i).concat(e.slice(i+1));break}return n!=null&&e.push({name:t,value:n}),e}var Et="http://www.w3.org/1999/xhtml";const ln={svg:"http://www.w3.org/2000/svg",xhtml:Et,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function lt(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),ln.hasOwnProperty(t)?{space:ln[t],local:e}:e}function kr(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===Et&&t.documentElement.namespaceURI===Et?t.createElement(e):t.createElementNS(n,e)}}function Er(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function En(e){var t=lt(e);return(t.local?Er:kr)(t)}function Cr(){}function Dt(e){return e==null?Cr:function(){return this.querySelector(e)}}function Nr(e){typeof e!="function"&&(e=Dt(e));for(var t=this._groups,n=t.length,i=new Array(n),s=0;s<n;++s)for(var l=t[s],a=l.length,u=i[s]=new Array(a),d,f,h=0;h<a;++h)(d=l[h])&&(f=e.call(d,d.__data__,h,l))&&("__data__"in d&&(f.__data__=d.__data__),u[h]=f);return new J(i,this._parents)}function Tr(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function Sr(){return[]}function Cn(e){return e==null?Sr:function(){return this.querySelectorAll(e)}}function $r(e){return function(){return Tr(e.apply(this,arguments))}}function Vr(e){typeof e=="function"?e=$r(e):e=Cn(e);for(var t=this._groups,n=t.length,i=[],s=[],l=0;l<n;++l)for(var a=t[l],u=a.length,d,f=0;f<u;++f)(d=a[f])&&(i.push(e.call(d,d.__data__,f,a)),s.push(d));return new J(i,s)}function Nn(e){return function(){return this.matches(e)}}function Tn(e){return function(t){return t.matches(e)}}var Dr=Array.prototype.find;function Ar(e){return function(){return Dr.call(this.children,e)}}function Or(){return this.firstElementChild}function Ir(e){return this.select(e==null?Or:Ar(typeof e=="function"?e:Tn(e)))}var Rr=Array.prototype.filter;function zr(){return Array.from(this.children)}function Lr(e){return function(){return Rr.call(this.children,e)}}function Mr(e){return this.selectAll(e==null?zr:Lr(typeof e=="function"?e:Tn(e)))}function qr(e){typeof e!="function"&&(e=Nn(e));for(var t=this._groups,n=t.length,i=new Array(n),s=0;s<n;++s)for(var l=t[s],a=l.length,u=i[s]=[],d,f=0;f<a;++f)(d=l[f])&&e.call(d,d.__data__,f,l)&&u.push(d);return new J(i,this._parents)}function Sn(e){return new Array(e.length)}function Jr(){return new J(this._enter||this._groups.map(Sn),this._parents)}function nt(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}nt.prototype={constructor:nt,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function Br(e){return function(){return e}}function Ur(e,t,n,i,s,l){for(var a=0,u,d=t.length,f=l.length;a<f;++a)(u=t[a])?(u.__data__=l[a],i[a]=u):n[a]=new nt(e,l[a]);for(;a<d;++a)(u=t[a])&&(s[a]=u)}function Fr(e,t,n,i,s,l,a){var u,d,f=new Map,h=t.length,y=l.length,w=new Array(h),b;for(u=0;u<h;++u)(d=t[u])&&(w[u]=b=a.call(d,d.__data__,u,t)+"",f.has(b)?s[u]=d:f.set(b,d));for(u=0;u<y;++u)b=a.call(e,l[u],u,l)+"",(d=f.get(b))?(i[u]=d,d.__data__=l[u],f.delete(b)):n[u]=new nt(e,l[u]);for(u=0;u<h;++u)(d=t[u])&&f.get(w[u])===d&&(s[u]=d)}function Pr(e){return e.__data__}function Hr(e,t){if(!arguments.length)return Array.from(this,Pr);var n=t?Fr:Ur,i=this._parents,s=this._groups;typeof e!="function"&&(e=Br(e));for(var l=s.length,a=new Array(l),u=new Array(l),d=new Array(l),f=0;f<l;++f){var h=i[f],y=s[f],w=y.length,b=Kr(e.call(h,h&&h.__data__,f,i)),N=b.length,V=u[f]=new Array(N),L=a[f]=new Array(N),R=d[f]=new Array(w);n(h,y,V,L,R,b,t);for(var k=0,te=0,P,W;k<N;++k)if(P=V[k]){for(k>=te&&(te=k+1);!(W=L[te])&&++te<N;);P._next=W||null}}return a=new J(a,i),a._enter=u,a._exit=d,a}function Kr(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Xr(){return new J(this._exit||this._groups.map(Sn),this._parents)}function Yr(e,t,n){var i=this.enter(),s=this,l=this.exit();return typeof e=="function"?(i=e(i),i&&(i=i.selection())):i=i.append(e+""),t!=null&&(s=t(s),s&&(s=s.selection())),n==null?l.remove():n(l),i&&s?i.merge(s).order():s}function Gr(e){for(var t=e.selection?e.selection():e,n=this._groups,i=t._groups,s=n.length,l=i.length,a=Math.min(s,l),u=new Array(s),d=0;d<a;++d)for(var f=n[d],h=i[d],y=f.length,w=u[d]=new Array(y),b,N=0;N<y;++N)(b=f[N]||h[N])&&(w[N]=b);for(;d<s;++d)u[d]=n[d];return new J(u,this._parents)}function Wr(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var i=e[t],s=i.length-1,l=i[s],a;--s>=0;)(a=i[s])&&(l&&a.compareDocumentPosition(l)^4&&l.parentNode.insertBefore(a,l),l=a);return this}function Qr(e){e||(e=Zr);function t(y,w){return y&&w?e(y.__data__,w.__data__):!y-!w}for(var n=this._groups,i=n.length,s=new Array(i),l=0;l<i;++l){for(var a=n[l],u=a.length,d=s[l]=new Array(u),f,h=0;h<u;++h)(f=a[h])&&(d[h]=f);d.sort(t)}return new J(s,this._parents).order()}function Zr(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function jr(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function es(){return Array.from(this)}function ts(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var i=e[t],s=0,l=i.length;s<l;++s){var a=i[s];if(a)return a}return null}function ns(){let e=0;for(const t of this)++e;return e}function is(){return!this.node()}function rs(e){for(var t=this._groups,n=0,i=t.length;n<i;++n)for(var s=t[n],l=0,a=s.length,u;l<a;++l)(u=s[l])&&e.call(u,u.__data__,l,s);return this}function ss(e){return function(){this.removeAttribute(e)}}function os(e){return function(){this.removeAttributeNS(e.space,e.local)}}function as(e,t){return function(){this.setAttribute(e,t)}}function ls(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function us(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function cs(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function ds(e,t){var n=lt(e);if(arguments.length<2){var i=this.node();return n.local?i.getAttributeNS(n.space,n.local):i.getAttribute(n)}return this.each((t==null?n.local?os:ss:typeof t=="function"?n.local?cs:us:n.local?ls:as)(n,t))}function $n(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function fs(e){return function(){this.style.removeProperty(e)}}function ps(e,t,n){return function(){this.style.setProperty(e,t,n)}}function ms(e,t,n){return function(){var i=t.apply(this,arguments);i==null?this.style.removeProperty(e):this.style.setProperty(e,i,n)}}function hs(e,t,n){return arguments.length>1?this.each((t==null?fs:typeof t=="function"?ms:ps)(e,t,n??"")):he(this.node(),e)}function he(e,t){return e.style.getPropertyValue(t)||$n(e).getComputedStyle(e,null).getPropertyValue(t)}function vs(e){return function(){delete this[e]}}function _s(e,t){return function(){this[e]=t}}function gs(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function ys(e,t){return arguments.length>1?this.each((t==null?vs:typeof t=="function"?gs:_s)(e,t)):this.node()[e]}function Vn(e){return e.trim().split(/^|\s+/)}function At(e){return e.classList||new Dn(e)}function Dn(e){this._node=e,this._names=Vn(e.getAttribute("class")||"")}Dn.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function An(e,t){for(var n=At(e),i=-1,s=t.length;++i<s;)n.add(t[i])}function On(e,t){for(var n=At(e),i=-1,s=t.length;++i<s;)n.remove(t[i])}function ws(e){return function(){An(this,e)}}function xs(e){return function(){On(this,e)}}function bs(e,t){return function(){(t.apply(this,arguments)?An:On)(this,e)}}function ks(e,t){var n=Vn(e+"");if(arguments.length<2){for(var i=At(this.node()),s=-1,l=n.length;++s<l;)if(!i.contains(n[s]))return!1;return!0}return this.each((typeof t=="function"?bs:t?ws:xs)(n,t))}function Es(){this.textContent=""}function Cs(e){return function(){this.textContent=e}}function Ns(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function Ts(e){return arguments.length?this.each(e==null?Es:(typeof e=="function"?Ns:Cs)(e)):this.node().textContent}function Ss(){this.innerHTML=""}function $s(e){return function(){this.innerHTML=e}}function Vs(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function Ds(e){return arguments.length?this.each(e==null?Ss:(typeof e=="function"?Vs:$s)(e)):this.node().innerHTML}function As(){this.nextSibling&&this.parentNode.appendChild(this)}function Os(){return this.each(As)}function Is(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Rs(){return this.each(Is)}function zs(e){var t=typeof e=="function"?e:En(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function Ls(){return null}function Ms(e,t){var n=typeof e=="function"?e:En(e),i=t==null?Ls:typeof t=="function"?t:Dt(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),i.apply(this,arguments)||null)})}function qs(){var e=this.parentNode;e&&e.removeChild(this)}function Js(){return this.each(qs)}function Bs(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Us(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Fs(e){return this.select(e?Us:Bs)}function Ps(e){return arguments.length?this.property("__data__",e):this.node().__data__}function Hs(e){return function(t){e.call(this,t,this.__data__)}}function Ks(e){return e.trim().split(/^|\s+/).map(function(t){var n="",i=t.indexOf(".");return i>=0&&(n=t.slice(i+1),t=t.slice(0,i)),{type:t,name:n}})}function Xs(e){return function(){var t=this.__on;if(t){for(var n=0,i=-1,s=t.length,l;n<s;++n)l=t[n],(!e.type||l.type===e.type)&&l.name===e.name?this.removeEventListener(l.type,l.listener,l.options):t[++i]=l;++i?t.length=i:delete this.__on}}}function Ys(e,t,n){return function(){var i=this.__on,s,l=Hs(t);if(i){for(var a=0,u=i.length;a<u;++a)if((s=i[a]).type===e.type&&s.name===e.name){this.removeEventListener(s.type,s.listener,s.options),this.addEventListener(s.type,s.listener=l,s.options=n),s.value=t;return}}this.addEventListener(e.type,l,n),s={type:e.type,name:e.name,value:t,listener:l,options:n},i?i.push(s):this.__on=[s]}}function Gs(e,t,n){var i=Ks(e+""),s,l=i.length,a;if(arguments.length<2){var u=this.node().__on;if(u){for(var d=0,f=u.length,h;d<f;++d)for(s=0,h=u[d];s<l;++s)if((a=i[s]).type===h.type&&a.name===h.name)return h.value}return}for(u=t?Ys:Xs,s=0;s<l;++s)this.each(u(i[s],t,n));return this}function In(e,t,n){var i=$n(e),s=i.CustomEvent;typeof s=="function"?s=new s(t,n):(s=i.document.createEvent("Event"),n?(s.initEvent(t,n.bubbles,n.cancelable),s.detail=n.detail):s.initEvent(t,!1,!1)),e.dispatchEvent(s)}function Ws(e,t){return function(){return In(this,e,t)}}function Qs(e,t){return function(){return In(this,e,t.apply(this,arguments))}}function Zs(e,t){return this.each((typeof t=="function"?Qs:Ws)(e,t))}function*js(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var i=e[t],s=0,l=i.length,a;s<l;++s)(a=i[s])&&(yield a)}var eo=[null];function J(e,t){this._groups=e,this._parents=t}function ze(){return new J([[document.documentElement]],eo)}function to(){return this}J.prototype=ze.prototype={constructor:J,select:Nr,selectAll:Vr,selectChild:Ir,selectChildren:Mr,filter:qr,data:Hr,enter:Jr,exit:Xr,join:Yr,merge:Gr,selection:to,order:Wr,sort:Qr,call:jr,nodes:es,node:ts,size:ns,empty:is,each:rs,attr:ds,style:hs,property:ys,classed:ks,text:Ts,html:Ds,raise:Os,lower:Rs,append:zs,insert:Ms,remove:Js,clone:Fs,datum:Ps,on:Gs,dispatch:Zs,[Symbol.iterator]:js};function Ot(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function Rn(e,t){var n=Object.create(e.prototype);for(var i in t)n[i]=t[i];return n}function Le(){}var Ae=.7,it=1/Ae,me="\\s*([+-]?\\d+)\\s*",Oe="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",U="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",no=/^#([0-9a-f]{3,8})$/,io=new RegExp(`^rgb\\(${me},${me},${me}\\)$`),ro=new RegExp(`^rgb\\(${U},${U},${U}\\)$`),so=new RegExp(`^rgba\\(${me},${me},${me},${Oe}\\)$`),oo=new RegExp(`^rgba\\(${U},${U},${U},${Oe}\\)$`),ao=new RegExp(`^hsl\\(${Oe},${U},${U}\\)$`),lo=new RegExp(`^hsla\\(${Oe},${U},${U},${Oe}\\)$`),un={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Ot(Le,Ie,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:cn,formatHex:cn,formatHex8:uo,formatHsl:co,formatRgb:dn,toString:dn});function cn(){return this.rgb().formatHex()}function uo(){return this.rgb().formatHex8()}function co(){return zn(this).formatHsl()}function dn(){return this.rgb().formatRgb()}function Ie(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=no.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?fn(t):n===3?new M(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?We(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?We(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=io.exec(e))?new M(t[1],t[2],t[3],1):(t=ro.exec(e))?new M(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=so.exec(e))?We(t[1],t[2],t[3],t[4]):(t=oo.exec(e))?We(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=ao.exec(e))?hn(t[1],t[2]/100,t[3]/100,1):(t=lo.exec(e))?hn(t[1],t[2]/100,t[3]/100,t[4]):un.hasOwnProperty(e)?fn(un[e]):e==="transparent"?new M(NaN,NaN,NaN,0):null}function fn(e){return new M(e>>16&255,e>>8&255,e&255,1)}function We(e,t,n,i){return i<=0&&(e=t=n=NaN),new M(e,t,n,i)}function fo(e){return e instanceof Le||(e=Ie(e)),e?(e=e.rgb(),new M(e.r,e.g,e.b,e.opacity)):new M}function Ct(e,t,n,i){return arguments.length===1?fo(e):new M(e,t,n,i??1)}function M(e,t,n,i){this.r=+e,this.g=+t,this.b=+n,this.opacity=+i}Ot(M,Ct,Rn(Le,{brighter(e){return e=e==null?it:Math.pow(it,e),new M(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Ae:Math.pow(Ae,e),new M(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new M(j(this.r),j(this.g),j(this.b),rt(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:pn,formatHex:pn,formatHex8:po,formatRgb:mn,toString:mn}));function pn(){return`#${Z(this.r)}${Z(this.g)}${Z(this.b)}`}function po(){return`#${Z(this.r)}${Z(this.g)}${Z(this.b)}${Z((isNaN(this.opacity)?1:this.opacity)*255)}`}function mn(){const e=rt(this.opacity);return`${e===1?"rgb(":"rgba("}${j(this.r)}, ${j(this.g)}, ${j(this.b)}${e===1?")":`, ${e})`}`}function rt(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function j(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Z(e){return e=j(e),(e<16?"0":"")+e.toString(16)}function hn(e,t,n,i){return i<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new q(e,t,n,i)}function zn(e){if(e instanceof q)return new q(e.h,e.s,e.l,e.opacity);if(e instanceof Le||(e=Ie(e)),!e)return new q;if(e instanceof q)return e;e=e.rgb();var t=e.r/255,n=e.g/255,i=e.b/255,s=Math.min(t,n,i),l=Math.max(t,n,i),a=NaN,u=l-s,d=(l+s)/2;return u?(t===l?a=(n-i)/u+(n<i)*6:n===l?a=(i-t)/u+2:a=(t-n)/u+4,u/=d<.5?l+s:2-l-s,a*=60):u=d>0&&d<1?0:a,new q(a,u,d,e.opacity)}function mo(e,t,n,i){return arguments.length===1?zn(e):new q(e,t,n,i??1)}function q(e,t,n,i){this.h=+e,this.s=+t,this.l=+n,this.opacity=+i}Ot(q,mo,Rn(Le,{brighter(e){return e=e==null?it:Math.pow(it,e),new q(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Ae:Math.pow(Ae,e),new q(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,i=n+(n<.5?n:1-n)*t,s=2*n-i;return new M(bt(e>=240?e-240:e+120,s,i),bt(e,s,i),bt(e<120?e+240:e-120,s,i),this.opacity)},clamp(){return new q(vn(this.h),Qe(this.s),Qe(this.l),rt(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=rt(this.opacity);return`${e===1?"hsl(":"hsla("}${vn(this.h)}, ${Qe(this.s)*100}%, ${Qe(this.l)*100}%${e===1?")":`, ${e})`}`}}));function vn(e){return e=(e||0)%360,e<0?e+360:e}function Qe(e){return Math.max(0,Math.min(1,e||0))}function bt(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const Ln=e=>()=>e;function ho(e,t){return function(n){return e+n*t}}function vo(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(i){return Math.pow(e+i*t,n)}}function _o(e){return(e=+e)==1?Mn:function(t,n){return n-t?vo(t,n,e):Ln(isNaN(t)?n:t)}}function Mn(e,t){var n=t-e;return n?ho(e,n):Ln(isNaN(e)?t:e)}const _n=function e(t){var n=_o(t);function i(s,l){var a=n((s=Ct(s)).r,(l=Ct(l)).r),u=n(s.g,l.g),d=n(s.b,l.b),f=Mn(s.opacity,l.opacity);return function(h){return s.r=a(h),s.g=u(h),s.b=d(h),s.opacity=f(h),s+""}}return i.gamma=e,i}(1);function G(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var Nt=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,kt=new RegExp(Nt.source,"g");function go(e){return function(){return e}}function yo(e){return function(t){return e(t)+""}}function wo(e,t){var n=Nt.lastIndex=kt.lastIndex=0,i,s,l,a=-1,u=[],d=[];for(e=e+"",t=t+"";(i=Nt.exec(e))&&(s=kt.exec(t));)(l=s.index)>n&&(l=t.slice(n,l),u[a]?u[a]+=l:u[++a]=l),(i=i[0])===(s=s[0])?u[a]?u[a]+=s:u[++a]=s:(u[++a]=null,d.push({i:a,x:G(i,s)})),n=kt.lastIndex;return n<t.length&&(l=t.slice(n),u[a]?u[a]+=l:u[++a]=l),u.length<2?d[0]?yo(d[0].x):go(t):(t=d.length,function(f){for(var h=0,y;h<t;++h)u[(y=d[h]).i]=y.x(f);return u.join("")})}var gn=180/Math.PI,Tt={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function qn(e,t,n,i,s,l){var a,u,d;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(d=e*n+t*i)&&(n-=e*d,i-=t*d),(u=Math.sqrt(n*n+i*i))&&(n/=u,i/=u,d/=u),e*i<t*n&&(e=-e,t=-t,d=-d,a=-a),{translateX:s,translateY:l,rotate:Math.atan2(t,e)*gn,skewX:Math.atan(d)*gn,scaleX:a,scaleY:u}}var Ze;function xo(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Tt:qn(t.a,t.b,t.c,t.d,t.e,t.f)}function bo(e){return e==null||(Ze||(Ze=document.createElementNS("http://www.w3.org/2000/svg","g")),Ze.setAttribute("transform",e),!(e=Ze.transform.baseVal.consolidate()))?Tt:(e=e.matrix,qn(e.a,e.b,e.c,e.d,e.e,e.f))}function Jn(e,t,n,i){function s(f){return f.length?f.pop()+" ":""}function l(f,h,y,w,b,N){if(f!==y||h!==w){var V=b.push("translate(",null,t,null,n);N.push({i:V-4,x:G(f,y)},{i:V-2,x:G(h,w)})}else(y||w)&&b.push("translate("+y+t+w+n)}function a(f,h,y,w){f!==h?(f-h>180?h+=360:h-f>180&&(f+=360),w.push({i:y.push(s(y)+"rotate(",null,i)-2,x:G(f,h)})):h&&y.push(s(y)+"rotate("+h+i)}function u(f,h,y,w){f!==h?w.push({i:y.push(s(y)+"skewX(",null,i)-2,x:G(f,h)}):h&&y.push(s(y)+"skewX("+h+i)}function d(f,h,y,w,b,N){if(f!==y||h!==w){var V=b.push(s(b)+"scale(",null,",",null,")");N.push({i:V-4,x:G(f,y)},{i:V-2,x:G(h,w)})}else(y!==1||w!==1)&&b.push(s(b)+"scale("+y+","+w+")")}return function(f,h){var y=[],w=[];return f=e(f),h=e(h),l(f.translateX,f.translateY,h.translateX,h.translateY,y,w),a(f.rotate,h.rotate,y,w),u(f.skewX,h.skewX,y,w),d(f.scaleX,f.scaleY,h.scaleX,h.scaleY,y,w),f=h=null,function(b){for(var N=-1,V=w.length,L;++N<V;)y[(L=w[N]).i]=L.x(b);return y.join("")}}}var ko=Jn(xo,"px, ","px)","deg)"),Eo=Jn(bo,", ",")",")"),ve=0,$e=0,Se=0,Bn=1e3,st,Ve,ot=0,ee=0,ut=0,Re=typeof performance=="object"&&performance.now?performance:Date,Un=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function It(){return ee||(Un(Co),ee=Re.now()+ut)}function Co(){ee=0}function at(){this._call=this._time=this._next=null}at.prototype=Fn.prototype={constructor:at,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?It():+n)+(t==null?0:+t),!this._next&&Ve!==this&&(Ve?Ve._next=this:st=this,Ve=this),this._call=e,this._time=n,St()},stop:function(){this._call&&(this._call=null,this._time=1/0,St())}};function Fn(e,t,n){var i=new at;return i.restart(e,t,n),i}function No(){It(),++ve;for(var e=st,t;e;)(t=ee-e._time)>=0&&e._call.call(void 0,t),e=e._next;--ve}function yn(){ee=(ot=Re.now())+ut,ve=$e=0;try{No()}finally{ve=0,So(),ee=0}}function To(){var e=Re.now(),t=e-ot;t>Bn&&(ut-=t,ot=e)}function So(){for(var e,t=st,n,i=1/0;t;)t._call?(i>t._time&&(i=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:st=n);Ve=e,St(i)}function St(e){if(!ve){$e&&($e=clearTimeout($e));var t=e-ee;t>24?(e<1/0&&($e=setTimeout(yn,e-Re.now()-ut)),Se&&(Se=clearInterval(Se))):(Se||(ot=Re.now(),Se=setInterval(To,Bn)),ve=1,Un(yn))}}function wn(e,t,n){var i=new at;return t=t==null?0:+t,i.restart(s=>{i.stop(),e(s+t)},t,n),i}var $o=kn("start","end","cancel","interrupt"),Vo=[],Pn=0,xn=1,$t=2,et=3,bn=4,Vt=5,tt=6;function ct(e,t,n,i,s,l){var a=e.__transition;if(!a)e.__transition={};else if(n in a)return;Do(e,n,{name:t,index:i,group:s,on:$o,tween:Vo,time:l.time,delay:l.delay,duration:l.duration,ease:l.ease,timer:null,state:Pn})}function Rt(e,t){var n=B(e,t);if(n.state>Pn)throw new Error("too late; already scheduled");return n}function F(e,t){var n=B(e,t);if(n.state>et)throw new Error("too late; already running");return n}function B(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Do(e,t,n){var i=e.__transition,s;i[t]=n,n.timer=Fn(l,0,n.time);function l(f){n.state=xn,n.timer.restart(a,n.delay,n.time),n.delay<=f&&a(f-n.delay)}function a(f){var h,y,w,b;if(n.state!==xn)return d();for(h in i)if(b=i[h],b.name===n.name){if(b.state===et)return wn(a);b.state===bn?(b.state=tt,b.timer.stop(),b.on.call("interrupt",e,e.__data__,b.index,b.group),delete i[h]):+h<t&&(b.state=tt,b.timer.stop(),b.on.call("cancel",e,e.__data__,b.index,b.group),delete i[h])}if(wn(function(){n.state===et&&(n.state=bn,n.timer.restart(u,n.delay,n.time),u(f))}),n.state=$t,n.on.call("start",e,e.__data__,n.index,n.group),n.state===$t){for(n.state=et,s=new Array(w=n.tween.length),h=0,y=-1;h<w;++h)(b=n.tween[h].value.call(e,e.__data__,n.index,n.group))&&(s[++y]=b);s.length=y+1}}function u(f){for(var h=f<n.duration?n.ease.call(null,f/n.duration):(n.timer.restart(d),n.state=Vt,1),y=-1,w=s.length;++y<w;)s[y].call(e,h);n.state===Vt&&(n.on.call("end",e,e.__data__,n.index,n.group),d())}function d(){n.state=tt,n.timer.stop(),delete i[t];for(var f in i)return;delete e.__transition}}function Ao(e,t){var n=e.__transition,i,s,l=!0,a;if(n){t=t==null?null:t+"";for(a in n){if((i=n[a]).name!==t){l=!1;continue}s=i.state>$t&&i.state<Vt,i.state=tt,i.timer.stop(),i.on.call(s?"interrupt":"cancel",e,e.__data__,i.index,i.group),delete n[a]}l&&delete e.__transition}}function Oo(e){return this.each(function(){Ao(this,e)})}function Io(e,t){var n,i;return function(){var s=F(this,e),l=s.tween;if(l!==n){i=n=l;for(var a=0,u=i.length;a<u;++a)if(i[a].name===t){i=i.slice(),i.splice(a,1);break}}s.tween=i}}function Ro(e,t,n){var i,s;if(typeof n!="function")throw new Error;return function(){var l=F(this,e),a=l.tween;if(a!==i){s=(i=a).slice();for(var u={name:t,value:n},d=0,f=s.length;d<f;++d)if(s[d].name===t){s[d]=u;break}d===f&&s.push(u)}l.tween=s}}function zo(e,t){var n=this._id;if(e+="",arguments.length<2){for(var i=B(this.node(),n).tween,s=0,l=i.length,a;s<l;++s)if((a=i[s]).name===e)return a.value;return null}return this.each((t==null?Io:Ro)(n,e,t))}function zt(e,t,n){var i=e._id;return e.each(function(){var s=F(this,i);(s.value||(s.value={}))[t]=n.apply(this,arguments)}),function(s){return B(s,i).value[t]}}function Hn(e,t){var n;return(typeof t=="number"?G:t instanceof Ie?_n:(n=Ie(t))?(t=n,_n):wo)(e,t)}function Lo(e){return function(){this.removeAttribute(e)}}function Mo(e){return function(){this.removeAttributeNS(e.space,e.local)}}function qo(e,t,n){var i,s=n+"",l;return function(){var a=this.getAttribute(e);return a===s?null:a===i?l:l=t(i=a,n)}}function Jo(e,t,n){var i,s=n+"",l;return function(){var a=this.getAttributeNS(e.space,e.local);return a===s?null:a===i?l:l=t(i=a,n)}}function Bo(e,t,n){var i,s,l;return function(){var a,u=n(this),d;return u==null?void this.removeAttribute(e):(a=this.getAttribute(e),d=u+"",a===d?null:a===i&&d===s?l:(s=d,l=t(i=a,u)))}}function Uo(e,t,n){var i,s,l;return function(){var a,u=n(this),d;return u==null?void this.removeAttributeNS(e.space,e.local):(a=this.getAttributeNS(e.space,e.local),d=u+"",a===d?null:a===i&&d===s?l:(s=d,l=t(i=a,u)))}}function Fo(e,t){var n=lt(e),i=n==="transform"?Eo:Hn;return this.attrTween(e,typeof t=="function"?(n.local?Uo:Bo)(n,i,zt(this,"attr."+e,t)):t==null?(n.local?Mo:Lo)(n):(n.local?Jo:qo)(n,i,t))}function Po(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function Ho(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function Ko(e,t){var n,i;function s(){var l=t.apply(this,arguments);return l!==i&&(n=(i=l)&&Ho(e,l)),n}return s._value=t,s}function Xo(e,t){var n,i;function s(){var l=t.apply(this,arguments);return l!==i&&(n=(i=l)&&Po(e,l)),n}return s._value=t,s}function Yo(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var i=lt(e);return this.tween(n,(i.local?Ko:Xo)(i,t))}function Go(e,t){return function(){Rt(this,e).delay=+t.apply(this,arguments)}}function Wo(e,t){return t=+t,function(){Rt(this,e).delay=t}}function Qo(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?Go:Wo)(t,e)):B(this.node(),t).delay}function Zo(e,t){return function(){F(this,e).duration=+t.apply(this,arguments)}}function jo(e,t){return t=+t,function(){F(this,e).duration=t}}function ea(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?Zo:jo)(t,e)):B(this.node(),t).duration}function ta(e,t){if(typeof t!="function")throw new Error;return function(){F(this,e).ease=t}}function na(e){var t=this._id;return arguments.length?this.each(ta(t,e)):B(this.node(),t).ease}function ia(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;F(this,e).ease=n}}function ra(e){if(typeof e!="function")throw new Error;return this.each(ia(this._id,e))}function sa(e){typeof e!="function"&&(e=Nn(e));for(var t=this._groups,n=t.length,i=new Array(n),s=0;s<n;++s)for(var l=t[s],a=l.length,u=i[s]=[],d,f=0;f<a;++f)(d=l[f])&&e.call(d,d.__data__,f,l)&&u.push(d);return new X(i,this._parents,this._name,this._id)}function oa(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,i=t.length,s=n.length,l=Math.min(i,s),a=new Array(i),u=0;u<l;++u)for(var d=t[u],f=n[u],h=d.length,y=a[u]=new Array(h),w,b=0;b<h;++b)(w=d[b]||f[b])&&(y[b]=w);for(;u<i;++u)a[u]=t[u];return new X(a,this._parents,this._name,this._id)}function aa(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function la(e,t,n){var i,s,l=aa(t)?Rt:F;return function(){var a=l(this,e),u=a.on;u!==i&&(s=(i=u).copy()).on(t,n),a.on=s}}function ua(e,t){var n=this._id;return arguments.length<2?B(this.node(),n).on.on(e):this.each(la(n,e,t))}function ca(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function da(){return this.on("end.remove",ca(this._id))}function fa(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Dt(e));for(var i=this._groups,s=i.length,l=new Array(s),a=0;a<s;++a)for(var u=i[a],d=u.length,f=l[a]=new Array(d),h,y,w=0;w<d;++w)(h=u[w])&&(y=e.call(h,h.__data__,w,u))&&("__data__"in h&&(y.__data__=h.__data__),f[w]=y,ct(f[w],t,n,w,f,B(h,n)));return new X(l,this._parents,t,n)}function pa(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Cn(e));for(var i=this._groups,s=i.length,l=[],a=[],u=0;u<s;++u)for(var d=i[u],f=d.length,h,y=0;y<f;++y)if(h=d[y]){for(var w=e.call(h,h.__data__,y,d),b,N=B(h,n),V=0,L=w.length;V<L;++V)(b=w[V])&&ct(b,t,n,V,w,N);l.push(w),a.push(h)}return new X(l,a,t,n)}var ma=ze.prototype.constructor;function ha(){return new ma(this._groups,this._parents)}function va(e,t){var n,i,s;return function(){var l=he(this,e),a=(this.style.removeProperty(e),he(this,e));return l===a?null:l===n&&a===i?s:s=t(n=l,i=a)}}function Kn(e){return function(){this.style.removeProperty(e)}}function _a(e,t,n){var i,s=n+"",l;return function(){var a=he(this,e);return a===s?null:a===i?l:l=t(i=a,n)}}function ga(e,t,n){var i,s,l;return function(){var a=he(this,e),u=n(this),d=u+"";return u==null&&(d=u=(this.style.removeProperty(e),he(this,e))),a===d?null:a===i&&d===s?l:(s=d,l=t(i=a,u))}}function ya(e,t){var n,i,s,l="style."+t,a="end."+l,u;return function(){var d=F(this,e),f=d.on,h=d.value[l]==null?u||(u=Kn(t)):void 0;(f!==n||s!==h)&&(i=(n=f).copy()).on(a,s=h),d.on=i}}function wa(e,t,n){var i=(e+="")=="transform"?ko:Hn;return t==null?this.styleTween(e,va(e,i)).on("end.style."+e,Kn(e)):typeof t=="function"?this.styleTween(e,ga(e,i,zt(this,"style."+e,t))).each(ya(this._id,e)):this.styleTween(e,_a(e,i,t),n).on("end.style."+e,null)}function xa(e,t,n){return function(i){this.style.setProperty(e,t.call(this,i),n)}}function ba(e,t,n){var i,s;function l(){var a=t.apply(this,arguments);return a!==s&&(i=(s=a)&&xa(e,a,n)),i}return l._value=t,l}function ka(e,t,n){var i="style."+(e+="");if(arguments.length<2)return(i=this.tween(i))&&i._value;if(t==null)return this.tween(i,null);if(typeof t!="function")throw new Error;return this.tween(i,ba(e,t,n??""))}function Ea(e){return function(){this.textContent=e}}function Ca(e){return function(){var t=e(this);this.textContent=t??""}}function Na(e){return this.tween("text",typeof e=="function"?Ca(zt(this,"text",e)):Ea(e==null?"":e+""))}function Ta(e){return function(t){this.textContent=e.call(this,t)}}function Sa(e){var t,n;function i(){var s=e.apply(this,arguments);return s!==n&&(t=(n=s)&&Ta(s)),t}return i._value=e,i}function $a(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,Sa(e))}function Va(){for(var e=this._name,t=this._id,n=Xn(),i=this._groups,s=i.length,l=0;l<s;++l)for(var a=i[l],u=a.length,d,f=0;f<u;++f)if(d=a[f]){var h=B(d,t);ct(d,e,n,f,a,{time:h.time+h.delay+h.duration,delay:0,duration:h.duration,ease:h.ease})}return new X(i,this._parents,e,n)}function Da(){var e,t,n=this,i=n._id,s=n.size();return new Promise(function(l,a){var u={value:a},d={value:function(){--s===0&&l()}};n.each(function(){var f=F(this,i),h=f.on;h!==e&&(t=(e=h).copy(),t._.cancel.push(u),t._.interrupt.push(u),t._.end.push(d)),f.on=t}),s===0&&l()})}var Aa=0;function X(e,t,n,i){this._groups=e,this._parents=t,this._name=n,this._id=i}function Xn(){return++Aa}var K=ze.prototype;X.prototype={constructor:X,select:fa,selectAll:pa,selectChild:K.selectChild,selectChildren:K.selectChildren,filter:sa,merge:oa,selection:ha,transition:Va,call:K.call,nodes:K.nodes,node:K.node,size:K.size,empty:K.empty,each:K.each,on:ua,attr:Fo,attrTween:Yo,style:wa,styleTween:ka,text:Na,textTween:$a,remove:da,tween:zo,delay:Qo,duration:ea,ease:na,easeVarying:ra,end:Da,[Symbol.iterator]:K[Symbol.iterator]};function Oa(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var Ia={time:null,delay:0,duration:250,ease:Oa};function Ra(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function za(e){var t,n;e instanceof X?(t=e._id,e=e._name):(t=Xn(),(n=Ia).time=It(),e=e==null?null:e+"");for(var i=this._groups,s=i.length,l=0;l<s;++l)for(var a=i[l],u=a.length,d,f=0;f<u;++f)(d=a[f])&&ct(d,e,t,f,a,n||Ra(d,t));return new X(i,this._parents,e,t)}ze.prototype.interrupt=Oo;ze.prototype.transition=za;function De(e,t,n){this.k=e,this.x=t,this.y=n}De.prototype={constructor:De,scale:function(e){return e===1?this:new De(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new De(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};De.prototype;const La={class:"setting-manager glass-bg"},Ma={class:"page-header"},qa={class:"left-section"},Ja={class:"book-title"},Ba={class:"tab-buttons"},Ua={class:"main-content"},Fa={class:"content-container"},Pa={class:"entities-container glass-bg"},Ha={class:"section-header"},Ka={class:"header-content"},Xa={class:"template-option"},Ya={class:"template-name"},Ga={class:"button-group"},Wa={class:"table-container"},Qa={class:"entity-name-cell"},Za={class:"entity-name"},ja={class:"entity-meta"},el={class:"dimension-grid"},tl={class:"dimension-label"},nl={class:"dimension-value"},il={key:0,class:"dimension-item more-dimensions"},rl={class:"dimension-value"},sl={class:"operation-buttons-wrapper"},ol={class:"pagination-container"},al={class:"templates-container glass-bg"},ll={class:"section-header"},ul={class:"header-content"},cl={class:"template-actions"},dl={class:"table-container"},fl=["data-index"],pl={class:"sort-number"},ml={class:"template-name-cell"},hl={class:"template-name"},vl={key:0,class:"template-description"},_l={class:"dimensions-grid"},gl={key:0,class:"dimension-chip more-chip"},yl={class:"template-stats"},wl={class:"stat-item"},xl={class:"stat-value"},bl={class:"stat-item"},kl={class:"stat-value"},El={class:"operation-buttons-wrapper"},Cl={class:"pagination-container"},Nl={class:"dialog-fixed-content"},Tl={class:"basic-info-section"},Sl={class:"form-row"},$l={class:"form-field"},Vl={class:"form-row"},Dl={class:"form-field full-width"},Al={class:"dialog-scrollable-content"},Ol={class:"add-dimension-section"},Il={key:0,class:"add-dimension-trigger"},Rl={key:1,class:"dimension-input-inline"},zl={class:"dimensions-list-container"},Ll={class:"dimensions-list-scrollable"},Ml={key:0,class:"empty-dimensions"},ql={key:1,class:"dimensions-list-native"},Jl={class:"dimension-item-native"},Bl={class:"dimension-text"},Ul={class:"dialog-footer-fixed"},Fl={class:"entity-card"},Pl={class:"entity-header"},Hl={key:0,class:"entity-dimensions"},Kl={class:"dimensions-container"},Xl=["onDblclick","title"],Yl={class:"dialog-footer"},Gl={class:"entity-card"},Wl={class:"entity-header"},Ql={key:0,class:"entity-dimensions"},Zl={class:"dimensions-container"},jl=["onDblclick","title"],eu={class:"dialog-footer"},tu={class:"dimension-editor-container"},nu={class:"editor-header"},iu={class:"dimension-info"},ru={class:"editor-stats"},su={class:"stat-item"},ou={class:"stat-item"},au={class:"editor-actions"},lu={class:"editor-content"},uu={class:"import-content"},cu={class:"format-hint-title"},du={class:"dialog-footer"},fu={class:"import-content"},pu={class:"format-hint-title"},mu={class:"dialog-footer"},hu={__name:"设定",setup(e){const t=Bi(),n=Ui(),i=C(t.params.id||t.query.id),s=C(t.query.title||t.params.title);He(()=>t.params,o=>{const r=o.id||t.query.id,m=t.query.title||o.title;r&&r!==i.value&&(console.log("路由参数变化，从书籍",i.value,"切换到",r),i.value=r,s.value=m,a.value="",N.value=[],V.value=[],L.value=[],re())},{deep:!0}),He(()=>t.query,o=>{o.title&&o.title!==s.value&&(s.value=o.title)},{deep:!0}),i.value||(x.error("未找到书籍信息"),n.push("/book/writing"));const l=C("entities"),a=C(""),u=C(!1),d=C(!1),f=C(!1),h=C(!1),y=C(""),w=C("");C(!1),C(0),C(0),C("");const b=C(!1),N=C([]),V=C([]),L=C([]);C(null);const R=C({name:"",description:"",dimensions:[]}),k=C({});C(!1),C(""),C(null);const te=C(null),P=C(1),W=C(10),Yn=H(()=>Lt.value.length),Gn=H(()=>{const o=(P.value-1)*W.value,r=o+W.value;return Lt.value.slice(o,r)}),Wn=o=>{P.value=o},Qn=o=>{W.value=o,P.value=1},_e=C(""),Zn=()=>{_e.value="",P.value=1},Lt=H(()=>{if(!a.value)return[];let o=V.value.filter(r=>r.template_id===a.value);if(_e.value.trim()){const r=_e.value.toLowerCase().trim();o=o.filter(m=>m.name.toLowerCase().includes(r))}return o.sort((r,m)=>{const g=new Date(r.updated_at||r.created_at).getTime();return new Date(m.updated_at||m.created_at).getTime()-g}),o}),Mt=H(()=>R.value.name.trim()&&R.value.dimensions.length>0),dt=H(()=>N.value.find(o=>o.id===a.value)),jn=o=>{const r=N.value.find(m=>m.id===o);return r?r.name:"未知模板"},ft=o=>L.value.filter(r=>r.template_id===o.id).length,ei=()=>{n.push({name:"bookWriting"})},qt=(o={})=>{R.value={name:"",description:"",dimensions:[],...o},u.value=!0},Jt=async()=>{try{console.log(i.value);const o=await window.pywebview.api.book_controller.save_template({...R.value,book_id:i.value}),r=typeof o=="string"?JSON.parse(o):o;r.status==="success"?(x.success("保存成功"),u.value=!1,re()):x.error(r.message||"保存失败")}catch(o){x.error("保存失败："+o.message)}},ti=async o=>{try{await Ge.confirm("删除模板将同时删除该模板下的所有实体，是否继续？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const r=await window.pywebview.api.book_controller.delete_template(o.id,i.value),m=typeof r=="string"?JSON.parse(r):r;m.status==="success"?(x.success("删除成功"),re(),await ge()):x.error(m.message||"删除失败")}catch(r){r!=="cancel"&&x.error("删除失败："+r.message)}},ni=(o={})=>{const r=N.value.find(m=>m.id===(o.template_id||a.value));if(k.value={id:"",name:"",description:"",template_id:r?.id||"",dimensions:{},...o},r&&r.dimensions){const m=r.dimensions.reduce((g,E)=>(g[E.name]=k.value.dimensions?.[E.name]||"",g),{});k.value.dimensions=m}d.value=!0},ne=o=>{o.key==="Enter"&&(o.shiftKey?(o.preventDefault(),ie()):!o.ctrlKey&&!o.altKey&&!o.metaKey&&o.target.tagName!=="TEXTAREA"&&(o.preventDefault(),ie().then(()=>{d.value=!1,f.value=!1})))},ie=async()=>{try{if(!k.value.name)return x.error("请输入实体名称"),Promise.reject("请输入实体名称");if(!k.value.template_id)return x.error("请选择模板类型"),Promise.reject("请选择模板类型");const o=N.value.find(E=>E.id===k.value.template_id);if(!o)return x.error("无效的模板类型"),Promise.reject("无效的模板类型");if(V.value.find(E=>E.template_id===k.value.template_id&&E.name===k.value.name&&E.id!==k.value.id))return x.error(`当前模板下已存在名为"${k.value.name}"的实体`),Promise.reject(`当前模板下已存在名为"${k.value.name}"的实体`);o.dimensions.forEach(E=>{const S=k.value.dimensions[E.name];S==null||typeof S=="string"&&S.trim()===""?k.value.dimensions[E.name]="未设定":k.value.dimensions[E.name]=String(S)});const m=await window.pywebview.api.book_controller.save_entity({...k.value,book_id:i.value}),g=typeof m=="string"?JSON.parse(m):m;if(g.status==="success")return x.success(k.value.id?"更新成功":"创建成功"),d.value=!1,await ge(),Promise.resolve();throw new Error(g.message||"保存失败")}catch(o){return console.error("保存实体失败:",o),x.error("保存失败："+o.message),Promise.reject(o)}},ii=async o=>{try{await Ge.confirm("确定要删除这个实体吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const r=await window.pywebview.api.book_controller.delete_entity(o.id,i.value,o.template_id),m=typeof r=="string"?JSON.parse(r):r;if(m.status==="success"){x.success("删除成功");const g=L.value.findIndex(E=>E.id===o.id);g!==-1&&L.value.splice(g,1),a.value&&(V.value=L.value.filter(E=>E.template_id===a.value)),await ge()}else x.error(m.message||"删除失败")}catch(r){r!=="cancel"&&x.error("删除失败："+r.message)}},ri=async o=>{a.value=o,o?V.value=L.value.filter(r=>r.template_id===o):V.value=[]},si=(o,r=3)=>{const g=Object.entries(o).slice(0,r);return Object.fromEntries(g)},oi=H(()=>k.value?.template_id?N.value.find(r=>r.id===k.value.template_id)?.dimensions||[]:[]),ai=o=>{const r=N.value.find(m=>m.id===o.template_id);if(!r){x.error("找不到对应的模板");return}k.value=JSON.parse(JSON.stringify(o)),k.value.dimensions||(k.value.dimensions={}),r.dimensions.forEach(m=>{m.name in k.value.dimensions||(k.value.dimensions[m.name]="")}),f.value=!0},Bt=o=>{y.value=o,w.value=k.value.dimensions[o]||"",h.value=!0},Ut=()=>{k.value.dimensions[y.value]=w.value,h.value=!1,x.success("维度内容已保存")},Ft=()=>{h.value=!1,w.value="",y.value=""},li=()=>{w.value=""},ui=o=>{o.key==="Escape"?Ft():o.key==="s"&&(o.ctrlKey||o.metaKey)&&(o.preventDefault(),Ut())},re=async()=>{b.value=!0;try{const o=await window.pywebview.api.book_controller.get_templates(i.value),r=typeof o=="string"?JSON.parse(o):o;if(r.status==="success"){let m=r.data||[];m.sort((g,E)=>g.sort_order!==void 0&&E.sort_order!==void 0?g.sort_order-E.sort_order:g.sort_order!==void 0?-1:E.sort_order!==void 0?1:new Date(g.created_at||0)-new Date(E.created_at||0)),N.value=m,N.value.length>0&&!a.value&&(a.value=N.value[0].id),await ge(),setTimeout(()=>{qe()},100)}else x.error(r.message||"加载模板失败")}catch(o){x.error("加载模板失败："+o.message)}finally{b.value=!1}},ge=async()=>{try{const o=await window.pywebview.api.book_controller.get_entities(i.value),r=typeof o=="string"?JSON.parse(o):o;r.status==="success"?(L.value=r.data||[],a.value&&(V.value=L.value.filter(m=>m.template_id===a.value))):x.error(r.message||"加载实体失败")}catch(o){console.error("加载实体失败：",o),x.error("加载实体失败："+o.message)}},Me=C(!1),se=C(-1),qe=()=>{Ye(()=>{const o=document.querySelector(".sortable-table .el-table__body-wrapper tbody");if(!o){console.log("未找到表格body");return}console.log("初始化拖拽排序"),o.removeEventListener("dragstart",pt),o.removeEventListener("dragover",mt),o.removeEventListener("drop",ht),o.removeEventListener("dragend",vt),o.addEventListener("dragstart",pt),o.addEventListener("dragover",mt),o.addEventListener("drop",ht),o.addEventListener("dragend",vt);const r=o.querySelectorAll("tr");console.log("找到行数:",r.length),r.forEach((m,g)=>{m.dataset.index=g,console.log(`设置第${g}行索引`);const E=m.querySelector(".sort-handle");E&&(E.draggable=!0,E.dataset.rowIndex=g)})})},pt=o=>{if(!o.target.closest(".sort-handle")){o.preventDefault();return}const m=o.target.closest("tr");if(!m||!m.dataset.index){o.preventDefault();return}console.log("开始拖拽，行索引:",m.dataset.index),Me.value=!0,se.value=parseInt(m.dataset.index),m.style.opacity="0.5",m.classList.add("dragging"),o.dataTransfer.effectAllowed="move",o.dataTransfer.setData("text/plain",m.dataset.index)},mt=o=>{if(!Me.value)return;o.preventDefault(),o.dataTransfer.dropEffect="move";const r=o.target.closest("tr");r&&(r.style.borderTop="2px solid var(--el-color-primary)")},ht=o=>{if(!Me.value)return;o.preventDefault();const r=o.target.closest("tr");if(r){const m=parseInt(r.dataset.index);console.log("拖拽放下，从",se.value,"到",m),se.value!==m&&!isNaN(se.value)&&!isNaN(m)&&ci(se.value,m),r.style.borderTop=""}},vt=o=>{console.log("拖拽结束"),Me.value=!1,se.value=-1;const r=o.target.closest("tr");r&&(r.style.opacity="1",r.classList.remove("dragging")),document.querySelectorAll(".sortable-table tbody tr").forEach(g=>{g.style.borderTop=""})},ci=async(o,r)=>{try{console.log("移动模板从",o,"到",r);const m=[...N.value],[g]=m.splice(o,1);m.splice(r,0,g),m.forEach((E,S)=>{E.sort_order=S}),N.value=m,await di(),x.success("模板排序已更新"),setTimeout(()=>{qe()},100)}catch(m){console.error("移动模板失败:",m),x.error("移动模板失败: "+m.message),await re()}},di=async()=>{try{const o=N.value.map((g,E)=>({id:g.id,sort_order:E})),r=await window.pywebview.api.book_controller.update_template_order({book_id:i.value,templates:o}),m=typeof r=="string"?JSON.parse(r):r;if(m.status!=="success")throw new Error(m.message||"保存排序失败")}catch(o){throw console.error("保存模板排序失败:",o),o}};He(l,o=>{o==="templates"&&setTimeout(()=>{console.log("切换到模板管理，重新初始化拖拽"),qe()},200)}),Fi(async()=>{console.log("组件挂载，bookId:",i.value),i.value&&(await re(),setTimeout(()=>{console.log("延迟初始化拖拽"),qe()},500))}),Pi(()=>{const o=document.querySelector(".sortable-table .el-table__body-wrapper tbody");o&&(o.removeEventListener("dragstart",pt),o.removeEventListener("dragover",mt),o.removeEventListener("drop",ht),o.removeEventListener("dragend",vt))});const fi=async()=>{try{if(!N.value.find(S=>S.id===a.value)){x.error("请先选择一个模板");return}if(V.value.filter(S=>S.template_id===a.value).length===0){x.error("当前模板下没有实体");return}const m={book_id:i.value,template_id:a.value,type:"names"},g=await window.pywebview.api.book_controller.save_entity_export(m),E=typeof g=="string"?JSON.parse(g):g;E.status==="success"?x.success("导出成功，文件已保存到指定目录"):x.error(`导出失败: ${E.message}`)}catch(o){console.error("导出错误:",o),x.error(`导出出错: ${o.message}`)}},pi=o=>{switch(o){case"exportCurrentNames":fi();break;case"exportCurrentDetails":hi();break;case"exportCurrentDetailsJson":vi();break;case"exportAllNames":mi();break}},mi=async()=>{try{if(V.value.length===0){x.error("没有可导出的实体");return}const o={book_id:i.value,template_id:"all",type:"all_names"},r=await window.pywebview.api.book_controller.save_entity_export(o),m=typeof r=="string"?JSON.parse(r):r;m.status==="success"?x.success("导出成功，文件已保存到指定目录"):x.error(`导出失败: ${m.message}`)}catch(o){console.error("导出错误:",o),x.error(`导出出错: ${o.message}`)}},hi=async()=>{try{if(!N.value.find(S=>S.id===a.value)){x.error("请先选择一个模板");return}if(V.value.filter(S=>S.template_id===a.value).length===0){x.error("当前模板下没有实体");return}const m={book_id:i.value,template_id:a.value,type:"details"},g=await window.pywebview.api.book_controller.save_entity_export(m),E=typeof g=="string"?JSON.parse(g):g;E.status==="success"?x.success("导出成功，文件已保存到指定目录"):x.error(`导出失败: ${E.message}`)}catch(o){console.error("导出错误:",o),x.error(`导出出错: ${o.message}`)}},vi=async()=>{try{if(!N.value.find(S=>S.id===a.value)){x.error("请先选择一个模板");return}if(V.value.filter(S=>S.template_id===a.value).length===0){x.error("当前模板下没有实体");return}const m={book_id:i.value,template_id:a.value,type:"details_json"},g=await window.pywebview.api.book_controller.save_entity_export(m),E=typeof g=="string"?JSON.parse(g):g;E.status==="success"?x.success("导出成功，文件已保存到指定目录"):x.error(`导出失败: ${E.message}`)}catch(o){console.error("导出错误:",o),x.error(`导出出错: ${o.message}`)}},_i=async o=>{try{let r=`【${o.name}】
`;if(o.description&&o.description.trim()){const m=o.description.replace(/\r?\n/g," ").replace(/\s+/g," ").trim();r+=`  描述: ${m}
`}if(o.dimensions&&Object.keys(o.dimensions).length>0){const m=Object.entries(o.dimensions).filter(([,g])=>g&&g.trim()!==""&&g!=="未设定");m.length>0&&(r+=`
  维度信息:
`,m.forEach(([g,E])=>{const S=E.replace(/\r?\n/g," ").replace(/\s+/g," ").trim();r+=`    • ${g}: ${S}
`}))}r+=`
`+"─".repeat(30)+`
`,await window.pywebview.api.copy_to_clipboard(r),x.success("已复制到剪贴板")}catch(r){console.error("复制失败:",r),x.error("复制失败："+r.message)}},gi=async o=>{try{await window.pywebview.api.copy_to_clipboard(JSON.stringify(o,null,2)),x.success("实体完整JSON数据已复制到剪贴板")}catch(r){console.error("复制完整JSON失败:",r),x.error("复制完整JSON失败："+r.message)}},ye=C(!1),we=C(""),yi=()=>{ye.value=!0,we.value=""},wi=async()=>{try{if(!we.value.trim()){x.error("请输入JSON字符串");return}const o=JSON.parse(we.value);if(!o.name){x.error("导入失败：缺少实体名称");return}const r=a.value;if(!r){x.error("请先选择一个模板");return}const m=V.value.find(D=>D.template_id===r&&D.name===o.name);if(m&&!await Ge.confirm(`当前模板下已存在名为"${o.name}"的实体，是否覆盖？`,"警告",{confirmButtonText:"覆盖",cancelButtonText:"取消",type:"warning"}).catch(()=>!1))return;const g={name:o.name,description:o.description||"",dimensions:{},template_id:r,book_id:i.value};m&&(g.id=m.id),N.value.find(D=>D.id===r).dimensions.forEach(D=>{g.dimensions[D.name]=o.dimensions&&o.dimensions[D.name]!==void 0?o.dimensions[D.name]:"未设定"});const S=await window.pywebview.api.book_controller.save_entity(g),Ue=typeof S=="string"?JSON.parse(S):S;if(Ue.status==="success")x.success(m?"更新成功":"导入成功"),ye.value=!1,ge();else throw new Error(Ue.message||"导入失败")}catch(o){o instanceof SyntaxError?x.error("导入失败：JSON格式不正确"):(console.error("导入失败:",o),x.error("导入失败："+o.message))}},xe=C(!1),be=C(""),xi=()=>{xe.value=!0,be.value=""},bi=async()=>{try{if(!be.value.trim()){x.error("请输入模板数据");return}const o=JSON.parse(be.value);if(!o.name||!Array.isArray(o.dimensions)){x.error("模板数据格式不正确");return}const r=N.value.find(S=>S.name===o.name);if(r&&!await Ge.confirm(`已存在名为"${o.name}"的模板，是否覆盖？`,"警告",{confirmButtonText:"覆盖",cancelButtonText:"取消",type:"warning"}).catch(()=>!1))return;const m={...o,book_id:i.value};r&&(m.id=r.id);const g=await window.pywebview.api.book_controller.save_template(m),E=typeof g=="string"?JSON.parse(g):g;if(E.status==="success")x.success(r?"更新成功":"导入成功"),xe.value=!1,re();else throw new Error(E.message||"导入失败")}catch(o){o instanceof SyntaxError?x.error("导入失败：JSON格式不正确"):(console.error("导入失败:",o),x.error("导入失败："+o.message))}},ki=async o=>{try{const r={name:o.name,description:o.description,dimensions:o.dimensions};await window.pywebview.api.copy_to_clipboard(JSON.stringify(r,null,2)),x.success("模板数据已复制到剪贴板")}catch(r){console.error("导出失败:",r),x.error("导出失败："+r.message)}},oe=C(1),Je=C(10),Ei=H(()=>Pt.value.length),Ci=H(()=>{const o=(oe.value-1)*Je.value,r=o+Je.value;return Pt.value.slice(o,r)}),Ni=o=>{oe.value=o},Ti=o=>{Je.value=o,oe.value=1},ke=C(""),Pt=H(()=>{if(!ke.value.trim())return N.value;const o=ke.value.toLowerCase().trim();return N.value.filter(r=>r.name.toLowerCase().includes(o))}),Si=()=>{ke.value="",oe.value=1},ae=C(!1),$i=()=>{document.body.style.overflow="hidden",k.value.dimensions&&Object.keys(k.value.dimensionValues||{}).length>6&&(ae.value=!0)},Vi=()=>{document.body.style.overflow="",ae.value=!1},Di=()=>{document.body.style.overflow="hidden",document.body.classList.add("modal-open"),Ye(()=>{_t.value&&_t.value.focus()})},Ai=()=>{document.body.style.overflow="",document.body.classList.remove("modal-open"),le.value=!1,Ee.value=""},Oi=o=>{R.value.dimensions.splice(o,1)},Ht=()=>{const o=Ee.value.trim();if(o){if(R.value.dimensions.some(m=>m.name===o)){x.warning("已存在相同名称的维度");return}R.value.dimensions.push({name:o,type:"text",required:!1}),Ee.value="",Ye(()=>{Ce.value&&Ce.value.focus()})}return!1},Ii=()=>{Be.value||setTimeout(()=>{Be.value||(le.value=!1)},200)},Ee=C(""),le=C(!1),Ce=C(null),_t=C(null);He(le,o=>{o&&Ye(()=>{Ce.value&&Ce.value.focus()})});const Ri=()=>{Be.value=!0,Ht(),setTimeout(()=>{Be.value=!1},100)},Be=C(!1),Kt=o=>{o.key==="Enter"&&(o.preventDefault(),o.shiftKey&&Mt.value&&Jt())};return(o,r)=>{const m=Hi,g=Xi,E=Wi,S=Gi,Ue=Qi,D=Zi,Fe=er,zi=ji,Li=tr,Mi=nr,Y=ir,Xt=rr,Yt=or,Gt=lr,ue=dr,ce=pr,Pe=fr,qi=mr,Wt=gr,Qt=_r,Zt=ar;return A(),z(fe,null,[_("div",La,[_("div",Ma,[_("div",qa,[_("h1",Ja,O(s.value),1),_("div",Ba,[c(g,{class:Ke(["tab-button",{active:l.value==="entities"}]),onClick:r[0]||(r[0]=v=>l.value="entities"),type:"primary",plain:l.value!=="entities"},{default:p(()=>[c(m,null,{default:p(()=>[c($(Ki))]),_:1}),r[34]||(r[34]=T(" 设定实体 "))]),_:1},8,["class","plain"]),c(g,{class:Ke(["tab-button",{active:l.value==="templates"}]),onClick:r[1]||(r[1]=v=>l.value="templates"),type:"primary",plain:l.value!=="templates"},{default:p(()=>[c(m,null,{default:p(()=>[c($(gt))]),_:1}),r[35]||(r[35]=T(" 模板管理 "))]),_:1},8,["class","plain"])])]),c(g,{class:"back-button modern-button",onClick:ei,type:"primary",size:"large"},{default:p(()=>[c(m,null,{default:p(()=>[c($(Yi))]),_:1}),r[36]||(r[36]=T(" 返回写作 "))]),_:1})]),_("div",Ua,[_("div",Fa,[Xe(_("div",Pa,[_("div",Ha,[_("div",Ka,[r[37]||(r[37]=_("h2",null,"设定实体列表",-1)),c(Ue,{modelValue:a.value,"onUpdate:modelValue":r[2]||(r[2]=v=>a.value=v),placeholder:"请选择模板",class:"template-select",onChange:ri},{default:p(()=>[(A(!0),z(fe,null,Ne(N.value,v=>(A(),pe(S,{key:v.id,label:`${v.name} (${ft(v)}个实体)`,value:v.id},{default:p(()=>[_("div",Xa,[_("span",Ya,O(v.name),1),c(E,{size:"small",type:"info",effect:"plain",class:"entity-count-tag"},{default:p(()=>[T(O(ft(v))+"个实体 ",1)]),_:2},1024)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),c(D,{modelValue:_e.value,"onUpdate:modelValue":r[3]||(r[3]=v=>_e.value=v),placeholder:"搜索实体名称",class:"search-input",clearable:"",onClear:Zn},{prefix:p(()=>[c(m,null,{default:p(()=>[c($(en))]),_:1})]),_:1},8,["modelValue"])]),_("div",Ga,[c(Li,{onCommand:pi,"split-button":"",type:"primary"},{dropdown:p(()=>[c(zi,null,{default:p(()=>[c(Fe,{command:"exportCurrentNames",disabled:!a.value},{default:p(()=>r[38]||(r[38]=[T("导出当前模板实体名称")])),_:1},8,["disabled"]),c(Fe,{command:"exportCurrentDetails",disabled:!a.value},{default:p(()=>r[39]||(r[39]=[T("导出当前模板实体详情")])),_:1},8,["disabled"]),c(Fe,{command:"exportCurrentDetailsJson",disabled:!a.value},{default:p(()=>r[40]||(r[40]=[T("导出当前模板实体详情(JSON)")])),_:1},8,["disabled"]),c(Fe,{command:"exportAllNames",disabled:V.value.length===0},{default:p(()=>r[41]||(r[41]=[T("导出所有模板实体名称")])),_:1},8,["disabled"])]),_:1})]),default:p(()=>[r[42]||(r[42]=_("span",null,"导出",-1))]),_:1}),c(g,{type:"success",onClick:yi,disabled:!a.value},{default:p(()=>[c(m,null,{default:p(()=>[c($(tn))]),_:1}),r[43]||(r[43]=T(" 导入实体 "))]),_:1},8,["disabled"]),c(g,{type:"primary",onClick:r[4]||(r[4]=v=>ni()),disabled:!a.value,class:"create-entity-button"},{default:p(()=>[c(m,null,{default:p(()=>[c($(yt))]),_:1}),r[44]||(r[44]=T(" 新建设定实体 "))]),_:1},8,["disabled"])])]),_("div",Wa,[a.value?Xe((A(),pe(Yt,{key:1,data:Gn.value,style:{width:"100%"},onRowClick:ai,"row-style":{height:"120px"},class:"modern-entity-table"},{default:p(()=>[c(Y,{prop:"name",label:"实体名称",width:"200",fixed:"left"},{default:p(({row:v})=>[_("div",Qa,[_("div",Za,O(v.name),1),_("div",ja,[c(E,{size:"small",type:"info",effect:"plain"},{default:p(()=>[T(O(jn(v.template_id)),1)]),_:2},1024)])])]),_:1}),c(Y,{label:"维度信息","min-width":"400"},{default:p(({row:v})=>[_("div",el,[(A(!0),z(fe,null,Ne(si(v.dimensions,6),(I,de)=>(A(),z("div",{key:de,class:Ke(["dimension-item",{unset:I==="未设定"}])},[_("div",tl,O(de),1),_("div",nl,O(I==="未设定"?"—":I),1)],2))),128)),Object.keys(v.dimensions).length>6?(A(),z("div",il,[r[45]||(r[45]=_("div",{class:"dimension-label"},"更多",-1)),_("div",rl,"+"+O(Object.keys(v.dimensions).length-6),1)])):Te("",!0)])]),_:1}),c(Y,{label:"操作",width:"280",fixed:"right",align:"center"},{default:p(({row:v})=>[_("div",sl,[c(Xt,{class:"operation-buttons"},{default:p(()=>[c(g,{size:"small",type:"primary",onClick:Q(I=>_i(v),["stop"])},{default:p(()=>[c(m,null,{default:p(()=>[c($(gt))]),_:1}),r[46]||(r[46]=T(" 复制 "))]),_:2},1032,["onClick"]),c(g,{size:"small",type:"info",onClick:Q(I=>gi(v),["stop"])},{default:p(()=>[c(m,null,{default:p(()=>[c($(sr))]),_:1}),r[47]||(r[47]=T(" JSON "))]),_:2},1032,["onClick"]),c(g,{size:"small",type:"danger",onClick:Q(I=>ii(v),["stop"])},{default:p(()=>[c(m,null,{default:p(()=>[c($(wt))]),_:1}),r[48]||(r[48]=T(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Zt,b.value]]):(A(),pe(Mi,{key:0,description:"请先选择一个模板来查看相关实体"}))]),_("div",ol,[c(Gt,{modelValue:P.value,"onUpdate:modelValue":r[5]||(r[5]=v=>P.value=v),"page-size":W.value,"page-sizes":[5,10,20,50,100],total:Yn.value,"pager-count":5,onSizeChange:Qn,onCurrentChange:Wn,layout:"total, sizes, prev, pager, next",background:""},null,8,["modelValue","page-size","total"])])],512),[[jt,l.value==="entities"]]),Xe(_("div",al,[_("div",ll,[_("div",ul,[r[49]||(r[49]=_("h2",null,"设定模板",-1)),c(D,{modelValue:ke.value,"onUpdate:modelValue":r[6]||(r[6]=v=>ke.value=v),placeholder:"搜索模板名称",class:"search-input",clearable:"",onClear:Si},{prefix:p(()=>[c(m,null,{default:p(()=>[c($(en))]),_:1})]),_:1},8,["modelValue"])]),_("div",cl,[c(g,{type:"success",class:"import-button",onClick:xi},{default:p(()=>[c(m,null,{default:p(()=>[c($(tn))]),_:1}),r[50]||(r[50]=T(" 导入模板 "))]),_:1}),c(g,{type:"primary",class:"create-button",onClick:r[7]||(r[7]=v=>qt())},{default:p(()=>[c(m,null,{default:p(()=>[c($(yt))]),_:1}),r[51]||(r[51]=T(" 新建模板 "))]),_:1})])]),_("div",dl,[Xe((A(),pe(Yt,{data:Ci.value,style:{width:"100%"},"row-style":{height:"100px"},class:"modern-template-table sortable-table","row-key":"id"},{default:p(()=>[c(Y,{label:"排序",width:"80",align:"center",fixed:"left"},{default:p(({$index:v})=>[_("div",{class:"sort-handle","data-index":v,title:"拖拽此处可调整模板顺序"},[c(m,{class:"drag-icon"},{default:p(()=>[c($(nn))]),_:1}),_("span",pl,O(v+1),1)],8,fl)]),_:1}),c(Y,{prop:"name",label:"模板名称",width:"200",fixed:"left"},{default:p(({row:v})=>[_("div",ml,[_("div",hl,O(v.name),1),v.description?(A(),z("div",vl,O(v.description),1)):Te("",!0)])]),_:1}),c(Y,{label:"维度配置","min-width":"350"},{default:p(({row:v})=>[_("div",_l,[(A(!0),z(fe,null,Ne(v.dimensions.slice(0,8),(I,de)=>(A(),z("div",{key:I.name,class:Ke(["dimension-chip",{primary:de<3,secondary:de>=3}])},O(I.name),3))),128)),v.dimensions.length>8?(A(),z("div",gl," +"+O(v.dimensions.length-8),1)):Te("",!0)])]),_:1}),c(Y,{label:"统计信息",width:"160",align:"center"},{default:p(({row:v})=>[_("div",yl,[_("div",wl,[_("div",xl,O(ft(v)),1),r[52]||(r[52]=_("div",{class:"stat-label"},"实体",-1))]),_("div",bl,[_("div",kl,O(v.dimensions.length),1),r[53]||(r[53]=_("div",{class:"stat-label"},"维度",-1))])])]),_:1}),c(Y,{label:"操作",width:"260",fixed:"right",align:"center"},{default:p(({row:v})=>[_("div",El,[c(Xt,{class:"operation-buttons"},{default:p(()=>[c(g,{size:"small",type:"primary",onClick:I=>qt(v)},{default:p(()=>[c(m,null,{default:p(()=>[c($(ur))]),_:1}),r[54]||(r[54]=T(" 编辑 "))]),_:2},1032,["onClick"]),c(g,{size:"small",type:"success",onClick:I=>ki(v)},{default:p(()=>[c(m,null,{default:p(()=>[c($(cr))]),_:1}),r[55]||(r[55]=T(" 导出 "))]),_:2},1032,["onClick"]),c(g,{size:"small",type:"danger",onClick:I=>ti(v)},{default:p(()=>[c(m,null,{default:p(()=>[c($(wt))]),_:1}),r[56]||(r[56]=T(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Zt,b.value]])]),_("div",Cl,[c(Gt,{modelValue:oe.value,"onUpdate:modelValue":r[8]||(r[8]=v=>oe.value=v),"page-size":Je.value,"page-sizes":[5,10,20,50],total:Ei.value,"pager-count":5,onSizeChange:Ti,onCurrentChange:Ni,layout:"total, sizes, prev, pager, next",background:""},null,8,["modelValue","page-size","total"])])],512),[[jt,l.value==="templates"]])])])]),c(ue,{modelValue:u.value,"onUpdate:modelValue":r[16]||(r[16]=v=>u.value=v),title:R.value.id?"编辑模板":"创建模板","close-on-click-modal":!1,"append-to-body":!0,"lock-scroll":!0,"destroy-on-close":!1,class:"native-template-dialog",width:"800px",modal:!0,"show-close":!0,onOpen:Di,onClose:Ai},{footer:p(()=>[_("div",Ul,[c(g,{onClick:r[15]||(r[15]=v=>u.value=!1),size:"large"},{default:p(()=>r[64]||(r[64]=[T(" 取消 ")])),_:1}),c(g,{type:"primary",onClick:Jt,disabled:!Mt.value,size:"large"},{default:p(()=>[c(m,null,{default:p(()=>[c($(xt))]),_:1}),T(" "+O(R.value.id?"保存修改":"创建模板"),1)]),_:1},8,["disabled"])])]),default:p(()=>[_("div",Nl,[_("div",Tl,[r[59]||(r[59]=_("h4",{class:"section-title"},"基本信息",-1)),_("div",Sl,[_("div",$l,[r[57]||(r[57]=_("label",{class:"field-label"},[T("模板名称 "),_("span",{class:"required"},"*")],-1)),c(D,{modelValue:R.value.name,"onUpdate:modelValue":r[9]||(r[9]=v=>R.value.name=v),placeholder:"请输入模板名称",clearable:"",ref_key:"templateNameInput",ref:_t,onKeydown:Kt},null,8,["modelValue"])])]),_("div",Vl,[_("div",Dl,[r[58]||(r[58]=_("label",{class:"field-label"},"模板描述",-1)),c(D,{modelValue:R.value.description,"onUpdate:modelValue":r[10]||(r[10]=v=>R.value.description=v),type:"textarea",rows:2,placeholder:"请输入模板描述（可选）",onKeydown:Kt},null,8,["modelValue"])])])]),r[60]||(r[60]=_("div",{class:"dimensions-section"},[_("div",{class:"section-header-fixed"},[_("h4",{class:"section-title"},"维度配置")])],-1))]),_("div",Al,[_("div",Ol,[le.value?(A(),z("div",Rl,[c(D,{modelValue:Ee.value,"onUpdate:modelValue":r[12]||(r[12]=v=>Ee.value=v),placeholder:"请输入维度名称",ref_key:"dimensionNameInput",ref:Ce,onKeydown:[rn(Q(Ht,["prevent"]),["enter"]),r[13]||(r[13]=rn(v=>le.value=!1,["esc"]))],onBlur:Ii,size:"small"},{append:p(()=>[c(g,{type:"primary",onClick:Q(Ri,["prevent"]),size:"small"},{default:p(()=>[c(m,null,{default:p(()=>[c($(xt))]),_:1})]),_:1})]),_:1},8,["modelValue","onKeydown"]),r[62]||(r[62]=_("div",{class:"input-tip"},"按回车确认，ESC取消",-1))])):(A(),z("div",Il,[c(g,{type:"primary",onClick:r[11]||(r[11]=v=>le.value=!0),class:"add-btn-full"},{default:p(()=>[c(m,null,{default:p(()=>[c($(yt))]),_:1}),r[61]||(r[61]=T(" 添加新维度 "))]),_:1})]))]),_("div",zl,[_("div",Ll,[R.value.dimensions.length===0?(A(),z("div",Ml,r[63]||(r[63]=[_("div",{class:"empty-icon"},"📝",-1),_("div",{class:"empty-text"},"暂无维度",-1),_("div",{class:"empty-hint"},"点击上方按钮添加第一个维度",-1)]))):(A(),z("div",ql,[c($(yr),{modelValue:R.value.dimensions,"onUpdate:modelValue":r[14]||(r[14]=v=>R.value.dimensions=v),group:"dimensions","item-key":"name",handle:".drag-handle","ghost-class":"ghost-dimension",animation:200,class:"dimensions-draggable"},{item:p(({element:v,index:I})=>[_("div",Jl,[c(m,{class:"drag-handle"},{default:p(()=>[c($(nn))]),_:1}),_("span",Bl,O(v.name),1),c(g,{type:"danger",size:"small",text:"",onClick:de=>Oi(I),class:"remove-button"},{default:p(()=>[c(m,null,{default:p(()=>[c($(sn))]),_:1})]),_:2},1032,["onClick"])])]),_:1},8,["modelValue"])]))])])])]),_:1},8,["modelValue","title"]),c(ue,{modelValue:d.value,"onUpdate:modelValue":r[21]||(r[21]=v=>d.value=v),title:k.value.id?"编辑实体":"创建实体","close-on-click-modal":!1,"append-to-body":!0,"lock-scroll":!0,"destroy-on-close":!1,class:"entity-detail-dialog",fullscreen:ae.value,onOpen:$i,onClose:Vi},{footer:p(()=>[_("div",Yl,[c(g,{onClick:r[19]||(r[19]=v=>ae.value=!ae.value)},{default:p(()=>[c(m,null,{default:p(()=>[c($(hr))]),_:1}),T(" "+O(ae.value?"退出全屏":"全屏模式"),1)]),_:1}),c(g,{onClick:r[20]||(r[20]=v=>d.value=!1)},{default:p(()=>r[66]||(r[66]=[T("取消")])),_:1}),c(g,{type:"primary",onClick:ie},{default:p(()=>r[67]||(r[67]=[T("保存")])),_:1})])]),default:p(()=>[_("div",Fl,[_("div",Pl,[c(Pe,{model:k.value,"label-width":"100px",onSubmit:Q(ie,["prevent"])},{default:p(()=>[c(ce,{label:"实体名称",required:""},{default:p(()=>[c(D,{modelValue:k.value.name,"onUpdate:modelValue":r[17]||(r[17]=v=>k.value.name=v),placeholder:"输入实体名称",clearable:"",onKeydown:ne,ref_key:"entityNameInput",ref:te},null,8,["modelValue"])]),_:1}),c(ce,{label:"描述"},{default:p(()=>[c(D,{modelValue:k.value.description,"onUpdate:modelValue":r[18]||(r[18]=v=>k.value.description=v),type:"textarea",rows:3,placeholder:"输入实体描述",onKeydown:ne},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),dt.value?(A(),z("div",Hl,[r[65]||(r[65]=_("h3",{class:"dimensions-title"},"维度信息",-1)),_("div",Kl,[c(qi,{height:"400px",class:"dimensions-scrollbar"},{default:p(()=>[c(Pe,{model:k.value,"label-position":"top",class:"dimensions-form"},{default:p(()=>[(A(!0),z(fe,null,Ne(dt.value.dimensions,v=>(A(),pe(ce,{key:v.name,class:"dimension-form-item"},{label:p(()=>[_("span",{class:"dimension-label clickable-label",onDblclick:I=>Bt(v.name),title:`双击展开 ${v.name} 的大型编辑器`},O(v.name),41,Xl)]),default:p(()=>[c(D,{modelValue:k.value.dimensions[v.name],"onUpdate:modelValue":I=>k.value.dimensions[v.name]=I,placeholder:"输入"+v.name,type:"textarea",rows:2,onKeydown:ne,class:"dimension-textarea"},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:2},1024))),128))]),_:1},8,["model"])]),_:1})])])):Te("",!0)])]),_:1},8,["modelValue","title","fullscreen"]),c(ue,{modelValue:f.value,"onUpdate:modelValue":r[25]||(r[25]=v=>f.value=v),fullscreen:"","show-close":!1,"close-on-click-modal":!1,class:"entity-detail-dialog fullscreen-dialog"},{footer:p(()=>[_("div",eu,[c(g,{size:"large",onClick:r[24]||(r[24]=v=>f.value=!1)},{default:p(()=>r[69]||(r[69]=[T("取消")])),_:1}),c(g,{size:"large",type:"primary",onClick:ie},{default:p(()=>r[70]||(r[70]=[T("保存")])),_:1})])]),default:p(()=>[_("div",Gl,[_("div",Wl,[c(Pe,{model:k.value,"label-width":"120px",onSubmit:Q(ie,["prevent"])},{default:p(()=>[c(ce,{label:"实体名称",required:""},{default:p(()=>[c(D,{modelValue:k.value.name,"onUpdate:modelValue":r[22]||(r[22]=v=>k.value.name=v),placeholder:"实体名称",size:"large",onKeydown:ne},null,8,["modelValue"])]),_:1}),c(ce,{label:"描述"},{default:p(()=>[c(D,{modelValue:k.value.description,"onUpdate:modelValue":r[23]||(r[23]=v=>k.value.description=v),type:"textarea",rows:4,placeholder:"输入实体描述",onKeydown:ne},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),dt.value?(A(),z("div",Ql,[r[68]||(r[68]=_("h3",{class:"dimensions-title"},"维度信息",-1)),_("div",Zl,[c(Pe,{model:k.value,"label-width":"120px",class:"dimensions-form"},{default:p(()=>[(A(!0),z(fe,null,Ne(oi.value,v=>(A(),pe(ce,{key:v.name,class:"dimension-form-item"},{label:p(()=>[_("span",{class:"dimension-label clickable-label",onDblclick:I=>Bt(v.name),title:`双击展开 ${v.name} 的大型编辑器`},O(v.name),41,jl)]),default:p(()=>[c(D,{modelValue:k.value.dimensions[v.name],"onUpdate:modelValue":I=>k.value.dimensions[v.name]=I,placeholder:"输入"+v.name,type:"textarea",rows:4,resize:"none",onKeydown:ne,class:"dimension-textarea"},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:2},1024))),128))]),_:1},8,["model"])])])):Te("",!0)])]),_:1},8,["modelValue"]),c(ue,{modelValue:h.value,"onUpdate:modelValue":r[27]||(r[27]=v=>h.value=v),title:`编辑维度: ${y.value}`,fullscreen:"","show-close":!1,"close-on-click-modal":!1,class:"dimension-editor-dialog native-editor-dialog"},{default:p(()=>[_("div",tu,[_("div",nu,[_("div",iu,[_("h3",null,O(y.value),1),_("div",ru,[_("span",su,[c(m,null,{default:p(()=>[c($(gt))]),_:1}),T(" "+O(w.value.length)+" 字符 ",1)]),_("span",ou,[c(m,null,{default:p(()=>[c($(vr))]),_:1}),T(" "+O(w.value.split(`
`).length)+" 行 ",1)])])]),_("div",au,[c(g,{onClick:li,class:"action-btn danger-btn",size:"large"},{default:p(()=>[c(m,null,{default:p(()=>[c($(wt))]),_:1}),r[71]||(r[71]=_("span",{class:"btn-text"},"清空",-1))]),_:1}),c(g,{onClick:Ut,class:"action-btn primary-btn",size:"large"},{default:p(()=>[c(m,null,{default:p(()=>[c($(xt))]),_:1}),r[72]||(r[72]=_("span",{class:"btn-text"},"保存",-1))]),_:1}),c(g,{onClick:Ft,class:"action-btn cancel-btn",size:"large"},{default:p(()=>[c(m,null,{default:p(()=>[c($(sn))]),_:1}),r[73]||(r[73]=_("span",{class:"btn-text"},"取消",-1))]),_:1})])]),_("div",lu,[c(D,{modelValue:w.value,"onUpdate:modelValue":r[26]||(r[26]=v=>w.value=v),type:"textarea",placeholder:"在此输入维度内容...",class:"large-editor",resize:"none",onKeydown:ui},null,8,["modelValue"])])])]),_:1},8,["modelValue","title"]),c(ue,{modelValue:ye.value,"onUpdate:modelValue":r[30]||(r[30]=v=>ye.value=v),title:"导入实体",width:"600px",class:"import-dialog"},{footer:p(()=>[_("span",du,[c(g,{onClick:r[29]||(r[29]=v=>ye.value=!1)},{default:p(()=>r[76]||(r[76]=[T("取消")])),_:1}),c(g,{type:"primary",onClick:wi},{default:p(()=>r[77]||(r[77]=[T("确认导入")])),_:1})])]),default:p(()=>[_("div",uu,[c(Qt,null,{default:p(()=>[c(Wt,null,{title:p(()=>[_("div",cu,[c(m,null,{default:p(()=>[c($(on))]),_:1}),r[74]||(r[74]=_("span",null,"查看JSON格式示例",-1))])]),default:p(()=>[r[75]||(r[75]=_("div",{class:"format-hint-content"},[_("pre",null,`{
  "name": "实体名称",
  "description": "实体描述",
  "dimensions": {
    "维度1": "值1",
    "维度2": "值2"
  }
}`)],-1))]),_:1})]),_:1}),c(D,{modelValue:we.value,"onUpdate:modelValue":r[28]||(r[28]=v=>we.value=v),type:"textarea",rows:10,placeholder:"请输入JSON字符串",class:"import-input"},null,8,["modelValue"])])]),_:1},8,["modelValue"]),c(ue,{modelValue:xe.value,"onUpdate:modelValue":r[33]||(r[33]=v=>xe.value=v),title:"导入模板",width:"800px",class:"import-template-dialog","close-on-click-modal":!1,"show-close":!0},{footer:p(()=>[_("div",mu,[c(g,{size:"large",onClick:r[32]||(r[32]=v=>xe.value=!1)},{default:p(()=>r[80]||(r[80]=[T("取消")])),_:1}),c(g,{size:"large",type:"primary",onClick:bi},{default:p(()=>r[81]||(r[81]=[T("确认导入")])),_:1})])]),default:p(()=>[_("div",fu,[c(Qt,null,{default:p(()=>[c(Wt,null,{title:p(()=>[_("div",pu,[c(m,null,{default:p(()=>[c($(on))]),_:1}),r[78]||(r[78]=_("span",null,"查看JSON格式示例",-1))])]),default:p(()=>[r[79]||(r[79]=_("div",{class:"format-hint-content"},[_("pre",null,`{
  "name": "模板名称",
  "description": "模板描述",
  "dimensions": [
    {
      "name": "维度1",
      "type": "text"
    },
    {
      "name": "维度2",
      "type": "text"
    }
  ]
}`)],-1))]),_:1})]),_:1}),c(D,{modelValue:be.value,"onUpdate:modelValue":r[31]||(r[31]=v=>be.value=v),type:"textarea",rows:10,placeholder:"请输入模板JSON数据",class:"import-input"},null,8,["modelValue"])])]),_:1},8,["modelValue"])],64)}}},Su=Ji(hu,[["__scopeId","data-v-b083c3b5"]]);export{Su as default};
