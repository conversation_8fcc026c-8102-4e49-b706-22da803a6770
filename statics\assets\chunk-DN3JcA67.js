import{aW as Be,bk as Le,c as D,_ as Ie,a as Je,r as E,w as le,o as He,E as m,U as Pe,az as se,b as q,m as w,d as t,g as s,ba as Me,e as a,j as Fe,B as je,C as R,aF as Ae,s as We,F as y,t as Ye,v as _,ab as ae,q as Ge,ak as Xe,X as Y,Y as G,$ as S,al as Ke,aM as Qe,am as Ze,bD as et,eg as tt,af as ot,ad as lt,bd as st,p as I,J as at,aa as rt,bI as nt,V as it,eh as ut,n as re,aB as ne,W as dt,b9 as ct,a_ as ft,k as pt,an as mt,av as J,R as ie}from"./entry-BIjVVog3.js";/* empty css                  *//* empty css                    *//* empty css                *//* empty css                    *//* empty css                *//* empty css                        *//* empty css                    *//* empty css               *//* empty css                 *//* empty css                 *//* empty css                        *//* empty css                  */const _t=Be("novelRules",()=>{const n=Le({rules:{},loaded:!1,loading:!1,error:null}),x=D(()=>n.rules),k=D(()=>n.loaded),U=D(()=>n.loading),N=D(()=>!!n.error),f=D(()=>n.error),z=D(()=>Object.entries(n.rules).map(([c,u])=>({id:c,...u}))),d=D(()=>c=>n.rules[c]||null),H=["qidian","fanqie","feilu","ciweimao","qimao"],P=c=>H.includes(c);async function O(){if(n.loaded&&!n.error)return console.log("小说规则已加载，跳过重复加载"),n.rules;try{n.loading=!0,n.error=null,console.log("开始加载小说规则...");const c=await window.pywebview.api.drssion_controller.get_novel_rules(),u=typeof c=="string"?JSON.parse(c):c;if(u.status==="success"){const g=typeof u.rules=="string"?JSON.parse(u.rules):u.rules;return n.rules=g||{},n.loaded=!0,console.log("小说规则加载成功",n.rules),n.rules}else throw new Error(u.message||"加载规则失败")}catch(c){throw console.error("加载小说规则失败:",c),n.error=c.message,n.loaded=!0,n.rules={},c}finally{n.loading=!1}}async function M(c){try{n.loading=!0,n.error=null;const u=await window.pywebview.api.drssion_controller.save_novel_rule(c),g=typeof u=="string"?JSON.parse(u):u;if(g.status==="success")return n.rules[c.id]={...c},console.log("规则保存成功:",c.id),g;throw new Error(g.message||"保存规则失败")}catch(u){throw console.error("保存规则失败:",u),n.error=u.message,u}finally{n.loading=!1}}async function F(c){try{n.loading=!0,n.error=null;const u=await window.pywebview.api.drssion_controller.delete_novel_rule(c),g=typeof u=="string"?JSON.parse(u):u;if(g.status==="success")return delete n.rules[c],console.log("规则删除成功:",c),g;throw new Error(g.message||"删除规则失败")}catch(u){throw console.error("删除规则失败:",u),n.error=u.message,u}finally{n.loading=!1}}async function j(c){try{n.loading=!0,n.error=null;const u=await window.pywebview.api.drssion_controller.test_novel_rule(c),g=typeof u=="string"?JSON.parse(u):u;if(g.status==="success")return console.log("规则测试成功"),g;throw new Error(g.message||"测试规则失败")}catch(u){throw console.error("测试规则失败:",u),n.error=u.message,u}finally{n.loading=!1}}async function A(){return console.log("强制重新加载小说规则..."),n.loaded=!1,n.rules={},n.error=null,await O()}function W(){n.rules={},n.loaded=!1,n.loading=!1,n.error=null}return{state:n,rules:x,loaded:k,loading:U,hasError:N,error:f,rulesList:z,getRuleById:d,isDefaultRule:P,loadRules:O,saveRule:M,deleteRule:F,testRule:j,reloadRules:A,resetState:W}}),vt={class:"novel-download"},wt={class:"settings-panel"},gt={class:"settings-card"},yt={class:"main-content"},ht={class:"top-section"},bt={class:"url-input-section"},kt={class:"section-header"},Ct={class:"url-input-content"},Et={class:"url-input-footer"},St={class:"url-count"},Vt={class:"url-actions"},Rt={class:"download-settings"},Dt={class:"section-header"},Tt={class:"settings-content"},xt={class:"path-input-group"},Ut={class:"download-params"},zt={class:"form-actions"},$t={class:"chrome-config-section"},qt={class:"section-header"},Nt={class:"chrome-config-list"},Ot={class:"config-name"},Bt={class:"config-path"},Lt={class:"config-tags"},It={class:"task-management"},Jt={class:"section-header"},Ht={class:"header-actions"},Pt={class:"task-list"},Mt=["onClick"],Ft={class:"task-info"},jt={class:"task-title"},At={class:"time"},Wt={class:"rule"},Yt={class:"task-progress"},Gt={class:"task-status"},Xt={class:"task-detail"},Kt={class:"detail-section"},Qt={class:"info-grid"},Zt={class:"info-item"},eo={class:"value"},to={class:"info-item"},oo={class:"value"},lo={class:"info-item"},so={class:"value"},ao={class:"info-item"},ro={class:"value"},no={class:"detail-section"},io={class:"section-title"},uo={class:"log-actions"},co={class:"log-content"},fo={class:"message"},po={key:1,class:"no-logs"},mo={class:"table-container"},_o={class:"dialog-footer"},vo={class:"dialog-footer"},wo={__name:"小说下载",setup(n){window.bridge=window.pywebview.api.drssion_controller;const x=Je(),k=_t(),U=D(()=>k.rules||{}),N=D(()=>x.state.config.chrome.userDataDirs||[]),f=E({url:"",rule:"",chapterCount:30,intervalTime:1,downloadPath:x.state.config.chrome.downloadDir||"",chromeConfigId:x.state.config.chrome.userDataDirs.find(o=>o.isDefault)?.id||""});E([]),E([]),E(!1);const z=E({visible:!1}),d=E({visible:!1,isEdit:!1,form:{id:"",name:"",book_title_rule:"",directory_rule:"",content_rule:"",description_rule:"",chapter_title_rule:"",need_decrypt:!1}}),H=["qidian","fanqie","feilu","ciweimao","qimao"],P=o=>H.includes(o),O=async()=>{await k.loadRules(),z.value.visible=!0},M=()=>{d.value.isEdit=!1,d.value.form={id:crypto.randomUUID(),name:"",book_title_rule:"",directory_rule:"",content_rule:"",description_rule:"",chapter_title_rule:"",need_decrypt:!1},d.value.visible=!0},F=o=>{d.value.isEdit=!0,d.value.form={...o},d.value.visible=!0},j=async o=>{try{await J.confirm("确定要删除该规则吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await k.deleteRule(o.id);e.status==="success"?(m.success("删除成功"),await V()):m.error(e.message||"删除失败")}catch(e){e!=="cancel"&&m.error("删除失败")}},A=async()=>{try{if(!d.value.form.name)throw new Error("规则名不能为空");if(!d.value.form.book_title_rule)throw new Error("书名选择器不能为空");if(!d.value.form.directory_rule)throw new Error("目录选择器不能为空");if(!d.value.form.content_rule)throw new Error("内容选择器不能为空");if(!d.value.form.description_rule)throw new Error("简介选择器不能为空");await k.saveRule(d.value.form),m.success("保存成功"),d.value.visible=!1,await V()}catch(o){m.error(o.message)}},W=async o=>{try{const e=await J.prompt("请输入要测试的URL","测试规则",{inputPattern:/^https?:\/\/.+/,inputErrorMessage:"请输入正确的URL"});m({message:"正在测试规则，请稍候...",type:"info",duration:2e3});const r=await k.testRule({rule:o,url:e.value});await J.alert(JSON.stringify(r,null,2),"测试结果",{closeOnClickModal:!0})}catch(e){e!=="cancel"&&m.error(e.message||"测试失败")}},c=E([]),u={initializing:{type:"info",text:"初始化"},running:{type:"primary",text:"下载中"},completed:{type:"success",text:"已完成"},failed:{type:"danger",text:"失败"},stopped:{type:"warning",text:"已停止"}},g=o=>u[o]?.type||"info",ue=o=>u[o]?.text||o,de=o=>o==="completed"?"success":o==="failed"?"exception":o==="stopped"?"warning":"",X=o=>{if(!o)return"";const e=new Date(o);return`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")} ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`},ce=async()=>{try{const o=N.value.find(p=>p.id===f.value.chromeConfigId);if(!o)throw new Error("请选择Chrome配置");const e=f.value.url.split(`
`).map(p=>p.trim()).filter(p=>p&&p.startsWith("http"));if(e.length===0)throw new Error("请输入有效的小说网址");if(!f.value.rule)throw new Error("请选择规则");Object.keys(U.value).length===0&&await k.loadRules();const r=U.value[f.value.rule];if(!r)throw new Error("选择的规则无效");const i={urls:e,rule:r,chapter_count:f.value.chapterCount||0,interval_time:f.value.intervalTime||3,download_path:f.value.downloadPath,chrome_config:{user_data_dir:`${o.path}/${o.name}`,port:o.port,load_extensions:o.enableExtensions}};m({message:"正在创建任务...",type:"info",duration:2e3});const h=JSON.parse(await window.bridge.create_download_task(i));if(h.status==="success")m.success("任务创建成功"),L.value="tasks",await V();else throw new Error(h.message||"创建任务失败")}catch(o){m.error(o.message)}},fe=async o=>{try{m({message:"正在停止任务...",type:"info",duration:1e3});const e=JSON.parse(await window.bridge.stop_task(o));if(e.status==="success")m.success(e.message||"任务已停止"),await V();else throw new Error(e.message||"停止任务失败")}catch(e){m.error(e.message)}},pe=async o=>{try{const e=c.value.find(i=>i.id===o);if(!e)throw new Error("任务不存在");m({message:"正在重试任务...",type:"info",duration:2e3});const r=JSON.parse(await window.bridge.create_download_task(e.config));if(r.status==="success")m.success(r.message||"任务已重新启动"),await V();else throw new Error(r.message||"重试任务失败")}catch(e){m.error(e.message)}},V=async()=>{try{const o=JSON.parse(await window.bridge.get_tasks());if(o.status==="success"){const e=new Set(c.value.filter(r=>r.showDetail).map(r=>r.id));c.value=o.data.map(r=>({...r,showDetail:e.has(r.id)||!1}))}else throw new Error(o.message||"获取任务列表失败")}catch(o){console.error("刷新任务列表失败:",o),m.error("刷新任务列表失败: "+o.message)}},me=()=>{window.receiveTaskOutput=(o,e)=>{try{const r=JSON.parse(atob(e)),i=c.value.find(h=>h.id===o);i&&(i.outputs||(i.outputs=[]),i.outputs.push(r),r.type==="status"&&(i.status=r.status,i.progress=r.progress||i.progress,i.book_info=r.book_info||i.book_info,i.chapters=r.chapters||i.chapters,i.error=r.error))}catch(r){console.error("处理任务更新失败:",r)}}},$=E(new Map),_e=o=>o.scrollHeight-o.scrollTop-o.clientHeight<20;le(c,o=>{o.forEach(e=>{e.showDetail&&e.outputs?.length&&ie(()=>{const r=document.querySelector(`[data-task-id="${e.id}"]`);if(r){const i=r.querySelector(".log-content");if(i){const h=$.value.get(e.id);(!h||h.autoScroll)&&(i.scrollTop=i.scrollHeight)}}})})},{deep:!0});const B=E(null),ve=()=>{B.value=setInterval(V,3e3)},we=()=>{B.value&&(clearInterval(B.value),B.value=null)};He(async()=>{try{await k.loadRules(),me(),await V(),ve(),m({message:"规则加载成功",type:"info",duration:1e3})}catch{m({message:"规则加载失败",type:"error",duration:3e3})}});const ge=()=>{$.value.forEach((o,e)=>{const r=document.querySelector(`[data-task-id="${e}"]`);if(r){const i=r.querySelector(".log-content");i&&i._scrollHandler&&(i.removeEventListener("scroll",i._scrollHandler),delete i._scrollHandler)}}),$.value.clear()};Pe(()=>{we(),ge()});const ye=o=>{ie(()=>{const e=document.querySelector(`[data-task-id="${o}"]`);if(e){const r=e.querySelector(".log-content");if(r){r.removeEventListener("scroll",r._scrollHandler);const i=()=>{const h=_e(r);$.value.set(o,{autoScroll:h})};r._scrollHandler=i,r.addEventListener("scroll",i),$.value.set(o,{autoScroll:!0}),r.scrollTop=r.scrollHeight}}})},he=o=>{const e=document.querySelector(`[data-task-id="${o}"]`);if(e){const r=e.querySelector(".log-content");r&&(r.scrollTop=r.scrollHeight,$.value.set(o,{autoScroll:!0}))}},be=o=>{c.value.forEach(e=>{e.id!==o.id&&(e.showDetail=!1)}),o.showDetail=!o.showDetail,o.showDetail&&ye(o.id)},ke=async o=>{try{const e=await window.pywebview.api.select_directory(),i=(typeof e=="string"?JSON.parse(e):e)?.data;i&&o==="downloadPath"&&(f.value.downloadPath=i,await x.updateConfigItem("chrome.downloadDir",i))}catch(e){m.error("选择目录失败: "+e.message)}},K=o=>{f.value.chromeConfigId=o.id},Ce=()=>f.value.url?f.value.url.split(`
`).filter(o=>o.trim()).length:0,Ee=()=>{if(!f.value.url)return;const o=f.value.url.split(`
`).map(e=>e.trim()).filter(e=>e).sort().join(`
`);f.value.url=o,m.success("URL格式已整理")},Se=()=>{J.confirm("确定要清空所有URL吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{f.value.url="",m.success("URL已清空")}).catch(()=>{})},L=E("settings");le(L,async o=>{o==="tasks"?await V():o==="settings"&&await k.loadRules()},{immediate:!1});const Ve=async o=>{try{await window.pywebview.api.open_directory(o),m.success("已打开下载目录")}catch(e){m.error("打开目录失败: "+e.message)}};return(o,e)=>{const r=je,i=We,h=se("Sort"),p=Ye,Re=se("Delete"),De=Ke,Te=Xe,C=Ge,Q=Ze,xe=st,b=lt,T=at,Z=ot,ee=Fe,te=Me,Ue=ut,ze=nt,$e=ct,qe=ft,oe=pt,Ne=mt;return w(),q("div",vt,[t($e,{modelValue:L.value,"onUpdate:modelValue":e[7]||(e[7]=l=>L.value=l),class:"download-tabs"},{default:s(()=>[t(te,{label:"下载设置",name:"settings"},{default:s(()=>[a("div",wt,[a("div",gt,[t(ee,{model:f.value,"label-width":"80px",class:"create-task-form"},{default:s(()=>[a("div",yt,[a("div",ht,[a("div",bt,[a("div",kt,[t(r,null,{default:s(()=>[t(R(Ae))]),_:1}),e[18]||(e[18]=a("span",{class:"title"},"批量输入网址",-1))]),a("div",Ct,[t(i,{modelValue:f.value.url,"onUpdate:modelValue":e[0]||(e[0]=l=>f.value.url=l),type:"textarea",rows:8,placeholder:"请输入小说网址，每行一个网址",resize:"none",spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"]),a("div",Et,[a("span",St,"已输入 "+y(Ce())+" 个网址",1),a("div",Vt,[t(p,{type:"primary",link:"",onClick:Ee},{default:s(()=>[t(r,null,{default:s(()=>[t(h)]),_:1}),e[19]||(e[19]=_(" 整理格式 "))]),_:1}),t(p,{type:"danger",link:"",onClick:Se},{default:s(()=>[t(r,null,{default:s(()=>[t(Re)]),_:1}),e[20]||(e[20]=_(" 清空 "))]),_:1})])])])]),a("div",Rt,[a("div",Dt,[t(r,null,{default:s(()=>[t(R(ae))]),_:1}),e[22]||(e[22]=a("span",{class:"title"},"下载设置",-1)),t(p,{type:"primary",link:"",class:"manage-rules-btn",onClick:O},{default:s(()=>[t(r,null,{default:s(()=>[t(R(ae))]),_:1}),e[21]||(e[21]=_(" 规则管理【标签】 "))]),_:1})]),a("div",Tt,[t(C,{label:"规则",required:""},{default:s(()=>[t(Te,{modelValue:f.value.rule,"onUpdate:modelValue":e[1]||(e[1]=l=>f.value.rule=l),placeholder:"请选择规则"},{default:s(()=>[(w(!0),q(Y,null,G(U.value,(l,v)=>(w(),S(De,{key:v,label:l.name,value:v},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(C,{label:"目录",required:""},{default:s(()=>[a("div",xt,[t(i,{modelValue:f.value.downloadPath,"onUpdate:modelValue":e[2]||(e[2]=l=>f.value.downloadPath=l),placeholder:"请选择下载目录",spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"]),t(p,{type:"primary",onClick:e[3]||(e[3]=l=>ke("downloadPath"))},{default:s(()=>[t(r,null,{default:s(()=>[t(R(Qe))]),_:1})]),_:1})])]),_:1}),a("div",Ut,[t(C,{label:"章节数"},{default:s(()=>[t(Q,{modelValue:f.value.chapterCount,"onUpdate:modelValue":e[4]||(e[4]=l=>f.value.chapterCount=l),min:0,step:10,"controls-position":"right",placeholder:"全部"},null,8,["modelValue"])]),_:1}),t(C,{label:"间隔(秒)"},{default:s(()=>[t(Q,{modelValue:f.value.intervalTime,"onUpdate:modelValue":e[5]||(e[5]=l=>f.value.intervalTime=l),min:1,max:60,step:1,"controls-position":"right"},null,8,["modelValue"])]),_:1})])]),a("div",zt,[t(p,{type:"primary",size:"large",onClick:ce},{default:s(()=>[t(r,null,{default:s(()=>[t(R(et))]),_:1}),e[23]||(e[23]=_(" 创建下载任务 "))]),_:1})])])]),a("div",$t,[a("div",qt,[t(r,null,{default:s(()=>[t(R(tt))]),_:1}),e[24]||(e[24]=a("span",{class:"title"},"Chrome配置",-1))]),a("div",Nt,[t(Z,{data:N.value,style:{width:"100%"},"highlight-current-row":"",onCurrentChange:K},{default:s(()=>[t(b,{width:"120"},{default:s(l=>[t(xe,{modelValue:f.value.chromeConfigId,"onUpdate:modelValue":e[6]||(e[6]=v=>f.value.chromeConfigId=v),label:l.row.id,onChange:()=>K(l.row)},{default:s(()=>e[25]||(e[25]=[a("span",{class:"sr-only"},"选择 ",-1)])),_:2},1032,["modelValue","label","onChange"])]),_:1}),t(b,{prop:"name",label:"配置名称","min-width":"120"},{default:s(l=>[a("span",Ot,y(l.row.name),1)]),_:1}),t(b,{prop:"path",label:"路径","min-width":"200","show-overflow-tooltip":""},{default:s(l=>[a("span",Bt,y(l.row.path),1)]),_:1}),t(b,{label:"标签",width:"120",align:"right"},{default:s(l=>[a("div",Lt,[t(T,{size:"small",class:"port-badge"},{default:s(()=>[_(y(l.row.port),1)]),_:2},1024),l.row.enableExtensions?(w(),S(T,{key:0,size:"small",type:"warning"},{default:s(()=>e[26]||(e[26]=[_("扩展")])),_:1})):I("",!0)])]),_:1})]),_:1},8,["data"])])])])]),_:1},8,["model"])])])]),_:1}),t(te,{label:"任务管理",name:"tasks"},{default:s(()=>[a("div",It,[a("div",Jt,[e[28]||(e[28]=a("h2",{class:"section-title"},"任务管理",-1)),a("div",Ht,[t(p,{type:"primary",onClick:V},{default:s(()=>[t(r,null,{default:s(()=>[t(R(rt))]),_:1}),e[27]||(e[27]=_(" 刷新列表 "))]),_:1})])]),a("div",Pt,[(w(!0),q(Y,null,G(c.value,l=>(w(),S(ze,{key:l.id,class:"task-card","body-style":{padding:"0"},"data-task-id":l.id},{default:s(()=>[a("div",{class:"task-header",onClick:v=>be(l)},[a("div",Ft,[a("div",jt,[a("span",At,y(X(l.created_at)),1),a("span",Wt,y(l.config.rule.name),1)]),a("div",Yt,[t(Ue,{percentage:l.progress,status:de(l.status),"stroke-width":8},null,8,["percentage","status"])])]),a("div",Gt,[t(T,{type:g(l.status),size:"small"},{default:s(()=>[_(y(ue(l.status)),1)]),_:2},1032,["type"]),t(r,{class:re(["expand-icon",{"is-active":l.showDetail}])},{default:s(()=>[t(R(ne))]),_:2},1032,["class"])])],8,Mt),it(a("div",Xt,[a("div",Kt,[e[33]||(e[33]=a("div",{class:"section-title"},"任务信息",-1)),a("div",Qt,[a("div",Zt,[e[29]||(e[29]=a("span",{class:"label"},"创建时间：",-1)),a("span",eo,y(X(l.created_at)),1)]),a("div",to,[e[30]||(e[30]=a("span",{class:"label"},"下载规则：",-1)),a("span",oo,y(l.config.rule.name),1)]),a("div",lo,[e[31]||(e[31]=a("span",{class:"label"},"总进度：",-1)),a("span",so,y(l.progress)+"%",1)]),a("div",ao,[e[32]||(e[32]=a("span",{class:"label"},"下载目录：",-1)),a("span",ro,y(l.config.download_path),1)])])]),a("div",no,[a("div",io,[e[38]||(e[38]=a("span",null,"任务日志",-1)),a("div",uo,[t(p,{type:"info",size:"small",onClick:v=>he(l.id),title:"跳转到最新日志"},{default:s(()=>[t(r,null,{default:s(()=>[t(R(ne))]),_:1}),e[34]||(e[34]=_(" 底部 "))]),_:2},1032,["onClick"]),l.status==="running"?(w(),S(p,{key:0,type:"warning",size:"small",onClick:v=>fe(l.id)},{default:s(()=>e[35]||(e[35]=[_(" 停止任务 ")])),_:2},1032,["onClick"])):I("",!0),l.status==="failed"?(w(),S(p,{key:1,type:"primary",size:"small",onClick:v=>pe(l.id)},{default:s(()=>e[36]||(e[36]=[_(" 重试任务 ")])),_:2},1032,["onClick"])):I("",!0),l.status==="completed"?(w(),S(p,{key:2,type:"success",size:"small",onClick:v=>Ve(l.config.download_path)},{default:s(()=>e[37]||(e[37]=[_(" 打开目录 ")])),_:2},1032,["onClick"])):I("",!0)])]),a("div",co,[l.outputs&&l.outputs.length?(w(!0),q(Y,{key:0},G(l.outputs,(v,Oe)=>(w(),q("div",{key:Oe,class:re(["log-line",v.type])},[v.type==="error"?(w(),S(T,{key:0,type:"danger",size:"small",effect:"dark"},{default:s(()=>e[39]||(e[39]=[_(" 错误 ")])),_:1})):v.type==="success"?(w(),S(T,{key:1,type:"success",size:"small",effect:"dark"},{default:s(()=>e[40]||(e[40]=[_(" 成功 ")])),_:1})):v.type==="warning"?(w(),S(T,{key:2,type:"warning",size:"small",effect:"dark"},{default:s(()=>e[41]||(e[41]=[_(" 警告 ")])),_:1})):(w(),S(T,{key:3,type:"info",size:"small",effect:"dark"},{default:s(()=>e[42]||(e[42]=[_(" 信息 ")])),_:1})),a("span",fo,y(v.message),1)],2))),128)):(w(),q("div",po," 暂无日志信息 "))])])],512),[[dt,l.showDetail]])]),_:2},1032,["data-task-id"]))),128))])])]),_:1})]),_:1},8,["modelValue"]),t(oe,{modelValue:z.value.visible,"onUpdate:modelValue":e[9]||(e[9]=l=>z.value.visible=l),title:"规则管理",width:"80%",class:"rule-dialog","close-on-click-modal":!1,"close-on-press-escape":!1,"modal-append-to-body":!1},{footer:s(()=>[a("div",_o,[t(p,{onClick:e[8]||(e[8]=l=>z.value.visible=!1)},{default:s(()=>e[46]||(e[46]=[_("关闭")])),_:1}),t(p,{type:"primary",onClick:M},{default:s(()=>e[47]||(e[47]=[_("添加规则")])),_:1})])]),default:s(()=>[a("div",mo,[t(Z,{data:Object.entries(U.value).map(([l,v])=>({id:l,...v})),style:{width:"100%"},height:"500px"},{default:s(()=>[t(b,{prop:"name",label:"规则名称","min-width":"120"}),t(b,{prop:"book_title_rule",label:"书名选择器","min-width":"150","show-overflow-tooltip":""}),t(b,{prop:"directory_rule",label:"目录选择器","min-width":"150","show-overflow-tooltip":""}),t(b,{prop:"content_rule",label:"内容选择器","min-width":"150","show-overflow-tooltip":""}),t(b,{prop:"description_rule",label:"简介选择器","min-width":"150","show-overflow-tooltip":""}),t(b,{prop:"need_decrypt",label:"要解密",width:"100"},{default:s(l=>[_(y(l.row.need_decrypt?"是":"否"),1)]),_:1}),t(b,{label:"操作",width:"200",fixed:"right"},{default:s(l=>[t(qe,null,{default:s(()=>[t(p,{type:"primary",link:"",onClick:v=>F(l.row)},{default:s(()=>e[43]||(e[43]=[_("编辑")])),_:2},1032,["onClick"]),t(p,{type:"danger",link:"",onClick:v=>j(l.row),disabled:P(l.row.id)},{default:s(()=>e[44]||(e[44]=[_("删除")])),_:2},1032,["onClick","disabled"]),t(p,{type:"success",link:"",onClick:v=>W(l.row)},{default:s(()=>e[45]||(e[45]=[_("测试")])),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])])]),_:1},8,["modelValue"]),t(oe,{modelValue:d.value.visible,"onUpdate:modelValue":e[17]||(e[17]=l=>d.value.visible=l),title:d.value.isEdit?"编辑规则":"添加规则",width:"50%",class:"edit-rule-dialog","close-on-click-modal":!1,"close-on-press-escape":!1,"modal-append-to-body":!1},{footer:s(()=>[a("span",vo,[t(p,{onClick:e[16]||(e[16]=l=>d.value.visible=!1)},{default:s(()=>e[48]||(e[48]=[_("取消")])),_:1}),t(p,{type:"primary",onClick:A},{default:s(()=>e[49]||(e[49]=[_("保存")])),_:1})])]),default:s(()=>[t(ee,{model:d.value.form,"label-width":"120px"},{default:s(()=>[t(C,{label:"规则名称",required:""},{default:s(()=>[t(i,{modelValue:d.value.form.name,"onUpdate:modelValue":e[10]||(e[10]=l=>d.value.form.name=l),spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"])]),_:1}),t(C,{label:"书名选择器",required:""},{default:s(()=>[t(i,{modelValue:d.value.form.book_title_rule,"onUpdate:modelValue":e[11]||(e[11]=l=>d.value.form.book_title_rule=l),spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"])]),_:1}),t(C,{label:"目录选择器",required:""},{default:s(()=>[t(i,{modelValue:d.value.form.directory_rule,"onUpdate:modelValue":e[12]||(e[12]=l=>d.value.form.directory_rule=l),spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"])]),_:1}),t(C,{label:"内容选择器",required:""},{default:s(()=>[t(i,{modelValue:d.value.form.content_rule,"onUpdate:modelValue":e[13]||(e[13]=l=>d.value.form.content_rule=l),spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"])]),_:1}),t(C,{label:"简介选择器",required:""},{default:s(()=>[t(i,{modelValue:d.value.form.description_rule,"onUpdate:modelValue":e[14]||(e[14]=l=>d.value.form.description_rule=l),spellcheck:"false",autocomplete:"off",autocorrect:"off",autocapitalize:"off"},null,8,["modelValue"])]),_:1}),t(C,{label:"需要解密"},{default:s(()=>[t(Ne,{modelValue:d.value.form.need_decrypt,"onUpdate:modelValue":e[15]||(e[15]=l=>d.value.form.need_decrypt=l)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Uo=Ie(wo,[["__scopeId","data-v-5eaa3c52"]]);export{Uo as default};
