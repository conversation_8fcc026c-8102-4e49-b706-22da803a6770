import{O as Fe,bg as Gi,bh as Xt,bi as Xi,w as De,c as be,b as ve,m as re,a1 as Ve,Z as Ge,C as V,n as ut,P as Me,bj as Na,U as Bo,d as X,g as pe,e as G,S as dn,bk as Ca,bl as kn,r as ge,bm as $a,p as Pe,$ as We,bn as Wi,X as Ze,bo as Ta,bp as ze,o as ct,bq as Ma,Y as Ln,R as He,T as Ui,br as Ia,bs as In,F as Qe,bt as Vo,bu as Da,N as Aa,a0 as Ot,v as Xe,aU as Ht,az as Zi,bv as Te,M as en,bw as Pa,_ as Yn,a as za,V as Oa,t as Ki,bx as Fa,by as xt,bz as St,q as Ba,s as qi,an as Va,am as Ha,b7 as <PERSON>,ar as La,bA as Ya,aB as Ga,j as Xa,h as lo,b8 as Wa,W as Ua,K as Ji,E as Ie,av as Pt,l as Za,bB as ni,a_ as Ka,k as qa,i as Ja,bC as Qa,ah as ja,bD as es,B as ts,bE as ns,b6 as os,G as is}from"./entry-BIjVVog3.js";/* empty css                 *//* empty css                 *//* empty css                  *//* empty css                        *//* empty css                *//* empty css                   *//* empty css                        *//* empty css                  */import{n as et}from"./index-browser-OxPLOBIU.js";function Gn(e){return Gi()?(kn(e),!0):!1}function ht(e){return typeof e=="function"?e():V(e)}const rs=typeof window<"u"&&typeof document<"u",as=e=>typeof e<"u",ss=Object.prototype.toString,ls=e=>ss.call(e)==="[object Object]",us=()=>{};function cs(e,t){function n(...i){return new Promise((o,r)=>{Promise.resolve(e(()=>t.apply(this,i),{fn:t,thisArg:this,args:i})).then(o).catch(r)})}return n}const Qi=e=>e();function ds(e=Qi){const t=ge(!0);function n(){t.value=!1}function i(){t.value=!0}const o=(...r)=>{t.value&&e(...r)};return{isActive:Pa(t),pause:n,resume:i,eventFilter:o}}function oi(e,t=!1,n="Timeout"){return new Promise((i,o)=>{setTimeout(t?()=>o(n):i,e)})}function fs(e,t,n={}){const{eventFilter:i=Qi,...o}=n;return De(e,cs(i,t),o)}function zt(e,t,n={}){const{eventFilter:i,...o}=n,{eventFilter:r,pause:a,resume:s,isActive:l}=ds(i);return{stop:fs(e,t,{...o,eventFilter:r}),pause:a,resume:s,isActive:l}}function hs(e,t={}){if(!Vo(e))return Da(e);const n=Array.isArray(e.value)?Array.from({length:e.value.length}):{};for(const i in e.value)n[i]=Aa(()=>({get(){return e.value[i]},set(o){var r;if((r=ht(t.replaceRef))!=null?r:!0)if(Array.isArray(e.value)){const s=[...e.value];s[i]=o,e.value=s}else{const s={...e.value,[i]:o};Object.setPrototypeOf(s,Object.getPrototypeOf(e.value)),e.value=s}else e.value[i]=o}}));return n}function Eo(e,t=!1){function n(d,{flush:g="sync",deep:m=!1,timeout:M,throwOnTimeout:k}={}){let N=null;const I=[new Promise(E=>{N=De(e,$=>{d($)!==t&&(N?.(),E($))},{flush:g,deep:m,immediate:!0})})];return M!=null&&I.push(oi(M,k).then(()=>ht(e)).finally(()=>N?.())),Promise.race(I)}function i(d,g){if(!Vo(d))return n($=>$===d,g);const{flush:m="sync",deep:M=!1,timeout:k,throwOnTimeout:N}=g??{};let T=null;const E=[new Promise($=>{T=De([e,d],([O,te])=>{t!==(O===te)&&(T?.(),$(O))},{flush:m,deep:M,immediate:!0})})];return k!=null&&E.push(oi(k,N).then(()=>ht(e)).finally(()=>(T?.(),ht(e)))),Promise.race(E)}function o(d){return n(g=>!!g,d)}function r(d){return i(null,d)}function a(d){return i(void 0,d)}function s(d){return n(Number.isNaN,d)}function l(d,g){return n(m=>{const M=Array.from(m);return M.includes(d)||M.includes(ht(d))},g)}function u(d){return c(1,d)}function c(d=1,g){let m=-1;return n(()=>(m+=1,m>=d),g)}return Array.isArray(ht(e))?{toMatch:n,toContains:l,changed:u,changedTimes:c,get not(){return Eo(e,!t)}}:{toMatch:n,toBe:i,toBeTruthy:o,toBeNull:r,toBeNaN:s,toBeUndefined:a,changed:u,changedTimes:c,get not(){return Eo(e,!t)}}}function xo(e){return Eo(e)}function ps(e){var t;const n=ht(e);return(t=n?.$el)!=null?t:n}const ji=rs?window:void 0;function er(...e){let t,n,i,o;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,i,o]=e,t=ji):[t,n,i,o]=e,!t)return us;Array.isArray(n)||(n=[n]),Array.isArray(i)||(i=[i]);const r=[],a=()=>{r.forEach(c=>c()),r.length=0},s=(c,d,g,m)=>(c.addEventListener(d,g,m),()=>c.removeEventListener(d,g,m)),l=De(()=>[ps(t),ht(o)],([c,d])=>{if(a(),!c)return;const g=ls(d)?{...d}:d;r.push(...n.flatMap(m=>i.map(M=>s(c,m,M,g))))},{immediate:!0,flush:"post"}),u=()=>{l(),a()};return Gn(u),u}function gs(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function ii(...e){let t,n,i={};e.length===3?(t=e[0],n=e[1],i=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,n=e[0],i=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:o=ji,eventName:r="keydown",passive:a=!1,dedupe:s=!1}=i,l=gs(t);return er(o,r,c=>{c.repeat&&ht(s)||l(c)&&n(c)},a)}function ms(e){return JSON.parse(JSON.stringify(e))}function uo(e,t,n,i={}){var o,r,a;const{clone:s=!1,passive:l=!1,eventName:u,deep:c=!1,defaultValue:d,shouldEmit:g}=i,m=dn(),M=n||m?.emit||((o=m?.$emit)==null?void 0:o.bind(m))||((a=(r=m?.proxy)==null?void 0:r.$emit)==null?void 0:a.bind(m?.proxy));let k=u;t||(t="modelValue"),k=k||`update:${t.toString()}`;const N=E=>s?typeof s=="function"?s(E):ms(E):E,T=()=>as(e[t])?N(e[t]):d,I=E=>{g?g(E)&&M(k,E):M(k,E)};if(l){const E=T(),$=ge(E);let O=!1;return De(()=>e[t],te=>{O||(O=!0,$.value=N(te),He(()=>O=!1))}),De($,te=>{!O&&(te!==e[t]||c)&&I(te)},{deep:c}),$}else return be({get(){return T()},set(E){I(E)}})}var vs={value:()=>{}};function Xn(){for(var e=0,t=arguments.length,n={},i;e<t;++e){if(!(i=arguments[e]+"")||i in n||/[\s.]/.test(i))throw new Error("illegal type: "+i);n[i]=[]}return new Nn(n)}function Nn(e){this._=e}function ys(e,t){return e.trim().split(/^|\s+/).map(function(n){var i="",o=n.indexOf(".");if(o>=0&&(i=n.slice(o+1),n=n.slice(0,o)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:i}})}Nn.prototype=Xn.prototype={constructor:Nn,on:function(e,t){var n=this._,i=ys(e+"",n),o,r=-1,a=i.length;if(arguments.length<2){for(;++r<a;)if((o=(e=i[r]).type)&&(o=ws(n[o],e.name)))return o;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++r<a;)if(o=(e=i[r]).type)n[o]=ri(n[o],e.name,t);else if(t==null)for(o in n)n[o]=ri(n[o],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new Nn(e)},call:function(e,t){if((o=arguments.length-2)>0)for(var n=new Array(o),i=0,o,r;i<o;++i)n[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(r=this._[e],i=0,o=r.length;i<o;++i)r[i].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var i=this._[e],o=0,r=i.length;o<r;++o)i[o].value.apply(t,n)}};function ws(e,t){for(var n=0,i=e.length,o;n<i;++n)if((o=e[n]).name===t)return o.value}function ri(e,t,n){for(var i=0,o=e.length;i<o;++i)if(e[i].name===t){e[i]=vs,e=e.slice(0,i).concat(e.slice(i+1));break}return n!=null&&e.push({name:t,value:n}),e}var So="http://www.w3.org/1999/xhtml";const ai={svg:"http://www.w3.org/2000/svg",xhtml:So,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function Wn(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),ai.hasOwnProperty(t)?{space:ai[t],local:e}:e}function _s(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===So&&t.documentElement.namespaceURI===So?t.createElement(e):t.createElementNS(n,e)}}function bs(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function tr(e){var t=Wn(e);return(t.local?bs:_s)(t)}function Es(){}function Ho(e){return e==null?Es:function(){return this.querySelector(e)}}function xs(e){typeof e!="function"&&(e=Ho(e));for(var t=this._groups,n=t.length,i=new Array(n),o=0;o<n;++o)for(var r=t[o],a=r.length,s=i[o]=new Array(a),l,u,c=0;c<a;++c)(l=r[c])&&(u=e.call(l,l.__data__,c,r))&&("__data__"in l&&(u.__data__=l.__data__),s[c]=u);return new je(i,this._parents)}function Ss(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function ks(){return[]}function nr(e){return e==null?ks:function(){return this.querySelectorAll(e)}}function Ns(e){return function(){return Ss(e.apply(this,arguments))}}function Cs(e){typeof e=="function"?e=Ns(e):e=nr(e);for(var t=this._groups,n=t.length,i=[],o=[],r=0;r<n;++r)for(var a=t[r],s=a.length,l,u=0;u<s;++u)(l=a[u])&&(i.push(e.call(l,l.__data__,u,a)),o.push(l));return new je(i,o)}function or(e){return function(){return this.matches(e)}}function ir(e){return function(t){return t.matches(e)}}var $s=Array.prototype.find;function Ts(e){return function(){return $s.call(this.children,e)}}function Ms(){return this.firstElementChild}function Is(e){return this.select(e==null?Ms:Ts(typeof e=="function"?e:ir(e)))}var Ds=Array.prototype.filter;function As(){return Array.from(this.children)}function Ps(e){return function(){return Ds.call(this.children,e)}}function zs(e){return this.selectAll(e==null?As:Ps(typeof e=="function"?e:ir(e)))}function Os(e){typeof e!="function"&&(e=or(e));for(var t=this._groups,n=t.length,i=new Array(n),o=0;o<n;++o)for(var r=t[o],a=r.length,s=i[o]=[],l,u=0;u<a;++u)(l=r[u])&&e.call(l,l.__data__,u,r)&&s.push(l);return new je(i,this._parents)}function rr(e){return new Array(e.length)}function Fs(){return new je(this._enter||this._groups.map(rr),this._parents)}function Dn(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}Dn.prototype={constructor:Dn,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function Bs(e){return function(){return e}}function Vs(e,t,n,i,o,r){for(var a=0,s,l=t.length,u=r.length;a<u;++a)(s=t[a])?(s.__data__=r[a],i[a]=s):n[a]=new Dn(e,r[a]);for(;a<l;++a)(s=t[a])&&(o[a]=s)}function Hs(e,t,n,i,o,r,a){var s,l,u=new Map,c=t.length,d=r.length,g=new Array(c),m;for(s=0;s<c;++s)(l=t[s])&&(g[s]=m=a.call(l,l.__data__,s,t)+"",u.has(m)?o[s]=l:u.set(m,l));for(s=0;s<d;++s)m=a.call(e,r[s],s,r)+"",(l=u.get(m))?(i[s]=l,l.__data__=r[s],u.delete(m)):n[s]=new Dn(e,r[s]);for(s=0;s<c;++s)(l=t[s])&&u.get(g[s])===l&&(o[s]=l)}function Rs(e){return e.__data__}function Ls(e,t){if(!arguments.length)return Array.from(this,Rs);var n=t?Hs:Vs,i=this._parents,o=this._groups;typeof e!="function"&&(e=Bs(e));for(var r=o.length,a=new Array(r),s=new Array(r),l=new Array(r),u=0;u<r;++u){var c=i[u],d=o[u],g=d.length,m=Ys(e.call(c,c&&c.__data__,u,i)),M=m.length,k=s[u]=new Array(M),N=a[u]=new Array(M),T=l[u]=new Array(g);n(c,d,k,N,T,m,t);for(var I=0,E=0,$,O;I<M;++I)if($=k[I]){for(I>=E&&(E=I+1);!(O=N[E])&&++E<M;);$._next=O||null}}return a=new je(a,i),a._enter=s,a._exit=l,a}function Ys(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Gs(){return new je(this._exit||this._groups.map(rr),this._parents)}function Xs(e,t,n){var i=this.enter(),o=this,r=this.exit();return typeof e=="function"?(i=e(i),i&&(i=i.selection())):i=i.append(e+""),t!=null&&(o=t(o),o&&(o=o.selection())),n==null?r.remove():n(r),i&&o?i.merge(o).order():o}function Ws(e){for(var t=e.selection?e.selection():e,n=this._groups,i=t._groups,o=n.length,r=i.length,a=Math.min(o,r),s=new Array(o),l=0;l<a;++l)for(var u=n[l],c=i[l],d=u.length,g=s[l]=new Array(d),m,M=0;M<d;++M)(m=u[M]||c[M])&&(g[M]=m);for(;l<o;++l)s[l]=n[l];return new je(s,this._parents)}function Us(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var i=e[t],o=i.length-1,r=i[o],a;--o>=0;)(a=i[o])&&(r&&a.compareDocumentPosition(r)^4&&r.parentNode.insertBefore(a,r),r=a);return this}function Zs(e){e||(e=Ks);function t(d,g){return d&&g?e(d.__data__,g.__data__):!d-!g}for(var n=this._groups,i=n.length,o=new Array(i),r=0;r<i;++r){for(var a=n[r],s=a.length,l=o[r]=new Array(s),u,c=0;c<s;++c)(u=a[c])&&(l[c]=u);l.sort(t)}return new je(o,this._parents).order()}function Ks(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function qs(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function Js(){return Array.from(this)}function Qs(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var i=e[t],o=0,r=i.length;o<r;++o){var a=i[o];if(a)return a}return null}function js(){let e=0;for(const t of this)++e;return e}function el(){return!this.node()}function tl(e){for(var t=this._groups,n=0,i=t.length;n<i;++n)for(var o=t[n],r=0,a=o.length,s;r<a;++r)(s=o[r])&&e.call(s,s.__data__,r,o);return this}function nl(e){return function(){this.removeAttribute(e)}}function ol(e){return function(){this.removeAttributeNS(e.space,e.local)}}function il(e,t){return function(){this.setAttribute(e,t)}}function rl(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function al(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function sl(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function ll(e,t){var n=Wn(e);if(arguments.length<2){var i=this.node();return n.local?i.getAttributeNS(n.space,n.local):i.getAttribute(n)}return this.each((t==null?n.local?ol:nl:typeof t=="function"?n.local?sl:al:n.local?rl:il)(n,t))}function ar(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function ul(e){return function(){this.style.removeProperty(e)}}function cl(e,t,n){return function(){this.style.setProperty(e,t,n)}}function dl(e,t,n){return function(){var i=t.apply(this,arguments);i==null?this.style.removeProperty(e):this.style.setProperty(e,i,n)}}function fl(e,t,n){return arguments.length>1?this.each((t==null?ul:typeof t=="function"?dl:cl)(e,t,n??"")):Rt(this.node(),e)}function Rt(e,t){return e.style.getPropertyValue(t)||ar(e).getComputedStyle(e,null).getPropertyValue(t)}function hl(e){return function(){delete this[e]}}function pl(e,t){return function(){this[e]=t}}function gl(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function ml(e,t){return arguments.length>1?this.each((t==null?hl:typeof t=="function"?gl:pl)(e,t)):this.node()[e]}function sr(e){return e.trim().split(/^|\s+/)}function Ro(e){return e.classList||new lr(e)}function lr(e){this._node=e,this._names=sr(e.getAttribute("class")||"")}lr.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function ur(e,t){for(var n=Ro(e),i=-1,o=t.length;++i<o;)n.add(t[i])}function cr(e,t){for(var n=Ro(e),i=-1,o=t.length;++i<o;)n.remove(t[i])}function vl(e){return function(){ur(this,e)}}function yl(e){return function(){cr(this,e)}}function wl(e,t){return function(){(t.apply(this,arguments)?ur:cr)(this,e)}}function _l(e,t){var n=sr(e+"");if(arguments.length<2){for(var i=Ro(this.node()),o=-1,r=n.length;++o<r;)if(!i.contains(n[o]))return!1;return!0}return this.each((typeof t=="function"?wl:t?vl:yl)(n,t))}function bl(){this.textContent=""}function El(e){return function(){this.textContent=e}}function xl(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function Sl(e){return arguments.length?this.each(e==null?bl:(typeof e=="function"?xl:El)(e)):this.node().textContent}function kl(){this.innerHTML=""}function Nl(e){return function(){this.innerHTML=e}}function Cl(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function $l(e){return arguments.length?this.each(e==null?kl:(typeof e=="function"?Cl:Nl)(e)):this.node().innerHTML}function Tl(){this.nextSibling&&this.parentNode.appendChild(this)}function Ml(){return this.each(Tl)}function Il(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Dl(){return this.each(Il)}function Al(e){var t=typeof e=="function"?e:tr(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function Pl(){return null}function zl(e,t){var n=typeof e=="function"?e:tr(e),i=t==null?Pl:typeof t=="function"?t:Ho(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),i.apply(this,arguments)||null)})}function Ol(){var e=this.parentNode;e&&e.removeChild(this)}function Fl(){return this.each(Ol)}function Bl(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Vl(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Hl(e){return this.select(e?Vl:Bl)}function Rl(e){return arguments.length?this.property("__data__",e):this.node().__data__}function Ll(e){return function(t){e.call(this,t,this.__data__)}}function Yl(e){return e.trim().split(/^|\s+/).map(function(t){var n="",i=t.indexOf(".");return i>=0&&(n=t.slice(i+1),t=t.slice(0,i)),{type:t,name:n}})}function Gl(e){return function(){var t=this.__on;if(t){for(var n=0,i=-1,o=t.length,r;n<o;++n)r=t[n],(!e.type||r.type===e.type)&&r.name===e.name?this.removeEventListener(r.type,r.listener,r.options):t[++i]=r;++i?t.length=i:delete this.__on}}}function Xl(e,t,n){return function(){var i=this.__on,o,r=Ll(t);if(i){for(var a=0,s=i.length;a<s;++a)if((o=i[a]).type===e.type&&o.name===e.name){this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=r,o.options=n),o.value=t;return}}this.addEventListener(e.type,r,n),o={type:e.type,name:e.name,value:t,listener:r,options:n},i?i.push(o):this.__on=[o]}}function Wl(e,t,n){var i=Yl(e+""),o,r=i.length,a;if(arguments.length<2){var s=this.node().__on;if(s){for(var l=0,u=s.length,c;l<u;++l)for(o=0,c=s[l];o<r;++o)if((a=i[o]).type===c.type&&a.name===c.name)return c.value}return}for(s=t?Xl:Gl,o=0;o<r;++o)this.each(s(i[o],t,n));return this}function dr(e,t,n){var i=ar(e),o=i.CustomEvent;typeof o=="function"?o=new o(t,n):(o=i.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}function Ul(e,t){return function(){return dr(this,e,t)}}function Zl(e,t){return function(){return dr(this,e,t.apply(this,arguments))}}function Kl(e,t){return this.each((typeof t=="function"?Zl:Ul)(e,t))}function*ql(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var i=e[t],o=0,r=i.length,a;o<r;++o)(a=i[o])&&(yield a)}var fr=[null];function je(e,t){this._groups=e,this._parents=t}function fn(){return new je([[document.documentElement]],fr)}function Jl(){return this}je.prototype=fn.prototype={constructor:je,select:xs,selectAll:Cs,selectChild:Is,selectChildren:zs,filter:Os,data:Ls,enter:Fs,exit:Gs,join:Xs,merge:Ws,selection:Jl,order:Us,sort:Zs,call:qs,nodes:Js,node:Qs,size:js,empty:el,each:tl,attr:ll,style:fl,property:ml,classed:_l,text:Sl,html:$l,raise:Ml,lower:Dl,append:Al,insert:zl,remove:Fl,clone:Hl,datum:Rl,on:Wl,dispatch:Kl,[Symbol.iterator]:ql};function tt(e){return typeof e=="string"?new je([[document.querySelector(e)]],[document.documentElement]):new je([[e]],fr)}function Ql(e){let t;for(;t=e.sourceEvent;)e=t;return e}function rt(e,t){if(e=Ql(e),t===void 0&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var i=n.createSVGPoint();return i.x=e.clientX,i.y=e.clientY,i=i.matrixTransform(t.getScreenCTM().inverse()),[i.x,i.y]}if(t.getBoundingClientRect){var o=t.getBoundingClientRect();return[e.clientX-o.left-t.clientLeft,e.clientY-o.top-t.clientTop]}}return[e.pageX,e.pageY]}const jl={passive:!1},rn={capture:!0,passive:!1};function co(e){e.stopImmediatePropagation()}function Ft(e){e.preventDefault(),e.stopImmediatePropagation()}function hr(e){var t=e.document.documentElement,n=tt(e).on("dragstart.drag",Ft,rn);"onselectstart"in t?n.on("selectstart.drag",Ft,rn):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function pr(e,t){var n=e.document.documentElement,i=tt(e).on("dragstart.drag",null);t&&(i.on("click.drag",Ft,rn),setTimeout(function(){i.on("click.drag",null)},0)),"onselectstart"in n?i.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}const mn=e=>()=>e;function ko(e,{sourceEvent:t,subject:n,target:i,identifier:o,active:r,x:a,y:s,dx:l,dy:u,dispatch:c}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:i,enumerable:!0,configurable:!0},identifier:{value:o,enumerable:!0,configurable:!0},active:{value:r,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:s,enumerable:!0,configurable:!0},dx:{value:l,enumerable:!0,configurable:!0},dy:{value:u,enumerable:!0,configurable:!0},_:{value:c}})}ko.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};function eu(e){return!e.ctrlKey&&!e.button}function tu(){return this.parentNode}function nu(e,t){return t??{x:e.x,y:e.y}}function ou(){return navigator.maxTouchPoints||"ontouchstart"in this}function iu(){var e=eu,t=tu,n=nu,i=ou,o={},r=Xn("start","drag","end"),a=0,s,l,u,c,d=0;function g($){$.on("mousedown.drag",m).filter(i).on("touchstart.drag",N).on("touchmove.drag",T,jl).on("touchend.drag touchcancel.drag",I).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function m($,O){if(!(c||!e.call(this,$,O))){var te=E(this,t.call(this,$,O),$,O,"mouse");te&&(tt($.view).on("mousemove.drag",M,rn).on("mouseup.drag",k,rn),hr($.view),co($),u=!1,s=$.clientX,l=$.clientY,te("start",$))}}function M($){if(Ft($),!u){var O=$.clientX-s,te=$.clientY-l;u=O*O+te*te>d}o.mouse("drag",$)}function k($){tt($.view).on("mousemove.drag mouseup.drag",null),pr($.view,u),Ft($),o.mouse("end",$)}function N($,O){if(e.call(this,$,O)){var te=$.changedTouches,P=t.call(this,$,O),D=te.length,W,F;for(W=0;W<D;++W)(F=E(this,P,$,O,te[W].identifier,te[W]))&&(co($),F("start",$,te[W]))}}function T($){var O=$.changedTouches,te=O.length,P,D;for(P=0;P<te;++P)(D=o[O[P].identifier])&&(Ft($),D("drag",$,O[P]))}function I($){var O=$.changedTouches,te=O.length,P,D;for(c&&clearTimeout(c),c=setTimeout(function(){c=null},500),P=0;P<te;++P)(D=o[O[P].identifier])&&(co($),D("end",$,O[P]))}function E($,O,te,P,D,W){var F=r.copy(),R=rt(W||te,O),x,K,w;if((w=n.call($,new ko("beforestart",{sourceEvent:te,target:g,identifier:D,active:a,x:R[0],y:R[1],dx:0,dy:0,dispatch:F}),P))!=null)return x=w.x-R[0]||0,K=w.y-R[1]||0,function A(S,H,L){var J=R,Y;switch(S){case"start":o[D]=A,Y=a++;break;case"end":delete o[D],--a;case"drag":R=rt(L||H,O),Y=a;break}F.call(S,$,new ko(S,{sourceEvent:H,subject:w,target:g,identifier:D,active:Y,x:R[0]+x,y:R[1]+K,dx:R[0]-J[0],dy:R[1]-J[1],dispatch:F}),P)}}return g.filter=function($){return arguments.length?(e=typeof $=="function"?$:mn(!!$),g):e},g.container=function($){return arguments.length?(t=typeof $=="function"?$:mn($),g):t},g.subject=function($){return arguments.length?(n=typeof $=="function"?$:mn($),g):n},g.touchable=function($){return arguments.length?(i=typeof $=="function"?$:mn(!!$),g):i},g.on=function(){var $=r.on.apply(r,arguments);return $===r?g:$},g.clickDistance=function($){return arguments.length?(d=($=+$)*$,g):Math.sqrt(d)},g}function Lo(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function gr(e,t){var n=Object.create(e.prototype);for(var i in t)n[i]=t[i];return n}function hn(){}var an=.7,An=1/an,Bt="\\s*([+-]?\\d+)\\s*",sn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",lt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",ru=/^#([0-9a-f]{3,8})$/,au=new RegExp(`^rgb\\(${Bt},${Bt},${Bt}\\)$`),su=new RegExp(`^rgb\\(${lt},${lt},${lt}\\)$`),lu=new RegExp(`^rgba\\(${Bt},${Bt},${Bt},${sn}\\)$`),uu=new RegExp(`^rgba\\(${lt},${lt},${lt},${sn}\\)$`),cu=new RegExp(`^hsl\\(${sn},${lt},${lt}\\)$`),du=new RegExp(`^hsla\\(${sn},${lt},${lt},${sn}\\)$`),si={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Lo(hn,Mt,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:li,formatHex:li,formatHex8:fu,formatHsl:hu,formatRgb:ui,toString:ui});function li(){return this.rgb().formatHex()}function fu(){return this.rgb().formatHex8()}function hu(){return mr(this).formatHsl()}function ui(){return this.rgb().formatRgb()}function Mt(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=ru.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?ci(t):n===3?new qe(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?vn(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?vn(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=au.exec(e))?new qe(t[1],t[2],t[3],1):(t=su.exec(e))?new qe(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=lu.exec(e))?vn(t[1],t[2],t[3],t[4]):(t=uu.exec(e))?vn(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=cu.exec(e))?hi(t[1],t[2]/100,t[3]/100,1):(t=du.exec(e))?hi(t[1],t[2]/100,t[3]/100,t[4]):si.hasOwnProperty(e)?ci(si[e]):e==="transparent"?new qe(NaN,NaN,NaN,0):null}function ci(e){return new qe(e>>16&255,e>>8&255,e&255,1)}function vn(e,t,n,i){return i<=0&&(e=t=n=NaN),new qe(e,t,n,i)}function pu(e){return e instanceof hn||(e=Mt(e)),e?(e=e.rgb(),new qe(e.r,e.g,e.b,e.opacity)):new qe}function No(e,t,n,i){return arguments.length===1?pu(e):new qe(e,t,n,i??1)}function qe(e,t,n,i){this.r=+e,this.g=+t,this.b=+n,this.opacity=+i}Lo(qe,No,gr(hn,{brighter(e){return e=e==null?An:Math.pow(An,e),new qe(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?an:Math.pow(an,e),new qe(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new qe($t(this.r),$t(this.g),$t(this.b),Pn(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:di,formatHex:di,formatHex8:gu,formatRgb:fi,toString:fi}));function di(){return`#${Ct(this.r)}${Ct(this.g)}${Ct(this.b)}`}function gu(){return`#${Ct(this.r)}${Ct(this.g)}${Ct(this.b)}${Ct((isNaN(this.opacity)?1:this.opacity)*255)}`}function fi(){const e=Pn(this.opacity);return`${e===1?"rgb(":"rgba("}${$t(this.r)}, ${$t(this.g)}, ${$t(this.b)}${e===1?")":`, ${e})`}`}function Pn(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function $t(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Ct(e){return e=$t(e),(e<16?"0":"")+e.toString(16)}function hi(e,t,n,i){return i<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new nt(e,t,n,i)}function mr(e){if(e instanceof nt)return new nt(e.h,e.s,e.l,e.opacity);if(e instanceof hn||(e=Mt(e)),!e)return new nt;if(e instanceof nt)return e;e=e.rgb();var t=e.r/255,n=e.g/255,i=e.b/255,o=Math.min(t,n,i),r=Math.max(t,n,i),a=NaN,s=r-o,l=(r+o)/2;return s?(t===r?a=(n-i)/s+(n<i)*6:n===r?a=(i-t)/s+2:a=(t-n)/s+4,s/=l<.5?r+o:2-r-o,a*=60):s=l>0&&l<1?0:a,new nt(a,s,l,e.opacity)}function mu(e,t,n,i){return arguments.length===1?mr(e):new nt(e,t,n,i??1)}function nt(e,t,n,i){this.h=+e,this.s=+t,this.l=+n,this.opacity=+i}Lo(nt,mu,gr(hn,{brighter(e){return e=e==null?An:Math.pow(An,e),new nt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?an:Math.pow(an,e),new nt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,i=n+(n<.5?n:1-n)*t,o=2*n-i;return new qe(fo(e>=240?e-240:e+120,o,i),fo(e,o,i),fo(e<120?e+240:e-120,o,i),this.opacity)},clamp(){return new nt(pi(this.h),yn(this.s),yn(this.l),Pn(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Pn(this.opacity);return`${e===1?"hsl(":"hsla("}${pi(this.h)}, ${yn(this.s)*100}%, ${yn(this.l)*100}%${e===1?")":`, ${e})`}`}}));function pi(e){return e=(e||0)%360,e<0?e+360:e}function yn(e){return Math.max(0,Math.min(1,e||0))}function fo(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const Yo=e=>()=>e;function vu(e,t){return function(n){return e+n*t}}function yu(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(i){return Math.pow(e+i*t,n)}}function wu(e){return(e=+e)==1?vr:function(t,n){return n-t?yu(t,n,e):Yo(isNaN(t)?n:t)}}function vr(e,t){var n=t-e;return n?vu(e,n):Yo(isNaN(e)?t:e)}const zn=function e(t){var n=wu(t);function i(o,r){var a=n((o=No(o)).r,(r=No(r)).r),s=n(o.g,r.g),l=n(o.b,r.b),u=vr(o.opacity,r.opacity);return function(c){return o.r=a(c),o.g=s(c),o.b=l(c),o.opacity=u(c),o+""}}return i.gamma=e,i}(1);function _u(e,t){t||(t=[]);var n=e?Math.min(t.length,e.length):0,i=t.slice(),o;return function(r){for(o=0;o<n;++o)i[o]=e[o]*(1-r)+t[o]*r;return i}}function bu(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function Eu(e,t){var n=t?t.length:0,i=e?Math.min(n,e.length):0,o=new Array(i),r=new Array(n),a;for(a=0;a<i;++a)o[a]=tn(e[a],t[a]);for(;a<n;++a)r[a]=t[a];return function(s){for(a=0;a<i;++a)r[a]=o[a](s);return r}}function xu(e,t){var n=new Date;return e=+e,t=+t,function(i){return n.setTime(e*(1-i)+t*i),n}}function at(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}function Su(e,t){var n={},i={},o;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(o in t)o in e?n[o]=tn(e[o],t[o]):i[o]=t[o];return function(r){for(o in n)i[o]=n[o](r);return i}}var Co=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ho=new RegExp(Co.source,"g");function ku(e){return function(){return e}}function Nu(e){return function(t){return e(t)+""}}function yr(e,t){var n=Co.lastIndex=ho.lastIndex=0,i,o,r,a=-1,s=[],l=[];for(e=e+"",t=t+"";(i=Co.exec(e))&&(o=ho.exec(t));)(r=o.index)>n&&(r=t.slice(n,r),s[a]?s[a]+=r:s[++a]=r),(i=i[0])===(o=o[0])?s[a]?s[a]+=o:s[++a]=o:(s[++a]=null,l.push({i:a,x:at(i,o)})),n=ho.lastIndex;return n<t.length&&(r=t.slice(n),s[a]?s[a]+=r:s[++a]=r),s.length<2?l[0]?Nu(l[0].x):ku(t):(t=l.length,function(u){for(var c=0,d;c<t;++c)s[(d=l[c]).i]=d.x(u);return s.join("")})}function tn(e,t){var n=typeof t,i;return t==null||n==="boolean"?Yo(t):(n==="number"?at:n==="string"?(i=Mt(t))?(t=i,zn):yr:t instanceof Mt?zn:t instanceof Date?xu:bu(t)?_u:Array.isArray(t)?Eu:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?Su:at)(e,t)}var gi=180/Math.PI,$o={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function wr(e,t,n,i,o,r){var a,s,l;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(l=e*n+t*i)&&(n-=e*l,i-=t*l),(s=Math.sqrt(n*n+i*i))&&(n/=s,i/=s,l/=s),e*i<t*n&&(e=-e,t=-t,l=-l,a=-a),{translateX:o,translateY:r,rotate:Math.atan2(t,e)*gi,skewX:Math.atan(l)*gi,scaleX:a,scaleY:s}}var wn;function Cu(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?$o:wr(t.a,t.b,t.c,t.d,t.e,t.f)}function $u(e){return e==null||(wn||(wn=document.createElementNS("http://www.w3.org/2000/svg","g")),wn.setAttribute("transform",e),!(e=wn.transform.baseVal.consolidate()))?$o:(e=e.matrix,wr(e.a,e.b,e.c,e.d,e.e,e.f))}function _r(e,t,n,i){function o(u){return u.length?u.pop()+" ":""}function r(u,c,d,g,m,M){if(u!==d||c!==g){var k=m.push("translate(",null,t,null,n);M.push({i:k-4,x:at(u,d)},{i:k-2,x:at(c,g)})}else(d||g)&&m.push("translate("+d+t+g+n)}function a(u,c,d,g){u!==c?(u-c>180?c+=360:c-u>180&&(u+=360),g.push({i:d.push(o(d)+"rotate(",null,i)-2,x:at(u,c)})):c&&d.push(o(d)+"rotate("+c+i)}function s(u,c,d,g){u!==c?g.push({i:d.push(o(d)+"skewX(",null,i)-2,x:at(u,c)}):c&&d.push(o(d)+"skewX("+c+i)}function l(u,c,d,g,m,M){if(u!==d||c!==g){var k=m.push(o(m)+"scale(",null,",",null,")");M.push({i:k-4,x:at(u,d)},{i:k-2,x:at(c,g)})}else(d!==1||g!==1)&&m.push(o(m)+"scale("+d+","+g+")")}return function(u,c){var d=[],g=[];return u=e(u),c=e(c),r(u.translateX,u.translateY,c.translateX,c.translateY,d,g),a(u.rotate,c.rotate,d,g),s(u.skewX,c.skewX,d,g),l(u.scaleX,u.scaleY,c.scaleX,c.scaleY,d,g),u=c=null,function(m){for(var M=-1,k=g.length,N;++M<k;)d[(N=g[M]).i]=N.x(m);return d.join("")}}}var Tu=_r(Cu,"px, ","px)","deg)"),Mu=_r($u,", ",")",")"),Iu=1e-12;function mi(e){return((e=Math.exp(e))+1/e)/2}function Du(e){return((e=Math.exp(e))-1/e)/2}function Au(e){return((e=Math.exp(2*e))-1)/(e+1)}const Cn=function e(t,n,i){function o(r,a){var s=r[0],l=r[1],u=r[2],c=a[0],d=a[1],g=a[2],m=c-s,M=d-l,k=m*m+M*M,N,T;if(k<Iu)T=Math.log(g/u)/t,N=function(P){return[s+P*m,l+P*M,u*Math.exp(t*P*T)]};else{var I=Math.sqrt(k),E=(g*g-u*u+i*k)/(2*u*n*I),$=(g*g-u*u-i*k)/(2*g*n*I),O=Math.log(Math.sqrt(E*E+1)-E),te=Math.log(Math.sqrt($*$+1)-$);T=(te-O)/t,N=function(P){var D=P*T,W=mi(O),F=u/(n*I)*(W*Au(t*D+O)-Du(O));return[s+F*m,l+F*M,u*W/mi(t*D+O)]}}return N.duration=T*1e3*t/Math.SQRT2,N}return o.rho=function(r){var a=Math.max(.001,+r),s=a*a,l=s*s;return e(a,s,l)},o}(Math.SQRT2,2,4);var Lt=0,Jt=0,Kt=0,br=1e3,On,Qt,Fn=0,It=0,Un=0,ln=typeof performance=="object"&&performance.now?performance:Date,Er=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function Go(){return It||(Er(Pu),It=ln.now()+Un)}function Pu(){It=0}function Bn(){this._call=this._time=this._next=null}Bn.prototype=xr.prototype={constructor:Bn,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?Go():+n)+(t==null?0:+t),!this._next&&Qt!==this&&(Qt?Qt._next=this:On=this,Qt=this),this._call=e,this._time=n,To()},stop:function(){this._call&&(this._call=null,this._time=1/0,To())}};function xr(e,t,n){var i=new Bn;return i.restart(e,t,n),i}function zu(){Go(),++Lt;for(var e=On,t;e;)(t=It-e._time)>=0&&e._call.call(void 0,t),e=e._next;--Lt}function vi(){It=(Fn=ln.now())+Un,Lt=Jt=0;try{zu()}finally{Lt=0,Fu(),It=0}}function Ou(){var e=ln.now(),t=e-Fn;t>br&&(Un-=t,Fn=e)}function Fu(){for(var e,t=On,n,i=1/0;t;)t._call?(i>t._time&&(i=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:On=n);Qt=e,To(i)}function To(e){if(!Lt){Jt&&(Jt=clearTimeout(Jt));var t=e-It;t>24?(e<1/0&&(Jt=setTimeout(vi,e-ln.now()-Un)),Kt&&(Kt=clearInterval(Kt))):(Kt||(Fn=ln.now(),Kt=setInterval(Ou,br)),Lt=1,Er(vi))}}function yi(e,t,n){var i=new Bn;return t=t==null?0:+t,i.restart(o=>{i.stop(),e(o+t)},t,n),i}var Bu=Xn("start","end","cancel","interrupt"),Vu=[],Sr=0,wi=1,Mo=2,$n=3,_i=4,Io=5,Tn=6;function Zn(e,t,n,i,o,r){var a=e.__transition;if(!a)e.__transition={};else if(n in a)return;Hu(e,n,{name:t,index:i,group:o,on:Bu,tween:Vu,time:r.time,delay:r.delay,duration:r.duration,ease:r.ease,timer:null,state:Sr})}function Xo(e,t){var n=ot(e,t);if(n.state>Sr)throw new Error("too late; already scheduled");return n}function dt(e,t){var n=ot(e,t);if(n.state>$n)throw new Error("too late; already running");return n}function ot(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Hu(e,t,n){var i=e.__transition,o;i[t]=n,n.timer=xr(r,0,n.time);function r(u){n.state=wi,n.timer.restart(a,n.delay,n.time),n.delay<=u&&a(u-n.delay)}function a(u){var c,d,g,m;if(n.state!==wi)return l();for(c in i)if(m=i[c],m.name===n.name){if(m.state===$n)return yi(a);m.state===_i?(m.state=Tn,m.timer.stop(),m.on.call("interrupt",e,e.__data__,m.index,m.group),delete i[c]):+c<t&&(m.state=Tn,m.timer.stop(),m.on.call("cancel",e,e.__data__,m.index,m.group),delete i[c])}if(yi(function(){n.state===$n&&(n.state=_i,n.timer.restart(s,n.delay,n.time),s(u))}),n.state=Mo,n.on.call("start",e,e.__data__,n.index,n.group),n.state===Mo){for(n.state=$n,o=new Array(g=n.tween.length),c=0,d=-1;c<g;++c)(m=n.tween[c].value.call(e,e.__data__,n.index,n.group))&&(o[++d]=m);o.length=d+1}}function s(u){for(var c=u<n.duration?n.ease.call(null,u/n.duration):(n.timer.restart(l),n.state=Io,1),d=-1,g=o.length;++d<g;)o[d].call(e,c);n.state===Io&&(n.on.call("end",e,e.__data__,n.index,n.group),l())}function l(){n.state=Tn,n.timer.stop(),delete i[t];for(var u in i)return;delete e.__transition}}function Mn(e,t){var n=e.__transition,i,o,r=!0,a;if(n){t=t==null?null:t+"";for(a in n){if((i=n[a]).name!==t){r=!1;continue}o=i.state>Mo&&i.state<Io,i.state=Tn,i.timer.stop(),i.on.call(o?"interrupt":"cancel",e,e.__data__,i.index,i.group),delete n[a]}r&&delete e.__transition}}function Ru(e){return this.each(function(){Mn(this,e)})}function Lu(e,t){var n,i;return function(){var o=dt(this,e),r=o.tween;if(r!==n){i=n=r;for(var a=0,s=i.length;a<s;++a)if(i[a].name===t){i=i.slice(),i.splice(a,1);break}}o.tween=i}}function Yu(e,t,n){var i,o;if(typeof n!="function")throw new Error;return function(){var r=dt(this,e),a=r.tween;if(a!==i){o=(i=a).slice();for(var s={name:t,value:n},l=0,u=o.length;l<u;++l)if(o[l].name===t){o[l]=s;break}l===u&&o.push(s)}r.tween=o}}function Gu(e,t){var n=this._id;if(e+="",arguments.length<2){for(var i=ot(this.node(),n).tween,o=0,r=i.length,a;o<r;++o)if((a=i[o]).name===e)return a.value;return null}return this.each((t==null?Lu:Yu)(n,e,t))}function Wo(e,t,n){var i=e._id;return e.each(function(){var o=dt(this,i);(o.value||(o.value={}))[t]=n.apply(this,arguments)}),function(o){return ot(o,i).value[t]}}function kr(e,t){var n;return(typeof t=="number"?at:t instanceof Mt?zn:(n=Mt(t))?(t=n,zn):yr)(e,t)}function Xu(e){return function(){this.removeAttribute(e)}}function Wu(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Uu(e,t,n){var i,o=n+"",r;return function(){var a=this.getAttribute(e);return a===o?null:a===i?r:r=t(i=a,n)}}function Zu(e,t,n){var i,o=n+"",r;return function(){var a=this.getAttributeNS(e.space,e.local);return a===o?null:a===i?r:r=t(i=a,n)}}function Ku(e,t,n){var i,o,r;return function(){var a,s=n(this),l;return s==null?void this.removeAttribute(e):(a=this.getAttribute(e),l=s+"",a===l?null:a===i&&l===o?r:(o=l,r=t(i=a,s)))}}function qu(e,t,n){var i,o,r;return function(){var a,s=n(this),l;return s==null?void this.removeAttributeNS(e.space,e.local):(a=this.getAttributeNS(e.space,e.local),l=s+"",a===l?null:a===i&&l===o?r:(o=l,r=t(i=a,s)))}}function Ju(e,t){var n=Wn(e),i=n==="transform"?Mu:kr;return this.attrTween(e,typeof t=="function"?(n.local?qu:Ku)(n,i,Wo(this,"attr."+e,t)):t==null?(n.local?Wu:Xu)(n):(n.local?Zu:Uu)(n,i,t))}function Qu(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function ju(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function ec(e,t){var n,i;function o(){var r=t.apply(this,arguments);return r!==i&&(n=(i=r)&&ju(e,r)),n}return o._value=t,o}function tc(e,t){var n,i;function o(){var r=t.apply(this,arguments);return r!==i&&(n=(i=r)&&Qu(e,r)),n}return o._value=t,o}function nc(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var i=Wn(e);return this.tween(n,(i.local?ec:tc)(i,t))}function oc(e,t){return function(){Xo(this,e).delay=+t.apply(this,arguments)}}function ic(e,t){return t=+t,function(){Xo(this,e).delay=t}}function rc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?oc:ic)(t,e)):ot(this.node(),t).delay}function ac(e,t){return function(){dt(this,e).duration=+t.apply(this,arguments)}}function sc(e,t){return t=+t,function(){dt(this,e).duration=t}}function lc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?ac:sc)(t,e)):ot(this.node(),t).duration}function uc(e,t){if(typeof t!="function")throw new Error;return function(){dt(this,e).ease=t}}function cc(e){var t=this._id;return arguments.length?this.each(uc(t,e)):ot(this.node(),t).ease}function dc(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;dt(this,e).ease=n}}function fc(e){if(typeof e!="function")throw new Error;return this.each(dc(this._id,e))}function hc(e){typeof e!="function"&&(e=or(e));for(var t=this._groups,n=t.length,i=new Array(n),o=0;o<n;++o)for(var r=t[o],a=r.length,s=i[o]=[],l,u=0;u<a;++u)(l=r[u])&&e.call(l,l.__data__,u,r)&&s.push(l);return new mt(i,this._parents,this._name,this._id)}function pc(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,i=t.length,o=n.length,r=Math.min(i,o),a=new Array(i),s=0;s<r;++s)for(var l=t[s],u=n[s],c=l.length,d=a[s]=new Array(c),g,m=0;m<c;++m)(g=l[m]||u[m])&&(d[m]=g);for(;s<i;++s)a[s]=t[s];return new mt(a,this._parents,this._name,this._id)}function gc(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function mc(e,t,n){var i,o,r=gc(t)?Xo:dt;return function(){var a=r(this,e),s=a.on;s!==i&&(o=(i=s).copy()).on(t,n),a.on=o}}function vc(e,t){var n=this._id;return arguments.length<2?ot(this.node(),n).on.on(e):this.each(mc(n,e,t))}function yc(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function wc(){return this.on("end.remove",yc(this._id))}function _c(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Ho(e));for(var i=this._groups,o=i.length,r=new Array(o),a=0;a<o;++a)for(var s=i[a],l=s.length,u=r[a]=new Array(l),c,d,g=0;g<l;++g)(c=s[g])&&(d=e.call(c,c.__data__,g,s))&&("__data__"in c&&(d.__data__=c.__data__),u[g]=d,Zn(u[g],t,n,g,u,ot(c,n)));return new mt(r,this._parents,t,n)}function bc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=nr(e));for(var i=this._groups,o=i.length,r=[],a=[],s=0;s<o;++s)for(var l=i[s],u=l.length,c,d=0;d<u;++d)if(c=l[d]){for(var g=e.call(c,c.__data__,d,l),m,M=ot(c,n),k=0,N=g.length;k<N;++k)(m=g[k])&&Zn(m,t,n,k,g,M);r.push(g),a.push(c)}return new mt(r,a,t,n)}var Ec=fn.prototype.constructor;function xc(){return new Ec(this._groups,this._parents)}function Sc(e,t){var n,i,o;return function(){var r=Rt(this,e),a=(this.style.removeProperty(e),Rt(this,e));return r===a?null:r===n&&a===i?o:o=t(n=r,i=a)}}function Nr(e){return function(){this.style.removeProperty(e)}}function kc(e,t,n){var i,o=n+"",r;return function(){var a=Rt(this,e);return a===o?null:a===i?r:r=t(i=a,n)}}function Nc(e,t,n){var i,o,r;return function(){var a=Rt(this,e),s=n(this),l=s+"";return s==null&&(l=s=(this.style.removeProperty(e),Rt(this,e))),a===l?null:a===i&&l===o?r:(o=l,r=t(i=a,s))}}function Cc(e,t){var n,i,o,r="style."+t,a="end."+r,s;return function(){var l=dt(this,e),u=l.on,c=l.value[r]==null?s||(s=Nr(t)):void 0;(u!==n||o!==c)&&(i=(n=u).copy()).on(a,o=c),l.on=i}}function $c(e,t,n){var i=(e+="")=="transform"?Tu:kr;return t==null?this.styleTween(e,Sc(e,i)).on("end.style."+e,Nr(e)):typeof t=="function"?this.styleTween(e,Nc(e,i,Wo(this,"style."+e,t))).each(Cc(this._id,e)):this.styleTween(e,kc(e,i,t),n).on("end.style."+e,null)}function Tc(e,t,n){return function(i){this.style.setProperty(e,t.call(this,i),n)}}function Mc(e,t,n){var i,o;function r(){var a=t.apply(this,arguments);return a!==o&&(i=(o=a)&&Tc(e,a,n)),i}return r._value=t,r}function Ic(e,t,n){var i="style."+(e+="");if(arguments.length<2)return(i=this.tween(i))&&i._value;if(t==null)return this.tween(i,null);if(typeof t!="function")throw new Error;return this.tween(i,Mc(e,t,n??""))}function Dc(e){return function(){this.textContent=e}}function Ac(e){return function(){var t=e(this);this.textContent=t??""}}function Pc(e){return this.tween("text",typeof e=="function"?Ac(Wo(this,"text",e)):Dc(e==null?"":e+""))}function zc(e){return function(t){this.textContent=e.call(this,t)}}function Oc(e){var t,n;function i(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&zc(o)),t}return i._value=e,i}function Fc(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,Oc(e))}function Bc(){for(var e=this._name,t=this._id,n=Cr(),i=this._groups,o=i.length,r=0;r<o;++r)for(var a=i[r],s=a.length,l,u=0;u<s;++u)if(l=a[u]){var c=ot(l,t);Zn(l,e,n,u,a,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new mt(i,this._parents,e,n)}function Vc(){var e,t,n=this,i=n._id,o=n.size();return new Promise(function(r,a){var s={value:a},l={value:function(){--o===0&&r()}};n.each(function(){var u=dt(this,i),c=u.on;c!==e&&(t=(e=c).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(l)),u.on=t}),o===0&&r()})}var Hc=0;function mt(e,t,n,i){this._groups=e,this._parents=t,this._name=n,this._id=i}function Cr(){return++Hc}var ft=fn.prototype;mt.prototype={constructor:mt,select:_c,selectAll:bc,selectChild:ft.selectChild,selectChildren:ft.selectChildren,filter:hc,merge:pc,selection:xc,transition:Bc,call:ft.call,nodes:ft.nodes,node:ft.node,size:ft.size,empty:ft.empty,each:ft.each,on:vc,attr:Ju,attrTween:nc,style:$c,styleTween:Ic,text:Pc,textTween:Fc,remove:wc,tween:Gu,delay:rc,duration:lc,ease:cc,easeVarying:fc,end:Vc,[Symbol.iterator]:ft[Symbol.iterator]};function Rc(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var Lc={time:null,delay:0,duration:250,ease:Rc};function Yc(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function Gc(e){var t,n;e instanceof mt?(t=e._id,e=e._name):(t=Cr(),(n=Lc).time=Go(),e=e==null?null:e+"");for(var i=this._groups,o=i.length,r=0;r<o;++r)for(var a=i[r],s=a.length,l,u=0;u<s;++u)(l=a[u])&&Zn(l,e,t,u,a,n||Yc(l,t));return new mt(i,this._parents,e,t)}fn.prototype.interrupt=Ru;fn.prototype.transition=Gc;const _n=e=>()=>e;function Xc(e,{sourceEvent:t,target:n,transform:i,dispatch:o}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:i,enumerable:!0,configurable:!0},_:{value:o}})}function pt(e,t,n){this.k=e,this.x=t,this.y=n}pt.prototype={constructor:pt,scale:function(e){return e===1?this:new pt(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new pt(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var Yt=new pt(1,0,0);pt.prototype;function po(e){e.stopImmediatePropagation()}function qt(e){e.preventDefault(),e.stopImmediatePropagation()}function Wc(e){return(!e.ctrlKey||e.type==="wheel")&&!e.button}function Uc(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e,e.hasAttribute("viewBox")?(e=e.viewBox.baseVal,[[e.x,e.y],[e.x+e.width,e.y+e.height]]):[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]):[[0,0],[e.clientWidth,e.clientHeight]]}function bi(){return this.__zoom||Yt}function Zc(e){return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function Kc(){return navigator.maxTouchPoints||"ontouchstart"in this}function qc(e,t,n){var i=e.invertX(t[0][0])-n[0][0],o=e.invertX(t[1][0])-n[1][0],r=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(o>i?(i+o)/2:Math.min(0,i)||Math.max(0,o),a>r?(r+a)/2:Math.min(0,r)||Math.max(0,a))}function Jc(){var e=Wc,t=Uc,n=qc,i=Zc,o=Kc,r=[0,1/0],a=[[-1/0,-1/0],[1/0,1/0]],s=250,l=Cn,u=Xn("start","zoom","end"),c,d,g,m=500,M=150,k=0,N=10;function T(w){w.property("__zoom",bi).on("wheel.zoom",D,{passive:!1}).on("mousedown.zoom",W).on("dblclick.zoom",F).filter(o).on("touchstart.zoom",R).on("touchmove.zoom",x).on("touchend.zoom touchcancel.zoom",K).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}T.transform=function(w,A,S,H){var L=w.selection?w.selection():w;L.property("__zoom",bi),w!==L?O(w,A,S,H):L.interrupt().each(function(){te(this,arguments).event(H).start().zoom(null,typeof A=="function"?A.apply(this,arguments):A).end()})},T.scaleBy=function(w,A,S,H){T.scaleTo(w,function(){var L=this.__zoom.k,J=typeof A=="function"?A.apply(this,arguments):A;return L*J},S,H)},T.scaleTo=function(w,A,S,H){T.transform(w,function(){var L=t.apply(this,arguments),J=this.__zoom,Y=S==null?$(L):typeof S=="function"?S.apply(this,arguments):S,se=J.invert(Y),ce=typeof A=="function"?A.apply(this,arguments):A;return n(E(I(J,ce),Y,se),L,a)},S,H)},T.translateBy=function(w,A,S,H){T.transform(w,function(){return n(this.__zoom.translate(typeof A=="function"?A.apply(this,arguments):A,typeof S=="function"?S.apply(this,arguments):S),t.apply(this,arguments),a)},null,H)},T.translateTo=function(w,A,S,H,L){T.transform(w,function(){var J=t.apply(this,arguments),Y=this.__zoom,se=H==null?$(J):typeof H=="function"?H.apply(this,arguments):H;return n(Yt.translate(se[0],se[1]).scale(Y.k).translate(typeof A=="function"?-A.apply(this,arguments):-A,typeof S=="function"?-S.apply(this,arguments):-S),J,a)},H,L)};function I(w,A){return A=Math.max(r[0],Math.min(r[1],A)),A===w.k?w:new pt(A,w.x,w.y)}function E(w,A,S){var H=A[0]-S[0]*w.k,L=A[1]-S[1]*w.k;return H===w.x&&L===w.y?w:new pt(w.k,H,L)}function $(w){return[(+w[0][0]+ +w[1][0])/2,(+w[0][1]+ +w[1][1])/2]}function O(w,A,S,H){w.on("start.zoom",function(){te(this,arguments).event(H).start()}).on("interrupt.zoom end.zoom",function(){te(this,arguments).event(H).end()}).tween("zoom",function(){var L=this,J=arguments,Y=te(L,J).event(H),se=t.apply(L,J),ce=S==null?$(se):typeof S=="function"?S.apply(L,J):S,Ee=Math.max(se[1][0]-se[0][0],se[1][1]-se[0][1]),q=L.__zoom,Z=typeof A=="function"?A.apply(L,J):A,de=l(q.invert(ce).concat(Ee/q.k),Z.invert(ce).concat(Ee/Z.k));return function(ke){if(ke===1)ke=Z;else{var Ne=de(ke),ye=Ee/Ne[2];ke=new pt(ye,ce[0]-Ne[0]*ye,ce[1]-Ne[1]*ye)}Y.zoom(null,ke)}})}function te(w,A,S){return!S&&w.__zooming||new P(w,A)}function P(w,A){this.that=w,this.args=A,this.active=0,this.sourceEvent=null,this.extent=t.apply(w,A),this.taps=0}P.prototype={event:function(w){return w&&(this.sourceEvent=w),this},start:function(){return++this.active===1&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(w,A){return this.mouse&&w!=="mouse"&&(this.mouse[1]=A.invert(this.mouse[0])),this.touch0&&w!=="touch"&&(this.touch0[1]=A.invert(this.touch0[0])),this.touch1&&w!=="touch"&&(this.touch1[1]=A.invert(this.touch1[0])),this.that.__zoom=A,this.emit("zoom"),this},end:function(){return--this.active===0&&(delete this.that.__zooming,this.emit("end")),this},emit:function(w){var A=tt(this.that).datum();u.call(w,this.that,new Xc(w,{sourceEvent:this.sourceEvent,target:T,transform:this.that.__zoom,dispatch:u}),A)}};function D(w,...A){if(!e.apply(this,arguments))return;var S=te(this,A).event(w),H=this.__zoom,L=Math.max(r[0],Math.min(r[1],H.k*Math.pow(2,i.apply(this,arguments)))),J=rt(w);if(S.wheel)(S.mouse[0][0]!==J[0]||S.mouse[0][1]!==J[1])&&(S.mouse[1]=H.invert(S.mouse[0]=J)),clearTimeout(S.wheel);else{if(H.k===L)return;S.mouse=[J,H.invert(J)],Mn(this),S.start()}qt(w),S.wheel=setTimeout(Y,M),S.zoom("mouse",n(E(I(H,L),S.mouse[0],S.mouse[1]),S.extent,a));function Y(){S.wheel=null,S.end()}}function W(w,...A){if(g||!e.apply(this,arguments))return;var S=w.currentTarget,H=te(this,A,!0).event(w),L=tt(w.view).on("mousemove.zoom",ce,!0).on("mouseup.zoom",Ee,!0),J=rt(w,S),Y=w.clientX,se=w.clientY;hr(w.view),po(w),H.mouse=[J,this.__zoom.invert(J)],Mn(this),H.start();function ce(q){if(qt(q),!H.moved){var Z=q.clientX-Y,de=q.clientY-se;H.moved=Z*Z+de*de>k}H.event(q).zoom("mouse",n(E(H.that.__zoom,H.mouse[0]=rt(q,S),H.mouse[1]),H.extent,a))}function Ee(q){L.on("mousemove.zoom mouseup.zoom",null),pr(q.view,H.moved),qt(q),H.event(q).end()}}function F(w,...A){if(e.apply(this,arguments)){var S=this.__zoom,H=rt(w.changedTouches?w.changedTouches[0]:w,this),L=S.invert(H),J=S.k*(w.shiftKey?.5:2),Y=n(E(I(S,J),H,L),t.apply(this,A),a);qt(w),s>0?tt(this).transition().duration(s).call(O,Y,H,w):tt(this).call(T.transform,Y,H,w)}}function R(w,...A){if(e.apply(this,arguments)){var S=w.touches,H=S.length,L=te(this,A,w.changedTouches.length===H).event(w),J,Y,se,ce;for(po(w),Y=0;Y<H;++Y)se=S[Y],ce=rt(se,this),ce=[ce,this.__zoom.invert(ce),se.identifier],L.touch0?!L.touch1&&L.touch0[2]!==ce[2]&&(L.touch1=ce,L.taps=0):(L.touch0=ce,J=!0,L.taps=1+!!c);c&&(c=clearTimeout(c)),J&&(L.taps<2&&(d=ce[0],c=setTimeout(function(){c=null},m)),Mn(this),L.start())}}function x(w,...A){if(this.__zooming){var S=te(this,A).event(w),H=w.changedTouches,L=H.length,J,Y,se,ce;for(qt(w),J=0;J<L;++J)Y=H[J],se=rt(Y,this),S.touch0&&S.touch0[2]===Y.identifier?S.touch0[0]=se:S.touch1&&S.touch1[2]===Y.identifier&&(S.touch1[0]=se);if(Y=S.that.__zoom,S.touch1){var Ee=S.touch0[0],q=S.touch0[1],Z=S.touch1[0],de=S.touch1[1],ke=(ke=Z[0]-Ee[0])*ke+(ke=Z[1]-Ee[1])*ke,Ne=(Ne=de[0]-q[0])*Ne+(Ne=de[1]-q[1])*Ne;Y=I(Y,Math.sqrt(ke/Ne)),se=[(Ee[0]+Z[0])/2,(Ee[1]+Z[1])/2],ce=[(q[0]+de[0])/2,(q[1]+de[1])/2]}else if(S.touch0)se=S.touch0[0],ce=S.touch0[1];else return;S.zoom("touch",n(E(Y,se,ce),S.extent,a))}}function K(w,...A){if(this.__zooming){var S=te(this,A).event(w),H=w.changedTouches,L=H.length,J,Y;for(po(w),g&&clearTimeout(g),g=setTimeout(function(){g=null},m),J=0;J<L;++J)Y=H[J],S.touch0&&S.touch0[2]===Y.identifier?delete S.touch0:S.touch1&&S.touch1[2]===Y.identifier&&delete S.touch1;if(S.touch1&&!S.touch0&&(S.touch0=S.touch1,delete S.touch1),S.touch0)S.touch0[1]=this.__zoom.invert(S.touch0[0]);else if(S.end(),S.taps===2&&(Y=rt(Y,this),Math.hypot(d[0]-Y[0],d[1]-Y[1])<N)){var se=tt(this).on("dblclick.zoom");se&&se.apply(this,arguments)}}}return T.wheelDelta=function(w){return arguments.length?(i=typeof w=="function"?w:_n(+w),T):i},T.filter=function(w){return arguments.length?(e=typeof w=="function"?w:_n(!!w),T):e},T.touchable=function(w){return arguments.length?(o=typeof w=="function"?w:_n(!!w),T):o},T.extent=function(w){return arguments.length?(t=typeof w=="function"?w:_n([[+w[0][0],+w[0][1]],[+w[1][0],+w[1][1]]]),T):t},T.scaleExtent=function(w){return arguments.length?(r[0]=+w[0],r[1]=+w[1],T):[r[0],r[1]]},T.translateExtent=function(w){return arguments.length?(a[0][0]=+w[0][0],a[1][0]=+w[1][0],a[0][1]=+w[0][1],a[1][1]=+w[1][1],T):[[a[0][0],a[0][1]],[a[1][0],a[1][1]]]},T.constrain=function(w){return arguments.length?(n=w,T):n},T.duration=function(w){return arguments.length?(s=+w,T):s},T.interpolate=function(w){return arguments.length?(l=w,T):l},T.on=function(){var w=u.on.apply(u,arguments);return w===u?T:w},T.clickDistance=function(w){return arguments.length?(k=(w=+w)*w,T):Math.sqrt(k)},T.tapDistance=function(w){return arguments.length?(N=+w,T):N},T}var ne=(e=>(e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom",e))(ne||{}),Uo=(e=>(e.Partial="partial",e.Full="full",e))(Uo||{}),kt=(e=>(e.Bezier="default",e.SimpleBezier="simple-bezier",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e))(kt||{}),_t=(e=>(e.Strict="strict",e.Loose="loose",e))(_t||{}),Do=(e=>(e.Arrow="arrow",e.ArrowClosed="arrowclosed",e))(Do||{}),nn=(e=>(e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal",e))(nn||{}),$r=(e=>(e.TopLeft="top-left",e.TopCenter="top-center",e.TopRight="top-right",e.BottomLeft="bottom-left",e.BottomCenter="bottom-center",e.BottomRight="bottom-right",e))($r||{});const Qc=["INPUT","SELECT","TEXTAREA"],jc=typeof document<"u"?document:null;function Ao(e){var t,n;const i=((n=(t=e.composedPath)==null?void 0:t.call(e))==null?void 0:n[0])||e.target,o=typeof i?.hasAttribute=="function"?i.hasAttribute("contenteditable"):!1,r=typeof i?.closest=="function"?i.closest(".nokey"):null;return Qc.includes(i?.nodeName)||o||!!r}function ed(e){return e.ctrlKey||e.metaKey||e.shiftKey||e.altKey}function Ei(e,t,n,i){const o=t.replace("+",`
`).replace(`

`,`
+`).split(`
`).map(a=>a.trim().toLowerCase());if(o.length===1)return e.toLowerCase()===t.toLowerCase();i||n.add(e.toLowerCase());const r=o.every((a,s)=>n.has(a)&&Array.from(n.values())[s]===o[s]);return i&&n.delete(e.toLowerCase()),r}function td(e,t){return n=>{if(!n.code&&!n.key)return!1;const i=nd(n.code,e);return Array.isArray(e)?e.some(o=>Ei(n[i],o,t,n.type==="keyup")):Ei(n[i],e,t,n.type==="keyup")}}function nd(e,t){return t.includes(e)?"code":"key"}function on(e,t){const n=be(()=>Te(t?.target)??jc),i=In(Te(e)===!0);let o=!1;const r=new Set;let a=l(Te(e));De(()=>Te(e),(u,c)=>{typeof c=="boolean"&&typeof u!="boolean"&&s(),a=l(u)},{immediate:!0}),er(["blur","contextmenu"],s),ii((...u)=>a(...u),u=>{var c,d;const g=Te(t?.actInsideInputWithModifier)??!0,m=Te(t?.preventDefault)??!1;if(o=ed(u),(!o||o&&!g)&&Ao(u))return;const k=((d=(c=u.composedPath)==null?void 0:c.call(u))==null?void 0:d[0])||u.target,N=k?.nodeName==="BUTTON"||k?.nodeName==="A";!m&&(o||!N)&&u.preventDefault(),i.value=!0},{eventName:"keydown",target:n}),ii((...u)=>a(...u),u=>{const c=Te(t?.actInsideInputWithModifier)??!0;if(i.value){if((!o||o&&!c)&&Ao(u))return;o=!1,i.value=!1}},{eventName:"keyup",target:n});function s(){o=!1,r.clear(),i.value=Te(e)===!0}function l(u){return u===null?(s(),()=>!1):typeof u=="boolean"?(s(),i.value=u,()=>!1):Array.isArray(u)||typeof u=="string"?td(u,r):u}return i}const Tr="vue-flow__node-desc",Mr="vue-flow__edge-desc",od="vue-flow__aria-live",Ir=["Enter"," ","Escape"],Vt={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};function Vn(e){return{...e.computedPosition||{x:0,y:0},width:e.dimensions.width||0,height:e.dimensions.height||0}}function Hn(e,t){const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),i=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*i)}function Kn(e){return{width:e.offsetWidth,height:e.offsetHeight}}function Dt(e,t=0,n=1){return Math.min(Math.max(e,t),n)}function Dr(e,t){return{x:Dt(e.x,t[0][0],t[1][0]),y:Dt(e.y,t[0][1],t[1][1])}}function xi(e){const t=e.getRootNode();return"elementFromPoint"in t?t:window.document}function bt(e){return e&&typeof e=="object"&&"id"in e&&"source"in e&&"target"in e}function Tt(e){return e&&typeof e=="object"&&"id"in e&&"position"in e&&!bt(e)}function jt(e){return Tt(e)&&"computedPosition"in e}function bn(e){return!Number.isNaN(e)&&Number.isFinite(e)}function id(e){return bn(e.width)&&bn(e.height)&&bn(e.x)&&bn(e.y)}function rd(e,t,n){const i={id:e.id.toString(),type:e.type??"default",dimensions:en({width:0,height:0}),computedPosition:en({z:0,...e.position}),handleBounds:{source:[],target:[]},draggable:void 0,selectable:void 0,connectable:void 0,focusable:void 0,selected:!1,dragging:!1,resizing:!1,initialized:!1,isParent:!1,position:{x:0,y:0},data:Re(e.data)?e.data:{},events:en(Re(e.events)?e.events:{})};return Object.assign(t??i,e,{id:e.id.toString(),parentNode:n})}function Ar(e,t,n){var i,o;const r={id:e.id.toString(),type:e.type??t?.type??"default",source:e.source.toString(),target:e.target.toString(),sourceHandle:(i=e.sourceHandle)==null?void 0:i.toString(),targetHandle:(o=e.targetHandle)==null?void 0:o.toString(),updatable:e.updatable??n?.updatable,selectable:e.selectable??n?.selectable,focusable:e.focusable??n?.focusable,data:Re(e.data)?e.data:{},events:en(Re(e.events)?e.events:{}),label:e.label??"",interactionWidth:e.interactionWidth??n?.interactionWidth,...n??{}};return Object.assign(t??r,e,{id:e.id.toString()})}function Pr(e,t,n,i){const o=typeof e=="string"?e:e.id,r=new Set,a=i==="source"?"target":"source";for(const s of n)s[a]===o&&r.add(s[i]);return t.filter(s=>r.has(s.id))}function ad(...e){if(e.length===3){const[r,a,s]=e;return Pr(r,a,s,"target")}const[t,n]=e,i=typeof t=="string"?t:t.id;return n.filter(r=>bt(r)&&r.source===i).map(r=>n.find(a=>Tt(a)&&a.id===r.target))}function sd(...e){if(e.length===3){const[r,a,s]=e;return Pr(r,a,s,"source")}const[t,n]=e,i=typeof t=="string"?t:t.id;return n.filter(r=>bt(r)&&r.target===i).map(r=>n.find(a=>Tt(a)&&a.id===r.source))}function zr({source:e,sourceHandle:t,target:n,targetHandle:i}){return`vueflow__edge-${e}${t??""}-${n}${i??""}`}function ld(e,t){return t.some(n=>bt(n)&&n.source===e.source&&n.target===e.target&&(n.sourceHandle===e.sourceHandle||!n.sourceHandle&&!e.sourceHandle)&&(n.targetHandle===e.targetHandle||!n.targetHandle&&!e.targetHandle))}function Po({x:e,y:t},{x:n,y:i,zoom:o}){return{x:e*o+n,y:t*o+i}}function un({x:e,y:t},{x:n,y:i,zoom:o},r=!1,a=[1,1]){const s={x:(e-n)/o,y:(t-i)/o};return r?qn(s,a):s}function ud(e,t){return{x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}}function Or({x:e,y:t,width:n,height:i}){return{x:e,y:t,x2:e+n,y2:t+i}}function cd({x:e,y:t,x2:n,y2:i}){return{x:e,y:t,width:n-e,height:i-t}}function Fr(e){let t={x:Number.POSITIVE_INFINITY,y:Number.POSITIVE_INFINITY,x2:Number.NEGATIVE_INFINITY,y2:Number.NEGATIVE_INFINITY};for(let n=0;n<e.length;n++){const i=e[n];t=ud(t,Or({...i.computedPosition,...i.dimensions}))}return cd(t)}function Br(e,t,n={x:0,y:0,zoom:1},i=!1,o=!1){const r={...un(t,n),width:t.width/n.zoom,height:t.height/n.zoom},a=[];for(const s of e){const{dimensions:l,selectable:u=!0,hidden:c=!1}=s,d=l.width??s.width??null,g=l.height??s.height??null;if(o&&!u||c)continue;const m=Hn(r,Vn(s)),M=d===null||g===null,k=i&&m>0,N=(d??0)*(g??0);(M||k||m>=N||s.dragging)&&a.push(s)}return a}function Vr(e,t){const n=new Set;if(typeof e=="string")n.add(e);else if(e.length>=1)for(const i of e)n.add(i.id);return t.filter(i=>n.has(i.source)||n.has(i.target))}function Si(e,t,n,i,o,r=.1,a={x:0,y:0}){const s=t/(e.width*(1+r)),l=n/(e.height*(1+r)),u=Math.min(s,l),c=Dt(u,i,o),d=e.x+e.width/2,g=e.y+e.height/2,m=t/2-d*c+(a.x??0),M=n/2-g*c+(a.y??0);return{x:m,y:M,zoom:c}}function dd(e,t){return{x:t.x+e.x,y:t.y+e.y,z:(e.z>t.z?e.z:t.z)+1}}function Hr(e,t){if(!e.parentNode)return!1;const n=t(e.parentNode);return n?n.selected?!0:Hr(n,t):!1}function cn(e,t){return typeof e>"u"?"":typeof e=="string"?e:`${t?`${t}__`:""}${Object.keys(e).sort().map(i=>`${i}=${e[i]}`).join("&")}`}function ki(e,t,n){return e<t?Dt(Math.abs(e-t),1,t)/t:e>n?-Dt(Math.abs(e-n),1,t)/t:0}function Rr(e,t,n=15,i=40){const o=ki(e.x,i,t.width-i)*n,r=ki(e.y,i,t.height-i)*n;return[o,r]}function go(e,t){if(t){const n=e.position.x+e.dimensions.width-t.dimensions.width,i=e.position.y+e.dimensions.height-t.dimensions.height;if(n>0||i>0||e.position.x<0||e.position.y<0){let o={};if(typeof t.style=="function"?o={...t.style(t)}:t.style&&(o={...t.style}),o.width=o.width??`${t.dimensions.width}px`,o.height=o.height??`${t.dimensions.height}px`,n>0)if(typeof o.width=="string"){const r=Number(o.width.replace("px",""));o.width=`${r+n}px`}else o.width+=n;if(i>0)if(typeof o.height=="string"){const r=Number(o.height.replace("px",""));o.height=`${r+i}px`}else o.height+=i;if(e.position.x<0){const r=Math.abs(e.position.x);if(t.position.x=t.position.x-r,typeof o.width=="string"){const a=Number(o.width.replace("px",""));o.width=`${a+r}px`}else o.width+=r;e.position.x=0}if(e.position.y<0){const r=Math.abs(e.position.y);if(t.position.y=t.position.y-r,typeof o.height=="string"){const a=Number(o.height.replace("px",""));o.height=`${a+r}px`}else o.height+=r;e.position.y=0}t.dimensions.width=Number(o.width.toString().replace("px","")),t.dimensions.height=Number(o.height.toString().replace("px","")),typeof t.style=="function"?t.style=r=>{const a=t.style;return{...a(r),...o}}:t.style={...t.style,...o}}}}function Ni(e,t){var n,i;const o=e.filter(a=>a.type==="add"||a.type==="remove");for(const a of o)if(a.type==="add")t.findIndex(l=>l.id===a.item.id)===-1&&t.push(a.item);else if(a.type==="remove"){const s=t.findIndex(l=>l.id===a.id);s!==-1&&t.splice(s,1)}const r=t.map(a=>a.id);for(const a of t)for(const s of e)if(s.id===a.id)switch(s.type){case"select":a.selected=s.selected;break;case"position":if(jt(a)&&(typeof s.position<"u"&&(a.position=s.position),typeof s.dragging<"u"&&(a.dragging=s.dragging),a.expandParent&&a.parentNode)){const l=t[r.indexOf(a.parentNode)];l&&jt(l)&&go(a,l)}break;case"dimensions":if(jt(a)&&(typeof s.dimensions<"u"&&(a.dimensions=s.dimensions),typeof s.updateStyle<"u"&&s.updateStyle&&(a.style={...a.style||{},width:`${(n=s.dimensions)==null?void 0:n.width}px`,height:`${(i=s.dimensions)==null?void 0:i.height}px`}),typeof s.resizing<"u"&&(a.resizing=s.resizing),a.expandParent&&a.parentNode)){const l=t[r.indexOf(a.parentNode)];l&&jt(l)&&(!!l.dimensions.width&&!!l.dimensions.height?go(a,l):He(()=>{go(a,l)}))}break}return t}function yt(e,t){return{id:e,type:"select",selected:t}}function Ci(e){return{item:e,type:"add"}}function $i(e){return{id:e,type:"remove"}}function Ti(e,t,n,i,o){return{id:e,source:t,target:n,sourceHandle:i||null,targetHandle:o||null,type:"remove"}}function wt(e,t=new Set,n=!1){const i=[];for(const[o,r]of e){const a=t.has(o);!(r.selected===void 0&&!a)&&r.selected!==a&&(n&&(r.selected=a),i.push(yt(r.id,a)))}return i}function ee(e){const t=new Set;let n=!1;const i=()=>t.size>0;e&&(n=!0,t.add(e));const o=s=>{t.delete(s)};return{on:s=>{e&&n&&t.delete(e),t.add(s);const l=()=>{o(s),e&&n&&t.add(e)};return Gn(l),{off:l}},off:o,trigger:s=>Promise.all(Array.from(t).map(l=>l(s))),hasListeners:i,fns:t}}function Mi(e,t,n){let i=e;do{if(i&&i.matches(t))return!0;if(i===n)return!1;i=i.parentElement}while(i);return!1}function fd(e,t,n,i,o){var r,a;const s=[];for(const l of e)(l.selected||l.id===o)&&(!l.parentNode||!Hr(l,i))&&(l.draggable||t&&typeof l.draggable>"u")&&s.push(en({id:l.id,position:l.position||{x:0,y:0},distance:{x:n.x-((r=l.computedPosition)==null?void 0:r.x)||0,y:n.y-((a=l.computedPosition)==null?void 0:a.y)||0},from:l.computedPosition,extent:l.extent,parentNode:l.parentNode,dimensions:l.dimensions,expandParent:l.expandParent}));return s}function mo({id:e,dragItems:t,findNode:n}){const i=[];for(const o of t){const r=n(o.id);r&&i.push(r)}return[e?i.find(o=>o.id===e):i[0],i]}function Lr(e){if(Array.isArray(e))switch(e.length){case 1:return[e[0],e[0],e[0],e[0]];case 2:return[e[0],e[1],e[0],e[1]];case 3:return[e[0],e[1],e[2],e[1]];case 4:return e;default:return[0,0,0,0]}return[e,e,e,e]}function hd(e,t,n){const[i,o,r,a]=typeof e!="string"?Lr(e.padding):[0,0,0,0];return n&&typeof n.computedPosition.x<"u"&&typeof n.computedPosition.y<"u"&&typeof n.dimensions.width<"u"&&typeof n.dimensions.height<"u"?[[n.computedPosition.x+a,n.computedPosition.y+i],[n.computedPosition.x+n.dimensions.width-o,n.computedPosition.y+n.dimensions.height-r]]:!1}function pd(e,t,n,i){let o=e.extent||n;if((o==="parent"||!Array.isArray(o)&&o?.range==="parent")&&!e.expandParent)if(e.parentNode&&i&&e.dimensions.width&&e.dimensions.height){const r=hd(o,e,i);r&&(o=r)}else t(new Ye(Le.NODE_EXTENT_INVALID,e.id)),o=n;else if(Array.isArray(o)){const r=i?.computedPosition.x||0,a=i?.computedPosition.y||0;o=[[o[0][0]+r,o[0][1]+a],[o[1][0]+r,o[1][1]+a]]}else if(o!=="parent"&&o?.range&&Array.isArray(o.range)){const[r,a,s,l]=Lr(o.padding),u=i?.computedPosition.x||0,c=i?.computedPosition.y||0;o=[[o.range[0][0]+u+l,o.range[0][1]+c+r],[o.range[1][0]+u-a,o.range[1][1]+c-s]]}return o==="parent"?[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]]:o}function gd({width:e,height:t},n){return[n[0],[n[1][0]-(e||0),n[1][1]-(t||0)]]}function Zo(e,t,n,i,o){const r=gd(e.dimensions,pd(e,n,i,o)),a=Dr(t,r);return{position:{x:a.x-(o?.computedPosition.x||0),y:a.y-(o?.computedPosition.y||0)},computedPosition:a}}function Gt(e,t,n=ne.Left,i=!1){const o=(t?.x??0)+e.computedPosition.x,r=(t?.y??0)+e.computedPosition.y,{width:a,height:s}=t??wd(e);if(i)return{x:o+a/2,y:r+s/2};switch(t?.position??n){case ne.Top:return{x:o+a/2,y:r};case ne.Right:return{x:o+a,y:r+s/2};case ne.Bottom:return{x:o+a/2,y:r+s};case ne.Left:return{x:o,y:r+s/2}}}function Ii(e,t){return e&&(t?e.find(n=>n.id===t):e[0])||null}function md({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:i,targetWidth:o,targetHeight:r,width:a,height:s,viewport:l}){const u={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+o),y2:Math.max(e.y+i,t.y+r)};u.x===u.x2&&(u.x2+=1),u.y===u.y2&&(u.y2+=1);const c=Or({x:(0-l.x)/l.zoom,y:(0-l.y)/l.zoom,width:a/l.zoom,height:s/l.zoom}),d=Math.max(0,Math.min(c.x2,u.x2)-Math.max(c.x,u.x)),g=Math.max(0,Math.min(c.y2,u.y2)-Math.max(c.y,u.y));return Math.ceil(d*g)>0}function vd(e,t,n=!1){const i=typeof e.zIndex=="number";let o=i?e.zIndex:0;const r=t(e.source),a=t(e.target);return!r||!a?0:(n&&(o=i?e.zIndex:Math.max(r.computedPosition.z||0,a.computedPosition.z||0)),o)}var Le=(e=>(e.MISSING_STYLES="MISSING_STYLES",e.MISSING_VIEWPORT_DIMENSIONS="MISSING_VIEWPORT_DIMENSIONS",e.NODE_INVALID="NODE_INVALID",e.NODE_NOT_FOUND="NODE_NOT_FOUND",e.NODE_MISSING_PARENT="NODE_MISSING_PARENT",e.NODE_TYPE_MISSING="NODE_TYPE_MISSING",e.NODE_EXTENT_INVALID="NODE_EXTENT_INVALID",e.EDGE_INVALID="EDGE_INVALID",e.EDGE_NOT_FOUND="EDGE_NOT_FOUND",e.EDGE_SOURCE_MISSING="EDGE_SOURCE_MISSING",e.EDGE_TARGET_MISSING="EDGE_TARGET_MISSING",e.EDGE_TYPE_MISSING="EDGE_TYPE_MISSING",e.EDGE_SOURCE_TARGET_SAME="EDGE_SOURCE_TARGET_SAME",e.EDGE_SOURCE_TARGET_MISSING="EDGE_SOURCE_TARGET_MISSING",e.EDGE_ORPHANED="EDGE_ORPHANED",e.USEVUEFLOW_OPTIONS="USEVUEFLOW_OPTIONS",e))(Le||{});const Di={MISSING_STYLES:()=>"It seems that you haven't loaded the necessary styles. Please import '@vue-flow/core/dist/style.css' to ensure that the graph is rendered correctly",MISSING_VIEWPORT_DIMENSIONS:()=>"The Vue Flow parent container needs a width and a height to render the graph",NODE_INVALID:e=>`Node is invalid
Node: ${e}`,NODE_NOT_FOUND:e=>`Node not found
Node: ${e}`,NODE_MISSING_PARENT:(e,t)=>`Node is missing a parent
Node: ${e}
Parent: ${t}`,NODE_TYPE_MISSING:e=>`Node type is missing
Type: ${e}`,NODE_EXTENT_INVALID:e=>`Only child nodes can use a parent extent
Node: ${e}`,EDGE_INVALID:e=>`An edge needs a source and a target
Edge: ${e}`,EDGE_SOURCE_MISSING:(e,t)=>`Edge source is missing
Edge: ${e} 
Source: ${t}`,EDGE_TARGET_MISSING:(e,t)=>`Edge target is missing
Edge: ${e} 
Target: ${t}`,EDGE_TYPE_MISSING:e=>`Edge type is missing
Type: ${e}`,EDGE_SOURCE_TARGET_SAME:(e,t,n)=>`Edge source and target are the same
Edge: ${e} 
Source: ${t} 
Target: ${n}`,EDGE_SOURCE_TARGET_MISSING:(e,t,n)=>`Edge source or target is missing
Edge: ${e} 
Source: ${t} 
Target: ${n}`,EDGE_ORPHANED:e=>`Edge was orphaned (suddenly missing source or target) and has been removed
Edge: ${e}`,EDGE_NOT_FOUND:e=>`Edge not found
Edge: ${e}`,USEVUEFLOW_OPTIONS:()=>"The options parameter is deprecated and will be removed in the next major version. Please use the id parameter instead"};class Ye extends Error{constructor(t,...n){var i;super((i=Di[t])==null?void 0:i.call(Di,...n)),this.name="VueFlowError",this.code=t,this.args=n}}function Ko(e){return"clientX"in e}function yd(e){return"sourceEvent"in e}function st(e,t){const n=Ko(e);let i,o;return n?(i=e.clientX,o=e.clientY):"touches"in e&&e.touches.length>0?(i=e.touches[0].clientX,o=e.touches[0].clientY):"changedTouches"in e&&e.changedTouches.length>0?(i=e.changedTouches[0].clientX,o=e.changedTouches[0].clientY):(i=0,o=0),{x:i-(t?.left??0),y:o-(t?.top??0)}}const Rn=()=>{var e;return typeof navigator<"u"&&((e=navigator?.userAgent)==null?void 0:e.indexOf("Mac"))>=0};function wd(e){var t,n;return{width:((t=e.dimensions)==null?void 0:t.width)??e.width??0,height:((n=e.dimensions)==null?void 0:n.height)??e.height??0}}function qn(e,t=[1,1]){return{x:t[0]*Math.round(e.x/t[0]),y:t[1]*Math.round(e.y/t[1])}}const _d=()=>!0;function vo(e){e?.classList.remove("valid","connecting","vue-flow__handle-valid","vue-flow__handle-connecting")}function bd(e,t,n){const i=[],o={x:e.x-n,y:e.y-n,width:n*2,height:n*2};for(const r of t.values())Hn(o,Vn(r))>0&&i.push(r);return i}const Ed=250;function xd(e,t,n,i){var o,r;let a=[],s=Number.POSITIVE_INFINITY;const l=bd(e,n,t+Ed);for(const u of l){const c=[...((o=u.handleBounds)==null?void 0:o.source)??[],...((r=u.handleBounds)==null?void 0:r.target)??[]];for(const d of c){if(i.nodeId===d.nodeId&&i.type===d.type&&i.id===d.id)continue;const{x:g,y:m}=Gt(u,d,d.position,!0),M=Math.sqrt((g-e.x)**2+(m-e.y)**2);M>t||(M<s?(a=[{...d,x:g,y:m}],s=M):M===s&&a.push({...d,x:g,y:m}))}}if(!a.length)return null;if(a.length>1){const u=i.type==="source"?"target":"source";return a.find(c=>c.type===u)??a[0]}return a[0]}function Ai(e,{handle:t,connectionMode:n,fromNodeId:i,fromHandleId:o,fromType:r,doc:a,lib:s,flowId:l,isValidConnection:u=_d},c,d,g){const m=r==="target",M=t?a.querySelector(`.${s}-flow__handle[data-id="${l}-${t?.nodeId}-${t?.id}-${t?.type}"]`):null,{x:k,y:N}=st(e),T=a.elementFromPoint(k,N),I=T?.classList.contains(`${s}-flow__handle`)?T:M,E={handleDomNode:I,isValid:!1,connection:null,toHandle:null};if(I){const $=Yr(void 0,I),O=I.getAttribute("data-nodeid"),te=I.getAttribute("data-handleid"),P=I.classList.contains("connectable"),D=I.classList.contains("connectableend");if(!O||!$)return E;const W={source:m?O:i,sourceHandle:m?te:o,target:m?i:O,targetHandle:m?o:te};E.connection=W;const R=P&&D&&(n===_t.Strict?m&&$==="source"||!m&&$==="target":O!==i||te!==o);E.isValid=R&&u(W,{nodes:d,edges:c,sourceNode:g(i),targetNode:g(O)}),E.toHandle=t}return E}function Yr(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}function Sd(e,t){let n=null;return t?n="valid":e&&!t&&(n="invalid"),n}function kd(e,t){let n=null;return t?n=!0:e&&!t&&(n=!1),n}function Nd(e,t,n,i,o,r=!1){var a,s,l;const u=i.get(e);if(!u)return null;const c=o===_t.Strict?(a=u.handleBounds)==null?void 0:a[t]:[...((s=u.handleBounds)==null?void 0:s.source)??[],...((l=u.handleBounds)==null?void 0:l.target)??[]],d=(n?c?.find(g=>g.id===n):c?.[0])??null;return d&&r?{...d,...Gt(u,d,d.position,!0)}:d}const zo={[ne.Left]:ne.Right,[ne.Right]:ne.Left,[ne.Top]:ne.Bottom,[ne.Bottom]:ne.Top},Cd=["production","prod"];function Jn(e,...t){Gr()&&console.warn(`[Vue Flow]: ${e}`,...t)}function Gr(){return!Cd.includes("production")}function Pi(e,t,n,i,o){const r=t.querySelectorAll(`.vue-flow__handle.${e}`);return r?.length?Array.from(r).map(a=>{const s=a.getBoundingClientRect();return{id:a.getAttribute("data-handleid"),type:e,nodeId:o,position:a.getAttribute("data-handlepos"),x:(s.left-n.left)/i,y:(s.top-n.top)/i,...Kn(a)}}):null}function Oo(e,t,n,i,o,r=!1,a){o.value=!1,e.selected?(r||e.selected&&t)&&(i([e]),He(()=>{a.blur()})):n([e])}function Re(e){return typeof V(e)<"u"}function $d(e,t,n,i){if(!e||!e.source||!e.target)return n(new Ye(Le.EDGE_INVALID,e?.id??"[ID UNKNOWN]")),!1;let o;return bt(e)?o=e:o={...e,id:zr(e)},o=Ar(o,void 0,i),ld(o,t)?!1:o}function Td(e,t,n,i,o){if(!t.source||!t.target)return o(new Ye(Le.EDGE_INVALID,e.id)),!1;if(!n)return o(new Ye(Le.EDGE_NOT_FOUND,e.id)),!1;const{id:r,...a}=e;return{...a,id:i?zr(t):r,source:t.source,target:t.target,sourceHandle:t.sourceHandle,targetHandle:t.targetHandle}}function zi(e,t,n){const i={},o=[];for(let r=0;r<e.length;++r){const a=e[r];if(!Tt(a)){n(new Ye(Le.NODE_INVALID,a?.id)||`[ID UNKNOWN|INDEX ${r}]`);continue}const s=rd(a,t(a.id),a.parentNode);a.parentNode&&(i[a.parentNode]=!0),o[r]=s}for(const r of o){const a=t(r.parentNode)||o.find(s=>s.id===r.parentNode);r.parentNode&&!a&&n(new Ye(Le.NODE_MISSING_PARENT,r.id,r.parentNode)),(r.parentNode||i[r.id])&&(i[r.id]&&(r.isParent=!0),a&&(a.isParent=!0))}return o}function Oi(e,t,n,i,o,r){let a=o;const s=i.get(a)||new Map;i.set(a,s.set(n,t)),a=`${o}-${e}`;const l=i.get(a)||new Map;if(i.set(a,l.set(n,t)),r){a=`${o}-${e}-${r}`;const u=i.get(a)||new Map;i.set(a,u.set(n,t))}}function yo(e,t,n){e.clear();for(const i of n){const{source:o,target:r,sourceHandle:a=null,targetHandle:s=null}=i,l={edgeId:i.id,source:o,target:r,sourceHandle:a,targetHandle:s},u=`${o}-${a}--${r}-${s}`,c=`${r}-${s}--${o}-${a}`;Oi("source",l,c,e,o,a),Oi("target",l,u,e,r,s)}}function Fi(e,t){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}function wo(e,t,n,i,o,r,a,s){const l=[];for(const u of e){const c=bt(u)?u:$d(u,s,o,r);if(!c)continue;const d=n(c.source),g=n(c.target);if(!d||!g){o(new Ye(Le.EDGE_SOURCE_TARGET_MISSING,c.id,c.source,c.target));continue}if(!d){o(new Ye(Le.EDGE_SOURCE_MISSING,c.id,c.source));continue}if(!g){o(new Ye(Le.EDGE_TARGET_MISSING,c.id,c.target));continue}if(t&&!t(c,{edges:s,nodes:a,sourceNode:d,targetNode:g})){o(new Ye(Le.EDGE_INVALID,c.id));continue}const m=i(c.id);l.push({...Ar(c,m,r),sourceNode:d,targetNode:g})}return l}const Bi=Symbol("vueFlow"),Xr=Symbol("nodeId"),Wr=Symbol("nodeRef"),Md=Symbol("edgeId"),Id=Symbol("edgeRef"),Qn=Symbol("slots");function Ur(e){const{vueFlowRef:t,snapToGrid:n,snapGrid:i,noDragClassName:o,nodes:r,nodeExtent:a,nodeDragThreshold:s,viewport:l,autoPanOnNodeDrag:u,autoPanSpeed:c,nodesDraggable:d,panBy:g,findNode:m,multiSelectionActive:M,nodesSelectionActive:k,selectNodesOnDrag:N,removeSelectedElements:T,addSelectedNodes:I,updateNodePositions:E,emits:$}=Oe(),{onStart:O,onDrag:te,onStop:P,onClick:D,el:W,disabled:F,id:R,selectable:x,dragHandle:K}=e,w=In(!1);let A=[],S,H=null,L={x:void 0,y:void 0},J={x:0,y:0},Y=null,se=!1,ce=0,Ee=!1;const q=Pd(),Z=({x:fe,y:xe})=>{L={x:fe,y:xe};let Se=!1;if(A=A.map(v=>{const p={x:fe-v.distance.x,y:xe-v.distance.y},{computedPosition:b}=Zo(v,n.value?qn(p,i.value):p,$.error,a.value,v.parentNode?m(v.parentNode):void 0);return Se=Se||v.position.x!==b.x||v.position.y!==b.y,v.position=b,v}),!!Se&&(E(A,!0,!0),w.value=!0,Y)){const[v,p]=mo({id:R,dragItems:A,findNode:m});te({event:Y,node:v,nodes:p})}},de=()=>{if(!H)return;const[fe,xe]=Rr(J,H,c.value);if(fe!==0||xe!==0){const Se={x:(L.x??0)-fe/l.value.zoom,y:(L.y??0)-xe/l.value.zoom};g({x:fe,y:xe})&&Z(Se)}ce=requestAnimationFrame(de)},ke=(fe,xe)=>{se=!0;const Se=m(R);!N.value&&!M.value&&Se&&(Se.selected||T()),Se&&Te(x)&&N.value&&Oo(Se,M.value,I,T,k,!1,xe);const v=q(fe.sourceEvent);if(L=v,A=fd(r.value,d.value,v,m,R),A.length){const[p,b]=mo({id:R,dragItems:A,findNode:m});O({event:fe.sourceEvent,node:p,nodes:b})}},Ne=(fe,xe)=>{var Se;fe.sourceEvent.type==="touchmove"&&fe.sourceEvent.touches.length>1||(s.value===0&&ke(fe,xe),L=q(fe.sourceEvent),H=((Se=t.value)==null?void 0:Se.getBoundingClientRect())||null,J=st(fe.sourceEvent,H))},ye=(fe,xe)=>{const Se=q(fe.sourceEvent);if(!Ee&&se&&u.value&&(Ee=!0,de()),!se){const v=Se.xSnapped-(L.x??0),p=Se.ySnapped-(L.y??0);Math.sqrt(v*v+p*p)>s.value&&ke(fe,xe)}(L.x!==Se.xSnapped||L.y!==Se.ySnapped)&&A.length&&se&&(Y=fe.sourceEvent,J=st(fe.sourceEvent,H),Z(Se))},$e=fe=>{let xe=!1;if(!se&&!w.value&&!M.value){const Se=fe.sourceEvent,v=q(Se),p=v.xSnapped-(L.x??0),b=v.ySnapped-(L.y??0),y=Math.sqrt(p*p+b*b);y!==0&&y<=s.value&&(D?.(Se),xe=!0)}if(A.length&&!xe){E(A,!1,!1);const[Se,v]=mo({id:R,dragItems:A,findNode:m});P({event:fe.sourceEvent,node:Se,nodes:v})}A=[],w.value=!1,Ee=!1,se=!1,L={x:void 0,y:void 0},cancelAnimationFrame(ce)};return De([()=>Te(F),W],([fe,xe],Se,v)=>{if(xe){const p=tt(xe);fe||(S=iu().on("start",b=>Ne(b,xe)).on("drag",b=>ye(b,xe)).on("end",b=>$e(b)).filter(b=>{const y=b.target,z=Te(K);return!b.button&&(!o.value||!Mi(y,`.${o.value}`,xe)&&(!z||Mi(y,z,xe)))}),p.call(S)),v(()=>{p.on(".drag",null),S&&(S.on("start",null),S.on("drag",null),S.on("end",null))})}}),w}function Dd(){return{doubleClick:ee(),click:ee(),mouseEnter:ee(),mouseMove:ee(),mouseLeave:ee(),contextMenu:ee(),updateStart:ee(),update:ee(),updateEnd:ee()}}function Ad(e,t){const n=Dd();return n.doubleClick.on(i=>{var o,r;t.edgeDoubleClick(i),(r=(o=e.events)==null?void 0:o.doubleClick)==null||r.call(o,i)}),n.click.on(i=>{var o,r;t.edgeClick(i),(r=(o=e.events)==null?void 0:o.click)==null||r.call(o,i)}),n.mouseEnter.on(i=>{var o,r;t.edgeMouseEnter(i),(r=(o=e.events)==null?void 0:o.mouseEnter)==null||r.call(o,i)}),n.mouseMove.on(i=>{var o,r;t.edgeMouseMove(i),(r=(o=e.events)==null?void 0:o.mouseMove)==null||r.call(o,i)}),n.mouseLeave.on(i=>{var o,r;t.edgeMouseLeave(i),(r=(o=e.events)==null?void 0:o.mouseLeave)==null||r.call(o,i)}),n.contextMenu.on(i=>{var o,r;t.edgeContextMenu(i),(r=(o=e.events)==null?void 0:o.contextMenu)==null||r.call(o,i)}),n.updateStart.on(i=>{var o,r;t.edgeUpdateStart(i),(r=(o=e.events)==null?void 0:o.updateStart)==null||r.call(o,i)}),n.update.on(i=>{var o,r;t.edgeUpdate(i),(r=(o=e.events)==null?void 0:o.update)==null||r.call(o,i)}),n.updateEnd.on(i=>{var o,r;t.edgeUpdateEnd(i),(r=(o=e.events)==null?void 0:o.updateEnd)==null||r.call(o,i)}),Object.entries(n).reduce((i,[o,r])=>(i.emit[o]=r.trigger,i.on[o]=r.on,i),{emit:{},on:{}})}function Pd(){const{viewport:e,snapGrid:t,snapToGrid:n,vueFlowRef:i}=Oe();return o=>{var r;const a=((r=i.value)==null?void 0:r.getBoundingClientRect())??{left:0,top:0},s=yd(o)?o.sourceEvent:o,{x:l,y:u}=st(s,a),c=un({x:l,y:u},e.value),{x:d,y:g}=n.value?qn(c,t.value):c;return{xSnapped:d,ySnapped:g,...c}}}function En(){return!0}function Zr({handleId:e,nodeId:t,type:n,isValidConnection:i,edgeUpdaterType:o,onEdgeUpdate:r,onEdgeUpdateEnd:a}){const{id:s,vueFlowRef:l,connectionMode:u,connectionRadius:c,connectOnClick:d,connectionClickStartHandle:g,nodesConnectable:m,autoPanOnConnect:M,autoPanSpeed:k,findNode:N,panBy:T,startConnection:I,updateConnection:E,endConnection:$,emits:O,viewport:te,edges:P,nodes:D,isValidConnection:W,nodeLookup:F}=Oe();let R=null,x=!1,K=null;function w(S){var H;const L=Te(n)==="target",J=Ko(S),Y=xi(S.target);if(J&&S.button===0||!J){let se=function(he){v=st(he,fe),Z=xd(un(v,te.value,!1,[1,1]),c.value,F.value,y),p||(b(),p=!0);const me=Ai(he,{handle:Z,connectionMode:u.value,fromNodeId:Te(t),fromHandleId:Te(e),fromType:L?"target":"source",isValidConnection:q,doc:Y,lib:"vue",flowId:s,nodeLookup:F.value},P.value,D.value,N);K=me.handleDomNode,R=me.connection,x=kd(!!Z,me.isValid);const Ce={...Q,isValid:x,to:me.toHandle&&x?Po({x:me.toHandle.x,y:me.toHandle.y},te.value):v,toHandle:me.toHandle,toPosition:x&&me.toHandle?me.toHandle.position:zo[y.position],toNode:me.toHandle?F.value.get(me.toHandle.nodeId):null};if(!(x&&Z&&Q?.toHandle&&Ce.toHandle&&Q.toHandle.type===Ce.toHandle.type&&Q.toHandle.nodeId===Ce.toHandle.nodeId&&Q.toHandle.id===Ce.toHandle.id&&Q.to.x===Ce.to.x&&Q.to.y===Ce.to.y)){if(E(Z&&x?Po({x:Z.x,y:Z.y},te.value):v,me.toHandle,Sd(!!Z,x)),Q=Ce,!Z&&!x&&!K)return vo(Se);R&&R.source!==R.target&&K&&(vo(Se),Se=K,K.classList.add("connecting","vue-flow__handle-connecting"),K.classList.toggle("valid",!!x),K.classList.toggle("vue-flow__handle-valid",!!x))}},ce=function(he){(Z||K)&&R&&x&&(r?r(he,R):O.connect(R)),O.connectEnd(he),o&&a?.(he),vo(Se),cancelAnimationFrame(de),$(he),p=!1,x=!1,R=null,K=null,Y.removeEventListener("mousemove",se),Y.removeEventListener("mouseup",ce),Y.removeEventListener("touchmove",se),Y.removeEventListener("touchend",ce)};const Ee=N(Te(t));let q=Te(i)||W.value||En;!q&&Ee&&(q=(L?Ee.isValidSourcePos:Ee.isValidTargetPos)||En);let Z,de=0;const{x:ke,y:Ne}=st(S),ye=Y?.elementFromPoint(ke,Ne),$e=Yr(Te(o),ye),fe=(H=l.value)==null?void 0:H.getBoundingClientRect();if(!fe||!$e)return;const xe=Nd(Te(t),$e,Te(e),F.value,u.value);if(!xe)return;let Se,v=st(S,fe),p=!1;const b=()=>{if(!M.value)return;const[he,me]=Rr(v,fe,k.value);T({x:he,y:me}),de=requestAnimationFrame(b)},y={...xe,nodeId:Te(t),type:$e,position:xe.position},z=F.value.get(Te(t)),ie={inProgress:!0,isValid:null,from:Gt(z,y,ne.Left,!0),fromHandle:y,fromPosition:y.position,fromNode:z,to:v,toHandle:null,toPosition:zo[y.position],toNode:null};I({nodeId:Te(t),id:Te(e),type:$e,position:ye?.getAttribute("data-handlepos")||ne.Top,...v},{x:ke-fe.left,y:Ne-fe.top}),O.connectStart({event:S,nodeId:Te(t),handleId:Te(e),handleType:$e});let Q=ie;Y.addEventListener("mousemove",se),Y.addEventListener("mouseup",ce),Y.addEventListener("touchmove",se),Y.addEventListener("touchend",ce)}}function A(S){var H,L;if(!d.value)return;const J=Te(n)==="target";if(!g.value){O.clickConnectStart({event:S,nodeId:Te(t),handleId:Te(e)}),I({nodeId:Te(t),type:Te(n),id:Te(e),position:ne.Top,...st(S)},void 0,!0);return}let Y=Te(i)||W.value||En;const se=N(Te(t));if(!Y&&se&&(Y=(J?se.isValidSourcePos:se.isValidTargetPos)||En),se&&(typeof se.connectable>"u"?m.value:se.connectable)===!1)return;const ce=xi(S.target),Ee=Ai(S,{handle:{nodeId:Te(t),id:Te(e),type:Te(n),position:ne.Top,...st(S)},connectionMode:u.value,fromNodeId:g.value.nodeId,fromHandleId:g.value.id??null,fromType:g.value.type,isValidConnection:Y,doc:ce,lib:"vue",flowId:s,nodeLookup:F.value},P.value,D.value,N),q=((H=Ee.connection)==null?void 0:H.source)===((L=Ee.connection)==null?void 0:L.target);Ee.isValid&&Ee.connection&&!q&&O.connect(Ee.connection),O.clickConnectEnd(S),$(S,!0)}return{handlePointerDown:w,handleClick:A}}function zd(){return Xt(Xr,"")}function Kr(e){const t=e??zd()??"",n=Xt(Wr,ge(null)),{findNode:i,edges:o,emits:r}=Oe(),a=i(t);return a||r.error(new Ye(Le.NODE_NOT_FOUND,t)),{id:t,nodeEl:n,node:a,parentNode:be(()=>i(a.parentNode)),connectedEdges:be(()=>Vr([a],o.value))}}function Od(){return{doubleClick:ee(),click:ee(),mouseEnter:ee(),mouseMove:ee(),mouseLeave:ee(),contextMenu:ee(),dragStart:ee(),drag:ee(),dragStop:ee()}}function Fd(e,t){const n=Od();return n.doubleClick.on(i=>{var o,r;t.nodeDoubleClick(i),(r=(o=e.events)==null?void 0:o.doubleClick)==null||r.call(o,i)}),n.click.on(i=>{var o,r;t.nodeClick(i),(r=(o=e.events)==null?void 0:o.click)==null||r.call(o,i)}),n.mouseEnter.on(i=>{var o,r;t.nodeMouseEnter(i),(r=(o=e.events)==null?void 0:o.mouseEnter)==null||r.call(o,i)}),n.mouseMove.on(i=>{var o,r;t.nodeMouseMove(i),(r=(o=e.events)==null?void 0:o.mouseMove)==null||r.call(o,i)}),n.mouseLeave.on(i=>{var o,r;t.nodeMouseLeave(i),(r=(o=e.events)==null?void 0:o.mouseLeave)==null||r.call(o,i)}),n.contextMenu.on(i=>{var o,r;t.nodeContextMenu(i),(r=(o=e.events)==null?void 0:o.contextMenu)==null||r.call(o,i)}),n.dragStart.on(i=>{var o,r;t.nodeDragStart(i),(r=(o=e.events)==null?void 0:o.dragStart)==null||r.call(o,i)}),n.drag.on(i=>{var o,r;t.nodeDrag(i),(r=(o=e.events)==null?void 0:o.drag)==null||r.call(o,i)}),n.dragStop.on(i=>{var o,r;t.nodeDragStop(i),(r=(o=e.events)==null?void 0:o.dragStop)==null||r.call(o,i)}),Object.entries(n).reduce((i,[o,r])=>(i.emit[o]=r.trigger,i.on[o]=r.on,i),{emit:{},on:{}})}function qr(){const{getSelectedNodes:e,nodeExtent:t,updateNodePositions:n,findNode:i,snapGrid:o,snapToGrid:r,nodesDraggable:a,emits:s}=Oe();return(l,u=!1)=>{const c=r.value?o.value[0]:5,d=r.value?o.value[1]:5,g=u?4:1,m=l.x*c*g,M=l.y*d*g,k=[];for(const N of e.value)if(N.draggable||a&&typeof N.draggable>"u"){const T={x:N.computedPosition.x+m,y:N.computedPosition.y+M},{computedPosition:I}=Zo(N,T,s.error,t.value,N.parentNode?i(N.parentNode):void 0);k.push({id:N.id,position:I,from:N.position,distance:{x:l.x,y:l.y},dimensions:N.dimensions})}n(k,!0,!1)}}const _o=.1,Bd=e=>((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2;function vt(){return Jn("Viewport not initialized yet."),Promise.resolve(!1)}const Vd={zoomIn:vt,zoomOut:vt,zoomTo:vt,fitView:vt,setCenter:vt,fitBounds:vt,project:e=>e,screenToFlowCoordinate:e=>e,flowToScreenCoordinate:e=>e,setViewport:vt,setTransform:vt,getViewport:()=>({x:0,y:0,zoom:1}),getTransform:()=>({x:0,y:0,zoom:1}),viewportInitialized:!1};function Hd(e){function t(i,o){return new Promise(r=>{e.d3Selection&&e.d3Zoom?e.d3Zoom.interpolate(o?.interpolate==="linear"?tn:Cn).scaleBy(bo(e.d3Selection,o?.duration,o?.ease,()=>{r(!0)}),i):r(!1)})}function n(i,o,r,a){return new Promise(s=>{var l;const{x:u,y:c}=Dr({x:-i,y:-o},e.translateExtent),d=Yt.translate(-u,-c).scale(r);e.d3Selection&&e.d3Zoom?(l=e.d3Zoom)==null||l.interpolate(a?.interpolate==="linear"?tn:Cn).transform(bo(e.d3Selection,a?.duration,a?.ease,()=>{s(!0)}),d):s(!1)})}return be(()=>e.d3Zoom&&e.d3Selection&&e.dimensions.width&&e.dimensions.height?{viewportInitialized:!0,zoomIn:o=>t(1.2,o),zoomOut:o=>t(1/1.2,o),zoomTo:(o,r)=>new Promise(a=>{e.d3Selection&&e.d3Zoom?e.d3Zoom.interpolate(r?.interpolate==="linear"?tn:Cn).scaleTo(bo(e.d3Selection,r?.duration,r?.ease,()=>{a(!0)}),o):a(!1)}),setViewport:(o,r)=>n(o.x,o.y,o.zoom,r),setTransform:(o,r)=>n(o.x,o.y,o.zoom,r),getViewport:()=>({x:e.viewport.x,y:e.viewport.y,zoom:e.viewport.zoom}),getTransform:()=>({x:e.viewport.x,y:e.viewport.y,zoom:e.viewport.zoom}),fitView:(o={padding:_o,includeHiddenNodes:!1,duration:0})=>{var r,a;const s=[];for(const g of e.nodes)g.dimensions.width&&g.dimensions.height&&(o?.includeHiddenNodes||!g.hidden)&&(!((r=o.nodes)!=null&&r.length)||(a=o.nodes)!=null&&a.length&&o.nodes.includes(g.id))&&s.push(g);if(!s.length)return Promise.resolve(!1);const l=Fr(s),{x:u,y:c,zoom:d}=Si(l,e.dimensions.width,e.dimensions.height,o.minZoom??e.minZoom,o.maxZoom??e.maxZoom,o.padding??_o,o.offset);return n(u,c,d,o)},setCenter:(o,r,a)=>{const s=typeof a?.zoom<"u"?a.zoom:e.maxZoom,l=e.dimensions.width/2-o*s,u=e.dimensions.height/2-r*s;return n(l,u,s,a)},fitBounds:(o,r={padding:_o})=>{const{x:a,y:s,zoom:l}=Si(o,e.dimensions.width,e.dimensions.height,e.minZoom,e.maxZoom,r.padding);return n(a,s,l,r)},project:o=>un(o,e.viewport,e.snapToGrid,e.snapGrid),screenToFlowCoordinate:o=>{if(e.vueFlowRef){const{x:r,y:a}=e.vueFlowRef.getBoundingClientRect(),s={x:o.x-r,y:o.y-a};return un(s,e.viewport,e.snapToGrid,e.snapGrid)}return{x:0,y:0}},flowToScreenCoordinate:o=>{if(e.vueFlowRef){const{x:r,y:a}=e.vueFlowRef.getBoundingClientRect(),s={x:o.x+r,y:o.y+a};return Po(s,e.viewport)}return{x:0,y:0}}}:Vd)}function bo(e,t=0,n=Bd,i=()=>{}){const o=typeof t=="number"&&t>0;return o||i(),o?e.transition().duration(t).ease(n).on("end",i):e}function Rd(e,t,n){const i=Xi(!0);return i.run(()=>{const o=()=>{i.run(()=>{let k,N,T=!!(n.nodes.value.length||n.edges.value.length);k=zt([e.modelValue,()=>{var I,E;return(E=(I=e.modelValue)==null?void 0:I.value)==null?void 0:E.length}],([I])=>{I&&Array.isArray(I)&&(N?.pause(),n.setElements(I),!N&&!T&&I.length?T=!0:N?.resume())}),N=zt([n.nodes,n.edges,()=>n.edges.value.length,()=>n.nodes.value.length],([I,E])=>{var $;($=e.modelValue)!=null&&$.value&&Array.isArray(e.modelValue.value)&&(k?.pause(),e.modelValue.value=[...I,...E],He(()=>{k?.resume()}))},{immediate:T}),kn(()=>{k?.stop(),N?.stop()})})},r=()=>{i.run(()=>{let k,N,T=!!n.nodes.value.length;k=zt([e.nodes,()=>{var I,E;return(E=(I=e.nodes)==null?void 0:I.value)==null?void 0:E.length}],([I])=>{I&&Array.isArray(I)&&(N?.pause(),n.setNodes(I),!N&&!T&&I.length?T=!0:N?.resume())}),N=zt([n.nodes,()=>n.nodes.value.length],([I])=>{var E;(E=e.nodes)!=null&&E.value&&Array.isArray(e.nodes.value)&&(k?.pause(),e.nodes.value=[...I],He(()=>{k?.resume()}))},{immediate:T}),kn(()=>{k?.stop(),N?.stop()})})},a=()=>{i.run(()=>{let k,N,T=!!n.edges.value.length;k=zt([e.edges,()=>{var I,E;return(E=(I=e.edges)==null?void 0:I.value)==null?void 0:E.length}],([I])=>{I&&Array.isArray(I)&&(N?.pause(),n.setEdges(I),!N&&!T&&I.length?T=!0:N?.resume())}),N=zt([n.edges,()=>n.edges.value.length],([I])=>{var E;(E=e.edges)!=null&&E.value&&Array.isArray(e.edges.value)&&(k?.pause(),e.edges.value=[...I],He(()=>{k?.resume()}))},{immediate:T}),kn(()=>{k?.stop(),N?.stop()})})},s=()=>{i.run(()=>{De(()=>t.maxZoom,()=>{t.maxZoom&&Re(t.maxZoom)&&n.setMaxZoom(t.maxZoom)},{immediate:!0})})},l=()=>{i.run(()=>{De(()=>t.minZoom,()=>{t.minZoom&&Re(t.minZoom)&&n.setMinZoom(t.minZoom)},{immediate:!0})})},u=()=>{i.run(()=>{De(()=>t.translateExtent,()=>{t.translateExtent&&Re(t.translateExtent)&&n.setTranslateExtent(t.translateExtent)},{immediate:!0})})},c=()=>{i.run(()=>{De(()=>t.nodeExtent,()=>{t.nodeExtent&&Re(t.nodeExtent)&&n.setNodeExtent(t.nodeExtent)},{immediate:!0})})},d=()=>{i.run(()=>{De(()=>t.applyDefault,()=>{Re(t.applyDefault)&&(n.applyDefault.value=t.applyDefault)},{immediate:!0})})},g=()=>{i.run(()=>{const k=async N=>{let T=N;typeof t.autoConnect=="function"&&(T=await t.autoConnect(N)),T!==!1&&n.addEdges([T])};De(()=>t.autoConnect,()=>{Re(t.autoConnect)&&(n.autoConnect.value=t.autoConnect)},{immediate:!0}),De(n.autoConnect,(N,T,I)=>{N?n.onConnect(k):n.hooks.value.connect.off(k),I(()=>{n.hooks.value.connect.off(k)})},{immediate:!0})})},m=()=>{const k=["id","modelValue","translateExtent","nodeExtent","edges","nodes","maxZoom","minZoom","applyDefault","autoConnect"];for(const N of Object.keys(t)){const T=N;if(!k.includes(T)){const I=ze(()=>t[T]),E=n[T];Vo(E)&&i.run(()=>{De(I,$=>{Re($)&&(E.value=$)},{immediate:!0})})}}};(()=>{o(),r(),a(),l(),s(),u(),c(),d(),g(),m()})()}),()=>i.stop()}function Ld(){return{edgesChange:ee(),nodesChange:ee(),nodeDoubleClick:ee(),nodeClick:ee(),nodeMouseEnter:ee(),nodeMouseMove:ee(),nodeMouseLeave:ee(),nodeContextMenu:ee(),nodeDragStart:ee(),nodeDrag:ee(),nodeDragStop:ee(),nodesInitialized:ee(),miniMapNodeClick:ee(),miniMapNodeDoubleClick:ee(),miniMapNodeMouseEnter:ee(),miniMapNodeMouseMove:ee(),miniMapNodeMouseLeave:ee(),connect:ee(),connectStart:ee(),connectEnd:ee(),clickConnectStart:ee(),clickConnectEnd:ee(),paneReady:ee(),init:ee(),move:ee(),moveStart:ee(),moveEnd:ee(),selectionDragStart:ee(),selectionDrag:ee(),selectionDragStop:ee(),selectionContextMenu:ee(),selectionStart:ee(),selectionEnd:ee(),viewportChangeStart:ee(),viewportChange:ee(),viewportChangeEnd:ee(),paneScroll:ee(),paneClick:ee(),paneContextMenu:ee(),paneMouseEnter:ee(),paneMouseMove:ee(),paneMouseLeave:ee(),edgeContextMenu:ee(),edgeMouseEnter:ee(),edgeMouseMove:ee(),edgeMouseLeave:ee(),edgeDoubleClick:ee(),edgeClick:ee(),edgeUpdateStart:ee(),edgeUpdate:ee(),edgeUpdateEnd:ee(),updateNodeInternals:ee(),error:ee(e=>Jn(e.message))}}function Yd(e,t){Ma(()=>{for(const[n,i]of Object.entries(t.value)){const o=r=>{e(n,r)};i.fns.add(o),Gn(()=>{i.off(o)})}})}function Jr(){return{vueFlowRef:null,viewportRef:null,nodes:[],edges:[],connectionLookup:new Map,nodeTypes:{},edgeTypes:{},initialized:!1,dimensions:{width:0,height:0},viewport:{x:0,y:0,zoom:1},d3Zoom:null,d3Selection:null,d3ZoomHandler:null,minZoom:.5,maxZoom:2,translateExtent:[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],nodeExtent:[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],selectionMode:Uo.Full,paneDragging:!1,preventScrolling:!0,zoomOnScroll:!0,zoomOnPinch:!0,zoomOnDoubleClick:!0,panOnScroll:!1,panOnScrollSpeed:.5,panOnScrollMode:nn.Free,paneClickDistance:0,panOnDrag:!0,edgeUpdaterRadius:10,onlyRenderVisibleElements:!1,defaultViewport:{x:0,y:0,zoom:1},nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,defaultMarkerColor:"#b1b1b7",connectionLineStyle:{},connectionLineType:null,connectionLineOptions:{type:kt.Bezier,style:{}},connectionMode:_t.Loose,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectionPosition:{x:Number.NaN,y:Number.NaN},connectionRadius:20,connectOnClick:!0,connectionStatus:null,isValidConnection:null,snapGrid:[15,15],snapToGrid:!1,edgesUpdatable:!1,edgesFocusable:!0,nodesFocusable:!0,nodesConnectable:!0,nodesDraggable:!0,nodeDragThreshold:1,elementsSelectable:!0,selectNodesOnDrag:!0,multiSelectionActive:!1,selectionKeyCode:"Shift",multiSelectionKeyCode:Rn()?"Meta":"Control",zoomActivationKeyCode:Rn()?"Meta":"Control",deleteKeyCode:"Backspace",panActivationKeyCode:"Space",hooks:Ld(),applyDefault:!0,autoConnect:!1,fitViewOnInit:!1,fitViewOnInitDone:!1,noDragClassName:"nodrag",noWheelClassName:"nowheel",noPanClassName:"nopan",defaultEdgeOptions:void 0,elevateEdgesOnSelect:!1,elevateNodesOnSelect:!0,autoPanOnNodeDrag:!0,autoPanOnConnect:!0,autoPanSpeed:15,disableKeyboardA11y:!1,ariaLiveMessage:""}}const Gd=["id","vueFlowRef","viewportRef","initialized","modelValue","nodes","edges","maxZoom","minZoom","translateExtent","hooks","defaultEdgeOptions"];function Xd(e,t,n){const i=Hd(e),o=v=>{const p=v??[];e.hooks.updateNodeInternals.trigger(p)},r=v=>sd(v,e.nodes,e.edges),a=v=>ad(v,e.nodes,e.edges),s=v=>Vr(v,e.edges),l=({id:v,type:p,nodeId:b})=>{var y;return Array.from(((y=e.connectionLookup.get(`${b}-${p}-${v??null}`))==null?void 0:y.values())??[])},u=v=>{if(v)return t.value.get(v)},c=v=>{if(v)return n.value.get(v)},d=(v,p,b)=>{var y,z;const le=[];for(const ie of v){const Q={id:ie.id,type:"position",dragging:b,from:ie.from};if(p&&(Q.position=ie.position,ie.parentNode)){const he=u(ie.parentNode);Q.position={x:Q.position.x-(((y=he?.computedPosition)==null?void 0:y.x)??0),y:Q.position.y-(((z=he?.computedPosition)==null?void 0:z.y)??0)}}le.push(Q)}le?.length&&e.hooks.nodesChange.trigger(le)},g=v=>{if(!e.vueFlowRef)return;const p=e.vueFlowRef.querySelector(".vue-flow__transformationpane");if(!p)return;const b=window.getComputedStyle(p),{m22:y}=new window.DOMMatrixReadOnly(b.transform),z=[];for(const le of v){const ie=le,Q=u(ie.id);if(Q){const he=Kn(ie.nodeElement);if(!!(he.width&&he.height&&(Q.dimensions.width!==he.width||Q.dimensions.height!==he.height||ie.forceUpdate))){const Ce=ie.nodeElement.getBoundingClientRect();Q.dimensions=he,Q.handleBounds.source=Pi("source",ie.nodeElement,Ce,y,Q.id),Q.handleBounds.target=Pi("target",ie.nodeElement,Ce,y,Q.id),z.push({id:Q.id,type:"dimensions",dimensions:he})}}}!e.fitViewOnInitDone&&e.fitViewOnInit&&i.value.fitView().then(()=>{e.fitViewOnInitDone=!0}),z.length&&e.hooks.nodesChange.trigger(z)},m=(v,p)=>{const b=new Set,y=new Set;for(const ie of v)Tt(ie)?b.add(ie.id):bt(ie)&&y.add(ie.id);const z=wt(t.value,b,!0),le=wt(n.value,y);if(e.multiSelectionActive){for(const ie of b)z.push(yt(ie,p));for(const ie of y)le.push(yt(ie,p))}z.length&&e.hooks.nodesChange.trigger(z),le.length&&e.hooks.edgesChange.trigger(le)},M=v=>{if(e.multiSelectionActive){const p=v.map(b=>yt(b.id,!0));e.hooks.nodesChange.trigger(p);return}e.hooks.nodesChange.trigger(wt(t.value,new Set(v.map(p=>p.id)),!0)),e.hooks.edgesChange.trigger(wt(n.value))},k=v=>{if(e.multiSelectionActive){const p=v.map(b=>yt(b.id,!0));e.hooks.edgesChange.trigger(p);return}e.hooks.edgesChange.trigger(wt(n.value,new Set(v.map(p=>p.id)))),e.hooks.nodesChange.trigger(wt(t.value,new Set,!0))},N=v=>{m(v,!0)},T=v=>{const b=(v||e.nodes).map(y=>(y.selected=!1,yt(y.id,!1)));e.hooks.nodesChange.trigger(b)},I=v=>{const b=(v||e.edges).map(y=>(y.selected=!1,yt(y.id,!1)));e.hooks.edgesChange.trigger(b)},E=v=>{if(!v||!v.length)return m([],!1);const p=v.reduce((b,y)=>{const z=yt(y.id,!1);return Tt(y)?b.nodes.push(z):b.edges.push(z),b},{nodes:[],edges:[]});p.nodes.length&&e.hooks.nodesChange.trigger(p.nodes),p.edges.length&&e.hooks.edgesChange.trigger(p.edges)},$=v=>{var p;(p=e.d3Zoom)==null||p.scaleExtent([v,e.maxZoom]),e.minZoom=v},O=v=>{var p;(p=e.d3Zoom)==null||p.scaleExtent([e.minZoom,v]),e.maxZoom=v},te=v=>{var p;(p=e.d3Zoom)==null||p.translateExtent(v),e.translateExtent=v},P=v=>{e.nodeExtent=v,o()},D=v=>{var p;(p=e.d3Zoom)==null||p.clickDistance(v)},W=v=>{e.nodesDraggable=v,e.nodesConnectable=v,e.elementsSelectable=v},F=v=>{const p=v instanceof Function?v(e.nodes):v;!e.initialized&&!p.length||(e.nodes=zi(p,u,e.hooks.error.trigger))},R=v=>{const p=v instanceof Function?v(e.edges):v;if(!e.initialized&&!p.length)return;const b=wo(p,e.isValidConnection,u,c,e.hooks.error.trigger,e.defaultEdgeOptions,e.nodes,e.edges);yo(e.connectionLookup,n.value,b),e.edges=b},x=v=>{const p=v instanceof Function?v([...e.nodes,...e.edges]):v;!e.initialized&&!p.length||(F(p.filter(Tt)),R(p.filter(bt)))},K=v=>{let p=v instanceof Function?v(e.nodes):v;p=Array.isArray(p)?p:[p];const b=zi(p,u,e.hooks.error.trigger),y=[];for(const z of b)y.push(Ci(z));y.length&&e.hooks.nodesChange.trigger(y)},w=v=>{let p=v instanceof Function?v(e.edges):v;p=Array.isArray(p)?p:[p];const b=wo(p,e.isValidConnection,u,c,e.hooks.error.trigger,e.defaultEdgeOptions,e.nodes,e.edges),y=[];for(const z of b)y.push(Ci(z));y.length&&e.hooks.edgesChange.trigger(y)},A=(v,p=!0,b=!1)=>{const y=v instanceof Function?v(e.nodes):v,z=Array.isArray(y)?y:[y],le=[],ie=[];function Q(me){const Ce=s(me);for(const Ae of Ce)(!Re(Ae.deletable)||Ae.deletable)&&ie.push(Ti(Ae.id,Ae.source,Ae.target,Ae.sourceHandle,Ae.targetHandle))}function he(me){const Ce=[];for(const Ae of e.nodes)Ae.parentNode===me&&Ce.push(Ae);if(Ce.length){for(const Ae of Ce)le.push($i(Ae.id));p&&Q(Ce);for(const Ae of Ce)he(Ae.id)}}for(const me of z){const Ce=typeof me=="string"?u(me):me;Ce&&(Re(Ce.deletable)&&!Ce.deletable||(le.push($i(Ce.id)),p&&Q([Ce]),b&&he(Ce.id)))}ie.length&&e.hooks.edgesChange.trigger(ie),le.length&&e.hooks.nodesChange.trigger(le)},S=v=>{const p=v instanceof Function?v(e.edges):v,b=Array.isArray(p)?p:[p],y=[];for(const z of b){const le=typeof z=="string"?c(z):z;le&&(Re(le.deletable)&&!le.deletable||y.push(Ti(typeof z=="string"?z:z.id,le.source,le.target,le.sourceHandle,le.targetHandle)))}e.hooks.edgesChange.trigger(y)},H=(v,p,b=!0)=>{const y=c(v.id);if(!y)return!1;const z=e.edges.indexOf(y),le=Td(v,p,y,b,e.hooks.error.trigger);if(le){const[ie]=wo([le],e.isValidConnection,u,c,e.hooks.error.trigger,e.defaultEdgeOptions,e.nodes,e.edges);return e.edges=e.edges.map((Q,he)=>he===z?ie:Q),yo(e.connectionLookup,n.value,[ie]),ie}return!1},L=(v,p,b={replace:!1})=>{const y=c(v);if(!y)return;const z=typeof p=="function"?p(y):p;y.data=b.replace?z:{...y.data,...z}},J=v=>Ni(v,e.nodes),Y=v=>{const p=Ni(v,e.edges);return yo(e.connectionLookup,n.value,p),p},se=(v,p,b={replace:!1})=>{const y=u(v);if(!y)return;const z=typeof p=="function"?p(y):p;b.replace?e.nodes.splice(e.nodes.indexOf(y),1,z):Object.assign(y,z)},ce=(v,p,b={replace:!1})=>{const y=u(v);if(!y)return;const z=typeof p=="function"?p(y):p;y.data=b.replace?z:{...y.data,...z}},Ee=(v,p,b=!1)=>{b?e.connectionClickStartHandle=v:e.connectionStartHandle=v,e.connectionEndHandle=null,e.connectionStatus=null,p&&(e.connectionPosition=p)},q=(v,p=null,b=null)=>{e.connectionStartHandle&&(e.connectionPosition=v,e.connectionEndHandle=p,e.connectionStatus=b)},Z=(v,p)=>{e.connectionPosition={x:Number.NaN,y:Number.NaN},e.connectionEndHandle=null,e.connectionStatus=null,p?e.connectionClickStartHandle=null:e.connectionStartHandle=null},de=v=>{const p=id(v),b=p?null:jt(v)?v:u(v.id);return!p&&!b?[null,null,p]:[p?v:Vn(b),b,p]},ke=(v,p=!0,b=e.nodes)=>{const[y,z,le]=de(v);if(!y)return[];const ie=[];for(const Q of b||e.nodes){if(!le&&(Q.id===z.id||!Q.computedPosition))continue;const he=Vn(Q),me=Hn(he,y);(p&&me>0||me>=Number(y.width)*Number(y.height))&&ie.push(Q)}return ie},Ne=(v,p,b=!0)=>{const[y]=de(v);if(!y)return!1;const z=Hn(y,p);return b&&z>0||z>=Number(y.width)*Number(y.height)},ye=v=>{const{viewport:p,dimensions:b,d3Zoom:y,d3Selection:z,translateExtent:le}=e;if(!y||!z||!v.x&&!v.y)return!1;const ie=Yt.translate(p.x+v.x,p.y+v.y).scale(p.zoom),Q=[[0,0],[b.width,b.height]],he=y.constrain()(ie,Q,le),me=e.viewport.x!==he.x||e.viewport.y!==he.y||e.viewport.zoom!==he.k;return y.transform(z,he),me},$e=v=>{const p=v instanceof Function?v(e):v,b=["d3Zoom","d3Selection","d3ZoomHandler","viewportRef","vueFlowRef","dimensions","hooks"];Re(p.defaultEdgeOptions)&&(e.defaultEdgeOptions=p.defaultEdgeOptions);const y=p.modelValue||p.nodes||p.edges?[]:void 0;y&&(p.modelValue&&y.push(...p.modelValue),p.nodes&&y.push(...p.nodes),p.edges&&y.push(...p.edges),x(y));const z=()=>{Re(p.maxZoom)&&O(p.maxZoom),Re(p.minZoom)&&$(p.minZoom),Re(p.translateExtent)&&te(p.translateExtent)};for(const le of Object.keys(p)){const ie=le,Q=p[ie];![...Gd,...b].includes(ie)&&Re(Q)&&(e[ie]=Q)}xo(()=>e.d3Zoom).not.toBeNull().then(z),e.initialized||(e.initialized=!0)};return{updateNodePositions:d,updateNodeDimensions:g,setElements:x,setNodes:F,setEdges:R,addNodes:K,addEdges:w,removeNodes:A,removeEdges:S,findNode:u,findEdge:c,updateEdge:H,updateEdgeData:L,updateNode:se,updateNodeData:ce,applyEdgeChanges:Y,applyNodeChanges:J,addSelectedElements:N,addSelectedNodes:M,addSelectedEdges:k,setMinZoom:$,setMaxZoom:O,setTranslateExtent:te,setNodeExtent:P,setPaneClickDistance:D,removeSelectedElements:E,removeSelectedNodes:T,removeSelectedEdges:I,startConnection:Ee,updateConnection:q,endConnection:Z,setInteractive:W,setState:$e,getIntersectingNodes:ke,getIncomers:r,getOutgoers:a,getConnectedEdges:s,getHandleConnections:l,isNodeIntersecting:Ne,panBy:ye,fitView:v=>i.value.fitView(v),zoomIn:v=>i.value.zoomIn(v),zoomOut:v=>i.value.zoomOut(v),zoomTo:(v,p)=>i.value.zoomTo(v,p),setViewport:(v,p)=>i.value.setViewport(v,p),setTransform:(v,p)=>i.value.setTransform(v,p),getViewport:()=>i.value.getViewport(),getTransform:()=>i.value.getTransform(),setCenter:(v,p,b)=>i.value.setCenter(v,p,b),fitBounds:(v,p)=>i.value.fitBounds(v,p),project:v=>i.value.project(v),screenToFlowCoordinate:v=>i.value.screenToFlowCoordinate(v),flowToScreenCoordinate:v=>i.value.flowToScreenCoordinate(v),toObject:()=>{const v=[],p=[];for(const b of e.nodes){const{computedPosition:y,handleBounds:z,selected:le,dimensions:ie,isParent:Q,resizing:he,dragging:me,events:Ce,...Ae}=b;v.push(Ae)}for(const b of e.edges){const{selected:y,sourceNode:z,targetNode:le,events:ie,...Q}=b;p.push(Q)}return JSON.parse(JSON.stringify({nodes:v,edges:p,position:[e.viewport.x,e.viewport.y],zoom:e.viewport.zoom,viewport:e.viewport}))},fromObject:v=>new Promise(p=>{const{nodes:b,edges:y,position:z,zoom:le,viewport:ie}=v;if(b&&F(b),y&&R(y),ie?.x&&ie?.y||z){const Q=ie?.x||z[0],he=ie?.y||z[1],me=ie?.zoom||le||e.viewport.zoom;return xo(()=>i.value.viewportInitialized).toBe(!0).then(()=>{i.value.setViewport({x:Q,y:he,zoom:me}).then(()=>{p(!0)})})}else p(!0)}),updateNodeInternals:o,viewportHelper:i,$reset:()=>{const v=Jr();if(e.edges=[],e.nodes=[],e.d3Zoom&&e.d3Selection){const p=Yt.translate(v.defaultViewport.x??0,v.defaultViewport.y??0).scale(Dt(v.defaultViewport.zoom??1,v.minZoom,v.maxZoom)),b=e.viewportRef.getBoundingClientRect(),y=[[0,0],[b.width,b.height]],z=e.d3Zoom.constrain()(p,y,v.translateExtent);e.d3Zoom.transform(e.d3Selection,z)}$e(v)},$destroy:()=>{}}}const Wd=["data-id","data-handleid","data-nodeid","data-handlepos"],Ud={name:"Handle",compatConfig:{MODE:3}},Ke=Fe({...Ud,props:{id:{default:null},type:{},position:{default:()=>ne.Top},isValidConnection:{type:Function},connectable:{type:[Boolean,Number,String,Function],default:void 0},connectableStart:{type:Boolean,default:!0},connectableEnd:{type:Boolean,default:!0}},setup(e,{expose:t}){const n=Ta(e,["position","connectable","connectableStart","connectableEnd","id"]),i=ze(()=>n.type??"source"),o=ze(()=>n.isValidConnection??null),{id:r,connectionStartHandle:a,connectionClickStartHandle:s,connectionEndHandle:l,vueFlowRef:u,nodesConnectable:c,noDragClassName:d,noPanClassName:g}=Oe(),{id:m,node:M,nodeEl:k,connectedEdges:N}=Kr(),T=ge(),I=ze(()=>typeof e.connectableStart<"u"?e.connectableStart:!0),E=ze(()=>typeof e.connectableEnd<"u"?e.connectableEnd:!0),$=ze(()=>{var R,x,K,w,A,S;return((R=a.value)==null?void 0:R.nodeId)===m&&((x=a.value)==null?void 0:x.id)===e.id&&((K=a.value)==null?void 0:K.type)===i.value||((w=l.value)==null?void 0:w.nodeId)===m&&((A=l.value)==null?void 0:A.id)===e.id&&((S=l.value)==null?void 0:S.type)===i.value}),O=ze(()=>{var R,x,K;return((R=s.value)==null?void 0:R.nodeId)===m&&((x=s.value)==null?void 0:x.id)===e.id&&((K=s.value)==null?void 0:K.type)===i.value}),{handlePointerDown:te,handleClick:P}=Zr({nodeId:m,handleId:e.id,isValidConnection:o,type:i}),D=be(()=>typeof e.connectable=="string"&&e.connectable==="single"?!N.value.some(R=>{const x=R[`${i.value}Handle`];return R[i.value]!==m?!1:x?x===e.id:!0}):typeof e.connectable=="number"?N.value.filter(R=>{const x=R[`${i.value}Handle`];return R[i.value]!==m?!1:x?x===e.id:!0}).length<e.connectable:typeof e.connectable=="function"?e.connectable(M,N.value):Re(e.connectable)?e.connectable:c.value);ct(()=>{var R;if(!M.dimensions.width||!M.dimensions.height)return;const x=(R=M.handleBounds[i.value])==null?void 0:R.find(J=>J.id===e.id);if(!u.value||x)return;const K=u.value.querySelector(".vue-flow__transformationpane");if(!k.value||!T.value||!K||!e.id)return;const w=k.value.getBoundingClientRect(),A=T.value.getBoundingClientRect(),S=window.getComputedStyle(K),{m22:H}=new window.DOMMatrixReadOnly(S.transform),L={id:e.id,position:e.position,x:(A.left-w.left)/H,y:(A.top-w.top)/H,type:i.value,nodeId:m,...Kn(T.value)};M.handleBounds[i.value]=[...M.handleBounds[i.value]??[],L]});function W(R){const x=Ko(R);D.value&&I.value&&(x&&R.button===0||!x)&&te(R)}function F(R){!m||!s.value&&!I.value||D.value&&P(R)}return t({handleClick:P,handlePointerDown:te,onClick:F,onPointerDown:W}),(R,x)=>(re(),ve("div",{ref_key:"handle",ref:T,"data-id":`${V(r)}-${V(m)}-${e.id}-${i.value}`,"data-handleid":e.id,"data-nodeid":V(m),"data-handlepos":R.position,class:ut(["vue-flow__handle",[`vue-flow__handle-${R.position}`,`vue-flow__handle-${e.id}`,V(d),V(g),i.value,{connectable:D.value,connecting:O.value,connectablestart:I.value,connectableend:E.value,connectionindicator:D.value&&(I.value&&!$.value||E.value&&$.value)}]]),onMousedown:W,onTouchstartPassive:W,onClick:F},[Ve(R.$slots,"default",{id:R.id})],42,Wd))}}),jn=function({sourcePosition:e=ne.Bottom,targetPosition:t=ne.Top,label:n,connectable:i=!0,isValidTargetPos:o,isValidSourcePos:r,data:a}){const s=a.label??n;return[Me(Ke,{type:"target",position:t,connectable:i,isValidConnection:o}),typeof s!="string"&&s?Me(s):Me(Ze,[s]),Me(Ke,{type:"source",position:e,connectable:i,isValidConnection:r})]};jn.props=["sourcePosition","targetPosition","label","isValidTargetPos","isValidSourcePos","connectable","data"];jn.inheritAttrs=!1;jn.compatConfig={MODE:3};const Zd=jn,eo=function({targetPosition:e=ne.Top,label:t,connectable:n=!0,isValidTargetPos:i,data:o}){const r=o.label??t;return[Me(Ke,{type:"target",position:e,connectable:n,isValidConnection:i}),typeof r!="string"&&r?Me(r):Me(Ze,[r])]};eo.props=["targetPosition","label","isValidTargetPos","connectable","data"];eo.inheritAttrs=!1;eo.compatConfig={MODE:3};const Kd=eo,to=function({sourcePosition:e=ne.Bottom,label:t,connectable:n=!0,isValidSourcePos:i,data:o}){const r=o.label??t;return[typeof r!="string"&&r?Me(r):Me(Ze,[r]),Me(Ke,{type:"source",position:e,connectable:n,isValidConnection:i})]};to.props=["sourcePosition","label","isValidSourcePos","connectable","data"];to.inheritAttrs=!1;to.compatConfig={MODE:3};const qd=to,Jd=["transform"],Qd=["width","height","x","y","rx","ry"],jd=["y"],ef={name:"EdgeText",compatConfig:{MODE:3}},tf=Fe({...ef,props:{x:{},y:{},label:{},labelStyle:{default:()=>({})},labelShowBg:{type:Boolean,default:!0},labelBgStyle:{default:()=>({})},labelBgPadding:{default:()=>[2,4]},labelBgBorderRadius:{default:2}},setup(e){const t=ge({x:0,y:0,width:0,height:0}),n=ge(null),i=be(()=>`translate(${e.x-t.value.width/2} ${e.y-t.value.height/2})`);ct(o),De([()=>e.x,()=>e.y,n,()=>e.label],o);function o(){if(!n.value)return;const r=n.value.getBBox();(r.width!==t.value.width||r.height!==t.value.height)&&(t.value=r)}return(r,a)=>(re(),ve("g",{transform:i.value,class:"vue-flow__edge-textwrapper"},[r.labelShowBg?(re(),ve("rect",{key:0,class:"vue-flow__edge-textbg",width:`${t.value.width+2*r.labelBgPadding[0]}px`,height:`${t.value.height+2*r.labelBgPadding[1]}px`,x:-r.labelBgPadding[0],y:-r.labelBgPadding[1],style:Ge(r.labelBgStyle),rx:r.labelBgBorderRadius,ry:r.labelBgBorderRadius},null,12,Qd)):Pe("",!0),G("text",Wi(r.$attrs,{ref_key:"el",ref:n,class:"vue-flow__edge-text",y:t.value.height/2,dy:"0.3em",style:r.labelStyle}),[Ve(r.$slots,"default",{},()=>[typeof r.label!="string"?(re(),We(Ot(r.label),{key:0})):(re(),ve(Ze,{key:1},[Xe(Qe(r.label),1)],64))])],16,jd)],8,Jd))}}),nf=["id","d","marker-end","marker-start"],of=["d","stroke-width"],rf={name:"BaseEdge",inheritAttrs:!1,compatConfig:{MODE:3}},no=Fe({...rf,props:{id:{},labelX:{},labelY:{},path:{},label:{},markerStart:{},markerEnd:{},interactionWidth:{default:20},labelStyle:{},labelShowBg:{type:Boolean},labelBgStyle:{},labelBgPadding:{},labelBgBorderRadius:{}},setup(e,{expose:t}){const n=ge(null),i=ge(null),o=ge(null),r=$a();return t({pathEl:n,interactionEl:i,labelEl:o}),(a,s)=>(re(),ve(Ze,null,[G("path",Wi(V(r),{id:a.id,ref_key:"pathEl",ref:n,d:a.path,class:"vue-flow__edge-path","marker-end":a.markerEnd,"marker-start":a.markerStart}),null,16,nf),a.interactionWidth?(re(),ve("path",{key:0,ref_key:"interactionEl",ref:i,fill:"none",d:a.path,"stroke-width":a.interactionWidth,"stroke-opacity":0,class:"vue-flow__edge-interaction"},null,8,of)):Pe("",!0),a.label&&a.labelX&&a.labelY?(re(),We(tf,{key:1,ref_key:"labelEl",ref:o,x:a.labelX,y:a.labelY,label:a.label,"label-show-bg":a.labelShowBg,"label-bg-style":a.labelBgStyle,"label-bg-padding":a.labelBgPadding,"label-bg-border-radius":a.labelBgBorderRadius,"label-style":a.labelStyle},null,8,["x","y","label","label-show-bg","label-bg-style","label-bg-padding","label-bg-border-radius","label-style"])):Pe("",!0)],64))}});function Qr({sourceX:e,sourceY:t,targetX:n,targetY:i}){const o=Math.abs(n-e)/2,r=n<e?n+o:n-o,a=Math.abs(i-t)/2,s=i<t?i+a:i-a;return[r,s,o,a]}function jr({sourceX:e,sourceY:t,targetX:n,targetY:i,sourceControlX:o,sourceControlY:r,targetControlX:a,targetControlY:s}){const l=e*.125+o*.375+a*.375+n*.125,u=t*.125+r*.375+s*.375+i*.125,c=Math.abs(l-e),d=Math.abs(u-t);return[l,u,c,d]}function xn(e,t){return e>=0?.5*e:t*25*Math.sqrt(-e)}function Vi({pos:e,x1:t,y1:n,x2:i,y2:o,c:r}){let a,s;switch(e){case ne.Left:a=t-xn(t-i,r),s=n;break;case ne.Right:a=t+xn(i-t,r),s=n;break;case ne.Top:a=t,s=n-xn(n-o,r);break;case ne.Bottom:a=t,s=n+xn(o-n,r);break}return[a,s]}function ea(e){const{sourceX:t,sourceY:n,sourcePosition:i=ne.Bottom,targetX:o,targetY:r,targetPosition:a=ne.Top,curvature:s=.25}=e,[l,u]=Vi({pos:i,x1:t,y1:n,x2:o,y2:r,c:s}),[c,d]=Vi({pos:a,x1:o,y1:r,x2:t,y2:n,c:s}),[g,m,M,k]=jr({sourceX:t,sourceY:n,targetX:o,targetY:r,sourceControlX:l,sourceControlY:u,targetControlX:c,targetControlY:d});return[`M${t},${n} C${l},${u} ${c},${d} ${o},${r}`,g,m,M,k]}function Hi({pos:e,x1:t,y1:n,x2:i,y2:o}){let r,a;switch(e){case ne.Left:case ne.Right:r=.5*(t+i),a=n;break;case ne.Top:case ne.Bottom:r=t,a=.5*(n+o);break}return[r,a]}function ta(e){const{sourceX:t,sourceY:n,sourcePosition:i=ne.Bottom,targetX:o,targetY:r,targetPosition:a=ne.Top}=e,[s,l]=Hi({pos:i,x1:t,y1:n,x2:o,y2:r}),[u,c]=Hi({pos:a,x1:o,y1:r,x2:t,y2:n}),[d,g,m,M]=jr({sourceX:t,sourceY:n,targetX:o,targetY:r,sourceControlX:s,sourceControlY:l,targetControlX:u,targetControlY:c});return[`M${t},${n} C${s},${l} ${u},${c} ${o},${r}`,d,g,m,M]}const Ri={[ne.Left]:{x:-1,y:0},[ne.Right]:{x:1,y:0},[ne.Top]:{x:0,y:-1},[ne.Bottom]:{x:0,y:1}};function af({source:e,sourcePosition:t=ne.Bottom,target:n}){return t===ne.Left||t===ne.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1}}function Li(e,t){return Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2)}function sf({source:e,sourcePosition:t=ne.Bottom,target:n,targetPosition:i=ne.Top,center:o,offset:r}){const a=Ri[t],s=Ri[i],l={x:e.x+a.x*r,y:e.y+a.y*r},u={x:n.x+s.x*r,y:n.y+s.y*r},c=af({source:l,sourcePosition:t,target:u}),d=c.x!==0?"x":"y",g=c[d];let m,M,k;const N={x:0,y:0},T={x:0,y:0},[I,E,$,O]=Qr({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(a[d]*s[d]===-1){M=o.x??I,k=o.y??E;const P=[{x:M,y:l.y},{x:M,y:u.y}],D=[{x:l.x,y:k},{x:u.x,y:k}];a[d]===g?m=d==="x"?P:D:m=d==="x"?D:P}else{const P=[{x:l.x,y:u.y}],D=[{x:u.x,y:l.y}];if(d==="x"?m=a.x===g?D:P:m=a.y===g?P:D,t===i){const K=Math.abs(e[d]-n[d]);if(K<=r){const w=Math.min(r-1,r-K);a[d]===g?N[d]=(l[d]>e[d]?-1:1)*w:T[d]=(u[d]>n[d]?-1:1)*w}}if(t!==i){const K=d==="x"?"y":"x",w=a[d]===s[K],A=l[K]>u[K],S=l[K]<u[K];(a[d]===1&&(!w&&A||w&&S)||a[d]!==1&&(!w&&S||w&&A))&&(m=d==="x"?P:D)}const W={x:l.x+N.x,y:l.y+N.y},F={x:u.x+T.x,y:u.y+T.y},R=Math.max(Math.abs(W.x-m[0].x),Math.abs(F.x-m[0].x)),x=Math.max(Math.abs(W.y-m[0].y),Math.abs(F.y-m[0].y));R>=x?(M=(W.x+F.x)/2,k=m[0].y):(M=m[0].x,k=(W.y+F.y)/2)}return[[e,{x:l.x+N.x,y:l.y+N.y},...m,{x:u.x+T.x,y:u.y+T.y},n],M,k,$,O]}function lf(e,t,n,i){const o=Math.min(Li(e,t)/2,Li(t,n)/2,i),{x:r,y:a}=t;if(e.x===r&&r===n.x||e.y===a&&a===n.y)return`L${r} ${a}`;if(e.y===a){const u=e.x<n.x?-1:1,c=e.y<n.y?1:-1;return`L ${r+o*u},${a}Q ${r},${a} ${r},${a+o*c}`}const s=e.x<n.x?1:-1,l=e.y<n.y?-1:1;return`L ${r},${a+o*l}Q ${r},${a} ${r+o*s},${a}`}function Fo(e){const{sourceX:t,sourceY:n,sourcePosition:i=ne.Bottom,targetX:o,targetY:r,targetPosition:a=ne.Top,borderRadius:s=5,centerX:l,centerY:u,offset:c=20}=e,[d,g,m,M,k]=sf({source:{x:t,y:n},sourcePosition:i,target:{x:o,y:r},targetPosition:a,center:{x:l,y:u},offset:c});return[d.reduce((T,I,E)=>{let $;return E>0&&E<d.length-1?$=lf(d[E-1],I,d[E+1],s):$=`${E===0?"M":"L"}${I.x} ${I.y}`,T+=$,T},""),g,m,M,k]}function uf(e){const{sourceX:t,sourceY:n,targetX:i,targetY:o}=e,[r,a,s,l]=Qr({sourceX:t,sourceY:n,targetX:i,targetY:o});return[`M ${t},${n}L ${i},${o}`,r,a,s,l]}const cf=Fe({name:"StraightEdge",props:["label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","markerEnd","markerStart","interactionWidth"],compatConfig:{MODE:3},setup(e,{attrs:t}){return()=>{const[n,i,o]=uf(e);return Me(no,{path:n,labelX:i,labelY:o,...t,...e})}}}),na=cf,df=Fe({name:"SmoothStepEdge",props:["sourcePosition","targetPosition","label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","borderRadius","markerEnd","markerStart","interactionWidth","offset"],compatConfig:{MODE:3},setup(e,{attrs:t}){return()=>{const[n,i,o]=Fo({...e,sourcePosition:e.sourcePosition??ne.Bottom,targetPosition:e.targetPosition??ne.Top});return Me(no,{path:n,labelX:i,labelY:o,...t,...e})}}}),qo=df,ff=Fe({name:"StepEdge",props:["sourcePosition","targetPosition","label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","markerEnd","markerStart","interactionWidth"],setup(e,{attrs:t}){return()=>Me(qo,{...e,...t,borderRadius:0})}}),oa=ff,hf=Fe({name:"BezierEdge",props:["sourcePosition","targetPosition","label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","curvature","markerEnd","markerStart","interactionWidth"],compatConfig:{MODE:3},setup(e,{attrs:t}){return()=>{const[n,i,o]=ea({...e,sourcePosition:e.sourcePosition??ne.Bottom,targetPosition:e.targetPosition??ne.Top});return Me(no,{path:n,labelX:i,labelY:o,...t,...e})}}}),ia=hf,pf=Fe({name:"SimpleBezierEdge",props:["sourcePosition","targetPosition","label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","markerEnd","markerStart","interactionWidth"],compatConfig:{MODE:3},setup(e,{attrs:t}){return()=>{const[n,i,o]=ta({...e,sourcePosition:e.sourcePosition??ne.Bottom,targetPosition:e.targetPosition??ne.Top});return Me(no,{path:n,labelX:i,labelY:o,...t,...e})}}}),ra=pf,gf={input:qd,default:Zd,output:Kd},mf={default:ia,straight:na,step:oa,smoothstep:qo,simplebezier:ra};function vf(e,t,n){const i=be(()=>k=>t.value.get(k)),o=be(()=>k=>n.value.get(k)),r=be(()=>{const k={...mf,...e.edgeTypes},N=Object.keys(k);for(const T of e.edges)T.type&&!N.includes(T.type)&&(k[T.type]=T.type);return k}),a=be(()=>{const k={...gf,...e.nodeTypes},N=Object.keys(k);for(const T of e.nodes)T.type&&!N.includes(T.type)&&(k[T.type]=T.type);return k}),s=be(()=>e.onlyRenderVisibleElements?Br(e.nodes,{x:0,y:0,width:e.dimensions.width,height:e.dimensions.height},e.viewport,!0):e.nodes),l=be(()=>{if(e.onlyRenderVisibleElements){const k=[];for(const N of e.edges){const T=t.value.get(N.source),I=t.value.get(N.target);md({sourcePos:T.computedPosition||{x:0,y:0},targetPos:I.computedPosition||{x:0,y:0},sourceWidth:T.dimensions.width,sourceHeight:T.dimensions.height,targetWidth:I.dimensions.width,targetHeight:I.dimensions.height,width:e.dimensions.width,height:e.dimensions.height,viewport:e.viewport})&&k.push(N)}return k}return e.edges}),u=be(()=>[...s.value,...l.value]),c=be(()=>{const k=[];for(const N of e.nodes)N.selected&&k.push(N);return k}),d=be(()=>{const k=[];for(const N of e.edges)N.selected&&k.push(N);return k}),g=be(()=>[...c.value,...d.value]),m=be(()=>{const k=[];for(const N of e.nodes)N.dimensions.width&&N.dimensions.height&&N.handleBounds!==void 0&&k.push(N);return k}),M=be(()=>s.value.length>0&&m.value.length===s.value.length);return{getNode:i,getEdge:o,getElements:u,getEdgeTypes:r,getNodeTypes:a,getEdges:l,getNodes:s,getSelectedElements:g,getSelectedNodes:c,getSelectedEdges:d,getNodesInitialized:m,areNodesInitialized:M}}class Nt{constructor(){this.currentId=0,this.flows=new Map}static getInstance(){var t;const n=(t=dn())==null?void 0:t.appContext.app,i=n?.config.globalProperties.$vueFlowStorage??Nt.instance;return Nt.instance=i??new Nt,n&&(n.config.globalProperties.$vueFlowStorage=Nt.instance),Nt.instance}set(t,n){return this.flows.set(t,n)}get(t){return this.flows.get(t)}remove(t){return this.flows.delete(t)}create(t,n){const i=Jr(),o=Ca(i),r={};for(const[g,m]of Object.entries(o.hooks)){const M=`on${g.charAt(0).toUpperCase()+g.slice(1)}`;r[M]=m.on}const a={};for(const[g,m]of Object.entries(o.hooks))a[g]=m.trigger;const s=be(()=>{const g=new Map;for(const m of o.nodes)g.set(m.id,m);return g}),l=be(()=>{const g=new Map;for(const m of o.edges)g.set(m.id,m);return g}),u=vf(o,s,l),c=Xd(o,s,l);c.setState({...o,...n});const d={...r,...u,...c,...hs(o),nodeLookup:s,edgeLookup:l,emits:a,id:t,vueFlowVersion:"1.44.0",$destroy:()=>{this.remove(t)}};return this.set(t,d),d}getId(){return`vue-flow-${this.currentId++}`}}function Oe(e){const t=Nt.getInstance(),n=Gi(),i=typeof e=="object",o=i?e:{id:e},r=o.id,a=r??n?.vueFlowId;let s;if(n){const l=Xt(Bi,null);typeof l<"u"&&l!==null&&(!a||l.id===a)&&(s=l)}if(s||a&&(s=t.get(a)),!s||a&&s.id!==a){const l=r??t.getId(),u=t.create(l,o);s=u,(n??Xi(!0)).run(()=>{De(u.applyDefault,(d,g,m)=>{const M=N=>{u.applyNodeChanges(N)},k=N=>{u.applyEdgeChanges(N)};d?(u.onNodesChange(M),u.onEdgesChange(k)):(u.hooks.value.nodesChange.off(M),u.hooks.value.edgesChange.off(k)),m(()=>{u.hooks.value.nodesChange.off(M),u.hooks.value.edgesChange.off(k)})},{immediate:!0}),Gn(()=>{if(s){const d=t.get(s.id);d?d.$destroy():Jn(`No store instance found for id ${s.id} in storage.`)}})})}else i&&s.setState(o);if(n&&(Ht(Bi,s),n.vueFlowId=s.id),i){const l=dn();l?.type.name!=="VueFlow"&&s.emits.error(new Ye(Le.USEVUEFLOW_OPTIONS))}return s}function yf(e){const{emits:t,dimensions:n}=Oe();let i;ct(()=>{const o=e.value,r=()=>{if(!o)return;const a=Kn(o);(a.width===0||a.height===0)&&t.error(new Ye(Le.MISSING_VIEWPORT_DIMENSIONS)),n.value={width:a.width||500,height:a.height||500}};r(),window.addEventListener("resize",r),o&&(i=new ResizeObserver(()=>r()),i.observe(o)),Ui(()=>{window.removeEventListener("resize",r),i&&o&&i.unobserve(o)})})}const wf={name:"UserSelection",compatConfig:{MODE:3}},_f=Fe({...wf,props:{userSelectionRect:{}},setup(e){return(t,n)=>(re(),ve("div",{class:"vue-flow__selection vue-flow__container",style:Ge({width:`${t.userSelectionRect.width}px`,height:`${t.userSelectionRect.height}px`,transform:`translate(${t.userSelectionRect.x}px, ${t.userSelectionRect.y}px)`})},null,4))}}),bf=["tabIndex"],Ef={name:"NodesSelection",compatConfig:{MODE:3}},xf=Fe({...Ef,setup(e){const{emits:t,viewport:n,getSelectedNodes:i,noPanClassName:o,disableKeyboardA11y:r,userSelectionActive:a}=Oe(),s=qr(),l=ge(null),u=Ur({el:l,onStart(M){t.selectionDragStart(M)},onDrag(M){t.selectionDrag(M)},onStop(M){t.selectionDragStop(M)}});ct(()=>{var M;r.value||(M=l.value)==null||M.focus({preventScroll:!0})});const c=be(()=>Fr(i.value)),d=be(()=>({width:`${c.value.width}px`,height:`${c.value.height}px`,top:`${c.value.y}px`,left:`${c.value.x}px`}));function g(M){t.selectionContextMenu({event:M,nodes:i.value})}function m(M){r||Vt[M.key]&&(M.preventDefault(),s({x:Vt[M.key].x,y:Vt[M.key].y},M.shiftKey))}return(M,k)=>!V(a)&&c.value.width&&c.value.height?(re(),ve("div",{key:0,class:ut(["vue-flow__nodesselection vue-flow__container",V(o)]),style:Ge({transform:`translate(${V(n).x}px,${V(n).y}px) scale(${V(n).zoom})`})},[G("div",{ref_key:"el",ref:l,class:ut([{dragging:V(u)},"vue-flow__nodesselection-rect"]),style:Ge(d.value),tabIndex:V(r)?void 0:-1,onContextmenu:g,onKeydown:m},null,46,bf)],6)):Pe("",!0)}});function Sf(e,t){return{x:e.clientX-t.left,y:e.clientY-t.top}}const kf={name:"Pane",compatConfig:{MODE:3}},Nf=Fe({...kf,props:{isSelecting:{type:Boolean},selectionKeyPressed:{type:Boolean}},setup(e){const{vueFlowRef:t,nodes:n,viewport:i,emits:o,userSelectionActive:r,removeSelectedElements:a,userSelectionRect:s,elementsSelectable:l,nodesSelectionActive:u,getSelectedEdges:c,getSelectedNodes:d,removeNodes:g,removeEdges:m,selectionMode:M,deleteKeyCode:k,multiSelectionKeyCode:N,multiSelectionActive:T,edgeLookup:I,nodeLookup:E,connectionLookup:$,defaultEdgeOptions:O,connectionStartHandle:te}=Oe(),P=ge(null),D=ge(new Set),W=ge(new Set),F=ge(),R=ze(()=>l.value&&(e.isSelecting||r.value)),x=ze(()=>te.value!==null);let K=!1,w=!1;const A=on(k,{actInsideInputWithModifier:!1}),S=on(N);De(A,q=>{q&&(g(d.value),m(c.value),u.value=!1)}),De(S,q=>{T.value=q});function H(q,Z){return de=>{de.target===Z&&q?.(de)}}function L(q){if(K||x.value){K=!1;return}o.paneClick(q),a(),u.value=!1}function J(q){q.preventDefault(),q.stopPropagation(),o.paneContextMenu(q)}function Y(q){o.paneScroll(q)}function se(q){var Z,de,ke;if(F.value=(Z=t.value)==null?void 0:Z.getBoundingClientRect(),!l.value||!e.isSelecting||q.button!==0||q.target!==P.value||!F.value)return;(ke=(de=q.target)==null?void 0:de.setPointerCapture)==null||ke.call(de,q.pointerId);const{x:Ne,y:ye}=Sf(q,F.value);w=!0,K=!1,a(),s.value={width:0,height:0,startX:Ne,startY:ye,x:Ne,y:ye},o.selectionStart(q)}function ce(q){var Z;if(!F.value||!s.value)return;K=!0;const{x:de,y:ke}=st(q,F.value),{startX:Ne=0,startY:ye=0}=s.value,$e={startX:Ne,startY:ye,x:de<Ne?de:Ne,y:ke<ye?ke:ye,width:Math.abs(de-Ne),height:Math.abs(ke-ye)},fe=D.value,xe=W.value;D.value=new Set(Br(n.value,$e,i.value,M.value===Uo.Partial,!0).map(v=>v.id)),W.value=new Set;const Se=((Z=O.value)==null?void 0:Z.selectable)??!0;for(const v of D.value){const p=$.value.get(v);if(p)for(const{edgeId:b}of p.values()){const y=I.value.get(b);y&&(y.selectable??Se)&&W.value.add(b)}}if(!Fi(fe,D.value)){const v=wt(E.value,D.value,!0);o.nodesChange(v)}if(!Fi(xe,W.value)){const v=wt(I.value,W.value);o.edgesChange(v)}s.value=$e,r.value=!0,u.value=!1}function Ee(q){var Z;q.button!==0||!w||((Z=q.target)==null||Z.releasePointerCapture(q.pointerId),!r.value&&s.value&&q.target===P.value&&L(q),r.value=!1,s.value=null,u.value=D.value.size>0,o.selectionEnd(q),e.selectionKeyPressed&&(K=!1),w=!1)}return(q,Z)=>(re(),ve("div",{ref_key:"container",ref:P,class:ut(["vue-flow__pane vue-flow__container",{selection:q.isSelecting}]),onClick:Z[0]||(Z[0]=de=>R.value?void 0:H(L,P.value)(de)),onContextmenu:Z[1]||(Z[1]=de=>H(J,P.value)(de)),onWheelPassive:Z[2]||(Z[2]=de=>H(Y,P.value)(de)),onPointerenter:Z[3]||(Z[3]=de=>R.value?void 0:V(o).paneMouseEnter(de)),onPointerdown:Z[4]||(Z[4]=de=>R.value?se(de):V(o).paneMouseMove(de)),onPointermove:Z[5]||(Z[5]=de=>R.value?ce(de):V(o).paneMouseMove(de)),onPointerup:Z[6]||(Z[6]=de=>R.value?Ee(de):void 0),onPointerleave:Z[7]||(Z[7]=de=>V(o).paneMouseLeave(de))},[Ve(q.$slots,"default"),V(r)&&V(s)?(re(),We(_f,{key:0,"user-selection-rect":V(s)},null,8,["user-selection-rect"])):Pe("",!0),V(u)&&V(d).length?(re(),We(xf,{key:1})):Pe("",!0)],34))}}),Cf={name:"Transform",compatConfig:{MODE:3}},$f=Fe({...Cf,setup(e){const{viewport:t,fitViewOnInit:n,fitViewOnInitDone:i}=Oe(),o=be(()=>n.value?!i.value:!1),r=be(()=>`translate(${t.value.x}px,${t.value.y}px) scale(${t.value.zoom})`);return(a,s)=>(re(),ve("div",{class:"vue-flow__transformationpane vue-flow__container",style:Ge({transform:r.value,opacity:o.value?0:void 0})},[Ve(a.$slots,"default")],4))}}),Tf={name:"Viewport",compatConfig:{MODE:3}},Mf=Fe({...Tf,setup(e){const{minZoom:t,maxZoom:n,defaultViewport:i,translateExtent:o,zoomActivationKeyCode:r,selectionKeyCode:a,panActivationKeyCode:s,panOnScroll:l,panOnScrollMode:u,panOnScrollSpeed:c,panOnDrag:d,zoomOnDoubleClick:g,zoomOnPinch:m,zoomOnScroll:M,preventScrolling:k,noWheelClassName:N,noPanClassName:T,emits:I,connectionStartHandle:E,userSelectionActive:$,paneDragging:O,d3Zoom:te,d3Selection:P,d3ZoomHandler:D,viewport:W,viewportRef:F,paneClickDistance:R}=Oe();yf(F);const x=In(!1),K=In(!1);let w=null,A=!1,S=0,H={x:0,y:0,zoom:0};const L=on(s),J=on(a),Y=on(r),se=ze(()=>(!J.value||J.value&&a.value===!0)&&(L.value||d.value)),ce=ze(()=>L.value||l.value),Ee=ze(()=>J.value||a.value===!0&&se.value!==!0);ct(()=>{if(!F.value){Jn("Viewport element is missing");return}const ye=F.value,$e=ye.getBoundingClientRect(),fe=Jc().clickDistance(R.value).scaleExtent([t.value,n.value]).translateExtent(o.value),xe=tt(ye).call(fe),Se=xe.on("wheel.zoom"),v=Yt.translate(i.value.x??0,i.value.y??0).scale(Dt(i.value.zoom??1,t.value,n.value)),p=[[0,0],[$e.width,$e.height]],b=fe.constrain()(v,p,o.value);fe.transform(xe,b),fe.wheelDelta(Z),te.value=fe,P.value=xe,D.value=Se,W.value={x:b.x,y:b.y,zoom:b.k},fe.on("start",y=>{var z;if(!y.sourceEvent)return null;S=y.sourceEvent.button,x.value=!0;const le=ke(y.transform);((z=y.sourceEvent)==null?void 0:z.type)==="mousedown"&&(O.value=!0),H=le,I.viewportChangeStart(le),I.moveStart({event:y,flowTransform:le})}),fe.on("end",y=>{if(!y.sourceEvent)return null;if(x.value=!1,O.value=!1,q(se.value,S??0)&&!A&&I.paneContextMenu(y.sourceEvent),A=!1,de(H,y.transform)){const z=ke(y.transform);H=z,I.viewportChangeEnd(z),I.moveEnd({event:y,flowTransform:z})}}),fe.filter(y=>{var z;const le=Y.value||M.value,ie=m.value&&y.ctrlKey,Q=y.button;if(Q===1&&y.type==="mousedown"&&(Ne(y,"vue-flow__node")||Ne(y,"vue-flow__edge")))return!0;if(!se.value&&!le&&!ce.value&&!g.value&&!m.value||$.value||!g.value&&y.type==="dblclick"||Ne(y,N.value)&&y.type==="wheel"||Ne(y,T.value)&&(y.type!=="wheel"||ce.value&&y.type==="wheel"&&!Y.value)||!m.value&&y.ctrlKey&&y.type==="wheel"||!le&&!ce.value&&!ie&&y.type==="wheel")return!1;if(!m&&y.type==="touchstart"&&((z=y.touches)==null?void 0:z.length)>1)return y.preventDefault(),!1;if(!se.value&&(y.type==="mousedown"||y.type==="touchstart")||a.value===!0&&Array.isArray(d.value)&&d.value.includes(0)&&Q===0||Array.isArray(d.value)&&!d.value.includes(Q)&&(y.type==="mousedown"||y.type==="touchstart"))return!1;const he=Array.isArray(d.value)&&d.value.includes(Q)||a.value===!0&&Array.isArray(d.value)&&!d.value.includes(0)||!Q||Q<=1;return(!y.ctrlKey||L.value||y.type==="wheel")&&he}),De([$,se],()=>{$.value&&!x.value?fe.on("zoom",null):$.value||fe.on("zoom",y=>{W.value={x:y.transform.x,y:y.transform.y,zoom:y.transform.k};const z=ke(y.transform);A=q(se.value,S??0),I.viewportChange(z),I.move({event:y,flowTransform:z})})},{immediate:!0}),De([$,ce,u,Y,m,k,N],()=>{ce.value&&!Y.value&&!$.value?xe.on("wheel.zoom",y=>{if(Ne(y,N.value))return!1;const z=Y.value||M.value,le=m.value&&y.ctrlKey;if(!(!k.value||ce.value||z||le))return!1;y.preventDefault(),y.stopImmediatePropagation();const Q=xe.property("__zoom").k||1,he=Rn();if(!L.value&&y.ctrlKey&&m.value&&he){const Et=rt(y),oo=Z(y),Wt=Q*2**oo;fe.scaleTo(xe,Wt,Et,y);return}const me=y.deltaMode===1?20:1;let Ce=u.value===nn.Vertical?0:y.deltaX*me,Ae=u.value===nn.Horizontal?0:y.deltaY*me;!he&&y.shiftKey&&u.value!==nn.Vertical&&!Ce&&Ae&&(Ce=Ae,Ae=0),fe.translateBy(xe,-(Ce/Q)*c.value,-(Ae/Q)*c.value);const Je=ke(xe.property("__zoom"));w&&clearTimeout(w),K.value?(I.move({event:y,flowTransform:Je}),I.viewportChange(Je),w=setTimeout(()=>{I.moveEnd({event:y,flowTransform:Je}),I.viewportChangeEnd(Je),K.value=!1},150)):(K.value=!0,I.moveStart({event:y,flowTransform:Je}),I.viewportChangeStart(Je))},{passive:!1}):typeof Se<"u"&&xe.on("wheel.zoom",function(y,z){const le=!k.value&&y.type==="wheel"&&!y.ctrlKey,ie=Y.value||M.value,Q=m.value&&y.ctrlKey;if(!ie&&!l.value&&!Q&&y.type==="wheel"||le||Ne(y,N.value))return null;y.preventDefault(),Se.call(this,y,z)},{passive:!1})},{immediate:!0})});function q(ye,$e){return $e===2&&Array.isArray(ye)&&ye.includes(2)}function Z(ye){const $e=ye.ctrlKey&&Rn()?10:1;return-ye.deltaY*(ye.deltaMode===1?.05:ye.deltaMode?1:.002)*$e}function de(ye,$e){return ye.x!==$e.x&&!Number.isNaN($e.x)||ye.y!==$e.y&&!Number.isNaN($e.y)||ye.zoom!==$e.k&&!Number.isNaN($e.k)}function ke(ye){return{x:ye.x,y:ye.y,zoom:ye.k}}function Ne(ye,$e){return ye.target.closest(`.${$e}`)}return(ye,$e)=>(re(),ve("div",{ref_key:"viewportRef",ref:F,class:"vue-flow__viewport vue-flow__container"},[X(Nf,{"is-selecting":Ee.value,"selection-key-pressed":V(J),class:ut({connecting:!!V(E),dragging:V(O),draggable:V(d)===!0||Array.isArray(V(d))&&V(d).includes(0)})},{default:pe(()=>[X($f,null,{default:pe(()=>[Ve(ye.$slots,"default")]),_:3})]),_:3},8,["is-selecting","selection-key-pressed","class"])],512))}}),If=["id"],Df=["id"],Af=["id"],Pf={name:"A11yDescriptions",compatConfig:{MODE:3}},zf=Fe({...Pf,setup(e){const{id:t,disableKeyboardA11y:n,ariaLiveMessage:i}=Oe();return(o,r)=>(re(),ve(Ze,null,[G("div",{id:`${V(Tr)}-${V(t)}`,style:{display:"none"}}," Press enter or space to select a node. "+Qe(V(n)?"":"You can then use the arrow keys to move the node around.")+" You can then use the arrow keys to move the node around, press delete to remove it and press escape to cancel. ",9,If),G("div",{id:`${V(Mr)}-${V(t)}`,style:{display:"none"}}," Press enter or space to select an edge. You can then press delete to remove it or press escape to cancel. ",8,Df),V(n)?Pe("",!0):(re(),ve("div",{key:0,id:`${V(od)}-${V(t)}`,"aria-live":"assertive","aria-atomic":"true",style:{position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)","clip-path":"inset(100%)"}},Qe(V(i)),9,Af))],64))}});function Of(){const e=Oe();De(()=>e.viewportHelper.value.viewportInitialized,t=>{t&&setTimeout(()=>{e.emits.init(e),e.emits.paneReady(e)},1)})}function Ff(e,t,n){return n===ne.Left?e-t:n===ne.Right?e+t:e}function Bf(e,t,n){return n===ne.Top?e-t:n===ne.Bottom?e+t:e}const Jo=function({radius:e=10,centerX:t=0,centerY:n=0,position:i=ne.Top,type:o}){return Me("circle",{class:`vue-flow__edgeupdater vue-flow__edgeupdater-${o}`,cx:Ff(t,e,i),cy:Bf(n,e,i),r:e,stroke:"transparent",fill:"transparent"})};Jo.props=["radius","centerX","centerY","position","type"];Jo.compatConfig={MODE:3};const Yi=Jo,Vf=Fe({name:"Edge",compatConfig:{MODE:3},props:["id"],setup(e){const{id:t,addSelectedEdges:n,connectionMode:i,edgeUpdaterRadius:o,emits:r,nodesSelectionActive:a,noPanClassName:s,getEdgeTypes:l,removeSelectedEdges:u,findEdge:c,findNode:d,isValidConnection:g,multiSelectionActive:m,disableKeyboardA11y:M,elementsSelectable:k,edgesUpdatable:N,edgesFocusable:T,hooks:I}=Oe(),E=be(()=>c(e.id)),{emit:$,on:O}=Ad(E.value,r),te=Xt(Qn),P=dn(),D=ge(!1),W=ge(!1),F=ge(""),R=ge(null),x=ge("source"),K=ge(null),w=ze(()=>typeof E.value.selectable>"u"?k.value:E.value.selectable),A=ze(()=>typeof E.value.updatable>"u"?N.value:E.value.updatable),S=ze(()=>typeof E.value.focusable>"u"?T.value:E.value.focusable);Ht(Md,e.id),Ht(Id,K);const H=be(()=>E.value.class instanceof Function?E.value.class(E.value):E.value.class),L=be(()=>E.value.style instanceof Function?E.value.style(E.value):E.value.style),J=be(()=>{const p=E.value.type||"default",b=te?.[`edge-${p}`];if(b)return b;let y=E.value.template??l.value[p];if(typeof y=="string"&&P){const z=Object.keys(P.appContext.components);z&&z.includes(p)&&(y=Zi(p,!1))}return y&&typeof y!="string"?y:(r.error(new Ye(Le.EDGE_TYPE_MISSING,y)),!1)}),{handlePointerDown:Y}=Zr({nodeId:F,handleId:R,type:x,isValidConnection:g,edgeUpdaterType:x,onEdgeUpdate:Ee,onEdgeUpdateEnd:q});return()=>{const p=d(E.value.source),b=d(E.value.target),y="pathOptions"in E.value?E.value.pathOptions:{};if(!p&&!b)return r.error(new Ye(Le.EDGE_SOURCE_TARGET_MISSING,E.value.id,E.value.source,E.value.target)),null;if(!p)return r.error(new Ye(Le.EDGE_SOURCE_MISSING,E.value.id,E.value.source)),null;if(!b)return r.error(new Ye(Le.EDGE_TARGET_MISSING,E.value.id,E.value.target)),null;if(!E.value||E.value.hidden||p.hidden||b.hidden)return null;let z;i.value===_t.Strict?z=p.handleBounds.source:z=[...p.handleBounds.source||[],...p.handleBounds.target||[]];const le=Ii(z,E.value.sourceHandle);let ie;i.value===_t.Strict?ie=b.handleBounds.target:ie=[...b.handleBounds.target||[],...b.handleBounds.source||[]];const Q=Ii(ie,E.value.targetHandle),he=le?.position||ne.Bottom,me=Q?.position||ne.Top,{x:Ce,y:Ae}=Gt(p,le,he),{x:Je,y:Et}=Gt(b,Q,me);return E.value.sourceX=Ce,E.value.sourceY=Ae,E.value.targetX=Je,E.value.targetY=Et,Me("g",{ref:K,key:e.id,"data-id":e.id,class:["vue-flow__edge",`vue-flow__edge-${J.value===!1?"default":E.value.type||"default"}`,s.value,H.value,{updating:D.value,selected:E.value.selected,animated:E.value.animated,inactive:!w.value&&!I.value.edgeClick.hasListeners()}],onClick:de,onContextmenu:ke,onDblclick:Ne,onMouseenter:ye,onMousemove:$e,onMouseleave:fe,onKeyDown:S.value?v:void 0,tabIndex:S.value?0:void 0,"aria-label":E.value.ariaLabel===null?void 0:E.value.ariaLabel||`Edge from ${E.value.source} to ${E.value.target}`,"aria-describedby":S.value?`${Mr}-${t}`:void 0,role:S.value?"button":"img"},[W.value?null:Me(J.value===!1?l.value.default:J.value,{id:e.id,sourceNode:p,targetNode:b,source:E.value.source,target:E.value.target,type:E.value.type,updatable:A.value,selected:E.value.selected,animated:E.value.animated,label:E.value.label,labelStyle:E.value.labelStyle,labelShowBg:E.value.labelShowBg,labelBgStyle:E.value.labelBgStyle,labelBgPadding:E.value.labelBgPadding,labelBgBorderRadius:E.value.labelBgBorderRadius,data:E.value.data,events:{...E.value.events,...O},style:L.value,markerStart:`url('#${cn(E.value.markerStart,t)}')`,markerEnd:`url('#${cn(E.value.markerEnd,t)}')`,sourcePosition:he,targetPosition:me,sourceX:Ce,sourceY:Ae,targetX:Je,targetY:Et,sourceHandleId:E.value.sourceHandle,targetHandleId:E.value.targetHandle,interactionWidth:E.value.interactionWidth,...y}),[A.value==="source"||A.value===!0?[Me("g",{onMousedown:xe,onMouseenter:se,onMouseout:ce},Me(Yi,{position:he,centerX:Ce,centerY:Ae,radius:o.value,type:"source","data-type":"source"}))]:null,A.value==="target"||A.value===!0?[Me("g",{onMousedown:Se,onMouseenter:se,onMouseout:ce},Me(Yi,{position:me,centerX:Je,centerY:Et,radius:o.value,type:"target","data-type":"target"}))]:null]])};function se(){D.value=!0}function ce(){D.value=!1}function Ee(p,b){$.update({event:p,edge:E.value,connection:b})}function q(p){$.updateEnd({event:p,edge:E.value}),W.value=!1}function Z(p,b){p.button===0&&(W.value=!0,F.value=b?E.value.target:E.value.source,R.value=(b?E.value.targetHandle:E.value.sourceHandle)??null,x.value=b?"target":"source",$.updateStart({event:p,edge:E.value}),Y(p))}function de(p){var b;const y={event:p,edge:E.value};w.value&&(a.value=!1,E.value.selected&&m.value?(u([E.value]),(b=K.value)==null||b.blur()):n([E.value])),$.click(y)}function ke(p){$.contextMenu({event:p,edge:E.value})}function Ne(p){$.doubleClick({event:p,edge:E.value})}function ye(p){$.mouseEnter({event:p,edge:E.value})}function $e(p){$.mouseMove({event:p,edge:E.value})}function fe(p){$.mouseLeave({event:p,edge:E.value})}function xe(p){Z(p,!0)}function Se(p){Z(p,!1)}function v(p){var b;!M.value&&Ir.includes(p.key)&&w.value&&(p.key==="Escape"?((b=K.value)==null||b.blur(),u([c(e.id)])):n([c(e.id)]))}}}),Hf=Vf,Rf=Fe({name:"ConnectionLine",compatConfig:{MODE:3},setup(){var e;const{id:t,connectionMode:n,connectionStartHandle:i,connectionEndHandle:o,connectionPosition:r,connectionLineType:a,connectionLineStyle:s,connectionLineOptions:l,connectionStatus:u,viewport:c,findNode:d}=Oe(),g=(e=Xt(Qn))==null?void 0:e["connection-line"],m=be(()=>{var I;return d((I=i.value)==null?void 0:I.nodeId)}),M=be(()=>{var I;return d((I=o.value)==null?void 0:I.nodeId)??null}),k=be(()=>({x:(r.value.x-c.value.x)/c.value.zoom,y:(r.value.y-c.value.y)/c.value.zoom})),N=be(()=>l.value.markerStart?`url(#${cn(l.value.markerStart,t)})`:""),T=be(()=>l.value.markerEnd?`url(#${cn(l.value.markerEnd,t)})`:"");return()=>{var I,E,$;if(!m.value||!i.value)return null;const O=i.value.id,te=i.value.type,P=m.value.handleBounds;let D=P?.[te]??[];if(n.value===_t.Loose){const L=P?.[te==="source"?"target":"source"]??[];D=[...D,...L]}if(!D)return null;const W=(O?D.find(L=>L.id===O):D[0])??null,F=W?.position??ne.Top,{x:R,y:x}=Gt(m.value,W,F);let K=null;M.value&&(n.value===_t.Strict?K=((I=M.value.handleBounds[te==="source"?"target":"source"])==null?void 0:I.find(L=>{var J;return L.id===((J=o.value)==null?void 0:J.id)}))||null:K=((E=[...M.value.handleBounds.source??[],...M.value.handleBounds.target??[]])==null?void 0:E.find(L=>{var J;return L.id===((J=o.value)==null?void 0:J.id)}))||null);const w=(($=o.value)==null?void 0:$.position)??(F?zo[F]:null);if(!F||!w)return null;const A=a.value??l.value.type??kt.Bezier;let S="";const H={sourceX:R,sourceY:x,sourcePosition:F,targetX:k.value.x,targetY:k.value.y,targetPosition:w};return A===kt.Bezier?[S]=ea(H):A===kt.Step?[S]=Fo({...H,borderRadius:0}):A===kt.SmoothStep?[S]=Fo(H):A===kt.SimpleBezier?[S]=ta(H):S=`M${R},${x} ${k.value.x},${k.value.y}`,Me("svg",{class:"vue-flow__edges vue-flow__connectionline vue-flow__container"},Me("g",{class:"vue-flow__connection"},g?Me(g,{sourceX:R,sourceY:x,sourcePosition:F,targetX:k.value.x,targetY:k.value.y,targetPosition:w,sourceNode:m.value,sourceHandle:W,targetNode:M.value,targetHandle:K,markerEnd:T.value,markerStart:N.value,connectionStatus:u.value}):Me("path",{d:S,class:[l.value.class,u,"vue-flow__connection-path"],style:{...s.value,...l.value.style},"marker-end":T.value,"marker-start":N.value})))}}}),Lf=Rf,Yf=["id","markerWidth","markerHeight","markerUnits","orient"],Gf={name:"MarkerType",compatConfig:{MODE:3}},Xf=Fe({...Gf,props:{id:{},type:{},color:{default:"none"},width:{default:12.5},height:{default:12.5},markerUnits:{default:"strokeWidth"},orient:{default:"auto-start-reverse"},strokeWidth:{default:1}},setup(e){return(t,n)=>(re(),ve("marker",{id:t.id,class:"vue-flow__arrowhead",viewBox:"-10 -10 20 20",refX:"0",refY:"0",markerWidth:`${t.width}`,markerHeight:`${t.height}`,markerUnits:t.markerUnits,orient:t.orient},[t.type===V(Do).ArrowClosed?(re(),ve("polyline",{key:0,style:Ge({stroke:t.color,fill:t.color,strokeWidth:t.strokeWidth}),"stroke-linecap":"round","stroke-linejoin":"round",points:"-5,-4 0,0 -5,4 -5,-4"},null,4)):Pe("",!0),t.type===V(Do).Arrow?(re(),ve("polyline",{key:1,style:Ge({stroke:t.color,strokeWidth:t.strokeWidth}),"stroke-linecap":"round","stroke-linejoin":"round",fill:"none",points:"-5,-4 0,0 -5,4"},null,4)):Pe("",!0)],8,Yf))}}),Wf={class:"vue-flow__marker vue-flow__container","aria-hidden":"true"},Uf={name:"MarkerDefinitions",compatConfig:{MODE:3}},Zf=Fe({...Uf,setup(e){const{id:t,edges:n,connectionLineOptions:i,defaultMarkerColor:o}=Oe(),r=be(()=>{const a=new Set,s=[],l=u=>{if(u){const c=cn(u,t);a.has(c)||(typeof u=="object"?s.push({...u,id:c,color:u.color||o.value}):s.push({id:c,color:o.value,type:u}),a.add(c))}};for(const u of[i.value.markerEnd,i.value.markerStart])l(u);for(const u of n.value)for(const c of[u.markerStart,u.markerEnd])l(c);return s.sort((u,c)=>u.id.localeCompare(c.id))});return(a,s)=>(re(),ve("svg",Wf,[G("defs",null,[(re(!0),ve(Ze,null,Ln(r.value,l=>(re(),We(Xf,{id:l.id,key:l.id,type:l.type,color:l.color,width:l.width,height:l.height,markerUnits:l.markerUnits,"stroke-width":l.strokeWidth,orient:l.orient},null,8,["id","type","color","width","height","markerUnits","stroke-width","orient"]))),128))])]))}}),Kf={name:"Edges",compatConfig:{MODE:3}},qf=Fe({...Kf,setup(e){const{findNode:t,getEdges:n,elevateEdgesOnSelect:i}=Oe();return(o,r)=>(re(),ve(Ze,null,[X(Zf),(re(!0),ve(Ze,null,Ln(V(n),a=>(re(),ve("svg",{key:a.id,class:"vue-flow__edges vue-flow__container",style:Ge({zIndex:V(vd)(a,V(t),V(i))})},[X(V(Hf),{id:a.id},null,8,["id"])],4))),128)),X(V(Lf))],64))}}),Jf=Fe({name:"Node",compatConfig:{MODE:3},props:["id","resizeObserver"],setup(e){const{id:t,noPanClassName:n,selectNodesOnDrag:i,nodesSelectionActive:o,multiSelectionActive:r,emits:a,removeSelectedNodes:s,addSelectedNodes:l,updateNodeDimensions:u,onUpdateNodeInternals:c,getNodeTypes:d,nodeExtent:g,elevateNodesOnSelect:m,disableKeyboardA11y:M,ariaLiveMessage:k,snapToGrid:N,snapGrid:T,nodeDragThreshold:I,nodesDraggable:E,elementsSelectable:$,nodesConnectable:O,nodesFocusable:te,hooks:P}=Oe(),D=ge(null);Ht(Wr,D),Ht(Xr,e.id);const W=Xt(Qn),F=dn(),R=qr(),{node:x,parentNode:K}=Kr(e.id),{emit:w,on:A}=Fd(x,a),S=ze(()=>typeof x.draggable>"u"?E.value:x.draggable),H=ze(()=>typeof x.selectable>"u"?$.value:x.selectable),L=ze(()=>typeof x.connectable>"u"?O.value:x.connectable),J=ze(()=>typeof x.focusable>"u"?te.value:x.focusable),Y=be(()=>H.value||S.value||P.value.nodeClick.hasListeners()||P.value.nodeDoubleClick.hasListeners()||P.value.nodeMouseEnter.hasListeners()||P.value.nodeMouseMove.hasListeners()||P.value.nodeMouseLeave.hasListeners()),se=ze(()=>!!x.dimensions.width&&!!x.dimensions.height),ce=be(()=>{const b=x.type||"default",y=W?.[`node-${b}`];if(y)return y;let z=x.template||d.value[b];if(typeof z=="string"&&F){const le=Object.keys(F.appContext.components);le&&le.includes(b)&&(z=Zi(b,!1))}return z&&typeof z!="string"?z:(a.error(new Ye(Le.NODE_TYPE_MISSING,z)),!1)}),Ee=Ur({id:e.id,el:D,disabled:()=>!S.value,selectable:H,dragHandle:()=>x.dragHandle,onStart(b){w.dragStart(b)},onDrag(b){w.drag(b)},onStop(b){w.dragStop(b)},onClick(b){v(b)}}),q=be(()=>x.class instanceof Function?x.class(x):x.class),Z=be(()=>{const b=(x.style instanceof Function?x.style(x):x.style)||{},y=x.width instanceof Function?x.width(x):x.width,z=x.height instanceof Function?x.height(x):x.height;return!b.width&&y&&(b.width=typeof y=="string"?y:`${y}px`),!b.height&&z&&(b.height=typeof z=="string"?z:`${z}px`),b}),de=ze(()=>Number(x.zIndex??Z.value.zIndex??0));return c(b=>{(b.includes(e.id)||!b.length)&&Ne()}),ct(()=>{De(()=>x.hidden,(b=!1,y,z)=>{!b&&D.value&&(e.resizeObserver.observe(D.value),z(()=>{D.value&&e.resizeObserver.unobserve(D.value)}))},{immediate:!0,flush:"post"})}),De([()=>x.type,()=>x.sourcePosition,()=>x.targetPosition],()=>{He(()=>{u([{id:e.id,nodeElement:D.value,forceUpdate:!0}])})}),De([()=>x.position.x,()=>x.position.y,()=>{var b;return(b=K.value)==null?void 0:b.computedPosition.x},()=>{var b;return(b=K.value)==null?void 0:b.computedPosition.y},()=>{var b;return(b=K.value)==null?void 0:b.computedPosition.z},de,()=>x.selected,()=>x.dimensions.height,()=>x.dimensions.width,()=>{var b;return(b=K.value)==null?void 0:b.dimensions.height},()=>{var b;return(b=K.value)==null?void 0:b.dimensions.width}],([b,y,z,le,ie,Q])=>{const he={x:b,y,z:Q+(m.value&&x.selected?1e3:0)};typeof z<"u"&&typeof le<"u"?x.computedPosition=dd({x:z,y:le,z:ie},he):x.computedPosition=he},{flush:"post",immediate:!0}),De([()=>x.extent,g],([b,y],[z,le])=>{(b!==z||y!==le)&&ke()}),x.extent==="parent"||typeof x.extent=="object"&&"range"in x.extent&&x.extent.range==="parent"?xo(()=>se).toBe(!0).then(ke):ke(),()=>x.hidden?null:Me("div",{ref:D,"data-id":x.id,class:["vue-flow__node",`vue-flow__node-${ce.value===!1?"default":x.type||"default"}`,{[n.value]:S.value,dragging:Ee?.value,draggable:S.value,selected:x.selected,selectable:H.value,parent:x.isParent},q.value],style:{visibility:se.value?"visible":"hidden",zIndex:x.computedPosition.z??de.value,transform:`translate(${x.computedPosition.x}px,${x.computedPosition.y}px)`,pointerEvents:Y.value?"all":"none",...Z.value},tabIndex:J.value?0:void 0,role:J.value?"button":void 0,"aria-describedby":M.value?void 0:`${Tr}-${t}`,"aria-label":x.ariaLabel,onMouseenter:ye,onMousemove:$e,onMouseleave:fe,onContextmenu:xe,onClick:v,onDblclick:Se,onKeydown:p},[Me(ce.value===!1?d.value.default:ce.value,{id:x.id,type:x.type,data:x.data,events:{...x.events,...A},selected:x.selected,resizing:x.resizing,dragging:Ee.value,connectable:L.value,position:x.computedPosition,dimensions:x.dimensions,isValidTargetPos:x.isValidTargetPos,isValidSourcePos:x.isValidSourcePos,parent:x.parentNode,parentNodeId:x.parentNode,zIndex:x.computedPosition.z??de.value,targetPosition:x.targetPosition,sourcePosition:x.sourcePosition,label:x.label,dragHandle:x.dragHandle,onUpdateNodeInternals:Ne})]);function ke(){const b=x.computedPosition,{computedPosition:y,position:z}=Zo(x,N.value?qn(b,T.value):b,a.error,g.value,K.value);(x.computedPosition.x!==y.x||x.computedPosition.y!==y.y)&&(x.computedPosition={...x.computedPosition,...y}),(x.position.x!==z.x||x.position.y!==z.y)&&(x.position=z)}function Ne(){D.value&&u([{id:e.id,nodeElement:D.value,forceUpdate:!0}])}function ye(b){Ee?.value||w.mouseEnter({event:b,node:x})}function $e(b){Ee?.value||w.mouseMove({event:b,node:x})}function fe(b){Ee?.value||w.mouseLeave({event:b,node:x})}function xe(b){return w.contextMenu({event:b,node:x})}function Se(b){return w.doubleClick({event:b,node:x})}function v(b){H.value&&(!i.value||!S.value||I.value>0)&&Oo(x,r.value,l,s,o,!1,D.value),w.click({event:b,node:x})}function p(b){if(!(Ao(b)||M.value))if(Ir.includes(b.key)&&H.value){const y=b.key==="Escape";Oo(x,r.value,l,s,o,y,D.value)}else S.value&&x.selected&&Vt[b.key]&&(b.preventDefault(),k.value=`Moved selected node ${b.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~x.position.x}, y: ${~~x.position.y}`,R({x:Vt[b.key].x,y:Vt[b.key].y},b.shiftKey))}}}),Qf=Jf;function jf(e={includeHiddenNodes:!1}){const{nodes:t}=Oe();return be(()=>{if(t.value.length===0)return!1;for(const n of t.value)if((e.includeHiddenNodes||!n.hidden)&&(n?.handleBounds===void 0||n.dimensions.width===0||n.dimensions.height===0))return!1;return!0})}const eh={class:"vue-flow__nodes vue-flow__container"},th={name:"Nodes",compatConfig:{MODE:3}},nh=Fe({...th,setup(e){const{getNodes:t,updateNodeDimensions:n,emits:i}=Oe(),o=jf(),r=ge();return De(o,a=>{a&&He(()=>{i.nodesInitialized(t.value)})},{immediate:!0}),ct(()=>{r.value=new ResizeObserver(a=>{const s=a.map(l=>({id:l.target.getAttribute("data-id"),nodeElement:l.target,forceUpdate:!0}));He(()=>n(s))})}),Ui(()=>{var a;return(a=r.value)==null?void 0:a.disconnect()}),(a,s)=>(re(),ve("div",eh,[r.value?(re(!0),ve(Ze,{key:0},Ln(V(t),(l,u,c,d)=>{const g=[l.id];if(d&&d.key===l.id&&Ia(d,g))return d;const m=(re(),We(V(Qf),{id:l.id,key:l.id,"resize-observer":r.value},null,8,["id","resize-observer"]));return m.memo=g,m},s,0),128)):Pe("",!0)]))}});function oh(){const{emits:e}=Oe();ct(()=>{if(Gr()){const t=document.querySelector(".vue-flow__pane");t&&window.getComputedStyle(t).zIndex!=="1"&&e.error(new Ye(Le.MISSING_STYLES))}})}const ih=G("div",{class:"vue-flow__edge-labels"},null,-1),rh={name:"VueFlow",compatConfig:{MODE:3}},ah=Fe({...rh,props:{id:{},modelValue:{},nodes:{},edges:{},edgeTypes:{},nodeTypes:{},connectionMode:{},connectionLineType:{},connectionLineStyle:{default:void 0},connectionLineOptions:{default:void 0},connectionRadius:{},isValidConnection:{type:[Function,null],default:void 0},deleteKeyCode:{default:void 0},selectionKeyCode:{type:[Boolean,null],default:void 0},multiSelectionKeyCode:{default:void 0},zoomActivationKeyCode:{default:void 0},panActivationKeyCode:{default:void 0},snapToGrid:{type:Boolean,default:void 0},snapGrid:{},onlyRenderVisibleElements:{type:Boolean,default:void 0},edgesUpdatable:{type:[Boolean,String],default:void 0},nodesDraggable:{type:Boolean,default:void 0},nodesConnectable:{type:Boolean,default:void 0},nodeDragThreshold:{},elementsSelectable:{type:Boolean,default:void 0},selectNodesOnDrag:{type:Boolean,default:void 0},panOnDrag:{type:[Boolean,Array],default:void 0},minZoom:{},maxZoom:{},defaultViewport:{},translateExtent:{},nodeExtent:{},defaultMarkerColor:{},zoomOnScroll:{type:Boolean,default:void 0},zoomOnPinch:{type:Boolean,default:void 0},panOnScroll:{type:Boolean,default:void 0},panOnScrollSpeed:{},panOnScrollMode:{},paneClickDistance:{},zoomOnDoubleClick:{type:Boolean,default:void 0},preventScrolling:{type:Boolean,default:void 0},selectionMode:{},edgeUpdaterRadius:{},fitViewOnInit:{type:Boolean,default:void 0},connectOnClick:{type:Boolean,default:void 0},applyDefault:{type:Boolean,default:void 0},autoConnect:{type:[Boolean,Function],default:void 0},noDragClassName:{},noWheelClassName:{},noPanClassName:{},defaultEdgeOptions:{},elevateEdgesOnSelect:{type:Boolean,default:void 0},elevateNodesOnSelect:{type:Boolean,default:void 0},disableKeyboardA11y:{type:Boolean,default:void 0},edgesFocusable:{type:Boolean,default:void 0},nodesFocusable:{type:Boolean,default:void 0},autoPanOnConnect:{type:Boolean,default:void 0},autoPanOnNodeDrag:{type:Boolean,default:void 0},autoPanSpeed:{}},emits:["nodesChange","edgesChange","nodesInitialized","paneReady","init","updateNodeInternals","error","connect","connectStart","connectEnd","clickConnectStart","clickConnectEnd","moveStart","move","moveEnd","selectionDragStart","selectionDrag","selectionDragStop","selectionContextMenu","selectionStart","selectionEnd","viewportChangeStart","viewportChange","viewportChangeEnd","paneScroll","paneClick","paneContextMenu","paneMouseEnter","paneMouseMove","paneMouseLeave","edgeUpdate","edgeContextMenu","edgeMouseEnter","edgeMouseMove","edgeMouseLeave","edgeDoubleClick","edgeClick","edgeUpdateStart","edgeUpdateEnd","nodeContextMenu","nodeMouseEnter","nodeMouseMove","nodeMouseLeave","nodeDoubleClick","nodeClick","nodeDragStart","nodeDrag","nodeDragStop","miniMapNodeClick","miniMapNodeDoubleClick","miniMapNodeMouseEnter","miniMapNodeMouseMove","miniMapNodeMouseLeave","update:modelValue","update:nodes","update:edges"],setup(e,{expose:t,emit:n}){const i=e,o=Na(),r=uo(i,"modelValue",n),a=uo(i,"nodes",n),s=uo(i,"edges",n),l=Oe(i),u=Rd({modelValue:r,nodes:a,edges:s},i,l);return Yd(n,l.hooks),Of(),oh(),Ht(Qn,o),Bo(()=>{u()}),t(l),(c,d)=>(re(),ve("div",{ref:V(l).vueFlowRef,class:"vue-flow"},[X(Mf,null,{default:pe(()=>[X(qf),ih,X(nh),Ve(c.$slots,"zoom-pane")]),_:3}),Ve(c.$slots,"default"),X(zf)],512))}}),sh={name:"Panel",compatConfig:{MODE:3}},aa=Fe({...sh,props:{position:{}},setup(e){const t=e,{userSelectionActive:n}=Oe(),i=be(()=>`${t.position}`.split("-"));return(o,r)=>(re(),ve("div",{class:ut(["vue-flow__panel",i.value]),style:Ge({pointerEvents:V(n)?"none":"all"})},[Ve(o.$slots,"default")],6))}});var gt=(e=>(e.Lines="lines",e.Dots="dots",e))(gt||{});const sa=function({dimensions:e,size:t,color:n}){return Me("path",{stroke:n,"stroke-width":t,d:`M${e[0]/2} 0 V${e[1]} M0 ${e[1]/2} H${e[0]}`})},la=function({radius:e,color:t}){return Me("circle",{cx:e,cy:e,r:e,fill:t})};gt.Lines+"",gt.Dots+"";const lh={[gt.Dots]:"#81818a",[gt.Lines]:"#eee"},uh=["id","x","y","width","height","patternTransform"],ch={key:2,height:"100",width:"100"},dh=["fill"],fh=["x","y","fill"],hh={name:"Background",compatConfig:{MODE:3}},ph=Fe({...hh,props:{id:{},variant:{default:()=>gt.Dots},gap:{default:20},size:{default:1},lineWidth:{default:1},patternColor:{},color:{},bgColor:{},height:{default:100},width:{default:100},x:{default:0},y:{default:0},offset:{default:0}},setup(e){const{id:t,viewport:n}=Oe(),i=be(()=>{const a=n.value.zoom,[s,l]=Array.isArray(e.gap)?e.gap:[e.gap,e.gap],u=[s*a||1,l*a||1],c=e.size*a,[d,g]=Array.isArray(e.offset)?e.offset:[e.offset,e.offset],m=[d*a||1+u[0]/2,g*a||1+u[1]/2];return{scaledGap:u,offset:m,size:c}}),o=ze(()=>`pattern-${t}${e.id?`-${e.id}`:""}`),r=ze(()=>e.color||e.patternColor||lh[e.variant||gt.Dots]);return(a,s)=>(re(),ve("svg",{class:"vue-flow__background vue-flow__container",style:Ge({height:`${a.height>100?100:a.height}%`,width:`${a.width>100?100:a.width}%`})},[Ve(a.$slots,"pattern-container",{id:o.value},()=>[G("pattern",{id:o.value,x:V(n).x%i.value.scaledGap[0],y:V(n).y%i.value.scaledGap[1],width:i.value.scaledGap[0],height:i.value.scaledGap[1],patternTransform:`translate(-${i.value.offset[0]},-${i.value.offset[1]})`,patternUnits:"userSpaceOnUse"},[Ve(a.$slots,"pattern",{},()=>[a.variant===V(gt).Lines?(re(),We(V(sa),{key:0,size:a.lineWidth,color:r.value,dimensions:i.value.scaledGap},null,8,["size","color","dimensions"])):a.variant===V(gt).Dots?(re(),We(V(la),{key:1,color:r.value,radius:i.value.size/2},null,8,["color","radius"])):Pe("",!0),a.bgColor?(re(),ve("svg",ch,[G("rect",{width:"100%",height:"100%",fill:a.bgColor},null,8,dh)])):Pe("",!0)])],8,uh)]),G("rect",{x:a.x,y:a.y,width:"100%",height:"100%",fill:`url(#${o.value})`},null,8,fh),Ve(a.$slots,"default",{id:o.value})],4))}}),gh={name:"ControlButton",compatConfig:{MODE:3}},mh=(e,t)=>{const n=e.__vccOpts||e;for(const[i,o]of t)n[i]=o;return n},vh={class:"vue-flow__controls-button"};function yh(e,t,n,i,o,r){return re(),ve("button",vh,[Ve(e.$slots,"default")])}const Sn=mh(gh,[["render",yh]]),wh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},_h=G("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"},null,-1),bh=[_h];function Eh(e,t){return re(),ve("svg",wh,bh)}const xh={render:Eh},Sh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},kh=G("path",{d:"M0 0h32v4.2H0z"},null,-1),Nh=[kh];function Ch(e,t){return re(),ve("svg",Sh,Nh)}const $h={render:Ch},Th={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},Mh=G("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0 0 27.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94a.919.919 0 0 1-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"},null,-1),Ih=[Mh];function Dh(e,t){return re(),ve("svg",Th,Ih)}const Ah={render:Dh},Ph={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},zh=G("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 0 0 0 13.714v15.238A3.056 3.056 0 0 0 3.048 32h18.285a3.056 3.056 0 0 0 3.048-3.048V13.714a3.056 3.056 0 0 0-3.048-3.047zM12.19 24.533a3.056 3.056 0 0 1-3.047-3.047 3.056 3.056 0 0 1 3.047-3.048 3.056 3.056 0 0 1 3.048 3.048 3.056 3.056 0 0 1-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"},null,-1),Oh=[zh];function Fh(e,t){return re(),ve("svg",Ph,Oh)}const Bh={render:Fh},Vh={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},Hh=G("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 0 0 0 13.714v15.238A3.056 3.056 0 0 0 3.048 32h18.285a3.056 3.056 0 0 0 3.048-3.048V13.714a3.056 3.056 0 0 0-3.048-3.047zM12.19 24.533a3.056 3.056 0 0 1-3.047-3.047 3.056 3.056 0 0 1 3.047-3.048 3.056 3.056 0 0 1 3.048 3.048 3.056 3.056 0 0 1-3.048 3.047z"},null,-1),Rh=[Hh];function Lh(e,t){return re(),ve("svg",Vh,Rh)}const Yh={render:Lh},Gh={name:"Controls",compatConfig:{MODE:3}},Xh=Fe({...Gh,props:{showZoom:{type:Boolean,default:!0},showFitView:{type:Boolean,default:!0},showInteractive:{type:Boolean,default:!0},fitViewParams:{},position:{default:()=>$r.BottomLeft}},emits:["zoomIn","zoomOut","fitView","interactionChange"],setup(e,{emit:t}){const{nodesDraggable:n,nodesConnectable:i,elementsSelectable:o,setInteractive:r,zoomIn:a,zoomOut:s,fitView:l,viewport:u,minZoom:c,maxZoom:d}=Oe(),g=ze(()=>n.value||i.value||o.value),m=ze(()=>u.value.zoom<=c.value),M=ze(()=>u.value.zoom>=d.value);function k(){a(),t("zoomIn")}function N(){s(),t("zoomOut")}function T(){l(e.fitViewParams),t("fitView")}function I(){r(!g.value),t("interactionChange",!g.value)}return(E,$)=>(re(),We(V(aa),{class:"vue-flow__controls",position:E.position},{default:pe(()=>[Ve(E.$slots,"top"),E.showZoom?(re(),ve(Ze,{key:0},[Ve(E.$slots,"control-zoom-in",{},()=>[X(Sn,{class:"vue-flow__controls-zoomin",disabled:M.value,onClick:k},{default:pe(()=>[Ve(E.$slots,"icon-zoom-in",{},()=>[(re(),We(Ot(V(xh))))])]),_:3},8,["disabled"])]),Ve(E.$slots,"control-zoom-out",{},()=>[X(Sn,{class:"vue-flow__controls-zoomout",disabled:m.value,onClick:N},{default:pe(()=>[Ve(E.$slots,"icon-zoom-out",{},()=>[(re(),We(Ot(V($h))))])]),_:3},8,["disabled"])])],64)):Pe("",!0),E.showFitView?Ve(E.$slots,"control-fit-view",{key:1},()=>[X(Sn,{class:"vue-flow__controls-fitview",onClick:T},{default:pe(()=>[Ve(E.$slots,"icon-fit-view",{},()=>[(re(),We(Ot(V(Ah))))])]),_:3})]):Pe("",!0),E.showInteractive?Ve(E.$slots,"control-interactive",{key:2},()=>[E.showInteractive?(re(),We(Sn,{key:0,class:"vue-flow__controls-interactive",onClick:I},{default:pe(()=>[g.value?Ve(E.$slots,"icon-unlock",{key:0},()=>[(re(),We(Ot(V(Yh))))]):Pe("",!0),g.value?Pe("",!0):Ve(E.$slots,"icon-lock",{key:1},()=>[(re(),We(Ot(V(Bh))))])]),_:3})):Pe("",!0)]):Pe("",!0),Ve(E.$slots,"default")]),_:3},8,["position"]))}}),Wh={class:"main-event-content"},Uh={key:0,class:"event-date"},Zh={class:"event-title"},Kh={key:1,class:"event-desc"},qh={class:"handles-container"},Jh={__name:"MainEventNode",props:{id:{type:String,required:!0},data:{type:Object,required:!0},selected:{type:Boolean,default:!1}},setup(e){const t=e,n=be(()=>{if(t.data.useCustomTime&&t.data.customTime)return t.data.customTime;const{year:i,month:o,day:r}=t.data;return i?`${i}/${o||""}/${r||""}`:""});return(i,o)=>(re(),ve("div",{class:ut(["main-event-node",{selected:e.selected}])},[G("div",Wh,[n.value?(re(),ve("div",Uh,Qe(n.value),1)):Pe("",!0),G("div",Zh,Qe(e.data.label),1),e.data.content?(re(),ve("div",Kh,Qe(e.data.content),1)):Pe("",!0)]),G("div",qh,[X(V(Ke),{id:"left",type:"source",position:V(ne).Left,class:"main-handle left-handle"},null,8,["position"]),X(V(Ke),{id:"left-target",type:"target",position:V(ne).Left,class:"main-handle left-handle"},null,8,["position"]),X(V(Ke),{id:"right",type:"source",position:V(ne).Right,class:"main-handle right-handle"},null,8,["position"]),X(V(Ke),{id:"right-target",type:"target",position:V(ne).Right,class:"main-handle right-handle"},null,8,["position"]),X(V(Ke),{id:"top",type:"target",position:V(ne).Top,class:"main-handle top-handle"},null,8,["position"]),X(V(Ke),{id:"bottom",type:"source",position:V(ne).Bottom,class:"main-handle bottom-handle"},null,8,["position"])])],2))}},Qh=Yn(Jh,[["__scopeId","data-v-52d9f0fd"]]),jh={class:"branch-event-content"},ep={key:0,class:"event-date"},tp={class:"event-title"},np={key:1,class:"event-desc"},op={__name:"BranchEventNode",props:{id:{type:String,required:!0},data:{type:Object,required:!0},selected:{type:Boolean,default:!1}},setup(e){const t=e,n=be(()=>{if(t.data.useCustomTime&&t.data.customTime)return t.data.customTime;const{year:r,month:a,day:s}=t.data;return r?`${r}/${a||""}/${s||""}`:""}),i=be(()=>t.data.isLeftSide!==void 0?t.data.isLeftSide:!1),o=be(()=>({borderColor:t.data.color||"#e84393",backgroundColor:`${t.data.color||"#e84393"}22`}));return(r,a)=>(re(),ve("div",{class:ut(["branch-event-node",{selected:e.selected,"left-side":i.value,"right-side":!i.value}]),style:Ge(o.value)},[X(V(Ke),{id:"left",type:"source",position:V(ne).Left,class:"branch-handle",style:Ge({background:e.data.color})},null,8,["position","style"]),G("div",jh,[n.value?(re(),ve("div",ep,Qe(n.value),1)):Pe("",!0),G("div",tp,Qe(e.data.label),1),e.data.content?(re(),ve("div",np,Qe(e.data.content),1)):Pe("",!0)]),X(V(Ke),{id:"right",type:"source",position:V(ne).Right,class:"branch-handle",style:Ge({background:e.data.color})},null,8,["position","style"]),X(V(Ke),{id:"top",type:"source",position:V(ne).Top,class:"branch-handle",style:Ge({background:e.data.color})},null,8,["position","style"]),X(V(Ke),{id:"bottom",type:"source",position:V(ne).Bottom,class:"branch-handle",style:Ge({background:e.data.color})},null,8,["position","style"])],6))}},ip=Yn(op,[["__scopeId","data-v-87441d98"]]),rp={class:"panel-buttons"},ap={class:"custom-dialog-body"},sp={class:"time-mode-switch"},lp={key:0,class:"date-inputs"},up={key:1,class:"custom-time-inputs"},cp={class:"color-picker-container"},dp={class:"color-meaning-hint"},fp={class:"color-meanings-popover"},hp=["onClick"],pp={class:"color-desc"},gp={class:"custom-dialog-footer"},mp={class:"custom-dialog-header"},vp={class:"custom-dialog-body"},yp={class:"custom-dialog-footer"},wp={class:"custom-dialog-header"},_p={class:"custom-dialog-body"},bp={class:"import-container"},Ep={class:"custom-dialog-footer"},Be=400,xp={__name:"TimelineFlow",setup(e,{expose:t}){const n=za(),i=be(()=>n.theme||"light"),o={Straight:"straight",SmoothStep:"smoothstep",SimpleBezier:"simplebezier",Bezier:"bezier",Step:"step"},r={Arrow:"arrow",ArrowClosed:"arrowclosed"},a=ge([]),s=ge([]),l=ge(!1);ge(!1),ge(null);const u=ge(null),c=ge(!1),d=ge({}),g=ge(""),m=ge(!1),M=ge({top:"0px",left:"0px"}),k=ge(""),N=ge(""),T=ge(""),I=ge(""),E={type:o.SmoothStep,animated:!0,style:{cursor:"move",strokeWidth:2},updatable:!0,draggable:!0},$=Oe(),{findNode:O,getSelectedNodes:te,addNodes:P,addEdges:D,removeNodes:W,removeEdges:F,updateEdge:R,updateNode:x,fitView:K}=$,w=()=>{try{const h=$.getNodes.value;return Array.isArray(h)?h:[]}catch(h){return console.error("获取节点失败:",h),[]}},A=()=>{try{const h=$.getEdges.value;return Array.isArray(h)?h:[]}catch(h){return console.error("获取边失败:",h),[]}},S=h=>{try{return A().find(_=>_.id===h)}catch(f){return console.error("查找边时出错:",f),null}},H=(h,f)=>{try{const _=O(h);if(!_){console.error(`找不到ID为${h}的节点`);return}_.position={...f},x(h,{..._,position:f})}catch(_){console.error("更新节点位置时出错:",_)}},L=[{id:"main-1",type:"main-event",position:{x:Be,y:100},data:{label:"故事开始",year:"2020",month:"1",day:"1",content:"主角踏上旅程",nodeType:"main",color:"#409EFF"}},{id:"main-2",type:"main-event",position:{x:Be,y:250},data:{label:"主角出发",year:"2021",month:"6",day:"15",content:"主角面临挑战",nodeType:"main",color:"#409EFF"}},{id:"main-3",type:"main-event",position:{x:Be,y:400},data:{label:"第一次危机",year:"2022",month:"3",day:"10",content:"主角遇到危机",nodeType:"main",color:"#409EFF"}},{id:"branch-1",type:"branch-event",position:{x:Be+250,y:100},data:{label:"初遇",year:"2020",month:"3",day:"15",content:"与女主角相遇",nodeType:"branch",color:"#e84393",parentId:"main-1",isLeftSide:!1},connectable:!0},{id:"branch-2",type:"branch-event",position:{x:Be-250,y:250},data:{label:"挫折",year:"2021",month:"5",day:"20",content:"遭遇挫折",nodeType:"branch",color:"#00b894",parentId:"main-2",isLeftSide:!0},connectable:!0}],J=[{id:"e-main1-main2",source:"main-1",target:"main-2",sourceHandle:"bottom",targetHandle:"top",type:o.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:r.ArrowClosed,color:"#409EFF"},updatable:!0,draggable:!0},{id:"e-main2-main3",source:"main-2",target:"main-3",sourceHandle:"bottom",targetHandle:"top",type:o.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:r.ArrowClosed,color:"#409EFF"},updatable:!0,draggable:!0},{id:"e-main1-branch1",source:"main-1",sourceHandle:"right",target:"branch-1",targetHandle:"left",type:o.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:"#e84393"},markerEnd:{type:r.ArrowClosed,color:"#e84393"}},{id:"e-main2-branch2",source:"main-2",sourceHandle:"left",target:"branch-2",targetHandle:"right",type:o.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:"#00b894"},markerEnd:{type:r.ArrowClosed,color:"#00b894"}}],Y=ge(!1),se=async()=>{try{const h=document.querySelector(".timeline-flow-wrapper");Y.value?(document.fullscreenElement?document.exitFullscreen?await document.exitFullscreen():document.webkitExitFullscreen?await document.webkitExitFullscreen():document.msExitFullscreen&&await document.msExitFullscreen():h.classList.remove("css-fullscreen"),Y.value=!1):h.requestFullscreen?(await h.requestFullscreen(),Y.value=!0):h.webkitRequestFullscreen?(await h.webkitRequestFullscreen(),Y.value=!0):h.msRequestFullscreen?(await h.msRequestFullscreen(),Y.value=!0):(h.classList.add("css-fullscreen"),Y.value=!0),He(()=>{K()})}catch(h){console.error("全屏切换失败:",h);const f=document.querySelector(".timeline-flow-wrapper");Y.value?(f.classList.remove("css-fullscreen"),Y.value=!1):(f.classList.add("css-fullscreen"),Y.value=!0),He(()=>{K()})}};ct(()=>{a.value=L,s.value=J,document.addEventListener("click",()=>{m.value=!1}),document.addEventListener("keydown",ti),document.addEventListener("fullscreenchange",ce),document.addEventListener("webkitfullscreenchange",ce),document.addEventListener("mozfullscreenchange",ce),document.addEventListener("MSFullscreenChange",ce),console.log("TimelineFlow组件已挂载，节点数:",a.value.length,"边数:",s.value.length),He(()=>{K()}),window.addEventListener("resize",Ee)}),Bo(()=>{window.removeEventListener("resize",Ee),document.removeEventListener("keydown",ti),document.removeEventListener("fullscreenchange",ce),document.removeEventListener("webkitfullscreenchange",ce),document.removeEventListener("mozfullscreenchange",ce),document.removeEventListener("MSFullscreenChange",ce)});const ce=()=>{const h=!!document.fullscreenElement||!!document.webkitFullscreenElement||!!document.mozFullScreenElement||!!document.msFullscreenElement,f=document.querySelector(".timeline-flow-wrapper"),_=f&&f.classList.contains("css-fullscreen");Y.value=h||_,He(()=>{K()})},Ee=()=>{q.value&&clearTimeout(q.value),q.value=setTimeout(()=>{console.log("窗口大小变化，重新适应视图"),K()},200)},q=ge(null),Z=()=>{const h=["#e84393","#00b894","#e17055","#6c5ce7","#fdcb6e","#00cec9","#ff7675","#74b9ff"];return h[Math.floor(Math.random()*h.length)]},de=h=>{At()},ke=h=>{console.log("连接变化:",h),At()},Ne=h=>{try{const f=h.node;u.value=f,f.data.nodeType==="main"&&(f.position.x=Be,console.log(`主干节点 ${f.id} 拖动中, Y位置: ${f.position.y}`))}catch(f){console.error("节点拖动时出错:",f)}},ye=(h,f)=>{try{const _=O(h),B=O(f);if(!_||!B){console.error("无法找到节点:",h,f);return}const j=A().find(ue=>ue.source===f&&ue.target===h||ue.source===h&&ue.target===f);if(j){const ue={...j,style:{...j.style}};R(j,ue),console.log(`已刷新分支到主干连接: ${j.id}`)}else{const ue=_.data.isLeftSide,oe={id:`e-${f}-${h}-${Date.now()}`,source:f,sourceHandle:ue?"left":"right",target:h,targetHandle:ue?"right":"left",type:o.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:_.data.color||Z()},markerEnd:{type:r.ArrowClosed,color:_.data.color||Z()}};D([oe]),x(h,{..._,data:{..._.data,parentId:f}}),console.log(`已创建新的分支到主干连接: ${oe.id}`)}}catch(_){console.error("确保分支到主干连接时出错:",_)}},$e=h=>{try{const f=h.node;f.data.nodeType==="main"&&(console.log(`主干节点 ${f.id} 拖动结束, 当前Y位置: ${f.position.y}`),H(f.id,{x:Be,y:f.position.y}),fe()),f.data.nodeType==="branch"&&f.data.parentId&&(console.log(`分支节点 ${f.id} 拖动结束, 当前位置: (${f.position.x}, ${f.position.y})`),setTimeout(()=>{ye(f.id,f.data.parentId)},50)),u.value=null,At()}catch(f){console.error("节点拖动结束时出错:",f),u.value=null}},fe=()=>{try{console.log("开始优化主干连接...");const h=w(),f=A();if(!h||!h.length)return;const _=h.filter(_e=>_e.data.nodeType==="main").sort((_e,Ue)=>_e.position.y-Ue.position.y);if(_.length<=1)return;console.log("排序后的主干节点顺序:",_.map(_e=>_e.id));const B=f.filter(_e=>{const Ue=O(_e.source),it=O(_e.target);return Ue?.data?.nodeType==="main"&&it?.data?.nodeType==="main"}),C=new Map;B.forEach(_e=>{C.set(`${_e.source}-${_e.target}`,_e)});let j=!1,ue=[];for(let _e=0;_e<_.length-1;_e++){const Ue=_[_e].id,it=_[_e+1].id,ae=`${Ue}-${it}`;C.has(ae)||(j=!0,ue.push({source:Ue,target:it}))}if(!j){console.log("主干连接已经是最新状态，无需更新");return}console.log("发现缺失的主干连接，添加这些连接");const oe=ue.map(_e=>({id:`e-main-${_e.source}-${_e.target}-${Date.now()}`,source:_e.source,sourceHandle:"bottom",target:_e.target,targetHandle:"top",type:o.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:r.ArrowClosed,color:"#409EFF"},animated:!1,updatable:!0,draggable:!0}));oe.length>0&&(D(oe),console.log(`已添加 ${oe.length} 条缺失的主干连接`));const we=new Set;for(let _e=0;_e<_.length-1;_e++)we.add(`${_[_e].id}-${_[_e+1].id}`);const U=B.filter(_e=>!we.has(`${_e.source}-${_e.target}`));return U.length>0&&(console.log(`移除 ${U.length} 条多余的主干连接`),F(U.map(_e=>_e.id))),!0}catch(h){return console.error("优化主干连接时出错:",h),!1}},xe=()=>{m.value=!1,T.value="",k.value="",I.value=""},Se=h=>{console.log("节点被点击:",h.node),m.value=!1},v=h=>{h.event.preventDefault();const{clientX:f,clientY:_}=h.event,B=document.querySelector(".timeline-flow-wrapper");if(!B)return;const C=B.getBoundingClientRect(),j=f-C.left,ue=_-C.top;m.value=!0,M.value={position:"absolute",top:`${ue}px`,left:`${j}px`},k.value=h.node.id,N.value=h.node.data.nodeType,T.value="node"},p=h=>{const f=k.value;if(!f)return;const _=O(f);if(_){switch(h){case"edit":Wt(f,_.data);break;case"delete":ya(f);break;case"add-up":gn(f,"up");break;case"add-down":gn(f,"down");break;case"add-left":gn(f,"left");break;case"add-right":gn(f,"right");break;case"add-free":Ea(f);break}m.value=!1}},b=h=>{if(console.log("创建新连接:",h),!(!h.source||!h.target))try{const f=O(h.source),_=O(h.target);if(!f||!_)return;let C={id:`e-${h.source}-${h.target}-${et(4)}`,...h,type:o.SmoothStep,animated:!0,style:{cursor:"move",strokeWidth:2}};if(f.data.nodeType==="main"&&_.data.nodeType==="main")C.type=o.Straight,C.style={...C.style,strokeWidth:4,stroke:"#409EFF"},C.markerEnd={type:r.ArrowClosed,color:"#409EFF"},h.sourceHandle==="bottom"&&h.targetHandle==="top"||(C.sourceHandle="bottom",C.targetHandle="top");else if(f.data.nodeType==="branch"&&_.data.nodeType==="main"){const j=f.data.color||Z();C.type=o.SmoothStep,C.style={...C.style,strokeWidth:3,stroke:j},C.markerEnd={type:r.ArrowClosed,color:j}}else if(f.data.nodeType==="main"&&_.data.nodeType==="branch"){const j=_.data.color||Z();C.type=o.SmoothStep,C.style={...C.style,strokeWidth:3,stroke:j},C.markerEnd={type:r.ArrowClosed,color:j}}else if(f.data.nodeType==="branch"&&_.data.nodeType==="branch"){const j=f.data.color||Z();C.type=o.SmoothStep,C.style={...C.style,strokeWidth:3,stroke:j},C.markerEnd={type:r.ArrowClosed,color:j}}D([C]),f.data.nodeType==="main"&&_.data.nodeType==="branch"?x(_.id,{..._,data:{..._.data,parentId:f.id}}):f.data.nodeType==="branch"&&_.data.nodeType==="main"&&x(f.id,{...f,data:{...f.data,targetId:_.id}}),console.log("成功创建新连接"),At()}catch(f){console.error("创建连接时出错:",f)}},y=h=>{const{edge:f,connection:_}=h;console.log("边更新:",f.id,_);try{const C=S(f.id)?.data?.offsetY||0;if(!R(f,_)){console.error("边更新失败");return}const ue=O(_.source),oe=O(_.target);if(ue&&oe){const we=A().find(U=>U.id===f.id||U.source===_.source&&U.target===_.target);we&&(ue.data.nodeType==="main"&&oe.data.nodeType==="main"?(we.type=o.SmoothStep,we.markerEnd||(we.markerEnd={type:r.ArrowClosed,color:we.style?.stroke||"#409EFF"})):ue.data.nodeType==="branch"&&oe.data.nodeType==="branch"&&(we.markerEnd||(we.markerEnd={type:r.ArrowClosed,color:ue.data.color||"#409EFF"}))),ue.data.nodeType==="branch"&&oe.data.nodeType==="main"?x(ue.id,{...ue,data:{...ue.data,targetId:oe.id}}):ue.data.nodeType==="main"&&oe.data.nodeType==="branch"&&x(oe.id,{...oe,data:{...oe.data,parentId:ue.id}}),He(()=>{ro()})}}catch(B){console.error("边更新处理出错:",B)}},z=h=>{const{edge:f,event:_}=h;console.log("边被点击:",f),_.detail===2&&Je(f)},le=h=>{h.event.preventDefault();const{clientX:f,clientY:_}=h.event,B=document.querySelector(".timeline-flow-wrapper");if(!B)return;const C=B.getBoundingClientRect(),j=f-C.left,ue=_-C.top;m.value=!0,M.value={position:"absolute",top:`${ue}px`,left:`${j}px`},T.value="edge",I.value=h.edge.id,console.log("显示边右键菜单:",h.edge.id)},ie=h=>{const f=I.value;if(!f)return;const _=A().find(B=>B.id===f);if(_){switch(console.log("边菜单操作:",h,f),h){case"change-style":Ce(_);break;case"delete":Je(_);break}m.value=!1}},Q=ge(!1),he=ge(null),me=ge({strokeWidth:2,stroke:"#409EFF",animated:!1}),Ce=h=>{he.value=h,me.value={strokeWidth:h.style?.strokeWidth||2,stroke:h.style?.stroke||"#409EFF",animated:h.animated||!1},Q.value=!0},Ae=()=>{if(he.value)try{const h=S(he.value.id);if(!h)return;const f=h.data?.offsetY||0;h.style={...h.style,strokeWidth:me.value.strokeWidth,stroke:me.value.stroke},h.animated=me.value.animated,h.data||(h.data={}),h.data.offsetY=f,h.markerEnd&&(h.markerEnd.color=me.value.stroke),console.log("边样式已更新:",h.id),Q.value=!1}catch(h){console.error("更新边样式时出错:",h)}},Je=h=>{try{const f=O(h.source),_=O(h.target);if(f?.data.nodeType==="main"&&_?.data.nodeType==="main"){Pt.alert("主干连接不能直接删除，请移除相关节点","提示");return}Pt.confirm("确定删除此连接吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{F([h.id]),console.log("边已删除:",h.id),f?.data.nodeType==="branch"&&_?.data.nodeType==="main"?x(f.id,{...f,data:{...f.data,targetId:null}}):f?.data.nodeType==="main"&&_?.data.nodeType==="branch"&&x(_.id,{..._,data:{..._.data,parentId:null}})}).catch(()=>{})}catch(f){console.error("处理边删除时出错:",f)}},Et=()=>{try{const h=w();if(!h){console.error("获取节点失败");return}const f=h.filter(j=>j.data.nodeType==="main"),_=f.length>0?Math.max(...f.map(j=>j.position.y)):0,B=`main-${et(6)}`,C={id:B,type:"main-event",position:{x:Be,y:_+150},connectable:!0,data:{label:"新事件",year:new Date().getFullYear(),month:new Date().getMonth()+1,day:new Date().getDate(),content:"",nodeType:"main",color:"#409EFF"}};if(h.value=[...h.value,C],f.length>0){const j=f.reduce((oe,we)=>oe.position.y>we.position.y?oe:we),ue={id:`e-${j.id}-${B}`,source:j.id,sourceHandle:"bottom",target:B,targetHandle:"top",type:o.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:r.ArrowClosed,color:"#409EFF"},updatable:!0,draggable:!0,data:{editable:!0,offsetY:0}};s.value=[...s.value,ue]}Wt(B,C.data)}catch(h){console.error("添加主干事件时出错:",h)}},oo=()=>{try{const h=w();if(!h){console.error("获取节点失败");return}const f=h.filter(we=>we.data.nodeType==="main");if(f.length===0){Pt.alert("请先添加主干事件","提示");return}const _=f[Math.floor(Math.random()*f.length)],B=Math.random()>.5,C=Z(),j=`branch-${et(6)}`,ue={id:j,type:"branch-event",position:{x:B?Be-250:Be+250,y:_.position.y},connectable:!0,data:{label:"分支事件",year:new Date().getFullYear(),month:new Date().getMonth()+1,day:new Date().getDate(),content:"",nodeType:"branch",color:C,parentId:_.id,isLeftSide:B}};P([ue]);const oe={id:`e-${_.id}-${j}`,source:_.id,sourceHandle:B?"left":"right",target:j,targetHandle:B?"right":"left",type:o.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:C},markerEnd:{type:r.ArrowClosed,color:C}};D([oe]),Wt(j,ue.data)}catch(h){console.error("添加分支事件时出错:",h)}},Wt=(h,f)=>{d.value={...f,year:f.year?Number(f.year):f.year,month:f.month?Number(f.month):f.month,day:f.day?Number(f.day):f.day},l.value=!!f.customTime,g.value=h,c.value=!0},ua=()=>{try{if(g.value==="new-node"){ca();return}if(!g.value)return;const h=O(g.value);if(!h)return;if(x(g.value,{...h,data:{...h.data,label:d.value.label,year:d.value.year,month:d.value.month,day:d.value.day,content:d.value.content,color:d.value.color||h.data.color,customTime:l.value?d.value.customTime:null,useCustomTime:l.value}}),h.data.nodeType==="branch"&&d.value.color){const f=A();if(!f){console.error("获取边失败");return}f.filter(B=>B.source===g.value||B.target===g.value).forEach(B=>{B.style={...B.style,stroke:d.value.color},B.markerEnd&&(B.markerEnd.color=d.value.color)})}c.value=!1,g.value="",At()}catch(h){console.error("保存编辑时出错:",h),c.value=!1,g.value=""}},ca=()=>{try{const h=Zt.value;if(!h.sourceNodeId){console.error("源节点ID为空"),c.value=!1;return}const f=O(h.sourceNodeId);if(!f){console.error("找不到源节点"),c.value=!1;return}const _=h.nodeType==="main"?`main-${et(6)}`:`branch-${et(6)}`,B={id:_,type:h.nodeType==="main"?"main-event":"branch-event",position:h.position,connectable:!0,data:{label:d.value.label,year:d.value.year,month:d.value.month,day:d.value.day,content:d.value.content,nodeType:h.nodeType,color:d.value.color||(h.nodeType==="main"?"#409EFF":Z()),parentId:h.nodeType==="branch"&&!h.isFreeEvent?h.sourceNodeId:null,isLeftSide:h.direction==="left",customTime:l.value?d.value.customTime:null,useCustomTime:l.value}};if(P([B]),console.log(`节点 ${_} 已添加`),!h.isFreeEvent)switch(h.direction){case"up":da(f,B);break;case"down":fa(f,B);break;case"left":ha(f,B);break;case"right":pa(f,B);break}c.value=!1,g.value="",h.nodeType==="main"&&(h.direction==="up"||h.direction==="down")&&setTimeout(()=>{Qo()},100)}catch(h){console.error("创建新节点时出错:",h),c.value=!1,g.value=""}},da=(h,f)=>{if(h.data.nodeType==="main"){const _={id:`e-${f.id}-${h.id}`,source:f.id,sourceHandle:"bottom",target:h.id,targetHandle:"top",type:o.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:r.ArrowClosed,color:"#409EFF"},updatable:!0,draggable:!0,data:{editable:!0,offsetY:0}};D([_])}else{const _=f.data.color,B={id:`e-${f.id}-${h.id}`,source:f.id,sourceHandle:"bottom",target:h.id,targetHandle:"top",type:o.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:_},markerEnd:{type:r.ArrowClosed,color:_}};D([B])}},fa=(h,f)=>{if(h.data.nodeType==="main"){const _=A(),B=_.filter(ue=>ue.source===h.id&&O(ue.target)?.data.nodeType==="main"),C=B.length>0?B[0].target:null;if(C){const ue=_.find(oe=>oe.source===h.id&&oe.target===C);ue&&F([ue.id])}const j={id:`e-${h.id}-${f.id}`,source:h.id,sourceHandle:"bottom",target:f.id,targetHandle:"top",type:o.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:r.ArrowClosed,color:"#409EFF"},updatable:!0,draggable:!0,data:{editable:!0,offsetY:0}};if(D([j]),C){const ue={id:`e-${f.id}-${C}`,source:f.id,sourceHandle:"bottom",target:C,targetHandle:"top",type:o.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:r.ArrowClosed,color:"#409EFF"},updatable:!0,draggable:!0,data:{editable:!0,offsetY:0}};D([ue])}}else{const _=h.data.color,B={id:`e-${h.id}-${f.id}`,source:h.id,sourceHandle:"bottom",target:f.id,targetHandle:"top",type:o.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:_},markerEnd:{type:r.ArrowClosed,color:_}};D([B])}},ha=(h,f)=>{const _=f.data.color,B={id:`e-${h.id}-${f.id}`,source:h.id,sourceHandle:"left",target:f.id,targetHandle:"right",type:o.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:_},markerEnd:{type:r.ArrowClosed,color:_}};D([B])},pa=(h,f)=>{const _=f.data.color,B={id:`e-${h.id}-${f.id}`,source:h.id,sourceHandle:"right",target:f.id,targetHandle:"left",type:o.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:_},markerEnd:{type:r.ArrowClosed,color:_}};D([B])},io=()=>{c.value=!1,g.value=""},ga=h=>{h.target===h.currentTarget&&io()},ma=h=>{h.target===h.currentTarget&&(Q.value=!1)},va=h=>{h.target===h.currentTarget&&(Ut.value=!1)},ya=h=>{Pt.confirm("确定删除此节点吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{try{const f=O(h);if(!f)return;if(f.data.nodeType==="main")wa(h);else{W([h]);const C=A().filter(j=>j.source===h||j.target===h);C.length>0&&F(C.map(j=>j.id))}At()}catch(f){console.error("删除节点时出错:",f)}}).catch(()=>{})},wa=h=>{const f=w(),_=A(),B=_.filter(U=>U.target===h&&O(U.source)?.data.nodeType==="main"),C=_.filter(U=>U.source===h&&O(U.target)?.data.nodeType==="main"),j=B.length>0?B[0].source:null,ue=C.length>0?C[0].target:null;console.log(`删除主干节点 ${h}，前节点: ${j||"无"}，后节点: ${ue||"无"}`),W([h]);const oe=_.filter(U=>U.source===h||U.target===h);oe.length>0&&F(oe.map(U=>U.id));const we=f.filter(U=>U.data.nodeType==="branch"&&U.data.parentId===h);j&&we.length>0&&we.forEach(U=>{x(U.id,{...U,data:{...U.data,parentId:j}});const _e=U.data.isLeftSide,Ue={id:`e-${j}-${U.id}`,source:j,sourceHandle:_e?"left":"right",target:U.id,targetHandle:_e?"right":"left",type:o.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:U.data.color},markerEnd:{type:r.ArrowClosed,color:U.data.color}};D([Ue])}),He(()=>{fe()})},ro=()=>{try{console.log("开始自动布局...");const h=w();if(!h){console.error("获取节点失败");return}h.filter(B=>B.data.nodeType==="main").forEach(B=>{B.position.x!==Be&&H(B.id,{x:Be,y:B.position.y})}),Qo();const _=h.filter(B=>B.data.nodeType==="branch"&&B.data.parentId);if(_.length>0){console.log(`正在检查 ${_.length} 个分支节点连接`);const B=A();_.forEach(C=>{const j=C.data.parentId;if(!j)return;const ue=B.find(oe=>oe.source===j&&oe.target===C.id||oe.source===C.id&&oe.target===j);if(!ue)console.log(`节点 ${C.id} 缺少到主干节点 ${j} 的连接，正在创建...`),setTimeout(()=>{ye(C.id,j)},50);else{console.log(`节点 ${C.id} 到主干节点 ${j} 的连接已存在`);const oe=ue.source===C.id?C:O(ue.source),we=ue.target===C.id?C:O(ue.target);if(oe&&we&&(oe.position.x!==oe._rf?.position?.x||oe.position.y!==oe._rf?.position?.y||we.position.x!==we._rf?.position?.x||we.position.y!==we._rf?.position?.y)){console.log(`刷新节点 ${C.id} 的连接显示`);const U={...ue,style:{...ue.style}};R(ue,U)}}})}console.log("自动布局完成")}catch(h){console.error("自动布局时出错:",h)}},Qo=()=>{try{console.log("开始重排主干节点...");const h=w();if(!h||!Array.isArray(h)||h.length===0){console.error("获取节点失败或节点列表为空");return}const f=h.filter(_=>_.data?.nodeType==="main");if(console.log(`找到 ${f.length} 个主干节点`),f.length<=1){console.log("主干节点数量不足，无需重排");return}f.sort((_,B)=>_.position.y-B.position.y),f.forEach(_=>{_.position.x!==Be&&H(_.id,{x:Be,y:_.position.y})}),fe()}catch(h){console.error("重排主干节点时出错:",h)}},_a=()=>{try{const h=w(),f=A(),_=h.map(U=>({id:U.id,type:U.type,position:{x:U.position.x,y:U.position.y},x:U.position.x,y:U.position.y,data:{label:U.data.label,year:U.data.year,month:U.data.month,day:U.data.day,content:U.data.content,nodeType:U.data.nodeType,color:U.data.color,parentId:U.data.parentId,isLeftSide:U.data.isLeftSide,customTime:U.data.customTime,useCustomTime:U.data.useCustomTime},label:U.data.label,year:U.data.year,month:U.data.month,day:U.data.day,content:U.data.content,nodeType:U.data.nodeType,color:U.data.color,parentId:U.data.parentId,isLeftSide:U.data.isLeftSide,customTime:U.data.customTime,useCustomTime:U.data.useCustomTime})),B=f.map(U=>({id:U.id,source:U.source,target:U.target,sourceHandle:U.sourceHandle,targetHandle:U.targetHandle,type:U.type,animated:U.animated,style:{strokeWidth:U.style?.strokeWidth||2,stroke:U.style?.stroke||"#409EFF"},markerEnd:U.markerEnd})),j=JSON.stringify({nodes:_,edges:B,version:"2.0"},null,2);try{window.pywebview.api.copy_to_clipboard(j).then(()=>{Ie.success("数据已复制到剪贴板并开始下载")}).catch(U=>{console.error("剪贴板复制失败:",U),Ie.info("数据已导出为文件")})}catch(U){console.error("剪贴板访问失败:",U),Ie.info("数据已导出为文件")}const ue=new Blob([j],{type:"application/json"}),oe=URL.createObjectURL(ue),we=document.createElement("a");we.href=oe,we.download=`timeline-data-${new Date().toISOString().split("T")[0]}.json`,we.click(),URL.revokeObjectURL(oe)}catch(h){console.error("导出数据时出错:",h),Ie.error("导出数据失败")}},Ut=ge(!1),pn=ge(""),jo=()=>{const h=`main-${et(6)}`,f={id:h,type:"main-event",position:{x:Be,y:100},connectable:!0,data:{label:"故事起点",year:new Date().getFullYear(),month:new Date().getMonth()+1,day:new Date().getDate(),content:"在此处开始您的故事",nodeType:"main",color:"#409EFF"}};return a.value=[f],s.value=[],console.log("已创建默认主线事件节点:",h),He(()=>{K()}),{nodes:[f],edges:[]}};t({addMainEvent:Et,addBranchEvent:oo,autoLayout:ro,getNodes:w,getEdges:A,fitView:K,exportTimelineData:_a,createDefaultMainEvent:jo,importFromJson:h=>{try{if(typeof h=="string")try{h=JSON.parse(h)}catch(_){return console.error("解析数据失败:",_),!1}return h&&h.status==="success"&&h.data&&(h=h.data),!h||!h.nodes&&!h.edges&&!h.mainTrunkEvents?(console.error("无效的时间线数据格式"),!1):(!h.nodes||h.nodes.length===0)&&(!h.mainTrunkEvents||h.mainTrunkEvents.length===0)?(console.log("导入的数据为空，创建一个默认节点"),jo(),!0):(a.value=[],s.value=[],He(()=>{if(h.nodes&&h.edges){const _=h.nodes.map(C=>({id:C.id||`node-${et(6)}`,type:C.nodeType==="main"?"main-event":"branch-event",position:{x:C.nodeType==="main"?Be:C.x||C.position?.x||(C.isLeftSide?Be-250:Be+250),y:C.y||C.position?.y||100},data:{label:C.label||C.data?.label||"未命名事件",year:Number(C.year||C.data?.year||new Date().getFullYear()),month:Number(C.month||C.data?.month||new Date().getMonth()+1),day:Number(C.day||C.data?.day||new Date().getDate()),content:C.content||C.data?.content||"",nodeType:C.nodeType||C.data?.nodeType||"main",color:C.color||C.data?.color||(C.nodeType==="main"?"#409EFF":Z()),parentId:C.parentId||C.data?.parentId,isLeftSide:C.isLeftSide||C.data?.isLeftSide,customTime:C.customTime||C.data?.customTime,useCustomTime:C.useCustomTime||C.data?.useCustomTime}}));console.log(`导入 ${_.length} 个节点`),a.value=_;const B=h.edges.map(C=>({id:C.id||`edge-${et(6)}`,source:C.source,target:C.target,sourceHandle:C.sourceHandle,targetHandle:C.targetHandle,type:C.type||o.SmoothStep,animated:C.animated||!1,style:{strokeWidth:C.style?.strokeWidth||2,stroke:C.style?.stroke||"#409EFF"},markerEnd:C.markerEnd||{type:r.ArrowClosed,color:C.style?.stroke||"#409EFF"},data:C.data}));console.log(`导入 ${B.length} 条边`),s.value=B}else if(h.mainTrunkEvents){const _=h.mainTrunkEvents.map((oe,we)=>({id:oe.id||`main-${et(6)}`,type:"main-event",position:{x:Be,y:100+we*150},data:{label:oe.title,year:Number(oe.year),month:Number(oe.month),day:Number(oe.day),content:oe.description||"",nodeType:"main",color:"#409EFF"}})),B=[];for(let oe=0;oe<_.length-1;oe++)B.push({id:`e-${_[oe].id}-${_[oe+1].id}`,source:_[oe].id,sourceHandle:"bottom",target:_[oe+1].id,targetHandle:"top",type:o.SmoothStep,style:{strokeWidth:4,stroke:"#409EFF"},markerEnd:{type:r.ArrowClosed,color:"#409EFF"}});const C=[],j=[];if(h.branches&&h.branchEvents){const oe={};h.branches.forEach(we=>{we.origin&&we.origin.id&&(oe[we.id]={mainNodeId:we.origin.id,point:we.origin.point||"right",color:we.color||Z()})}),h.branchEvents.forEach((we,U)=>{const _e=oe[we.branchId];if(!_e)return;const Ue=_e.point==="left",it={id:we.id||`branch-${et(6)}`,type:"branch-event",position:{x:Ue?Be-250:Be+250,y:100+U*100},data:{label:we.title,year:Number(we.year),month:Number(we.month),day:Number(we.day),content:we.description||"",nodeType:"branch",color:_e.color,parentId:_e.mainNodeId,isLeftSide:Ue}};C.push(it),j.push({id:`e-${_e.mainNodeId}-${it.id}`,source:_e.mainNodeId,sourceHandle:Ue?"left":"right",target:it.id,targetHandle:Ue?"right":"left",type:o.SmoothStep,animated:!0,style:{strokeWidth:3,stroke:_e.color},markerEnd:{type:r.ArrowClosed,color:_e.color}})})}const ue=[];h.connections&&h.connections.length>0&&h.connections.forEach(oe=>{ue.push({id:`e-custom-${et(6)}`,source:oe.source,target:oe.target,type:o.SmoothStep,animated:oe.animated||!1,style:{strokeWidth:oe.style?.strokeWidth||2,stroke:oe.style?.stroke||"#409EFF"},markerEnd:{type:r.ArrowClosed,color:oe.style?.stroke||"#409EFF"}})}),a.value=[..._,...C],s.value=[...B,...j,...ue]}He(()=>{ro()})}),!0)}catch(f){return console.error("导入数据时出错:",f),!1}},getTimelineData:()=>{try{const h=w(),f=A(),_=h.map(C=>({id:C.id,type:C.type,x:C.position.x,y:C.position.y,data:C.data,nodeType:C.data.nodeType})),B=f.map(C=>({id:C.id,source:C.source,target:C.target,sourceHandle:C.sourceHandle,targetHandle:C.targetHandle,type:C.type,animated:C.animated,style:{strokeWidth:C.style?.strokeWidth||2,stroke:C.style?.stroke||"#409EFF"},markerEnd:C.markerEnd}));return{nodes:_,edges:B,version:"2.0"}}catch(h){throw console.error("获取时间线数据失败:",h),h}},triggerSave:()=>{const h=new CustomEvent("save-timeline");document.dispatchEvent(h)}});const ba=()=>{try{const h=JSON.parse(pn.value);if(!h||!h.nodes||!h.edges){Pt.alert("无效的数据格式","错误",{type:"error"});return}a.value=[],s.value=[],h.nodes.forEach(_=>{const B={x:_.position?.x||_.x||(_.nodeType==="main"?Be:_.isLeftSide?Be-250:Be+250),y:_.position?.y||_.y||100},C={label:_.data?.label||_.label||"未命名事件",year:_.data?.year||_.year||new Date().getFullYear(),month:_.data?.month||_.month||new Date().getMonth()+1,day:_.data?.day||_.day||new Date().getDate(),content:_.data?.content||_.content||"",nodeType:_.data?.nodeType||_.nodeType||"main",color:_.data?.color||_.color||(_.nodeType==="main"?"#409EFF":Z()),parentId:_.data?.parentId||_.parentId,isLeftSide:_.data?.isLeftSide||_.isLeftSide,customTime:_.data?.customTime||_.customTime,useCustomTime:_.data?.useCustomTime||_.useCustomTime};P([{id:_.id,type:_.type,position:B,data:C}])}),h.edges.filter(_=>{const B=h.nodes.find(j=>j.id===_.source)?.data||h.nodes.find(j=>j.id===_.source),C=h.nodes.find(j=>j.id===_.target)?.data||h.nodes.find(j=>j.id===_.target);return!((B?.nodeType==="main"||B?.data?.nodeType==="main")&&(C?.nodeType==="main"||C?.data?.nodeType==="main"))}).forEach(_=>{const B={id:_.id,source:_.source,target:_.target,sourceHandle:_.sourceHandle,targetHandle:_.targetHandle,type:_.type||o.SmoothStep,animated:_.animated||!1,style:{strokeWidth:_.style?.strokeWidth||2,stroke:_.style?.stroke||"#409EFF"},markerEnd:_.markerEnd||{type:r.ArrowClosed,color:_.style?.stroke||"#409EFF"}};D([B])}),Ut.value=!1,pn.value="",He(()=>{fe(),K(),Ie.success("数据导入成功")})}catch(h){console.error("导入数据时出错:",h),Pt.alert("导入数据失败: "+h.message,"错误",{type:"error"})}},ao=["#409EFF","#e84393","#00b894","#e17055","#6c5ce7","#fdcb6e","#00cec9","#ff7675","#74b9ff","#a29bfe","#55efc4","#fab1a0","#ff9ff3","#ffeaa7","#636e72","#2d3436","#b71540","#0a3d62"],Zt=ge({sourceNodeId:"",direction:"",position:{x:0,y:0},isFreeEvent:!1,nodeType:""}),gn=(h,f)=>{const _=O(h);if(!_)return;let B={x:_.position.x,y:_.position.y};switch(f){case"up":B.y-=180;break;case"down":B.y+=80;break;case"left":B.x-=250;break;case"right":B.x+=250;break}Zt.value={sourceNodeId:h,direction:f,position:B,isFreeEvent:!1,nodeType:f==="left"||f==="right"?"branch":_.data.nodeType};const C={label:"新事件",year:new Date().getFullYear(),month:new Date().getMonth()+1,day:new Date().getDate(),content:"",nodeType:Zt.value.nodeType,color:Zt.value.nodeType==="main"?"#409EFF":Z(),isLeftSide:f==="left"};d.value=C,g.value="new-node",c.value=!0},Ea=h=>{const f=O(h);if(!f)return;const _={x:f.position.x+100,y:f.position.y+100};Zt.value={sourceNodeId:h,direction:"free",position:_,isFreeEvent:!0,nodeType:f.data.nodeType};const B={label:"自由事件",year:new Date().getFullYear(),month:new Date().getMonth()+1,day:new Date().getDate(),content:"",nodeType:f.data.nodeType,color:f.data.nodeType==="main"?"#409EFF":Z()};d.value=B,g.value="new-node",c.value=!0},so=h=>({"#409EFF":"主线/主要事件 - 蓝色","#e84393":"爱情/情感 - 粉红色","#00b894":"成长/进步 - 绿色","#e17055":"冲突/战斗 - 橙红色","#6c5ce7":"神秘/魔法 - 紫色","#fdcb6e":"发现/启示 - 黄色","#00cec9":"旅程/探索 - 青色","#ff7675":"危机/危险 - 红色","#74b9ff":"回忆/过去 - 淡蓝色","#a29bfe":"梦境/幻想 - 淡紫色","#55efc4":"希望/治愈 - 薄荷绿","#fab1a0":"友情/同伴 - 淡橙色","#ff9ff3":"浪漫/爱慕 - 粉色","#ffeaa7":"欢乐/庆祝 - 淡黄色","#636e72":"悲伤/失落 - 灰色","#2d3436":"黑暗/恐惧 - 深灰色","#b71540":"仇恨/复仇 - 深红色","#0a3d62":"智慧/思考 - 深蓝色"})[h]||"未知颜色",ei=h=>({"#409EFF":"主线/主要事件 - 蓝色","#e84393":"爱情/情感 - 粉红色","#00b894":"成长/进步 - 绿色","#e17055":"冲突/战斗 - 橙红色","#6c5ce7":"神秘/魔法 - 紫色","#fdcb6e":"发现/启示 - 黄色","#00cec9":"旅程/探索 - 青色","#ff7675":"危机/危险 - 红色","#74b9ff":"回忆/过去 - 淡蓝色","#a29bfe":"梦境/幻想 - 淡紫色","#55efc4":"希望/治愈 - 薄荷绿","#fab1a0":"友情/同伴 - 淡橙色","#ff9ff3":"浪漫/爱慕 - 粉色","#ffeaa7":"欢乐/庆祝 - 淡黄色","#636e72":"悲伤/失落 - 灰色","#2d3436":"黑暗/恐惧 - 深灰色","#b71540":"仇恨/复仇 - 深红色","#0a3d62":"智慧/思考 - 深蓝色"})[h]||"未知颜色",xa=h=>{d.value.color=h,Ie({message:`已选择: ${ei(h)}`,type:"success",duration:1500})},Sa=h=>{if(h&&d.value.year)d.value.customTime||(d.value.customTime=`${d.value.year}年${d.value.month||""}月${d.value.day||""}日`);else if(!h&&d.value.customTime){const f=d.value.customTime.match(/(\d+)\s*年/),_=d.value.customTime.match(/(\d+)\s*月/),B=d.value.customTime.match(/(\d+)\s*日/);f&&(d.value.year=parseInt(f[1])),_&&(d.value.month=parseInt(_[1])),B&&(d.value.day=parseInt(B[1]))}},ti=h=>{if((h.ctrlKey||h.metaKey)&&h.key==="s"){h.preventDefault();const f=new CustomEvent("save-timeline");document.dispatchEvent(f),console.log("触发保存事件: Ctrl+S")}},At=()=>{const h=new CustomEvent("timeline-data-changed");document.dispatchEvent(h)};return(h,f)=>{const _=Ki,B=qi,C=Ba,j=Va,ue=Ha,oe=Ra,we=La,U=Ya,_e=Xa,Ue=Wa,it=Ji;return re(),ve("div",{class:ut(["timeline-flow-wrapper",i.value])},[X(V(ah),{nodes:a.value,edges:s.value,"default-viewport":{x:0,y:0,zoom:.85},"min-zoom":.3,"max-zoom":2,"nodes-draggable":!0,"snap-to-grid":!0,"snap-grid":[20,20],"connection-line-style":{stroke:"#409EFF",strokeWidth:2},"connection-line-type":"smoothstep","default-edge-options":E,"connect-on-click":!1,"edges-updatable":!0,"edges-draggable":!1,"edge-updatable":!0,"enable-pan-on-drag":!0,"enable-pan-on-scroll":!0,"enable-zoom-on-scroll":!0,"fit-view-on-init":!0,"fit-view-padding":[50,50],class:"timeline-flow",onNodesChange:de,onEdgesChange:ke,onConnect:b,onEdgeUpdate:y,onEdgeClick:z,onEdgeContextmenu:le,onPaneclick:xe,onNodeClick:Se,onNodeDrag:Ne,onNodeDragStop:$e,onNodeContextMenu:v},{"node-main-event":pe(ae=>[X(Qh,xt(St(ae)),null,16)]),"node-branch-event":pe(ae=>[X(ip,xt(St(ae)),null,16)]),"edge-bezier":pe(ae=>[X(V(ia),xt(St(ae)),null,16)]),"edge-straight":pe(ae=>[X(V(na),xt(St(ae)),null,16)]),"edge-smoothstep":pe(ae=>[X(V(qo),xt(St(ae)),null,16)]),"edge-step":pe(ae=>[X(V(oa),xt(St(ae)),null,16)]),"edge-simplebezier":pe(ae=>[X(V(ra),xt(St(ae)),null,16)]),default:pe(()=>[X(V(ph),{"pattern-color":"#aaa",gap:20,size:1}),X(V(Xh)),X(V(aa),{position:"top-right",class:"panel-top"},{default:pe(()=>[G("div",rp,[X(_,{type:"primary",onClick:se,size:"small",icon:V(Fa)},{default:pe(()=>[Xe(Qe(Y.value?"退出全屏":"全屏"),1)]),_:1},8,["icon"])])]),_:1})]),_:1},8,["nodes","edges"]),c.value?(re(),ve("div",{key:0,class:"custom-dialog-overlay",onClick:ga},[G("div",{class:"custom-dialog",onClick:f[8]||(f[8]=lo(()=>{},["stop"]))},[G("div",{class:"custom-dialog-header"},[f[35]||(f[35]=G("h3",null,"编辑事件详情",-1)),G("button",{class:"custom-dialog-close",onClick:io},"×")]),G("div",ap,[X(_e,{model:d.value,"label-width":"60px",class:"edit-event-form",size:"small"},{default:pe(()=>[X(C,{label:"标题"},{default:pe(()=>[X(B,{modelValue:d.value.label,"onUpdate:modelValue":f[0]||(f[0]=ae=>d.value.label=ae),placeholder:"请输入事件标题"},null,8,["modelValue"])]),_:1}),X(C,{label:"时间"},{default:pe(()=>[G("div",sp,[X(j,{modelValue:l.value,"onUpdate:modelValue":f[1]||(f[1]=ae=>l.value=ae),"active-text":"自定义时间","inactive-text":"标准时间","inline-prompt":"",onChange:Sa},null,8,["modelValue"])]),l.value?(re(),ve("div",up,[X(B,{modelValue:d.value.customTime,"onUpdate:modelValue":f[5]||(f[5]=ae=>d.value.customTime=ae),placeholder:"如：第三纪元 2415年",class:"custom-time-input"},null,8,["modelValue"])])):(re(),ve("div",lp,[X(ue,{modelValue:d.value.year,"onUpdate:modelValue":f[2]||(f[2]=ae=>d.value.year=ae),min:1900,max:2100,placeholder:"年",controls:!1,class:"date-input-year"},null,8,["modelValue"]),f[36]||(f[36]=G("span",{class:"date-separator"},"年",-1)),X(ue,{modelValue:d.value.month,"onUpdate:modelValue":f[3]||(f[3]=ae=>d.value.month=ae),min:1,max:12,placeholder:"月",controls:!1,class:"date-input-month"},null,8,["modelValue"]),f[37]||(f[37]=G("span",{class:"date-separator"},"月",-1)),X(ue,{modelValue:d.value.day,"onUpdate:modelValue":f[4]||(f[4]=ae=>d.value.day=ae),min:1,max:31,placeholder:"日",controls:!1,class:"date-input-day"},null,8,["modelValue"]),f[38]||(f[38]=G("span",{class:"date-separator"},"日",-1))]))]),_:1}),X(C,{label:"内容"},{default:pe(()=>[X(B,{modelValue:d.value.content,"onUpdate:modelValue":f[6]||(f[6]=ae=>d.value.content=ae),type:"textarea",rows:4,placeholder:"请输入事件内容描述",class:"content-textarea"},null,8,["modelValue"])]),_:1}),d.value.nodeType==="branch"?(re(),We(C,{key:0,label:"颜色"},{default:pe(()=>[G("div",cp,[X(oe,{modelValue:d.value.color,"onUpdate:modelValue":f[7]||(f[7]=ae=>d.value.color=ae),"show-alpha":"",predefine:ao},null,8,["modelValue"]),G("div",dp,[X(we,{placement:"right",content:so(d.value.color),effect:"light"},{default:pe(()=>[G("span",null,"当前: "+Qe(ei(d.value.color)),1)]),_:1},8,["content"]),X(U,{placement:"right",width:380,trigger:"click","popper-class":"color-popover"},{default:pe(()=>[f[39]||(f[39]=G("div",{class:"color-popover-header"},[G("h4",null,"选择事件颜色"),G("p",{class:"color-popover-tip"},"点击颜色直接选择")],-1)),G("div",fp,[(re(),ve(Ze,null,Ln(ao,(ae,ka)=>G("div",{class:ut(["color-item",{"color-item-active":d.value.color===ae}]),key:ka,onClick:Pp=>xa(ae)},[X(we,{content:`点击选择: ${so(ae)}`,placement:"top",effect:"light","show-after":300},{default:pe(()=>[G("div",{class:"color-swatch",style:Ge({backgroundColor:ae})},null,4)]),_:2},1032,["content"]),G("span",pp,Qe(so(ae)),1)],10,hp)),64))])]),reference:pe(()=>[X(_,{type:"primary",size:"small",plain:"",class:"color-help-button",icon:V(Ga)},{default:pe(()=>f[40]||(f[40]=[Xe(" 查看更多颜色含义 ")])),_:1},8,["icon"])]),_:1})])])]),_:1})):Pe("",!0)]),_:1},8,["model"])]),G("div",gp,[X(_,{onClick:io},{default:pe(()=>f[41]||(f[41]=[Xe("取消")])),_:1}),X(_,{type:"primary",onClick:ua},{default:pe(()=>f[42]||(f[42]=[Xe("确认")])),_:1})])])])):Pe("",!0),Q.value?(re(),ve("div",{key:1,class:"custom-dialog-overlay",onClick:ma},[G("div",{class:"custom-dialog custom-dialog-small",onClick:f[14]||(f[14]=lo(()=>{},["stop"]))},[G("div",mp,[f[43]||(f[43]=G("h3",null,"编辑连接样式",-1)),G("button",{class:"custom-dialog-close",onClick:f[9]||(f[9]=ae=>Q.value=!1)},"×")]),G("div",vp,[X(_e,{model:me.value,"label-width":"60px",class:"edge-edit-form",size:"small"},{default:pe(()=>[X(C,{label:"线宽"},{default:pe(()=>[X(Ue,{modelValue:me.value.strokeWidth,"onUpdate:modelValue":f[10]||(f[10]=ae=>me.value.strokeWidth=ae),min:1,max:10,step:1},null,8,["modelValue"])]),_:1}),X(C,{label:"颜色"},{default:pe(()=>[X(oe,{modelValue:me.value.stroke,"onUpdate:modelValue":f[11]||(f[11]=ae=>me.value.stroke=ae),"show-alpha":"",predefine:ao},null,8,["modelValue"])]),_:1}),X(C,{label:"动画效果"},{default:pe(()=>[X(j,{modelValue:me.value.animated,"onUpdate:modelValue":f[12]||(f[12]=ae=>me.value.animated=ae)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),G("div",yp,[X(_,{onClick:f[13]||(f[13]=ae=>Q.value=!1)},{default:pe(()=>f[44]||(f[44]=[Xe("取消")])),_:1}),X(_,{type:"primary",onClick:Ae},{default:pe(()=>f[45]||(f[45]=[Xe("确认")])),_:1})])])])):Pe("",!0),Oa(G("div",{class:"context-menu",style:Ge(M.value)},[N.value==="main"?(re(),ve(Ze,{key:0},[G("div",{class:"context-menu-item",onClick:f[15]||(f[15]=ae=>p("add-up"))}," 向上添加事件 "),G("div",{class:"context-menu-item",onClick:f[16]||(f[16]=ae=>p("add-down"))}," 向下添加事件 "),G("div",{class:"context-menu-item",onClick:f[17]||(f[17]=ae=>p("add-left"))}," 向左添加事件 "),G("div",{class:"context-menu-item",onClick:f[18]||(f[18]=ae=>p("add-right"))}," 向右添加事件 "),G("div",{class:"context-menu-item",onClick:f[19]||(f[19]=ae=>p("add-free"))}," 添加自由事件 "),G("div",{class:"context-menu-item",onClick:f[20]||(f[20]=ae=>p("edit"))}," 编辑节点 "),G("div",{class:"context-menu-item",onClick:f[21]||(f[21]=ae=>p("delete"))}," 删除节点 ")],64)):N.value==="branch"?(re(),ve(Ze,{key:1},[G("div",{class:"context-menu-item",onClick:f[22]||(f[22]=ae=>p("add-up"))}," 向上添加事件 "),G("div",{class:"context-menu-item",onClick:f[23]||(f[23]=ae=>p("add-down"))}," 向下添加事件 "),G("div",{class:"context-menu-item",onClick:f[24]||(f[24]=ae=>p("add-left"))}," 向左添加事件 "),G("div",{class:"context-menu-item",onClick:f[25]||(f[25]=ae=>p("add-right"))}," 向右添加事件 "),G("div",{class:"context-menu-item",onClick:f[26]||(f[26]=ae=>p("add-free"))}," 添加自由事件 "),G("div",{class:"context-menu-item",onClick:f[27]||(f[27]=ae=>p("edit"))}," 编辑节点 "),G("div",{class:"context-menu-item",onClick:f[28]||(f[28]=ae=>p("delete"))}," 删除节点 ")],64)):T.value==="edge"?(re(),ve(Ze,{key:2},[G("div",{class:"context-menu-item",onClick:f[29]||(f[29]=ae=>ie("change-style"))}," 修改样式 "),G("div",{class:"context-menu-item",onClick:f[30]||(f[30]=ae=>ie("delete"))}," 删除连接 ")],64)):Pe("",!0)],4),[[Ua,m.value]]),Ut.value?(re(),ve("div",{key:2,class:"custom-dialog-overlay",onClick:va},[G("div",{class:"custom-dialog",onClick:f[34]||(f[34]=lo(()=>{},["stop"]))},[G("div",wp,[f[46]||(f[46]=G("h3",null,"导入时间线数据",-1)),G("button",{class:"custom-dialog-close",onClick:f[31]||(f[31]=ae=>Ut.value=!1)},"×")]),G("div",_p,[G("div",bp,[X(it,{title:"数据格式提示",type:"info",description:"请粘贴有效的JSON格式数据，包含节点和连接信息","show-icon":"",closable:!1,size:"small"}),X(B,{modelValue:pn.value,"onUpdate:modelValue":f[32]||(f[32]=ae=>pn.value=ae),type:"textarea",rows:9,placeholder:"粘贴JSON数据...",class:"import-textarea"},null,8,["modelValue"])])]),G("div",Ep,[X(_,{onClick:f[33]||(f[33]=ae=>Ut.value=!1),size:"small"},{default:pe(()=>f[47]||(f[47]=[Xe("取消")])),_:1}),X(_,{type:"primary",onClick:ba,size:"small"},{default:pe(()=>f[48]||(f[48]=[Xe("导入")])),_:1})])])])):Pe("",!0)],2)}}},Sp=Yn(xp,[["__scopeId","data-v-e4aa443e"]]),kp={class:"timeline-page"},Np={class:"flow-timeline-section"},Cp={class:"flow-header"},$p={class:"book-title"},Tp={class:"flow-controls"},Mp={class:"import-container"},Ip={class:"import-tips"},Dp={class:"dialog-footer"},Ap={__name:"时间线",setup(e){const t=Za(),n=Ja(),i=ge(t.query.id||t.params.id),o=ge(t.query.title||t.params.title||"我的时间线"),r=ge(null),a=ge(!1),s=ge(""),l=ge(!1);let u=null;const c=ge(!1);let d=null;function g(){if(r.value)try{r.value.exportTimelineData()}catch(P){console.error("导出失败:",P),Ie.error("导出失败: "+P.message)}else Ie.warning("时间线组件未初始化")}async function m(){if(!r.value){Ie.warning("时间线组件未初始化");return}try{const P=ni.service({lock:!0,text:"准备导出TXT文件...",background:"rgba(255, 255, 255, 0.7)"}),D=r.value.getTimelineData();await I(!1);const W=await window.pywebview.api.export_timeline_to_txt(i.value,JSON.stringify(D));let F;try{F=JSON.parse(W)}catch(R){console.error("解析响应数据失败:",R),P.close(),Ie.error("导出失败: 数据格式错误");return}P.close(),F&&F.status==="success"?Ie.success(F.message||"时间线已成功导出为TXT文件"):Ie.error("导出失败: "+(F?.message||"未知错误"))}catch(P){console.error("导出TXT失败:",P),Ie.error("导出失败: "+(P.message||"未知错误"))}}function M(){a.value=!0}function k(){if(r.value)try{if(!s.value.trim()){Ie.warning("请输入有效的JSON数据");return}let P;try{P=JSON.parse(s.value)}catch{Ie.error("JSON格式无效，请检查数据格式");return}if(!P||!P.nodes&&!P.edges&&!P.mainTrunkEvents){Ie.error("数据格式不正确，缺少必要的节点或边数据");return}r.value.importFromJson(P)?(Ie.success("数据导入成功"),a.value=!1,s.value="",I(!1)):Ie.error("导入失败，请检查数据格式")}catch(P){console.error("导入失败:",P),Ie.error("导入失败: "+P.message)}else Ie.warning("时间线组件未初始化")}function N(){n.push({name:"bookWriting",query:{id:i.value}})}function T(){const P=()=>{c.value=!0};document.addEventListener("timeline-data-changed",P),d=P,u=setInterval(()=>{c.value&&(console.log("自动保存时间线数据..."),I(!1))},1*60*1e3)}async function I(P=!1){if(!r.value){console.error("时间线组件未初始化，无法保存");return}try{l.value=!0;const D=r.value.getTimelineData();await window.pywebview.api.save_timeline(i.value,JSON.stringify(D)),c.value=!1,console.log("时间线数据保存成功"),P&&Ie({message:"时间线数据已保存",type:"success",offset:70,duration:2e3})}catch(D){console.error("保存时间线数据失败:",D),Ie.error("保存失败: "+(D.message||"未知错误"))}finally{l.value=!1}}ct(async()=>{T(),await E(),document.addEventListener("save-timeline",O),window.addEventListener("beforeunload",te)});async function E(){try{const P=ni.service({lock:!0,text:"加载时间线数据...",background:"rgba(255, 255, 255, 0.7)"});try{const D=await window.pywebview.api.get_timeline(i.value);let W;try{W=JSON.parse(D)}catch(R){console.error("解析响应数据失败:",R),P.close(),Ie.error("加载时间线数据失败: 数据格式错误");return}if(W&&W.status==="success"&&W.data&&(W.data.nodes&&W.data.nodes.length>0||W.data.mainTrunkEvents&&W.data.mainTrunkEvents.length>0)){const R=$(W.data);He(()=>{r.value&&(r.value.importFromJson(R),P.close(),Ie.success("时间线数据加载成功"))})}else if(P.close(),Ie.info("没有找到时间线数据，正在创建默认主线事件..."),r.value){const R=r.value.createDefaultMainEvent();console.log("创建的默认数据:",R),await I(!1),Ie.success("已创建默认时间线起点")}else Ie.warning("时间线组件未初始化，无法创建默认事件")}catch(D){console.error("加载时间线数据失败:",D),P.close(),Ie.error("加载时间线数据失败: "+D.message)}}catch(P){console.error("初始化加载失败:",P),Ie.error("初始化加载失败: "+P.message)}}function $(P){if(P.nodes&&P.edges)return P;let D=[],W=[];return P.mainTrunkEvents&&P.mainTrunkEvents.forEach((F,R)=>{if(D.push({id:F.id,type:"main-event",position:{x:400,y:100+R*150},data:{label:F.title,year:F.year,month:F.month,day:F.day,content:F.description||"",nodeType:"main",color:"#409EFF"}}),R>0){const x=P.mainTrunkEvents[R-1].id;W.push({id:`e-${x}-${F.id}`,source:x,target:F.id,sourceHandle:"bottom",targetHandle:"top",type:"smoothstep",style:{strokeWidth:4,stroke:"#409EFF"}})}}),P.branches&&P.branchEvents&&P.branches.forEach(F=>{P.branchEvents.filter(x=>x.branchId===F.id).forEach((x,K)=>{const w=F.origin?.point==="left",A=w?150:650,S=F.origin?.position?.y||100+K*150;D.push({id:x.id,type:"branch-event",position:{x:A,y:S},data:{label:x.title,year:x.year,month:x.month,day:x.day,content:x.description||"",nodeType:"branch",color:F.color,isLeftSide:w,parentId:F.origin?.id||null}}),F.origin&&F.origin.id&&W.push({id:`e-${F.origin.id}-${x.id}`,source:F.origin.id,target:x.id,sourceHandle:w?"left":"right",targetHandle:w?"right":"left",type:"smoothstep",animated:!0,style:{strokeWidth:3,stroke:F.color}})})}),P.connections&&P.connections.length>0&&P.connections.forEach(F=>{W.push({id:`e-${F.source}-${F.target}-custom`,source:F.source,target:F.target,type:"smoothstep",animated:F.animated||!1,style:{strokeWidth:F.style?.strokeWidth||2,stroke:F.style?.stroke||"#409EFF"}})}),{nodes:D,edges:W}}Bo(()=>{u&&(clearInterval(u),u=null),typeof d=="function"&&document.removeEventListener("timeline-data-changed",d),document.removeEventListener("save-timeline",O),window.removeEventListener("beforeunload",te)});function O(){I(!0)}function te(P){if(c.value){const D="您有未保存的更改，确定要离开吗？";return P.returnValue=D,D}}return(P,D)=>{const W=Ki,F=ts,R=Ka,x=Ji,K=qi,w=qa;return re(),ve("div",kp,[G("div",Np,[G("div",Cp,[G("h1",$p,Qe(o.value)+" - 时间线",1),G("div",Tp,[X(R,null,{default:pe(()=>[X(W,{type:"success",plain:"",onClick:g,icon:V(Qa)},{default:pe(()=>D[4]||(D[4]=[Xe(" 导出数据 ")])),_:1},8,["icon"]),X(W,{type:"success",plain:"",onClick:m,icon:V(ja)},{default:pe(()=>D[5]||(D[5]=[Xe(" 导出TXT ")])),_:1},8,["icon"]),X(W,{type:"info",plain:"",onClick:M,icon:V(es)},{default:pe(()=>D[6]||(D[6]=[Xe(" 导入数据 ")])),_:1},8,["icon"]),X(W,{type:"primary",plain:"",onClick:D[0]||(D[0]=A=>I(!0)),loading:l.value},{default:pe(()=>[X(F,null,{default:pe(()=>[X(V(ns))]),_:1}),D[7]||(D[7]=Xe(" 保存数据 "))]),_:1},8,["loading"]),X(W,{class:"back-button",onClick:N,type:"primary",plain:""},{default:pe(()=>[X(F,null,{default:pe(()=>[X(V(os))]),_:1}),D[8]||(D[8]=Xe(" 返回写作 "))]),_:1})]),_:1})])]),X(Sp,{ref_key:"timelineFlowRef",ref:r},null,512),X(w,{modelValue:a.value,"onUpdate:modelValue":D[3]||(D[3]=A=>a.value=A),title:"导入时间线数据",width:"500px","close-on-click-modal":!1,class:"native-style-dialog"},{footer:pe(()=>[G("span",Dp,[X(W,{onClick:D[2]||(D[2]=A=>a.value=!1)},{default:pe(()=>D[11]||(D[11]=[Xe("取消")])),_:1}),X(W,{type:"primary",onClick:k},{default:pe(()=>D[12]||(D[12]=[Xe("导入")])),_:1})])]),default:pe(()=>[G("div",Mp,[X(x,{title:"导入说明",type:"info",closable:!1},{default:pe(()=>D[9]||(D[9]=[G("p",null,"请粘贴之前导出的JSON格式数据，支持以下方式导入：",-1),G("ol",{class:"import-instructions"},[G("li",null,"从导出功能复制的JSON数据"),G("li",null,"从已导出的JSON文件中复制内容")],-1)])),_:1}),X(K,{modelValue:s.value,"onUpdate:modelValue":D[1]||(D[1]=A=>s.value=A),type:"textarea",rows:10,placeholder:"在此粘贴JSON数据...",class:"import-textarea"},null,8,["modelValue"]),G("div",Ip,[X(F,null,{default:pe(()=>[X(V(is))]),_:1}),D[10]||(D[10]=G("span",null,"导入会替换当前所有时间线数据，请确保已保存重要内容",-1))])])]),_:1},8,["modelValue"])])])}}},Xp=Yn(Ap,[["__scopeId","data-v-88be4916"]]);export{Xp as default};
