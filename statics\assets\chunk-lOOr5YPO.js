import{_ as nn,r as S,bk as on,c as ft,o as an,U as ln,E as p,b as X,m as U,e as v,d as c,g as T,t as rn,B as sn,C as Ee,aa as cn,s as un,p as Pe,$ as pt,v as W,ee as mn,ah as dn,F as He,a5 as fn,ef as pn,aD as _n,aK as hn,aL as gn,aJ as Tn,al as En,ak as yn,an as vn,bB as An,X as wn,Y as bn,a7 as Sn,ac as Ln,b8 as Mn,k as Rn,R as ye}from"./entry-BIjVVog3.js";/* empty css                  *//* empty css                 *//* empty css                        *//* empty css                   *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                    *//*! @license DOMPurify 3.2.4 | (c) Cure<PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.4/LICENSE */const{entries:Vt,setPrototypeOf:kt,isFrozen:Dn,getPrototypeOf:Cn,getOwnPropertyDescriptor:On}=Object;let{freeze:k,seal:V,create:Yt}=Object,{apply:yt,construct:vt}=typeof Reflect<"u"&&Reflect;k||(k=function(a){return a});V||(V=function(a){return a});yt||(yt=function(a,m,s){return a.apply(m,s)});vt||(vt=function(a,m){return new a(...m)});const Qe=P(Array.prototype.forEach),Nn=P(Array.prototype.lastIndexOf),Pt=P(Array.prototype.pop),Ue=P(Array.prototype.push),xn=P(Array.prototype.splice),tt=P(String.prototype.toLowerCase),_t=P(String.prototype.toString),Ht=P(String.prototype.match),Fe=P(String.prototype.replace),In=P(String.prototype.indexOf),kn=P(String.prototype.trim),j=P(Object.prototype.hasOwnProperty),I=P(RegExp.prototype.test),ze=Pn(TypeError);function P(i){return function(a){for(var m=arguments.length,s=new Array(m>1?m-1:0),h=1;h<m;h++)s[h-1]=arguments[h];return yt(i,a,s)}}function Pn(i){return function(){for(var a=arguments.length,m=new Array(a),s=0;s<a;s++)m[s]=arguments[s];return vt(i,m)}}function u(i,a){let m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:tt;kt&&kt(i,null);let s=a.length;for(;s--;){let h=a[s];if(typeof h=="string"){const D=m(h);D!==h&&(Dn(a)||(a[s]=D),h=D)}i[h]=!0}return i}function Hn(i){for(let a=0;a<i.length;a++)j(i,a)||(i[a]=null);return i}function de(i){const a=Yt(null);for(const[m,s]of Vt(i))j(i,m)&&(Array.isArray(s)?a[m]=Hn(s):s&&typeof s=="object"&&s.constructor===Object?a[m]=de(s):a[m]=s);return a}function We(i,a){for(;i!==null;){const s=On(i,a);if(s){if(s.get)return P(s.get);if(typeof s.value=="function")return P(s.value)}i=Cn(i)}function m(){return null}return m}const Ut=k(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),ht=k(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),gt=k(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Un=k(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Tt=k(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Fn=k(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Ft=k(["#text"]),zt=k(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Et=k(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Wt=k(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),et=k(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),zn=V(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Wn=V(/<%[\w\W]*|[\w\W]*%>/gm),Bn=V(/\$\{[\w\W]*/gm),Gn=V(/^data-[\-\w.\u00B7-\uFFFF]+$/),Vn=V(/^aria-[\-\w]+$/),$t=V(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Yn=V(/^(?:\w+script|data):/i),$n=V(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Xt=V(/^html$/i),Xn=V(/^[a-z][.\w]*(-[.\w]+)+$/i);var Bt=Object.freeze({__proto__:null,ARIA_ATTR:Vn,ATTR_WHITESPACE:$n,CUSTOM_ELEMENT:Xn,DATA_ATTR:Gn,DOCTYPE_NAME:Xt,ERB_EXPR:Wn,IS_ALLOWED_URI:$t,IS_SCRIPT_OR_DATA:Yn,MUSTACHE_EXPR:zn,TMPLIT_EXPR:Bn});const Be={element:1,text:3,progressingInstruction:7,comment:8,document:9},jn=function(){return typeof window>"u"?null:window},qn=function(a,m){if(typeof a!="object"||typeof a.createPolicy!="function")return null;let s=null;const h="data-tt-policy-suffix";m&&m.hasAttribute(h)&&(s=m.getAttribute(h));const D="dompurify"+(s?"#"+s:"");try{return a.createPolicy(D,{createHTML(C){return C},createScriptURL(C){return C}})}catch{return console.warn("TrustedTypes policy "+D+" could not be created."),null}},Gt=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function jt(){let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:jn();const a=r=>jt(r);if(a.version="3.2.4",a.removed=[],!i||!i.document||i.document.nodeType!==Be.document||!i.Element)return a.isSupported=!1,a;let{document:m}=i;const s=m,h=s.currentScript,{DocumentFragment:D,HTMLTemplateElement:C,Node:ve,Element:Z,NodeFilter:N,NamedNodeMap:nt=i.NamedNodeMap||i.MozNamedAttrMap,HTMLFormElement:re,DOMParser:ot,trustedTypes:fe}=i,te=Z.prototype,F=We(te,"cloneNode"),q=We(te,"remove"),se=We(te,"nextSibling"),ne=We(te,"childNodes"),oe=We(te,"parentNode");if(typeof C=="function"){const r=m.createElement("template");r.content&&r.content.ownerDocument&&(m=r.content.ownerDocument)}let _,ie="";const{implementation:Ae,createNodeIterator:we,createDocumentFragment:Ge,getElementsByTagName:pe}=m,{importNode:be}=s;let M=Gt();a.isSupported=typeof Vt=="function"&&typeof oe=="function"&&Ae&&Ae.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:Se,ERB_EXPR:Le,TMPLIT_EXPR:Me,DATA_ATTR:at,ARIA_ATTR:lt,IS_SCRIPT_OR_DATA:Ve,ATTR_WHITESPACE:Y,CUSTOM_ELEMENT:rt}=Bt;let{IS_ALLOWED_URI:Re}=Bt,w=null;const _e=u({},[...Ut,...ht,...gt,...Tt,...Ft]);let b=null;const De=u({},[...zt,...Et,...Wt,...et]);let E=Object.seal(Yt(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),ce=null,Ce=null,Ye=!0,Oe=!0,$e=!1,n=!0,t=!1,d=!0,f=!1,g=!1,B=!1,G=!1,z=!1,$=!1,Xe=!0,ue=!1;const st="user-content-";let Ne=!0,me=!1,ae={},le=null;const A=u({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let he=null;const xe=u({},["audio","video","img","source","image","track"]);let it=null;const At=u({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),je="http://www.w3.org/1998/Math/MathML",qe="http://www.w3.org/2000/svg",Q="http://www.w3.org/1999/xhtml";let ge=Q,ct=!1,ut=null;const qt=u({},[je,qe,Q],_t);let Ke=u({},["mi","mo","mn","ms","mtext"]),Je=u({},["annotation-xml"]);const Kt=u({},["title","style","font","a","script"]);let Ie=null;const Jt=["application/xhtml+xml","text/html"],Zt="text/html";let L=null,Te=null;const Qt=m.createElement("form"),wt=function(e){return e instanceof RegExp||e instanceof Function},mt=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(Te&&Te===e)){if((!e||typeof e!="object")&&(e={}),e=de(e),Ie=Jt.indexOf(e.PARSER_MEDIA_TYPE)===-1?Zt:e.PARSER_MEDIA_TYPE,L=Ie==="application/xhtml+xml"?_t:tt,w=j(e,"ALLOWED_TAGS")?u({},e.ALLOWED_TAGS,L):_e,b=j(e,"ALLOWED_ATTR")?u({},e.ALLOWED_ATTR,L):De,ut=j(e,"ALLOWED_NAMESPACES")?u({},e.ALLOWED_NAMESPACES,_t):qt,it=j(e,"ADD_URI_SAFE_ATTR")?u(de(At),e.ADD_URI_SAFE_ATTR,L):At,he=j(e,"ADD_DATA_URI_TAGS")?u(de(xe),e.ADD_DATA_URI_TAGS,L):xe,le=j(e,"FORBID_CONTENTS")?u({},e.FORBID_CONTENTS,L):A,ce=j(e,"FORBID_TAGS")?u({},e.FORBID_TAGS,L):{},Ce=j(e,"FORBID_ATTR")?u({},e.FORBID_ATTR,L):{},ae=j(e,"USE_PROFILES")?e.USE_PROFILES:!1,Ye=e.ALLOW_ARIA_ATTR!==!1,Oe=e.ALLOW_DATA_ATTR!==!1,$e=e.ALLOW_UNKNOWN_PROTOCOLS||!1,n=e.ALLOW_SELF_CLOSE_IN_ATTR!==!1,t=e.SAFE_FOR_TEMPLATES||!1,d=e.SAFE_FOR_XML!==!1,f=e.WHOLE_DOCUMENT||!1,G=e.RETURN_DOM||!1,z=e.RETURN_DOM_FRAGMENT||!1,$=e.RETURN_TRUSTED_TYPE||!1,B=e.FORCE_BODY||!1,Xe=e.SANITIZE_DOM!==!1,ue=e.SANITIZE_NAMED_PROPS||!1,Ne=e.KEEP_CONTENT!==!1,me=e.IN_PLACE||!1,Re=e.ALLOWED_URI_REGEXP||$t,ge=e.NAMESPACE||Q,Ke=e.MATHML_TEXT_INTEGRATION_POINTS||Ke,Je=e.HTML_INTEGRATION_POINTS||Je,E=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&wt(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(E.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&wt(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(E.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(E.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),t&&(Oe=!1),z&&(G=!0),ae&&(w=u({},Ft),b=[],ae.html===!0&&(u(w,Ut),u(b,zt)),ae.svg===!0&&(u(w,ht),u(b,Et),u(b,et)),ae.svgFilters===!0&&(u(w,gt),u(b,Et),u(b,et)),ae.mathMl===!0&&(u(w,Tt),u(b,Wt),u(b,et))),e.ADD_TAGS&&(w===_e&&(w=de(w)),u(w,e.ADD_TAGS,L)),e.ADD_ATTR&&(b===De&&(b=de(b)),u(b,e.ADD_ATTR,L)),e.ADD_URI_SAFE_ATTR&&u(it,e.ADD_URI_SAFE_ATTR,L),e.FORBID_CONTENTS&&(le===A&&(le=de(le)),u(le,e.FORBID_CONTENTS,L)),Ne&&(w["#text"]=!0),f&&u(w,["html","head","body"]),w.table&&(u(w,["tbody"]),delete ce.tbody),e.TRUSTED_TYPES_POLICY){if(typeof e.TRUSTED_TYPES_POLICY.createHTML!="function")throw ze('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof e.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw ze('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');_=e.TRUSTED_TYPES_POLICY,ie=_.createHTML("")}else _===void 0&&(_=qn(fe,h)),_!==null&&typeof ie=="string"&&(ie=_.createHTML(""));k&&k(e),Te=e}},bt=u({},[...ht,...gt,...Un]),St=u({},[...Tt,...Fn]),en=function(e){let o=oe(e);(!o||!o.tagName)&&(o={namespaceURI:ge,tagName:"template"});const l=tt(e.tagName),y=tt(o.tagName);return ut[e.namespaceURI]?e.namespaceURI===qe?o.namespaceURI===Q?l==="svg":o.namespaceURI===je?l==="svg"&&(y==="annotation-xml"||Ke[y]):!!bt[l]:e.namespaceURI===je?o.namespaceURI===Q?l==="math":o.namespaceURI===qe?l==="math"&&Je[y]:!!St[l]:e.namespaceURI===Q?o.namespaceURI===qe&&!Je[y]||o.namespaceURI===je&&!Ke[y]?!1:!St[l]&&(Kt[l]||!bt[l]):!!(Ie==="application/xhtml+xml"&&ut[e.namespaceURI]):!1},K=function(e){Ue(a.removed,{element:e});try{oe(e).removeChild(e)}catch{q(e)}},Ze=function(e,o){try{Ue(a.removed,{attribute:o.getAttributeNode(e),from:o})}catch{Ue(a.removed,{attribute:null,from:o})}if(o.removeAttribute(e),e==="is")if(G||z)try{K(o)}catch{}else try{o.setAttribute(e,"")}catch{}},Lt=function(e){let o=null,l=null;if(B)e="<remove></remove>"+e;else{const R=Ht(e,/^[\r\n\t ]+/);l=R&&R[0]}Ie==="application/xhtml+xml"&&ge===Q&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const y=_?_.createHTML(e):e;if(ge===Q)try{o=new ot().parseFromString(y,Ie)}catch{}if(!o||!o.documentElement){o=Ae.createDocument(ge,"template",null);try{o.documentElement.innerHTML=ct?ie:y}catch{}}const O=o.body||o.documentElement;return e&&l&&O.insertBefore(m.createTextNode(l),O.childNodes[0]||null),ge===Q?pe.call(o,f?"html":"body")[0]:f?o.documentElement:O},Mt=function(e){return we.call(e.ownerDocument||e,e,N.SHOW_ELEMENT|N.SHOW_COMMENT|N.SHOW_TEXT|N.SHOW_PROCESSING_INSTRUCTION|N.SHOW_CDATA_SECTION,null)},dt=function(e){return e instanceof re&&(typeof e.nodeName!="string"||typeof e.textContent!="string"||typeof e.removeChild!="function"||!(e.attributes instanceof nt)||typeof e.removeAttribute!="function"||typeof e.setAttribute!="function"||typeof e.namespaceURI!="string"||typeof e.insertBefore!="function"||typeof e.hasChildNodes!="function")},Rt=function(e){return typeof ve=="function"&&e instanceof ve};function ee(r,e,o){Qe(r,l=>{l.call(a,e,o,Te)})}const Dt=function(e){let o=null;if(ee(M.beforeSanitizeElements,e,null),dt(e))return K(e),!0;const l=L(e.nodeName);if(ee(M.uponSanitizeElement,e,{tagName:l,allowedTags:w}),e.hasChildNodes()&&!Rt(e.firstElementChild)&&I(/<[/\w]/g,e.innerHTML)&&I(/<[/\w]/g,e.textContent)||e.nodeType===Be.progressingInstruction||d&&e.nodeType===Be.comment&&I(/<[/\w]/g,e.data))return K(e),!0;if(!w[l]||ce[l]){if(!ce[l]&&Ot(l)&&(E.tagNameCheck instanceof RegExp&&I(E.tagNameCheck,l)||E.tagNameCheck instanceof Function&&E.tagNameCheck(l)))return!1;if(Ne&&!le[l]){const y=oe(e)||e.parentNode,O=ne(e)||e.childNodes;if(O&&y){const R=O.length;for(let H=R-1;H>=0;--H){const J=F(O[H],!0);J.__removalCount=(e.__removalCount||0)+1,y.insertBefore(J,se(e))}}}return K(e),!0}return e instanceof Z&&!en(e)||(l==="noscript"||l==="noembed"||l==="noframes")&&I(/<\/no(script|embed|frames)/i,e.innerHTML)?(K(e),!0):(t&&e.nodeType===Be.text&&(o=e.textContent,Qe([Se,Le,Me],y=>{o=Fe(o,y," ")}),e.textContent!==o&&(Ue(a.removed,{element:e.cloneNode()}),e.textContent=o)),ee(M.afterSanitizeElements,e,null),!1)},Ct=function(e,o,l){if(Xe&&(o==="id"||o==="name")&&(l in m||l in Qt))return!1;if(!(Oe&&!Ce[o]&&I(at,o))){if(!(Ye&&I(lt,o))){if(!b[o]||Ce[o]){if(!(Ot(e)&&(E.tagNameCheck instanceof RegExp&&I(E.tagNameCheck,e)||E.tagNameCheck instanceof Function&&E.tagNameCheck(e))&&(E.attributeNameCheck instanceof RegExp&&I(E.attributeNameCheck,o)||E.attributeNameCheck instanceof Function&&E.attributeNameCheck(o))||o==="is"&&E.allowCustomizedBuiltInElements&&(E.tagNameCheck instanceof RegExp&&I(E.tagNameCheck,l)||E.tagNameCheck instanceof Function&&E.tagNameCheck(l))))return!1}else if(!it[o]){if(!I(Re,Fe(l,Y,""))){if(!((o==="src"||o==="xlink:href"||o==="href")&&e!=="script"&&In(l,"data:")===0&&he[e])){if(!($e&&!I(Ve,Fe(l,Y,"")))){if(l)return!1}}}}}}return!0},Ot=function(e){return e!=="annotation-xml"&&Ht(e,rt)},Nt=function(e){ee(M.beforeSanitizeAttributes,e,null);const{attributes:o}=e;if(!o||dt(e))return;const l={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:b,forceKeepAttr:void 0};let y=o.length;for(;y--;){const O=o[y],{name:R,namespaceURI:H,value:J}=O,ke=L(R);let x=R==="value"?J:kn(J);if(l.attrName=ke,l.attrValue=x,l.keepAttr=!0,l.forceKeepAttr=void 0,ee(M.uponSanitizeAttribute,e,l),x=l.attrValue,ue&&(ke==="id"||ke==="name")&&(Ze(R,e),x=st+x),d&&I(/((--!?|])>)|<\/(style|title)/i,x)){Ze(R,e);continue}if(l.forceKeepAttr||(Ze(R,e),!l.keepAttr))continue;if(!n&&I(/\/>/i,x)){Ze(R,e);continue}t&&Qe([Se,Le,Me],It=>{x=Fe(x,It," ")});const xt=L(e.nodeName);if(Ct(xt,ke,x)){if(_&&typeof fe=="object"&&typeof fe.getAttributeType=="function"&&!H)switch(fe.getAttributeType(xt,ke)){case"TrustedHTML":{x=_.createHTML(x);break}case"TrustedScriptURL":{x=_.createScriptURL(x);break}}try{H?e.setAttributeNS(H,R,x):e.setAttribute(R,x),dt(e)?K(e):Pt(a.removed)}catch{}}}ee(M.afterSanitizeAttributes,e,null)},tn=function r(e){let o=null;const l=Mt(e);for(ee(M.beforeSanitizeShadowDOM,e,null);o=l.nextNode();)ee(M.uponSanitizeShadowNode,o,null),Dt(o),Nt(o),o.content instanceof D&&r(o.content);ee(M.afterSanitizeShadowDOM,e,null)};return a.sanitize=function(r){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=null,l=null,y=null,O=null;if(ct=!r,ct&&(r="<!-->"),typeof r!="string"&&!Rt(r))if(typeof r.toString=="function"){if(r=r.toString(),typeof r!="string")throw ze("dirty is not a string, aborting")}else throw ze("toString is not a function");if(!a.isSupported)return r;if(g||mt(e),a.removed=[],typeof r=="string"&&(me=!1),me){if(r.nodeName){const J=L(r.nodeName);if(!w[J]||ce[J])throw ze("root node is forbidden and cannot be sanitized in-place")}}else if(r instanceof ve)o=Lt("<!---->"),l=o.ownerDocument.importNode(r,!0),l.nodeType===Be.element&&l.nodeName==="BODY"||l.nodeName==="HTML"?o=l:o.appendChild(l);else{if(!G&&!t&&!f&&r.indexOf("<")===-1)return _&&$?_.createHTML(r):r;if(o=Lt(r),!o)return G?null:$?ie:""}o&&B&&K(o.firstChild);const R=Mt(me?r:o);for(;y=R.nextNode();)Dt(y),Nt(y),y.content instanceof D&&tn(y.content);if(me)return r;if(G){if(z)for(O=Ge.call(o.ownerDocument);o.firstChild;)O.appendChild(o.firstChild);else O=o;return(b.shadowroot||b.shadowrootmode)&&(O=be.call(s,O,!0)),O}let H=f?o.outerHTML:o.innerHTML;return f&&w["!doctype"]&&o.ownerDocument&&o.ownerDocument.doctype&&o.ownerDocument.doctype.name&&I(Xt,o.ownerDocument.doctype.name)&&(H="<!DOCTYPE "+o.ownerDocument.doctype.name+`>
`+H),t&&Qe([Se,Le,Me],J=>{H=Fe(H,J," ")}),_&&$?_.createHTML(H):H},a.setConfig=function(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};mt(r),g=!0},a.clearConfig=function(){Te=null,g=!1},a.isValidAttribute=function(r,e,o){Te||mt({});const l=L(r),y=L(e);return Ct(l,y,o)},a.addHook=function(r,e){typeof e=="function"&&Ue(M[r],e)},a.removeHook=function(r,e){if(e!==void 0){const o=Nn(M[r],e);return o===-1?void 0:xn(M[r],o,1)[0]}return Pt(M[r])},a.removeHooks=function(r){M[r]=[]},a.removeAllHooks=function(){M=Gt()},a}var Kn=jt();const Jn={class:"mhtml-reader"},Zn={class:"reader-container"},Qn={class:"sidebar"},eo={class:"directory-input"},to={class:"custom-tree-node"},no={key:0,class:"note-indicator"},oo={class:"content"},ao={key:0,class:"placeholder"},lo={key:1,class:"reader-view"},ro={class:"reader-header"},so={class:"actions"},io={key:0,class:"loading-overlay"},co=["srcdoc"],uo={key:2,class:"editor-view"},mo=["srcdoc"],fo={class:"notes-panel"},po={key:0,class:"no-notes"},_o={key:1,class:"notes-list"},ho={class:"note-header"},go={class:"note-title"},To={class:"reading-progress"},Eo={class:"extract-dialog-content"},yo={class:"extract-actions"},vo={__name:"阅读器",setup(i){const a=S(!1),m=S([]),s=S(null),h=S(null),D=S([]),C=S(!1),ve=S(null),Z=S(null),N=S(null),nt=S(null),re=S(""),ot=S(null),fe=S(null),te=S(null),F=S("normal"),q=S("utf-8"),se=S(""),ne=S(""),oe=S(0),_=on({visible:!1,outputDir:"",mainHtmlFile:"",mainHtmlPath:"",fileCount:0});ft(()=>{if(!h.value)return"";if(re.value)return re.value;try{const n=atob(h.value),t=[],d=512;for(let B=0;B<n.length;B+=d){const G=n.slice(B,B+d),z=new Array(G.length);for(let $=0;$<G.length;$++)z[$]=G.charCodeAt($);t.push(new Uint8Array(z))}const f=new Blob(t,{type:"message/rfc822"}),g=URL.createObjectURL(f);return re.value=g,g}catch(n){return console.error("创建MHTML URL失败:",n),`data:message/rfc822;base64,${h.value}`}});const ie=ft(()=>se.value?Kn.sanitize(se.value):""),Ae={children:"children",label:"name"};an(()=>{window.addEventListener("message",De),Ge()}),ln(()=>{window.removeEventListener("message",De),re.value&&URL.revokeObjectURL(re.value)});const we=async()=>{if(!ne.value){p.warning("请输入有效的目录路径");return}a.value=!0;try{const n=await window.pywebview.api.local_controller.load_dir_mhtml(ne.value),t=typeof n=="string"?JSON.parse(n):n;t.status==="success"?(m.value=t.data||[],t.data&&t.data.length>0?p.success("加载目录成功"):p.info("目录为空或不存在MHTML文件")):p.error("加载目录失败："+(t?.message||"未知错误"))}catch(n){console.error("加载目录出错:",n),p.error("加载目录出错: "+n.toString())}finally{a.value=!1}},Ge=async()=>{ne.value?await we():(ne.value="",await we())},pe=ft(()=>{if(!h.value)return"<html><body><div>无内容可显示</div></body></html>";if(F.value==="normal")return ie.value||"<html><body><div>正在处理内容...</div></body></html>";if(F.value==="raw")return`<html><body><pre style="white-space: pre-wrap; word-break: break-all;">${h.value}</pre></body></html>`;if(F.value==="decoded")try{const n=atob(h.value);return`<html><body><pre style="white-space: pre-wrap; word-break: break-all;">${be(n)}</pre></body></html>`}catch(n){return`<html><body><div>解码失败: ${n.message}</div></body></html>`}else if(F.value==="plainhtml")try{const t=atob(h.value).match(/<html[\s\S]*<\/html>/i);return t?t[0]:"<html><body><div>未找到HTML内容</div></body></html>"}catch(n){return`<html><body><div>解析失败: ${n.message}</div></body></html>`}else if(F.value==="simple")try{const n=atob(h.value);let t="";return t=n.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g,""),`
        <html>
        <head>
          <meta charset="utf-8">
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.5;
              padding: 20px;
              background: #fff;
              color: #333;
            }
            pre {
              white-space: pre-wrap;
              word-break: break-all;
              background: #f8f8f8;
              padding: 15px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 14px;
              max-height: none;
              overflow: visible;
            }
          </style>
        </head>
        <body>
          <h2>MHTML文件内容（简单文本模式）</h2>
          <pre>${be(t)}</pre>
        </body>
        </html>
      `}catch(n){return`<html><body><div>解析文件失败: ${n.message}</div></body></html>`}return"<html><body><div>未知视图模式</div></body></html>"}),be=n=>n.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;"),M=async n=>{if(n.type==="file"&&n.fileType==="mhtml")try{a.value=!0;const t=await window.pywebview.api.local_controller.get_mhtml_content(n.path),d=typeof t=="string"?JSON.parse(t):t;if(d.status==="success"&&d.data){s.value=n,h.value=d.data.content,D.value=d.data.notes||[];const f=await window.pywebview.api.local_controller.improve_mhtml_rendering(n.path,q.value),g=typeof f=="string"?JSON.parse(f):f;g.status==="success"&&g.data?(se.value=g.data.html,g.data.charset&&(q.value=g.data.charset),F.value="normal",p.success(`文件加载成功，包含${g.data.resourceCount}个资源`),ye(()=>{Y()})):(console.warn("增强渲染失败，回退到简单模式"),F.value="simple",ye(()=>{Y()}),p.warning("渲染失败，显示简单内容")),C.value=!1}else p.error("加载文件失败："+(d?.message||"未知错误"))}catch(t){console.error("加载文件出错:",t),p.error("加载文件出错: "+t.toString()),F.value="simple",ye(()=>{Y()})}finally{a.value=!1}},Se=n=>{C.value=n,ye(()=>{b(),n&&Le()})},Le=()=>{N.value&&(N.value.onload=()=>{try{const n=N.value.contentDocument||N.value.contentWindow.document;console.log("iframe loaded, ready for annotations")}catch(n){console.error("无法访问iframe内容:",n)}})},Me=()=>{D.value.push({id:Date.now(),content:"",position:{x:0,y:0},timestamp:new Date().toISOString()})},at=n=>{D.value.splice(n,1)},lt=async()=>{if(s.value)try{a.value=!0;const n=await window.pywebview.api.local_controller.save_mhtml_with_notes(s.value.path,null,D.value),t=typeof n=="string"?JSON.parse(n):n;t.status==="success"?(p.success("笔记保存成功"),await Ge()):p.error("保存笔记失败："+(t?.message||"未知错误"))}catch(n){console.error("保存笔记出错:",n),p.error("保存笔记出错: "+n.toString())}finally{a.value=!1}},Ve=n=>{console.log("iframe已加载");try{const t=n.target,d=t.contentDocument||t.contentWindow.document;if(_e(t),d&&d.body){const f=d.body.innerHTML;console.log(`iframe内容加载完成，内容大小: ${f.length}`),f.length<10?(console.warn("iframe内容可能为空"),p.warning("文档内容可能未正确加载")):p.success("文档加载成功")}else console.warn("iframe文档为空"),p.warning("文档可能未正确加载"),Y();E()}catch(t){console.error("访问iframe内容失败:",t),Y()}},Y=()=>{if(C.value){if(N.value){const n=N.value.contentDocument||N.value.contentWindow.document;n.open(),n.write(pe.value),n.close(),ye(()=>{_e(N.value)})}}else{const n=Z.value;if(n){const t=n.contentDocument||n.contentWindow.document;t.open(),t.write(pe.value),t.close(),ye(()=>{_e(n)})}}},rt=async()=>{if(s.value)try{a.value=!0,console.log("字符集变更为:",q.value);const n=await window.pywebview.api.local_controller.process_mhtml_to_html(s.value.path,q.value),t=typeof n=="string"?JSON.parse(n):n;t.status==="success"&&t.data?(se.value=t.data.html,Y(),p.success(`已切换到${q.value}字符集`)):p.error("更新内容失败："+(t?.message||"未知错误"))}catch(n){console.error("更新字符集出错:",n),p.error("更新字符集出错: "+n.toString())}finally{a.value=!1}},Re=()=>({normal:"正常模式",raw:"原始内容",decoded:"解码内容",plainhtml:"直接HTML",simple:"简单文本模式"})[F.value]||"正常模式",w=n=>{F.value=n,console.log(`切换到${n}模式`),p.success(`已切换到${Re()}`),Y()};function _e(n){try{const t=n.contentDocument||n.contentWindow.document;t.querySelectorAll("a").forEach(g=>{g.addEventListener("click",B=>{B.preventDefault(),console.log("链接点击被阻止:",g.href)})}),t.querySelectorAll("img").forEach(g=>{g.onerror=function(){this.style.display="none",console.log("图片加载失败:",this.src)}})}catch(t){console.error("修复iframe链接时出错:",t)}}const b=()=>{try{const n=C.value?fe.value:ot.value;if(!n||!te.value){console.warn("找不到HTML容器或内容为空");return}n.innerHTML=`
      <div class="html-container" style="width:100%; height:100%; overflow:auto;">
        ${te.value}
      </div>
    `,console.log("已渲染HTML内容"),p.success("文档已加载")}catch(n){console.error("渲染HTML内容失败:",n),p.error("显示文档内容失败")}},De=n=>{n.data==="mhtml-loaded"&&(console.log("MHTML内容加载完成"),p.success("文档加载成功"),setTimeout(()=>{E()},500))},E=()=>{if(Z.value)try{const n=Z.value,t=n.contentDocument||n.contentWindow.document;if(!t||!t.body){console.warn("iframe文档尚未准备好，稍后重试"),setTimeout(E,500);return}n.contentWindow.addEventListener("scroll",()=>{const d=t.documentElement.scrollHeight-t.documentElement.clientHeight,f=t.documentElement.scrollTop||t.body.scrollTop;oe.value=Math.round(f/d*100)}),console.log("滚动跟踪设置成功")}catch(n){console.error("设置滚动跟踪失败:",n)}},ce=n=>{if(Z.value)try{const t=Z.value,d=t.contentDocument||t.contentWindow.document,f=d.documentElement.scrollHeight-d.documentElement.clientHeight,g=n/100*f;d.documentElement.scrollTop=g,d.body.scrollTop=g}catch(t){console.error("滚动到位置失败:",t)}},Ce=async()=>{try{if(!s.value)return;a.value=!0;const n=await window.pywebview.api.local_controller.get_mhtml_content(s.value.path),t=typeof n=="string"?JSON.parse(n):n;if(t.status==="success"&&t.data)try{const f=atob(t.data.content).replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g,""),g=`
          <html>
          <head>
            <meta charset="${q.value}">
            <style>
              body { font-family: monospace; white-space: pre-wrap; padding: 20px; line-height: 1.5; }
            </style>
          </head>
          <body>${be(f)}</body>
          </html>
        `;se.value=g,F.value="normal",Y(),p.success("已使用应急模式显示文件内容")}catch(d){p.error("应急显示失败: "+d.toString())}else p.error("获取文件内容失败")}catch(n){console.error("应急显示出错:",n),p.error("应急显示出错: "+n.toString())}finally{a.value=!1}},Ye=async()=>{try{if(!s.value)return;a.value=!0,p.info("正在提取MHTML文件，请稍候...");const n=await window.pywebview.api.local_controller.extract_mhtml(s.value.path),t=typeof n=="string"?JSON.parse(n):n;t.status==="success"&&t.data?(_.outputDir=t.data.outputDir,_.mainHtmlFile=t.data.mainHtmlFile,_.mainHtmlPath=t.data.mainHtmlPath,_.fileCount=t.data.fileCount,_.visible=!0,p.success(`MHTML提取成功，共提取了${t.data.fileCount}个文件`)):p.error("提取MHTML失败："+(t?.message||"未知错误"))}catch(n){console.error("提取MHTML出错:",n),p.error("提取MHTML出错: "+n.toString())}finally{a.value=!1}},Oe=async()=>{try{if(!_.outputDir)return;await window.pywebview.api.local_controller.open_directory(_.outputDir)}catch(n){console.error("打开目录失败:",n),p.error("打开目录失败: "+n.toString())}},$e=async()=>{try{if(!_.mainHtmlPath)return;await window.pywebview.api.local_controller.open_file(_.mainHtmlPath)}catch(n){console.error("打开文件失败:",n),p.error("打开文件失败: "+n.toString())}};return(n,t)=>{const d=sn,f=rn,g=un,B=pn,G=_n,z=gn,$=hn,Xe=Tn,ue=En,st=yn,Ne=vn,me=An,ae=Mn,le=Rn;return U(),X("div",Jn,[v("div",Zn,[v("div",Qn,[v("div",eo,[c(g,{modelValue:ne.value,"onUpdate:modelValue":t[0]||(t[0]=A=>ne.value=A),placeholder:"输入MHTML文件目录路径",size:"small"},{append:T(()=>[c(f,{onClick:we},{default:T(()=>[c(d,null,{default:T(()=>[c(Ee(cn))]),_:1})]),_:1})]),_:1},8,["modelValue"])]),t[6]||(t[6]=v("h3",null,"MHTML文件目录",-1)),c(B,{data:m.value,props:Ae,onNodeClick:M,"default-expand-all":!0,"node-key":"path"},{default:T(({data:A})=>[v("div",to,[v("span",null,[A.type==="directory"?(U(),pt(d,{key:0},{default:T(()=>[c(Ee(mn))]),_:1})):(U(),pt(d,{key:1},{default:T(()=>[c(Ee(dn))]),_:1})),W(" "+He(A.name),1)]),A.hasNotes?(U(),X("span",no,[c(d,null,{default:T(()=>[c(Ee(fn))]),_:1})])):Pe("",!0)])]),_:1},8,["data"])]),v("div",oo,[s.value?(U(),X("div",lo,[v("div",ro,[v("h3",null,He(s.value?.name),1),v("div",so,[s.value?(U(),pt(Xe,{key:0,onCommand:w},{dropdown:T(()=>[c($,null,{default:T(()=>[c(z,{command:"normal"},{default:T(()=>t[7]||(t[7]=[W("正常模式")])),_:1}),c(z,{command:"raw"},{default:T(()=>t[8]||(t[8]=[W("原始内容")])),_:1}),c(z,{command:"decoded"},{default:T(()=>t[9]||(t[9]=[W("解码内容")])),_:1}),c(z,{command:"plainhtml"},{default:T(()=>t[10]||(t[10]=[W("直接HTML")])),_:1}),c(z,{command:"simple"},{default:T(()=>t[11]||(t[11]=[W("简单文本模式")])),_:1})]),_:1})]),default:T(()=>[c(f,{type:"primary"},{default:T(()=>[W(He(Re()),1)]),_:1})]),_:1})):Pe("",!0),c(st,{modelValue:q.value,"onUpdate:modelValue":t[1]||(t[1]=A=>q.value=A),onChange:rt,size:"small",style:{width:"100px"}},{default:T(()=>[c(ue,{label:"UTF-8",value:"utf-8"}),c(ue,{label:"GBK",value:"gbk"}),c(ue,{label:"GB2312",value:"gb2312"}),c(ue,{label:"Big5",value:"big5"})]),_:1},8,["modelValue"]),c(Ne,{modelValue:C.value,"onUpdate:modelValue":t[2]||(t[2]=A=>C.value=A),"active-text":"批注模式","inactive-text":"阅读模式",onChange:Se},null,8,["modelValue"]),c(f,{type:"primary",onClick:lt,disabled:!C.value},{default:T(()=>t[12]||(t[12]=[W(" 保存批注 ")])),_:1},8,["disabled"]),c(f,{type:"warning",onClick:Ce,size:"small"},{default:T(()=>t[13]||(t[13]=[W(" 应急显示 ")])),_:1}),c(f,{type:"success",onClick:Ye,disabled:!s.value,size:"small"},{default:T(()=>t[14]||(t[14]=[W(" 提取MHTML ")])),_:1},8,["disabled"])])]),v("div",{class:"reader-content",ref_key:"readerContent",ref:ve},[a.value?(U(),X("div",io,[c(me),t[15]||(t[15]=v("p",null,"正在处理MHTML文件...",-1))])):Pe("",!0),h.value&&!C.value?(U(),X("iframe",{key:1,ref_key:"mhtmlFrame",ref:Z,class:"mhtml-frame",srcdoc:pe.value,sandbox:"allow-same-origin allow-scripts allow-forms",onLoad:Ve},null,40,co)):Pe("",!0),C.value?(U(),X("div",uo,[v("div",{class:"mhtml-container",ref_key:"mhtmlContainer",ref:nt},[v("iframe",{ref_key:"editFrame",ref:N,class:"mhtml-frame",srcdoc:pe.value,sandbox:"allow-same-origin allow-scripts allow-forms",onLoad:Ve},null,40,mo)],512),v("div",fo,[t[18]||(t[18]=v("h4",null,"文档批注",-1)),D.value.length===0?(U(),X("div",po,t[16]||(t[16]=[v("p",null,'尚无批注，点击"添加批注"按钮进行添加',-1)]))):(U(),X("div",_o,[(U(!0),X(wn,null,bn(D.value,(A,he)=>(U(),X("div",{key:he,class:"note-item"},[v("div",ho,[v("span",go,"批注 #"+He(he+1),1),c(f,{type:"danger",size:"small",circle:"",onClick:xe=>at(he),icon:Ee(Sn)},null,8,["onClick","icon"])]),c(g,{modelValue:A.content,"onUpdate:modelValue":xe=>A.content=xe,type:"textarea",rows:3,placeholder:"请输入批注内容"},null,8,["modelValue","onUpdate:modelValue"])]))),128))])),c(f,{type:"primary",onClick:Me,class:"add-note-btn"},{default:T(()=>[c(d,null,{default:T(()=>[c(Ee(Ln))]),_:1}),t[17]||(t[17]=W(" 添加批注 "))]),_:1})])])):Pe("",!0)],512)])):(U(),X("div",ao,[c(G,{description:"请选择MHTML文件进行阅读"})]))])]),v("div",To,[c(ae,{modelValue:oe.value,"onUpdate:modelValue":t[3]||(t[3]=A=>oe.value=A),max:100,min:0,onChange:ce,size:"small"},null,8,["modelValue"])]),c(le,{modelValue:_.visible,"onUpdate:modelValue":t[5]||(t[5]=A=>_.visible=A),title:"MHTML提取成功",width:"500px"},{default:T(()=>[v("div",Eo,[t[21]||(t[21]=v("p",null,"已成功将MHTML文件提取到以下目录：",-1)),c(g,{modelValue:_.outputDir,"onUpdate:modelValue":t[4]||(t[4]=A=>_.outputDir=A),readonly:""},{append:T(()=>[c(f,{onClick:Oe},{default:T(()=>t[19]||(t[19]=[W(" 打开目录 ")])),_:1})]),_:1},8,["modelValue"]),v("p",null,"提取文件数量："+He(_.fileCount),1),v("div",yo,[c(f,{type:"primary",onClick:$e},{default:T(()=>t[20]||(t[20]=[W(" 打开主HTML文件 ")])),_:1})])])]),_:1},8,["modelValue"])])}}},No=nn(vo,[["__scopeId","data-v-821f9736"]]);export{No as default};
