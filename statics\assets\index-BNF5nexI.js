import{_ as oe,u as ae,a as te,r as u,c as A,o as se,E as n,w as le,b as y,d as a,e as l,f as ne,g as t,h as ie,n as D,i as re,j as ue,k as ce,l as de,m as h,A as ve,p as F,q as me,s as fe,t as ge,v as r,x as pe,y as _e,z as we,B as ye,C as P,D as he,F as V,G as ke,H as be,I as Ce,J as Ve,K as xe,L as Ie}from"./entry-BIjVVog3.js";/* empty css                         *//* empty css                 *//* empty css               *//* empty css                *//* empty css                    *//* empty css                 */const Le={class:"login-content"},Ee={class:"login-box"},Se={class:"machine-code-input-group"},Te={class:"login-options"},Me={key:0,class:"login-error"},Ue={class:"dialog-footer"},Ne={key:0},Ae={key:0,class:"time-notice"},De={key:1,class:"loading-time"},Fe={class:"dialog-footer"},Pe={__name:"index",setup(Be){const B=re();de();const s=ae(),R=te(),K=u(null),d=u(!1),E=A(()=>s.error),f=u(!1),k=u(!1);u(!1);const b=u(!1),m=u(null),x=u(!1),v=u(!1),_=u("正在检查网络时间..."),w=u(!1),q=A(()=>R.theme==="dark"),i=u({machine_code:"",activation_code:""}),$={machine_code:[{required:!0,message:"请输入机器码",trigger:"blur"}],activation_code:[{required:!0,message:"请输入激活码",trigger:"blur"}]},C=async()=>{if(w.value||d.value){console.log("手动登录已在进行中，跳过重复登录");return}if(s.isAutoLoginInProgress){console.log("自动登录正在进行中，等待完成..."),n.info("正在自动登录，请稍候...");let o=0;const e=1e4,g=100;for(;s.isAutoLoginInProgress&&o<e;)await new Promise(p=>setTimeout(p,g)),o+=g;if(s.isLoggedIn){console.log("自动登录已完成，用户已登录");return}o>=e&&console.log("自动登录超时，继续手动登录")}w.value=!0,d.value=!0;try{if(!navigator.onLine){n.error("网络连接不可用，请检查您的网络设置后重试"),d.value=!1,w.value=!1;return}if(await M(),v.value&&n.warning("网络时间同步异常，可能导致激活码验证失败，请确保网络正常"),!await s.getMachineCode(!0)){n.error("无法获取机器码，请检查网络连接"),d.value=!1,w.value=!1;return}if(!i.value.activation_code){n.warning("请输入激活码"),d.value=!1,w.value=!1;return}await s.login({activation_code:i.value.activation_code},f.value)?(n.success("登录成功"),s.saveUserSettings({activationCode:f.value?i.value.activation_code:"",autoLogin:f.value}),s.startActivationTimer(),B.push("/")):(console.error("登录失败:",s.error),s.error&&n.error(s.error))}catch(o){console.error("登录错误:",o),o.message&&o.message.includes("NetworkError")?n.error("网络连接错误，请检查您的网络连接"):o.message&&o.message.includes("Timeout")?n.error("请求超时，服务器可能暂时无法访问"):n.error(o.message||"登录过程中发生错误")}finally{d.value=!1,w.value=!1}},z=async o=>{o&&i.value.activation_code?await s.saveUserSettings({autoLogin:o,activationCode:i.value.activation_code}):o||await s.saveUserSettings({autoLogin:!1,activationCode:""})},S=async()=>{try{const o=await s.getMachineCode();o&&(i.value.machine_code=o)}catch{n.error("获取机器码失败")}},H=()=>{k.value=!0},J=async()=>{try{const o=await s.loadUserSettings();o.activationCode&&(i.value.activation_code=o.activationCode,f.value=o.autoLogin||!1)}catch(o){console.error("加载保存的激活码失败:",o)}},j=async()=>{b.value=!0,await T()},T=async()=>{x.value=!0,m.value=null;try{const o=await s.getNetworkTimeInfo(),e=typeof o=="string"?JSON.parse(o):o;if(e.status==="success")m.value=e.data,v.value=!1,_.value="网络连接正常";else{const g=e.message||"网络连接异常";n.warning(g),v.value=!0,_.value="网络连接异常",console.warn("网络连接检查失败:",g)}}catch(o){console.error("检测网络状态失败:",o),n.error("无法检测网络状态"),v.value=!0,_.value="网络连接异常"}finally{x.value=!1}};se(async()=>{try{await S(),navigator.onLine||(console.warn("网络连接不可用，自动登录可能失败"),v.value=!0,_.value="网络连接不可用"),await J();try{await M(),v.value?console.warn("网络时间同步异常，可能影响激活码验证"):console.log("网络时间同步正常")}catch(o){console.warn("网络时间检查失败:",o),v.value=!0,_.value="网络时间检查失败，请确保网络连接正常"}!s.isLoggedIn&&f.value&&i.value.activation_code&&(console.log("检测到已保存的激活码，等待App.vue自动登录完成..."),setTimeout(async()=>{!s.isLoggedIn&&!w.value&&!s.isAutoLoginInProgress?(console.log("App.vue自动登录未成功，启动登录页面的自动登录"),await C()):console.log("跳过登录页面自动登录：用户已登录或有其他登录正在进行中")},2e3))}catch(o){console.error("初始化失败:",o),n.warning("应用初始化失败，请检查网络连接")}}),le(()=>i.value.activation_code,async o=>{f.value&&o&&await s.saveUserSettings({activationCode:o})});const G=()=>{const o=`
机器码：${i.value.machine_code}

联系邮箱：<EMAIL>

  `.trim();window.pywebview.api.copy_to_clipboard(o).then(()=>{n.success({message:"联系信息已成功复制到剪贴板",duration:2e3})}).catch(e=>{console.error("复制失败:",e),n.error("复制失败，请手动复制")})},M=async()=>{try{const o=await s.checkNetworkTimeStatus();v.value=o.hasError,_.value=o.message}catch{v.value=!0,_.value="网络时间检查失败，请确保网络连接正常"}};return(o,e)=>{const g=fe,p=ge,I=me,O=_e,W=we,U=ye,Q=ue,N=ce,X=Ve,L=Ce,Y=be,Z=xe,ee=Ie;return h(),y("div",{class:D(["login-container",{dark:q.value}])},[a(ve),l("div",Le,[l("div",Ee,[e[12]||(e[12]=l("div",{class:"login-header"},[l("img",{src:ne,alt:"Logo",class:"logo"}),l("h2",null,"Nukita PVV")],-1)),a(Q,{ref_key:"loginFormRef",ref:K,model:i.value,rules:$,onSubmit:ie(C,["prevent"])},{default:t(()=>[a(I,{prop:"machine_code"},{default:t(()=>[l("div",Se,[a(g,{modelValue:i.value.machine_code,"onUpdate:modelValue":e[0]||(e[0]=c=>i.value.machine_code=c),placeholder:"机器码","prefix-icon":"UserFilled",disabled:d.value,readonly:""},null,8,["modelValue","disabled"]),a(p,{class:"get-machine-code-btn",type:"primary",plain:"",onClick:S,disabled:d.value},{default:t(()=>e[7]||(e[7]=[r(" 获取机器码 ")])),_:1},8,["disabled"])])]),_:1}),a(I,{prop:"activation_code"},{default:t(()=>[a(g,{modelValue:i.value.activation_code,"onUpdate:modelValue":e[1]||(e[1]=c=>i.value.activation_code=c),placeholder:"输入激活码","prefix-icon":"Key",disabled:d.value,onKeyup:pe(C,["enter"])},null,8,["modelValue","disabled"])]),_:1}),l("div",Te,[a(O,{modelValue:f.value,"onUpdate:modelValue":e[2]||(e[2]=c=>f.value=c),onChange:z},{default:t(()=>e[8]||(e[8]=[r(" 记住激活码并自动登录 ")])),_:1},8,["modelValue"]),a(W,{type:"primary",underline:!1,onClick:H},{default:t(()=>e[9]||(e[9]=[r("获取激活码")])),_:1})]),a(I,null,{default:t(()=>[a(p,{type:"primary",loading:d.value,onClick:C,style:{width:"100%"}},{default:t(()=>e[10]||(e[10]=[r(" 登录 ")])),_:1},8,["loading"])]),_:1}),E.value?(h(),y("div",Me,[a(U,null,{default:t(()=>[a(P(he))]),_:1}),r(" "+V(E.value),1)])):F("",!0),l("div",{class:"network-time-notice",onClick:j},[a(U,{class:D({"error-icon":v.value})},{default:t(()=>[a(P(ke))]),_:1},8,["class"]),e[11]||(e[11]=l("span",null,"本软件需要网络连接才能正常登录",-1))])]),_:1},8,["model"])])]),a(N,{modelValue:k.value,"onUpdate:modelValue":e[4]||(e[4]=c=>k.value=c),title:"获取激活码",width:"400px"},{footer:t(()=>[l("span",Ue,[a(p,{onClick:e[3]||(e[3]=c=>k.value=!1)},{default:t(()=>e[13]||(e[13]=[r("关闭")])),_:1}),a(p,{type:"primary",onClick:G},{default:t(()=>e[14]||(e[14]=[r(" 复制联系信息 ")])),_:1})])]),default:t(()=>[e[15]||(e[15]=l("div",{class:"contact-info"},[l("p",null,"请联系管理员获取激活码："),l("p",null,[l("strong",null,"邮箱："),r("<EMAIL>")]),l("p",{class:"tip"},"请提供您的机器码，以便生成对应的激活码")],-1))]),_:1},8,["modelValue"]),a(N,{modelValue:b.value,"onUpdate:modelValue":e[6]||(e[6]=c=>b.value=c),title:"网络连接状态",width:"500px"},{footer:t(()=>[l("span",Fe,[a(p,{onClick:e[5]||(e[5]=c=>b.value=!1)},{default:t(()=>e[17]||(e[17]=[r("关闭")])),_:1}),a(p,{type:"primary",onClick:T,loading:x.value},{default:t(()=>e[18]||(e[18]=[r(" 重新检测网络 ")])),_:1},8,["loading"])])]),default:t(()=>[m.value?(h(),y("div",Ne,[a(Y,{border:"",column:1},{default:t(()=>[a(L,{label:"连接状态"},{default:t(()=>[a(X,{type:Math.abs(m.value.time_diff)<60?"success":"warning"},{default:t(()=>[r(V(Math.abs(m.value.time_diff)<60?"网络连接正常":"网络连接异常"),1)]),_:1},8,["type"])]),_:1}),a(L,{label:"连接质量"},{default:t(()=>[r(V(Math.abs(m.value.time_diff)<10?"优":Math.abs(m.value.time_diff)<30?"良":Math.abs(m.value.time_diff)<60?"一般":"较差"),1)]),_:1}),a(L,{label:"最后检测时间"},{default:t(()=>[r(V(new Date().toLocaleString()),1)]),_:1})]),_:1}),Math.abs(m.value.time_diff)>60?(h(),y("div",Ae,[a(Z,{title:"网络连接异常，可能影响登录",type:"warning",closable:!1,"show-icon":""},{default:t(()=>e[16]||(e[16]=[l("div",null,"网络连接异常可能导致登录问题，请检查您的网络设置或稍后再试。",-1)])),_:1})])):F("",!0)])):(h(),y("div",De,[a(ee,{rows:4,animated:""})]))]),_:1},8,["modelValue"]),e[19]||(e[19]=l("div",{class:"background-shapes"},[l("div",{class:"shape shape-1"}),l("div",{class:"shape shape-2"}),l("div",{class:"shape shape-3"})],-1))],2)}}},je=oe(Pe,[["__scopeId","data-v-b4843c60"]]);export{je as default};
