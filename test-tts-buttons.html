<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS按钮主题测试</title>
    <style>
        :root {
            /* 白色主题变量 */
            --el-bg-color-rgb: 255, 255, 255;
            --el-border-color-rgb: 220, 223, 230;
            --el-text-color-primary: #303133;
            --el-color-primary-rgb: 64, 158, 255;
            --el-color-primary-light-3: #79bbff;
            --el-color-danger-rgb: 245, 108, 108;
            --el-color-danger-light-3: #f89898;
        }

        .dark-theme {
            /* 深色主题变量 */
            --el-bg-color-rgb: 20, 20, 20;
            --el-border-color-rgb: 84, 84, 84;
            --el-text-color-primary: #e5eaf3;
            --el-color-primary-rgb: 64, 158, 255;
            --el-color-primary-light-3: #79bbff;
            --el-color-danger-rgb: 245, 108, 108;
            --el-color-danger-light-3: #f89898;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: rgb(var(--el-bg-color-rgb));
            color: var(--el-text-color-primary);
            transition: all 0.3s ease;
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: rgba(var(--el-color-primary-rgb), 0.8);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .tts-controls {
            display: flex;
            gap: 6px;
            align-items: center;
            margin: 20px 0;
        }

        .tts-btn {
            position: relative;
            transition: all 0.2s ease;
            border-radius: 4px;
            font-size: 12px;
            padding: 6px 10px;
            height: 32px;
            /* 使用CSS变量适配主题 */
            border: 1px solid rgba(var(--el-border-color-rgb), 0.3) !important;
            background: rgba(var(--el-bg-color-rgb), 0.6) !important;
            color: var(--el-text-color-primary) !important;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .tts-btn:hover {
            background: rgba(var(--el-bg-color-rgb), 0.8) !important;
            border-color: rgba(var(--el-border-color-rgb), 0.5) !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .tts-btn.is-playing {
            background: linear-gradient(135deg, #ff4757, #ff3742);
            border-color: #ff4757;
            color: white;
            box-shadow: 0 0 0 2px rgba(255, 71, 87, 0.3);
            cursor: pointer; /* 确保播放状态下仍可点击 */
        }

        .tts-btn.is-loading {
            background: linear-gradient(135deg, #3742fa, #2f3af2);
            border-color: #3742fa;
            color: white;
            cursor: wait; /* 加载状态下显示等待光标 */
        }

        .tts-btn.is-disabled {
            opacity: 0.4;
            cursor: not-allowed;
        }

        .tts-btn.is-disabled:hover {
            transform: none;
            background: rgba(var(--el-bg-color-rgb), 0.6) !important;
            border-color: rgba(var(--el-border-color-rgb), 0.3) !important;
            box-shadow: none;
        }

        .section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid rgba(var(--el-border-color-rgb), 0.3);
            border-radius: 8px;
            background: rgba(var(--el-bg-color-rgb), 0.5);
        }

        h1, h2 {
            color: var(--el-text-color-primary);
        }

        .icon {
            width: 14px;
            height: 14px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">切换主题</button>
    
    <div class="test-container">
        <h1>TTS按钮主题适配测试</h1>
        
        <div class="section">
            <h2>正常状态</h2>
            <div class="tts-controls">
                <button class="tts-btn selected-play-btn">
                    <span class="icon">▶</span>
                    <span>选中播放</span>
                </button>
                <button class="tts-btn full-play-btn">
                    <span class="icon">⏯</span>
                    <span>全文播放</span>
                </button>
                <button class="tts-btn">
                    <span class="icon">⚙</span>
                    <span>设置</span>
                </button>
            </div>
        </div>

        <div class="section">
            <h2>播放状态</h2>
            <div class="tts-controls">
                <button class="tts-btn selected-play-btn is-playing">
                    <span class="icon">⏸</span>
                    <span>停止</span>
                </button>
                <button class="tts-btn full-play-btn is-playing">
                    <span class="icon">⏸</span>
                    <span>停止</span>
                </button>
            </div>
        </div>

        <div class="section">
            <h2>加载状态</h2>
            <div class="tts-controls">
                <button class="tts-btn selected-play-btn is-loading">
                    <span class="icon">⏳</span>
                    <span>加载中</span>
                </button>
                <button class="tts-btn full-play-btn is-loading">
                    <span class="icon">⏳</span>
                    <span>加载中</span>
                </button>
            </div>
        </div>

        <div class="section">
            <h2>禁用状态</h2>
            <div class="tts-controls">
                <button class="tts-btn selected-play-btn is-disabled">
                    <span class="icon">▶</span>
                    <span>选中播放</span>
                </button>
                <button class="tts-btn full-play-btn is-disabled">
                    <span class="icon">⏯</span>
                    <span>全文播放</span>
                </button>
                <button class="tts-btn is-disabled">
                    <span class="icon">⚙</span>
                    <span>设置</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            document.body.classList.toggle('dark-theme');
        }
    </script>
</body>
</html>
