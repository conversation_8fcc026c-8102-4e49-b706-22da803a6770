#!/usr/bin/env python3
"""
测试ConfigManager的修复效果
"""
import sys
import os
import threading
import time
import json

# 添加backend路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.bridge.ConfigManager import ConfigManager

def test_concurrent_saves():
    """测试并发保存"""
    config_file = "test_settings.json"
    
    # 清理测试文件
    if os.path.exists(config_file):
        os.remove(config_file)
    
    config_manager = ConfigManager(config_file)
    
    def save_config_worker(worker_id, iterations=10):
        """工作线程函数"""
        for i in range(iterations):
            test_config = {
                "worker": worker_id,
                "iteration": i,
                "timestamp": time.time(),
                "editor": {
                    "fontSize": 16 + worker_id,
                    "fontFamily": f"Font-{worker_id}"
                }
            }
            
            success = config_manager.save_config(test_config)
            print(f"Worker {worker_id}, Iteration {i}: {'Success' if success else 'Failed'}")
            
            # 短暂延迟
            time.sleep(0.01)
    
    # 创建多个线程同时保存配置
    threads = []
    num_workers = 5
    
    print(f"启动 {num_workers} 个工作线程进行并发测试...")
    
    for worker_id in range(num_workers):
        thread = threading.Thread(target=save_config_worker, args=(worker_id,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print("并发测试完成")
    
    # 验证最终配置文件
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                final_config = json.load(f)
            print("最终配置文件内容:")
            print(json.dumps(final_config, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"读取最终配置失败: {e}")
    else:
        print("配置文件不存在!")
    
    # 清理测试文件
    if os.path.exists(config_file):
        os.remove(config_file)
    
    # 清理可能的临时文件
    for file in os.listdir('.'):
        if file.startswith('test_settings.json.tmp') or file.endswith('.bak'):
            try:
                os.remove(file)
                print(f"清理临时文件: {file}")
            except:
                pass

if __name__ == "__main__":
    print("开始测试ConfigManager修复效果...")
    test_concurrent_saves()
    print("测试完成!")
