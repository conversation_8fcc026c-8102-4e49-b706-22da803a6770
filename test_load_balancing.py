#!/usr/bin/env python3
"""
负载均衡功能测试脚本
用于验证 ModelController 的负载均衡实现是否正常工作
"""

import sys
import os
import json
import tempfile
import shutil
from collections import Counter

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.bridge.ModelController import ModelController

def create_test_provider_config():
    """创建测试用的服务商配置"""
    return {
        "id": "test_provider",
        "name": "测试服务商",
        "baseUrl": "https://api.test.com/v1",
        "apiKeys": [
            {
                "id": "key1",
                "key": "sk-test-key-1",
                "status": "active",
                "weight": 1
            },
            {
                "id": "key2", 
                "key": "sk-test-key-2",
                "status": "active",
                "weight": 2
            },
            {
                "id": "key3",
                "key": "sk-test-key-3", 
                "status": "active",
                "weight": 3
            },
            {
                "id": "key4",
                "key": "sk-test-key-4",
                "status": "inactive",  # 非活跃状态
                "weight": 1
            },
            {
                "id": "key5",
                "key": "",  # 空密钥
                "status": "active",
                "weight": 1
            }
        ],
        "models": [
            {"id": "gpt-4", "available": True}
        ],
        "proxy": {
            "enabled": False
        }
    }

def test_load_balancing_distribution():
    """测试负载均衡分布是否符合权重设置"""
    print("=== 测试负载均衡分布 ===")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建 ModelController 实例
        controller = ModelController("dummy-key", "https://api.test.com/v1", temp_dir)
        
        # 设置测试配置
        test_provider = create_test_provider_config()
        controller.providers = [test_provider]
        
        # 模拟多次负载均衡选择
        selections = []
        test_count = 1000
        
        print(f"进行 {test_count} 次负载均衡选择测试...")
        
        for i in range(test_count):
            # 直接调用负载均衡方法
            try:
                # 模拟 _get_load_balanced_client 的逻辑
                import random
                api_keys = test_provider.get('apiKeys', [])
                active_keys = [key for key in api_keys if key.get('status') == 'active']
                
                # 创建加权列表
                weighted_keys = []
                for key in active_keys:
                    if key.get('key'):  # 只包含非空密钥
                        weight = key.get('weight', 1)
                        weight = max(1, weight)
                        for _ in range(weight):
                            weighted_keys.append(key)
                
                if weighted_keys:
                    selected_key = random.choice(weighted_keys)
                    selections.append(selected_key.get('id'))
                    
            except Exception as e:
                print(f"选择过程中出错: {e}")
        
        # 统计结果
        if selections:
            counter = Counter(selections)
            print(f"\n负载均衡选择结果统计 (总计 {len(selections)} 次):")
            print("密钥ID    | 选择次数 | 期望权重 | 实际比例")
            print("-" * 45)
            
            total_weight = 1 + 2 + 3  # key1(1) + key2(2) + key3(3) = 6
            for key_id, count in counter.most_common():
                if key_id == 'key1':
                    expected_weight = 1
                elif key_id == 'key2':
                    expected_weight = 2
                elif key_id == 'key3':
                    expected_weight = 3
                else:
                    expected_weight = 0
                
                expected_ratio = expected_weight / total_weight
                actual_ratio = count / len(selections)
                
                print(f"{key_id:8} | {count:8} | {expected_weight:8} | {actual_ratio:.3f} (期望: {expected_ratio:.3f})")
            
            # 验证是否符合预期
            key1_ratio = counter.get('key1', 0) / len(selections)
            key2_ratio = counter.get('key2', 0) / len(selections)
            key3_ratio = counter.get('key3', 0) / len(selections)
            
            print(f"\n验证结果:")
            print(f"key1 比例: {key1_ratio:.3f} (期望: ~0.167)")
            print(f"key2 比例: {key2_ratio:.3f} (期望: ~0.333)")  
            print(f"key3 比例: {key3_ratio:.3f} (期望: ~0.500)")
            
            # 检查是否在合理范围内 (±5%)
            tolerance = 0.05
            key1_ok = abs(key1_ratio - 1/6) < tolerance
            key2_ok = abs(key2_ratio - 2/6) < tolerance  
            key3_ok = abs(key3_ratio - 3/6) < tolerance
            
            if key1_ok and key2_ok and key3_ok:
                print("✅ 负载均衡分布符合权重设置")
                return True
            else:
                print("❌ 负载均衡分布不符合权重设置")
                return False
        else:
            print("❌ 没有成功的选择结果")
            return False
            
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir, ignore_errors=True)

def test_inactive_key_filtering():
    """测试非活跃密钥过滤"""
    print("\n=== 测试非活跃密钥过滤 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        controller = ModelController("dummy-key", "https://api.test.com/v1", temp_dir)
        test_provider = create_test_provider_config()
        controller.providers = [test_provider]
        
        # 模拟选择过程
        selections = []
        for i in range(100):
            try:
                import random
                api_keys = test_provider.get('apiKeys', [])
                active_keys = [key for key in api_keys if key.get('status') == 'active']
                
                weighted_keys = []
                for key in active_keys:
                    if key.get('key'):  # 只包含非空密钥
                        weight = key.get('weight', 1)
                        for _ in range(weight):
                            weighted_keys.append(key)
                
                if weighted_keys:
                    selected_key = random.choice(weighted_keys)
                    selections.append(selected_key.get('id'))
                    
            except Exception as e:
                print(f"选择过程中出错: {e}")
        
        # 检查是否包含非活跃密钥
        inactive_selected = any(key_id == 'key4' for key_id in selections)
        empty_key_selected = any(key_id == 'key5' for key_id in selections)
        
        print(f"选择结果中是否包含非活跃密钥 (key4): {'是' if inactive_selected else '否'}")
        print(f"选择结果中是否包含空密钥 (key5): {'是' if empty_key_selected else '否'}")
        
        if not inactive_selected and not empty_key_selected:
            print("✅ 非活跃密钥和空密钥过滤正常")
            return True
        else:
            print("❌ 非活跃密钥或空密钥过滤失败")
            return False
            
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)

def main():
    """主测试函数"""
    print("开始负载均衡功能测试...\n")
    
    test1_passed = test_load_balancing_distribution()
    test2_passed = test_inactive_key_filtering()
    
    print(f"\n=== 测试总结 ===")
    print(f"负载均衡分布测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"非活跃密钥过滤测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！负载均衡功能正常工作。")
        return 0
    else:
        print("\n⚠️  部分测试失败，请检查负载均衡实现。")
        return 1

if __name__ == "__main__":
    exit(main())
