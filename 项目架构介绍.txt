# PVV项目架构介绍

3.我们的项目使用的是pnpm管理项目。
4.不用每次你都启动服务测试，vite项目我会自己启动一次服务就够了。
5.不用写总结文档，在结束的时候需要的话，我主动让你总结。
6.不用老是创建test文件来测试，直接在项目中修改后，我会看效果，不用额外的测试文件。
7.我们的开发环境是windows
8.如果使用element的icon，注意只能使用下面的，其他的导入会报错，不存在。
  platform-eleme
  eleme
  delete-solid
  delete
  s-tools
  setting
  user-solid
  user
  phone
  phone-outline
  more
  more-outline
  star-on
  star-off
  s-goods
  goods
  warning
  warning-outline
  question
  info
  remove
  circle-plus
  success
  error
  zoom-in
  zoom-out
  remove-outline
  circle-plus-outline
  circle-check
  circle-close
  s-help
  help
  minus
  plus
  check
  close
  picture
  picture-outline
  picture-outline-round
  upload
  upload2
  download
  camera-solid
  camera
  video-camera-solid
  video-camera
  message-solid
  bell
  s-cooperation
  s-order
  s-platform
  s-fold
  s-unfold
  s-operation
  s-promotion
  s-home
  s-release
  s-ticket
  s-management
  s-open
  s-shop
  s-marketing
  s-flag
  s-comment
  s-finance
  s-claim
  s-custom
  s-opportunity
  s-data
  s-check
  s-grid
  menu
  share
  d-caret
  caret-left
  caret-right
  caret-bottom
  caret-top
  bottom-left
  bottom-right
  back
  right
  bottom
  top
  top-left
  top-right
  arrow-left
  arrow-right
  arrow-down
  arrow-up
  d-arrow-left
  d-arrow-right
  video-pause
  video-play
  refresh
  refresh-right
  refresh-left
  finished
  sort
  sort-up
  sort-down
  rank
  loading
  view
  c-scale-to-original
  date
  edit
  edit-outline
  folder
  folder-opened
  folder-add
  folder-remove
  folder-delete
  folder-checked
  tickets
  document-remove
  document-delete
  document-copy
  document-checked
  document
  document-add
  printer
  paperclip
  takeaway-box
  search
  monitor
  attract
  mobile
  scissors
  umbrella
  headset
  brush
  mouse
  coordinate
  magic-stick
  reading
  data-line
  data-board
  pie-chart
  data-analysis
  collection-tag
  film
  suitcase
  suitcase-1
  receiving
  collection
  files
  notebook-1
  notebook-2
  toilet-paper
  office-building
  school
  table-lamp
  house
  no-smoking
  smoking
  shopping-cart-full
  shopping-cart-1
  shopping-cart-2
  shopping-bag-1
  shopping-bag-2
  sold-out
  sell
  present
  box
  bank-card
  money
  coin
  wallet
  discount
  price-tag
  news
  guide
  male
  female
  thumb
  cpu
  link
  connection
  open
  turn-off
  set-up
  chat-round
  chat-line-round
  chat-square
  chat-dot-round
  chat-dot-square
  chat-line-square
  message
  postcard
  position
  turn-off-microphone
  microphone
  close-notification
  bangzhu
  time
  odometer
  crop
  aim
  switch-button
  full-screen
  copy-document
  mic
  stopwatch
  medal-1
  medal
  trophy
  trophy-1
  first-aid-kit
  discover
  place
  location
  location-outline
  location-information
  add-location
  delete-location
  map-location
  alarm-clock
  timer
  watch-1
  watch
  lock
  unlock
  key
  service
  mobile-phone
  bicycle
  truck
  ship
  basketball
  football
  soccer
  baseball
  wind-power
  light-rain
  lightning
  heavy-rain
  sunrise
  sunrise-1
  sunset
  sunny
  cloudy
  partly-cloudy
  cloudy-and-sunny
  moon
  moon-night
  dish
  dish-1
  food
  chicken
  fork-spoon
  knife-fork
  burger
  tableware
  sugar
  dessert
  ice-cream
  hot-water
  water-cup
  coffee-cup
  cold-drink
  goblet
  goblet-full
  goblet-square
  goblet-square-full
  refrigerator
  grape
  watermelon
  cherry
  apple
  pear
  orange
  coffee
  ice-tea
  ice-drink
  milk-tea
  potato-strips
  lollipop
  ice-cream-square
  ice-cream-round
## 项目概述
PVV是一个基于PyWebView的桌面应用程序，采用前后端分离架构，前端使用Vue3构建现代化用户界面，后端使用Python提供API服务。项目通过嵌入式资源打包技术，将Vue3前端资源打包成单个Python模块，实现了完全独立的桌面应用程序。

## 核心架构

### 1. 前端架构 (frontend/)
**技术栈：Vue3 + Element Plus + Pinia + Vite**

#### 主要依赖：
- Vue 3.5.13 - 核心框架
- Element Plus 2.9.1 - UI组件库
- Pinia 2.3.0 - 状态管理
- Vue Router 4.5.0 - 路由管理
- @matechat/core - 聊天组件库
- @tiptap/* - 富文本编辑器
- @vue-flow/* - 流程图组件
- ECharts 5.6.0 - 图表库
- TinyMCE 7.7.1 - 富文本编辑器
- Markdown-it 14.1.0 - Markdown解析
- CodeMirror 5.65.19 - 代码编辑器

#### 核心功能模块：
- **用在editor.vue界面的聊天** (components/ChatPanel.vue, ChatSidebar.vue)
- **用在editor.vue鼠标右键ai对话的弹窗** (components/AIAssistantWindow.vue)
- **文件管理器** (components/FileManager.vue)
- **代码预览** (components/CodePreviewModal.vue)
- **Markdown编辑器** (components/SimpleMarkdownEditor.vue)
- **思维导图** (components/mindmap/)
- **设置面板** (components/settings/)
- **书籍管理** (views/book/)
- **项目注入** (views/inject_project/)

#### 状态管理 (stores/):
- app.js - 应用全局状态
- book.js - 书籍相关状态
- config.js - 配置管理
- user.js - 用户管理
- aiProviders.js - AI提供商配置
- customPool.js - 自定义池管理

### 2. 后端架构 (backend/)
**技术栈：Python + PyWebView + 模块化控制器**

#### 核心模块 (backend/bridge/):
- **API.py** - 主API接口类，继承ResponsePacket，提供统一的API响应格式
- **Base.py** - 基础响应包装类
- **BookController.py** - 书籍管理控制器
- **ModelController.py** - AI模型管理控制器
- **ProjectController.py** - 项目管理控制器
- **InjectProjectController.py** - 项目注入控制器
- **LocalController.py** - 本地文件操作控制器
- **ConfigManager.py** - 配置管理器
- **UserManager.py** - 用户管理器
- **EdgeTtsController.py** - 语音合成控制器
- **DrssionController.py** - 浏览器自动化控制器
- **CacheManager.py** - 缓存管理器
- **PathManager.py** - 路径管理器
- **HardwareIdentifier.py** - 硬件标识符
- **TimeValidator.py** - 时间验证器

#### 运行时模块 (backend/pvvruntime/):
- **Runtime.py** - 运行时管理
- **Watcher.py** - 文件监控

### 3. 资源打包系统
**核心文件：**
- **pack_frontend_resources.py** - 前端资源打包脚本
  - 将Vue3构建后的dist目录打包成单个Python模块
  - 支持gzip压缩减少文件体积
  - 自动识别MIME类型
  - 生成frontend_resources.py模块

- **embedded_server.py** - 嵌入式HTTP服务器
  - 为PyWebView提供嵌入的前端资源服务
  - 处理静态资源请求
  - 支持MIME类型识别
  - 提供资源统计信息

- **frontend_resources.py** - 打包后的前端资源模块
  - 包含所有前端静态资源的Base64编码数据
  - 提供get_resource()、get_mime_type()等API
  - 支持资源列表和统计信息查询

### 4. 应用启动系统
**PVV2.py** - 主启动文件
- 跨平台编码设置，支持Windows/macOS/Linux
- PyWebView窗口配置和启动
- API绑定和初始化
- 系统托盘集成
- 优雅退出处理

## 工作流程

### 开发流程：
1. **前端开发** - 在frontend/目录使用Vue3开发界面
2. **后端开发** - 在backend/目录开发Python API
3. **资源打包** - 运行pack_frontend_resources.py打包前端资源
4. **应用启动** - 运行PVV2.py启动完整应用



### 通信机制：
- 前端通过window.pywebview.api调用后端Python方法
- 后端通过webview.window对象与前端通信
- 所有API响应统一使用ResponsePacket格式
- 支持异步调用和错误处理

## 核心特性

### 1. 完全离线运行
- 前端资源完全嵌入到Python应用中
- 无需外部Web服务器
- 支持完全离线使用

### 2. 跨平台支持
- Windows、macOS、Linux全平台支持
- 统一的编码处理机制
- 平台特定的优化配置

### 3. 模块化架构
- 前后端完全分离
- 控制器模块化设计
- 易于扩展和维护

### 4. 丰富的功能集成
- AI聊天和助手功能
- 文档编辑和管理
- 项目注入和管理
- 语音合成
- 浏览器自动化
- 思维导图
- 代码编辑和预览

### 5. 现代化UI/UX
- Vue3 Composition API
- Element Plus组件库
- 响应式设计
- 主题切换支持
- 丰富的交互组件

## 技术亮点

1. **资源嵌入技术** - 创新的前端资源打包方案，实现真正的单文件分发
2. **PyWebView集成** - 完美结合Python后端和现代Web前端技术
3. **模块化设计** - 高度模块化的后端控制器架构
4. **跨平台兼容** - 完善的跨平台编码和配置处理
5. **现代化前端** - 采用最新的Vue3生态系统和组件库

这个架构设计使得PVV项目既具备了现代Web应用的用户体验，又拥有了桌面应用的完整功能和离线能力。
